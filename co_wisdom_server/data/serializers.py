from django.core.validators import validate_comma_separated_integer_list
from rest_framework import serializers

from data import models


class DateTimeModelSerializer(serializers.ModelSerializer):
    CreateDate = serializers.DateTimeField(format='%Y-%m-%d %H:%M:%S')
    ModifyDate = serializers.DateTimeField(format='%Y-%m-%d %H:%M:%S')



class ProjectCoachInterviewSerializer(DateTimeModelSerializer):
    class Meta:
        model = models.ProjectCoachInterview
        fields = "__all__"


class ProjectCoachSerializer(DateTimeModelSerializer):
    class Meta:
        model = models.Projectcoach
        fields = "__all__"


class AppProjectExamSerializer(DateTimeModelSerializer):
    class Meta:
        model = models.AppProbjectexam
        fields = "__all__"


class AppArticleSerializer(DateTimeModelSerializer):
    class Meta:
        model = models.AppArticle
        fields = "__all__"


class AppArticlecategorySerializer(DateTimeModelSerializer):
    class Meta:
        model = models.AppArticlecategory
        fields = "__all__"


class AppCoachSerializer(serializers.ModelSerializer):
    class Meta:
        model = models.AppCoach
        fields = "__all__"


class AppCoachrateSerializer(DateTimeModelSerializer):
    class Meta:
        model = models.AppCoachrate
        fields = "__all__"


class AppCoachscheduleSerializer(DateTimeModelSerializer):
    class Meta:
        model = models.AppCoachschedule
        fields = "__all__"


class AppCompanySerializer(DateTimeModelSerializer):
    class Meta:
        model = models.AppCompany
        fields = "__all__"


class AppEmailqueueSerializer(DateTimeModelSerializer):
    class Meta:
        model = models.AppEmailqueue
        fields = "__all__"


class AppEvaluationSerializer(DateTimeModelSerializer):
    class Meta:
        model = models.AppEvaluation
        fields = "__all__"


class AppEvaluationoptionSerializer(serializers.ModelSerializer):
    class Meta:
        model = models.AppEvaluationoption
        fields = "__all__"


class AppEvaluationquestionSerializer(DateTimeModelSerializer):
    class Meta:
        model = models.AppEvaluationquestion
        fields = "__all__"


class AppEvaluationresultSerializer(serializers.ModelSerializer):
    class Meta:
        model = models.AppEvaluationresult
        fields = "__all__"


class AppEvaluationtmpresultSerializer(serializers.ModelSerializer):
    class Meta:
        model = models.AppEvaluationtmpresult
        fields = "__all__"


class AppEvalutionreportSerializer(DateTimeModelSerializer):
    class Meta:
        model = models.AppEvalutionreport
        fields = "__all__"


class AppExamSerializer(DateTimeModelSerializer):
    class Meta:
        model = models.AppExam
        fields = "__all__"


class AppExamfactorySerializer(DateTimeModelSerializer):
    class Meta:
        model = models.AppExamfactory
        fields = "__all__"


class AppExammapquestionSerializer(serializers.ModelSerializer):
    class Meta:
        model = models.AppExammapquestion
        fields = "__all__"


class AppExamoptionSerializer(serializers.ModelSerializer):
    class Meta:
        model = models.AppExamoption
        fields = "__all__"


class AppExamquestionSerializer(DateTimeModelSerializer):
    class Meta:
        model = models.AppExamquestion
        fields = "__all__"


class AppExamresultSerializer(serializers.ModelSerializer):
    class Meta:
        model = models.AppExamresult
        fields = "__all__"


class AppFeedbackSerializer(serializers.ModelSerializer):
    class Meta:
        model = models.AppFeedback
        fields = "__all__"


class AppGrowthdiarySerializer(DateTimeModelSerializer):
    class Meta:
        model = models.AppGrowthdiary
        fields = "__all__"


class AppHxmeetingdataSerializer(DateTimeModelSerializer):
    class Meta:
        model = models.AppHxmeetingdata
        fields = "__all__"


class AppIldpSerializer(DateTimeModelSerializer):
    class Meta:
        model = models.AppIldp
        fields = "__all__"


class AppImSerializer(DateTimeModelSerializer):
    class Meta:
        model = models.AppIm
        fields = "__all__"


class AppMemberSerializer(DateTimeModelSerializer):
    CompanyWorkingDate = serializers.DateTimeField(format='%Y-%m-%d %H:%M:%S', required=False, read_only=True)
    GwWorkingDate = serializers.DateTimeField(format='%Y-%m-%d %H:%M:%S', required=False, read_only=True)
    WorkingDate = serializers.DateTimeField(format='%Y-%m-%d %H:%M:%S', required=False, read_only=True)
    
    class Meta:
        model = models.AppMember
        fields = "__all__"


class AppMemberinterestedSerializer(DateTimeModelSerializer):
    class Meta:
        model = models.AppMemberinterested
        fields = "__all__"


class AppMessageSerializer(serializers.ModelSerializer):
    post_time = serializers.DateTimeField(format='%Y-%m-%d %H:%M:%S')
    read_time = serializers.DateTimeField(format='%Y-%m-%d %H:%M:%S')
    replaytime = serializers.DateTimeField(format='%Y-%m-%d %H:%M:%S')

    class Meta:
        model = models.AppMessage
        fields = "__all__"


class AppMessagecontentSerializer(DateTimeModelSerializer):
    class Meta:
        model = models.AppMessagecontent
        fields = "__all__"


class AppMessagetemplatesSerializer(DateTimeModelSerializer):
    class Meta:
        model = models.AppMessagetemplates
        fields = "__all__"


class AppModelreportreportSerializer(DateTimeModelSerializer):
    class Meta:
        model = models.AppModelreportreport
        fields = "__all__"


class AppModelreportresultSerializer(DateTimeModelSerializer):
    class Meta:
        model = models.AppModelreportresult
        fields = "__all__"


class AppModeltemplateSerializer(DateTimeModelSerializer):
    class Meta:
        model = models.AppModeltemplate
        fields = "__all__"


class AppProbjectSerializer(DateTimeModelSerializer):
    class Meta:
        model = models.AppProbject
        fields = "__all__"


class AppProbjectexamSerializer(DateTimeModelSerializer):
    class Meta:
        model = models.AppProbjectexam
        fields = "__all__"


class AppProbjectexamrelationuserSerializer(DateTimeModelSerializer):
    CompleteDate = serializers.DateTimeField(format='%Y-%m-%d %H:%M:%S')
    class Meta:
        model = models.AppProbjectexamrelationuser
        fields = "__all__"


class AppProbjectinterviewSerializer(DateTimeModelSerializer):
    StartTime = serializers.DateTimeField(format='%Y-%m-%d %H:%M:%S')
    EndTime = serializers.DateTimeField(format='%Y-%m-%d %H:%M:%S')

    class Meta:
        model = models.AppProbjectinterview
        fields = "__all__"


class AppProbjectlearningactionSerializer(DateTimeModelSerializer):
    class Meta:
        model = models.AppProbjectlearningaction
        fields = "__all__"


class AppProbjectneedSerializer(DateTimeModelSerializer):
    class Meta:
        model = models.AppProbjectneed
        fields = "__all__"


class AppProbjectobjectiveSerializer(DateTimeModelSerializer):
    class Meta:
        model = models.AppProbjectobjective
        fields = "__all__"


class AppProbjectrelationSerializer(DateTimeModelSerializer):
    class Meta:
        model = models.AppProbjectrelation
        fields = "__all__"


class AppProbjectreportSerializer(DateTimeModelSerializer):
    class Meta:
        model = models.AppProbjectreport
        fields = "__all__"


class AppProbjectsatisfactionSerializer(DateTimeModelSerializer):
    class Meta:
        model = models.AppProbjectsatisfaction
        fields = "__all__"


class AppProbjectsettingSerializer(DateTimeModelSerializer):
    class Meta:
        model = models.AppProbjectsetting
        fields = "__all__"


class AppProbjectsettingcoachSerializer(DateTimeModelSerializer):
    class Meta:
        model = models.AppProbjectsettingcoach
        fields = "__all__"


class AppProbjectsettingcoachneedSerializer(DateTimeModelSerializer):
    class Meta:
        model = models.AppProbjectsettingcoachneed
        fields = "__all__"


class AppProbjectupabilitySerializer(serializers.ModelSerializer):
    class Meta:
        model = models.AppProbjectupability
        fields = "__all__"


class AppProbjectuploadreportSerializer(DateTimeModelSerializer):
    class Meta:
        model = models.AppProbjectuploadreport
        fields = "__all__"


class AppRescategoriesSerializer(serializers.ModelSerializer):
    class Meta:
        model = models.AppRescategories
        fields = "__all__"


class AppResourcesSerializer(DateTimeModelSerializer):
    class Meta:
        model = models.AppResources
        fields = "__all__"


class AppScheduleinfoSerializer(DateTimeModelSerializer):
    class Meta:
        model = models.AppScheduleinfo
        fields = "__all__"


class AppSurveySerializer(DateTimeModelSerializer):
    class Meta:
        model = models.AppSurvey
        fields = "__all__"


class AppSurveyanswersSerializer(serializers.ModelSerializer):
    class Meta:
        model = models.AppSurveyanswers
        fields = "__all__"


class AppSurveyoptionSerializer(serializers.ModelSerializer):
    class Meta:
        model = models.AppSurveyoption
        fields = "__all__"


class AppSurveysubjectSerializer(DateTimeModelSerializer):
    class Meta:
        model = models.AppSurveysubject
        fields = "__all__"


class AppUseronlineSerializer(DateTimeModelSerializer):
    class Meta:
        model = models.AppUseronline
        fields = "__all__"


class AppDictionarylistSerializer(DateTimeModelSerializer):
    class Meta:
        model = models.AppDictionarylist
        fields = "__all__"


class AppDictionarySerializer(DateTimeModelSerializer):
    class Meta:
        model = models.AppDictionary
        fields = "__all__"


class SysCitySerializer(serializers.ModelSerializer):
    class Meta:
        model = models.SysCity
        fields = "__all__"


class SysMenuSerializer(DateTimeModelSerializer):
    class Meta:
        model = models.SysMenu
        fields = "__all__"


class SysProvinceSerializer(serializers.ModelSerializer):
    class Meta:
        model = models.SysProvince
        fields = "__all__"


class SysRoleSerializer(DateTimeModelSerializer):
    class Meta:
        model = models.SysRole
        fields = "__all__"


class SysRoleauthSerializer(DateTimeModelSerializer):
    class Meta:
        model = models.SysRoleauth
        fields = "__all__"


class SysRoleauthdataSerializer(DateTimeModelSerializer):
    class Meta:
        model = models.SysRoleauthdata
        fields = "__all__"


class SysSettingSerializer(DateTimeModelSerializer):
    class Meta:
        model = models.SysSetting
        fields = "__all__"


class SysTableinfoSerializer(serializers.ModelSerializer):
    class Meta:
        model = models.SysTableinfo
        fields = "__all__"


class SysTablecolumnSerializer(DateTimeModelSerializer):
    class Meta:
        model = models.SysTablecolumn
        fields = "__all__"


class SysUpfileSerializer(DateTimeModelSerializer):
    class Meta:
        model = models.SysUpfile
        fields = "__all__"


class SysUserSerializer(DateTimeModelSerializer):
    class Meta:
        model = models.SysUser
        fields = "__all__"


class AppProjectInterviewShareSerializer(DateTimeModelSerializer):
    class Meta:
        model = models.AppProbjectInterviewShare
        fields = "__all__"


class AppCacheTmpResultSerializer(DateTimeModelSerializer):
    class Meta:
        model = models.AppCacheTmpResult
        fields = "__all__"


class AppLearningActionNoteSerializer(DateTimeModelSerializer):
    class Meta:
        model = models.AppLearningActionNote
        fields = "__all__"


class AppProjectSettingAdminSerializer(DateTimeModelSerializer):
    class Meta:
        model = models.AppProjectSettingAdmin
        fields = "__all__"


class AppScheduleAttendeeSerializer(serializers.ModelSerializer):
    class Meta:
        model = models.AppScheduleAttendee
        fields = "__all__"
