import datetime
import os
import uuid

from django.conf import settings
from django.db.models import <PERSON><PERSON><PERSON><PERSON>, TextField
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.viewsets import GenericViewSet

from data import models
from utils import response
from utils.model import add_row, update_row, delete_row, get_page_rows


def model_field_exists(cls, field):
    for f in cls._meta.get_fields(include_hidden=True):
        if f.name == field:
            return True
    return False


class ResponseViewSet(GenericViewSet):

    @action(methods=['post'], detail=False)
    def addfront(self, request):
        main_data = request.data.get('mainData')
        res = self.add_row(main_data)
        return Response(
            res.raw()
        )

    @action(methods=['post'], detail=False)
    def getpagedata(self, request):
        t = self.get_page_rows(request.data)
        return Response(
            self.page_data(t)
        )

    @action(methods=['post'], detail=False)
    def getpagedatafront(self, request):
        t = self.get_page_rows(request.data)
        return Response(
            self.page_data(t)
        )

    @action(methods=['get'], detail=False)
    def getentity(self, request):
        res = self.get_entity(request)
        return Response(
            res.ok().raw()
        )

    @action(methods=['post'], detail=False, url_path='update')
    def update_new(self, request):
        main_data = request.data.get('mainData')
        res = self.update_row(main_data)
        return Response(
            res.raw()
        )

    @action(methods=['post'], detail=False, url_path='del')
    def del_new(self, request):
        keys = request.data
        res = self.delete_row(keys)
        return Response(
            res.raw()
        )

    @action(methods=['post'], detail=False)
    def upload(self, request):
        file_obj = request.FILES.get('fileInput')
        if not file_obj:
            return Response(
                response.Content().error('请上传文件').raw()
            )
        res = self.upload_file(file_obj)
        return Response(
            res.raw()
        )

    def upload_file(self, fileinfo, allowtypes=None):
        if allowtypes is None:
            allowtypes = ['.gif', '.jpeg', '.jpg', '.png', '.xlsx', '.zip', '.mp4', '.pdf', '.doc', '.docx', '.xls',
                          '.ppt', '.pptx']
        bantypes = ['.exe', '.bat', '.cmd', '.aspx', '.php', '.asp', '.java', '.py', '.dll']
        res = response.Content()
        filename, file_extension = os.path.splitext(fileinfo.name)
        if file_extension not in allowtypes or file_extension in bantypes:
            return res.error('不支持此文件格式上传')
        model_name = self.serializer_class.Meta.model._meta.model_name
        time = datetime.datetime.now().strftime('%Y%m%d%H')
        filename = str(uuid.uuid4())[:8] + file_extension
        filepath = os.path.join(settings.MEDIA_ROOT, model_name, time, filename)
        parent_path = os.path.dirname(filepath)
        if not os.path.exists(parent_path):
            os.makedirs(parent_path)
        try:
            destination = open(filepath, 'wb+')
            for chunk in fileinfo.chunks():
                destination.write(chunk)
            destination.close()
        except Exception as e:
            return res.error('上传文件失败')
        relative_path = settings.UPLOAD_URL + model_name + '/' + time + '/' + filename
        newfile = models.SysUpfile(FileName=fileinfo.name, FilePath=relative_path, FileSize=int(fileinfo.size / 1024),
                                   FileExtensions=file_extension, Enable=1, CreateDate=datetime.datetime.now(),
                                   Creator=self.request.user.UserTrueName)
        newfile.save()
        res.data = relative_path
        res.lower_result = 1
        return res.ok('文件上传成功')


    def filter_blank_for_nonstringarg(self, request):
        request_dic = request.data.get('mainData')
        delete_list = []
        for k, v in request_dic.items():
            if v == '':
                try:
                    field = self.serializer_class.Meta.model._meta.get_field(k)
                except Exception as e:
                    continue
                if field.__class__ is not CharField or field.__class__ is not TextField:
                    delete_list.append(k)
        for key in delete_list:
            del request.data['mainData'][key]

    def __filter_blank_for_nonstringarg(self, request_dic):
        delete_list = []
        for k, v in request_dic.items():
            if v == '':
                try:
                    field = self.serializer_class.Meta.model._meta.get_field(k)
                except Exception as e:
                    continue
                if field.__class__ is not CharField or field.__class__ is not TextField:
                    delete_list.append(k)
        for key in delete_list:
            del request_dic[key]

    def add_row(self, data):
        data['CreateID'] = self.request.user.pk
        data['Creator'] = self.request.user.UserTrueName
        data['CreateDate'] = datetime.datetime.now().replace(microsecond=0)
        data['ModifyDate'] = datetime.datetime.now().replace(microsecond=0)
        self.__filter_blank_for_nonstringarg(data)
        return add_row(data, self.serializer_class)

    def update_row(self, data):
        self.__filter_blank_for_nonstringarg(data)
        data['ModifyDate'] = datetime.datetime.now().replace(microsecond=0)
        return update_row(data, self.serializer_class)

    def delete_row(self, data):
        return delete_row(data, self.serializer_class)

    def get_page_rows(self, data):
        return get_page_rows(data, self.serializer_class)

    def page_data(self, data_set):
        return response.page_data(data_set, self.serializer_class)

    def get_entity(self, request):
        keyvalue = request.query_params.get('keyvalue')
        field = request.query_params.get('field', '')
        if field == '':
            field = 'pk'
        res = response.Content()
        if keyvalue is None:
            return res.error_type(response.ResponseType.NoKey)
        # if not model_field_exists(self.serializer_class.meta.model, field):
        #     return res.error('字段不存在')
        filter_dict = {
            field: keyvalue
        }
        row = self.queryset.filter(**filter_dict).first()
        if row is None:
            return res.error('')
        res.row = row
        res.data = self.serializer_class(row, many=False).data
        return res.ok_type(response.ResponseType.OperSuccess)