from django.db import models


class Article(models.Model):
    title = models.CharField(max_length=255, null=True)
    typeId = models.IntegerField(null=True)
    pageCode = models.CharField(max_length=50, null=True)
    shareUsers = models.CharField(max_length=255, null=True)
    contents = models.TextField(null=True)
    filePath = models.CharField(max_length=255, null=True)
    sort = models.IntegerField(null=True)
    enable = models.IntegerField(null=True)

    class Meta:
        db_table = 'article'


class ArticleCategory(models.Model):
    enable = models.IntegerField(null=True)
    order_no = models.IntegerField(null=True)
    parentId = models.IntegerField(null=False)
    cateName = models.CharField(max_length=50, null=True)

    class Meta:
        db_table = 'article_category'


class Menu(models.Model):
    menu_name = models.CharField(max_length=50, null=False)
    auth = models.TextField(null=True)
    icon = models.Char<PERSON>ield(max_length=50, null=True)
    description = models.CharField(max_length=200, null=True)
    enable = models.IntegerField(null=True)
    order_no = models.IntegerField(null=True)
    table_name = models.CharField(max_length=200, null=True)
    parent_id = models.IntegerField(null=False)
    url = models.TextField(null=True)
    router_name = models.CharField(max_length=200, null=True)

    class Meta:
        db_table = 'menu'


class RoleAuth(models.Model):
    auth_value = models.TextField(null=False)
    menu = models.ForeignKey(Menu, on_delete=models.CASCADE, null=False)
    role_id = models.IntegerField(null=True)
    user_id = models.IntegerField(null=True)

    class Meta:
        db_table = 'role_auth'


class Role(models.Model):
    role_name = models.CharField(max_length=50)

    class Meta:
        db_table = 'data_role'


class SysDictionary(models.Model):
    config_json = models.TextField(null=True)  # ConfigJson
    sql = models.TextField(null=True)  # Dbsql
    dic_name = models.CharField(max_length=100)  # DicName
    dic_no = models.CharField(max_length=100)  # DicNo
    order_no = models.IntegerField(null=True)  # OrderNo
    remark = models.TextField(null=True)  # Remark
    enable = models.IntegerField(null=True, default=1)

    class Meta:
        db_table = 'data_dictionary'


class SysDictionaryList(models.Model):
    dictionary = models.ForeignKey(SysDictionary, on_delete=models.CASCADE, null=True)  # Dic_ID
    dic_name = models.CharField(max_length=100)  # DicName
    dic_value = models.CharField(max_length=100)  # DicValue
    order_no = models.IntegerField(null=True)  # OrderNo
    remark = models.TextField(null=True)  # Remark
    enable = models.IntegerField(null=True, default=1)

    class Meta:
        db_table = 'data_dictionarylist'


###old data
class AppProbject(models.Model):
    Probject_Id = models.AutoField(db_column='Probject_Id', primary_key=True)  # Field name made lowercase.
    Company_Id = models.IntegerField(db_column='Company_Id')  # Field name made lowercase.
    Name = models.CharField(db_column='Name', max_length=250)  # Field name made lowercase.
    Background = models.TextField(db_column='Background', blank=True, null=True)  # Field name made lowercase.
    Number = models.IntegerField(db_column='Number')  # Field name made lowercase.
    Objective = models.TextField(db_column='Objective', blank=True, null=True)  # Field name made lowercase.
    Times = models.IntegerField(db_column='Times')  # Field name made lowercase.
    Starttime = models.DateTimeField(db_column='Starttime')  # Field name made lowercase.
    Endtime = models.DateTimeField(db_column='Endtime')  # Field name made lowercase.
    Video = models.CharField(db_column='Video', max_length=250, blank=True, null=True)  # Field name made lowercase.
    Website = models.CharField(db_column='Website', max_length=250, blank=True, null=True)  # Field name made lowercase.
    Instro = models.TextField(db_column='Instro', blank=True, null=True)  # Field name made lowercase.
    Status = models.IntegerField(db_column='Status')  # Field name made lowercase.
    Manager_UserId = models.TextField(db_column='Manager_UserId', blank=True, null=True)  # Field name made lowercase.
    Relation_UserId = models.IntegerField(db_column='Relation_UserId', blank=True,
                                          null=True)  # Field name made lowercase.
    CoachIds = models.TextField(db_column='CoachIds', blank=True, null=True)  # Field name made lowercase.
    Enable = models.IntegerField(db_column='Enable', blank=True, null=True)  # Field name made lowercase.
    CreateDate = models.DateTimeField(db_column='CreateDate', auto_now_add=True)  # Field name made lowercase.
    Creator = models.CharField(db_column='Creator', max_length=50, blank=True, null=True)  # Field name made lowercase.
    ModifyDate = models.DateTimeField(db_column='ModifyDate', auto_now=True)  # Field name made lowercase.
    Modifier = models.CharField(db_column='Modifier', max_length=50, blank=True,
                                null=True)  # Field name made lowercase.
    InterViewOrder = models.TextField(db_column='InterViewOrder', blank=True, null=True)  # Field name made lowercase.
    remark = models.TextField(blank=True, null=True)
    dothing = models.IntegerField(blank=True, null=True)
    FilePath = models.TextField(db_column='FilePath', blank=True, null=True)  # Field name made lowercase.

    class Meta:
        db_table = 'app_probject'


class AppProbjectexam(models.Model):
    Id = models.AutoField(db_column='Id', primary_key=True)  # Field name made lowercase.
    CompanyId = models.IntegerField(db_column='CompanyId', default=0)  # Field name made lowercase.
    ProbjectId = models.IntegerField(db_column='ProbjectId')  # Field name made lowercase.
    TypeId = models.IntegerField(db_column='TypeId', blank=True, null=True)  # Field name made lowercase.
    ExId = models.IntegerField(db_column='ExId')  # Field name made lowercase.
    ConfigJson = models.TextField(db_column='ConfigJson', blank=True, null=True)  # Field name made lowercase.
    Enable = models.IntegerField(db_column='Enable', blank=True, null=True)  # Field name made lowercase.
    CreateDate = models.DateTimeField(db_column='CreateDate', auto_now_add=True)  # Field name made lowercase.
    Creator = models.CharField(db_column='Creator', max_length=50, blank=True, null=True)  # Field name made lowercase.
    ModifyDate = models.DateTimeField(db_column='ModifyDate', auto_now=True)  # Field name made lowercase.
    Modifier = models.CharField(db_column='Modifier', max_length=50, blank=True,
                                null=True)  # Field name made lowercase.
    ExName = models.CharField(db_column='ExName', max_length=250, blank=True, null=True)  # Field name made lowercase.
    PageCode = models.CharField(db_column='PageCode', max_length=100, blank=True,
                                null=True)  # Field name made lowercase.

    class Meta:
        db_table = 'app_probjectexam'


class AppProbjectexamrelationuser(models.Model):
    Id = models.AutoField(db_column='Id', primary_key=True)  # Field name made lowercase.
    CompanyId = models.IntegerField(db_column='CompanyId', default=0)  # Field name made lowercase.
    ProbjectId = models.IntegerField(db_column='ProbjectId', blank=True, null=True)  # Field name made lowercase.
    ExId = models.IntegerField(db_column='ExId')  # Field name made lowercase.
    UserId = models.IntegerField(db_column='UserId')  # Field name made lowercase.
    Enable = models.IntegerField(db_column='Enable', blank=True, null=True)  # Field name made lowercase.
    EndDate = models.DateTimeField(db_column='EndDate', blank=True, null=True)  # Field name made lowercase.
    Status = models.IntegerField(db_column='Status', blank=True, null=True)  # Field name made lowercase.
    CompleteDate = models.DateTimeField(db_column='CompleteDate', blank=True, null=True)  # Field name made lowercase.
    CreateDate = models.DateTimeField(db_column='CreateDate', auto_now_add=True)  # Field name made lowercase.
    Creator = models.CharField(db_column='Creator', max_length=50, blank=True, null=True)  # Field name made lowercase.
    ModifyDate = models.DateTimeField(db_column='ModifyDate', auto_now=True)  # Field name made lowercase.
    Modifier = models.CharField(db_column='Modifier', max_length=50, blank=True,
                                null=True)  # Field name made lowercase.
    ExName = models.CharField(db_column='ExName', max_length=250, blank=True, null=True)  # Field name made lowercase.
    TypeId = models.IntegerField(db_column='TypeId', blank=True, null=True)  # Field name made lowercase.
    ReportId = models.IntegerField(db_column='ReportId', blank=True, null=True)  # Field name made lowercase.
    pici = models.CharField(max_length=100, blank=True, null=True)
    PageCode = models.CharField(db_column='PageCode', max_length=100, blank=True,
                                null=True)  # Field name made lowercase.
    roleid = models.IntegerField(blank=True, null=True)
    interested_id = models.IntegerField(default=0)

    class Meta:
        db_table = 'app_probjectexamrelationuser'


class AppProbjectinterview(models.Model):
    Id = models.AutoField(db_column='Id', primary_key=True)  # Field name made lowercase.
    ScheduleId = models.IntegerField(db_column='ScheduleId', blank=True, null=True)  # Field name made lowercase.
    CompanyId = models.IntegerField(db_column='CompanyId', default=0)  # Field name made lowercase.
    ProbjectId = models.IntegerField(db_column='ProbjectId')  # Field name made lowercase.
    User_Id = models.IntegerField(db_column='User_Id')  # Field name made lowercase.
    Coach_Id = models.IntegerField(db_column='Coach_Id')  # Field name made lowercase.
    Title = models.CharField(db_column='Title', max_length=250)  # Field name made lowercase.
    Remark = models.TextField(db_column='Remark', blank=True, null=True)  # Field name made lowercase.
    status = models.IntegerField(blank=True, null=True)
    CloseReason = models.CharField(db_column='CloseReason', max_length=250, blank=True,
                                   null=True)  # Field name made lowercase.
    StartTime = models.DateTimeField(db_column='StartTime', blank=True, null=True)  # Field name made lowercase.
    EndTime = models.DateTimeField(db_column='EndTime', blank=True, null=True)  # Field name made lowercase.
    Times = models.IntegerField(db_column='Times', blank=True, null=True)  # Field name made lowercase.
    Satisfaction = models.FloatField(db_column='Satisfaction', blank=True, null=True)  # Field name made lowercase.
    Purpose = models.TextField(db_column='Purpose', blank=True, null=True)  # Field name made lowercase.
    CreateDate = models.DateTimeField(db_column='CreateDate', auto_now_add=True)  # Field name made lowercase.
    Creator = models.CharField(db_column='Creator', max_length=50, blank=True, null=True)  # Field name made lowercase.
    ModifyDate = models.DateTimeField(db_column='ModifyDate', auto_now=True)  # Field name made lowercase.
    Modifier = models.CharField(db_column='Modifier', max_length=50, blank=True,
                                null=True)  # Field name made lowercase.
    nowInterview = models.IntegerField(db_column='nowInterview', blank=True, null=True)  # Field name made lowercase.
    AllInterview = models.IntegerField(db_column='AllInterview', blank=True, null=True)  # Field name made lowercase.
    pici = models.CharField(max_length=50, blank=True, null=True)
    growupSatisfied = models.FloatField(db_column='growupSatisfied', blank=True,
                                        null=True)  # Field name made lowercase.
    inputSatisfied = models.FloatField(db_column='inputSatisfied', blank=True, null=True)  # Field name made lowercase.
    UpAbility = models.CharField(db_column='UpAbility', max_length=200, blank=True,
                                 null=True)  # Field name made lowercase.
    interviewType = models.IntegerField(db_column='interviewType', blank=True, null=True)  # Field name made lowercase.
    CloseType = models.CharField(db_column='CloseType', max_length=200, blank=True,
                                 null=True)  # Field name made lowercase.
    ProbjectRank = models.FloatField(db_column='ProbjectRank', blank=True, null=True)  # Field name made lowercase.
    masteruserid = models.IntegerField(blank=True, null=True)
    typeid = models.IntegerField(blank=True, null=True)
    interviewStatus = models.IntegerField(db_column='interviewStatus', blank=True,
                                          null=True)  # Field name made lowercase.
    interviewsubject = models.IntegerField(blank=True, null=True)
    hxdata = models.TextField(blank=True, null=True)
    jobstatus = models.IntegerField(default=0)
    needrecord = models.IntegerField(default=1)

    class Meta:
        db_table = 'app_probjectinterview'


class AppProbjectlearningaction(models.Model):
    Id = models.AutoField(db_column='Id', primary_key=True)  # Field name made lowercase.
    CompanyId = models.IntegerField(db_column='CompanyId', default=0)  # Field name made lowercase.
    ProbjectId = models.IntegerField(db_column='ProbjectId')  # Field name made lowercase.
    User_Id = models.IntegerField(db_column='User_Id')  # Field name made lowercase.
    Coach_Id = models.IntegerField(db_column='Coach_Id', default=0)  # Field name made lowercase.
    Title = models.CharField(db_column='Title', max_length=250)  # Field name made lowercase.
    LearningPlan = models.TextField(db_column='LearningPlan', blank=True, null=True)  # Field name made lowercase.
    LearningFilePath = models.CharField(db_column='LearningFilePath', max_length=250, blank=True,
                                        null=True)  # Field name made lowercase.
    IsCompateLearningPlan = models.IntegerField(db_column='IsCompateLearningPlan', blank=True,
                                                null=True)  # Field name made lowercase.
    LearningReport = models.TextField(db_column='LearningReport', blank=True, null=True)  # Field name made lowercase.
    LearningReportReview = models.TextField(db_column='LearningReportReview', blank=True,
                                            null=True)  # Field name made lowercase.
    ActionPlan = models.TextField(db_column='ActionPlan', blank=True, null=True)  # Field name made lowercase.
    IsCompateActionPlan = models.IntegerField(db_column='IsCompateActionPlan', blank=True,
                                              null=True)  # Field name made lowercase.
    ActionReport = models.TextField(db_column='ActionReport', blank=True, null=True)  # Field name made lowercase.
    ActionReportReview = models.TextField(db_column='ActionReportReview', blank=True,
                                          null=True)  # Field name made lowercase.
    CreateDate = models.DateTimeField(db_column='CreateDate', auto_now_add=True)  # Field name made lowercase.
    Creator = models.CharField(db_column='Creator', max_length=50, blank=True, null=True)  # Field name made lowercase.
    ModifyDate = models.DateTimeField(db_column='ModifyDate', auto_now=True)  # Field name made lowercase.
    Modifier = models.CharField(db_column='Modifier', max_length=50, blank=True,
                                null=True)  # Field name made lowercase.
    ResId = models.IntegerField(db_column='ResId', blank=True, null=True)  # Field name made lowercase.
    interviewId = models.IntegerField(db_column='interviewId', blank=True, null=True)  # Field name made lowercase.

    class Meta:
        db_table = 'app_probjectlearningaction'


class AppProbjectneed(models.Model):
    Id = models.AutoField(db_column='Id', primary_key=True)  # Field name made lowercase.
    CompanyId = models.IntegerField(db_column='CompanyId', blank=True, null=True)  # Field name made lowercase.
    ProbjectId = models.IntegerField(db_column='ProbjectId')  # Field name made lowercase.
    UserId = models.IntegerField(db_column='UserId')  # Field name made lowercase.
    CreateDate = models.DateTimeField(db_column='CreateDate', auto_now_add=True)  # Field name made lowercase.
    Creator = models.CharField(db_column='Creator', max_length=50, blank=True, null=True)  # Field name made lowercase.
    ModifyDate = models.DateTimeField(db_column='ModifyDate', auto_now=True)  # Field name made lowercase.
    Modifier = models.CharField(db_column='Modifier', max_length=50, blank=True,
                                null=True)  # Field name made lowercase.
    sex = models.IntegerField(blank=True, null=True)
    ages = models.IntegerField(blank=True, null=True)
    education = models.CharField(max_length=50, blank=True, null=True)
    jlzz = models.TextField(blank=True, null=True)
    jlzzother = models.TextField(blank=True, null=True)
    cynx = models.IntegerField(blank=True, null=True)
    xyjb = models.TextField(blank=True, null=True)
    companyattr = models.TextField(blank=True, null=True)
    companyattrother = models.TextField(blank=True, null=True)
    jlxssOne = models.IntegerField(db_column='jlxssOne', blank=True, null=True)  # Field name made lowercase.
    jlxssTearm = models.IntegerField(db_column='jlxssTearm', blank=True, null=True)  # Field name made lowercase.
    jlhy = models.TextField(blank=True, null=True)
    jlhyother = models.TextField(blank=True, null=True)
    objective = models.TextField(blank=True, null=True)
    objectiveother = models.TextField(blank=True, null=True)
    jlqyhy = models.TextField(blank=True, null=True)
    jlzw = models.TextField(blank=True, null=True)
    jlzwother = models.TextField(blank=True, null=True)
    jlgzhy = models.TextField(blank=True, null=True)
    jlgzhyother = models.TextField(blank=True, null=True)
    jlqyhyother = models.TextField(blank=True, null=True)

    class Meta:
        db_table = 'app_probjectneed'


class AppProbjectobjective(models.Model):
    Id = models.AutoField(db_column='Id', primary_key=True)  # Field name made lowercase.
    CompanyId = models.IntegerField(db_column='CompanyId', blank=True, null=True)  # Field name made lowercase.
    ProbjectId = models.IntegerField(db_column='ProbjectId', blank=True, null=True)  # Field name made lowercase.
    UserId = models.IntegerField(db_column='UserId', blank=True, null=True)  # Field name made lowercase.
    Objectives = models.TextField(db_column='Objectives', blank=True, null=True)  # Field name made lowercase.
    PlanTime = models.CharField(db_column='PlanTime', max_length=50, blank=True,
                                null=True)  # Field name made lowercase.
    studyPlan = models.TextField(db_column='studyPlan', blank=True, null=True)  # Field name made lowercase.
    pxObjectives = models.TextField(db_column='pxObjectives', blank=True, null=True)  # Field name made lowercase.
    CoachId = models.IntegerField(db_column='CoachId')  # Field name made lowercase.
    CreateDate = models.DateTimeField(db_column='CreateDate', auto_now_add=True)  # Field name made lowercase.
    Creator = models.CharField(db_column='Creator', max_length=50, blank=True, null=True)  # Field name made lowercase.
    ModifyDate = models.DateTimeField(db_column='ModifyDate', auto_now=True)  # Field name made lowercase.
    Modifier = models.CharField(db_column='Modifier', max_length=50, blank=True,
                                null=True)  # Field name made lowercase.

    class Meta:
        db_table = 'app_probjectobjective'


class AppProbjectrelation(models.Model):
    Id = models.AutoField(db_column='Id', primary_key=True)  # Field name made lowercase.
    CompanyId = models.IntegerField(db_column='CompanyId', blank=True, null=True)  # Field name made lowercase.
    ProbjectId = models.IntegerField(db_column='ProbjectId', blank=True, null=True)  # Field name made lowercase.
    UserId = models.IntegerField(db_column='UserId', blank=True, null=True)  # Field name made lowercase.
    Objectives = models.TextField(db_column='Objectives', blank=True, null=True)  # Field name made lowercase.
    PlanTime = models.CharField(db_column='PlanTime', max_length=50, blank=True,
                                null=True)  # Field name made lowercase.
    studyPlan = models.TextField(db_column='studyPlan', blank=True, null=True)  # Field name made lowercase.
    CoachId = models.IntegerField(db_column='CoachId')  # Field name made lowercase.
    IsManger = models.IntegerField(db_column='IsManger', blank=True, null=True)  # Field name made lowercase.
    RolesId = models.IntegerField(db_column='RolesId', blank=True, null=True)  # Field name made lowercase.
    CreateDate = models.DateTimeField(db_column='CreateDate', auto_now_add=True)  # Field name made lowercase.
    Creator = models.CharField(db_column='Creator', max_length=50, blank=True, null=True)  # Field name made lowercase.
    ModifyDate = models.DateTimeField(db_column='ModifyDate', auto_now=True)  # Field name made lowercase.
    Modifier = models.CharField(db_column='Modifier', max_length=50, blank=True,
                                null=True)  # Field name made lowercase.
    Interested_Id = models.TextField(db_column='Interested_Id', blank=True, null=True)  # Field name made lowercase.
    ProjectObjectives = models.TextField(db_column='ProjectObjectives', blank=True,
                                         null=True)  # Field name made lowercase.
    jlppcs = models.IntegerField(blank=True, null=True)

    class Meta:
        db_table = 'app_probjectrelation'


class AppProbjectreport(models.Model):
    Id = models.AutoField(db_column='Id', primary_key=True)  # Field name made lowercase.
    CompanyId = models.IntegerField(db_column='CompanyId', default=0)  # Field name made lowercase.
    ProbjectId = models.IntegerField(db_column='ProbjectId')  # Field name made lowercase.
    Title = models.CharField(db_column='Title', max_length=250)  # Field name made lowercase.
    Remark = models.TextField(db_column='Remark', blank=True, null=True)  # Field name made lowercase.
    Json = models.TextField(db_column='Json', blank=True, null=True)  # Field name made lowercase.
    Pdf = models.CharField(db_column='Pdf', max_length=250, blank=True, null=True)  # Field name made lowercase.
    Enable = models.IntegerField(db_column='Enable', blank=True, null=True)  # Field name made lowercase.
    CreateDate = models.DateTimeField(db_column='CreateDate', auto_now_add=True)  # Field name made lowercase.
    Creator = models.CharField(db_column='Creator', max_length=50, blank=True, null=True)  # Field name made lowercase.
    ModifyDate = models.DateTimeField(db_column='ModifyDate', auto_now=True)  # Field name made lowercase.
    Modifier = models.CharField(db_column='Modifier', max_length=50, blank=True,
                                null=True)  # Field name made lowercase.
    reporttype = models.CharField(max_length=100, blank=True, null=True)
    status = models.IntegerField(default=0, help_text='之前没有用这字段，LBI报告新定义状态 0: 默认状态, 1:已生成(生成), 2:已推送')
    starttime = models.DateTimeField(blank=True, null=True)
    endtime = models.DateTimeField(blank=True, null=True)
    otherjson = models.TextField(blank=True, null=True)
    pici = models.CharField(max_length=100, blank=True, null=True)
    # advice = models.TextField(null=True, blank=True, help_text='LBI报告发展建议')

    class Meta:
        db_table = 'app_probjectreport'


class AppProbjectsatisfaction(models.Model):
    Id = models.AutoField(db_column='Id', primary_key=True)  # Field name made lowercase.
    CompanyId = models.IntegerField(db_column='CompanyId', default=0)  # Field name made lowercase.
    ProbjectId = models.IntegerField(db_column='ProbjectId')  # Field name made lowercase.
    User_Id = models.IntegerField(db_column='User_Id', blank=True, null=True)  # Field name made lowercase.
    ProbjectRank = models.FloatField(db_column='ProbjectRank', blank=True, null=True)  # Field name made lowercase.
    CocahRank = models.FloatField(db_column='CocahRank', blank=True, null=True)  # Field name made lowercase.
    GrowthRank = models.FloatField(db_column='GrowthRank', blank=True, null=True)  # Field name made lowercase.
    InvestmentRank = models.FloatField(db_column='InvestmentRank', blank=True, null=True)  # Field name made lowercase.
    CreateDate = models.DateTimeField(db_column='CreateDate', auto_now_add=True)  # Field name made lowercase.
    Creator = models.CharField(db_column='Creator', max_length=50, blank=True, null=True)  # Field name made lowercase.
    ModifyDate = models.DateTimeField(db_column='ModifyDate', auto_now=True)  # Field name made lowercase.
    Modifier = models.CharField(db_column='Modifier', max_length=50, blank=True,
                                null=True)  # Field name made lowercase.
    interviewId = models.IntegerField(db_column='interviewId', blank=True, null=True)  # Field name made lowercase.
    userType = models.IntegerField(db_column='userType', blank=True, null=True)  # Field name made lowercase.
    subject = models.TextField(blank=True, null=True)
    modelobser = models.TextField(blank=True, null=True)
    actionplan = models.TextField(blank=True, null=True)
    times = models.FloatField(blank=True, null=True)
    A1 = models.TextField(db_column='A1', blank=True, null=True)  # Field name made lowercase.
    A2 = models.TextField(db_column='A2', blank=True, null=True)  # Field name made lowercase.
    A3 = models.TextField(db_column='A3', blank=True, null=True)  # Field name made lowercase.
    A4 = models.TextField(db_column='A4', blank=True, null=True)  # Field name made lowercase.
    A5 = models.TextField(db_column='A5', blank=True, null=True)  # Field name made lowercase.
    A6 = models.TextField(db_column='A6', blank=True, null=True)  # Field name made lowercase.
    A7 = models.TextField(db_column='A7', blank=True, null=True)  # Field name made lowercase.
    A8 = models.TextField(db_column='A8', blank=True, null=True)  # Field name made lowercase.
    ModelCode = models.CharField(max_length=50, blank=True, null=True)

    class Meta:
        db_table = 'app_probjectsatisfaction'


class AppProbjectsetting(models.Model):
    ProbjectSetting_Id = models.AutoField(db_column='ProbjectSetting_Id',
                                          primary_key=True)  # Field name made lowercase.
    Probject_Id = models.IntegerField(db_column='Probject_Id')  # Field name made lowercase.
    KeyName = models.CharField(db_column='KeyName', max_length=50)  # Field name made lowercase.
    KeyValue = models.CharField(db_column='KeyValue', max_length=200, blank=True)  # Field name made lowercase.
    KeyJson = models.TextField(db_column='KeyJson', blank=True, null=True)  # Field name made lowercase.
    Remark = models.CharField(db_column='Remark', max_length=200, blank=True, null=True)  # Field name made lowercase.
    CreateDate = models.DateTimeField(db_column='CreateDate', auto_now_add=True)  # Field name made lowercase.
    Creator = models.CharField(db_column='Creator', max_length=50, blank=True, null=True)  # Field name made lowercase.
    ModifyDate = models.DateTimeField(db_column='ModifyDate', auto_now=True)  # Field name made lowercase.
    Modifier = models.CharField(db_column='Modifier', max_length=50, blank=True,
                                null=True)  # Field name made lowercase.

    class Meta:
        db_table = 'app_probjectsetting'


class AppProbjectsettingcoach(models.Model):
    Id = models.AutoField(db_column='Id', primary_key=True)  # Field name made lowercase.
    Probject_Id = models.IntegerField(db_column='Probject_Id')  # Field name made lowercase.
    KeyName = models.CharField(db_column='KeyName', max_length=50)  # Field name made lowercase.
    KeyValue = models.CharField(db_column='KeyValue', max_length=200, blank=True)  # Field name made lowercase.
    Enable = models.IntegerField(db_column='Enable', blank=True, null=True)  # Field name made lowercase.
    Remark = models.CharField(db_column='Remark', max_length=200, blank=True, null=True)  # Field name made lowercase.
    CreateDate = models.DateTimeField(db_column='CreateDate', auto_now_add=True)  # Field name made lowercase.
    Creator = models.CharField(db_column='Creator', max_length=50, blank=True, null=True)  # Field name made lowercase.
    ModifyDate = models.DateTimeField(db_column='ModifyDate', auto_now=True)  # Field name made lowercase.
    Modifier = models.CharField(db_column='Modifier', max_length=50, blank=True,
                                null=True)  # Field name made lowercase.

    class Meta:
        db_table = 'app_probjectsettingcoach'


class AppProbjectsettingcoachneed(models.Model):
    Id = models.AutoField(db_column='Id', primary_key=True)  # Field name made lowercase.
    Probject_Id = models.IntegerField(db_column='Probject_Id')  # Field name made lowercase.
    KeyName = models.CharField(db_column='KeyName', max_length=50)  # Field name made lowercase.
    KeyValue = models.CharField(db_column='KeyValue', max_length=200, blank=True)  # Field name made lowercase.
    Enable = models.IntegerField(db_column='Enable', blank=True, null=True)  # Field name made lowercase.
    Remark = models.CharField(db_column='Remark', max_length=200, blank=True, null=True)  # Field name made lowercase.
    CreateDate = models.DateTimeField(db_column='CreateDate', auto_now_add=True)  # Field name made lowercase.
    Creator = models.CharField(db_column='Creator', max_length=50, blank=True, null=True)  # Field name made lowercase.
    ModifyDate = models.DateTimeField(db_column='ModifyDate', auto_now=True)  # Field name made lowercase.
    Modifier = models.CharField(db_column='Modifier', max_length=50, blank=True,
                                null=True)  # Field name made lowercase.

    class Meta:
        db_table = 'app_probjectsettingcoachneed'


class AppProbjectupability(models.Model):
    Id = models.AutoField(db_column='Id', primary_key=True)  # Field name made lowercase.
    CompanyId = models.IntegerField(db_column='CompanyId', default=0)  # Field name made lowercase.
    ProbjectId = models.IntegerField(db_column='ProbjectId')  # Field name made lowercase.
    User_Id = models.IntegerField(db_column='User_Id')  # Field name made lowercase.
    Times = models.IntegerField(db_column='Times', blank=True, null=True)  # Field name made lowercase.
    UpAbility = models.CharField(db_column='UpAbility', max_length=200, blank=True,
                                 null=True)  # Field name made lowercase.

    class Meta:
        db_table = 'app_probjectupability'


class AppProbjectuploadreport(models.Model):
    Id = models.AutoField(db_column='Id', primary_key=True)  # Field name made lowercase.
    CompanyId = models.IntegerField(db_column='CompanyId', blank=True, null=True)  # Field name made lowercase.
    ProbjectId = models.IntegerField(db_column='ProbjectId', blank=True, null=True)  # Field name made lowercase.
    UserId = models.IntegerField(db_column='UserId', blank=True, null=True)  # Field name made lowercase.
    FilePath = models.CharField(db_column='FilePath', max_length=200, blank=True,
                                null=True)  # Field name made lowercase.
    FileName = models.CharField(db_column='FileName', max_length=150, blank=True,
                                null=True)  # Field name made lowercase.
    FileType = models.IntegerField(db_column='FileType')  # Field name made lowercase.
    Views = models.IntegerField(db_column='Views', blank=True, null=True)  # Field name made lowercase.
    CreateDate = models.DateTimeField(db_column='CreateDate', auto_now_add=True)  # Field name made lowercase.
    Creator = models.CharField(db_column='Creator', max_length=50, blank=True, null=True)  # Field name made lowercase.
    ModifyDate = models.DateTimeField(db_column='ModifyDate', auto_now=True)  # Field name made lowercase.
    Modifier = models.CharField(db_column='Modifier', max_length=50, blank=True,
                                null=True)  # Field name made lowercase.
    Interested_Id = models.TextField(db_column='Interested_Id', blank=True, null=True)  # Field name made lowercase.

    class Meta:
        db_table = 'app_probjectuploadreport'


class AppMember(models.Model):
    Member_Id = models.AutoField(db_column='Member_Id', primary_key=True)  # Field name made lowercase.
    Company_Id = models.IntegerField(db_column='Company_Id', blank=True, null=True)  # Field name made lowercase.
    User_Id = models.IntegerField(db_column='User_Id', blank=True, null=True)  # Field name made lowercase.
    MessageId = models.CharField(db_column='MessageId', max_length=50, blank=True,
                                 null=True)  # Field name made lowercase.
    TrueName = models.CharField(db_column='TrueName', max_length=50, blank=True,
                                null=True)  # Field name made lowercase.
    Sex = models.IntegerField(db_column='Sex', blank=True, null=True)  # Field name made lowercase.
    Photograph = models.CharField(db_column='Photograph', max_length=250, blank=True,
                                  null=True)  # Field name made lowercase.
    Age = models.IntegerField(db_column='Age', blank=True, null=True)  # Field name made lowercase.
    birthday = models.DateField(blank=True, null=True)
    mobile = models.CharField(max_length=15, blank=True, null=True)
    email = models.CharField(max_length=150, blank=True, null=True)
    wechat = models.CharField(max_length=50, blank=True, null=True)
    Education = models.CharField(db_column='Education', max_length=150, blank=True,
                                 null=True)  # Field name made lowercase.
    School = models.CharField(db_column='School', max_length=150, blank=True, null=True)  # Field name made lowercase.
    GraduationDate = models.DateTimeField(db_column='GraduationDate', blank=True,
                                          null=True)  # Field name made lowercase.
    Major = models.CharField(db_column='Major', max_length=150, blank=True, null=True)  # Field name made lowercase.
    Degree = models.CharField(db_column='Degree', max_length=150, blank=True, null=True)  # Field name made lowercase.
    CompanyWorkingDate = models.DateTimeField(db_column='CompanyWorkingDate', blank=True,
                                              null=True)  # Field name made lowercase.
    GwWorkingDate = models.DateTimeField(db_column='GwWorkingDate', blank=True, null=True)  # Field name made lowercase.
    WorkingDate = models.DateTimeField(db_column='WorkingDate', blank=True, null=True)  # Field name made lowercase.
    Competency = models.CharField(db_column='Competency', max_length=250, blank=True,
                                  null=True)  # Field name made lowercase.
    Duty = models.CharField(db_column='Duty', max_length=50, blank=True, null=True)  # Field name made lowercase.
    Department = models.CharField(db_column='Department', max_length=50, blank=True,
                                  null=True)  # Field name made lowercase.
    ParentDepartment = models.CharField(db_column='ParentDepartment', max_length=50, blank=True,
                                        null=True)  # Field name made lowercase.
    Remark = models.TextField(db_column='Remark', blank=True, null=True)  # Field name made lowercase.
    ManagerRemark = models.TextField(db_column='ManagerRemark', blank=True, null=True)  # Field name made lowercase.
    CoachRemark = models.TextField(db_column='CoachRemark', blank=True, null=True)  # Field name made lowercase.
    Enable = models.IntegerField(db_column='Enable', blank=True, null=True)  # Field name made lowercase.
    CreateDate = models.DateTimeField(db_column='CreateDate', auto_now_add=True)  # Field name made lowercase.
    Creator = models.CharField(db_column='Creator', max_length=50, blank=True, null=True)  # Field name made lowercase.
    ModifyDate = models.DateTimeField(db_column='ModifyDate', auto_now=True)  # Field name made lowercase.
    Modifier = models.CharField(db_column='Modifier', max_length=50, blank=True,
                                null=True)  # Field name made lowercase.
    employees = models.IntegerField(blank=True, null=True)
    Interested_Id = models.IntegerField(db_column='Interested_Id', default=0)  # Field name made lowercase.
    Concertyears = models.IntegerField(db_column='Concertyears', blank=True, null=True)  # Field name made lowercase.
    Relation = models.CharField(db_column='Relation', max_length=100, blank=True,
                                null=True)  # Field name made lowercase.
    CompanyName = models.CharField(db_column='CompanyName', max_length=200, blank=True,
                                   null=True)  # Field name made lowercase.
    employee = models.IntegerField(blank=True, null=True, help_text='直系下属人数')
    address = models.CharField(max_length=200, blank=True, null=True)
    Tel = models.CharField(db_column='Tel', max_length=100, blank=True, null=True)  # Field name made lowercase.
    liaojie = models.CharField(max_length=200, blank=True, null=True)
    gzaddr = models.CharField(max_length=200, blank=True, null=True)
    qitzw = models.CharField(max_length=200, blank=True, null=True)
    Sendemail = models.IntegerField(db_column='Sendemail', blank=True, null=True)  # Field name made lowercase.
    Sendmsg = models.IntegerField(db_column='Sendmsg', blank=True, null=True)  # Field name made lowercase.
    SendWeixin = models.IntegerField(db_column='SendWeixin', blank=True, null=True)  # Field name made lowercase.
    UseInWxApplet = models.IntegerField(db_column='UseInWxApplet', blank=True, null=True)  # Field name made lowercase.
    SendPush = models.IntegerField(db_column='SendPush', blank=True, null=True)  # Field name made lowercase.
    manage_role = models.IntegerField(blank=True, null=True,
                                      help_text='管理角色 1:一线经理 2:部门经理 3:事业部经理 4:事业部总经理 5:集团高管 6:首席执行官')

    class Meta:
        db_table = 'app_member'


class AppMemberinterested(models.Model):
    Interested_Id = models.AutoField(db_column='Interested_Id', primary_key=True)  # Field name made lowercase.
    Member_Id = models.IntegerField(db_column='Member_Id', blank=True, null=True, help_text='利益相关者id')  # Field name made lowercase.
    MasterMember_Id = models.IntegerField(db_column='MasterMember_Id', blank=True, help_text='被教练者id',
                                          null=True)  # Field name made lowercase.
    Relation = models.CharField(db_column='Relation', max_length=50, blank=True, help_text='上下级关系',
                                null=True)  # Field name made lowercase.
    Concertyears = models.IntegerField(db_column='Concertyears', blank=True, null=True, help_text='合作年限')  # Field name made lowercase.
    Enable = models.IntegerField(db_column='Enable', blank=True, null=True)  # Field name made lowercase.
    CreateDate = models.DateTimeField(db_column='CreateDate', auto_now_add=True)  # Field name made lowercase.
    Creator = models.CharField(db_column='Creator', max_length=50, blank=True, null=True)  # Field name made lowercase.
    ModifyDate = models.DateTimeField(db_column='ModifyDate', auto_now=True)  # Field name made lowercase.
    Modifier = models.CharField(db_column='Modifier', max_length=50, blank=True,
                                null=True)  # Field name made lowercase.
    ProbjectId = models.IntegerField(db_column='ProbjectId', blank=True, null=True)  # Field name made lowercase.
    CoachId = models.IntegerField(db_column='CoachId', blank=True, null=True)  # Field name made lowercase.

    class Meta:
        db_table = 'app_memberinterested'


class AppCompany(models.Model):
    Company_Id = models.AutoField(db_column='Company_Id', primary_key=True)  # Field name made lowercase.
    CompanyName = models.CharField(db_column='CompanyName', max_length=250)  # Field name made lowercase.
    ShortName = models.CharField(db_column='ShortName', max_length=150, blank=True,
                                 null=True)  # Field name made lowercase.
    Logo = models.CharField(db_column='Logo', max_length=250, blank=True, null=True)  # Field name made lowercase.
    CompanyAttr = models.CharField(db_column='CompanyAttr', max_length=250)  # Field name made lowercase.
    Industry = models.CharField(db_column='Industry', max_length=250)  # Field name made lowercase.
    Personnels = models.CharField(db_column='Personnels', max_length=250)  # Field name made lowercase.
    Video = models.CharField(db_column='Video', max_length=250, blank=True, null=True)  # Field name made lowercase.
    website = models.CharField(max_length=250, blank=True, null=True)
    Instro = models.TextField(db_column='Instro', blank=True, null=True)  # Field name made lowercase.
    ParentId = models.IntegerField(db_column='ParentId', null=True)  # Field name made lowercase.
    OrderNo = models.IntegerField(db_column='OrderNo', blank=True, null=True)  # Field name made lowercase.
    Enable = models.IntegerField(db_column='Enable', blank=True, null=True)  # Field name made lowercase.
    CreateDate = models.DateTimeField(db_column='CreateDate', auto_now_add=True)  # Field name made lowercase.
    Creator = models.CharField(db_column='Creator', max_length=50, blank=True, null=True)  # Field name made lowercase.
    ModifyDate = models.DateTimeField(db_column='ModifyDate', auto_now=True)  # Field name made lowercase.
    Modifier = models.CharField(db_column='Modifier', max_length=50, blank=True,
                                null=True)  # Field name made lowercase.
    Manage_Id = models.IntegerField(db_column='Manage_Id', blank=True, null=True)  # Field name made lowercase.

    class Meta:
        db_table = 'app_company'


class Projectcoach(models.Model):
    Id = models.AutoField(primary_key=True)  # Field name made lowercase.
    ProbjectId = models.IntegerField()
    UserId = models.IntegerField(null=True)
    CoachId = models.IntegerField()
    Matching = models.FloatField(null=True)  # Field name made lowercase.
    Status = models.IntegerField(null=True)  # Field name made lowercase.
    CreateDate = models.DateTimeField(auto_now_add=True)  # Field name made lowercase.
    Creator = models.CharField(max_length=50, null=True)  # Field name made lowercase.
    ModifyDate = models.DateTimeField(auto_now=True)  # Field name made lowercase.
    Modifier = models.CharField(max_length=50, null=True)  # Field name made lowercase.
    MatchingTimes = models.IntegerField(null=True)  # Field name made lowercase.

    class Meta:
        db_table = 'app_probjectcoach'


class ProjectCoachInterview(models.Model):
    Id = models.AutoField(primary_key=True)
    ProbjectId = models.IntegerField()
    UserId = models.IntegerField(null=True)
    CoachId = models.IntegerField()
    PassReason = models.TextField(null=True, blank=True)
    NoPassReason = models.TextField(null=True, blank=True)
    Status = models.IntegerField(null=True)
    CreateDate = models.DateTimeField(auto_now_add=True)
    ModifyDate = models.DateTimeField(auto_now=True)
    Creator = models.CharField(max_length=50, null=True, blank=True)
    Modifier = models.CharField(max_length=50, null=True, blank=True)
    interviewTime = models.DateTimeField(null=True)
    interviewId = models.IntegerField(null=True)
    iscomplate = models.IntegerField(null=True)
    StudentStatus = models.IntegerField(null=True, help_text='0: 未评价 1: 通过 2: 待定 3:不合适')
    StudentinterviewTime = models.DateTimeField(null=True)
    StudentinterviewId = models.IntegerField(null=True)
    Studentiscomplate = models.IntegerField(default=0, null=True)
    StudentUserId = models.IntegerField(null=True)
    StudentPassReason = models.TextField(null=True, blank=True)
    StudentNoPassReason = models.TextField(null=True, blank=True)
    qypici = models.CharField(max_length=100, null=True)
    hxpici = models.CharField(max_length=100, null=True)
    hxjobstatus = models.IntegerField(default=0)  # 化学任务
    qyjobstatus = models.IntegerField(default=0)  # 企业面试任务

    class Meta:
        db_table = 'app_probjectcoachinterview'


class AppCacheTmpResult(models.Model):
    Id = models.AutoField(db_column='Id', primary_key=True)  # Field name made lowercase.
    KeyName = models.CharField(max_length=100, blank=True, null=True)  # Field name made lowercase.
    KeyVal = models.TextField(blank=True, null=True)  # Field name made lowercase.
    Enable = models.IntegerField(db_column='Enable', blank=True, null=True)  # Field name made lowercase.
    CreateDate = models.DateTimeField(db_column='CreateDate', auto_now_add=True)  # Field name made lowercase.
    Creator = models.CharField(db_column='Creator', max_length=50, blank=True, null=True)  # Field name made lowercase.
    ModifyDate = models.DateTimeField(db_column='ModifyDate', auto_now=True)  # Field name made lowercase.
    Modifier = models.CharField(db_column='Modifier', max_length=50, blank=True,
                                null=True)  # Field name made lowercase.

    class Meta:
        db_table = 'app_cachetmpresult'


class AppCoach(models.Model):
    CoachId = models.AutoField(db_column='CoachId', primary_key=True)  # Field name made lowercase.
    User_Id = models.IntegerField(db_column='User_Id', blank=True, null=True)  # Field name made lowercase.
    CoachLevel = models.CharField(db_column='CoachLevel', max_length=50, blank=True,
                                  null=True)  # Field name made lowercase.
    TrueName = models.CharField(db_column='TrueName', max_length=50, blank=True,
                                null=True)  # Field name made lowercase.
    Photograph = models.CharField(db_column='Photograph', max_length=250, blank=True,
                                  null=True)  # Field name made lowercase.
    englistname = models.CharField(max_length=50, blank=True, null=True)
    Age = models.IntegerField(db_column='Age', blank=True, null=True)  # Field name made lowercase.
    birthday = models.DateField(blank=True, null=True)
    mobile = models.CharField(max_length=15, blank=True, null=True)
    email = models.CharField(max_length=150, blank=True, null=True)
    Sex = models.IntegerField(db_column='Sex', blank=True, null=True)  # Field name made lowercase.
    industry = models.CharField(max_length=50, blank=True, null=True)
    IsOpen = models.IntegerField(db_column='IsOpen', blank=True, null=True)  # Field name made lowercase.
    InterviewType = models.CharField(db_column='InterviewType', max_length=250, blank=True,
                                     null=True)  # Field name made lowercase.
    MessageId = models.CharField(db_column='MessageId', max_length=50, blank=True,
                                 null=True)  # Field name made lowercase.
    Wechat = models.CharField(db_column='Wechat', max_length=20, blank=True, null=True)  # Field name made lowercase.
    QQ = models.CharField(db_column='QQ', max_length=20, blank=True, null=True)  # Field name made lowercase.
    Skype = models.CharField(db_column='Skype', max_length=20, blank=True, null=True)  # Field name made lowercase.
    Timezone = models.CharField(db_column='Timezone', max_length=20, blank=True,
                                null=True)  # Field name made lowercase.
    WebLang = models.CharField(db_column='WebLang', max_length=20, blank=True, null=True)  # Field name made lowercase.
    CoachLang = models.CharField(db_column='CoachLang', max_length=20, blank=True,
                                 null=True)  # Field name made lowercase.
    Sendemail = models.IntegerField(db_column='Sendemail', blank=True, null=True)  # Field name made lowercase.
    Sendmsg = models.IntegerField(db_column='Sendmsg', blank=True, null=True)  # Field name made lowercase.
    summary = models.TextField(blank=True, null=True)
    careerthinking = models.TextField(blank=True, null=True)
    experiencehighlights = models.TextField(blank=True, null=True)
    coachstyle = models.TextField(blank=True, null=True)
    LastReadbooks = models.TextField(db_column='LastReadbooks', blank=True, null=True)  # Field name made lowercase.
    gratefulthings = models.TextField(blank=True, null=True)
    fulfillingthings = models.TextField(blank=True, null=True)
    cynx = models.IntegerField(blank=True, null=True)
    xyjb = models.TextField(blank=True, null=True)
    qysx = models.TextField(blank=True, null=True)
    jlzz = models.TextField(blank=True, null=True)
    jlhy = models.TextField(blank=True, null=True)
    jlzw = models.TextField(blank=True, null=True)
    jljb = models.TextField(blank=True, null=True)
    scly = models.TextField(blank=True, null=True)
    Source = models.IntegerField(db_column='Source', blank=True, null=True)  # Field name made lowercase.
    Manage_Id = models.IntegerField(db_column='Manage_Id', blank=True, null=True)  # Field name made lowercase.
    jlxssOne = models.IntegerField(db_column='jlxssOne', blank=True, null=True)  # Field name made lowercase.
    jlxssTearm = models.IntegerField(db_column='jlxssTearm', blank=True, null=True)  # Field name made lowercase.
    jlgzhy = models.TextField(blank=True, null=True)
    kcrz = models.TextField(blank=True, null=True)
    cprz = models.TextField(blank=True, null=True)
    xlzxrz = models.TextField(blank=True, null=True)
    pxsrz = models.TextField(blank=True, null=True)
    qitrz = models.TextField(blank=True, null=True)
    education = models.CharField(max_length=50, blank=True, null=True)
    school = models.TextField(blank=True, null=True)
    IDCard = models.CharField(db_column='IDCard', max_length=50, blank=True, null=True)  # Field name made lowercase.
    Passport = models.CharField(db_column='Passport', max_length=50, blank=True,
                                null=True)  # Field name made lowercase.
    Address = models.CharField(db_column='Address', max_length=150, blank=True, null=True)  # Field name made lowercase.
    cerfile = models.TextField(blank=True, null=True)
    gzjl = models.TextField(blank=True, null=True)
    outlook = models.TextField(blank=True, null=True)
    endorsement = models.TextField(blank=True, null=True)
    ManagerMark = models.TextField(db_column='ManagerMark', blank=True, null=True)  # Field name made lowercase.
    invoice = models.TextField(blank=True, null=True)
    overallIntroduction = models.TextField(db_column='overallIntroduction', blank=True,
                                           null=True)  # Field name made lowercase.
    SendWeixin = models.IntegerField(db_column='SendWeixin', blank=True, null=True)  # Field name made lowercase.
    UseInWxApplet = models.IntegerField(db_column='UseInWxApplet', blank=True, null=True)  # Field name made lowercase.
    SendPush = models.IntegerField(db_column='SendPush', blank=True, null=True)  # Field name made lowercase.
    jlzzother = models.TextField(blank=True, null=True)
    companyattrother = models.TextField(blank=True, null=True)
    jlhyother = models.TextField(blank=True, null=True)
    objectiveother = models.TextField(blank=True, null=True)
    jlzwother = models.TextField(blank=True, null=True)
    jlgzhyother = models.TextField(blank=True, null=True)
    jlqyhyother = models.TextField(blank=True, null=True)
    xyjbother = models.TextField(blank=True, null=True)
    openday = models.IntegerField(blank=True, null=True)

    class Meta:
        db_table = 'app_coach'


class AppCoachrate(models.Model):
    Id = models.AutoField(db_column='Id', primary_key=True)  # Field name made lowercase.
    CompanyId = models.IntegerField(db_column='CompanyId', blank=True, null=True)  # Field name made lowercase.
    ProbjectId = models.IntegerField(db_column='ProbjectId')  # Field name made lowercase.
    UserId = models.IntegerField(db_column='UserId')  # Field name made lowercase.
    Rank = models.IntegerField(db_column='Rank', blank=True, null=True)  # Field name made lowercase.
    reView = models.TextField(db_column='reView', blank=True, null=True)  # Field name made lowercase.
    Replay = models.TextField(db_column='Replay', blank=True, null=True)  # Field name made lowercase.
    Enable = models.IntegerField(db_column='Enable', blank=True, null=True)  # Field name made lowercase.
    CreateDate = models.DateTimeField(db_column='CreateDate', auto_now_add=True)  # Field name made lowercase.
    Creator = models.CharField(db_column='Creator', max_length=50, blank=True, null=True)  # Field name made lowercase.
    ModifyDate = models.DateTimeField(db_column='ModifyDate', auto_now=True)  # Field name made lowercase.
    Modifier = models.CharField(db_column='Modifier', max_length=50, blank=True,
                                null=True)  # Field name made lowercase.
    CoachId = models.IntegerField(db_column='CoachId', blank=True, null=True)  # Field name made lowercase.

    class Meta:
        db_table = 'app_coachrate'


class AppCoachschedule(models.Model):
    ScheduleId = models.AutoField(db_column='ScheduleId', primary_key=True)  # Field name made lowercase.
    TypeId = models.IntegerField(db_column='TypeId', blank=True, null=True)  # Field name made lowercase.
    UserId = models.IntegerField(db_column='UserId', blank=True, null=True)  # Field name made lowercase.
    CoachId = models.IntegerField(db_column='CoachId')  # Field name made lowercase.
    Title = models.CharField(db_column='Title', max_length=250, blank=True, null=True)  # Field name made lowercase.
    Starttime = models.DateTimeField(db_column='Starttime')  # Field name made lowercase.
    EndTime = models.DateTimeField(db_column='EndTime')  # Field name made lowercase.
    InterViewId = models.IntegerField(db_column='InterViewId', blank=True, null=True)  # Field name made lowercase.
    Enable = models.IntegerField(db_column='Enable', blank=True, null=True)  # Field name made lowercase.
    CreateDate = models.DateTimeField(db_column='CreateDate', auto_now_add=True)  # Field name made lowercase.
    Creator = models.CharField(db_column='Creator', max_length=50, blank=True, null=True)  # Field name made lowercase.
    ModifyDate = models.DateTimeField(db_column='ModifyDate', auto_now=True)  # Field name made lowercase.
    Modifier = models.CharField(db_column='Modifier', max_length=50, blank=True,
                                null=True)  # Field name made lowercase.
    remark = models.TextField(blank=True, null=True)
    public = models.BooleanField(default=True) # admin can access remark
    platform = models.IntegerField(default=2, null=True, help_text='区分线上平台和移动端 1:移动端 2:线上平台')
    schedule_id = models.IntegerField(null=True, help_text='如果是移动端，这个id就是移动端的schedule_id，否则为空')

    class Meta:
        db_table = 'app_coachschedule'


class AppGrowthdiary(models.Model):
    NoteId = models.AutoField(db_column='NoteId', primary_key=True)  # Field name made lowercase.
    ProbjectId = models.IntegerField(db_column='ProbjectId')  # Field name made lowercase.
    User_Id = models.IntegerField(db_column='User_Id')  # Field name made lowercase.
    Coach_Id = models.IntegerField(db_column='Coach_Id')  # Field name made lowercase.
    Title = models.CharField(db_column='Title', max_length=250)  # Field name made lowercase.
    Remark = models.TextField(db_column='Remark', blank=True, null=True)  # Field name made lowercase.
    isPublic = models.IntegerField(db_column='isPublic', blank=True, null=True)  # Field name made lowercase.
    CreateDate = models.DateTimeField(db_column='CreateDate', auto_now_add=True)  # Field name made lowercase.
    Creator = models.CharField(db_column='Creator', max_length=50, blank=True, null=True)  # Field name made lowercase.
    ModifyDate = models.DateTimeField(db_column='ModifyDate', auto_now=True)  # Field name made lowercase.
    Modifier = models.CharField(db_column='Modifier', max_length=50, blank=True,
                                null=True)  # Field name made lowercase.

    class Meta:
        db_table = 'app_growthdiary'


class AppHxmeetingdata(models.Model):
    Id = models.AutoField(db_column='Id', primary_key=True)  # Field name made lowercase.
    TypeId = models.IntegerField(db_column='TypeId', blank=True, null=True)  # Field name made lowercase.
    InterViewId = models.IntegerField(db_column='InterViewId', blank=True, null=True)  # Field name made lowercase.
    PageCode = models.CharField(db_column='PageCode', max_length=50, blank=True,
                                null=True)  # Field name made lowercase.
    HxData = models.TextField(db_column='HxData', blank=True, null=True)  # Field name made lowercase.
    Enable = models.IntegerField(db_column='Enable', blank=True, null=True)  # Field name made lowercase.
    CreateDate = models.DateTimeField(db_column='CreateDate', auto_now_add=True)  # Field name made lowercase.
    Creator = models.CharField(db_column='Creator', max_length=50, blank=True, null=True)  # Field name made lowercase.
    ModifyDate = models.DateTimeField(db_column='ModifyDate', auto_now=True)  # Field name made lowercase.
    Modifier = models.CharField(db_column='Modifier', max_length=50, blank=True,
                                null=True)  # Field name made lowercase.

    class Meta:
        db_table = 'app_hxmeetingdata'


class AppScheduleinfo(models.Model):
    ScheduleId = models.AutoField(db_column='ScheduleId', primary_key=True)  # Field name made lowercase.
    JobGroup = models.CharField(db_column='JobGroup', max_length=200, blank=True,
                                null=True)  # Field name made lowercase.
    JobName = models.CharField(db_column='JobName', max_length=200)  # Field name made lowercase.
    RunStatus = models.IntegerField(db_column='RunStatus')  # Field name made lowercase.
    CronExpress = models.CharField(db_column='CronExpress', max_length=200, blank=True,
                                   null=True)  # Field name made lowercase.
    StarRunTime = models.DateTimeField(db_column='StarRunTime')  # Field name made lowercase.
    EndRunTime = models.DateTimeField(db_column='EndRunTime')  # Field name made lowercase.
    NextRunTime = models.DateTimeField(db_column='NextRunTime', blank=True, null=True)  # Field name made lowercase.
    AllowConcurrent = models.IntegerField(db_column='AllowConcurrent', blank=True,
                                          null=True)  # Field name made lowercase.
    RunImmediately = models.IntegerField(db_column='RunImmediately', blank=True,
                                         null=True)  # Field name made lowercase.
    AutoEnable = models.IntegerField(db_column='AutoEnable', blank=True, null=True)  # Field name made lowercase.
    Token = models.CharField(db_column='Token', max_length=200, blank=True, null=True)  # Field name made lowercase.
    AppID = models.CharField(db_column='AppID', max_length=100, blank=True, null=True)  # Field name made lowercase.
    ServiceCode = models.CharField(db_column='ServiceCode', max_length=200, blank=True,
                                   null=True)  # Field name made lowercase.
    InterfaceCode = models.CharField(db_column='InterfaceCode', max_length=200, blank=True,
                                     null=True)  # Field name made lowercase.
    TaskDescription = models.TextField(db_column='TaskDescription', blank=True, null=True)  # Field name made lowercase.
    Enable = models.IntegerField(db_column='Enable', blank=True, null=True)  # Field name made lowercase.
    CreateDate = models.DateTimeField(db_column='CreateDate', auto_now_add=True)  # Field name made lowercase.
    Creator = models.CharField(db_column='Creator', max_length=50, blank=True, null=True)  # Field name made lowercase.
    ModifyDate = models.DateTimeField(db_column='ModifyDate', auto_now=True)  # Field name made lowercase.
    Modifier = models.CharField(db_column='Modifier', max_length=50, blank=True,
                                null=True)  # Field name made lowercase.

    class Meta:
        db_table = 'app_scheduleinfo'


class AppSurvey(models.Model):
    SurveyId = models.AutoField(db_column='SurveyId', primary_key=True)  # Field name made lowercase.
    PageCode = models.CharField(db_column='PageCode', max_length=100, blank=True,
                                null=True)  # Field name made lowercase.
    Title = models.CharField(db_column='Title', max_length=255)  # Field name made lowercase.
    TypeId = models.IntegerField(db_column='TypeId', blank=True, null=True)  # Field name made lowercase.
    ImgPath = models.CharField(db_column='ImgPath', max_length=255, blank=True, null=True)  # Field name made lowercase.
    Remark = models.CharField(db_column='Remark', max_length=255, blank=True, null=True)  # Field name made lowercase.
    Status = models.IntegerField(db_column='Status', blank=True, null=True)  # Field name made lowercase.
    MoreVote = models.IntegerField(db_column='MoreVote', blank=True, null=True)  # Field name made lowercase.
    IsCode = models.IntegerField(db_column='IsCode', blank=True, null=True)  # Field name made lowercase.
    CreateDate = models.DateTimeField(db_column='CreateDate', auto_now_add=True)  # Field name made lowercase.
    Creator = models.CharField(db_column='Creator', max_length=50, blank=True, null=True)  # Field name made lowercase.
    ModifyDate = models.DateTimeField(db_column='ModifyDate', auto_now=True)  # Field name made lowercase.
    Modifier = models.CharField(db_column='Modifier', max_length=50, blank=True,
                                null=True)  # Field name made lowercase.

    class Meta:
        db_table = 'app_survey'


class AppSurveyanswers(models.Model):
    Id = models.AutoField(db_column='Id', primary_key=True)  # Field name made lowercase.
    ProbjectId = models.IntegerField(db_column='ProbjectId', blank=True, null=True)  # Field name made lowercase.
    App_SurveyId = models.IntegerField(db_column='App_SurveyId', blank=True, null=True)  # Field name made lowercase.
    SubjectId = models.IntegerField(db_column='SubjectId', blank=True, null=True)  # Field name made lowercase.
    UserId = models.BigIntegerField(db_column='UserId')  # Field name made lowercase.
    TestId = models.IntegerField(db_column='TestId', blank=True, null=True)  # Field name made lowercase.
    Answers = models.CharField(db_column='Answers', max_length=255, blank=True, null=True)  # Field name made lowercase.
    CreateTime = models.DateTimeField(db_column='CreateTime', blank=True, null=True)  # Field name made lowercase.
    Ip = models.CharField(db_column='Ip', max_length=255, blank=True, null=True)  # Field name made lowercase.
    pici = models.CharField(max_length=50, blank=True, null=True)

    class Meta:
        db_table = 'app_surveyanswers'


class AppSurveyoption(models.Model):
    Id = models.AutoField(db_column='Id', primary_key=True)  # Field name made lowercase.
    SubId = models.IntegerField(db_column='SubId', blank=True, null=True)  # Field name made lowercase.
    Title = models.CharField(db_column='Title', max_length=255)  # Field name made lowercase.
    Sort = models.IntegerField(db_column='Sort', blank=True, null=True)  # Field name made lowercase.
    ImgPath = models.CharField(db_column='ImgPath', max_length=255, blank=True, null=True)  # Field name made lowercase.

    class Meta:
        db_table = 'app_surveyoption'


class AppSurveysubject(models.Model):
    SubId = models.AutoField(db_column='SubId', primary_key=True)  # Field name made lowercase.
    SurveyId = models.IntegerField(db_column='SurveyId', blank=True, null=True)  # Field name made lowercase.
    TypeId = models.IntegerField(db_column='TypeId', blank=True, null=True)  # Field name made lowercase.
    Title = models.CharField(db_column='Title', max_length=255)  # Field name made lowercase.
    Sort = models.IntegerField(db_column='Sort', blank=True, null=True)  # Field name made lowercase.
    CreateDate = models.DateTimeField(db_column='CreateDate', auto_now_add=True)  # Field name made lowercase.
    Creator = models.CharField(db_column='Creator', max_length=50, blank=True, null=True)  # Field name made lowercase.
    ModifyDate = models.DateTimeField(db_column='ModifyDate', auto_now=True)  # Field name made lowercase.
    Modifier = models.CharField(db_column='Modifier', max_length=50, blank=True,
                                null=True)  # Field name made lowercase.
    Enable = models.CharField(db_column='Enable', max_length=10, blank=True, null=True)  # Field name made lowercase.
    MaxNum = models.IntegerField(db_column='MaxNum', blank=True, null=True)  # Field name made lowercase.
    isOtherInput = models.IntegerField(db_column='isOtherInput', blank=True, null=True)  # Field name made lowercase.

    class Meta:
        db_table = 'app_surveysubject'


class AppModelreportreport(models.Model):
    Id = models.AutoField(db_column='Id', primary_key=True)  # Field name made lowercase.
    ProbjectId = models.IntegerField(db_column='ProbjectId', blank=True, null=True)  # Field name made lowercase.
    ModelReportId = models.IntegerField(db_column='ModelReportId', blank=True, null=True)  # Field name made lowercase.
    UserId = models.IntegerField(db_column='UserId', blank=True, null=True)  # Field name made lowercase.
    ModelContent = models.TextField(db_column='ModelContent', blank=True, null=True)  # Field name made lowercase.
    OtherPdf = models.TextField(db_column='OtherPdf', blank=True, null=True)  # Field name made lowercase.
    CreateDate = models.DateTimeField(db_column='CreateDate', auto_now_add=True)  # Field name made lowercase.
    Creator = models.CharField(db_column='Creator', max_length=50, blank=True, null=True)  # Field name made lowercase.
    ModifyDate = models.DateTimeField(db_column='ModifyDate', auto_now=True)  # Field name made lowercase.
    Modifier = models.CharField(db_column='Modifier', max_length=50, blank=True,
                                null=True)  # Field name made lowercase.
    reportjson = models.TextField(blank=True, null=True)
    status = models.IntegerField(blank=True, null=True)
    pici = models.CharField(max_length=100, blank=True, null=True)
    title = models.CharField(max_length=200, blank=True, null=True)
    rolesid = models.CharField(max_length=100, blank=True, null=True)
    CoachId = models.IntegerField(db_column='CoachId', blank=True, null=True)  # Field name made lowercase.
    ModelCode = models.CharField(db_column='ModelCode', max_length=100)  # Field name made lowercase.
    ReportCode = models.CharField(db_column='ReportCode', max_length=100, blank=True,
                                  null=True)  # Field name made lowercase.
    Enable = models.IntegerField(db_column='Enable', blank=True, null=True)  # Field name made lowercase.
    tindex = models.IntegerField(blank=True, null=True)

    class Meta:
        db_table = 'app_modelreportreport'


class AppModelreportresult(models.Model):
    Id = models.AutoField(db_column='Id', primary_key=True)  # Field name made lowercase.
    ProbjectId = models.IntegerField(db_column='ProbjectId')  # Field name made lowercase.
    UserId = models.IntegerField(db_column='UserId')  # Field name made lowercase.
    ModelReportId = models.IntegerField(db_column='ModelReportId', blank=True, null=True)  # Field name made lowercase.
    Answer = models.TextField(db_column='Answer', blank=True, null=True)  # Field name made lowercase.
    OtherInput = models.TextField(db_column='OtherInput', blank=True, null=True)  # Field name made lowercase.
    CreateDate = models.DateTimeField(db_column='CreateDate', auto_now_add=True)  # Field name made lowercase.
    Creator = models.CharField(db_column='Creator', max_length=50, blank=True, null=True)  # Field name made lowercase.
    ModifyDate = models.DateTimeField(db_column='ModifyDate', auto_now=True)  # Field name made lowercase.
    Modifier = models.CharField(db_column='Modifier', max_length=50, blank=True,
                                null=True)  # Field name made lowercase.
    pici = models.CharField(max_length=50, blank=True, null=True)
    DataId = models.IntegerField(db_column='DataId', blank=True, null=True)  # Field name made lowercase.
    ModelCode = models.CharField(db_column='ModelCode', max_length=100)  # Field name made lowercase.

    class Meta:
        db_table = 'app_modelreportresult'


class AppModeltemplate(models.Model):
    ModelReportId = models.AutoField(db_column='ModelReportId', primary_key=True)  # Field name made lowercase.
    PageCode = models.CharField(db_column='PageCode', max_length=100, blank=True,
                                null=True)  # Field name made lowercase.
    ModelName = models.CharField(db_column='ModelName', max_length=250)  # Field name made lowercase.
    ImgUrl = models.CharField(db_column='ImgUrl', max_length=200, blank=True, null=True)  # Field name made lowercase.
    Template = models.TextField(db_column='Template', blank=True, null=True)  # Field name made lowercase.
    Example = models.TextField(db_column='Example', blank=True, null=True)  # Field name made lowercase.
    Remark = models.TextField(db_column='Remark', blank=True, null=True)  # Field name made lowercase.
    Enable = models.IntegerField(db_column='Enable', blank=True, null=True)  # Field name made lowercase.
    CreateDate = models.DateTimeField(db_column='CreateDate', auto_now_add=True)  # Field name made lowercase.
    Creator = models.CharField(db_column='Creator', max_length=50, blank=True, null=True)  # Field name made lowercase.
    ModifyDate = models.DateTimeField(db_column='ModifyDate', auto_now=True)  # Field name made lowercase.
    Modifier = models.CharField(db_column='Modifier', max_length=50, blank=True,
                                null=True)  # Field name made lowercase.

    class Meta:
        db_table = 'app_modeltemplate'


class AppEvaluation(models.Model):
    EvaluationId = models.AutoField(db_column='EvaluationId', primary_key=True)  # Field name made lowercase.
    ExamId = models.IntegerField(db_column='ExamId', blank=True, null=True)  # Field name made lowercase.
    EvalType = models.IntegerField(db_column='EvalType')  # Field name made lowercase.
    PageCode = models.CharField(db_column='PageCode', max_length=100, blank=True,
                                null=True)  # Field name made lowercase.
    EvalName = models.CharField(db_column='EvalName', max_length=250)  # Field name made lowercase.
    StartDate = models.DateTimeField(db_column='StartDate', blank=True, null=True)  # Field name made lowercase.
    EndDate = models.DateTimeField(db_column='EndDate', blank=True, null=True)  # Field name made lowercase.
    UseType = models.IntegerField(db_column='UseType', blank=True, null=True)  # Field name made lowercase.
    Users = models.TextField(db_column='Users', blank=True, null=True)  # Field name made lowercase.
    GroupId = models.TextField(db_column='GroupId', blank=True, null=True)  # Field name made lowercase.
    EvalAble = models.TextField(db_column='EvalAble', blank=True, null=True)  # Field name made lowercase.
    ImgUrl = models.CharField(db_column='ImgUrl', max_length=200, blank=True, null=True)  # Field name made lowercase.
    Remark = models.TextField(db_column='Remark', blank=True, null=True)  # Field name made lowercase.
    Enable = models.IntegerField(db_column='Enable', blank=True, null=True)  # Field name made lowercase.
    domore = models.IntegerField(blank=True, null=True)
    CreateDate = models.DateTimeField(db_column='CreateDate', auto_now_add=True)  # Field name made lowercase.
    Creator = models.CharField(db_column='Creator', max_length=50, blank=True, null=True)  # Field name made lowercase.
    ModifyDate = models.DateTimeField(db_column='ModifyDate', auto_now=True)  # Field name made lowercase.
    Modifier = models.CharField(db_column='Modifier', max_length=50, blank=True,
                                null=True)  # Field name made lowercase.

    class Meta:
        db_table = 'app_evaluation'


class AppEvaluationoption(models.Model):
    Id = models.AutoField(db_column='Id', primary_key=True)  # Field name made lowercase.
    EQid = models.IntegerField(db_column='EQid')  # Field name made lowercase.
    Title = models.CharField(db_column='Title', max_length=250)  # Field name made lowercase.
    ImgPath = models.CharField(db_column='ImgPath', max_length=250, blank=True, null=True)  # Field name made lowercase.
    CreateTime = models.DateTimeField(db_column='CreateTime', blank=True, null=True)  # Field name made lowercase.
    isAnswer = models.IntegerField(db_column='isAnswer', blank=True, null=True)  # Field name made lowercase.
    Remark = models.CharField(db_column='Remark', max_length=255, blank=True, null=True)  # Field name made lowercase.

    class Meta:
        db_table = 'app_evaluationoption'


class AppEvaluationquestion(models.Model):
    EQid = models.AutoField(db_column='EQid', primary_key=True)  # Field name made lowercase.
    EvaluationId = models.IntegerField(db_column='EvaluationId', blank=True, null=True)  # Field name made lowercase.
    Title = models.CharField(db_column='Title', max_length=250)  # Field name made lowercase.
    ability = models.CharField(max_length=50, blank=True, null=True)
    behaviour = models.CharField(max_length=50, blank=True, null=True)
    personality = models.CharField(max_length=50, blank=True, null=True)
    TypeId = models.IntegerField(db_column='TypeId', blank=True, null=True)  # Field name made lowercase.
    QuestionContent = models.TextField(db_column='QuestionContent', blank=True, null=True)  # Field name made lowercase.
    Answer = models.TextField(db_column='Answer', blank=True, null=True)  # Field name made lowercase.
    ImgPath = models.CharField(db_column='ImgPath', max_length=255, blank=True, null=True)  # Field name made lowercase.
    MaxNum = models.IntegerField(db_column='MaxNum', blank=True, null=True)  # Field name made lowercase.
    isOtherInput = models.IntegerField(db_column='isOtherInput', blank=True, null=True)  # Field name made lowercase.
    CreateDate = models.DateTimeField(db_column='CreateDate', auto_now_add=True)  # Field name made lowercase.
    Creator = models.CharField(db_column='Creator', max_length=50, blank=True, null=True)  # Field name made lowercase.
    ModifyDate = models.DateTimeField(db_column='ModifyDate', auto_now=True)  # Field name made lowercase.
    Modifier = models.CharField(db_column='Modifier', max_length=50, blank=True,
                                null=True)  # Field name made lowercase.
    Enable = models.IntegerField(db_column='Enable', blank=True, null=True)  # Field name made lowercase.
    Sort = models.IntegerField(db_column='Sort', blank=True, null=True)  # Field name made lowercase.

    class Meta:
        db_table = 'app_evaluationquestion'


class AppEvaluationresult(models.Model):
    Id = models.AutoField(db_column='Id', primary_key=True)  # Field name made lowercase.
    ProbjectId = models.IntegerField(db_column='ProbjectId')  # Field name made lowercase.
    Userid = models.IntegerField(db_column='Userid')  # Field name made lowercase.
    EvaluationId = models.IntegerField(db_column='EvaluationId')  # Field name made lowercase.
    EqId = models.IntegerField(db_column='EqId')  # Field name made lowercase.
    Answer = models.TextField(db_column='Answer', blank=True, null=True)  # Field name made lowercase.
    OtherInput = models.TextField(db_column='OtherInput', blank=True, null=True)  # Field name made lowercase.
    CreateTime = models.DateTimeField(db_column='CreateTime', blank=True, null=True)  # Field name made lowercase.
    Score = models.IntegerField(db_column='Score', blank=True, null=True)  # Field name made lowercase.
    tIndex = models.IntegerField(db_column='tIndex', blank=True, null=True)  # Field name made lowercase.
    pici = models.CharField(max_length=50, blank=True, null=True)
    Interested_Id = models.IntegerField(db_column='Interested_Id', blank=True, null=True)  # Field name made lowercase.

    class Meta:
        db_table = 'app_evaluationresult'


class AppEvaluationtmpresult(models.Model):
    Id = models.AutoField(db_column='Id', primary_key=True)  # Field name made lowercase.
    ProbjectId = models.IntegerField(db_column='ProbjectId')  # Field name made lowercase.
    Userid = models.IntegerField(db_column='Userid')  # Field name made lowercase.
    EvaluationId = models.IntegerField(db_column='EvaluationId')  # Field name made lowercase.
    EqId = models.IntegerField(db_column='EqId')  # Field name made lowercase.
    Answer = models.TextField(db_column='Answer', blank=True, null=True)  # Field name made lowercase.
    OtherInput = models.TextField(db_column='OtherInput', blank=True, null=True)  # Field name made lowercase.
    CreateTime = models.DateTimeField(db_column='CreateTime', blank=True, null=True)  # Field name made lowercase.
    Score = models.IntegerField(db_column='Score', blank=True, null=True)  # Field name made lowercase.
    tIndex = models.IntegerField(db_column='tIndex', blank=True, null=True)  # Field name made lowercase.
    pici = models.CharField(max_length=50, blank=True, null=True)
    Interested_Id = models.IntegerField(db_column='Interested_Id', blank=True, null=True)

    class Meta:
        db_table = 'app_evaluationtmpresult'


class AppEvalutionreport(models.Model):
    Id = models.AutoField(db_column='Id', primary_key=True)  # Field name made lowercase.
    ProbjectId = models.IntegerField(db_column='ProbjectId', blank=True, null=True)  # Field name made lowercase.
    EvaluationId = models.IntegerField(db_column='EvaluationId', blank=True, null=True)  # Field name made lowercase.
    Userid = models.IntegerField(db_column='Userid', blank=True, null=True)  # Field name made lowercase.
    EvalContent = models.TextField(db_column='EvalContent', blank=True, null=True)  # Field name made lowercase.
    OtherPdf = models.TextField(db_column='OtherPdf', blank=True, null=True)  # Field name made lowercase.
    CreateDate = models.DateTimeField(db_column='CreateDate', auto_now_add=True)  # Field name made lowercase.
    Creator = models.CharField(db_column='Creator', max_length=50, blank=True, null=True)  # Field name made lowercase.
    ModifyDate = models.DateTimeField(db_column='ModifyDate', auto_now=True)  # Field name made lowercase.
    Modifier = models.CharField(db_column='Modifier', max_length=50, blank=True,
                                null=True)  # Field name made lowercase.
    zyxf = models.TextField(blank=True, null=True)
    chyl = models.TextField(blank=True, null=True)
    bcxw = models.TextField(blank=True, null=True)
    gjfm = models.TextField(blank=True, null=True)
    reportjson = models.TextField(blank=True, null=True)
    status = models.IntegerField(blank=True, null=True)

    class Meta:
        db_table = 'app_evalutionreport'


class AppExam(models.Model):
    ExamId = models.AutoField(db_column='ExamId', primary_key=True)  # Field name made lowercase.
    ExamIde = models.IntegerField(db_column='ExamIde', blank=True, null=True)  # Field name made lowercase.
    PageCode = models.CharField(db_column='PageCode', max_length=100, blank=True,
                                null=True)  # Field name made lowercase.
    ExamName = models.CharField(db_column='ExamName', max_length=200)  # Field name made lowercase.
    ExamMode = models.IntegerField(db_column='ExamMode', blank=True, null=True)  # Field name made lowercase.
    ExamTypeId = models.IntegerField(db_column='ExamTypeId', blank=True, null=True)  # Field name made lowercase.
    QuestionType = models.IntegerField(db_column='QuestionType', blank=True, null=True)  # Field name made lowercase.
    AutoQuestion = models.TextField(db_column='AutoQuestion', blank=True, null=True)  # Field name made lowercase.
    Questions = models.TextField(db_column='Questions', blank=True, null=True)  # Field name made lowercase.
    QuestionNum = models.IntegerField(db_column='QuestionNum', blank=True, null=True)  # Field name made lowercase.
    ExamExpTime = models.IntegerField(db_column='ExamExpTime', blank=True, null=True)  # Field name made lowercase.
    ExamContent = models.TextField(db_column='ExamContent', blank=True, null=True)  # Field name made lowercase.
    ExamSorce = models.IntegerField(db_column='ExamSorce', blank=True, null=True)  # Field name made lowercase.
    HGSorce = models.IntegerField(db_column='HGSorce', blank=True, null=True)  # Field name made lowercase.
    Remark = models.TextField(db_column='Remark', blank=True, null=True)  # Field name made lowercase.
    ReExam = models.IntegerField(db_column='ReExam', blank=True, null=True)  # Field name made lowercase.
    ExamRead = models.IntegerField(db_column='ExamRead', blank=True, null=True)  # Field name made lowercase.
    ShowResult = models.IntegerField(db_column='ShowResult', blank=True, null=True)  # Field name made lowercase.
    Status = models.IntegerField(db_column='Status', blank=True, null=True)  # Field name made lowercase.
    CreateDate = models.DateTimeField(db_column='CreateDate', auto_now_add=True)  # Field name made lowercase.
    Creator = models.CharField(db_column='Creator', max_length=50, blank=True, null=True)  # Field name made lowercase.
    ModifyDate = models.DateTimeField(db_column='ModifyDate', auto_now=True)  # Field name made lowercase.
    Modifier = models.CharField(db_column='Modifier', max_length=50, blank=True,
                                null=True)  # Field name made lowercase.

    class Meta:
        db_table = 'app_exam'


class AppExamfactory(models.Model):
    ExamFactoryId = models.AutoField(db_column='ExamFactoryId', primary_key=True)  # Field name made lowercase.
    Title = models.CharField(db_column='Title', max_length=250)  # Field name made lowercase.
    TypeId = models.IntegerField(db_column='TypeId', blank=True, null=True)  # Field name made lowercase.
    Remark = models.CharField(db_column='Remark', max_length=255, blank=True, null=True)  # Field name made lowercase.
    CreateDate = models.DateTimeField(db_column='CreateDate', auto_now_add=True)  # Field name made lowercase.
    Creator = models.CharField(db_column='Creator', max_length=50, blank=True, null=True)  # Field name made lowercase.
    ModifyDate = models.DateTimeField(db_column='ModifyDate', auto_now=True)  # Field name made lowercase.
    Modifier = models.CharField(db_column='Modifier', max_length=50, blank=True,
                                null=True)  # Field name made lowercase.

    class Meta:
        db_table = 'app_examfactory'


class AppExammapquestion(models.Model):
    MapId = models.AutoField(db_column='MapId', primary_key=True)  # Field name made lowercase.
    ExamId = models.IntegerField(db_column='ExamId', blank=True, null=True)  # Field name made lowercase.
    EQid = models.IntegerField(db_column='EQid', blank=True, null=True)  # Field name made lowercase.

    class Meta:
        db_table = 'app_exammapquestion'


class AppExamoption(models.Model):
    Id = models.AutoField(db_column='Id', primary_key=True)  # Field name made lowercase.
    EQid = models.IntegerField(db_column='EQid')  # Field name made lowercase.
    Title = models.CharField(db_column='Title', max_length=250)  # Field name made lowercase.
    ImgPath = models.CharField(db_column='ImgPath', max_length=250, blank=True, null=True)  # Field name made lowercase.
    CreateTime = models.DateTimeField(db_column='CreateTime', blank=True, null=True)  # Field name made lowercase.
    isAnswer = models.IntegerField(db_column='isAnswer', blank=True, null=True)  # Field name made lowercase.
    Remark = models.CharField(db_column='Remark', max_length=255, blank=True, null=True)  # Field name made lowercase.

    class Meta:
        db_table = 'app_examoption'


class AppExamquestion(models.Model):
    EQid = models.AutoField(db_column='EQid', primary_key=True)  # Field name made lowercase.
    ExamFactoryId = models.IntegerField(db_column='ExamFactoryId', blank=True, null=True)  # Field name made lowercase.
    Title = models.CharField(db_column='Title', max_length=250)  # Field name made lowercase.
    ability = models.CharField(max_length=50, blank=True, null=True)
    behaviour = models.CharField(max_length=50, blank=True, null=True)
    personality = models.CharField(max_length=50, blank=True, null=True)
    TypeId = models.IntegerField(db_column='TypeId', blank=True, null=True)  # Field name made lowercase.
    QuestionContent = models.TextField(db_column='QuestionContent', blank=True, null=True)  # Field name made lowercase.
    Answer = models.TextField(db_column='Answer', blank=True, null=True)  # Field name made lowercase.
    ImgPath = models.CharField(db_column='ImgPath', max_length=255, blank=True, null=True)  # Field name made lowercase.
    MaxNum = models.IntegerField(db_column='MaxNum', blank=True, null=True)  # Field name made lowercase.
    isOtherInput = models.IntegerField(db_column='isOtherInput', blank=True, null=True)  # Field name made lowercase.
    CreateDate = models.DateTimeField(db_column='CreateDate', auto_now_add=True)  # Field name made lowercase.
    Creator = models.CharField(db_column='Creator', max_length=50, blank=True, null=True)  # Field name made lowercase.
    ModifyDate = models.DateTimeField(db_column='ModifyDate', auto_now=True)  # Field name made lowercase.
    Modifier = models.CharField(db_column='Modifier', max_length=50, blank=True,
                                null=True)  # Field name made lowercase.

    class Meta:
        db_table = 'app_examquestion'


class AppExamresult(models.Model):
    Id = models.AutoField(db_column='Id', primary_key=True)  # Field name made lowercase.
    ProbjectId = models.IntegerField(db_column='ProbjectId')  # Field name made lowercase.
    Userid = models.IntegerField(db_column='Userid')  # Field name made lowercase.
    ExamId = models.IntegerField(db_column='ExamId')  # Field name made lowercase.
    EqId = models.IntegerField(db_column='EqId')  # Field name made lowercase.
    Answer = models.TextField(db_column='Answer', blank=True, null=True)  # Field name made lowercase.
    OtherInput = models.TextField(db_column='OtherInput', blank=True, null=True)  # Field name made lowercase.
    CreateTime = models.DateTimeField(db_column='CreateTime', blank=True, null=True)  # Field name made lowercase.
    Score = models.IntegerField(db_column='Score', blank=True, null=True)  # Field name made lowercase.

    class Meta:
        db_table = 'app_examresult'


class AppIldp(models.Model):
    Id = models.AutoField(db_column='Id', primary_key=True)  # Field name made lowercase.
    ProbjectId = models.IntegerField(db_column='ProbjectId', blank=True, null=True)  # Field name made lowercase.
    UserId = models.IntegerField(db_column='UserId', blank=True, null=True)  # Field name made lowercase.
    CoachId = models.IntegerField(db_column='CoachId', blank=True, null=True)  # Field name made lowercase.
    truename = models.CharField(max_length=50, blank=True, null=True)
    times = models.CharField(max_length=50, blank=True, null=True)
    Title = models.CharField(db_column='Title', max_length=200, blank=True, null=True)  # Field name made lowercase.
    A1 = models.TextField(db_column='A1', blank=True, null=True)  # Field name made lowercase.
    A2 = models.TextField(db_column='A2', blank=True, null=True)  # Field name made lowercase.
    A3 = models.TextField(db_column='A3', blank=True, null=True)  # Field name made lowercase.
    A4 = models.TextField(db_column='A4', blank=True, null=True)  # Field name made lowercase.
    A5 = models.CharField(db_column='A5', max_length=20, blank=True, null=True)  # Field name made lowercase.
    A6 = models.CharField(db_column='A6', max_length=20, blank=True, null=True)  # Field name made lowercase.
    A7 = models.CharField(db_column='A7', max_length=20, blank=True, null=True)  # Field name made lowercase.
    A8 = models.TextField(db_column='A8', blank=True, null=True)  # Field name made lowercase.
    A9 = models.TextField(db_column='A9', blank=True, null=True)  # Field name made lowercase.
    A5other = models.TextField(db_column='A5other', blank=True, null=True)  # Field name made lowercase.
    A6other = models.TextField(db_column='A6other', blank=True, null=True)  # Field name made lowercase.
    A7other = models.TextField(db_column='A7other', blank=True, null=True)  # Field name made lowercase.
    Enable = models.IntegerField(db_column='Enable', blank=True, null=True)  # Field name made lowercase.
    CreateDate = models.DateTimeField(db_column='CreateDate', auto_now_add=True)  # Field name made lowercase.
    Creator = models.CharField(db_column='Creator', max_length=50, blank=True, null=True)  # Field name made lowercase.
    ModifyDate = models.DateTimeField(db_column='ModifyDate', auto_now=True)  # Field name made lowercase.
    Modifier = models.CharField(db_column='Modifier', max_length=50, blank=True,
                                null=True)  # Field name made lowercase.
    Satisfaction = models.FloatField(db_column='Satisfaction', blank=True, null=True)  # Field name made lowercase.
    studentaction = models.TextField(blank=True, null=True)
    coachaction = models.TextField(blank=True, null=True)
    devplan = models.TextField(blank=True, null=True)
    remark = models.TextField(blank=True, null=True)
    interviewId = models.IntegerField(db_column='interviewId', blank=True, null=True)  # Field name made lowercase.
    studentstatus = models.IntegerField(blank=True, null=True)
    coachstatus = models.IntegerField(blank=True, null=True)
    growupSatisfied = models.FloatField(db_column='growupSatisfied', blank=True,
                                        null=True)  # Field name made lowercase.
    inputSatisfied = models.FloatField(db_column='inputSatisfied', blank=True, null=True)  # Field name made lowercase.
    KeyName = models.CharField(db_column='KeyName', max_length=50, blank=True, null=True)  # Field name made lowercase.
    modelobser = models.TextField(blank=True, null=True)

    class Meta:
        db_table = 'app_ildp'


class SysMenu(models.Model):
    Menu_Id = models.AutoField(db_column='Menu_Id', primary_key=True)  # Field name made lowercase.
    MenuName = models.CharField(db_column='MenuName', max_length=50)  # Field name made lowercase.
    Auth = models.TextField(db_column='Auth', blank=True, null=True)  # Field name made lowercase.
    Icon = models.CharField(db_column='Icon', max_length=50, blank=True, null=True)  # Field name made lowercase.
    Description = models.CharField(db_column='Description', max_length=200, blank=True,
                                   null=True)  # Field name made lowercase.
    Enable = models.IntegerField(db_column='Enable', blank=True, null=True)  # Field name made lowercase.
    OrderNo = models.IntegerField(db_column='OrderNo', blank=True, null=True)  # Field name made lowercase.
    TableName = models.CharField(db_column='TableName', max_length=200, blank=True,
                                 null=True)  # Field name made lowercase.
    ParentId = models.IntegerField(db_column='ParentId')  # Field name made lowercase.
    Url = models.TextField(db_column='Url', blank=True, null=True)  # Field name made lowercase.
    CreateDate = models.DateTimeField(db_column='CreateDate', auto_now_add=True)  # Field name made lowercase.
    Creator = models.CharField(db_column='Creator', max_length=50, blank=True, null=True)  # Field name made lowercase.
    ModifyDate = models.DateTimeField(db_column='ModifyDate', auto_now=True)  # Field name made lowercase.
    Modifier = models.CharField(db_column='Modifier', max_length=50, blank=True,
                                null=True)  # Field name made lowercase.
    RouterName = models.CharField(db_column='RouterName', max_length=200, blank=True,
                                  null=True)  # Field name made lowercase.

    class Meta:
        db_table = 'sys_menu'


class SysProvince(models.Model):
    ProvinceId = models.AutoField(db_column='ProvinceId', primary_key=True)  # Field name made lowercase.
    ProvinceCode = models.CharField(db_column='ProvinceCode', max_length=20)  # Field name made lowercase.
    ProvinceName = models.CharField(db_column='ProvinceName', max_length=30)  # Field name made lowercase.
    RegionCode = models.CharField(db_column='RegionCode', max_length=20, blank=True,
                                  null=True)  # Field name made lowercase.

    class Meta:
        db_table = 'sys_province'


class SysRole(models.Model):
    Role_Id = models.AutoField(db_column='Role_Id', primary_key=True)  # Field name made lowercase.
    CreateDate = models.DateTimeField(db_column='CreateDate', auto_now_add=True)  # Field name made lowercase.
    Creator = models.CharField(db_column='Creator', max_length=50, blank=True, null=True)  # Field name made lowercase.
    DeleteBy = models.CharField(db_column='DeleteBy', max_length=50, blank=True,
                                null=True)  # Field name made lowercase.
    DeptName = models.CharField(db_column='DeptName', max_length=50, blank=True,
                                null=True)  # Field name made lowercase.
    Dept_Id = models.IntegerField(db_column='Dept_Id', blank=True, null=True)  # Field name made lowercase.
    Enable = models.IntegerField(db_column='Enable', blank=True, null=True)  # Field name made lowercase.
    Modifier = models.CharField(db_column='Modifier', max_length=50, blank=True,
                                null=True)  # Field name made lowercase.
    ModifyDate = models.DateTimeField(db_column='ModifyDate', auto_now=True)  # Field name made lowercase.
    OrderNo = models.IntegerField(db_column='OrderNo', blank=True, null=True)  # Field name made lowercase.
    ParentId = models.IntegerField(db_column='ParentId')  # Field name made lowercase.
    RoleName = models.CharField(db_column='RoleName', max_length=50, blank=True,
                                null=True)  # Field name made lowercase.

    class Meta:
        db_table = 'sys_role'


class SysRoleauth(models.Model):
    Auth_Id = models.AutoField(db_column='Auth_Id', primary_key=True)  # Field name made lowercase.
    AuthValue = models.TextField(db_column='AuthValue', blank=True)  # Field name made lowercase.
    CreateDate = models.DateTimeField(db_column='CreateDate', auto_now_add=True)  # Field name made lowercase.
    Creator = models.TextField(db_column='Creator', blank=True, null=True)  # Field name made lowercase.
    Menu_Id = models.IntegerField(db_column='Menu_Id')  # Field name made lowercase.
    Modifier = models.TextField(db_column='Modifier', blank=True, null=True)  # Field name made lowercase.
    ModifyDate = models.DateTimeField(db_column='ModifyDate', auto_now=True)  # Field name made lowercase.
    Role_Id = models.IntegerField(db_column='Role_Id', blank=True, null=True)  # Field name made lowercase.
    User_Id = models.IntegerField(db_column='User_Id', blank=True, null=True)  # Field name made lowercase.

    class Meta:
        db_table = 'sys_roleauth'


class SysRoleauthdata(models.Model):
    Auth_Id = models.AutoField(db_column='Auth_Id', primary_key=True)  # Field name made lowercase.
    DataType_Id = models.IntegerField(db_column='DataType_Id', blank=True, null=True)  # Field name made lowercase.
    Role_Id = models.IntegerField(db_column='Role_Id', blank=True, null=True)  # Field name made lowercase.
    User_Id = models.IntegerField(db_column='User_Id', blank=True, null=True)  # Field name made lowercase.
    Node_Id = models.CharField(db_column='Node_Id', max_length=50, blank=True, null=True)  # Field name made lowercase.
    LevelID = models.IntegerField(db_column='LevelID', blank=True, null=True)  # Field name made lowercase.
    AuthValue = models.TextField(db_column='AuthValue', blank=True, null=True)  # Field name made lowercase.
    Creator = models.CharField(db_column='Creator', max_length=50, blank=True, null=True)  # Field name made lowercase.
    CreateDate = models.DateTimeField(db_column='CreateDate', auto_now_add=True)  # Field name made lowercase.
    Modifier = models.CharField(db_column='Modifier', max_length=50, blank=True,
                                null=True)  # Field name made lowercase.
    ModifyDate = models.DateTimeField(db_column='ModifyDate', auto_now=True)  # Field name made lowercase.

    class Meta:
        db_table = 'sys_roleauthdata'


class SysSetting(models.Model):
    KeyId = models.AutoField(db_column='KeyId', primary_key=True)  # Field name made lowercase.
    KeyCode = models.CharField(db_column='KeyCode', max_length=50, blank=True)  # Field name made lowercase.
    KeyName = models.CharField(db_column='KeyName', max_length=50, blank=True)  # Field name made lowercase.
    KeyValue = models.CharField(db_column='KeyValue', max_length=200, blank=True)  # Field name made lowercase.
    KeyJson = models.TextField(db_column='KeyJson', blank=True, null=True)  # Field name made lowercase.
    Remark = models.CharField(db_column='Remark', max_length=200, blank=True, null=True)  # Field name made lowercase.
    CreateDate = models.DateTimeField(db_column='CreateDate', auto_now_add=True)  # Field name made lowercase.
    Creator = models.CharField(db_column='Creator', max_length=50, blank=True, null=True)  # Field name made lowercase.
    ModifyDate = models.DateTimeField(db_column='ModifyDate', auto_now=True)  # Field name made lowercase.
    Modifier = models.CharField(db_column='Modifier', max_length=50, blank=True,
                                null=True)  # Field name made lowercase.

    class Meta:
        db_table = 'sys_setting'


class SysTablecolumn(models.Model):
    ColumnId = models.AutoField(db_column='ColumnId', primary_key=True)  # Field name made lowercase.
    ApiInPut = models.IntegerField(db_column='ApiInPut', blank=True, null=True)  # Field name made lowercase.
    ApiIsNull = models.IntegerField(db_column='ApiIsNull', blank=True, null=True)  # Field name made lowercase.
    ApiOutPut = models.IntegerField(db_column='ApiOutPut', blank=True, null=True)  # Field name made lowercase.
    ColSize = models.IntegerField(db_column='ColSize', blank=True, null=True)  # Field name made lowercase.
    ColumnCNName = models.CharField(db_column='ColumnCNName', max_length=100, blank=True,
                                    null=True)  # Field name made lowercase.
    ColumnName = models.CharField(db_column='ColumnName', max_length=100, blank=True,
                                  null=True)  # Field name made lowercase.
    ColumnType = models.TextField(db_column='ColumnType', blank=True, null=True)  # Field name made lowercase.
    ColumnWidth = models.IntegerField(db_column='ColumnWidth', blank=True, null=True)  # Field name made lowercase.
    Columnformat = models.TextField(db_column='Columnformat', blank=True, null=True)  # Field name made lowercase.
    CreateDate = models.DateTimeField(db_column='CreateDate', auto_now_add=True)  # Field name made lowercase.
    CreateID = models.IntegerField(db_column='CreateID', blank=True, null=True)  # Field name made lowercase.
    Creator = models.CharField(db_column='Creator', max_length=200, blank=True, null=True)  # Field name made lowercase.
    DropNo = models.CharField(db_column='DropNo', max_length=50, blank=True, null=True)  # Field name made lowercase.
    EditColNo = models.IntegerField(db_column='EditColNo', blank=True, null=True)  # Field name made lowercase.
    EditRowNo = models.IntegerField(db_column='EditRowNo', blank=True, null=True)  # Field name made lowercase.
    EditType = models.CharField(db_column='EditType', max_length=200, blank=True,
                                null=True)  # Field name made lowercase.
    Enable = models.IntegerField(db_column='Enable', blank=True, null=True)  # Field name made lowercase.
    IsColumnData = models.IntegerField(db_column='IsColumnData', blank=True, null=True)  # Field name made lowercase.
    IsDisplay = models.IntegerField(db_column='IsDisplay', blank=True, null=True)  # Field name made lowercase.
    IsImage = models.IntegerField(db_column='IsImage', blank=True, null=True)  # Field name made lowercase.
    IsKey = models.IntegerField(db_column='IsKey', blank=True, null=True)  # Field name made lowercase.
    IsNull = models.IntegerField(db_column='IsNull', blank=True, null=True)  # Field name made lowercase.
    IsReadDataset = models.IntegerField(db_column='IsReadDataset', blank=True, null=True)  # Field name made lowercase.
    Maxlength = models.IntegerField(db_column='Maxlength', blank=True, null=True)  # Field name made lowercase.
    Modifier = models.TextField(db_column='Modifier', blank=True, null=True)  # Field name made lowercase.
    ModifyDate = models.DateTimeField(db_column='ModifyDate', auto_now=True)  # Field name made lowercase.
    ModifyID = models.IntegerField(db_column='ModifyID', blank=True, null=True)  # Field name made lowercase.
    OrderNo = models.IntegerField(db_column='OrderNo', blank=True, null=True)  # Field name made lowercase.
    Script = models.TextField(db_column='Script', blank=True, null=True)  # Field name made lowercase.
    SearchColNo = models.IntegerField(db_column='SearchColNo', blank=True, null=True)  # Field name made lowercase.
    SearchRowNo = models.IntegerField(db_column='SearchRowNo', blank=True, null=True)  # Field name made lowercase.
    SearchType = models.CharField(db_column='SearchType', max_length=200, blank=True,
                                  null=True)  # Field name made lowercase.
    Sortable = models.IntegerField(db_column='Sortable', blank=True, null=True)  # Field name made lowercase.
    TableName = models.CharField(db_column='TableName', max_length=200, blank=True,
                                 null=True)  # Field name made lowercase.
    Table_Id = models.IntegerField(db_column='Table_Id', blank=True, null=True)  # Field name made lowercase.
    IsRichText = models.IntegerField(db_column='IsRichText', blank=True, null=True)  # Field name made lowercase.
    Remark = models.TextField(db_column='Remark', blank=True, null=True)  # Field name made lowercase.
    IsEdit = models.IntegerField(db_column='IsEdit', blank=True, null=True)  # Field name made lowercase.

    class Meta:
        db_table = 'sys_tablecolumn'


class SysTableinfo(models.Model):
    Table_Id = models.AutoField(db_column='Table_Id', primary_key=True)  # Field name made lowercase.
    CnName = models.CharField(db_column='CnName', max_length=50, blank=True, null=True)  # Field name made lowercase.
    ColumnCNName = models.CharField(db_column='ColumnCNName', max_length=100, blank=True,
                                    null=True)  # Field name made lowercase.
    DBServer = models.TextField(db_column='DBServer', blank=True, null=True)  # Field name made lowercase.
    DataTableType = models.CharField(db_column='DataTableType', max_length=200, blank=True,
                                     null=True)  # Field name made lowercase.
    DetailCnName = models.CharField(db_column='DetailCnName', max_length=200, blank=True,
                                    null=True)  # Field name made lowercase.
    DetailName = models.CharField(db_column='DetailName', max_length=200, blank=True,
                                  null=True)  # Field name made lowercase.
    EditorType = models.CharField(db_column='EditorType', max_length=100, blank=True,
                                  null=True)  # Field name made lowercase.
    Enable = models.IntegerField(db_column='Enable', blank=True, null=True)  # Field name made lowercase.
    ExpressField = models.CharField(db_column='ExpressField', max_length=200, blank=True,
                                    null=True)  # Field name made lowercase.
    FolderName = models.CharField(db_column='FolderName', max_length=200, blank=True,
                                  null=True)  # Field name made lowercase.
    Namespace = models.CharField(db_column='Namespace', max_length=200, blank=True,
                                 null=True)  # Field name made lowercase.
    OrderNo = models.IntegerField(db_column='OrderNo', blank=True, null=True)  # Field name made lowercase.
    ParentId = models.IntegerField(db_column='ParentId', blank=True, null=True)  # Field name made lowercase.
    RichText = models.CharField(db_column='RichText', max_length=100, blank=True,
                                null=True)  # Field name made lowercase.
    SortName = models.CharField(db_column='SortName', max_length=50, blank=True,
                                null=True)  # Field name made lowercase.
    TableName = models.CharField(db_column='TableName', max_length=50, blank=True,
                                 null=True)  # Field name made lowercase.
    TableTrueName = models.CharField(db_column='TableTrueName', max_length=100, blank=True,
                                     null=True)  # Field name made lowercase.
    UploadField = models.CharField(db_column='UploadField', max_length=100, blank=True,
                                   null=True)  # Field name made lowercase.
    UploadMaxCount = models.IntegerField(db_column='UploadMaxCount', blank=True,
                                         null=True)  # Field name made lowercase.
    Trackable = models.IntegerField(db_column='Trackable', blank=True, null=True)  # Field name made lowercase.

    class Meta:
        db_table = 'sys_tableinfo'


class SysUpfile(models.Model):
    NetworkFileId = models.AutoField(db_column='NetworkFileId', primary_key=True)  # Field name made lowercase.
    FolderId = models.IntegerField(db_column='FolderId', blank=True, null=True)  # Field name made lowercase.
    FileName = models.CharField(db_column='FileName', max_length=200, blank=True,
                                null=True)  # Field name made lowercase.
    FilePath = models.CharField(db_column='FilePath', max_length=250, blank=True,
                                null=True)  # Field name made lowercase.
    FileSize = models.CharField(db_column='FileSize', max_length=50, blank=True,
                                null=True)  # Field name made lowercase.
    FileExtensions = models.CharField(db_column='FileExtensions', max_length=50, blank=True,
                                      null=True)  # Field name made lowercase.
    FileType = models.CharField(db_column='FileType', max_length=50, blank=True,
                                null=True)  # Field name made lowercase.
    Icon = models.CharField(db_column='Icon', max_length=50, blank=True, null=True)  # Field name made lowercase.
    Sharing = models.IntegerField(db_column='Sharing', blank=True, null=True)  # Field name made lowercase.
    SharingFolderId = models.CharField(db_column='SharingFolderId', max_length=50, blank=True,
                                       null=True)  # Field name made lowercase.
    SharingCreateDate = models.DateTimeField(db_column='SharingCreateDate', blank=True,
                                             null=True)  # Field name made lowercase.
    SharingEndDate = models.DateTimeField(db_column='SharingEndDate', blank=True,
                                          null=True)  # Field name made lowercase.
    Enable = models.IntegerField(db_column='Enable', blank=True, null=True)  # Field name made lowercase.
    DeleteMark = models.IntegerField(db_column='DeleteMark', blank=True, null=True)  # Field name made lowercase.
    CreateDate = models.DateTimeField(db_column='CreateDate', auto_now_add=True)  # Field name made lowercase.
    Creator = models.CharField(db_column='Creator', max_length=50, blank=True, null=True)  # Field name made lowercase.
    ModifyDate = models.DateTimeField(db_column='ModifyDate', auto_now=True)  # Field name made lowercase.
    Modifier = models.CharField(db_column='Modifier', max_length=50, blank=True,
                                null=True)  # Field name made lowercase.

    class Meta:
        db_table = 'sys_upfile'


class SysUser(models.Model):
    User_Id = models.AutoField(db_column='User_Id', primary_key=True)  # Field name made lowercase.
    Address = models.CharField(db_column='Address', max_length=200, blank=True, null=True)  # Field name made lowercase.
    AppType = models.IntegerField(db_column='AppType', blank=True, null=True)  # Field name made lowercase.
    AuditDate = models.DateTimeField(db_column='AuditDate', blank=True, null=True)  # Field name made lowercase.
    AuditStatus = models.IntegerField(db_column='AuditStatus', blank=True, null=True)  # Field name made lowercase.
    Auditor = models.CharField(db_column='Auditor', max_length=200, blank=True, null=True)  # Field name made lowercase.
    CreateDate = models.DateTimeField(db_column='CreateDate', auto_now_add=True)  # Field name made lowercase.
    CreateID = models.IntegerField(db_column='CreateID', blank=True, null=True)  # Field name made lowercase.
    Creator = models.CharField(db_column='Creator', max_length=200, blank=True, null=True)  # Field name made lowercase.
    DeptName = models.CharField(db_column='DeptName', max_length=150, blank=True,
                                null=True)  # Field name made lowercase.
    Dept_Id = models.IntegerField(db_column='Dept_Id', blank=True, null=True)  # Field name made lowercase.
    Email = models.CharField(db_column='Email', max_length=100, blank=True, null=True)  # Field name made lowercase.
    Enable = models.IntegerField(db_column='Enable')  # Field name made lowercase.
    Gender = models.IntegerField(db_column='Gender', blank=True, null=True)  # Field name made lowercase.
    HeadImageUrl = models.CharField(db_column='HeadImageUrl', max_length=200, blank=True,
                                    null=True)  # Field name made lowercase.
    LastLoginDate = models.DateTimeField(db_column='LastLoginDate', blank=True, null=True)  # Field name made lowercase.
    LastModifyPwdDate = models.DateTimeField(db_column='LastModifyPwdDate', blank=True,
                                             null=True)  # Field name made lowercase.
    Mobile = models.CharField(db_column='Mobile', max_length=100, blank=True, null=True)  # Field name made lowercase.
    Modifier = models.CharField(db_column='Modifier', max_length=200, blank=True,
                                null=True)  # Field name made lowercase.
    ModifyDate = models.DateTimeField(db_column='ModifyDate', auto_now=True)  # Field name made lowercase.
    ModifyID = models.IntegerField(db_column='ModifyID', blank=True, null=True)  # Field name made lowercase.
    OrderNo = models.IntegerField(db_column='OrderNo', blank=True, null=True)  # Field name made lowercase.
    Role_Id = models.IntegerField(db_column='Role_Id')  # Field name made lowercase.
    RoleName = models.CharField(db_column='RoleName', max_length=150)  # Field name made lowercase.
    PhoneNo = models.CharField(db_column='PhoneNo', max_length=11, blank=True, null=True)  # Field name made lowercase.
    Remark = models.CharField(db_column='Remark', max_length=200, blank=True, null=True)  # Field name made lowercase.
    Tel = models.CharField(db_column='Tel', max_length=20, blank=True, null=True)  # Field name made lowercase.
    UserName = models.CharField(db_column='UserName', max_length=100)  # Field name made lowercase.
    UserPwd = models.CharField(db_column='UserPwd', max_length=200, blank=True, null=True)  # Field name made lowercase.
    UserTrueName = models.CharField(db_column='UserTrueName', max_length=20)  # Field name made lowercase.
    Token = models.TextField(db_column='Token', blank=True, null=True)  # Field name made lowercase.
    OpenIM = models.IntegerField(db_column='OpenIM', blank=True, null=True)  # Field name made lowercase.
    MessageId = models.CharField(db_column='MessageId', max_length=200, blank=True,
                                 null=True)  # Field name made lowercase.
    MessageIdPwd = models.CharField(db_column='MessageIdPwd', max_length=200, blank=True,
                                    null=True)  # Field name made lowercase.

    class Meta:
        db_table = 'sys_user'


class SysCity(models.Model):
    CityId = models.AutoField(db_column='CityId', primary_key=True)  # Field name made lowercase.
    CityCode = models.CharField(db_column='CityCode', max_length=20, blank=True,
                                null=True)  # Field name made lowercase.
    CityName = models.CharField(db_column='CityName', max_length=30, blank=True,
                                null=True)  # Field name made lowercase.
    ProvinceCode = models.CharField(db_column='ProvinceCode', max_length=20, blank=True,
                                    null=True)  # Field name made lowercase.

    class Meta:
        db_table = 'sys_city'


class AppDictionary(models.Model):
    Dic_ID = models.AutoField(db_column='Dic_ID', primary_key=True)  # Field name made lowercase.
    ConfigJson = models.TextField(db_column='ConfigJson', blank=True, null=True)  # Field name made lowercase.
    CreateDate = models.DateTimeField(db_column='CreateDate', auto_now_add=True)  # Field name made lowercase.
    CreateID = models.IntegerField(db_column='CreateID', blank=True, null=True)  # Field name made lowercase.
    Creator = models.CharField(db_column='Creator', max_length=30, blank=True, null=True)  # Field name made lowercase.
    DBServer = models.TextField(db_column='DBServer', blank=True, null=True)  # Field name made lowercase.
    DbSql = models.TextField(db_column='DbSql', blank=True, null=True)  # Field name made lowercase.
    DicName = models.CharField(db_column='DicName', max_length=100)  # Field name made lowercase.
    DicNo = models.CharField(db_column='DicNo', max_length=100)  # Field name made lowercase.
    Enable = models.IntegerField(db_column='Enable')  # Field name made lowercase.
    Modifier = models.CharField(db_column='Modifier', max_length=30, blank=True,
                                null=True)  # Field name made lowercase.
    ModifyDate = models.DateTimeField(db_column='ModifyDate', auto_now=True)  # Field name made lowercase.
    ModifyID = models.IntegerField(db_column='ModifyID', blank=True, null=True)  # Field name made lowercase.
    OrderNo = models.IntegerField(db_column='OrderNo', blank=True, null=True)  # Field name made lowercase.
    ParentId = models.IntegerField(db_column='ParentId')  # Field name made lowercase.
    Remark = models.TextField(db_column='Remark', blank=True, null=True)  # Field name made lowercase.

    class Meta:
        db_table = 'sys_dictionary'


class AppDictionarylist(models.Model):
    DicList_ID = models.AutoField(db_column='DicList_ID', primary_key=True)  # Field name made lowercase.
    CreateDate = models.DateTimeField(db_column='CreateDate', auto_now_add=True)  # Field name made lowercase.
    CreateID = models.IntegerField(db_column='CreateID', blank=True, null=True)  # Field name made lowercase.
    Creator = models.CharField(db_column='Creator', max_length=30, blank=True, null=True)  # Field name made lowercase.
    DicName = models.CharField(db_column='DicName', max_length=100, blank=True, null=True)  # Field name made lowercase.
    DicValue = models.CharField(db_column='DicValue', max_length=100, blank=True,
                                null=True)  # Field name made lowercase.
    Dic_ID = models.IntegerField(db_column='Dic_ID', blank=True, null=True)  # Field name made lowercase.
    Enable = models.IntegerField(db_column='Enable', blank=True, null=True)  # Field name made lowercase.
    Modifier = models.CharField(db_column='Modifier', max_length=30, blank=True,
                                null=True)  # Field name made lowercase.
    ModifyDate = models.DateTimeField(db_column='ModifyDate', auto_now=True)  # Field name made lowercase.
    ModifyID = models.IntegerField(db_column='ModifyID', blank=True, null=True)  # Field name made lowercase.
    OrderNo = models.IntegerField(db_column='OrderNo', blank=True, null=True)  # Field name made lowercase.
    Remark = models.TextField(db_column='Remark', blank=True, null=True)  # Field name made lowercase.

    class Meta:
        db_table = 'sys_dictionarylist'


class AppRescategories(models.Model):
    CategoryId = models.AutoField(db_column='CategoryId', primary_key=True)  # Field name made lowercase.
    ParentId = models.IntegerField(db_column='ParentId', blank=True, null=True)  # Field name made lowercase.
    Name = models.CharField(db_column='Name', max_length=100)  # Field name made lowercase.
    DisplaySequence = models.IntegerField(db_column='DisplaySequence')  # Field name made lowercase.
    IconUrl = models.CharField(db_column='IconUrl', max_length=255, blank=True, null=True)  # Field name made lowercase.
    IsShow = models.BooleanField(db_column='IsShow', blank=True, null=True)  # Field name made lowercase.

    class Meta:
        db_table = 'app_rescategories'


class AppUseronline(models.Model):
    Id = models.AutoField(db_column='Id', primary_key=True)  # Field name made lowercase.
    UserId = models.IntegerField(db_column='UserId')  # Field name made lowercase.
    logindate = models.DateTimeField(blank=True, null=True)
    lasttime = models.DateTimeField(blank=True, null=True)
    times = models.IntegerField(blank=True, null=True)
    CreateDate = models.DateTimeField(db_column='CreateDate', auto_now_add=True)  # Field name made lowercase.
    Creator = models.CharField(db_column='Creator', max_length=50, blank=True, null=True)  # Field name made lowercase.
    ModifyDate = models.DateTimeField(db_column='ModifyDate', auto_now=True)  # Field name made lowercase.
    Modifier = models.CharField(db_column='Modifier', max_length=50, blank=True,
                                null=True)  # Field name made lowercase.

    class Meta:
        db_table = 'app_useronline'


class AppResources(models.Model):
    CategoryId = models.IntegerField(db_column='CategoryId')  # Field name made lowercase.
    Cate1 = models.CharField(db_column='Cate1', max_length=50, blank=True, null=True)  # Field name made lowercase.
    Cate2 = models.CharField(db_column='Cate2', max_length=50, blank=True, null=True)  # Field name made lowercase.
    Cate3 = models.CharField(db_column='Cate3', max_length=50, blank=True, null=True)  # Field name made lowercase.
    ResId = models.AutoField(db_column='ResId', primary_key=True)  # Field name made lowercase.
    Title = models.CharField(db_column='Title', max_length=200)  # Field name made lowercase.
    Tags = models.TextField(db_column='Tags', blank=True, null=True)  # Field name made lowercase.
    Description = models.TextField(db_column='Description', blank=True, null=True)  # Field name made lowercase.
    Content = models.TextField(db_column='Content')  # Field name made lowercase.
    ImgPath = models.CharField(db_column='ImgPath', max_length=250, blank=True, null=True)  # Field name made lowercase.
    Video = models.CharField(db_column='Video', max_length=250, blank=True, null=True)  # Field name made lowercase.
    FilePath = models.CharField(db_column='FilePath', max_length=250, blank=True,
                                null=True)  # Field name made lowercase.
    IsShow = models.BooleanField(db_column='IsShow', blank=True, null=True)  # Field name made lowercase.
    IsTop = models.IntegerField(db_column='IsTop', blank=True, null=True)  # Field name made lowercase.
    ViewCount = models.IntegerField(db_column='ViewCount', blank=True, null=True)  # Field name made lowercase.
    OrderNo = models.IntegerField(db_column='OrderNo', blank=True, null=True)  # Field name made lowercase.
    Enable = models.IntegerField(db_column='Enable', blank=True, null=True)  # Field name made lowercase.
    CreateDate = models.DateTimeField(db_column='CreateDate', auto_now_add=True)  # Field name made lowercase.
    Creator = models.CharField(db_column='Creator', max_length=50, blank=True, null=True)  # Field name made lowercase.
    ModifyDate = models.DateTimeField(db_column='ModifyDate', auto_now=True)  # Field name made lowercase.
    Modifier = models.CharField(db_column='Modifier', max_length=50, blank=True,
                                null=True)  # Field name made lowercase.

    class Meta:
        db_table = 'app_resources'


class AppMessage(models.Model):
    id = models.AutoField(primary_key=True)
    type = models.CharField(max_length=50, blank=True, null=True)
    post_userid = models.IntegerField(blank=True, null=True)
    post_user_name = models.CharField(max_length=100, blank=True, null=True)
    accept_userid = models.IntegerField(blank=True, null=True)
    accept_user_name = models.CharField(max_length=100, blank=True, null=True)
    is_read = models.IntegerField(blank=True, null=True)
    title = models.CharField(max_length=100, blank=True, null=True)
    msgcontent = models.TextField(blank=True, null=True)
    post_time = models.DateTimeField()
    read_time = models.DateTimeField(blank=True, null=True)
    DataId = models.CharField(db_column='DataId', max_length=50, blank=True, null=True)  # Field name made lowercase.
    replay = models.TextField(blank=True, null=True)
    replaytime = models.DateTimeField(blank=True, null=True)
    replay_name = models.CharField(max_length=100, blank=True, null=True)

    class Meta:
        db_table = 'app_message'


class AppMessagecontent(models.Model):
    ContentId = models.AutoField(db_column='ContentId', primary_key=True)  # Field name made lowercase.
    sendtype = models.IntegerField(blank=True, null=True)
    accept = models.CharField(max_length=200, blank=True, null=True)
    Title = models.CharField(db_column='Title', max_length=100)  # Field name made lowercase.
    Content = models.TextField(db_column='Content')  # Field name made lowercase.
    Date = models.DateTimeField(db_column='Date')  # Field name made lowercase.
    CreateDate = models.DateTimeField(db_column='CreateDate', auto_now_add=True)  # Field name made lowercase.
    Creator = models.CharField(db_column='Creator', max_length=50, blank=True, null=True)  # Field name made lowercase.
    ModifyDate = models.DateTimeField(db_column='ModifyDate', auto_now=True)  # Field name made lowercase.
    Modifier = models.CharField(db_column='Modifier', max_length=50, blank=True,
                                null=True)  # Field name made lowercase.

    class Meta:
        db_table = 'app_messagecontent'


class AppMessagetemplates(models.Model):
    MessageTemplateId = models.AutoField(db_column='MessageTemplateId', primary_key=True)  # Field name made lowercase.
    MessageType = models.CharField(db_column='MessageType', max_length=100)  # Field name made lowercase.
    Name = models.CharField(db_column='Name', max_length=100)  # Field name made lowercase.
    SendEmail = models.BooleanField(db_column='SendEmail')  # Field name made lowercase.
    SendSMS = models.BooleanField(db_column='SendSMS')  # Field name made lowercase.
    SendInnerMessage = models.BooleanField(db_column='SendInnerMessage')  # Field name made lowercase.
    SendWeixin = models.IntegerField(db_column='SendWeixin', blank=True, null=True)  # Field name made lowercase.
    TagDescription = models.TextField(db_column='TagDescription')  # Field name made lowercase.
    EmailSubject = models.TextField(db_column='EmailSubject', blank=True, null=True)  # Field name made lowercase.
    EmailBody = models.TextField(db_column='EmailBody', blank=True, null=True)  # Field name made lowercase.
    InnerMessageSubject = models.TextField(db_column='InnerMessageSubject', blank=True,
                                           null=True)  # Field name made lowercase.
    InnerMessageBody = models.TextField(db_column='InnerMessageBody', blank=True,
                                        null=True)  # Field name made lowercase.
    SMSBody = models.TextField(db_column='SMSBody', blank=True, null=True)  # Field name made lowercase.
    WeixinTemplateId = models.CharField(db_column='WeixinTemplateId', max_length=150, blank=True,
                                        null=True)  # Field name made lowercase.
    WeiXinTemplateNo = models.CharField(db_column='WeiXinTemplateNo', max_length=50, blank=True,
                                        null=True)  # Field name made lowercase.
    WeiXinName = models.CharField(db_column='WeiXinName', max_length=200, blank=True,
                                  null=True)  # Field name made lowercase.
    UseInWxApplet = models.BooleanField(db_column='UseInWxApplet', blank=True, null=True)  # Field name made lowercase.
    WxAppletTemplateId = models.CharField(db_column='WxAppletTemplateId', max_length=150, blank=True,
                                          null=True)  # Field name made lowercase.
    AppletTemplateNo = models.CharField(db_column='AppletTemplateNo', max_length=50, blank=True,
                                        null=True)  # Field name made lowercase.
    AppletTemplateName = models.CharField(db_column='AppletTemplateName', max_length=200, blank=True,
                                          null=True)  # Field name made lowercase.
    SendPush = models.IntegerField(db_column='SendPush', blank=True, null=True)  # Field name made lowercase.
    PushSubject = models.TextField(db_column='PushSubject', blank=True, null=True)  # Field name made lowercase.
    PushBody = models.TextField(db_column='PushBody', blank=True, null=True)  # Field name made lowercase.
    CreateDate = models.DateTimeField(db_column='CreateDate', auto_now_add=True)  # Field name made lowercase.
    Creator = models.CharField(db_column='Creator', max_length=50, blank=True, null=True)  # Field name made lowercase.
    ModifyDate = models.DateTimeField(db_column='ModifyDate', auto_now=True)  # Field name made lowercase.
    Modifier = models.CharField(db_column='Modifier', max_length=50, blank=True,
                                null=True)  # Field name made lowercase.

    class Meta:
        db_table = 'app_messagetemplates'


class AppIm(models.Model):
    MsgId = models.AutoField(db_column='MsgId', primary_key=True)  # Field name made lowercase.
    ProbjectId = models.IntegerField(db_column='ProbjectId', blank=True, null=True)  # Field name made lowercase.
    FromUserId = models.IntegerField(db_column='FromUserId')  # Field name made lowercase.
    ToUserId = models.IntegerField(db_column='ToUserId')  # Field name made lowercase.
    MsgType = models.IntegerField(db_column='MsgType')  # Field name made lowercase.
    MsgContent = models.TextField(db_column='MsgContent')  # Field name made lowercase.
    MsgFilePath = models.CharField(db_column='MsgFilePath', max_length=250, blank=True,
                                   null=True)  # Field name made lowercase.
    CreateDate = models.DateTimeField(db_column='CreateDate', auto_now_add=True)  # Field name made lowercase.
    Creator = models.CharField(db_column='Creator', max_length=50, blank=True, null=True)  # Field name made lowercase.
    ModifyDate = models.DateTimeField(db_column='ModifyDate', auto_now=True)  # Field name made lowercase.
    Modifier = models.CharField(db_column='Modifier', max_length=50, blank=True,
                                null=True)  # Field name made lowercase.

    class Meta:
        db_table = 'app_im'


class AppArticle(models.Model):
    Id = models.AutoField(db_column='Id', primary_key=True)  # Field name made lowercase.
    Title = models.CharField(db_column='Title', max_length=255, blank=True, null=True)  # Field name made lowercase.
    TypeId = models.IntegerField(db_column='TypeId', blank=True, null=True)  # Field name made lowercase.
    PageCode = models.CharField(db_column='PageCode', max_length=50, blank=True,
                                null=True)  # Field name made lowercase.
    ShareUsers = models.CharField(db_column='ShareUsers', max_length=255, blank=True,
                                  null=True)  # Field name made lowercase.
    Contents = models.TextField(db_column='Contents', blank=True, null=True)  # Field name made lowercase.
    FilePath = models.CharField(db_column='FilePath', max_length=255, blank=True,
                                null=True)  # Field name made lowercase.
    Sort = models.IntegerField(db_column='Sort', blank=True, null=True)  # Field name made lowercase.
    Enable = models.IntegerField(db_column='Enable', blank=True, null=True)  # Field name made lowercase.
    CreateDate = models.DateTimeField(db_column='CreateDate', auto_now_add=True)  # Field name made lowercase.
    Creator = models.CharField(db_column='Creator', max_length=50, blank=True, null=True)  # Field name made lowercase.
    ModifyDate = models.DateTimeField(db_column='ModifyDate', auto_now=True)  # Field name made lowercase.
    Modifier = models.CharField(db_column='Modifier', max_length=50, blank=True,
                                null=True)  # Field name made lowercase.

    class Meta:
        db_table = 'app_article'


class AppArticlecategory(models.Model):
    Cate_Id = models.AutoField(db_column='Cate_Id', primary_key=True)  # Field name made lowercase.
    CreateDate = models.DateTimeField(db_column='CreateDate', auto_now_add=True)  # Field name made lowercase.
    Creator = models.CharField(db_column='Creator', max_length=50, blank=True, null=True)  # Field name made lowercase.
    Enable = models.IntegerField(db_column='Enable', blank=True, null=True)  # Field name made lowercase.
    Modifier = models.CharField(db_column='Modifier', max_length=50, blank=True,
                                null=True)  # Field name made lowercase.
    ModifyDate = models.DateTimeField(db_column='ModifyDate', auto_now=True)  # Field name made lowercase.
    OrderNo = models.IntegerField(db_column='OrderNo', blank=True, null=True)  # Field name made lowercase.
    ParentId = models.IntegerField(db_column='ParentId')  # Field name made lowercase.
    CateName = models.CharField(db_column='CateName', max_length=50, blank=True,
                                null=True)  # Field name made lowercase.

    class Meta:
        db_table = 'app_articlecategory'


class AppEmailqueue(models.Model):
    EmailId = models.CharField(db_column='EmailId', primary_key=True, auto_created=True,
                               max_length=50)  # Field name made lowercase.
    EmailPriority = models.IntegerField(db_column='EmailPriority')  # Field name made lowercase.
    IsBodyHtml = models.BooleanField(db_column='IsBodyHtml')  # Field name made lowercase.
    EmailTo = models.TextField(db_column='EmailTo')  # Field name made lowercase.
    EmailCc = models.TextField(db_column='EmailCc', blank=True, null=True)  # Field name made lowercase.
    EmailBcc = models.TextField(db_column='EmailBcc', blank=True, null=True)  # Field name made lowercase.
    EmailSubject = models.TextField(db_column='EmailSubject')  # Field name made lowercase.
    EmailBody = models.TextField(db_column='EmailBody')  # Field name made lowercase.
    NextTryTime = models.DateTimeField(db_column='NextTryTime')  # Field name made lowercase.
    NumberOfTries = models.IntegerField(db_column='NumberOfTries')  # Field name made lowercase.
    CreateDate = models.DateTimeField(db_column='CreateDate', auto_now_add=True)  # Field name made lowercase.
    Creator = models.CharField(db_column='Creator', max_length=50, blank=True, null=True)  # Field name made lowercase.
    ModifyDate = models.DateTimeField(db_column='ModifyDate', auto_now=True)  # Field name made lowercase.
    Modifier = models.CharField(db_column='Modifier', max_length=50, blank=True,
                                null=True)  # Field name made lowercase.

    class Meta:
        db_table = 'app_emailqueue'


class AppFeedback(models.Model):
    id = models.AutoField(primary_key=True)
    typeid = models.CharField(max_length=100, blank=True, null=True)
    title = models.CharField(max_length=100, blank=True, null=True)
    content = models.TextField(blank=True, null=True)
    user_id = models.IntegerField(blank=True, null=True)
    user_rename = models.CharField(max_length=50, blank=True, null=True)
    user_name = models.CharField(max_length=50, blank=True, null=True)
    user_tel = models.CharField(max_length=30, blank=True, null=True)
    user_email = models.CharField(max_length=100, blank=True, null=True)
    add_time = models.DateTimeField()
    reply_content = models.TextField(blank=True, null=True)
    reply_time = models.DateTimeField(blank=True, null=True)
    is_lock = models.IntegerField()

    class Meta:
        db_table = 'app_feedback'


class AppProbjectInterviewShare(models.Model):
    Id = models.AutoField(db_column='Id', primary_key=True)  # Field name made lowercase.
    InterviewId = models.IntegerField(db_column='InterviewId')  # Field name made lowercase.
    ProbjectId = models.IntegerField(db_column='ProbjectId')  # Field name made lowercase.
    User_Id = models.IntegerField(db_column='User_Id')  # Field name made lowercase.
    Coach_Id = models.IntegerField(db_column='Coach_Id')  # Field name made lowercase.
    CreateDate = models.DateTimeField(db_column='CreateDate', auto_now_add=True)  # Field name made lowercase.
    Creator = models.CharField(db_column='Creator', max_length=50, blank=True, null=True)  # Field name made lowercase.
    ModifyDate = models.DateTimeField(db_column='ModifyDate', auto_now=True)  # Field name made lowercase.
    Modifier = models.CharField(db_column='Modifier', max_length=50, blank=True,
                                null=True)  # Field name made lowercase.

    class Meta:
        db_table = 'app_probjectinterviewshare'


class AppLearningActionNote(models.Model):
    learning_action = models.ForeignKey(AppProbjectlearningaction, on_delete=models.CASCADE)
    note = models.TextField(blank=False, null=False)
    CreateDate = models.DateTimeField(db_column='CreateDate', auto_now_add=True)
    ModifyDate = models.DateTimeField(db_column='ModifyDate', auto_now=True)

    class Meta:
        db_table = 'app_learning_action_note'


class AppProjectSettingAdmin(models.Model):
    Id = models.AutoField(db_column='Id', primary_key=True)  # Field name made lowercase.
    pid = models.IntegerField()  # Field name made lowercase.
    KeyName = models.CharField(db_column='KeyName', max_length=50)  # Field name made lowercase.
    KeyValue = models.CharField(db_column='KeyValue', max_length=200, blank=True)  # Field name made lowercase.
    Enable = models.IntegerField(db_column='Enable', blank=True, null=True)  # Field name made lowercase.
    Remark = models.CharField(db_column='Remark', max_length=200, blank=True, null=True)  # Field name made lowercase.
    CreateDate = models.DateTimeField(db_column='CreateDate', auto_now_add=True)  # Field name made lowercase.
    Creator = models.CharField(db_column='Creator', max_length=50, blank=True, null=True)  # Field name made lowercase.
    ModifyDate = models.DateTimeField(db_column='ModifyDate', auto_now=True)  # Field name made lowercase.
    Modifier = models.CharField(db_column='Modifier', max_length=50, blank=True,
                                null=True)  # Field name made lowercase.

    class Meta:
        db_table = 'app_project_setting_admin'


class AppScheduleAttendee(models.Model):
    schedule = models.ForeignKey(AppCoachschedule, on_delete=models.CASCADE, null=False)
    attendee = models.IntegerField(null=False)

    class Meta:
        db_table = 'app_schedule_attendee'


class Log(models.Model):
    url = models.TextField('请求url', null=True)
    request_body = models.TextField('请求body', null=True)
    request_content_length = models.CharField('请求content_length', null=True, max_length=10)
    user_agent = models.TextField('请求user_agent', null=True)
    token = models.TextField('请求token', null=True)
    user_id = models.IntegerField('请求用户id', null=True)
    user_name = models.CharField('请求用户名', null=True, max_length=50)
    response_body = models.TextField('返回body', null=True)
    response_code = models.CharField('返回code', max_length=10, null=True)
    response_content_length = models.IntegerField('返回content_length', null=True)
    spend_time = models.IntegerField('请求花费时间', default=0)

    deleted = models.BooleanField('是否删除', default=False)
    created_at = models.DateTimeField('创建时间', auto_now_add=True)

    class Meta:
        db_table = 'log'
        verbose_name = '日志'
        verbose_name_plural = verbose_name
