from django.db import models
from data.user.models import User
import uuid


# Create your models here.
class Company(models.Model):
    cid = models.UUIDField(default=uuid.uuid4, editable=False)
    company_name = models.CharField(max_length=150, null=False)
    short_name = models.Char<PERSON>ield(max_length=150)
    logo = models.URLField(null=True)
    category = models.CharField(max_length=250)  # CompanyAttr
    industry = models.CharField(max_length=250, null=True)
    employee_scale = models.CharField(max_length=250, null=True)
    video = models.URLField(null=True)
    website = models.URLField(null=True)
    intro = models.TextField(null=True)
    parentid = models.IntegerField(null=True)
    create_date = models.DateTimeField(auto_now_add=True)

    class Meta:
        db_table = 'company'


from data.company.team.models import Team


class CompanyUser(models.Model):
    user = models.OneToOneField(User, on_delete=models.CASCADE, primary_key=True)
    team = models.ForeignKey(Team, on_delete=models.CASCADE, null=True)
    company = models.ForeignKey(Company, on_delete=models.CASCADE, null=True)
    working_date_company = models.DateTimeField(null=True)  # CompanyWorkingDate
    working_date_position = models.DateTimeField(null=True)  # GwWorkingDate
    working_date_first = models.DateTimeField(null=True)  # WorkingDate
    competency = models.CharField(max_length=250, null=True)
    duty = models.CharField(max_length=50, null=True)
    department = models.CharField(max_length=50, null=True)
    parent_department = models.CharField(max_length=50, null=True)
    remark = models.TextField(null=True)
    cowisdom_remark = models.TextField(null=True)  # ManagerRemark
    coach_remark = models.TextField(null=True)
    underlings_number = models.IntegerField(null=True)  # employees
    team_scale = models.IntegerField(null=True)  # employee
    office_address = models.CharField(max_length=200, null=True) # gzaddr
    other_position = models.CharField(max_length=200, null=True) # qitzw
    coach_understand = models.CharField(max_length=200, null=True) # liaojie

    class Meta:
        abstract = True
