from data.user.models import User
from data.user.views import getRoleId
from . import models
from data.project import models as project_models


def getMyCompany(request):
    user = User.objects.get(id=request.user.id)
    companys = set()
    role = getRoleId(user)
    if role == 3:  # project owner
        all_project = project_models.Project.objects.filter(owner_id=request.user.id)
        for project in all_project:
            companys.add(project.company)
        return companys
    elif role == 5 or role == 6 or role == 7 or role == 8:  # member
        all_member = project_models.Membership.objects.filter(user_id=request.user.id)
        for member in all_member:
            companys.add(member.project.company)
        return companys
    elif role == 1: # admin
        return models.Company.objects.all()
    return None
