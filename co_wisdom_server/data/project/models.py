from django.db import models

from data.project.schedule.models import Schedule
from data.user.models import User
from data.company.models import Company
from data.company.team.models import Team
from data.user.coach.models import Coach
from data.user.coachee.models import Coachee
from data.user.project_owner.models import Project_owner
import uuid


# Create your models here.
class Project(models.Model):
    pid = models.UUIDField(default=uuid.uuid4, editable=False)
    project_name = models.CharField(max_length=250, null=False)
    company = models.ForeignKey(Company, on_delete=models.CASCADE)
    team = models.ForeignKey(Team, on_delete=models.CASCADE, null=True)
    background = models.TextField(null=True)
    coachee_count = models.IntegerField(default=0)
    objective = models.TextField(null=True)
    times = models.IntegerField(default=0)
    time_start = models.DateTimeField()
    time_end = models.DateTimeField()
    video = models.URLField(null=True)
    website = models.URLField(null=True)
    intro = models.TextField(null=True)
    is_signed = models.Boolean<PERSON>ield(default=False)  # Status
    member = models.ManyToManyField(User, null=True, through='Membership')
    owner = models.ForeignKey(Project_owner, on_delete=models.CASCADE, null=True)
    create_date = models.DateTimeField(auto_now_add=True)
    interview_order = models.TextField(null=True)
    remark = models.TextField(null=True)
    dothing = models.IntegerField(null=True)
    file_path = models.TextField(null=True)

    class Meta:
        db_table = 'project'


# app_projectcoach
# project & coach relation data
class Matching(models.Model):
    project = models.ForeignKey(Project, on_delete=models.CASCADE)  # ProbjectId
    coach = models.ForeignKey(Coach, on_delete=models.CASCADE)  # CoachId
    matching = models.FloatField(null=True, default=0)# matching score
    matching_times = models.IntegerField(null=True, default=0)
    status = models.IntegerField(null=True, default=0)

    class Meta:
        db_table = 'matching'



# app_projectrelation
# project & stuff related data
class Membership(models.Model):
    project = models.ForeignKey(Project, on_delete=models.CASCADE)  # ProbjectId
    user = models.ForeignKey(User, on_delete=models.CASCADE)
    isAdmin = models.IntegerField(default=0)#isManger
    role_id = models.IntegerField(null=True)#RolesId
    coach = models.ForeignKey(Coach, on_delete=models.CASCADE, null=True)  # CoacheeId for coachee
    matching_times = models.IntegerField(default=0) # jlppcs for coachee
    coachee_related = models.ForeignKey(Coachee, on_delete=models.CASCADE, null=True) # for stakeholder
    plan_time = models.CharField(max_length=50, null=True)  # PlanTime
    study_plan = models.TextField(null=True)
    project_objective = models.TextField(null=True) #ProjectObjectives
    objective = models.TextField(null=True)  # Objectives

    class Meta:
        db_table = 'project_member'


# app_projectinterview
class Interview(models.Model):
    iid = models.UUIDField(default=uuid.uuid4, editable=False)
    schedule = models.OneToOneField(Schedule, on_delete=models.CASCADE, null=True)
    project = models.ForeignKey(Project, on_delete=models.CASCADE)
    coach = models.ForeignKey(Coach, on_delete=models.CASCADE)
    user = models.ForeignKey(User, on_delete=models.CASCADE, null=True)
    title = models.CharField(max_length=250)
    remark = models.TextField(null=True)
    status = models.IntegerField(null=True)
    close_reason = models.CharField(max_length=250, null=True)  # CloseReason
    times = models.IntegerField(null=True)  # Times minutes
    satisfaction = models.FloatField(null=True)
    start_time = models.DateTimeField(null=True)
    end_time = models.DateTimeField(null=True)
    purpose = models.TextField(null=True)
    create_time = models.DateTimeField(null=True)
    total = models.IntegerField(null=True)  # AllInterview
    current_count = models.IntegerField(null=True)  # nowInterview
    pici = models.CharField(max_length=50, null=True)# report and evaluate
    grow_up_satisfied = models.FloatField(null=True)
    input_satisfied = models.FloatField(null=True)
    up_ability = models.CharField(max_length=200, null=True)
    interview_type = models.IntegerField(null=True)#0 被教人 1 利益者
    close_type = models.CharField(max_length=200, null=True)
    project_rank = models.FloatField(null=True)
    interview_status = models.IntegerField(null=True)#0 正常 1 异常
    interview_subject = models.IntegerField(null=True)#0 常规 1 第一次（ildp） 2 三方 3 阶段性 4 总结
    hxdata = models.TextField(null=True)

    class Meta:
        db_table = 'interview'


