from django.db import models

from data.user.coach.models import Coach
from data.user.models import User


class Schedule(models.Model):
    type = models.IntegerField(null=True)
    user = models.ForeignKey(User, on_delete=models.CASCADE, null=True)
    coach = models.ForeignKey(Coach, on_delete=models.CASCADE, null=True)
    title = models.CharField(max_length=250, null=True)
    start_time = models.DateTimeField()
    end_time = models.DateTimeField()
    enable = models.IntegerField(default=1)
    interview_id = models.IntegerField(null=True)

    class Meta:
        db_table = 'schedule'
