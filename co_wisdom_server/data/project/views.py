from django.shortcuts import render
from data.user.models import User
from data.user.views import getRoleId
from . import models


# Create your views here.
def getMyProject(request):
    user = User.objects.get(id=request.user.id)
    role = getRoleId(user)
    if role == 3:
        all_project = models.Project.objects.filter(owner_id=request.user.id)
        return all_project
    elif role == 5 or role == 6 or role == 7 or role == 8:
        all_member = models.Membership.objects.filter(user_id=request.user.id)
        all_project = set()
        for member in all_member:
            all_project.add(member.project)
            return all_project
    elif role == 1:
        return models.Project.objects.all()
    return None
