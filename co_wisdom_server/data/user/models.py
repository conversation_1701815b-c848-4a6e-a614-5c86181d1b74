from django.db import models
import uuid

# Create your models here.


class User(models.Model):
    uid = models.UUIDField(default=uuid.uuid4, editable=False)
    login_name = models.CharField(max_length=150, null=False, unique=True)
    login_password = models.CharField(max_length=128, null=False)
    real_name = models.CharField(max_length=150, null=False)
    eng_name = models.CharField(max_length=150, null=True)
    email = models.EmailField(max_length=254, null=False)
    gender = models.IntegerField(null=True)
    mobile = models.CharField(max_length=100, null=False)
    date_joined = models.DateTimeField(max_length=6, null=False)
    birthday = models.DateField(null=True)
    age = models.IntegerField(null=True)
    pic = models.URLField(null=True)
    address = models.CharField(max_length=150, null=True)
    education = models.Char<PERSON>ield(max_length=50, null=True)  # 大专，本科，硕士，博士，博士后
    school = models.TextField(null=True)  # school
    major = models.TextField(null=True)
    graduation_date = models.DateField(null=True)  # GraduationDate
    id_number = models.CharField(max_length=50, null=True)  # IDCard
    passport = models.CharField(max_length=50, null=True)  # Passport
    role = models.IntegerField()

    class Meta:
        db_table = 'user'


class Auth(models.Model):
    user = models.OneToOneField(User, on_delete=models.CASCADE, primary_key=True)
    token = models.CharField(max_length=300)
    last_login = models.DateTimeField(null=True)
    last_modify_password = models.DateTimeField(null=True)

    class Meta:
        db_table = 'auth'

