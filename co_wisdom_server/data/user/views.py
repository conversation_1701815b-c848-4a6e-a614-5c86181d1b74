from django.shortcuts import render
from .coach.models import Coach
from .coachee.models import Coach<PERSON>
from data.user.project_owner.models import Project_owner
from .company_admin.models import Company_admin
from .evaluator.models import Evaluator
from .stakeholder.models import Stakeholder
from .. import models

# Create your views here.
roleAdmin = 0b1
roleCoach = 0b1 << 1
roleCoachee = 0b1 << 2
roleProjectOwner = 0b1 << 3
roleProjectAdmin = 0b1 << 4
roleStakeholder = 0b1 << 5
roleEvaluator = 0b1 << 6


def business_user_info(user):
    company_id = 0
    role_name = "管理员"
    role_id = getRoleId(user)
    if user.role & roleCoachee:
        role = Coachee.objects.get(user_id=user.id)
        company_id = role.company_id
        role_name = "被教练者"
    elif user.role & roleProjectAdmin:
        role = Company_admin.objects.get(user_id=user.id)
        company_id = role.company_id
        role_name = "企业管理员"
    elif user.role & roleStakeholder:
        role = Stakeholder.objects.get(user_id=user.id)
        company_id = role.company_id
        role_name = "利益相关者"
    elif user.role & roleEvaluator:
        role = Evaluator.objects.get(user_id=user.id)
        company_id = role.company_id
        role_name = "测评者"
    elif user.role & roleCoach:
        role = Coach.objects.get(user_id=user.id)
        role_name = "教练"
    return {"coachid": 0, "coachimg": "", "coachmessageid": "", "coachtruename": user.real_name,
            "companyid": company_id, "hxpwd": "", "hxuser": "", "img": "",
            "msgnumber": 0, "mtbjr": 0, "probject": 0, "roleName": role_name, "roleid": role_id,
            "roles": "probjectadmin", "userEmail": user.email,
            "userId": user.id, "userName": user.login_name
            }


def getRoleId(user):
    role_id = 0
    if user.role & roleCoachee:
        role_id = 6
    elif user.role & roleProjectAdmin:
        role_id = 5
    elif user.role & roleStakeholder:
        role_id = 7
    elif user.role & roleEvaluator:
        role_id = 8
    elif user.role & roleCoach:
        role_id = 4
    elif user.role & roleProjectOwner:
        role_id = 3
    return role_id


def transRoleIdToRoleBitwise(role_id):
    if role_id == 1:
        return roleAdmin
    elif role_id == 2:
        return roleAdmin
    elif role_id == 3:
        return roleProjectOwner
    elif role_id == 4:
        return roleCoach
    elif role_id == 5:
        return roleProjectAdmin
    elif role_id == 6:
        return roleCoachee
    elif role_id == 7:
        return roleStakeholder
    elif role_id == 8:
        return roleEvaluator
    return None


def getUserMenu(user):
    menus = []
    role_id = getRoleId(user)
    role_auth_set = models.RoleAuth.objects.filter(role_id=role_id)
    for role_auth in role_auth_set:
        menu = {"icon": role_auth.menu.icon, "id": role_auth.menu.id, "name": role_auth.menu.menu_name,
                "parentId": role_auth.menu.parent_id, "url": role_auth.menu.url,
                "permission": role_auth.auth_value.split(",")}
        menus.append(menu)
    return menus
