from django.db import models
from data.user.models import User
from data.company.team.models import Team
from data.company.models import Company
from data.user.coachee.models import Coachee 
from data.company.models import CompanyUser
# Create your models here.
class Stakeholder(CompanyUser):
    coachee_related = models.ForeignKey(Coachee, on_delete=models.CASCADE,null=True)
    relation = models.TextField(null=True)
    cooperation_years = models.IntegerField(null=True)#Concertyears-app_member
    
    class Meta:
        db_table = 'stakeholder'