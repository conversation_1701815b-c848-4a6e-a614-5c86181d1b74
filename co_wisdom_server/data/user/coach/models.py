from django.db import models
from data.user.models import User
# Create your models here.
class Coach(models.Model):
    user = models.OneToOneField(User, on_delete=models.CASCADE,primary_key=True)
    coach_industry = models.TextField(null=True)#industry
    email_enable = models.BooleanField(default=True)
    sms_enable = models.BooleanField(default=True)
    coach_style = models.TextField(null=True)
    experience_highlights = models.TextField(null=True)
    last_reads = models.TextField(null=True)#LastReadbooks
    career_thinking = models.TextField(null=True)
    summary = models.TextField(null=True)
    grateful_things = models.TextField(null=True)
    fulfilling_things = models.TextField(null=True)
    working_years = models.IntegerField(null=True)#cynx
    coachee_level = models.TextField(null=True)#xyjb
    company_category = models.TextField(null=True)#qysx
    certification = models.TextField(null=True)#jlzz 
    industry = models.TextField(null=True)#jlhy 
    position = models.TextField(null=True)#jlzw
    areas_expert = models.TextField(null=True)#scly
    cowisdom_level = models.TextField(null=True)#jljb coachlevel
    score = models.FloatField(null=True)#Source
    manager_id = models.IntegerField(null=True)
    hours_single = models.IntegerField(default=0, null=True)#jlxssOne
    hours_team = models.IntegerField(default=0, null=True)#jlxssTearm
    working_industry = models.TextField(null=True)#jlgzhy
    course_certification = models.TextField(null=True)#kcrz
    evaluation_certification = models.TextField(null=True)#cprz
    psychological_counseling_certification = models.TextField(null=True)#xlzxrz
    trainer_certification = models.TextField(null=True)#pxsrz
    other_certification = models.TextField(null=True)#qitrz
    certification_file = models.TextField(null=True)#cerfile
    carrer = models.TextField(null=True)#gzjl
    life_objective = models.TextField(null=True)#outlook
    endorsement = models.TextField(null=True)
    cowisdom_remark = models.TextField(null=True)#ManagerMark
    invoice = models.TextField(null=True)
    overall_introduction = models.TextField(null=True)#overallIntroduction 

    class Meta:
        db_table = 'coach'