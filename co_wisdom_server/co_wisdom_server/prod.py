from co_wisdom_server.base import *

DEBUG = False

DATABASES = {
    'default':
        {
            'ENGINE': 'django.db.backends.mysql',  # 数据库引擎
            'NAME': 'co_wisdom_prod',  # 数据库名称
            'HOST': '127.0.0.1',  # 数据库地址，本机 ip 地址 127.0.0.1
            'PORT': 3306,  # 端口
            'USER': 'root',  # 数据库用户名
            'PASSWORD': 'newpassword',  # 数据库密码
            'OPTIONS': {'charset': 'utf8mb4'},
        }
}

# sentry配置
# sentry_sdk.init(
#     dsn="https://<EMAIL>/4504393100230656",
#     integrations=[
#         DjangoIntegration(),
#     ],

    # Set traces_sample_rate to 1.0 to capture 100%
    # of transactions for performance monitoring.
    # We recommend adjusting this value in production.
    # traces_sample_rate=1.0,

    # If you wish to associate users to errors (assuming you are using
    # django.contrib.auth) you may enable sending PII data.
    # send_default_pii=True
# )
# 下单支付回调地址，也可以在调用接口的时候覆盖。
JSAPI_NOTIFY_URL = 'https://www.qzcoach.com/coapi/api/v2/app/order/wechat_pay_call_back/'

# 退款回调地址
REFUNDS_NOTIFY_URL = 'https://www.qzcoach.com/coapi/api/v2/app/order/wechat_refunds_call_back/'