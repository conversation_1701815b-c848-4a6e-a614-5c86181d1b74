"""
WSGI config for co_wisdom_server project.

It exposes the WSGI callable as a module-level variable named ``application``.

For more information on this file, see
https://docs.djangoproject.com/en/3.1/howto/deployment/wsgi/
"""

import os

from django.core.wsgi import get_wsgi_application
from utils.env_settings import get_settings

os.environ.setdefault('DJANGO_SETTINGS_MODULE', get_settings())

application = get_wsgi_application()
