from co_wisdom_server.base import *

DEBUG = True

SITE_URL = 'https://www.qzcoachtest.com/'

DATABASES = {
    'default':
        {
            'ENGINE': 'django.db.backends.mysql',  # 数据库引擎
            'NAME': 'co_wisdom_test',  # 数据库名称
            'HOST': '127.0.0.1',  # 数据库地址，本机 ip 地址 127.0.0.1
            'PORT': 3306,  # 端口
            'USER': 'root',  # 数据库用户名
            'PASSWORD': 'newpassword',  # 数据库密码
            'OPTIONS': {'charset': 'utf8mb4'},
        }
}

# 下单支付回调地址，也可以在调用接口的时候覆盖。
JSAPI_NOTIFY_URL = 'https://www.qzcoachtest.com/coapi/api/v2/app/order/wechat_pay_call_back/'

# 退款回调地址
REFUNDS_NOTIFY_URL = 'https://www.qzcoachtest.com/coapi/api/v2/app/order/wechat_refunds_call_back/'

SECRET_KEY = 'z&$vs=#&$j*z_4ci+xpe-#8u87+_8%x272m$nb#t=31^ppTest'

LARK_DOCUMENT_INTERVIEW_TABLE = {
    "token": "LYHMbE3iDanQvUs4NBEc6T58nId",  # 化学面谈多维表token
    "table": "tblOQDkhojO9RvBD"  # 化学面谈多维表table
}
LARK_DOCUMENT_COACH_INFO_EXCEL = {
    "token": "XKwfst7cFhMHq4tOhnCcLnWBn9e",  # 教练结算信息统计表token
    "sheet_id": "7c36b1",  # 教练结算信息统计表sheet id
    "range": "A:G"  # 教练结算信息统计表C操作列范围
}

# 短信邀请码
SMS_INVITE_CODE = 'c40d8758-3d05-41bf-9a2f-2e34cadd4689'

# 管理后台-结算单用户标识
ADMIN_USER_ID = 1
