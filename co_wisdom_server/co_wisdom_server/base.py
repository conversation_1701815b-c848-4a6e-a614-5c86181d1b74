"""
Django settings for co_wisdom_server project.

Generated by 'django-admin startproject' using Django 3.1.2.

For more information on this file, see
https://docs.djangoproject.com/en/3.1/topics/settings/

For the full list of settings and their values, see
https://docs.djangoproject.com/en/3.1/ref/settings/
"""

from pathlib import Path
import os
from datetime import timedelta
from celery.schedules import crontab
from concurrent_log_handler import ConcurrentRotatingFileHandler
# import sentry_sdk
# from sentry_sdk.integrations.django import DjangoIntegration

# Build paths inside the project like this: BASE_DIR / 'subdir'.
BASE_DIR = Path(__file__).resolve().parent.parent

# Quick-start development settings - unsuitable for production
# See https://docs.djangoproject.com/en/3.1/howto/deployment/checklist/

# SECURITY WARNING: keep the secret key used in production secret!
SECRET_KEY = 'z&$vs=#&$j*z_4ci+xpe-#8u87+_8%x272m$nb#t=31^pp185l'

# SECURITY WARNING: don't run with debug turned on in production!

ALLOWED_HOSTS = ['***********', 'platform.qzcoach.com', 'www.qzcoach.com', '*']

# Application definition

INSTALLED_APPS = [
    'django.contrib.admin',
    'django.contrib.auth',
    'django.contrib.contenttypes',
    'django.contrib.sessions',
    'django.contrib.messages',
    'django.contrib.staticfiles',
    'corsheaders',
    'rest_framework',
    'drf_yasg',
    'data',
    'data.user',
    'data.user.company_admin',
    'data.user.project_owner',
    'data.user.coach',
    'data.user.coachee',
    'data.user.stakeholder',
    'data.user.evaluator',
    'data.company',
    'data.company.team',
    'data.project',
    'controller.account',
    'utils',
    'rest_framework.authtoken',
    'wisdom_v2'
]

MIDDLEWARE = [
    'corsheaders.middleware.CorsMiddleware',
    'django.middleware.security.SecurityMiddleware',
    'django.contrib.sessions.middleware.SessionMiddleware',
    'django.middleware.gzip.GZipMiddleware',
    'django.middleware.common.CommonMiddleware',
    'django.middleware.csrf.CsrfViewMiddleware',
    'django.contrib.auth.middleware.AuthenticationMiddleware',
    'django.contrib.messages.middleware.MessageMiddleware',
    'django.middleware.clickjacking.XFrameOptionsMiddleware',
    'utils.middleware.BlackListToken',
    'utils.middleware.LogMiddleware',
    'utils.middleware.ExceptionMessageMiddleware',
]

ROOT_URLCONF = 'co_wisdom_server.urls'

TEMPLATES = [
    {
        'BACKEND': 'django.template.backends.django.DjangoTemplates',
        'DIRS': [],
        'APP_DIRS': True,
        'OPTIONS': {
            'context_processors': [
                'django.template.context_processors.debug',
                'django.template.context_processors.request',
                'django.contrib.auth.context_processors.auth',
                'django.contrib.messages.context_processors.messages',
            ],
        },
    },
]

WSGI_APPLICATION = 'co_wisdom_server.wsgi.application'

# 跨域增加忽略

CORS_ALLOW_CREDENTIALS = True
# CORS_ORIGIN_ALLOW_ALL = True

CORS_ORIGIN_WHITELIST = [
    'http://localhost:8080',
    'https://localhost:8080',
    'https://admin.qzcoachtest.com',
    'https://admin.qzcoach.com',
    'https://admin.qzcoach.cn',
    'https://assistant.qzcoach.com'
]

# 设置允许哪些请求方式进行跨域请求
CORS_ALLOW_METHODS = (
    'DELETE',
    'GET',
    'OPTIONS',
    'PATCH',
    'POST',
    'PUT',
    'VIEW',
)

# 接受请求头。默认的如下:

CORS_ALLOW_HEADERS = (
    'accept',
    'accept-encoding',
    'authorization',
    'content-type',
    'dnt',
    'origin',
    'user-agent',
    'x-csrftoken',
    'x-requested-with',
)

# Password validation
# https://docs.djangoproject.com/en/3.1/ref/settings/#auth-password-validators

AUTH_PASSWORD_VALIDATORS = [
    {
        'NAME': 'django.contrib.auth.password_validation.UserAttributeSimilarityValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.MinimumLengthValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.CommonPasswordValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.NumericPasswordValidator',
    },
]

# Internationalization
# https://docs.djangoproject.com/en/3.1/topics/i18n/

LANGUAGE_CODE = 'zh-hans'

TIME_ZONE = 'Asia/Shanghai'

USE_I18N = True

USE_L10N = True

USE_TZ = False

# Static files (CSS, JavaScript, Images)
# https://docs.djangoproject.com/en/3.1/howto/static-files/

STATIC_URL = '/swagger_static/'

SWAGGER_SETTINGS = {
    # 'SECURITY_DEFINITIONS': {
    #     'basic': {
    #         'type': 'basic'
    #     }
    # },
    'SECURITY_DEFINITIONS': {
        'apiKey': {
            'type': 'apiKey',
            'name': 'Authorization',
            'in': 'header'
        }
    },
}

UPLOAD_URL = 'Upload/'
MEDIA_URL = 'Upload/'
MEDIA_ROOT = os.path.join(BASE_DIR, "Upload")
REST_FRAMEWORK = {
    'DEFAULT_AUTHENTICATION_CLASSES': (
        'utils.authentication.Authentication',
    ),
    #全局配置JWT验证设置
    # 'DEFAULT_PERMISSION_CLASSES': (
    #         'rest_framework.permissions.IsAuthenticated',
    # ),
}

# Celery 定时任务配置
CELERY_TIMEZONE = 'Asia/Shanghai'
CELERY_BROKER_URL = 'redis://127.0.0.1:6379/1'
CELERY_RESULT_BACKEND = 'redis://127.0.0.1:6379/2'

CELERY_TASK_SERIALIZER = 'pickle'
CELERY_RESULT_SERIALIZER = 'json'
CELERY_ACCEPT_CONTENT = ['pickle', 'json']
BROKER_TRANSPORT_OPTIONS = {'visibility_timeout': 31104000,
                            'fanout_prefix': True}

CELERYD_MAX_TASKS_PER_CHILD = 10
CELERY_WORKER_MAX_TASKS_PER_CHILD = 100
LARK_CELERY_ROBOT = "https://open.feishu.cn/open-apis/bot/v2/hook/71265a3d-0bb7-4cb8-9827-837c76ed2860"
LARK_HOME_ERROR_ROBOT = "https://open.feishu.cn/open-apis/bot/v2/hook/22be6307-1963-4baa-a866-5580a81f4172"
LARK_ERROR_MESSAGE_ROBOT = "https://open.feishu.cn/open-apis/bot/v2/hook/06912531-f42d-43ea-945c-59d1d9675a74"
LARK_ERROR_MESSAGE_ROBOT_DEV_TEST = "https://open.feishu.cn/open-apis/bot/v2/hook/ecaf6718-6a69-4e2a-b29d-212bbe6200a2"
LARK_WORK_WECHAT_MESSAGE_ROBOT = "https://open.feishu.cn/open-apis/bot/v2/hook/3d54ec0a-c87d-4944-8d29-807a2c732ffa"
LARK_WECHAT_PAY_ROBOT = "https://open.feishu.cn/open-apis/bot/v2/hook/9665e142-0e61-40f8-8792-12005ef27554"

CELERY_ROUTES = {
    'share_task': 'co_wisdom_celery_task',
}

CELERY_BEAT_SCHEDULE = {
    # 'Save Content': {
    #     'task': 'utils.task.save_content',
    #     'schedule': 5,
    # },
    'write Content': {
        'task': 'utils.task.write_content',
        'schedule': crontab(minute=0, hour=0)  # 每天执行一次
    },
    'Send Email Of 9': {
        'task': 'utils.task.send_email_of_9h',
        'schedule': crontab(minute=0, hour=9)  # 每天9点执行一次
    },
    'Send Email Of Remind': {
        'task': 'utils.task.send_email_of_remind',
        'schedule': crontab(minute=0, hour=9)  # 每天9点执行一次
    },
    'Send Email Of 16': {
        'task': 'utils.task.send_email_of_16h',
        'schedule': crontab(minute=0, hour=16)  # 每天16点执行一次
    },
    'Send Email Timer': {
        'task': 'utils.task.interview_before_task',
        'schedule': 60,  # 每分钟执行一次
    },

    'Send Msg Of Login': {
        'task': 'utils.task.send_login_wechat_msg',
        'schedule': crontab(minute=0, hour=9)  # 每天9点执行一次
    },

    'Send User Behavior Data': {
        'task': 'utils.task.send_user_behavior_data',
        'schedule': crontab(minute=0, hour=9)  # 每天9点执行一次
    },

    'Send Interview Remind Of 12': {
        'task': 'utils.task.send_interview_of_12',
        'schedule': crontab(minute=0, hour=12)  # 每天12点执行一次
    },
    'Update Interview Status': {
        'task': 'utils.task.update_interview_status',
        'schedule': 60,  # 每分钟执行一次
    },
    'Update Evaluation Module Status': {
        'task': 'utils.task.update_evaluation_module_status',
        'schedule': crontab(minute=0, hour=0),  # 每天0点执行一次
    },
    'Send Interview Start Message Of 8': {
        'task': 'utils.task.send_interview_start_message_of_8',
        'schedule': crontab(minute=0, hour=8),  # 每天8点执行一次
    },
    'Send Interview Record Write Message Of 9': {
        'task': 'utils.task.send_interview_record_write_message_of_9',
        'schedule': crontab(minute=0, hour=9),  # 每天9点执行一次
    },
    'Send Add Interview Gt 14 Of 9': {
        'task': 'utils.task.send_add_interview_gt_14_of_9',
        'schedule': crontab(minute=0, hour=9, day_of_week=1),  # 每周一9点执行一次
    },
    'Coach Task Write Remind Of 9': {
        'task': 'utils.task.coach_task_write_remind_of_9',
        'schedule': crontab(minute=0, hour=9)   # 每天9点执行一次
    },
    'Send Growth Goal Reminder of 8': {
        'task': 'utils.task.send_work_wechat_growth_goal_reminder_of_8',
        'schedule': crontab(minute=0, hour=8)   # 每天8点执行一次
    },
    'Send Undone Interview Reminder Of 8': {
        'task': 'utils.task.send_work_wechat_undone_interview_reminder_of_8',
        'schedule': crontab(minute=0, hour=8)  # 每天8点执行一次
    },
    'Send Next Interview Reminder Of 8': {
        'task': 'utils.task.send_work_wechat_next_interview_reminder_of_8',
        'schedule': crontab(minute=0, hour=8, day_of_week=1)  # 每周一8点执行一次
    },
    'Send Interview Reminder Of 8': {
        'task': 'utils.task.send_work_wechat_interview_reminder_of_8',
        'schedule': crontab(minute=0, hour=8)  # 每天8点执行一次
    },
    'Send Coach Task Of 8': {
        'task': 'utils.task.send_work_wechat_coach_task_of_8',
        'schedule': crontab(minute=0, hour=8)  # 每天8点执行一次
    },
    'Send Coach Task 7 Day Of 8': {
        'task': 'utils.task.send_work_wechat_coach_task_7day_of_8',
        'schedule': crontab(minute=0, hour=8)  # 每天8点执行一次
    },
    'Send Work Wx Message Count': {
        'task': 'utils.task.send_work_wx_message_count',
        'schedule': crontab(minute=59, hour=23)  # 每天23点59分执行一次
    },
    'Send Work Wx_start Evaluation Of 9 Wx Message Count': {
        'task': 'utils.task.send_work_wx_start_evaluation_of_9',
        'schedule': crontab(minute=0, hour=9)  # 每天9点执行一次
    },
    'Send Work Wx Undone Evaluation Of 9': {
        'task': 'utils.task.send_work_wx_undone_evaluation_of_9',
        'schedule': crontab(minute=0, hour=9)  # 每天9点执行一次
    },
    'Send Work Wx Reading Of 9': {
        'task': 'utils.task.send_work_wx_reading_of_9',
        'schedule': crontab(minute=0, hour=9)  # 每天9点执行一次
    },
    # 集体辅导通知过于频繁，临时移除
    'Send Work Wx Offline Group Coach Of 20': {
        'task': 'utils.task.send_work_wx_offline_group_coach_of_20',
        'schedule': crontab(minute=0, hour=20)  # 每天下午8点执行一次
    },
    'Send Change Observation Write Message Of 9': {
        'task': 'utils.task.send_change_observation_write_message_of_9',
        'schedule': crontab(minute=0, hour=9)  # 每天9点执行一次
    },
    'Send Stakeholder Coach Task Write Message Of 9': {
        'task': 'utils.task.send_stakeholder_coach_task_write_message_of_9',
        'schedule': crontab(minute=0, hour=9)  # 每天9点执行一次
    },
    'Close UnPaid Order': {
        'task': 'utils.task.close_unpaid_order',
        'schedule': 60,  # 每分钟执行一次
    },
    'Send Interview Record Write Remind Message Of 21': {
        'task': 'utils.task.send_interview_record_write_remind_message_of_21',
        'schedule': crontab(minute=0, hour=21)  # 每天21点执行一次
    },
    'Send Interview Record Write Remind Message 2h': {
        'task': 'utils.task.send_interview_record_write_remind_message_2h',
        'schedule': 60,  # 每分钟执行一次
    },
    'Send Interview Start Before 30': {
        'task': 'utils.task.send_interview_start_before_30',
        'schedule': 60,  # 每分钟执行一次
    },
    'Send Project Interview Start Before 30': {
        'task': 'utils.task.send_project_interview_start_before_30',
        'schedule': 60,  # 每分钟执行一次
    },
    'Lark Send Unconfirmed Interview Message': {
        'task': 'utils.task.lark_send_unconfirmed_interview_message',
        'schedule': crontab(minute=0, hour='0,8,12,16,20'),  # 每天8，12，16，20，0点执行一次
    },
    'Update Work Wechat User Customer Dict': {
        'task': 'utils.task.update_work_wechat_user_customer_dict',
        'schedule': 60 * 60 * 4,  # 每4小时执行一次
    },
    'Update Work Wechat User Department Dict': {
        'task': 'utils.task.update_work_wechat_department_users',
        'schedule': 60 * 60 * 4,  # 每4小时执行一次
    },
    'Update Wechat Stable Token': {
        'task': 'utils.task.update_wechat_stable_token',
        'schedule': 60 * 4,  # 每4分钟执行一次
    },
    'Update Coach Offer Status': {
        'task': 'utils.task.update_coach_offer_status',
        'schedule': 60,  # 每分钟执行一次
    },
    'Chemical Interview Later 30': {
        'task': 'utils.task.chemical_interview_later_30',
        'schedule': 60,  # 每分钟执行一次
    },
    'Send Interview Before 30': {
        'task': 'utils.task.send_interview_before_30',
        'schedule': 60,  # 每分钟执行一次
    },
    'Send Chemical Interview Not Appointment Coach Message': {
        'task': 'utils.task.send_chemical_interview_not_appointment_coach_message',
        'schedule': crontab(minute=0, hour='8,10,12,14,16,18,20,22,0'),  # 每天8-24点，每两小时执行一次
    },
    'Send Not Join Interview Message': {
        'task': 'utils.task.send_not_join_interview_message',
        'schedule': 60,  # 每分钟执行一次
    },
    'Send Stakeholder Interview Record Notice': {
        'task': 'utils.task.stakeholder_interview_record_30_notice',
        'schedule': 60,  # 每分钟执行一次
    },
    'Send Stakeholder Interview Report Notice': {
        'task': 'utils.task.stakeholder_interview_report_30_notice',
        'schedule': 60,  # 每分钟执行一次
    },
    'Send Chemical Interview Coach Schedule': {
        'task': 'utils.task.chemical_interview_coach_schedule',
        'schedule': crontab(minute=0, hour='0,8,10,12,14,16,18,20,22'),  # 每天0,8,10,12,14,16,18,20,22点执行一次
    },
    'Send Stakeholder Interview Coach Schedule': {
        'task': 'utils.task.stakeholder_interview_coach_schedule',
        'schedule': crontab(minute=0, hour='0,8,10,12,14,16,18,20,22'),  # 每天0,8,10,12,14,16,18,20,22点执行一次
    },
    'Send Stakeholder Interview Reservation Count': {
        'task': 'utils.task.send_chemical_interview_reservation_count_message',
        'schedule': crontab(minute=0, hour='0,8,10,12,14,16,18,20,22'),  # 每天0,8,10,12,14,16,18,20,22点执行一次
    },
    'Generate Lbi Personal Report': {
        'task': 'utils.task.generate_lbi_personal_report',
        'schedule': crontab(minute=0, hour=1)  # 每天1点执行一次
    },
    # 定时生成结算订单
    'Generate Business Order': {
        'task': 'utils.task.generate_business_order',
        'schedule': 300,  # 每5分钟执行一次
    },
    'Update Activity Coach Status': {
        'task': 'utils.task.update_activity_coach_status',
        'schedule': crontab(minute=0, hour=0)  # 每天执行一次
    },
    'Send Personal Apply Internship': {
        'task': 'utils.task.send_personal_apply_internship',
        'schedule': crontab(minute=0, hour=9)  # 每天9点执行一次
    },
    'Update Change Observation Personal Report': {
        'task': 'utils.task.update_change_observation_personal_report',
        'schedule': crontab(minute=0, hour=9)  # 每天9点执行一次
    },
    'Send Lark Coach Update Resume Msg': {
        'task': 'utils.task.send_lark_coach_update_resume_msg',
        'schedule': crontab(minute=0, hour=9)  # 每天9点执行一次
    },
    # 'Refund Timeout Activity Order All': {
    #     'task': 'utils.task.refund_timeout_activity_order_all',
    #     'schedule': crontab(minute=0, hour=0)  # 每天0点执行一次
    # },
    'Project End Date Check': {
        'task': 'utils.task.project_end_date_check',
        'schedule': crontab(minute=0, hour=10)  # 每天10点执行一次
    },
    'To C Interview Record Notice': {
        'task': 'utils.task.to_c_interview_record_notice',
        'schedule': crontab(minute=0, hour=10)  # 每天10点执行一次
    },
    'Send Company Interview Remind': {
        'task': 'utils.task.company_interview_remind',
        'schedule': 60,  # 每分钟执行一次
    },
}




# SIMPLE_JWT = {
#     'ACCESS_TOKEN_LIFETIME': timedelta(days=7),
#     'REFRESH_TOKEN_LIFETIME': timedelta(days=14),
#     'ROTATE_REFRESH_TOKENS': False,
#     'BLACKLIST_AFTER_ROTATION': True,
#     'UPDATE_LAST_LOGIN': False,
#
#     # 'ALGORITHM': 'HS256',
#     # 'SIGNING_KEY': settings.SECRET_KEY,
#     'VERIFYING_KEY': None,
#     'AUDIENCE': None,
#     'ISSUER': None,
#
#     'AUTH_HEADER_TYPES': ('Bearer',),
#     'AUTH_HEADER_NAME': 'HTTP_AUTHORIZATION',
#     'USER_ID_FIELD': 'id',
#     'USER_ID_CLAIM': 'id',
#
#     'AUTH_TOKEN_CLASSES': ('rest_framework_simplejwt.tokens.AccessToken',),
#     'TOKEN_TYPE_CLAIM': 'token_type',
#
#     'JTI_CLAIM': 'jti',
#
#     'SLIDING_TOKEN_REFRESH_EXP_CLAIM': 'refresh_exp',
#     'SLIDING_TOKEN_LIFETIME': timedelta(minutes=5),
#     'SLIDING_TOKEN_REFRESH_LIFETIME': timedelta(days=1),
# }


EMAIL_BACKEND = 'django.core.mail.backends.smtp.EmailBackend'
EMAIL_HOST = 'smtp.zoho.com'
EMAIL_PORT = 465
EMAIL_HOST_USER = '<EMAIL>'
EMAIL_HOST_PASSWORD = 'o?r4wSln'
EMAIL_USE_SSL = True
DEFAULT_FROM_EMAIL = '群智平台管理员 <<EMAIL>>'

SITE_URL = 'https://www.qzcoach.com/'
SITE_NAME = '群智企业教练平台'

GENDER = {
    1: '先生',
    2: '女士'
}

PROJECT_BLACK_LIST = []
LOG_PATH = os.getcwd() + '/run'
if not os.path.exists(LOG_PATH):
    os.makedirs(LOG_PATH)

LOGGING = {
    'version': 1,
    'disable_existing_loggers': False,
    'formatters': {
        'standard': {
            'format': '%(asctime)s [%(threadName)s:%(thread)d] [%(name)s:%(lineno)d] [%(module)s:%(funcName)s] \
             [%(levelname)s]- %(message)s'},
        'log': {
            'format': '%(levelname)s %(asctime)s %(message)s'
        },
        'time': {
            'format': '%(asctime)s '
        }
    },
    'filters': {
    },
    'handlers': {
        # 短信日志
        'sms': {
            'level': 'INFO',
            'class': 'concurrent_log_handler.ConcurrentRotatingFileHandler',  # 保存到文件，自动切
            'filename': LOG_PATH + '/sms.log',
            'maxBytes': 1024 * 1024 * 50,  # 日志大小 50M
            'backupCount': 5,
            'formatter': 'log',
            'encoding': "utf-8"
        },
        'getui': {
            'level': 'INFO',
            'class': 'concurrent_log_handler.ConcurrentRotatingFileHandler',  # 保存到文件，自动切
            'filename': LOG_PATH + '/getui.log',
            'maxBytes': 1024 * 1024 * 50,  # 日志大小 50M
            'backupCount': 5,
            'formatter': 'log',
            'encoding': "utf-8"
        },
        'all_log': {
            'level': 'DEBUG',
            'class': 'concurrent_log_handler.ConcurrentRotatingFileHandler',  # 保存到文件，自动切
            'filename': LOG_PATH + '/all_log.log',
            'maxBytes': 1024 * 1024 * 30,  # 日志大小 30M
            'backupCount': 5,
            'formatter': 'log',
            'encoding': "utf-8"
        },
        'others': {
            'level': 'INFO',
            'class': 'concurrent_log_handler.ConcurrentRotatingFileHandler',  # 保存到文件，自动切
            'filename': LOG_PATH + '/others.log',
            'maxBytes': 1024 * 1024 * 30,  # 日志大小 30M
            'backupCount': 5,
            'formatter': 'log',
            'encoding': "utf-8"
        },
        'api_action': {
            'level': 'INFO',
            'class': 'concurrent_log_handler.ConcurrentRotatingFileHandler',  # 保存到文件，自动切
            'filename': LOG_PATH + '/api_action.log',
            'maxBytes': 1024 * 1024 * 30,  # 日志大小 30M
            'backupCount': 5,
            'formatter': 'log',
            'encoding': "utf-8"
        },
        'third_party_error': {
            'level': 'INFO',
            'class': 'concurrent_log_handler.ConcurrentRotatingFileHandler',  # 保存到文件，自动切
            'filename': LOG_PATH + '/third_party_error.log',
            'maxBytes': 1024 * 1024 * 30,  # 日志大小 30M
            'backupCount': 5,
            'formatter': 'log',
            'encoding': "utf-8"
        },

        # 'console': {
        #     'level': 'DEBUG',
        #     'class': 'logging.StreamHandler',
        #     'formatter': 'log'
        # },

    },
    'loggers': {
        'sms': {
            'handlers': ['sms'],
            'level': 'INFO',
            'propagate': False
        },
        'getui': {
            'handlers': ['getui'],
            'level': 'INFO',
            'propagate': False
        },
        'all_log': {
            'handlers': ['all_log'],
            'level': 'DEBUG',
            'propagate': False
        },
        'others': {
            'handlers': ['others'],
            'level': 'INFO',
            'propagate': False
        },
        'api_action': {
            'handlers': ['api_action'],
            'level': 'INFO',
            'propagate': False
        },
        'third_party_error': {
            'handlers': ['third_party_error'],
            'level': 'INFO',
            'propagate': False
        },
    }
}

# 小程序
APP_ID = 'wxf8ab6dd6bffaab9e'
APP_SECRET = '421d1102dddaf58531ebf4f68bb80538'

# 支付私钥路径
APICLIENT_KEY = '/root/wisdom/apiclient_key.pem'

# 订阅号
SUBSCRIPTION_APP_ID = 'wxd11ad54683712b4b'
SUBSCRIPTION_APP_SECRET = '10fba8e309b1e68f48d1fca75900bffb'
WECHAT_ACCESS_TOKEN_URL = 'redis://127.0.0.1:6379/3'

# APP
APPID = 'svgZPqhDurADsl3akXNtb6'
APPKEY = 'Dubkr2zU1lADH2ROW8tud5'
APPSECRET = 'Lqvkt1gwTHARy9p9ZvfOI2'
MASTERSECRET = 'n8zMGCdAvmAlB4Yu2PvG32'
# 登录提醒
WELCOME_TEMPLATE = '81QoVxThgrEtEU_IheBNU1gKyywbXjQES2PZc7kcqmI'
# 明日辅导提醒
TOMORROW_INTERVIEW_TEMPLATE = 'AOT2rSFFgpEhp6LCJC1UkADMzZTOXmNhQdkCp8NaB-o'
# 开始辅导前提醒
BEFORE_INTERVIEW_TEMPLATE = 'z0c_DoVfMzWalHzgvPZe64HEp-WHHNE0FBbd-CC-Uag'
# 提醒完成约谈记录
END_RECORD_TEMPLATE = 'tYJG50SpXj5lywWg2dH_d4E2oYYd6U3lxZeVtUSVCEg'
# 个人辅导开始通知
PERSONAL_BEFORE_INTERVIEW_TEMPLATE = 'z0c_DoVfMzWalHzgvPZe62cyI9iqS9R-wMSLXANjJuc'
# 个人辅导教练同意通知
PERSONAL_COACH_AGREE_INTERVIEW_TEMPLATE = 'IlnUspJRNR1Y_LPgWRO9X5N2HFhHMXNhJwGw2v4FtOU'
# 个人辅导修改时间通知
PERSONAL_INTERVIEW_UPDATE_TIME_TEMPLATE = 'x4FXBW0695MDIyE7837InPm11oQaYgFyn2ZhXX_mno0'

# 微信开放平台
OPEN_APP_ID = 'wx2505a76f7f346877'
OPEN_APP_SECRET = '94b5eb391ed9efc81c0a34585143da6d'

# 腾讯云身份验证
SecretId = 'AKID68L6DQ7pbA1PH5r1eeMy9npP7Cln7bV1urn6'
SecretKey = 'HkL74I7h17cmdAtp3ovk9sfNv934hhnwHez9yy'

XIAOE_TECH_APP_ID = "app0MfriE4F4911"
XIAOE_TECH_CLIENT_ID = "xoptlybjzaf3656"
XIAOE_TECH_SECRET_KEY = "4o9pr6CiwuvMn2omliiOpYiXvYvahQyq"

WORD_WECHAT_CALENDAR_COLOR = '#FF3030'
WORK_WECHAT_CORPID = 'ww238dcb59339947aa'
WORK_WECHAT_AGENT_ID = 1000009
WORK_WECHAT_CORPSECRET = 'v6LUoYsJy2kVKQCnc8eMXA34JXN9-arH6jhUu_EnpvM'
WORK_WECHAT_CALLBACK_TOKEN = 'Nwj'
WORK_WECHAT_CALLBACK_KEY = 'f1KA1xe13gefMsngemZWNwGeVPXfao4Jkenc8rmygHY'
WORK_WECHAT_CONTACTS_SECRET = 'TxsnVFs9r-GBeM1esezRXFMSWm551tghgJ6pkPjoS4U'
# WORK_WECHAT_COACH_ID = 1942016137
WORK_WECHAT_ASSISTANT_AGENT_ID = 1000013
WORK_WECHAT_ASSISTANT_AGENT_SECRET = '5GVQwo7q9EqskYL4_D48nM9MHCESW4b5H1aN4-4axg0'

WORK_WECHAT_COACH_DEPARTMENT_ID = 1942016139
WORK_WECHAT_MANAGE_DEPARTMENT_ID = 1942016145
WORK_WECHAT_EXTERNAL_DEPARTMENT_ID = 1
DEPARTMENT_EVENT_KEY = 'change_contact'
CHANGE_EXTERNAL_USER = 'change_external_contact'
WORK_WECHAT_CALLBACK_URL = [
    'https://www.qzcoach.cn/coapi/api/v2/app/work_wechat/callback/',
    # 'https://www.qzcoachtest.com/coapi/api/v2/app/work_wechat/callback/',
    'https://www.qzcoach.com/coapi/api/v2/app/work_wechat/callback/']
PROJECT_OPERAE_QR_CODE = 'https://open.work.weixin.qq.com/wwopen/userQRCode?vcode=vc8b96af1abdd77067'
WORK_WECHAT_DEPARTMENT_REDIS_KEY = 'work_wechat_department_user'


# lbi报告生成pdf文件
PDF_URL = "http://reportexport.qzcoach.com/report/"

# 教练简历的分享图
RESUME_SHARE_IMAGE_URL = "http://imagestack.qzcoach.com/"
RESUME_BACKGROUND_IMAGE_URL = 'https://static.qzcoach.com/app/view_background/myresume/share_resume.png'

# token缓存
TOKEN_LOGIN_REDIS = 'redis://127.0.0.1:6379/1'
# 临时缓存
DATA_REDIS = 'redis://127.0.0.1:6379/2'
# 个推
GETUI_TOKEN_URL = 'redis://127.0.0.1:6379/4'
# 第三方数据
THIRD_PARTY_DATA_REDIS = 'redis://127.0.0.1:6379/7'
# 延期风险邮件记录
DELAY_EMAIL = 'redis://127.0.0.1:6379/5'
# 企业微信消息缓存
WORK_WECHAT_REDIS = 'redis://127.0.0.1:6379/6'
# 利益相关者邮件发送redis
STAKEHOLDER_EMAIL_SEND_REDIS = 'redis://127.0.0.1:6379/6'


# 阿里云sls服务配置基础信息
SLS_ENDPOINT = 'cn-beijing.log.aliyuncs.com'
SLS_ACCESS_KEY_ID = 'LTAI5tMdifo6TAMifEQpqgHp'
SLS_ACCESS_KEY = '******************************'
SLS_PROJECT = 'co-wisdom'
SLS_LOG_STORE = 'cw-ls1'
# sls埋点数据类型
# 前后端共用，需要新增修改对应type字段，文档地址：https://sx683824si.feishu.cn/wiki/wikcn8numetwuwBbcA5nQqJdS0g
# 5:企微消息推送；7:后端第三方请求 8:后端业务请求拦截
SLS_SEND_WORK_WECHAT_LOG = 5
SLS_SEND_THIRD_API_LOG = 7
SLS_SEND_BUSINESS_API_LOG = 8


# 阿里云sdn基础链接
ALIYUN_SDN_BASE_URL = 'https://static.qzcoach.com/'

# 教练任务/改变观察反馈/利益相关者访谈 默认图片链接
NOT_SELECT_IMAGE_URL = 'https://static.qzcoach.com/app/report_not_fill.png'
# 测评默认图片链接
SELECT_IMAGE_URL = 'https://static.qzcoach.com/app/report_fill.png'

CACHES = {
    "default": {
        "BACKEND": "django_redis.cache.RedisCache",
        "LOCATION": "redis://127.0.0.1:6379/7",
        "OPTIONS": {
            "CLIENT_CLASS": "django_redis.client.DefaultClient",
        }
    }
}

# 微信支付相关

# 微信支付商户号，服务商模式下为服务商户号，即官方文档中的sp_mchid。
MCHID = '1623521932'

# 商户证书序列号
CERT_SERIAL_NO = '556E2D5C3976067686E7AD3FCEA9B00F5E7DB5D8'

# API v3密钥， https://pay.weixin.qq.com/wiki/doc/apiv3/wechatpay/wechatpay3_2.shtml
APIV3_KEY = 'QUwfWktN2tUVkMEz4u2p2gC8SH6AMqDN'

# APPID，应用ID，服务商模式下为服务商应用ID，即官方文档中的sp_appid，也可以在调用接口的时候覆盖。 （上方APP_ID）

# 微信支付平台证书缓存目录，初始调试的时候可以设为None，首次使用确保此目录为空目录。
CERT_DIR = './cert'

# 接入模式：False=直连商户模式，True=服务商模式。
PARTNER_MODE = False

# 代理设置，None或者{"https": "http://10.10.1.10:1080"}，
# 详细格式参见https://docs.python-requests.org/zh_CN/latest/user/advanced.html
PROXY = None

# 声网
SDK_APP_ID = '1400781343'
SDK_APP_SECRET = 'be8a8ec9b8900faf83753aeacddffc1e88fee6670c5a18d40d47f9efeb7c090e'

# 开发测试环境飞书机器人
DEV_TEST_LARK_ROBOT = 'https://open.feishu.cn/open-apis/bot/v2/hook/eac66b26-e9e0-4b03-9eba-5426e249fcc4'

# 教练申请 项目匹配教练通知机器人
PERSONAL_APPLY_PROJECT_LARK_ROBOT = 'https://open.feishu.cn/open-apis/bot/v2/hook/9d0fc717-867f-4242-b196-91820edfea42'

# 开发测试环境400飞书机器人
LARK_BUSINESS_ERROR_MESSAGE_ROBOT_DEV_TEST = 'https://open.feishu.cn/open-apis/bot/v2/hook/093643e8-1cf5-4982-a95d-632c3246c303'

# 正式环境400飞书机器人
LARK_BUSINESS_ERROR_MESSAGE_ROBOT = 'https://open.feishu.cn/open-apis/bot/v2/hook/8a754789-331c-4081-9422-4631aab9c175'


# 飞书云文档app_id
LARK_DOCUMENT_APP_ID = '********************'

# 飞书云文档密钥
LARK_DOCUMENT_APP_SECRET = 'hkqS3pX4mdHUnUrNZpgpQpeE1dfyrUpj'

# 飞书云文档电子表格-错误统计表
LARK_DOCUMENT_ERR_EXCEL = {
    "token": "shtcnt9OcxIzurnxhDDJnTvp9vg",  # 错误统计表token
    "sheet_id": "4ba546",  # 错误统计表sheet id
    "range": "A:J"  # 错误统计表C操作列范围
}
LARK_DOCUMENT_COACH_INFO_EXCEL = {
    "token": "U1rSsKIT8hz1Jmtl0mhcY80Enqb",  # 教练结算信息统计表token
    "sheet_id": "9b8e0c",  # 教练结算信息统计表sheet id
    "range": "A:G"  # 教练结算信息统计表C操作列范围
}

LARK_DOCUMENT_INTERVIEW_TABLE = {
    "token": "IeHPb2er6afNaPsrMOpcPs0tnuh",  # 化学面谈多维表token
    "table": "tblJYHDm1L8CBFGy"  # 化学面谈多维表table
}

# 化学面谈飞书机器人
LARK_CHEMICAL_INTERVIEW_ROBOT = 'https://open.feishu.cn/open-apis/bot/v2/hook/ab07c14c-bec5-4fa5-96dc-0eb8e2633c30'

# 利益相关者访谈飞书机器人
LARK_STAKEHOLDER_INTERVIEW_ROBOT = 'https://open.feishu.cn/open-apis/bot/v2/hook/c35ed63b-8064-4f51-8c57-c22959364e26'

# 项目消息提醒
LARK_PROJECT_ROBOT = 'https://open.feishu.cn/open-apis/bot/v2/hook/f47d4243-0ebd-4df8-930c-bc6b1ff79cf8'

# 改变观察反馈提醒
LARK_CHANGE_OBSERVATION_ROBOT = "https://open.feishu.cn/open-apis/bot/v2/hook/06a8842b-a2cf-4b3a-afb4-3436e3372d41"

# 个人信息收集提交提醒
LARK_USER_INFO_COLLECT_ROBOT = 'https://open.feishu.cn/open-apis/bot/v2/hook/8f263193-1b73-4e0b-9ff3-89a2c13d835f'


# 默认项目运营手机号（胡畔）
DEFAULT_PROJECT_MANAGER_PHONE = '15911091301'

# 默认项目运营二维码（胡畔）
DEFAULT_PROJECT_MANAGER_QRCODE = 'http://open.work.weixin.qq.com/wwopen/userQRCode?vcode=vc8b96af1abdd77067'

# 默认C端运营二维码（南方）
DEFAULT_TO_C_MANAGER_QRCODE = 'https://static.qzcoach.com/app/workwechat/contactUs_qrcode.png'

# 短信使用的模板
SMS_TEMPLATE_PREFIX = '【群智企业教练】'

# 短信邀请码
SMS_INVITE_CODE = 'b6f5cf44-2e2a-4f3c-be26-63401e83d4e4'

# 管理后台-结算单用户标识
ADMIN_USER_ID = 1282
