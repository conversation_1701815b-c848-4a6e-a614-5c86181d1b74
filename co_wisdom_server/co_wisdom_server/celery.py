from __future__ import absolute_import, unicode_literals

import os

from celery import Celery, platforms
from kombu import Exchange, Queue
from utils.env_settings import get_settings

# set the default Django settings module for the 'celery' program.
conf = get_settings()
os.environ.setdefault('DJANGO_SETTINGS_MODULE', conf)

app = Celery('co_wisdom')

# Using a string here means the worker don't have to serialize
# the configuration object to child processes.
# - namespace='CELERY' means all celery-related configuration keys
#   should have a `CELERY_` prefix.
app.config_from_object('django.conf:settings', namespace='CELERY')

# Load task modules from all registered Django app configs.
app.autodiscover_tasks()

# 允许root 用户运行celery
platforms.C_FORCE_ROOT = True

# 设置队列
default_exchange = Exchange('default', type='direct')
task_exchange = Exchange('task', type='direct')

app.conf.task_queues = (
    Queue('default', default_exchange, routing_key='default'),
    Queue('task', task_exchange, routing_key='task'),
)

app.conf.task_default_queue = 'default'
app.conf.task_default_exchange = 'default'
app.conf.task_default_routing_key = 'default'

@app.task(bind=True)
def debug_task(self):
    print('Request: {0!r}'.format(self.request))


app_celery = app
