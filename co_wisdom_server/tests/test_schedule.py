from copy import deepcopy

import pendulum
from django.db.models import Q

from wisdom_v2.common.recurring_schedule_public import get_next_repeat_date, date_in_recurring_schedule, \
    generate_repeated_instances
from wisdom_v2.common.schedule_public import excerpt_schedule, split_schedule, get_schedule_day_list, \
    is_time_slot_available
from django.test import TestCase

from wisdom_v2.enum.schedule_enum import RecurringScheduleRepeatTypeEnum
from wisdom_v2.models import Schedule, PublicAttr, Project, User
from wisdom_v2.enum.service_content_enum import ScheduleTypeEnum
from utils.api_response import WisdomValidationError
from datetime import datetime, timedelta, time

from wisdom_v2.models_file import RecurringSchedule


def add_schedule(project, user, start_time, end_time, type):
    attr = PublicAttr.objects.create(
        project=project,
        user=user,
        start_time=start_time,
        end_time=end_time,
        created_at=datetime.now(),
    )
    schedule = Schedule.objects.create(
        title='test',
        type=type,
        platform=1,
        remark='test',
        is_open_manage=False,
        public_attr=attr,
        created_at=datetime.now(),
        is_all_day=False,
    )
    return schedule


def add_recurring_schedule(schedule, repeat_type, end_repeat_date, excluded_dates):
    recurring_schedule = RecurringSchedule.objects.create(
        schedule=schedule,
        repeat_type=repeat_type,
        end_repeat_date=end_repeat_date,
        excluded_dates=excluded_dates,
    )
    return recurring_schedule


class ScheduleViewTest(TestCase):
    def setUp(self):
        # 创建测试用的PublicAttr和Schedule实例
        self.project = Project.objects.create(name=1)
        self.user = User.objects.create(name=1)
        self.attr = PublicAttr.objects.create(
            project=self.project,
            user=self.user,
            start_time=datetime.combine(datetime.now().date(), time.min),
            end_time=datetime.combine(datetime.now().date(), time.max),
            created_at=datetime.now(),
        )
        self.schedule = Schedule.objects.create(
            title='test',
            type=ScheduleTypeEnum.unavailable,
            platform=1,
            remark='test',
            is_open_manage=False,
            public_attr=self.attr,
            created_at=datetime.now(),
            is_all_day=False,
        )

    def tearDown(self):
        # 在每个测试方法之后清空测试数据
        Schedule.objects.all().delete()

    def test_excerpt_schedule(self):
        # 检查当schedule为空时，函数应该抛出异常
        with self.assertRaises(WisdomValidationError):
            excerpt_schedule(None, self.attr.start_time, self.attr.end_time)

        # 检查当start_time和end_time都为空时，函数应该抛出异常
        with self.assertRaises(WisdomValidationError):
            excerpt_schedule(self.schedule, None, None)
        schedule = add_schedule(
            self.project, self.user,
            datetime.now(),
            datetime.combine(datetime.now() + timedelta(days=4), time.min),
            ScheduleTypeEnum.available)

        start_time = schedule.public_attr.start_time + timedelta(days=1)
        end_time = schedule.public_attr.end_time - timedelta(days=1)

        # 检查正常情况下函数的行为
        schedule = excerpt_schedule(schedule, start_time, end_time)
        self.assertEqual(schedule.public_attr.start_time.date(), start_time.date())
        self.assertEqual(schedule.public_attr.end_time.date(), end_time.date())

        # 当start_time为None但end_time不为None时，函数应只修改end_time
        schedule = excerpt_schedule(schedule, None, end_time)
        self.assertEqual(schedule.public_attr.start_time, schedule.public_attr.start_time)
        self.assertEqual(schedule.public_attr.end_time.date(), end_time.date())

        # 当end_time为None但start_time不为None时，函数应只修改start_time
        schedule = excerpt_schedule(schedule, start_time, None)
        self.assertEqual(schedule.public_attr.start_time.date(), start_time.date())
        self.assertEqual(schedule.public_attr.end_time, schedule.public_attr.end_time)

    def test_split_schedule(self):
        # 当日程只在一天内，函数应该返回单一日程列表
        self.attr.start_time = datetime.now()
        self.attr.end_time = self.attr.start_time
        self.attr.save()
        schedules = split_schedule(self.schedule)
        self.assertEqual(len(schedules), 1)

        # 当日程跨越多天，函数应返回等于跨越天数的日程列表
        self.attr.start_time = datetime.now()
        self.attr.end_time = self.attr.start_time + timedelta(days=2)
        self.attr.save()
        schedules = split_schedule(self.schedule)
        self.assertEqual(len(schedules), 3)

        # 检测第一天的开始/结束时间
        self.assertEqual(schedules[0].public_attr.start_time, self.attr.start_time)
        self.assertEqual(schedules[0].public_attr.end_time.date(), self.attr.start_time.date())
        # 检测最后一天的开始/结束时间
        self.assertEqual(schedules[-1].public_attr.start_time.date(), self.attr.end_time.date())
        self.assertEqual(schedules[-1].public_attr.end_time, self.attr.end_time)
        # 检测中间时间的开始/结束时间是不是当天最大/最小时间
        self.assertEqual(schedules[1].public_attr.start_time.date(), schedules[1].public_attr.end_time.date())
        self.assertEqual(schedules[1].public_attr.start_time.time(), time.min)
        self.assertEqual(schedules[1].public_attr.end_time.time(), time.max)

        # 创建一个结束时间是第二天00:00:00点的日程
        schedule = add_schedule(
            self.project, self.user,
            datetime.now(),
            datetime.combine(datetime.now() + timedelta(days=1), time.min),
            ScheduleTypeEnum.available)
        schedules = split_schedule(schedule)
        # 结束时间是第二天00:00:00点的日程不需要在第二天创建一条00:00-00:00的数据
        self.assertEqual(len(schedules), 1)

    def test_is_time_slot_available(self):
        # 创建当天12-24点可预约辅导
        add_schedule(
            self.project, self.user,
            datetime.combine(datetime.now().date(), time.min) + timedelta(hours=12),
            datetime.combine(datetime.now().date(), time.max),
            ScheduleTypeEnum.available)

        # 创建一个时间段列表，其中有一些是不可用的
        interval_minutes = 30  # 使用你需要的间隔时间

        start_time = datetime.now()
        end_time = start_time + timedelta(minutes=30)
        time_slots = get_schedule_day_list(self.user.id, start_time, interval_minutes)

        if start_time.hour < 12:
            # 开始时间在不可用时间段，结果应该是False
            self.assertFalse(is_time_slot_available(time_slots, start_time, end_time))
        else:
            # 在可用的时间段内，结果应该是True
            self.assertTrue(is_time_slot_available(time_slots, start_time, end_time))

        # 在全天预约（包含可预约和不可预约时间段）的时间段，结果应该是False
        start_time = datetime.now().replace(hour=0, minute=0)
        end_time = start_time + timedelta(hours=24)
        self.assertFalse(is_time_slot_available(time_slots, start_time, end_time))

    def test_get_schedule_day_list(self):
        Schedule.objects.all().delete()
        day_time = datetime.strptime('2023-05-15', "%Y-%m-%d")

        # 设置可预约日程 2023-05-15 15:00-20:00
        add_schedule(
            self.project, self.user,
            datetime.combine(day_time, time(15, 0)),
            datetime.combine(day_time, time(20, 0)),
            ScheduleTypeEnum.available)

        result = get_schedule_day_list(self.user.id, day_time)
        self.assertEqual(len(result), 48)

        # 2023-05-15 15:00-20:00可预约
        self.assertTrue(is_time_slot_available(
            result, datetime.combine(day_time, time(15, 0)),
            datetime.combine(day_time, time(20, 0))))
        # 2023-05-15 00:00-15:00不可预约
        self.assertFalse(is_time_slot_available(
            result, datetime.combine(day_time, time.min),
            datetime.combine(day_time, time(15, 0))))
        # 2023-05-15 20:00-24:00不可预约
        self.assertFalse(is_time_slot_available(
            result, datetime.combine(day_time, time(20, 0)),
            datetime.combine(day_time, time.max)))
        Schedule.objects.all().delete()

        # 设置可预约日程 2023-05-15 10:00-15:00 和 2023-05-15 18:00-20:00
        add_schedule(
            self.project, self.user,
            datetime.combine(day_time, time(10, 0)),
            datetime.combine(day_time, time(15, 0)),
            ScheduleTypeEnum.available)
        add_schedule(
            self.project, self.user,
            datetime.combine(day_time, time(18, 0)),
            datetime.combine(day_time, time(20, 0)),
            ScheduleTypeEnum.available)

        result = get_schedule_day_list(self.user.id, day_time)
        self.assertEqual(len(result), 48)

        # 2023-05-15 10:00-15:00 可预约
        self.assertTrue(is_time_slot_available(
            result, datetime.combine(day_time, time(10, 0)),
            datetime.combine(day_time, time(15, 0))))
        # 2023-05-15 18:00-20:00 可预约
        self.assertTrue(is_time_slot_available(
            result, datetime.combine(day_time, time(18, 0)),
            datetime.combine(day_time, time(20, 0))))
        # 2023-05-15 00:00-10:00 不可预约
        self.assertFalse(is_time_slot_available(
            result, datetime.combine(day_time, time.min),
            datetime.combine(day_time, time(10, 0))))
        # 2023-05-15 15:00-18:00 不可预约
        self.assertFalse(is_time_slot_available(
            result, datetime.combine(day_time, time(15, 0)),
            datetime.combine(day_time, time(18, 0))))
        # 2023-05-15 20:00-24:00 不可预约
        self.assertFalse(is_time_slot_available(
            result, datetime.combine(day_time, time(20, 0)),
            datetime.combine(day_time, time.max)))
        Schedule.objects.all().delete()

        # 设置不可预约日程 2023-05-15 15:00-20:00
        add_schedule(
            self.project, self.user,
            datetime.combine(day_time, time(15, 0)),
            datetime.combine(day_time, time(20, 0)),
            ScheduleTypeEnum.unavailable)

        result = get_schedule_day_list(self.user.id, day_time)
        self.assertEqual(len(result), 48)

        # 2023-05-15 00:00-15:00 可预约
        self.assertTrue(is_time_slot_available(
            result, datetime.combine(day_time, time.min),
            datetime.combine(day_time, time(15, 0))))
        # 2023-05-15 20:00-24:00 可预约
        self.assertTrue(is_time_slot_available(
            result, datetime.combine(day_time, time(20, 0)),
            datetime.combine(day_time, time.max)))
        # 2023-05-15 15:00-20:00 不可预约
        self.assertFalse(is_time_slot_available(
            result, datetime.combine(day_time, time(15, 0)),
            datetime.combine(day_time, time(20, 0))))

        Schedule.objects.all().delete()

        # 设置可预约日程 2023-05-15 10:00-15:00 和 2023-05-15 18:00-22:00
        add_schedule(
            self.project, self.user,
            datetime.combine(day_time, time(10, 0)),
            datetime.combine(day_time, time(15, 0)),
            ScheduleTypeEnum.available)
        add_schedule(
            self.project, self.user,
            datetime.combine(day_time, time(18, 0)),
            datetime.combine(day_time, time(22, 0)),
            ScheduleTypeEnum.available)

        # 再设置不可预约日程 2023-05-15 19:00-20:00
        add_schedule(
            self.project, self.user,
            datetime.combine(day_time, time(19, 0)),
            datetime.combine(day_time, time(20, 0)),
            ScheduleTypeEnum.unavailable)

        result = get_schedule_day_list(self.user.id, day_time)
        self.assertEqual(len(result), 48)

        # 2023-05-15 10:00-15:00 可预约
        self.assertTrue(is_time_slot_available(
            result, datetime.combine(day_time, time(10, 0)),
            datetime.combine(day_time, time(15, 0))))
        # 2023-05-15 18:00-19:00 可预约
        self.assertTrue(is_time_slot_available(
            result, datetime.combine(day_time, time(18, 0)),
            datetime.combine(day_time, time(19, 0))))
        # 2023-05-15 20:00-22:00 可预约
        self.assertTrue(is_time_slot_available(
            result, datetime.combine(day_time, time(20, 0)),
            datetime.combine(day_time, time(22, 0))))
        # 2023-05-15 00:00-10:00 不可预约
        self.assertFalse(is_time_slot_available(
            result, datetime.combine(day_time, time.min),
            datetime.combine(day_time, time(10, 0))))
        # 2023-05-15 15:00-18:00 不可预约
        self.assertFalse(is_time_slot_available(
            result, datetime.combine(day_time, time(15, 0)),
            datetime.combine(day_time, time(18, 0))))
        # 2023-05-15 19:00-20:00 不可预约
        self.assertFalse(is_time_slot_available(
            result, datetime.combine(day_time, time(19, 0)),
            datetime.combine(day_time, time(20, 0))))
        # 2023-05-15 22:00-24:00 不可预约
        self.assertFalse(is_time_slot_available(
            result, datetime.combine(day_time, time(22, 0)),
            datetime.combine(day_time, time.max)))
        Schedule.objects.all().delete()

        # 设置不可预约日程 2023-05-15 00:00-15:00 和 2023-05-15 18:00-24:00
        add_schedule(
            self.project, self.user,
            datetime.combine(day_time, time.min),
            datetime.combine(day_time, time(15, 0)),
            ScheduleTypeEnum.unavailable)
        add_schedule(
            self.project, self.user,
            datetime.combine(day_time, time(18, 0)),
            datetime.combine(day_time, time.max),
            ScheduleTypeEnum.unavailable)

        # 再设置可预约日程 2023-05-15 19:00-20:00
        add_schedule(
            self.project, self.user,
            datetime.combine(day_time, time(19, 0)),
            datetime.combine(day_time, time(20, 0)),
            ScheduleTypeEnum.available)

        result = get_schedule_day_list(self.user.id, day_time)
        self.assertEqual(len(result), 48)

        # 2023-05-15 15:00-18:00 可预约
        self.assertTrue(is_time_slot_available(
            result, datetime.combine(day_time, time(19, 0)),
            datetime.combine(day_time, time(20, 0))))
        # 2023-05-15 00:00-15:00 不可预约
        self.assertFalse(is_time_slot_available(
            result, datetime.combine(day_time, time.min),
            datetime.combine(day_time, time(19, 0))))
        # 2023-05-15 18:00-19:00 不可预约
        self.assertFalse(is_time_slot_available(
            result, datetime.combine(day_time, time(20, 0)),
            datetime.combine(day_time, time.max)))
        Schedule.objects.all().delete()

        # 设置可预约日程 2023-05-15 10:00-15:00
        add_schedule(
            self.project, self.user,
            datetime.combine(day_time, time(10, 0)),
            datetime.combine(day_time, time(15, 0)),
            ScheduleTypeEnum.available)

        # 再设置可预约日程 2023-05-15 12:00-15:00
        add_schedule(
            self.project, self.user,
            datetime.combine(day_time, time(12, 0)),
            datetime.combine(day_time, time(15, 0)),
            ScheduleTypeEnum.available)

        result = get_schedule_day_list(self.user.id, day_time)
        self.assertEqual(len(result), 48)

        # 2023-05-15 10:00-15:00 可预约
        self.assertTrue(is_time_slot_available(
            result, datetime.combine(day_time, time(10, 0)),
            datetime.combine(day_time, time(15, 0))))
        # 2023-05-15 00:00-10:00 不可预约
        self.assertFalse(is_time_slot_available(
            result, datetime.combine(day_time, time.min),
            datetime.combine(day_time, time(10, 0))))
        # 2023-05-15 15:00-24:00 不可预约
        self.assertFalse(is_time_slot_available(
            result, datetime.combine(day_time, time(15, 0)),
            datetime.combine(day_time, time.max)))
        Schedule.objects.all().delete()

        # 设置不可预约日程 2023-05-15 10:00-15:00
        add_schedule(
            self.project, self.user,
            datetime.combine(day_time, time(10, 0)),
            datetime.combine(day_time, time(15, 0)),
            ScheduleTypeEnum.unavailable)

        # 再设置不可预约日程 2023-05-15 12:00-15:00
        add_schedule(
            self.project, self.user,
            datetime.combine(day_time, time(12, 0)),
            datetime.combine(day_time, time(15, 0)),
            ScheduleTypeEnum.unavailable)

        result = get_schedule_day_list(self.user.id, day_time)
        self.assertEqual(len(result), 48)

        # 2023-05-15 00:00-10:00 可预约
        self.assertTrue(is_time_slot_available(
            result, datetime.combine(day_time, time.min),
            datetime.combine(day_time, time(10, 0))))
        # 2023-05-15 10:00-15:00 不可预约
        self.assertFalse(is_time_slot_available(
            result, datetime.combine(day_time, time(10, 0)),
            datetime.combine(day_time, time(15, 0))))
        # 2023-05-15 15:00-24:00 可预约
        self.assertTrue(is_time_slot_available(
            result, datetime.combine(day_time, time(15, 0)),
            datetime.combine(day_time, time.max)))
        Schedule.objects.all().delete()

        start_day = datetime.strptime('2023-05-15', "%Y-%m-%d")
        end_day = datetime.strptime('2023-05-20', "%Y-%m-%d")
        add_schedule(
            self.project, self.user,
            datetime.combine(start_day, time.min),
            datetime.combine(end_day, time.max),
            ScheduleTypeEnum.unavailable)

        current_day = start_day
        while current_day <= end_day:
            result = get_schedule_day_list(self.user.id, current_day)
            self.assertEqual(len(result), 48)  # 一天的时间段

            # 验证全天不可预约
            self.assertFalse(all(result))  # 全天可预约
            current_day += timedelta(days=1)
        Schedule.objects.all().delete()

        # 设置跨天不可预约日程
        start_day = datetime.strptime('2023-05-15', "%Y-%m-%d")
        end_day = datetime.strptime('2023-05-20', "%Y-%m-%d")
        add_schedule(
            self.project, self.user,
            datetime.combine(start_day, time.min),
            datetime.combine(end_day, time.max),
            ScheduleTypeEnum.unavailable)

        # 设置第二天可预约日程
        add_schedule(
            self.project, self.user,
            datetime.combine(start_day + timedelta(days=1), time.min),
            datetime.combine(start_day + timedelta(days=2), time.min),
            ScheduleTypeEnum.available)

        current_day = start_day
        while current_day <= end_day:
            result = get_schedule_day_list(self.user.id, current_day)
            self.assertEqual(len(result), 48)  # 一天的时间段

            # 第二天可预约 其余时间不可预约
            if current_day == start_day + timedelta(days=1):
                self.assertTrue(all(result))  # 全天可预约
            else:
                self.assertFalse(all(result))  # 全天不可预约
            current_day += timedelta(days=1)
        Schedule.objects.all().delete()

        # 添加一个开始结束时间一样的不可预约日程，全天可预约
        add_schedule(
            self.project, self.user,
            datetime.combine(day_time, time(10, 0)),
            datetime.combine(day_time, time(10, 0)),
            ScheduleTypeEnum.unavailable)
        result = get_schedule_day_list(self.user.id, day_time)
        self.assertEqual(len(result), 48)
        self.assertTrue(all(result))

        # 先添加全天可预约类型，后添加非全天的可预约类型，全天可预约
        add_schedule(
            self.project, self.user,
            datetime.combine(day_time, time.min),
            datetime.combine(day_time, time.max),
            ScheduleTypeEnum.available)
        add_schedule(
            self.project, self.user,
            datetime.strptime('2023-05-15 10:00', "%Y-%m-%d %H:%M"),
            datetime.strptime('2023-05-15 15:00', "%Y-%m-%d %H:%M"),
            ScheduleTypeEnum.available)
        result = get_schedule_day_list(self.user.id, day_time)
        self.assertEqual(len(result), 48)
        self.assertTrue(all(result))
        # 全天可预约
        self.assertTrue(is_time_slot_available(result, datetime.combine(day_time, time.min), datetime.combine(day_time, time.max)))
        Schedule.objects.all().delete()

        # 先添加全天可预约类型，后添加非全天的不可预约类型，除了不可预约时段，其他都可预约；
        add_schedule(
            self.project, self.user,
            datetime.combine(day_time, time.min),
            datetime.combine(day_time, time.max),
            ScheduleTypeEnum.available)
        add_schedule(
            self.project, self.user,
            datetime.strptime('2023-05-15 10:00', "%Y-%m-%d %H:%M"),
            datetime.strptime('2023-05-15 15:00', "%Y-%m-%d %H:%M"),
            ScheduleTypeEnum.unavailable)
        result = get_schedule_day_list(self.user.id, day_time)
        self.assertEqual(len(result), 48)
        # 10:00-15:00不可预约
        self.assertFalse(is_time_slot_available(
            result, datetime.strptime('2023-05-15 10:00', "%Y-%m-%d %H:%M"), datetime.strptime('2023-05-15 15:00', "%Y-%m-%d %H:%M")))
        # 00:00-10:00可预约
        self.assertTrue(is_time_slot_available(
            result, datetime.combine(day_time, time.min), datetime.strptime('2023-05-15 10:00', "%Y-%m-%d %H:%M")))
        # 15:00-24:00可预约
        self.assertTrue(is_time_slot_available(
            result, datetime.strptime('2023-05-15 15:00', "%Y-%m-%d %H:%M"), datetime.combine(day_time, time.max)))
        Schedule.objects.all().delete()

        # 先添加全天不可预约类型，后添加非全天的可预约类型，10:00-15:00可预约，其他时间不可预约
        day_time = datetime.strptime('2023-05-15', "%Y-%m-%d")
        add_schedule(
            self.project, self.user,
            datetime.combine(day_time, time.min),
            datetime.combine(day_time, time.max),
            ScheduleTypeEnum.unavailable)
        add_schedule(
            self.project, self.user,
            datetime.strptime('2023-05-15 10:00', "%Y-%m-%d %H:%M"),
            datetime.strptime('2023-05-15 15:00', "%Y-%m-%d %H:%M"),
            ScheduleTypeEnum.available)
        result = get_schedule_day_list(self.user.id, day_time)
        self.assertEqual(len(result), 48)

        # 10:00-15:00可预约
        self.assertTrue(is_time_slot_available(
            result, datetime.strptime('2023-05-15 10:00', "%Y-%m-%d %H:%M"),
            datetime.strptime('2023-05-15 15:00', "%Y-%m-%d %H:%M")))
        # 00:00-10:00不可预约
        self.assertFalse(is_time_slot_available(
            result, datetime.combine(day_time, time.min),
            datetime.strptime('2023-05-15 10:00', "%Y-%m-%d %H:%M")))
        # 15:00-24:00不可预约
        self.assertFalse(is_time_slot_available(
            result, datetime.strptime('2023-05-15 15:00', "%Y-%m-%d %H:%M"),
            datetime.combine(day_time, time.max)))
        Schedule.objects.all().delete()

        # 先添加全天不可预约类型，后添加非全天的不可预约类型，全天不可预约
        add_schedule(
            self.project, self.user,
            datetime.combine(day_time, time.min),
            datetime.combine(day_time, time.max),
            ScheduleTypeEnum.unavailable)
        add_schedule(
            self.project, self.user,
            datetime.strptime('2023-05-15 10:00', "%Y-%m-%d %H:%M"),
            datetime.strptime('2023-05-15 15:00', "%Y-%m-%d %H:%M"),
            ScheduleTypeEnum.unavailable)
        result = get_schedule_day_list(self.user.id, day_time)
        self.assertEqual(len(result), 48)
        # 全天不可预约
        self.assertFalse(is_time_slot_available(
            result, datetime.combine(day_time, time.min),
            datetime.combine(day_time, time.max)))
        Schedule.objects.all().delete()

        # 先添加非全天的可预约类型，后添加全天的可预约类型，全天可预约
        add_schedule(
            self.project, self.user,
            datetime.strptime('2023-05-15 10:00', "%Y-%m-%d %H:%M"),
            datetime.strptime('2023-05-15 15:00', "%Y-%m-%d %H:%M"),
            ScheduleTypeEnum.available)
        add_schedule(
            self.project, self.user,
            datetime.combine(day_time, time.min),
            datetime.combine(day_time, time.max),
            ScheduleTypeEnum.available)
        result = get_schedule_day_list(self.user.id, day_time)
        self.assertEqual(len(result), 48)
        # 全天可预约
        self.assertTrue(is_time_slot_available(
            result, datetime.combine(day_time, time.min),
            datetime.combine(day_time, time.max)))
        Schedule.objects.all().delete()

        # 先添加非全天的可预约类型，后添加全天的不可预约类型，全天不可预约
        add_schedule(
            self.project, self.user,
            datetime.strptime('2023-05-15 10:00', "%Y-%m-%d %H:%M"),
            datetime.strptime('2023-05-15 15:00', "%Y-%m-%d %H:%M"),
            ScheduleTypeEnum.available)
        add_schedule(
            self.project, self.user,
            datetime.combine(day_time, time.min),
            datetime.combine(day_time, time.max),
            ScheduleTypeEnum.unavailable)
        result = get_schedule_day_list(self.user.id, day_time)
        self.assertEqual(len(result), 48)
        # 全天不可预约
        self.assertFalse(is_time_slot_available(
            result, datetime.combine(day_time, time.min),
            datetime.combine(day_time,time.max)))
        Schedule.objects.all().delete()

        # 先添加不可预约日程，再添加全天可预约日程，应该全天都可预约
        add_schedule(
            self.project, self.user,
            datetime.strptime('2023-05-15 10:00', "%Y-%m-%d %H:%M"),
            datetime.strptime('2023-05-15 15:00', "%Y-%m-%d %H:%M"),
            ScheduleTypeEnum.unavailable)
        add_schedule(
            self.project, self.user,
            datetime.combine(day_time, time.min),
            datetime.combine(day_time, time.max),
            ScheduleTypeEnum.available)
        result = get_schedule_day_list(self.user.id, day_time)
        self.assertTrue(all(result))  # 全天可预约
        Schedule.objects.all().delete()

        # 先添加不可预约日程，再添加全天不可预约日程，应该全天都不可预约
        add_schedule(
            self.project, self.user,
            datetime.strptime('2023-05-15 10:00', "%Y-%m-%d %H:%M"),
            datetime.strptime('2023-05-15 15:00', "%Y-%m-%d %H:%M"),
            ScheduleTypeEnum.unavailable)
        add_schedule(
            self.project, self.user,
            datetime.combine(day_time, time.min),
            datetime.combine(day_time, time.max),
            ScheduleTypeEnum.unavailable)
        result = get_schedule_day_list(self.user.id, day_time)
        self.assertFalse(any(result))  # 全天不可预约
        Schedule.objects.all().delete()

        # 先添加全天可预约日程，再添加全天不可预约日程，应该全天都不可预约
        add_schedule(
            self.project, self.user,
            datetime.combine(day_time, time.min),
            datetime.combine(day_time, time.max),
            ScheduleTypeEnum.available)
        add_schedule(
            self.project, self.user,
            datetime.combine(day_time, time.min),
            datetime.combine(day_time, time.max),
            ScheduleTypeEnum.unavailable)
        result = get_schedule_day_list(self.user.id, day_time)
        self.assertFalse(any(result))  # 全天不可预约
        Schedule.objects.all().delete()

        # 先添加全天可预约日程，再添加全天可预约日程，应该全天都可预约
        add_schedule(
            self.project, self.user,
            datetime.combine(day_time, time.min),
            datetime.combine(day_time, time.max),
            ScheduleTypeEnum.available)
        add_schedule(
            self.project, self.user,
            datetime.combine(day_time, time.min),
            datetime.combine(day_time, time.max),
            ScheduleTypeEnum.available)
        result = get_schedule_day_list(self.user.id, day_time)
        self.assertTrue(all(result))  # 全天可预约
        Schedule.objects.all().delete()

        # 先添加全天不可预约日程，再添加全天可预约日程，应该全天都可预约
        add_schedule(
            self.project, self.user,
            datetime.combine(day_time, time.min),
            datetime.combine(day_time, time.max),
            ScheduleTypeEnum.unavailable)
        add_schedule(
            self.project, self.user,
            datetime.combine(day_time, time.min),
            datetime.combine(day_time, time.max),
            ScheduleTypeEnum.available)
        result = get_schedule_day_list(self.user.id, day_time)
        self.assertTrue(all(result))  # 全天可预约
        Schedule.objects.all().delete()

        # 先添加添加全天辅导，再添加全天可预约日程，应该全天都不可预约
        add_schedule(
            self.project, self.user,
            datetime.combine(day_time, time.min),
            datetime.combine(day_time, time.max),
            ScheduleTypeEnum.interview)
        add_schedule(
            self.project, self.user,
            datetime.combine(day_time, time.min),
            datetime.combine(day_time, time.max),
            ScheduleTypeEnum.available)
        result = get_schedule_day_list(self.user.id, day_time)
        self.assertFalse(all(result))  # 全天不可预约
        Schedule.objects.all().delete()

        # 教练已有B端1对1辅导2023-05-15 11:00-12:00，再设置可预约日程2023-05-15 10:00-13:00
        add_schedule(
            self.project, self.user,
            datetime.combine(day_time, time(11, 0)),
            datetime.combine(day_time, time(12, 0)),
            ScheduleTypeEnum.interview)
        add_schedule(
            self.project, self.user,
            datetime.combine(day_time, time(10, 0)),
            datetime.combine(day_time, time(13, 0)),
            ScheduleTypeEnum.available)

        result = get_schedule_day_list(self.user.id, day_time)
        self.assertEqual(len(result), 48)

        # 2023-05-15 10:00-11:00、12:00-13:00可预约，2023-05-15 00:00-10:00、11:00-12:00、13:00-24:00不可预约
        self.assertTrue(is_time_slot_available(
            result, datetime.combine(day_time, time(10)),
            datetime.combine(day_time, time(11))))
        self.assertTrue(is_time_slot_available(
            result, datetime.combine(day_time, time(12)),
            datetime.combine(day_time, time(13))))
        self.assertFalse(is_time_slot_available(
            result, datetime.combine(day_time, time.min),
            datetime.combine(day_time, time(10))))
        self.assertFalse(is_time_slot_available(
            result, datetime.combine(day_time, time(11)),
            datetime.combine(day_time, time(12))))
        self.assertFalse(is_time_slot_available(
            result, datetime.combine(day_time, time(13)),
            datetime.combine(day_time, time.max)))
        Schedule.objects.all().delete()

        # 教练已有B端1对1辅导2023-05-15 11:00-12:00，再设置不可预约日程2023-05-15 10:00-13:00
        interview_schedule = add_schedule(
            self.project, self.user,
            datetime.combine(day_time, time(11, 0)),
            datetime.combine(day_time, time(12, 0)),
            ScheduleTypeEnum.interview)
        add_schedule(
            self.project, self.user,
            datetime.combine(day_time, time(10, 0)),
            datetime.combine(day_time, time(13, 0)),
            ScheduleTypeEnum.unavailable)

        # 取消1对1辅导
        interview_schedule.deleted = True
        interview_schedule.save()

        result = get_schedule_day_list(self.user.id, day_time)
        self.assertEqual(len(result), 48)

        # 2023-05-15 00:00-10:00、13:00-24:00可预约，2023-05-15 10:00-13:00不可预约
        self.assertFalse(is_time_slot_available(result, datetime.combine(day_time, time(10)),
                                                datetime.combine(day_time, time(13))))
        self.assertTrue(is_time_slot_available(result, datetime.combine(day_time, time.min),
                                               datetime.combine(day_time, time(10))))
        self.assertTrue(is_time_slot_available(result, datetime.combine(day_time, time(13)),
                                               datetime.combine(day_time, time.max)))
        Schedule.objects.all().delete()

        # 教练已有B端1对1辅导2023-05-15 11:00-12:00，再设置可预约日程2023-05-15 10:00-13:00
        interview_schedule = add_schedule(
            self.project, self.user,
            datetime.combine(day_time, time(11, 0)),
            datetime.combine(day_time, time(12, 0)),
            ScheduleTypeEnum.interview)
        add_schedule(
            self.project, self.user,
            datetime.combine(day_time, time(10, 0)),
            datetime.combine(day_time, time(13, 0)),
            ScheduleTypeEnum.available)

        # 取消1对1辅导
        interview_schedule.deleted = True
        interview_schedule.save()

        result = get_schedule_day_list(self.user.id, day_time)
        self.assertEqual(len(result), 48)

        # 2023-05-15 10:00-13:00可预约，2023-05-15 00:00-10:00、13:00-24:00不可预约
        self.assertTrue(is_time_slot_available(
            result, datetime.combine(day_time, time(10)),
            datetime.combine(day_time, time(13))))
        self.assertFalse(is_time_slot_available(
            result, datetime.combine(day_time, time.min),
            datetime.combine(day_time, time(10))))
        self.assertFalse(is_time_slot_available(
            result, datetime.combine(day_time, time(13)),
            datetime.combine(day_time, time.max)))
        Schedule.objects.all().delete()

        # 边界测试：添加一个全天可预约类型的日程
        add_schedule(
            self.project, self.user,
            datetime.combine(day_time, time.min),
            datetime.combine(day_time, time.max),
            ScheduleTypeEnum.available)
        result = get_schedule_day_list(self.user.id, day_time)
        self.assertEqual(len(result), 48)
        # 全天可预约
        self.assertTrue(all(result))  # 全天可预约
        Schedule.objects.all().delete()

        # 边界测试：添加一个全天不可预约类型的日程
        add_schedule(
            self.project, self.user,
            datetime.combine(day_time, time.min),
            datetime.combine(day_time, time.max),
            ScheduleTypeEnum.unavailable)
        result = get_schedule_day_list(self.user.id, day_time)
        self.assertEqual(len(result), 48)
        # 全天不可预约
        self.assertFalse(any(result))  # 全天不可预约
        Schedule.objects.all().delete()

        # 边界测试：添加一个全天辅导类型的日程
        add_schedule(
            self.project, self.user,
            datetime.combine(day_time, time.min),
            datetime.combine(day_time, time.max),
            ScheduleTypeEnum.interview)
        result = get_schedule_day_list(self.user.id, day_time)
        self.assertEqual(len(result), 48)
        # 全天不可预约
        self.assertFalse(all(result))  # 全天不可预约
        Schedule.objects.all().delete()

        # 尝试在一个非常短的时间段内添加一个日程
        add_schedule(
            self.project, self.user,
            datetime.combine(day_time, time(13, 0)),
            datetime.combine(day_time, time(13, 1)),
            ScheduleTypeEnum.available)
        result = get_schedule_day_list(self.user.id, day_time)
        self.assertTrue(is_time_slot_available(
            result, datetime.combine(day_time, time(13, 0)),
            datetime.combine(day_time, time(13, 1))))
        Schedule.objects.all().delete()

        # 添加在闰年2月29日的日程
        day_time = datetime(2024, 2, 29)  # Next leap year
        add_schedule(
            self.project, self.user,
            datetime.combine(day_time, time.min),
            datetime.combine(day_time, time.max),
            ScheduleTypeEnum.available)
        result = get_schedule_day_list(self.user.id, day_time)
        self.assertTrue(all(result))
        Schedule.objects.all().delete()

        # 添加在跨年日期的日程
        day_time = datetime(2023, 12, 31)
        add_schedule(
            self.project, self.user,
            datetime.combine(day_time, time.min),
            datetime.combine(day_time + timedelta(days=1), time.min),
            ScheduleTypeEnum.available)
        result_dec31 = get_schedule_day_list(self.user.id, day_time)
        result_jan1 = get_schedule_day_list(self.user.id, day_time + timedelta(days=1))
        self.assertTrue(all(result_dec31))
        self.assertTrue(all(result_jan1))
        Schedule.objects.all().delete()

        day_time = datetime.strptime('2023-05-15', "%Y-%m-%d")
        # 添加一个重复不可用日程
        schedule = add_schedule(
            self.project, self.user,
            datetime.combine(day_time, time.min),
            datetime.combine(day_time, time.max),
            ScheduleTypeEnum.unavailable)
        add_recurring_schedule(
            schedule, RecurringScheduleRepeatTypeEnum.daily, day_time + timedelta(days=10),  [(day_time + timedelta(days=7)).strftime('%Y-%m-%d')])
        # 重复不可用日程是否不可用
        result = get_schedule_day_list(self.user.id, day_time + timedelta(days=5))
        self.assertFalse(all(result))

        # 标记删除的日程是否生成
        result = get_schedule_day_list(self.user.id, day_time + timedelta(days=7))
        self.assertTrue(all(result))

        # 到达截止时间，重复日程是否停止
        result = get_schedule_day_list(self.user.id, day_time + timedelta(days=11))
        self.assertTrue(all(result))
        Schedule.objects.all().delete()

        # 添加一个重复不可预约日程，查询当天返回
        schedule = add_schedule(
            self.project, self.user,
            datetime.combine(day_time, time.min),
            datetime.combine(day_time, time.max),
            ScheduleTypeEnum.unavailable)
        add_recurring_schedule(
            schedule, RecurringScheduleRepeatTypeEnum.daily, None, None)
        result = get_schedule_day_list(self.user.id, day_time)
        self.assertFalse(all(result))
        Schedule.objects.all().delete()

    def test_get_next_repeat_date(self):
        day_time = datetime.strptime('2023-05-15', "%Y-%m-%d")
        # 每天重复，当前时间计算下一天
        schedule = add_schedule(
            self.project, self.user,
            datetime.combine(day_time, time(10, 0)),
            datetime.combine(day_time, time(15, 0)),
            ScheduleTypeEnum.unavailable)
        recurring_schedule = add_recurring_schedule(
            schedule, RecurringScheduleRepeatTypeEnum.daily, None, None)
        new_date = get_next_repeat_date(
            day_time,
            schedule.public_attr.start_time,
            recurring_schedule.repeat_type, 1, 1)
        self.assertEqual(new_date, day_time + timedelta(days=1))
        Schedule.objects.all().delete()

        # 每天重复，非当前时间计算下一天
        schedule = add_schedule(
            self.project, self.user,
            datetime.combine(day_time, time(10, 0)),
            datetime.combine(day_time, time(15, 0)),
            ScheduleTypeEnum.unavailable)
        recurring_schedule = add_recurring_schedule(
            schedule, RecurringScheduleRepeatTypeEnum.daily, None, None)
        new_date = get_next_repeat_date(
            day_time + timedelta(days=1),
            schedule.public_attr.start_time,
            recurring_schedule.repeat_type, 1, 1)
        self.assertEqual(new_date, day_time + timedelta(days=2))
        Schedule.objects.all().delete()

        # 每天重复，前时间计算上一天
        schedule = add_schedule(
            self.project, self.user,
            datetime.combine(day_time, time(10, 0)),
            datetime.combine(day_time, time(15, 0)),
            ScheduleTypeEnum.unavailable)
        recurring_schedule = add_recurring_schedule(
            schedule, RecurringScheduleRepeatTypeEnum.daily, None, None)
        new_date = get_next_repeat_date(
            day_time,
            schedule.public_attr.start_time,
            recurring_schedule.repeat_type, 1, -1)
        self.assertEqual(new_date, day_time - timedelta(days=1))
        Schedule.objects.all().delete()

        # 每天重复，非当前时间计算上一天
        schedule = add_schedule(
            self.project, self.user,
            datetime.combine(day_time, time(10, 0)),
            datetime.combine(day_time, time(15, 0)),
            ScheduleTypeEnum.unavailable)
        recurring_schedule = add_recurring_schedule(
            schedule, RecurringScheduleRepeatTypeEnum.daily, None, None)
        new_date = get_next_repeat_date(
            day_time + timedelta(days=1),
            schedule.public_attr.start_time,
            recurring_schedule.repeat_type, 1, -1)
        self.assertEqual(new_date, day_time)
        Schedule.objects.all().delete()

        # 每周重复，当前时间计算下一天
        schedule = add_schedule(
            self.project, self.user,
            datetime.combine(day_time, time(10, 0)),
            datetime.combine(day_time, time(15, 0)),
            ScheduleTypeEnum.unavailable)
        recurring_schedule = add_recurring_schedule(
            schedule, RecurringScheduleRepeatTypeEnum.weekly, None, None)
        new_date = get_next_repeat_date(
            day_time,
            schedule.public_attr.start_time,
            recurring_schedule.repeat_type, 1, 1)
        self.assertEqual(schedule.public_attr.start_time.weekday(), new_date.weekday())
        Schedule.objects.all().delete()

        # 每周重复，非当前时间计算下一天
        schedule = add_schedule(
            self.project, self.user,
            datetime.combine(day_time, time(10, 0)),
            datetime.combine(day_time, time(15, 0)),
            ScheduleTypeEnum.unavailable)
        recurring_schedule = add_recurring_schedule(
            schedule, RecurringScheduleRepeatTypeEnum.weekly, None, None)
        new_date = get_next_repeat_date(
            day_time + timedelta(days=1),
            schedule.public_attr.start_time,
            recurring_schedule.repeat_type, 1, 1)
        self.assertEqual(schedule.public_attr.start_time.weekday(), new_date.weekday())
        Schedule.objects.all().delete()

        # 每周重复，前时间计算上一天
        schedule = add_schedule(
            self.project, self.user,
            datetime.combine(day_time, time(10, 0)),
            datetime.combine(day_time, time(15, 0)),
            ScheduleTypeEnum.unavailable)
        recurring_schedule = add_recurring_schedule(
            schedule, RecurringScheduleRepeatTypeEnum.weekly, None, None)
        new_date = get_next_repeat_date(
            day_time,
            schedule.public_attr.start_time,
            recurring_schedule.repeat_type, 1, -1)
        self.assertEqual(schedule.public_attr.start_time.weekday(), new_date.weekday())
        Schedule.objects.all().delete()

        # 每周重复，非当前时间计算上一天
        schedule = add_schedule(
            self.project, self.user,
            datetime.combine(day_time, time(10, 0)),
            datetime.combine(day_time, time(15, 0)),
            ScheduleTypeEnum.unavailable)
        recurring_schedule = add_recurring_schedule(
            schedule, RecurringScheduleRepeatTypeEnum.weekly, None, None)
        new_date = get_next_repeat_date(
            day_time + timedelta(days=1),
            schedule.public_attr.start_time,
            recurring_schedule.repeat_type, 1, -7)
        self.assertEqual(schedule.public_attr.start_time.weekday(), new_date.weekday())
        Schedule.objects.all().delete()

        # 每月重复，当前时间计算下一天
        schedule = add_schedule(
            self.project, self.user,
            datetime.combine(day_time, time(10, 0)),
            datetime.combine(day_time, time(15, 0)),
            ScheduleTypeEnum.unavailable)
        recurring_schedule = add_recurring_schedule(
            schedule, RecurringScheduleRepeatTypeEnum.monthly, None, None)
        new_date = get_next_repeat_date(
            day_time,
            schedule.public_attr.start_time,
            recurring_schedule.repeat_type, 1, 1)

        self.assertEqual(schedule.public_attr.start_time.day, new_date.day)
        Schedule.objects.all().delete()

        # 每月重复，非当前时间计算下一天
        schedule = add_schedule(
            self.project, self.user,
            datetime.combine(day_time, time(10, 0)),
            datetime.combine(day_time, time(15, 0)),
            ScheduleTypeEnum.unavailable)
        recurring_schedule = add_recurring_schedule(
            schedule, RecurringScheduleRepeatTypeEnum.monthly, None, None)
        new_date = get_next_repeat_date(
            pendulum.instance(day_time).add(months=1).naive(),
            schedule.public_attr.start_time,
            recurring_schedule.repeat_type, 1, 1)
        self.assertEqual(schedule.public_attr.start_time.day, new_date.day)
        Schedule.objects.all().delete()

        # 每月重复，前时间计算上一天
        schedule = add_schedule(
            self.project, self.user,
            datetime.combine(day_time, time(10, 0)),
            datetime.combine(day_time, time(15, 0)),
            ScheduleTypeEnum.unavailable)
        recurring_schedule = add_recurring_schedule(
            schedule, RecurringScheduleRepeatTypeEnum.monthly, None, None)
        new_date = get_next_repeat_date(
            day_time,
            schedule.public_attr.start_time,
            recurring_schedule.repeat_type, 1, -1)
        self.assertEqual(schedule.public_attr.start_time.day, new_date.day)
        Schedule.objects.all().delete()

        # 每月重复，非当前时间计算上一天
        schedule = add_schedule(
            self.project, self.user,
            datetime.combine(day_time, time(10, 0)),
            datetime.combine(day_time, time(15, 0)),
            ScheduleTypeEnum.unavailable)
        recurring_schedule = add_recurring_schedule(
            schedule, RecurringScheduleRepeatTypeEnum.monthly, None, None)
        new_date = get_next_repeat_date(
            pendulum.instance(day_time).add(months=1).naive(),
            schedule.public_attr.start_time,
            recurring_schedule.repeat_type, 1, -1)
        self.assertEqual(schedule.public_attr.start_time.day, new_date.day)
        Schedule.objects.all().delete()

    def test_generate_repeated_instances(self):
        day_time = datetime.strptime('2023-05-15', "%Y-%m-%d")
        max_instances = 15

        # 每天重复，向后推敲，次数计算，时间判断
        schedule = add_schedule(
            self.project, self.user,
            datetime.combine(day_time, time(10, 0)),
            datetime.combine(day_time, time(15, 0)),
            ScheduleTypeEnum.unavailable)
        recurring_schedule = add_recurring_schedule(
            schedule, RecurringScheduleRepeatTypeEnum.daily, None, None)
        instances = generate_repeated_instances(recurring_schedule, day_time + timedelta(
            days=10), status=1, max_instances=max_instances)
        self.assertEqual(len(instances), max_instances)
        self.assertTrue(instances[0].public_attr.start_time, day_time + timedelta(days=10))
        Schedule.objects.all().delete()

        # 每天重复，向后推敲，限制时间，次数计算
        schedule = add_schedule(
            self.project, self.user,
            datetime.combine(day_time, time(10, 0)),
            datetime.combine(day_time, time(15, 0)),
            ScheduleTypeEnum.unavailable)
        recurring_schedule = add_recurring_schedule(
            schedule, RecurringScheduleRepeatTypeEnum.daily, (day_time + timedelta(days=12)).date(), None)
        instances = generate_repeated_instances(recurring_schedule, day_time + timedelta(
            days=10), status=1, max_instances=max_instances)
        self.assertEqual(len(instances), 3)  # 需要包含结束时间
        self.assertTrue(instances[0].public_attr.start_time, day_time + timedelta(days=10))
        Schedule.objects.all().delete()

        # 每天重复，向前推敲，次数计算，时间判断
        schedule = add_schedule(
            self.project, self.user,
            datetime.combine(day_time, time(10, 0)),
            datetime.combine(day_time, time(15, 0)),
            ScheduleTypeEnum.unavailable)
        recurring_schedule = add_recurring_schedule(
            schedule, RecurringScheduleRepeatTypeEnum.daily, None, None)
        instances = generate_repeated_instances(recurring_schedule, day_time + timedelta(
            days=20), status=2, max_instances=max_instances)
        self.assertEqual(len(instances), max_instances)
        self.assertTrue(instances[0].public_attr.start_time, day_time + timedelta(days=10))
        Schedule.objects.all().delete()

        # 每天重复，向前推敲，限制时间，次数计算
        schedule = add_schedule(
            self.project, self.user,
            datetime.combine(day_time, time(10, 0)),
            datetime.combine(day_time, time(15, 0)),
            ScheduleTypeEnum.unavailable)
        recurring_schedule = add_recurring_schedule(
            schedule, RecurringScheduleRepeatTypeEnum.daily, None, None)
        instances = generate_repeated_instances(recurring_schedule, day_time + timedelta(
            days=10), status=2, max_instances=max_instances)
        self.assertEqual(len(instances), 11)
        self.assertTrue(instances[0].public_attr.start_time, day_time - timedelta(days=1))
        Schedule.objects.all().delete()

        # 每周重复，向后推敲，次数计算，时间判断
        schedule = add_schedule(
            self.project, self.user,
            datetime.combine(day_time, time(10, 0)),
            datetime.combine(day_time, time(15, 0)),
            ScheduleTypeEnum.unavailable)
        recurring_schedule = add_recurring_schedule(
            schedule, RecurringScheduleRepeatTypeEnum.weekly, None, None)
        instances = generate_repeated_instances(recurring_schedule, day_time + timedelta(
            weeks=10), status=1, max_instances=max_instances)
        self.assertEqual(len(instances), max_instances)
        self.assertTrue(instances[0].public_attr.start_time, day_time + timedelta(weeks=10))
        Schedule.objects.all().delete()

        # 每周重复，向后推敲，限制时间，次数计算
        schedule = add_schedule(
            self.project, self.user,
            datetime.combine(day_time, time(10, 0)),
            datetime.combine(day_time, time(15, 0)),
            ScheduleTypeEnum.unavailable)
        recurring_schedule = add_recurring_schedule(
            schedule, RecurringScheduleRepeatTypeEnum.weekly, (day_time + timedelta(weeks=12)).date(), None)
        instances = generate_repeated_instances(recurring_schedule, day_time + timedelta(
            weeks=10), status=1, max_instances=max_instances)
        self.assertEqual(len(instances), 3)  # 需要包含结束时间
        self.assertTrue(instances[0].public_attr.start_time, day_time + timedelta(weeks=10))
        Schedule.objects.all().delete()

        # 每周重复，向前推敲，次数计算，时间判断
        schedule = add_schedule(
            self.project, self.user,
            datetime.combine(day_time, time(10, 0)),
            datetime.combine(day_time, time(15, 0)),
            ScheduleTypeEnum.unavailable)
        recurring_schedule = add_recurring_schedule(
            schedule, RecurringScheduleRepeatTypeEnum.weekly, None, None)
        instances = generate_repeated_instances(recurring_schedule, day_time + timedelta(
            weeks=20), status=2, max_instances=max_instances)
        self.assertEqual(len(instances), max_instances)
        self.assertTrue(instances[0].public_attr.start_time, day_time + timedelta(weeks=10))
        Schedule.objects.all().delete()

        # 每周重复，向前推敲，限制时间，次数计算
        schedule = add_schedule(
            self.project, self.user,
            datetime.combine(day_time, time(10, 0)),
            datetime.combine(day_time, time(15, 0)),
            ScheduleTypeEnum.unavailable)
        recurring_schedule = add_recurring_schedule(
            schedule, RecurringScheduleRepeatTypeEnum.weekly, None, None)
        instances = generate_repeated_instances(recurring_schedule, day_time + timedelta(
            weeks=10), status=2, max_instances=max_instances)
        self.assertEqual(len(instances), 11)
        self.assertTrue(instances[0].public_attr.start_time, day_time - timedelta(weeks=1))
        Schedule.objects.all().delete()

        # 每月重复，向后推敲，次数计算，时间判断
        schedule = add_schedule(
            self.project, self.user,
            datetime.combine(day_time, time(10, 0)),
            datetime.combine(day_time, time(15, 0)),
            ScheduleTypeEnum.unavailable)
        recurring_schedule = add_recurring_schedule(
            schedule, RecurringScheduleRepeatTypeEnum.monthly, None, [])
        instances = generate_repeated_instances(
            recurring_schedule, pendulum.instance(day_time).add(months=10).naive(), status=1, max_instances=max_instances)
        self.assertEqual(len(instances), max_instances)
        self.assertTrue(instances[0].public_attr.start_time, day_time.day)
        Schedule.objects.all().delete()

        # 每月重复，向后推敲，限制时间，次数计算
        schedule = add_schedule(
            self.project, self.user,
            datetime.combine(day_time, time(10, 0)),
            datetime.combine(day_time, time(15, 0)),
            ScheduleTypeEnum.unavailable)
        recurring_schedule = add_recurring_schedule(
            schedule, RecurringScheduleRepeatTypeEnum.monthly, pendulum.instance(day_time).add(months=12).naive().date(), None)
        instances = generate_repeated_instances(
            recurring_schedule, pendulum.instance(day_time).add(months=10).naive(), status=1, max_instances=max_instances)
        self.assertEqual(len(instances), 3)  # 需要包含结束时间
        self.assertTrue(instances[0].public_attr.start_time, day_time.day)
        Schedule.objects.all().delete()

        # 每月重复，向前推敲，次数计算，时间判断
        schedule = add_schedule(
            self.project, self.user,
            datetime.combine(day_time, time(10, 0)),
            datetime.combine(day_time, time(15, 0)),
            ScheduleTypeEnum.unavailable)
        recurring_schedule = add_recurring_schedule(
            schedule, RecurringScheduleRepeatTypeEnum.monthly, None, None)
        instances = generate_repeated_instances(
            recurring_schedule, pendulum.instance(day_time).add(months=20).naive(), status=2, max_instances=max_instances)
        self.assertEqual(len(instances), max_instances)
        self.assertTrue(instances[0].public_attr.start_time, day_time.day)
        Schedule.objects.all().delete()

        # 每月重复，向前推敲，限制时间，次数计算
        schedule = add_schedule(
            self.project, self.user,
            datetime.combine(day_time, time(10, 0)),
            datetime.combine(day_time, time(15, 0)),
            ScheduleTypeEnum.unavailable)
        recurring_schedule = add_recurring_schedule(
            schedule, RecurringScheduleRepeatTypeEnum.monthly, None, None)
        instances = generate_repeated_instances(
            recurring_schedule, pendulum.instance(day_time).add(months=10).naive(), status=2, max_instances=max_instances)
        self.assertEqual(len(instances), 11)
        self.assertTrue(instances[0].public_attr.start_time.day, day_time.day)
        Schedule.objects.all().delete()

def test_date_in_recurring_schedule(self):
        day_time = datetime.strptime('2023-05-15', "%Y-%m-%d")
        # 每天重复，时间在不在重复里面
        schedule = add_schedule(
            self.project, self.user,
            datetime.combine(day_time, time(10, 0)),
            datetime.combine(day_time, time(15, 0)),
            ScheduleTypeEnum.unavailable)
        recurring_schedule = add_recurring_schedule(
            schedule, RecurringScheduleRepeatTypeEnum.daily,
            (day_time + timedelta(days=10)).date(),
            [(day_time + timedelta(days=5)).date().strftime("%Y-%m-%d")])
        status = date_in_recurring_schedule(
            recurring_schedule,
            (day_time + timedelta(days=1)).date())
        self.assertTrue(status)
        status = date_in_recurring_schedule(
            recurring_schedule,
            (day_time - timedelta(days=1)).date())
        self.assertFalse(status)
        # 未被排除
        status = date_in_recurring_schedule(
            recurring_schedule,
            (day_time + timedelta(days=6)).date())
        self.assertTrue(status)
        # 被排除
        status = date_in_recurring_schedule(
            recurring_schedule,
            (day_time + timedelta(days=5)).date())
        self.assertFalse(status)
        # 超过结束时间
        status = date_in_recurring_schedule(
            recurring_schedule,
            (day_time + timedelta(days=11)).date())
        self.assertFalse(status)
        Schedule.objects.all().delete()

        # 每周重复，时间在不在重复里面
        schedule = add_schedule(
            self.project, self.user,
            datetime.combine(day_time, time(10, 0)),
            datetime.combine(day_time, time(15, 0)),
            ScheduleTypeEnum.unavailable)
        recurring_schedule = add_recurring_schedule(
            schedule, RecurringScheduleRepeatTypeEnum.weekly,
            (pendulum.instance(day_time).add(months=2).naive()).date(),
            [(day_time + timedelta(days=7 * 2)).date().strftime('%Y-%m-%d')])
        # 正好是循环中
        status = date_in_recurring_schedule(
            recurring_schedule,
            (day_time + timedelta(days=7)).date())
        self.assertTrue(status)
        # 不在循环中
        status = date_in_recurring_schedule(
            recurring_schedule,
            (day_time + timedelta(days=3)).date())
        self.assertFalse(status)
        # 未被排除
        status = date_in_recurring_schedule(
            recurring_schedule,
            (day_time + timedelta(days=7 * 3)).date())
        self.assertTrue(status)
        # 被排除
        status = date_in_recurring_schedule(
            recurring_schedule,
            (day_time + timedelta(days=7 * 2)).date())
        self.assertFalse(status)
        # 超过开始时间
        status = date_in_recurring_schedule(
            recurring_schedule,
            (day_time - timedelta(days=7)).date())
        self.assertFalse(status)
        # 超过结束时间
        status = date_in_recurring_schedule(
            recurring_schedule,
            (day_time + timedelta(days=7 * 10)).date())
        self.assertFalse(status)
        Schedule.objects.all().delete()

        # 每月重复，时间在不在重复里面
        schedule = add_schedule(
            self.project, self.user,
            datetime.combine(day_time, time(10, 0)),
            datetime.combine(day_time, time(15, 0)),
            ScheduleTypeEnum.unavailable)
        recurring_schedule = add_recurring_schedule(
            schedule, RecurringScheduleRepeatTypeEnum.monthly,
            (pendulum.instance(day_time).add(months=10).naive()).date(),
            [(pendulum.instance(day_time).add(months=5).naive()).date().strftime('%Y-%m-%d')])
        # 正好是循环中
        status = date_in_recurring_schedule(
            recurring_schedule,
            pendulum.instance(day_time).add(months=1).naive().date())
        self.assertTrue(status)
        # 不在循环中
        status = date_in_recurring_schedule(
            recurring_schedule,
            pendulum.instance(day_time).add(months=2, days=1).naive().date())
        self.assertFalse(status)
        # 未被排除
        status = date_in_recurring_schedule(
            recurring_schedule,
            pendulum.instance(day_time).add(months=4).naive().date())
        self.assertTrue(status)
        # 被排除
        status = date_in_recurring_schedule(
            recurring_schedule,
            pendulum.instance(day_time).add(months=5).naive().date())
        self.assertFalse(status)
        # 超过开始时间
        status = date_in_recurring_schedule(
            recurring_schedule,
            (day_time - timedelta(days=1)).date())
        self.assertFalse(status)
        # 超过结束时间
        status = date_in_recurring_schedule(
            recurring_schedule,
            pendulum.instance(day_time).add(months=11).naive().date())
        self.assertFalse(status)
        Schedule.objects.all().delete()
