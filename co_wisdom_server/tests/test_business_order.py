import json
import math
import random

import pendulum

from decimal import Decimal
from django.test import TestCase
from rest_framework.test import APIClient

from utils.authentication import get_token
from wisdom_v2.common import business_order_public, coach_public
from wisdom_v2.enum.business_order_enum import WorkTypeEnum, BusinessOrderDurationTypeEnum, BusinessOrderDataTypeEnum, \
    BusinessOrderPayStatusEnum, BusinessOrderTypeEnum, BusinessOrderWithdrawalStatusEnum, \
    BusinessOrderSettlementStatusEnum
from wisdom_v2.enum.pay_enum import OrderStatusEnum, SettlementChannelEnum
from wisdom_v2.enum.service_content_enum import CoachOfferStatusEnum
from wisdom_v2.models import ProjectInterview, Project, User, Coach, Order, PublicAttr, GroupCoach, ProjectBundle, \
    ProjectGroupCoach, InterviewRecordTemplate, ProjectOfferPrice, ProjectOffer, CoachOffer, Resume
from wisdom_v2.models_file import PublicCoursesCoach, BusinessOrder, BusinessOrder2Object, PublicCourses
from wisdom_v2.models_file.project import ProjectSettlement
from wisdom_v2.enum.project_interview_enum import ProjectInterviewTypeEnum, ProjectInterviewPlaceCategoryEnum, \
    GroupCoachTypeEnum
from wisdom_v2.views import constant
from wisdom_v2.views import business_order_actions

base_time = pendulum.now()



class BackendGenerateBusinessOrderTest(TestCase):
    def setUp(self):
        self.client = APIClient()
        # 创建测试所需的基本实例
        self.project = Project.objects.create(name="Test Project")
        self.coach_user = User.objects.create(
            name="coach", password="7q5HYd2BQe2H9VWcDoDfvg==")
        self.user = User.objects.create(
            name="coachee", password="7q5HYd2BQe2H9VWcDoDfvg==")
        self.coach = Coach.objects.create(user=self.coach_user, deleted=False, is_settlement_info=True)
        self.resume = Resume.objects.create(coach=self.coach, is_customization=False, deleted=False)

        self.project_offer = ProjectOffer.objects.create(project=self.project, deleted=False, price=10000)
        self.coach_offer = CoachOffer.objects.create(
            status=CoachOfferStatusEnum.joined.value, coach=self.coach, project_offer=self.project_offer, deleted=False)
        self.auth_header = f"Token {self.get_user_token()}DjangoTestToken"


    def get_user_token(self):
        token = get_token(self.coach_user, is_backend=True)
        return token

    def add_public_attr(self, start_time, end_time, type, project_id=None):
        attr = PublicAttr.objects.create(
            project_id=project_id,
            user=self.coach_user,
            target_user=self.user,
            type=type,
            start_time=start_time.to_datetime_string(),
            end_time=end_time.to_datetime_string()
        )
        return attr

    def add_order(self):
        attr = self.add_public_attr(base_time.add(1), base_time.add(2), constant.ORDER_TAX_POINT)
        count = random.randint(2, 10)
        order = Order.objects.create(
            payer_amount=round(10000 * count),
            total_amount=round(10000 * count + 2000),
            discount_amount=2000,
            count=count,
            status=OrderStatusEnum.paid.value,
            channel=SettlementChannelEnum.wechat.value,
            success_time=base_time.subtract(days=1).to_datetime_string(),
            public_attr=attr
        )
        return order

    def add_interview(
            self, start_time, end_time,
            order=None, interview_type=ProjectInterviewTypeEnum.formal_interview.value,
            place_category=ProjectInterviewPlaceCategoryEnum.online_one_to_one.value, ):

        if order:
            project_id = None
        else:
            project_id = self.project.id
        attr = self.add_public_attr(start_time, end_time, constant.ATTR_TYPE_INTERVIEW, project_id=project_id)
        interview = ProjectInterview.objects.create(
            type=interview_type,
            place_category=place_category,
            coach_record_status=True,
            coachee_record_status=True,
            public_attr=attr, order=order
        )
        return interview

    def add_group_coach_interview(
            self, start_time, end_time,
            group_coach_type=GroupCoachTypeEnum.collective_tutoring.value):
        project_bundle = ProjectBundle.objects.create(project=self.project)
        project_group_coach = ProjectGroupCoach.objects.create(
            project=self.project, type=group_coach_type,
            start_time=start_time.to_datetime_string(), end_time=end_time.to_datetime_string())

        interview = self.add_interview(
            start_time, end_time,
            place_category=ProjectInterviewPlaceCategoryEnum.offline_group_coach.value
        )

        interview_record_template = InterviewRecordTemplate.objects.create()
        GroupCoach.objects.create(
            project_bundle=project_bundle,
            project_group_coach=project_group_coach,
            interview=interview,
            inter_view_record_template=interview_record_template,
        )
        return interview

    def add_public_courses_coach(self, work_type, start_time, end_time):
        public_courses = PublicCourses.objects.create()
        PublicCoursesCoach.objects.create(
            public_courses=public_courses,
            type=work_type,
            coach=self.coach,
            start_time=start_time.to_datetime_string(),
            end_time=end_time.to_datetime_string(),
        )

    def add_project_settlement(self, work_type, start_time, end_time):
        ProjectSettlement.objects.create(
            work_type=work_type,
            coach=self.coach,
            project_id=self.project.id,
            work_start_time=start_time.to_datetime_string(),
            work_end_time=end_time.to_datetime_string(),
        )

    def add_base_data(self, start_time, end_time):
        # 创建已完成的项目辅导
        self.add_interview(start_time, end_time)

        # 创建已完成的个人辅导
        self.add_interview(start_time, end_time, order=self.add_order())

        # 创建已完成的利益相关者辅导
        self.add_interview(
            start_time, end_time,
            interview_type=ProjectInterviewTypeEnum.stakeholder_interview)

        # 创建工作坊辅导
        self.add_group_coach_interview(
            start_time, end_time,
            group_coach_type=GroupCoachTypeEnum.collective_tutoring.value)

        # 创建小组辅导
        self.add_group_coach_interview(
            start_time, end_time,
            group_coach_type=GroupCoachTypeEnum.group_tutoring.value)

        public_courses_coach_type = [
            WorkTypeEnum.teaching.value,  # 公开课 授课
            WorkTypeEnum.evaluation.value,  # 公开课 口试
            WorkTypeEnum.one_to_one.value,  # 公开课 一对一辅导
            WorkTypeEnum.group_counseling.value]  # 公开课 小组辅导

        for item in public_courses_coach_type:
            self.add_public_courses_coach(item, start_time, end_time)

        project_settlement_type = WorkTypeEnum.get_describe_keys()
        for item in project_settlement_type:
            self.add_project_settlement(item, start_time, end_time)
            ProjectOfferPrice.objects.create(
                project_offer=self.project_offer, price=10000, work_type= item,
                time_type=BusinessOrderDurationTypeEnum.day.value)

    def test_backend_generate_business_order(self):
        """
        基础数据测试
        """

        # 调用backend_generate_business_order函数创建基础数据
        # 目前有17个不同类型的数据源 查看订单数量是否一致
        # 时间是已完成的
        self.add_base_data(base_time.subtract(days=2), base_time.subtract(days=1))
        business_order_public.backend_generate_business_order()
        all_business_order = BusinessOrder.objects.all()
        self.assertEqual(all_business_order.count(), 17)

        # 创建一组没到开始时间的辅导，看订单是否创建
        self.add_base_data(base_time.add(days=1), base_time.add(days=2))
        business_order_public.backend_generate_business_order()
        all_business_order = BusinessOrder.objects.all()
        self.assertEqual(all_business_order.count(), 17)

        for item in all_business_order:
            business_order2object = item.business_order2object.filter(deleted=False).first()

            # 检查用户数据的准确性
            if item.user_id:
                self.assertEqual(self.user.id, item.user_id)
            else:
                self.assertTrue(business_order2object.data_type != BusinessOrderDataTypeEnum.interview.value)

            # 个人订单默认已支付
            if item.type == BusinessOrderTypeEnum.personal:
                self.assertEqual(item.pay_status, BusinessOrderPayStatusEnum.paid.value)

                interview = ProjectInterview.objects.filter(id=business_order2object.object_id, deleted=False).first()
                order = interview.order

                member_paid = Decimal(str(order.payer_amount)) / Decimal(str(order.count))
                self.assertEqual(item.member_paid, member_paid)

                platform_scale,  coach_actual_income, tax_amount, platform_service_amount = coach_public.calculate_coach_income_details(interview)
                self.assertEqual(coach_actual_income, math.ceil(member_paid * Decimal('0.75')))
                self.assertEqual(item.coach_actual_income, coach_actual_income)

                platform_service_amount = int(member_paid - coach_actual_income)
                self.assertEqual(item.platform_service_amount, platform_service_amount)

            # 创建一个同id的辅导，看是否正确触发错误通知。
            status = business_order_public.is_order_duplicate(business_order2object.object_id, item.work_type)
            self.assertFalse(status)

            # 税点一致的情况下，修改后税金和总收入不会出现金额变动
            coach_price = item.coach_price if item.coach_price else 10000
            item.coach_price = coach_price
            item.save()
            duration = item.duration
            tax_point = item.tax_point if item.tax_point else 0
            # 调用方法计算
            real_time_tax_amount, real_time_coach_actual_income = business_order_public.calculation_tax_amount(
                coach_price, duration, tax_point, item)

            # 验证基础计算逻辑
            self.assertEqual(item.coach_price * duration, real_time_tax_amount + real_time_coach_actual_income)
            self.assertEqual(Decimal(str(item.coach_price * duration * tax_point)) / Decimal('100'), real_time_tax_amount)
            self.assertEqual(item.coach_price * duration * (1 - tax_point / 100), real_time_coach_actual_income)

    def test_admin_user_behavior_testing(self):
        """
        测试管理后台用户行为
        """
        # 创建基础数据源
        self.add_base_data(base_time.subtract(days=2), base_time.subtract(days=1))
        business_order_public.backend_generate_business_order()
        all_business_order = BusinessOrder.objects.all()
        self.assertEqual(all_business_order.count(), 17)

        not_pay_all_business_order = all_business_order.exclude(type=BusinessOrderTypeEnum.personal.value)
        # 标记支付状态
        data = business_order_actions.business_order_update_pay_status(not_pay_all_business_order, BusinessOrderPayStatusEnum.paid)
        self.assertTrue(data.get('all_success'))

        for item in all_business_order:
            self.assertEqual(item.pay_status, BusinessOrderPayStatusEnum.paid)
            self.assertTrue(item.pay_time)

        # 获取生成的订单id列表
        ids = list(all_business_order.values_list('id', flat=True))
        ids = [str(item) for item in ids]

        # 请求接口，状态变更为可提现
        can_withdraw_url = '/coapi/api/v2/settlements/draw_money/'
        order_data = json.dumps({'ids': ids})
        response = self.client.post(
            can_withdraw_url, order_data, content_type='application/json',  HTTP_AUTHORIZATION=self.auth_header)
        self.assertEqual(response.json().get('retCode'), 200)
        # 所有订单变为可提现订单状态
        can_withdraw_business_order = BusinessOrder.objects.filter(
            id__in=ids, withdrawal_status=BusinessOrderWithdrawalStatusEnum.can_withdraw.value)
        # 对比可提现订单数量
        self.assertEqual(can_withdraw_business_order.count(), len(ids))
        # 检测参数是否更新
        for can_withdraw_item in can_withdraw_business_order:
            self.assertTrue(can_withdraw_item.apply_withdrawal_processor, self.coach_user)
            self.assertIsNotNone(can_withdraw_item.apply_withdrawal_time)

        # 请求接口 状态变为已提现
        withdrawn = '/coapi/api/v2/settlements/'
        response = self.client.post(
            withdrawn, order_data, content_type='application/json', HTTP_AUTHORIZATION=self.auth_header)
        self.assertEqual(response.json().get('retCode'), 200)
        # 所有订单变为已提现订单状态，并且有结算单id
        withdrawn = BusinessOrder.objects.filter(
            id__in=ids, withdrawal_status=BusinessOrderWithdrawalStatusEnum.withdrawn.value)
        # 对比订单数量
        self.assertEqual(withdrawn.count(), len(ids))
        # 检测结算单信息是否更新
        for withdrawn_item in withdrawn:
            self.assertIsNotNone(withdrawn_item.business_settlement_id)

        # 请求接口 状态变为结算
        settled = '/coapi/api/v2/settlements/settled/'
        response = self.client.post(
            settled, order_data, content_type='application/json', HTTP_AUTHORIZATION=self.auth_header)
        self.assertEqual(response.json().get('retCode'), 200)
        # 所有订单变为已提现订单状态，并且有结算单id
        settled = BusinessOrder.objects.filter(
            id__in=ids, settlement_status=BusinessOrderSettlementStatusEnum.settled.value)
        # 对比订单数量
        self.assertEqual(settled.count(), len(ids))
        # 检测结算单信息是否更新
        for settled_item in settled:
            self.assertIsNotNone(settled_item.settlement_time)
            # 如果有辅导类型数据，需要看interview是否成功标记成已结算
            if settled_item.type in [BusinessOrderTypeEnum.enterprise, BusinessOrderTypeEnum.personal]:
                business_order2object = settled_item.business_order2object.filter(deleted=False).first()
                # 项目结算来源的订单无interview数据，无需修改
                if business_order2object and business_order2object.data_type == BusinessOrderDataTypeEnum.interview.value:
                    interview = ProjectInterview.objects.filter(id=business_order2object.object_id).first()
                    if interview:
                        self.assertTrue(interview.is_settlement)

    def test_app_user_behavior_testing(self):
        """
        测试app用户行为
        """
        # 创建基础数据源
        self.add_base_data(base_time.subtract(days=2), base_time.subtract(days=1))
        business_order_public.backend_generate_business_order()
        all_business_order = BusinessOrder.objects.all()
        self.assertEqual(all_business_order.count(), 17)

        not_pay_all_business_order = all_business_order.exclude(type=BusinessOrderTypeEnum.personal.value)
        # 标记支付状态
        data = business_order_actions.business_order_update_pay_status(not_pay_all_business_order, BusinessOrderPayStatusEnum.paid)
        self.assertTrue(data.get('all_success'))

        for item in all_business_order:
            self.assertEqual(item.pay_status, BusinessOrderPayStatusEnum.paid)
            self.assertTrue(item.pay_time)

        # 获取生成的订单id列表
        ids = list(all_business_order.values_list('id', flat=True))
        ids = [str(item) for item in ids]
        order_data = json.dumps({'ids': ids})
        app_order_data = json.dumps({
            'order_ids': ids,
            'coach_user_id': self.coach_user.id
        })

        # 请求接口，状态变更为可提现
        can_withdraw_url = '/coapi/api/v2/settlements/draw_money/'
        response = self.client.post(
            can_withdraw_url, order_data, content_type='application/json',  HTTP_AUTHORIZATION=self.auth_header)
        self.assertEqual(response.json().get('retCode'), 200)
        # 所有订单变为可提现订单状态
        can_withdraw_business_order = BusinessOrder.objects.filter(
            id__in=ids, withdrawal_status=BusinessOrderWithdrawalStatusEnum.can_withdraw.value)
        # 对比可提现订单数量
        self.assertEqual(can_withdraw_business_order.count(), len(ids))
        # 检测参数是否更新
        for can_withdraw_item in can_withdraw_business_order:
            self.assertTrue(can_withdraw_item.apply_withdrawal_processor, self.coach_user)
            self.assertIsNotNone(can_withdraw_item.apply_withdrawal_time)

        # 请求接口 状态变为已提现
        withdrawn = '/coapi/api/v2/app/business_order/create_settlements/'
        response = self.client.post(
            withdrawn, app_order_data, content_type='application/json', HTTP_AUTHORIZATION=self.auth_header)
        self.assertEqual(response.json().get('retCode'), 200)
        # 所有订单变为已提现订单状态，并且有结算单id
        withdrawn = BusinessOrder.objects.filter(
            id__in=ids, withdrawal_status=BusinessOrderWithdrawalStatusEnum.withdrawn.value)
        # 对比订单数量
        self.assertEqual(withdrawn.count(), len(ids))
        # 检测结算单信息是否更新
        for withdrawn_item in withdrawn:
            self.assertIsNotNone(withdrawn_item.business_settlement_id)

        # 请求接口 状态变为结算
        settled = '/coapi/api/v2/settlements/settled/'
        response = self.client.post(
            settled, order_data, content_type='application/json', HTTP_AUTHORIZATION=self.auth_header)
        self.assertEqual(response.json().get('retCode'), 200)
        # 所有订单变为已提现订单状态，并且有结算单id
        settled = BusinessOrder.objects.filter(
            id__in=ids, settlement_status=BusinessOrderSettlementStatusEnum.settled.value)
        # 对比订单数量
        self.assertEqual(settled.count(), len(ids))
        # 检测结算单信息是否更新
        for settled_item in settled:
            self.assertIsNotNone(settled_item.settlement_time)
            # 如果有辅导类型数据，需要看interview是否成功标记成已结算
            if settled_item.type in [BusinessOrderTypeEnum.enterprise, BusinessOrderTypeEnum.personal]:
                business_order2object = settled_item.business_order2object.filter(deleted=False).first()
                # 项目结算来源的订单无interview数据，无需修改
                if business_order2object and business_order2object.data_type == BusinessOrderDataTypeEnum.interview.value:
                    interview = ProjectInterview.objects.filter(id=business_order2object.object_id).first()
                    if interview:
                        self.assertTrue(interview.is_settlement)

    def test_business_order_extra_cost(self):
        self.add_base_data(base_time.subtract(days=2), base_time.subtract(days=1))
        business_order_public.backend_generate_business_order()
        all_business_order = BusinessOrder.objects.all()
        self.assertEqual(all_business_order.count(), 17)


    def tearDown(self):
        # 清理测试产生的数据
        BusinessOrder2Object.objects.all().delete()
        BusinessOrder.objects.all().delete()
        ProjectInterview.objects.all().delete()
        PublicCoursesCoach.objects.all().delete()
        ProjectSettlement.objects.all().delete()
        Coach.objects.all().delete()
        User.objects.all().delete()
        Project.objects.all().delete()
        Resume.objects.all().delete()
        ProjectOffer.objects.all().delete()
        CoachOffer.objects.all().delete()
