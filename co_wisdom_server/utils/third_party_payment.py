import time
from random import sample
from string import ascii_letters, digits
from ast import literal_eval

from django.conf import settings
from utils.wechatpay import wxpay
from utils.feishu_robot import push_wx_error_message
from utils.aliyun_sls_log import AliyunSlsLogLayout


class ThirdPartyPayment:
    """
    第三方支付
    channel 1 微信支付
    """
    def __init__(self, channel=1, user_id=None, user_name=None):
        self.channel = channel
        self.user_id = user_id
        self.user_name = user_name

    def pay(self, pay_data):
        if self.channel == 1:  # 微信支付
            is_success, result = self.wechat_pay(pay_data)
            return is_success, result
        return False, '支付通道错误'

    def refunds(self, refund_data):
        if self.channel == 1:  # 微信支付
            is_success, result = self.wechat_refunds(refund_data)
            return is_success, result
        return False, '退款通道错误'

    def close_pay(self, order_no):
        if self.channel == 1:  # 微信支付
            is_success, result = self.wechat_close_pay(order_no)
            return is_success, result
        return False, '关闭订单错误'

    def wechat_close_pay(self, order_no):
        code, message = wxpay.close(out_trade_no=order_no.hex, mchid=settings.MCHID)
        AliyunSlsLogLayout().send_third_api_log(user_id=self.user_id, user_name=self.user_name, message='微信支付关闭订单', content={
            'code': code,
            'message': message
        })
        if code not in [204, '204']:
            push_wx_error_message(name='微信支付关闭订单失败', level='error', content=message)
            return False, '关闭订单失败'
        return True, message

    def wechat_pay(self, pay_data):
        code, message = wxpay.pay(**pay_data)
        message = literal_eval(message) if type(message) != dict else message
        AliyunSlsLogLayout().send_third_api_log(user_id=self.user_id, user_name=self.user_name, message='微信支付预支付下单', content={
            'code': code,
            'message': message
        })
        if code not in ['200', 200]:
            push_wx_error_message(name='微信支付预支付下单失败', level='error', content=message)
            return False, message.get('message')

        prepay_id = message['prepay_id']
        timeStamp = int(time.time())
        nonceStr = ''.join(sample(ascii_letters + digits, 8))
        data = [settings.APP_ID, str(timeStamp), nonceStr, 'prepay_id=%s' % prepay_id]
        paySign = wxpay.sign(data)
        result = {
            "appId": settings.APP_ID,
            "timeStamp": timeStamp,
            "nonceStr": nonceStr,
            "package": {"prepay_id": prepay_id},
            "signType": "RSA",
            "paySign": paySign,
        }
        return True, result

    def wechat_refunds(self, refund_data):
        code, message = wxpay.refund(**refund_data)
        message = literal_eval(message) if type(message) != dict else message
        AliyunSlsLogLayout().send_third_api_log(user_id=self.user_id, user_name=self.user_name, message='微信支付申请退款', content={
            'code': code,
            'message': message
        })
        if code not in [200, '200']:
            push_wx_error_message(name='微信支付申请退款失败', level='error', content=message)
            return False, message.get('message')
        return True, message

    def call_back(self, headers, body, call_back_type):
        try:
            if self.channel == 1:  # 微信支付回调
                is_success, result = self.wechat_call_back(headers, body, call_back_type)
                return is_success, result
            return False, '回调通道错误'
        except Exception as e:
            return False, str(e)

    def wechat_call_back(self, headers, body, call_back_type):
        result = wxpay.callback(headers, body)
        text = '微信支付-支付回调' if call_back_type == 1 else '微信支付-退款回调'
        AliyunSlsLogLayout().send_third_api_log(user_id=self.user_id, user_name=self.user_name, message=text, content={
            'message': result
        })
        if call_back_type == 1:  # 支付回调
            if result and result.get('event_type') == 'TRANSACTION.SUCCESS':
                return True, result
        elif call_back_type == 2:  # 退款回调
            if result and result.get('event_type') == 'REFUND.SUCCESS':
                return True, result
        return False, '微信支付回调解密错误'

    def wechat_send_coupon(self, stock_id, openid, out_request_no, stock_creator_mchid):
        code, message = wxpay.marketing_favor_stock_send(stock_id=stock_id, openid=openid,
                                                         out_request_no=out_request_no,
                                                         stock_creator_mchid=stock_creator_mchid)
        message = literal_eval(message) if type(message) != dict else message
        AliyunSlsLogLayout().send_third_api_log(user_id=self.user_id, user_name=self.user_name, message='微信支付发放优惠券', content={
            'code': code,
            'message': message
        })
        if code not in [200, '200']:
            push_wx_error_message(name='微信支付发放优惠券失败', level='error', content=message)
            return False, message.get('message')
        return True, message

    def send_coupon(self, stock_id, openid, out_request_no, stock_creator_mchid):
        if self.channel == 1:  # 微信支付
            is_success, result = self.wechat_send_coupon(stock_id, openid, out_request_no, stock_creator_mchid)
            return is_success, result
        return False, '发券通道错误'

    def coupon_detail(self, coupon_id, openid):
        if self.channel == 1:  # 微信支付
            is_success, result = self.wechat_coupon_detail(coupon_id, openid)
            return is_success, result
        return False, '查券通道错误'

    def wechat_coupon_detail(self, coupon_id, openid):
        code, message = wxpay.marketing_favor_coupon_detail(coupon_id=coupon_id, openid=openid)
        message = literal_eval(message) if type(message) != dict else message
        AliyunSlsLogLayout().send_third_api_log(user_id=self.user_id, user_name=self.user_name, message='微信支付查询优惠券', content={
            'code': code,
            'message': message
        })
        if code not in [200, '200']:
            push_wx_error_message(name='微信支付查询优惠券失败', level='error', content=message)
            return False, message.get('message')
        return True, message
