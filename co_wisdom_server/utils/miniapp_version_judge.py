from itertools import zip_longest

from utils.message.lark_message import LarkMessageCenter

def compare_version(version1, version2):
    """
    请求的微信小程序版本判断,不支持字母判断
    :param version1: 请求中携带的版本号 str '1.2.3'
    :param version2: 需要对比的版本号
    :return: int 0：等于 1：大于 -1：小于
    """

    if not version1 or not version2:
        return 1

    try:
        version1_parts = list(map(int, version1.split('.')))
        version2_parts = list(map(int, version2.split('.')))

        for part1, part2 in zip_longest(version1_parts, version2_parts, fillvalue=0):
            if part1 > part2:
                return 1
            elif part1 < part2:
                return -1
        return 0
    except Exception as e:
        LarkMessageCenter().send_other_backend_message(
            'compare_version',
            'error',
            {'error': str(e), 'version1': version1, 'version2': version2}
        )
        return 1
