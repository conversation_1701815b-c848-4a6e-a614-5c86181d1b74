import json
import logging
import time
import datetime
import os
import uuid
from wisdom_v2.common import user_additional_info_public, company_interview_public

import jwt
import pendulum
import redis
import requests
import math
import xml.etree.cElementTree as ET

from decimal import Decimal
from django.conf import settings
from django.db import transaction
from django.db.models import Q, Sum, Avg, <PERSON><PERSON><PERSON>, F, Count, Max
from django.core.cache import cache
from celery import shared_task
from django.utils import timezone
from celery.exceptions import SoftTimeLimitExceeded
from controller.projectexam.constant import EX_SELF, EX_OUTHER
from utils.aliyun_sls_log import AliyunSlsLogLayout
from utils.message.email import message_send_email_base

from utils.messagecenter.center import push_message
from utils.feishu_robot import push_celery_hanging_message, push_wx_error_message, push_send_work_wx_message_count, \
    send_lark_message
from data import models
from utils.messagecenter.actions import get_exam_other_link, get_exam_r45_link, get_exam_r24_link, get_exam_self_link, \
    get_interview_end_to_student_link, get_exam_r112_not_end_to_student_link, \
    get_send_email_of_evaluation_end_link, get_param_h24_to_student_link, get_param_1h_to_coach_link, \
    get_param_1h_to_student_link, get_param_15m_to_interested_default_link, get_param_15m_to_interested_link, \
    get_param_15m_to_student_link, get_param_15m_to_coach_link, get_interview_end_to_coach_type_1_link
from utils.messagecenter.actions import get_user_is_student, get_interview_end_to_coach_not_link
from utils.messagecenter.sms import SendSMS
from utils.qr_code import get_base_qr_code, get_qr_code
from utils.queryset import multiple_field_distinct
from utils import lbi_evaluation_action, interview_detail_xlsx, aesdecrypt, interview_to_lark_data, randomPassword, \
    aesencrypt
from utils import trainee_coaches_data
from utils import work_wechat
from wisdom_v2.common import coach_public, resume_public, activity_public, business_order_public, \
    change_observation_public, interview_public, coach_interview_public, project_public, chemical_interview_public, \
    customer_portrait_public, coach_task_public
from wisdom_v2.enum.chemical_interview_enum import ChemicalInterviewCoachSourceEnum, ChemicalInterviewStatusEnum
from wisdom_v2.enum.message_type_enum import LarkMessageTypeEnum

from wisdom_v2.enum.project_enum import ProjectEvaluationReportTypeEnum
from wisdom_v2.enum.pay_enum import OrderStatusEnum
from wisdom_v2.enum.service_content_enum import UserBehaviorDataTypeEnum, CoachTypeEnum, \
    InterviewRecordTemplateRoleEnum, MultipleAssociationRelationTypeEnum, PersonalReportTypeEnum, NewCoachTaskTypeEnum, \
    PdfReportTypeEnum, NoticeTemplateTypeEnum, NoticeChannelTypeEnum, CoachOfferStatusEnum, ProjectOfferStatusEnum, \
    ProjectCoachStatusEnum, ActivityCoachStatusEnum, ChangeObservationInviteTypeEnum
from wisdom_v2.enum.user_enum import CoachUserTypeEnum, PersonalApplyTypeEnum, PersonalApplyStatusEnum, UserRoleEnum
from wisdom_v2.models import EvaluationModule, UserBehaviorData, Project, WorkWechatUser, OneToOneCoach, CoachTask, \
    ProjectBundle, GrowthGoals, ArticleModule, EvaluationReport, ProjectEvaluationReport, ProjectCoach, \
    EvaluationReportScore, EvaluationAnswer, ChangeObservationAnswer, ChangeObservation, \
    PersonalReport, MultipleAssociationRelation, ProjectInterested, GrowthGoalsChange, UserNoticeRecord, Stock, Order, \
    UserBackend, UserContract, Resume, CoachOffer, Coach, PersonalApply
from utils.send_account_email import get_project_manage_wx_user_id
from wisdom_v2.enum.project_interview_enum import ProjectInterviewPlaceCategoryEnum, InterviewRecordTypeEnum, \
    ProjectInterviewTypeEnum, GroupCoachTypeEnum, InterviewMeetingChannelTypeEnum
from wisdom_v2 import utils
from wisdom_v2.models_file import ChemicalInterview2Coach, StakeholderInterview, StakeholderInterviewModule, \
    ChemicalInterviewModule, ActivityCoach, InterviewMeeting
from wisdom_v2.views import constant
from wisdom_v2.views.constant import ATTR_STATUS_INTERVIEW_CANCEL, ATTR_TYPE_INTERVIEW, INTERVIEW_TYPE_COACHING, \
    MANAGE_EVALUATION, LBI_EVALUATION, ATTR_STATUS_INTERVIEW_CONFIRM, ATTR_TYPE_EVALUATION_ANSWER, \
    CANCEL_INTERVIEW_MEETING, UPDATE_INTERVIEW_MEETING
from wisdom_v2.app_views.app_evaluation_report_action import get_histogram_data, get_pie_chart, QUESTION_TEXT
from wisdom_v2.views.coach_actions import CoachSerializers
from wisdom_v2.app_views.app_order_action import remove_order_data
from wisdom_v2.common import stakeholder_interview_public


from utils.wechatpay import wxpay
from utils.third_party_payment import ThirdPartyPayment
from utils.lark_document import LarkDocument
from utils.message.lark_message import LarkMessageCenter

work_wechat_redis = redis.Redis.from_url(settings.WORK_WECHAT_REDIS)
data_redis = redis.Redis.from_url(settings.DATA_REDIS)
api_action_logging = logging.getLogger('api_action')
third_party_redis = redis.Redis.from_url(settings.THIRD_PARTY_DATA_REDIS)


# Role = 7 = 利益相关者
# Role = 6 = 被教练者
# Role = 5 = 企业管理员
# Role = 4 = 教练
# Role = 3 = 项目负责人


TEMPLATE_PARAM = {
    "收件人姓名": "truename",
    "性别": "gender",
    "网址": "url",
    "用户名": "username",
    "密码": "password",
    "项目管理员姓名": "manager_name",
    "项目管理员邮箱": "manager_emial",
    "项目管理员手机": "manage_phone",
    "教练姓名": "coachname",
    "企业管理员姓名": "companymanager",
    "约谈时间": "interviewtime",
    "测评截止时间": "ex_end_time",
    "距离约谈开始剩余时间": "usable_time",
    "测评\调研名称": "exname",
    "报告名称": "title",
    "第几次约谈": "num",
    "被教练者姓名": "studentname",
    "公司名称": "company",
    "对方姓名": "interviewer"
}

TYPE_MAP = {
    0: '常规约谈',
    1: '目标约谈',
    2: '三方约谈',
    3: '阶段回顾',
    4: '总结约谈',
    5: '一对多约谈',
    6: '一影子观察',
}


def get_time_slot(end_time):
    now_time = timezone.now()
    res_time = end_time - now_time
    days = res_time.days
    hours = int(res_time.seconds / 60 / 60)
    return days, hours


def get_str_name(user_names):
    str_name = ''
    for name in set(user_names):
        str_name += '%s，' % name[0]
    return str_name


@shared_task(queue='task', ignore_result=True, soft_time_limit=20)
def write_content():
    try:
        file_path = '/root/today.py'
        with open(file_path, 'w') as f:
            t = time.strftime("%Y-%m-%d", time.localtime())
            f.write(t)
    except SoftTimeLimitExceeded:
        push_celery_hanging_message('write_content')
    except Exception as e:
        LarkMessageCenter().send_other_backend_message(
            'celery任务错误', 'error', {'error': f'write_content:{str(e)}'})


@shared_task(queue='task', ignore_result=True, soft_time_limit=20)
def save_content(a, b, path):
    try:
        with open(path, 'w') as f:
            f.write(str(a + b))
    except SoftTimeLimitExceeded:
        push_celery_hanging_message('save_content')
    except Exception as e:
        LarkMessageCenter().send_other_backend_message(
            'celery任务错误', 'error', {'error': f'save_content:{str(e)}'})


@shared_task(queue='task', ignore_result=True, soft_time_limit=30)
def send_email_of_remind():
    # ===================逾期后上午9点提醒一次=======================
    try:
        project_exam_all = models.AppProbjectexamrelationuser.objects.filter(Enable=1, Status=0,
                                                                             EndDate__lt=timezone.now())
        default_exam_remind_template = 'default_exam_remind'
        default_exam_remind_manage_template = 'default_exam_remind_manage'
        now_time = timezone.now()

        # 定时提醒LBI自评与他评
        project_evaluation = project_exam_all.filter(ExId__in=[EX_SELF, EX_OUTHER])
        exam_other_dic = {}
        try:
            for evaluation in project_evaluation:
                end_time = evaluation.EndDate
                now_time = timezone.now()
                res_time = now_time - end_time
                days = res_time.days
                # 1: 自评，2: 他评
                eval_type = models.AppEvaluation.objects.get(EvaluationId=evaluation.ExId).EvalType
                if eval_type == 1:
                    user = models.SysUser.objects.get(pk=evaluation.UserId)

                    params = {
                        'exam_name': evaluation.ExName,
                        'days': days + 1,
                        'truename': user.UserTrueName,
                        'path_link': get_exam_self_link(evaluation.ProbjectId),
                        'flag': 0
                    }

                    push_message.delay(user, default_exam_remind_template, params, evaluation.ProbjectId)

                else:

                    if not exam_other_dic.get(evaluation.UserId):
                        exam_other_dic[evaluation.UserId] = {
                            'exam_name': evaluation.ExName,
                            'days': days + 1,
                            'path_link': '-',
                            'probjectid': evaluation.ProbjectId,
                            'interested_id': evaluation.interested_id,
                            'flag': 0

                        }
                    else:
                        exam_other_dic[evaluation.UserId]['flag'] = 1
                        exam_other_dic[evaluation.UserId]['path_link'] = '-'

            if exam_other_dic:
                for k, v in exam_other_dic.items():
                    interest = models.SysUser.objects.get(User_Id=k)
                    v['path_link'] = get_exam_other_link(k, v['probjectid'], v['interested_id'], v['flag'])
                    push_message.delay(interest, default_exam_remind_template, v, v['probjectid'])
        except Exception as e:
            print('send other error', project_evaluation, e)

        # 定时提醒被教练者填写未完成的教练准备度
        exams = project_exam_all.filter(PageCode='r112')
        for exam in exams.filter(Status=0):
            try:
                exam_time = now_time - exam.EndDate
                dic = {
                    "exam_name": exam.ExName,
                    'days': exam_time.days + 1,
                    'path_link': get_exam_r112_not_end_to_student_link(exam.ProbjectId),

                }
                student = models.SysUser.objects.get(pk=exam.UserId)
                push_message.delay(student, default_exam_remind_template, dic, exam.ProbjectId)
            except Exception as e:
                print('send r112 error', exam, e)
        exams_r112 = models.AppProbjectexamrelationuser.objects.filter(Enable=1, PageCode='r112',
                                                                       EndDate__lt=timezone.now())

        # 定时提醒企业管理员督促被教练者填写未完成的教练准备度
        if 0 in exams_r112.values_list('Status', flat=True):
            # 根据项目去重(每个项目只触发一次)
            for e in multiple_field_distinct(exams_r112.filter(Status=0), ['ProbjectId']):
                try:
                    project = models.AppProbject.objects.get(pk=e.ProbjectId)
                    exam_time = now_time - e.EndDate
                    company = models.AppCompany.objects.get(Company_Id=project.Company_Id).CompanyName

                    # 企业管理员可能有多个 IsManger project_id
                    user_ids = models.AppProbjectrelation.objects.filter(ProbjectId=project.pk,
                                                                         IsManger=True).values_list('UserId', flat=True)
                    user_names = models.SysUser.objects.filter(
                        pk__in=exams_r112.filter(Status=0,
                                                 ProbjectId=project.pk).values_list('UserId', flat=True)).values_list(
                        'UserTrueName')
                    company_user = models.SysUser.objects.filter(pk__in=user_ids)
                    str_names = get_str_name(user_names)
                    dic = {
                        "exam_name": e.ExName,
                        'days': exam_time.days + 1,
                        'company': company,
                        'user_name': str_names

                    }
                    if company_user.exists:
                        for user in company_user:
                            push_message.delay(user, default_exam_remind_manage_template, dic, project.pk)

                    # 群智管理员就是项目管理员 AppProject: Relation_UserId
                    project_manage_user = models.SysUser.objects.get(pk=project.Relation_UserId)
                    push_message.delay(project_manage_user, default_exam_remind_manage_template, dic, project.pk)
                except Exception as ex:
                    print('send r112 to manage error', e, ex)

        # r24被教练者和利益相关者看到的是不同的页面 （11）
        project_exams = project_exam_all.filter(PageCode='r24')
        # 一个利益相关者是多个被教练者的利益相关者
        template_dic = {}
        for exam in project_exams:
            try:
                exam_time = now_time - exam.EndDate
                if not template_dic.get(exam.UserId):
                    template_dic[exam.UserId] = {
                        "exam_name": exam.ExName,
                        'days': exam_time.days + 1,

                    }
                else:
                    template_dic[exam.UserId]['flag'] = 1
            except Exception as e:
                print('send r24 error', exam, e)

        for k, v in template_dic.items():
            v['path_link'] = get_exam_r24_link(k, v.get('projectid'), v.get('interested_id'), v.get('flag'))
            interest = models.SysUser.objects.get(pk=k)
            push_message.delay(interest, default_exam_remind_template, v, v.get('projectid'))

        # r45 定时-未完成定时提醒 (20)
        project_exams_r45 = project_exam_all.filter(PageCode='r45')
        # 一个利益相关者是多个被教练者的利益相关者
        template_dic_r45 = {}
        for exam in project_exams_r45:
            try:
                exam_time = now_time - exam.EndDate

                if not template_dic_r45.get(exam.UserId):
                    template_dic_r45[exam.UserId] = {
                        "exam_name": exam.ExName,
                        'days': exam_time.days + 1,
                        'flag': 0,
                        'probjectid': exam.ProbjectId,
                        'interested_id': exam.interested_id
                    }
                else:
                    template_dic_r45[exam.UserId]['flag'] = 1
            except Exception as e:
                print('send r45 error', exam, e)

        for k, v in template_dic_r45.items():
            try:
                interest = models.SysUser.objects.get(pk=k)
                v['path_link'] = get_exam_r45_link(k, v['probjectid'], v['interested_id'], v['flag'])
                push_message.delay(interest, default_exam_remind_template, v, v['probjectid'])
            except Exception as e:
                print('send r45 error', k, v, e)
    except SoftTimeLimitExceeded:
        push_celery_hanging_message('send_email_of_remind')


@shared_task(queue='task', ignore_result=True, soft_time_limit=30)
def send_email_of_9h():
    try:
        project_exam_all = models.AppProbjectexamrelationuser.objects.filter(Enable=1, Status=0,
                                                                             EndDate__gt=timezone.now())

        # 定时提醒LBI自评与他评
        project_evaluation = project_exam_all.filter(ExId__in=[EX_SELF, EX_OUTHER])
        exam_other_dic = {}
        try:
            for evaluation in project_evaluation:
                end_time = evaluation.EndDate
                now_time = timezone.now()
                res_time = end_time - now_time
                days = res_time.days
                hours = int(res_time.seconds / 60 / 60)
                # 1: 自评，2: 他评
                exam_id = models.AppEvaluation.objects.get(EvaluationId=evaluation.ExId).ExamId
                if exam_id == EX_SELF:
                    template = 'remind_exam_self_9'
                    user = models.SysUser.objects.get(pk=evaluation.UserId)

                    params = {
                        'exName': evaluation.ExName,
                        'end_time': evaluation.EndDate.strftime('%Y-%m-%d'),
                        'gender': settings.GENDER[user.Gender] if user.Gender else '先生/女士',
                        'time_slot': '%s天%s小时' % (days, hours),
                        'truename': user.UserTrueName,
                        'path_link': get_exam_self_link(evaluation.ProbjectId),
                        'flag': 0
                    }

                    push_message.delay(user, template, params, evaluation.ProbjectId)

                else:
                    student = models.SysUser.objects.get(pk=evaluation.interested_id)

                    if not exam_other_dic.get(evaluation.UserId):
                        exam_other_dic[evaluation.UserId] = {
                            'title': evaluation.ExName,
                            'invitedate': evaluation.EndDate.strftime('%Y-%m-%d'),
                            "exname": evaluation.ExName,
                            "student_names": student.UserTrueName,
                            'time_slot': '%s天%s小时' % (days, hours),
                            'path_link': '-',
                            'probjectid': evaluation.ProbjectId,
                            'interested_id': evaluation.interested_id,
                            'flag': 0

                        }
                    else:
                        exam_other_dic[evaluation.UserId]['student_names'] = exam_other_dic[evaluation.UserId][
                                                                                 'student_names'] + '、' + student.UserTrueName
                        exam_other_dic[evaluation.UserId]['flag'] = 1
                        exam_other_dic[evaluation.UserId]['path_link'] = '-'

            if exam_other_dic:
                exam_other = 'remind_exam_other_9'
                for k, v in exam_other_dic.items():
                    interest = models.SysUser.objects.get(User_Id=k)
                    v['path_link'] = get_exam_other_link(k, v['probjectid'], v['interested_id'], v['flag'])
                    push_message.delay(interest, exam_other, v, v['probjectid'])
        except Exception as e:
            print('send other error', project_evaluation, e)

        # 约谈结束定时提醒教练和被教练者填写约谈记录（34，35）
        interview_end_to_coach = 'interview_end_to_coach'
        interview_end_to_student = 'interview_end_to_student'
        project_interview = models.AppProbjectinterview.objects.filter(status=1, needrecord=True,
                                                                       EndTime__lt=timezone.now()).exclude(
            Satisfaction__isnull=False, Times__isnull=False)

        for i in project_interview:
            try:
                student = models.SysUser.objects.get(pk=i.User_Id)
                coach = models.SysUser.objects.get(pk=i.Coach_Id)
                company = models.AppCompany.objects.get(Company_Id=models.AppProbjectrelation.objects.filter(
                    ProbjectId=i.ProbjectId, UserId=i.User_Id).first().CompanyId)

                dic = {
                    'studentname': student.UserTrueName,
                    'end_data': i.EndTime.strftime('%Y-%m-%d'),
                    'company': company.CompanyName,
                    'coachname': coach.UserTrueName,
                    'path_link': '-'
                }
                if i.interviewType == 0:
                    if not i.Times:
                        dic['path_link'] = get_interview_end_to_coach_not_link(i.ProbjectId, student.pk)
                        push_message.delay(coach, interview_end_to_coach, dic, i.ProbjectId)
                    if not i.Satisfaction:
                        dic['path_link'] = get_interview_end_to_student_link()
                        push_message.delay(student, interview_end_to_student, dic, i.ProbjectId)
                else:
                    if not i.Times:
                        dic['path_link'] = get_interview_end_to_coach_type_1_link(i.ProbjectId, i.masteruserid)
                        push_message.delay(coach, interview_end_to_coach, dic, i.ProbjectId)
            except Exception as e:
                print('send interview end error ', i, e)

        # 定时提醒被教练者填写未完成的教练准备度
        exams = project_exam_all.filter(PageCode='r112')
        exam_r112_not_end_to_student = 'exam_r112_not_end_to_student'
        exam_r112_not_end_to_manage = 'exam_r112_not_end_to_manage'

        for exam in exams.filter(Status=0):
            try:

                dic = {
                    "exname": exam.ExName,
                    'time_slot': '%s天%s小时' % get_time_slot(exam.EndDate),
                    'path_link': get_exam_r112_not_end_to_student_link(exam.ProbjectId),
                    'invitedate': exam.EndDate

                }
                student = models.SysUser.objects.get(pk=exam.UserId)
                push_message.delay(student, exam_r112_not_end_to_student, dic, exam.ProbjectId)
            except Exception as e:
                print('send r112 error', exam, e)
        exams_r112 = models.AppProbjectexamrelationuser.objects.filter(Enable=1, PageCode='r112',
                                                                       EndDate__gt=timezone.now())
        # 定时提醒企业管理员督促被教练者填写未完成的教练准备度
        if 0 in exams_r112.values_list('Status', flat=True):
            for e in exams_r112.filter(Status=0):
                try:
                    project = models.AppProbject.objects.get(pk=e.ProbjectId)

                    # 企业管理员可能有多个 IsManger project_id
                    user_ids = models.AppProbjectrelation.objects.filter(ProbjectId=project.pk,
                                                                         IsManger=True).values_list('UserId', flat=True)
                    user_names = models.SysUser.objects.filter(
                        pk__in=exams_r112.filter(Status=0,
                                                 ProbjectId=project.pk).values_list('UserId', flat=True)).values_list(
                        'UserTrueName')
                    company_user = models.SysUser.objects.filter(pk__in=user_ids)
                    str_names = get_str_name(user_names)
                    dic = {
                        "projectname": project.Name,
                        "exname": e.ExName,
                        "pass_count": exams_r112.filter(Status=1, ProbjectId=project.pk).count(),
                        "fail_count": exams_r112.filter(Status=0, ProbjectId=project.pk).count(),
                        "invitedate": e.EndDate.strftime('%Y-%m-%d'),
                        "time_now": timezone.now().strftime("%Y-%m-%d %H:%M"),
                        'time_slot': '%s天%s小时' % get_time_slot(e.EndDate),
                        "usernames": str_names,
                    }
                    if company_user.exists:
                        for user in company_user:
                            push_message.delay(user, exam_r112_not_end_to_manage, dic, project.pk)

                    # 群智管理员就是项目管理员 AppProject: Relation_UserId
                    project_manage_user = models.SysUser.objects.get(pk=project.Relation_UserId)
                    push_message.delay(project_manage_user, exam_r112_not_end_to_manage, dic, project.pk)
                except Exception as ex:
                    print('send r112 to manage error', e, ex)

        # r24被教练者和利益相关者看到的是不同的页面 （11）
        # https://www.qzcoachtest.com/interested/reportmodel?modelid=3&pagecode=r24&probjectid=13&userid=716
        project_exams = project_exam_all.filter(PageCode='r24')
        exam_r24_not_end = 'exam_r24_not_end'
        # 一个利益相关者是多个被教练者的利益相关者
        template_dic = {}
        for exam in project_exams:
            try:
                student = models.SysUser.objects.get(pk=exam.interested_id)
                if not template_dic.get(exam.UserId):
                    template_dic[exam.UserId] = {
                        "exname": exam.ExName,
                        'time_slot': '%s天%s小时' % get_time_slot(exam.EndDate),
                        'invitedate': exam.EndDate,
                        "student_names": student.UserTrueName,
                        'path_link': '-',
                        'roleid': exam.roleid,
                        'projectid': exam.ProbjectId,
                        'interested_id': exam.interested_id,
                        'flag': 0
                    }
                else:
                    template_dic[exam.UserId]['student_names'] = template_dic[exam.UserId]['student_names'] + '、' + student.UserTrueName
                    template_dic[exam.UserId]['flag'] = 1
            except Exception as e:
                print('send r24 error', exam, e)

        for k, v in template_dic.items():
            v['path_link'] = get_exam_r24_link(k, v.get('projectid'), v.get('interested_id'), v.get('flag'))
            interest = models.SysUser.objects.get(pk=k)
            push_message.delay(interest, exam_r24_not_end, v, v.get('projectid'))

        # r45 定时-未完成定时提醒 (20)
        project_exams_r45 = project_exam_all.filter(PageCode='r45')
        exam_r45_not_end = 'exam_r45_not_end'
        # 一个利益相关者是多个被教练者的利益相关者
        template_dic_r45 = {}
        for exam in project_exams_r45:
            try:
                student = models.SysUser.objects.get(pk=exam.interested_id)
                if not template_dic_r45.get(exam.UserId):
                    template_dic_r45[exam.UserId] = {
                        "exname": exam.ExName,
                        'time_slot': '%s天%s小时' % get_time_slot(exam.EndDate),
                        'invitedate': exam.EndDate,
                        "student_names": student.UserTrueName,
                        'flag': 0,
                        'probjectid': exam.ProbjectId,
                        'interested_id': exam.interested_id
                    }
                else:
                    template_dic_r45[exam.UserId]['student_names'] = template_dic_r45[exam.UserId]['student_names'] + '、' + student.UserTrueName
                    template_dic_r45[exam.UserId]['flag'] = 1
            except Exception as e:
                print('send r45 error', exam, e)

        for k, v in template_dic_r45.items():
            try:
                interest = models.SysUser.objects.get(pk=k)
                v['path_link'] = get_exam_r45_link(k, v['probjectid'], v['interested_id'], v['flag'])

                push_message.delay(interest, exam_r45_not_end, v, v['probjectid'])
            except Exception as e:
                print('send r45 error', k, v, e)
    except SoftTimeLimitExceeded:
        push_celery_hanging_message('send_email_of_9h')


@shared_task(queue='task', ignore_result=True, soft_time_limit=15)
def send_email_of_evaluation_end(project_id, userid):
    """ 所有人填写完测评，给群智管理员发邮件 用户提交时跑（会传入project_id） """
    try:
        template = 'evaluation_end_to_manage'
        project_exams = models.AppProbjectexamrelationuser.objects.filter(Enable=1, ProbjectId=project_id)
        # 群智管理员就是项目管理员  AppProject: Relation_UserId
        project = models.AppProbject.objects.get(pk=project_id)
        project_manage_user = models.SysUser.objects.get(pk=project.Relation_UserId)
        company = models.AppCompany.objects.get(Company_Id=project.Company_Id)
        if (not project_exams.filter(UserId=userid, Status=0, ExId=EX_SELF).exists()) and (not project_exams.filter(
                interested_id=userid, Status=0, ExId=EX_OUTHER).exists()):
            student = models.SysUser.objects.get(pk=userid)
            if student.Role_Id == 7:
                student = models.SysUser.objects.get(pk=models.AppProbjectrelation.objects.get(UserId=userid).Interested_Id)
            params = {
                'company': company.CompanyName,
                'gender': settings.GENDER[project_manage_user.Gender] if project_manage_user.Gender else '先生/女士',
                'student': student.UserTrueName,
                'path_link': get_send_email_of_evaluation_end_link(project.pk)
            }
            push_message.delay(project_manage_user, template, params, project.pk)
    except SoftTimeLimitExceeded:
        push_celery_hanging_message('send_email_of_evaluation_end')


@shared_task(queue='task', ignore_result=True, soft_time_limit=30)
def interview_before_task():
    """ 约谈开始前提醒：被教练者/教练/企业管理员/群智管理员 """
    # 企业管理员：AppProbjectrelation ismanger=1 的userId
    try:
        time_now = timezone.now().strftime('%Y-%m-%d %H:%M')
        time_h24 = datetime.timedelta(hours=24)
        time_h1 = datetime.timedelta(hours=1)
        time_m15 = datetime.timedelta(minutes=15)
        interviews = models.AppProbjectinterview.objects.filter(status=1, StartTime__gt=time_now).exclude(
            Q(Times__isnull=False) | Q(Satisfaction__isnull=False)).only('CompanyId', 'User_Id', 'Coach_Id', 'StartTime',
                                                                         'EndTime', 'nowInterview', 'ProbjectId')
        for interview in interviews:
            try:
                coach = models.SysUser.objects.get(pk=interview.Coach_Id)
                project = models.AppProbject.objects.get(pk=interview.ProbjectId)
                student = models.SysUser.objects.get(pk=interview.User_Id)
                interview_time = interview.StartTime.strftime('%Y-%m-%d %H:%M') + '-' + interview.EndTime.strftime('%H:%M')
                company = models.AppCompany.objects.get(
                    Company_Id=models.AppMember.objects.get(User_Id=interview.User_Id).Company_Id)
                # 企业管理员可能有多个 IsManger project_id
                user_ids = models.AppProbjectrelation.objects.filter(ProbjectId=project.pk,
                                                                     IsManger=True).values_list('UserId', flat=True)
                # 非正式约谈 次数都是1
                num = interview.nowInterview
                if interview.interviewType != 0:
                    num = 1

                # 约谈24小时前提醒
                if time_now == (interview.StartTime - time_h24).strftime('%Y-%m-%d %H:%M') and interview.interviewType == 0:
                    interview_24h_to_student = 'interview_24h_to_student'
                    interview_24h_to_coach = 'interview_24h_to_coach'
                    # interview_24h_to_company_manage = 'interview_24h_to_company_manage'
                    interview_24h_to_manage = 'interview_24h_to_manage'

                    # 开始前24小时提醒被教练者（40）
                    param_h24_to_student = {
                        "coachname": coach.UserTrueName,
                        "interviewtime": interview_time,
                        "truename": student.UserTrueName,
                        "num": num,
                        "path_link": get_param_h24_to_student_link()
                    }
                    push_message.delay(student, interview_24h_to_student, param_h24_to_student, interview.ProbjectId)

                    #  开始前24小时提醒教练（41）
                    param_h24_to_coach = {
                        "studentname": student.UserTrueName,
                        "interviewtime": interview_time,
                        "company": company.CompanyName,
                        "num": num,

                    }
                    push_message.delay(coach, interview_24h_to_coach, param_h24_to_coach, interview.ProbjectId)

                    # 开始前24小时提醒企业管理员(42-1)
                    param_h24_to_company_manage = {
                        "studentname": student.UserTrueName,
                        "coachname": coach.UserTrueName,
                        "interviewtime": interview_time,
                        "num": num,

                    }
                    # 企业管理员可能有多个
                    company_user = models.SysUser.objects.filter(pk__in=user_ids)
                    if company_user.exists:
                        for user in company_user:
                            push_message.delay(user, interview_24h_to_manage, param_h24_to_company_manage, interview.ProbjectId)

                    # 开始前24小时提醒企业管理员(42-2)
                    # 群智管理员就是项目管理员 AppProject: Relation_UserId
                    project_manage_user = models.SysUser.objects.get(pk=project.Relation_UserId)
                    push_message.delay(project_manage_user, interview_24h_to_manage, param_h24_to_company_manage, interview.ProbjectId)

                # 约谈1小时/15分钟前提醒
                elif time_now == (interview.StartTime - time_h1).strftime('%Y-%m-%d %H:%M') or\
                        time_now == (interview.StartTime - time_m15).strftime('%Y-%m-%d %H:%M'):

                    interview_to_user = 'interview_1h_to_user'
                    interview_to_manage = 'interview_to_15m_manage'

                    # interested = models.AppMemberinterested.objects.filter(MasterMember_Id=student.pk,
                    #                                                        ProbjectId=project.pk)
                    if time_now == (interview.StartTime - time_h1).strftime('%Y-%m-%d %H:%M'):
                        time_slot = '1小时'
                        # 开始前1小时提醒教练
                        param_1h_to_coach = {
                            'time_slot': time_slot,
                            "interviewer": student.UserTrueName,
                            "truename": coach.UserTrueName,
                            "interviewtime": interview_time,
                            "num": num,
                            "path_link": get_param_1h_to_coach_link(interview.ProbjectId, student.pk)

                        }
                        push_message.delay(coach, interview_to_user, param_1h_to_coach, interview.ProbjectId)

                        if interview.interviewType == 0:
                            # 开始前1小时提醒被教练者
                            param_1h_to_student = {
                                'time_slot': time_slot,
                                "interviewer": coach.UserTrueName,
                                "truename": student.UserTrueName,
                                "interviewtime": interview_time,
                                "num": num,
                                "path_link": get_param_1h_to_student_link()
                            }
                            push_message.delay(student, interview_to_user, param_1h_to_student, interview.ProbjectId)

                        else:
                            # 开始前1小时提醒利益相关者
                            is_student = get_user_is_student(student.pk, interview.ProbjectId)

                            param_15m_to_interested = {
                                'time_slot': time_slot,
                                "interviewer": coach.UserTrueName,
                                "truename": student.UserTrueName,
                                "interviewtime": interview_time,
                                "num": 1,
                                "path_link": get_param_15m_to_interested_default_link()

                            }
                            if is_student:
                                param_15m_to_interested['path_link'] = get_param_15m_to_interested_link()
                            push_message.delay(student, interview_to_user, param_15m_to_interested, interview.ProbjectId)

                    else:

                        time_slot = '15分钟'
                        # 开始前15分钟提醒教练
                        param_15m_to_coach = {
                            'time_slot': time_slot,
                            "interviewer": student.UserTrueName,
                            "truename": student.UserTrueName,
                            "interviewtime": interview_time,
                            "num": num,
                            "path_link": get_param_15m_to_coach_link(interview.ProbjectId, student.pk)
                        }
                        push_message.delay(coach, interview_to_user, param_15m_to_coach, interview.ProbjectId)

                        if interview.interviewType == 0:
                            # 开始前15分钟提醒被教练者
                            param_15m_to_student = {
                                'time_slot': time_slot,
                                "interviewer": coach.UserTrueName,
                                "truename": student.UserTrueName,
                                "interviewtime": interview_time,
                                "num": num,
                                "path_link": get_param_15m_to_student_link(),
                            }
                            push_message.delay(student, interview_to_user, param_15m_to_student, interview.ProbjectId)

                        else:
                            # 开始前15分钟提醒利益相关者
                            is_student = get_user_is_student(student.pk, interview.ProbjectId)

                            param_15m_to_interested = {
                                'time_slot': time_slot,
                                "interviewer": coach.UserTrueName,
                                "truename": student.UserTrueName,
                                "interviewtime": interview_time,
                                "num": 1,
                                "path_link": get_param_15m_to_interested_default_link()

                            }
                            if is_student:
                                param_15m_to_interested['path_link'] = get_param_15m_to_interested_link()
                            push_message.delay(student, interview_to_user, param_15m_to_interested, interview.ProbjectId)

                        # 开始前15分钟提醒企业管理员
                        # 企业管理员可能有多个
                        param_15m_to_manage = {
                            "studentname": student.UserTrueName,
                            "coachname": coach.UserTrueName,
                            "interviewtime": interview_time,
                            "num": num}
                        company_user = models.SysUser.objects.filter(pk__in=user_ids)
                        if company_user.exists:
                            for user in company_user:
                                push_message.delay(user, interview_to_manage, param_15m_to_manage, interview.ProbjectId)
                        # 开始前15分钟提醒群智管理员
                        # 群智管理员就是项目管理员 AppProject: Relation_UserId
                        project_manage_user = models.SysUser.objects.get(pk=project.Relation_UserId)
                        push_message.delay(project_manage_user, interview_to_manage, param_15m_to_manage, interview.ProbjectId)
            except Exception as e:
                print('send befor task error', interview. e)
    except SoftTimeLimitExceeded:
        push_celery_hanging_message('interview_before_task')


@shared_task(queue='task', ignore_result=True, soft_time_limit=30)
def send_email_of_16h():
    try:
        # 截止时间小于24小时，下午4点提醒 (教练准备度)
        time_now = timezone.now()
        time_h24 = datetime.timedelta(hours=24)

        exams = models.AppProbjectexamrelationuser.objects.filter(Enable=1, Status=0,
                                                                  EndDate__range=(time_now, (time_now + time_h24)))
        exam_r112_not_end_to_student = 'exam_r112_not_end_to_student'
        for exam in exams.filter(PageCode='r112'):
            if (exam.EndDate - time_h24) <= timezone.now():
                dic = {
                    "exname": exam.ExName,
                    'time_slot': '%s天%s小时' % get_time_slot(exam.EndDate),
                    'path_link': get_exam_r112_not_end_to_student_link(exam.ProbjectId),
                    'invitedate': exam.EndDate
                }
                student = models.SysUser.objects.get(pk=exam.UserId)
                push_message.delay(student, exam_r112_not_end_to_student, dic, exam.ProbjectId)
        # 截止时间小于24小时，下午4点提醒 (利益相关者调研)
        # 一个利益相关者是多个被教练者的利益相关者
        template_dic = {}
        for exam in exams.filter(PageCode='r24'):
            try:
                student = models.SysUser.objects.get(pk=exam.interested_id)
                if not template_dic.get(exam.UserId):
                    template_dic[exam.UserId] = {
                        "exname": exam.ExName,
                        'time_slot': '%s天%s小时' % get_time_slot(exam.EndDate),
                        'invitedate': exam.EndDate,
                        "student_names": student.UserTrueName,
                        'path_link': '-',
                        'roleid': exam.roleid,
                        'projectid': exam.ProbjectId,
                        'interested_id': exam.interested_id,
                        'flag': 0
                    }
                else:
                    template_dic[exam.UserId]['student_names'] = template_dic[exam.UserId][
                                                                     'student_names'] + '、' + student.UserTrueName
                    template_dic[exam.UserId]['path_link'] = '{}interested/coach'.format(settings.SITE_URL)
            except Exception as e:
                print('16h send r24 error', exam, e)

        for k, v in template_dic.items():
            try:
                v['path_link'] = get_exam_r24_link(k, v.get('projectid'), v.get('interested_id'), v.get('flag'))
                interest = models.SysUser.objects.get(pk=k)
                push_message.delay(interest, 'exam_r24_not_end', v, v.get('projectid'))
            except Exception as e:
                print('16h send r24 dict errot', k, v, e)

        # ===================逾期后下午4点提醒一次=======================
        project_exam_all = models.AppProbjectexamrelationuser.objects.filter(Enable=1, Status=0, EndDate__lt=timezone.now())
        default_exam_remind_template = 'default_exam_remind'
        default_exam_remind_manage_template = 'default_exam_remind_manage'
        now_time = timezone.now()

        # 定时提醒LBI自评与他评
        project_evaluation = project_exam_all.filter(ExId__in=[EX_SELF, EX_OUTHER])
        exam_other_dic = {}
        try:
            for evaluation in project_evaluation:
                end_time = evaluation.EndDate
                now_time = timezone.now()
                res_time = now_time - end_time
                days = res_time.days
                # 1: 自评，2: 他评
                eval_type = models.AppEvaluation.objects.get(EvaluationId=evaluation.ExId).EvalType
                if eval_type == 1:
                    user = models.SysUser.objects.get(pk=evaluation.UserId)

                    params = {
                        'exam_name': evaluation.ExName,
                        'days': days + 1,
                        'truename': user.UserTrueName,
                        'path_link': get_exam_self_link(evaluation.ProbjectId),
                        'flag': 0
                    }

                    push_message.delay(user, default_exam_remind_template, params, evaluation.ProbjectId)

                else:

                    if not exam_other_dic.get(evaluation.UserId):
                        exam_other_dic[evaluation.UserId] = {
                            'exam_name': evaluation.ExName,
                            'days': days + 1,
                            'path_link': '-',
                            'probjectid': evaluation.ProbjectId,
                            'interested_id': evaluation.interested_id,
                            'flag': 0

                        }
                    else:
                        exam_other_dic[evaluation.UserId]['flag'] = 1
                        exam_other_dic[evaluation.UserId]['path_link'] = '-'

            if exam_other_dic:
                for k, v in exam_other_dic.items():
                    interest = models.SysUser.objects.get(User_Id=k)
                    v['path_link'] = get_exam_other_link(k, v['probjectid'], v['interested_id'], v['flag'])
                    push_message.delay(interest, default_exam_remind_template, v, v['probjectid'])
        except Exception as e:
            print('send other error', project_evaluation, e)

        # 定时提醒被教练者填写未完成的教练准备度
        exams = project_exam_all.filter(PageCode='r112')
        for exam in exams.filter(Status=0):
            try:
                exam_time = now_time - exam.EndDate
                dic = {
                    "exam_name": exam.ExName,
                    'days': exam_time.days + 1,
                    'path_link': get_exam_r112_not_end_to_student_link(exam.ProbjectId),

                }
                student = models.SysUser.objects.get(pk=exam.UserId)
                push_message.delay(student, default_exam_remind_template, dic, exam.ProbjectId)
            except Exception as e:
                print('send r112 error', exam, e)
        exams_r112 = models.AppProbjectexamrelationuser.objects.filter(Enable=1, PageCode='r112',
                                                                       EndDate__lt=timezone.now())
        # 定时提醒企业管理员督促被教练者填写未完成的教练准备度
        if 0 in exams_r112.values_list('Status', flat=True):
            for e in exams_r112.filter(Status=0):
                try:
                    project = models.AppProbject.objects.get(pk=e.ProbjectId)
                    exam_time = now_time - e.EndDate
                    company = models.AppCompany.objects.get(Company_Id=project.Company_Id).CompanyName

                    # 企业管理员可能有多个 IsManger project_id
                    user_ids = models.AppProbjectrelation.objects.filter(ProbjectId=project.pk,
                                                                         IsManger=True).values_list('UserId', flat=True)
                    user_names = models.SysUser.objects.filter(
                        pk__in=exams_r112.filter(Status=0,
                                                 ProbjectId=project.pk).values_list('UserId', flat=True)).values_list(
                        'UserTrueName')
                    company_user = models.SysUser.objects.filter(pk__in=user_ids)
                    str_names = get_str_name(user_names)
                    dic = {
                        "exam_name": e.ExName,
                        'days': exam_time.days + 1,
                        'company': company,
                        'user_name': str_names

                    }
                    if company_user.exists:
                        for user in company_user:
                            push_message.delay(user, default_exam_remind_manage_template, dic, project.pk)

                    # 群智管理员就是项目管理员 AppProject: Relation_UserId
                    project_manage_user = models.SysUser.objects.get(pk=project.Relation_UserId)
                    push_message.delay(project_manage_user, default_exam_remind_manage_template, dic, project.pk)
                except Exception as ex:
                    print('send r112 to manage error', e, ex)

        # r24被教练者和利益相关者看到的是不同的页面 （11）
        project_exams = project_exam_all.filter(PageCode='r24')
        # 一个利益相关者是多个被教练者的利益相关者
        template_dic = {}
        for exam in project_exams:
            try:
                exam_time = now_time - exam.EndDate
                student = models.SysUser.objects.get(pk=exam.interested_id)
                if not template_dic.get(exam.UserId):
                    template_dic[exam.UserId] = {
                        "exam_name": exam.ExName,
                        'days': exam_time.days + 1,

                    }
                else:
                    template_dic[exam.UserId]['flag'] = 1
            except Exception as e:
                print('send r24 error', exam, e)

        for k, v in template_dic.items():
            v['path_link'] = get_exam_r24_link(k, v.get('projectid'), v.get('interested_id'), v.get('flag'))
            interest = models.SysUser.objects.get(pk=k)
            push_message.delay(interest, default_exam_remind_template, v, v.get('projectid'))

        # r45 定时-未完成定时提醒 (20)
        project_exams_r45 = project_exam_all.filter(PageCode='r45')
        # 一个利益相关者是多个被教练者的利益相关者
        template_dic_r45 = {}
        for exam in project_exams_r45:
            try:
                exam_time = now_time - exam.EndDate

                if not template_dic_r45.get(exam.UserId):
                    template_dic_r45[exam.UserId] = {
                        "exam_name": exam.ExName,
                        'days': exam_time.days + 1,
                        'flag': 0,
                        'probjectid': exam.ProbjectId,
                        'interested_id': exam.interested_id
                    }
                else:
                    template_dic_r45[exam.UserId]['flag'] = 1
            except Exception as e:
                print('send r45 error', exam, e)

        for k, v in template_dic_r45.items():
            try:
                interest = models.SysUser.objects.get(pk=k)
                v['path_link'] = get_exam_r45_link(k, v['probjectid'], v['interested_id'], v['flag'])
                push_message.delay(interest, default_exam_remind_template, v, v['probjectid'])
            except Exception as e:
                print('send r45 error', k, v, e)
    except SoftTimeLimitExceeded:
        push_celery_hanging_message('send_email_of_16h')


from utils.wechat_oauth import WeChatMiniProgram
from wisdom_v2.models import User, ProjectMember, PublicAttr, ProjectInterview
from utils.messagecenter.center import push_v2_message
from utils.messagecenter.getui import push_to_single, send_work_wechat_coach_notice, send_work_wechat_coachee_notice


@shared_task(queue='task', ignore_result=True, soft_time_limit=15)
def send_wechat_msg(template_id, touser, value_list, url=None, **kwargs):
    try:
        # res = WeChatSubscription().template_send(template_id, user.msg_openid, first={'value': '你好，以下课程将在15分钟后开始'},
        #                                          remark={'value': '若因故无法参加，请及时联系老师'},
        #                                          value_list=[{'value': '一对一辅导'}, {'value': '张三'}])
        if template_id and touser and value_list:
            if not isinstance(touser, str):
                touser = touser.openid
            if touser:
                WeChatMiniProgram().template_send(template_id, touser, value_list, url=None,  **kwargs)
    except SoftTimeLimitExceeded:
        push_celery_hanging_message('send_wechat_msg')
    except Exception as e:
        LarkMessageCenter().send_other_backend_message(
            'celery任务错误', 'error', {'error': f'send_wechat_msg:{str(e)}'})


@shared_task(queue='task', ignore_result=True, soft_time_limit=15)
def send_login_wechat_msg():
    """ 登录通知 --09:00 """
    try:
        user_ids = ProjectMember.objects.filter(role=6, user__last_active__isnull=False, deleted=False,
                                                user__is_send_login_msg=False).values_list('user_id', flat=True)

        for user_id in user_ids:
            if not PublicAttr.objects.filter(target_user_id=user_id, type=1).exists():
                user = User.objects.get(pk=user_id)
                user_project = ProjectMember.objects.filter(role=6, user_id=user_id, deleted=False).first()
                user_project = user_project.project.id if user_project and user_project.project else None
                coach_count = ProjectMember.objects.filter(role=4, project_id=user_project, deleted=False).count()
                if user.last_active.date() + datetime.timedelta(days=1) == datetime.datetime.today().date() and coach_count:
                    # Hi【接收方姓名】你好，欢迎来到群智教练平台，今日有6位教练等待你的预约！
                    send_wechat_msg.delay(settings.WELCOME_TEMPLATE, user.openid,
                                          value_list={'thing3': {'value': f'你有{coach_count}位教练可预约，快来预约辅导吧！'},
                                                      'thing4': {'value': '教练辅导'}})
                    push_v2_message.delay(user, 'login_msg', project_id=user_project)
                    # push_to_single.delay(user.user_cid, title='今日有%s位教练可以预约' % coach_count,
                                        #  body='预约一次教练辅导来体验如何通过对话解决工作难题')
    except SoftTimeLimitExceeded:
        push_celery_hanging_message('send_login_wechat_msg')
    except Exception as e:
        LarkMessageCenter().send_other_backend_message(
            'celery任务错误', 'error', {'error': f'send_login_wechat_msg:{str(e)}'})


@shared_task(queue='task', ignore_result=True, soft_time_limit=30)
def send_interview_of_12():
    """
    前一天中午12点给所有辅导发送消息
    """
    try:
        project_interview = ProjectInterview.objects.filter(
            place_category=ProjectInterviewPlaceCategoryEnum.online_one_to_one,  # 线上一对一辅导
            public_attr__start_time__date=timezone.now().date() + datetime.timedelta(days=1),  # 辅导时间是明天的
            public_attr__status=ATTR_STATUS_INTERVIEW_CONFIRM,  # 约谈类型，状态是已确认的
            public_attr__target_user_id__isnull=False  # 查询有用户数据的辅导
        ).exclude(public_attr__project__isnull=True)  # 排除C端辅导
        for interview in project_interview:
            topic = interview.topic if interview.topic else interview.coachee_topic
            public = interview.public_attr
            interview_time = public.start_time.strftime('%Y年%m月%d日 %H:%M')
            # 先发送小程序订阅消息
            if interview.type != ProjectInterviewTypeEnum.stakeholder_interview:
                send_wechat_msg.delay(
                    settings.TOMORROW_INTERVIEW_TEMPLATE,
                    public.target_user,
                    value_list={
                        'thing1': {'value': topic[:20]},
                        'time3': {'value': interview_time},
                        'thing5': {'value': '如无法按时参加，请及时调整或联系管理员'}})

            # 根据辅导类型发送不同提醒
            if interview.type == ProjectInterviewTypeEnum.chemical_interview:
                # 发送企业微信通知
                interview_date = f"{interview.public_attr.start_time.strftime('%Y-%m-%d %H:%M')}-" \
                                 f"{interview.public_attr.end_time.strftime('%H:%M')}"
                text = f'您{interview_date}的化学面谈将在明天开始，请点击下方链接参加访谈'
                page = f"pages_interview/interview/detail?id={interview.id}&refer=2"
                title = "辅导即将开始"
                msg, sender, external_user_id = get_project_manage_wx_user_id(
                    interview.public_attr.project.pk,
                    interview.public_attr.target_user_id,
                    'send_interview_of_12'
                )
                if msg:
                    sender_user = WorkWechatUser.objects.filter(
                        wx_user_id=sender,
                        user__isnull=False,
                        deleted=False
                    ).first()
                    if sender_user:
                        send_work_wechat_coachee_notice.delay(
                            sender, text, external_user_id, page, title,
                            content_type='add_msg_template',
                            coach_id=None,
                            coach_name=None,
                            project_id=interview.public_attr.project.id,
                            project_name=interview.public_attr.project.name,
                            coachee_id=interview.public_attr.target_user_id,
                            coachee_name=interview.public_attr.target_user.cover_name,
                            forward_by=sender_user.user.cover_name,
                            forward_by_id=sender_user.user.id,
                        )
                # 发送邮件通知
                message_type = 'chemical_interview_tomorrow_message'
                project_member = ProjectMember.objects.filter(
                    user=interview.public_attr.target_user,
                    project=interview.public_attr.project, deleted=False).first()
                manage_list = project_member.project.manager_list
                page = 'pages_interview/interview/detail'
                scene = f"id={interview.id}&refer=2"
                # 生成二维码图跳转链接
                url = get_qr_code(f'{settings.SITE_URL}landing/?p=/{page}&{scene}')

                # 生成短链接
                url_state, short_link = WeChatMiniProgram().get_url_link(page, scene)
                chemical_interview_time = f"{interview.public_attr.start_time.strftime('%Y.%m.%d %H:%M')}-" \
                                          f"{interview.public_attr.end_time.strftime('%H:%M')}"
                params = {
                    "true_name": project_member.user.cover_name,
                    "coach_name": interview.public_attr.user.cover_name,
                    "chemical_interview_time": chemical_interview_time,
                    "manager_name": manage_list[0]['true_name'] if manage_list else '',
                    "manager_email": manage_list[0]['email'] if manage_list else '',
                    "manager_phone": manage_list[0]['phone'] if manage_list else '',
                    "qr_code_url": url,
                    "short_link": short_link
                }
                email = [project_member.user.email]
                message_send_email_base(message_type=message_type, params=params, to_email=email,
                                       project_id=project_member.project_id, receiver_ids=project_member.user_id)
            elif interview.type == ProjectInterviewTypeEnum.stakeholder_interview:

                stakeholder_interview = StakeholderInterview.objects.filter(
                    interview=interview,deleted=False).first()
                # 给利益相关者发送邮件通知
                page = f"pages_interview/interview/detail"
                scene = f'id={interview.id}&refer=2'
                # 生成短链接
                url_state, short_link = WeChatMiniProgram().get_url_link(page, scene)

                manage_list = interview.public_attr.project.manager_list
                url = get_qr_code(f'{settings.SITE_URL}landing/?p=/{page}&{scene}')
                message_type = 'stakeholder_interview_start_notice'
                params = {
                    'stakeholder_name': interview.public_attr.target_user.cover_name,
                    'coachee_name': stakeholder_interview.project_interested.master.cover_name,
                    'interview_time': interview_time,
                    'coach_name': interview.public_attr.user.cover_name,
                    "manager_name": manage_list[0]['true_name'] if manage_list else '',
                    "manager_email": manage_list[0]['email'] if manage_list else '',
                    "manager_phone": manage_list[0]['phone'] if manage_list else '',
                    "qr_code_url": url,
                    "short_link": short_link
                }
                to_email = [interview.public_attr.target_user.email]
                message_send_email_base(message_type, params, to_email, project_id=interview.public_attr.project_id,
                                       receiver_ids=interview.public_attr.target_user_id)

                # 给利益相关者发送短信通知
                if interview.public_attr.target_user.phone:
                    sms_scene = f'id={interview.id}&invite_code={settings.SMS_INVITE_CODE}&refer=3'
                    state, url = WeChatMiniProgram().get_url_link("pages/landing/landing", f'p=/{page}&{sms_scene}')
                    if state:
                        SendSMS().send_stakeholder_interview_start_24h_notice(
                            interview.public_attr.user.cover_name,
                            stakeholder_interview.project_interested.master.cover_name,
                            interview_time, url,
                            interview.public_attr.target_user.phone,
                        )

                # 给教练发送企业微信通知
                work_wechat_user = WorkWechatUser.objects.filter(
                    wx_user_id__isnull=False, user_id=public.user.id, deleted=False).first()
                if work_wechat_user:
                    user = work_wechat_user.user
                    date = '{}-{}'.format(
                        interview.public_attr.start_time.strftime('%Y-%m-%d %H:%M'),
                        interview.public_attr.end_time.strftime('%H:%M'))
                    content_item = [
                        {"key": "客户名称", "value": public.target_user.cover_name},
                        {"key": "所属企业", "value": public.project.company.real_name},
                        {"key": "所属项目", "value": public.project.name},
                        {"key": "辅导时长", "value": int(interview.times)},
                        {"key": "辅导时间", "value": date},
                    ]
                    send_work_wechat_coach_notice.delay(
                        work_wechat_user.wx_user_id,
                        'stakeholder_interview_start_notice_24h',
                        interview_id=interview.pk,
                        project_name=interview.public_attr.project.name,
                        coachee_name=interview.public_attr.target_user.cover_name,
                        interview_time=interview.public_attr.start_time.strftime('%H:%M'),
                        content_item=content_item,
                        coachee_id=interview.public_attr.target_user_id,
                        coach_name=user.cover_name,
                        coach_id=work_wechat_user.user_id
                    )

            else:
                interview_time = public.start_time.strftime('%Y-%m-%d %H:%M') + '~' + public.end_time.strftime('%H:%M')

                push_v2_message.delay(
                    public.user, 'tomorrow_remind_msg',
                    param={'target_user': public.target_user.cover_name, 'interview_time': interview_time}, project_id=public.project_id)
                push_v2_message.delay(
                    public.target_user, 'tomorrow_remind_msg',
                    param={'target_user': public.user.cover_name, 'interview_time': interview_time}, project_id=public.project_id)


    except SoftTimeLimitExceeded:
        push_celery_hanging_message('send_interview_of_12')
    except Exception as e:
        LarkMessageCenter().send_other_backend_message(
            'celery任务错误', 'error', {'error': f'send_interview_of_12:{str(e)}'})


@shared_task(queue='task', ignore_result=True, soft_time_limit=30)
def send_interview_of_9():
    """ 前一天提醒通知 --09:00 """
    try:
        project_interview = ProjectInterview.objects.filter(
            place_category=ProjectInterviewPlaceCategoryEnum.online_one_to_one,  # 线上一对一辅导
            public_attr__start_time__date=timezone.now().date() + datetime.timedelta(days=1),  # 辅导时间是明天的
            public_attr__type=ATTR_TYPE_INTERVIEW, public_attr__status=ATTR_STATUS_INTERVIEW_CONFIRM,  # 约谈类型，状态是已确认的
            public_attr__target_user_id__isnull=False,  # 查询有用户数据的辅导
            deleted=False
        ).exclude(public_attr__project__isnull=True)  # 排除C端辅导
        for interview in project_interview:
            topic = interview.topic if interview.topic else interview.coachee_topic
            public = interview.public_attr
            interview_time = public.start_time.strftime('%Y-%m-%d %H:%M') + '~' + public.end_time.strftime('%H:%M')

            push_v2_message.delay(
                public.user, 'tomorrow_remind_msg',
                param={'target_user': public.target_user.cover_name, 'interview_time': interview_time}, project_id=public.project_id)
            push_v2_message.delay(
                public.target_user, 'tomorrow_remind_msg',
                param={'target_user': public.user.cover_name, 'interview_time': interview_time}, project_id=public.project_id)
            send_wechat_msg.delay(
                settings.TOMORROW_INTERVIEW_TEMPLATE,
                public.target_user,
                value_list={
                    'thing1': {'value': topic[:20]},
                    'time3': {'value': public.start_time.strftime('%Y年%m月%d日 %H:%M')},
                    'thing5': {'value': '如无法按时参加，请及时调整或联系管理员'}})
    except SoftTimeLimitExceeded:
        push_celery_hanging_message('send_interview_of_9')
    except Exception as e:
        LarkMessageCenter().send_other_backend_message(
            'celery任务错误', 'error', {'error': f'send_interview_of_9:{str(e)}'})


@shared_task(queue='task', ignore_result=True, soft_time_limit=30)
def send_interview_of_17():
    """ 前一天提醒通知--17：00 """
    try:
        project_interview = ProjectInterview.objects.filter(
            place_category=ProjectInterviewPlaceCategoryEnum.online_one_to_one,  # 线上一对一辅导
            public_attr__start_time__hour__range=(0, 8),# 当天预约第二天早上八点前的辅导则在当天下午五点提醒
            public_attr__start_time__date=timezone.now().date() + datetime.timedelta(days=1),  # 辅导时间是明天的
            public_attr__type=ATTR_TYPE_INTERVIEW, public_attr__status=ATTR_STATUS_INTERVIEW_CONFIRM,  # 约谈类型，状态是已确认的
            public_attr__target_user_id__isnull=False,  # 查询有用户数据的辅导
            deleted=False
        ).exclude(public_attr__project__isnull=True)  # 排除C端辅导

        for interview in project_interview:
            topic = interview.topic if interview.topic else interview.coachee_topic
            public = interview.public_attr

            interview_time = public.start_time.strftime('%Y-%m-%d %H:%M') + '~' + public.end_time.strftime('%H:%M')

            push_v2_message.delay(
                public.user, 'tomorrow_remind_msg',
                param={'target_user': public.target_user.cover_name, 'interview_time': interview_time}, project_id=public.project_id)
            push_v2_message.delay(
                public.target_user, 'tomorrow_remind_msg',
                param={'target_user': public.user.cover_name, 'interview_time': interview_time}, project_id=public.project_id)
            send_wechat_msg.delay(settings.TOMORROW_INTERVIEW_TEMPLATE, public.target_user, value_list={
                'thing1': {'value': topic[:20]},
                'time3': {'value': public.start_time.strftime('%Y年%m月%d日 %H:%M')},
                'thing5': {'value': '如无法按时参加，请及时调整或联系管理员'}})
    except SoftTimeLimitExceeded:
        push_celery_hanging_message('send_interview_of_17')
    except Exception as e:
        LarkMessageCenter().send_other_backend_message(
            'celery任务错误', 'error', {'error': f'send_interview_of_17:{str(e)}'})


@shared_task(queue='task', ignore_result=True, soft_time_limit=30)
def send_interview_before_30():
    try:
        """ 开始前30分钟提醒 """
        time_minute = datetime.datetime.now() + datetime.timedelta(minutes=30)
        public_query = PublicAttr.objects.filter(
            start_time__date=time_minute.date(),
            start_time__hour=time_minute.hour,
            start_time__minute=time_minute.minute,
            type=1, status=3).exclude(
            interview_public_attr__place_category=ProjectInterviewPlaceCategoryEnum.offline_group_coach
        ).exclude(project__isnull=True)
        for public in public_query:
            interview = ProjectInterview.objects.get(public_attr=public)
            work_wechat_user = WorkWechatUser.objects.filter(
                wx_user_id__isnull=False,
                user_id=public.user.id,
                deleted=False
            ).first()
            interview_time = public.start_time.strftime('%Y-%m-%d %H:%M') + '~' + public.end_time.strftime('%H:%M')
            if interview.type != ProjectInterviewTypeEnum.stakeholder_interview:
                send_wechat_msg.delay(settings.BEFORE_INTERVIEW_TEMPLATE, public.target_user, value_list={
                    'thing1': {'value': interview.public_attr.project.name if interview.public_attr.project_id else '个人辅导'},
                    'time2': {'value': interview.public_attr.start_time.strftime('%Y年%m月%d日 %H:%M:%S')},
                    'thing4': {'value': "如无法按时参加，请及时调整或联系管理员"},
                    'thing11': {'value': interview.public_attr.target_user.cover_name}})
            if interview.type == ProjectInterviewTypeEnum.formal_interview:
                push_v2_message.delay(public.user, 'before_10_remind_msg',
                                      param={'target_user': public.target_user.cover_name, 'interview_time': interview_time})
                if work_wechat_user:
                    company = public.project.company
                    company_name = company.real_name
                    content_item = [
                        {"key": "客户名称", "value": public.target_user.cover_name},
                        {"key": "所属企业", "value": company_name},
                        {"key": "所属项目", "value": public.project.name},
                        {"key": "辅导时长", "value": '{}分钟'.format(interview.times)},
                        {"key": "辅导时间", "value": interview_time},
                    ]
                    old_interview = ProjectInterview.objects.filter(
                        public_attr__start_time__lt=public.start_time,
                        public_attr__user_id=public.user.id,
                        public_attr__target_user_id=public.target_user.id,
                        type=INTERVIEW_TYPE_COACHING, deleted=False
                    ).exclude(public_attr__status=ATTR_STATUS_INTERVIEW_CANCEL).order_by(
                        '-public_attr__start_time').first()
                    
                    send_work_wechat_coach_notice(
                        work_wechat_user.wx_user_id,
                        'formal_interview_30_minute',
                        content_item=content_item,
                        coach_id=public.user.id,
                        company=company_name,
                        project_name=public.project.name,
                        coachee_name=public.target_user.cover_name,
                        project_id=public.project_id,
                        coachee_id=public.target_user.id,
                        coach_name=public.user.cover_name,
                        interview_id=old_interview.id if old_interview else None
                    )
                        
            elif interview.type == ProjectInterviewTypeEnum.stakeholder_interview:

                stakeholder_interview = StakeholderInterview.objects.filter(
                    interview=interview, deleted=False).first()
                # 给利益相关者发送邮件通知
                page = f"pages_interview/interview/detail"
                scene = f'id={interview.id}&refer=2'
                # 生成短链接
                url_state, short_link = WeChatMiniProgram().get_url_link(page, scene)

                manage_list = interview.public_attr.project.manager_list
                url = get_qr_code(f'{settings.SITE_URL}landing/?p=/{page}&{scene}')
                message_type = 'stakeholder_interview_start_notice_30m'
                params = {
                    'stakeholder_name': interview.public_attr.target_user.cover_name,
                    'coachee_name': stakeholder_interview.project_interested.master.cover_name,
                    'coach_name': interview.public_attr.user.cover_name,
                    "manager_name": manage_list[0]['true_name'] if manage_list else '',
                    "manager_email": manage_list[0]['email'] if manage_list else '',
                    "manager_phone": manage_list[0]['phone'] if manage_list else '',
                    "qr_code_url": url,
                    "short_link": short_link,
                }
                to_email = [interview.public_attr.target_user.email]
                message_send_email_base(message_type, params, to_email, project_id=interview.public_attr.project_id,
                                       receiver_ids=interview.public_attr.target_user_id)

                # 给利益相关者发送短信通知
                if interview.public_attr.target_user.phone:
                    sms_scene = f'id={interview.id}&invite_code={settings.SMS_INVITE_CODE}&refer=3'
                    state, url = WeChatMiniProgram().get_url_link("pages/landing/landing", f'p=/{page}&{sms_scene}')
                    if state:
                        SendSMS().send_stakeholder_interview_start_30m_notice(
                            interview.public_attr.user.cover_name,
                            stakeholder_interview.project_interested.master.cover_name,
                            url, interview.public_attr.target_user.phone,
                        )

                if work_wechat_user:
                    company = public.project.company
                    company_name = company.real_name
                    content_item = [
                        {"key": "客户名称", "value": public.target_user.cover_name},
                        {"key": "所属企业", "value": company_name},
                        {"key": "所属项目", "value": public.project.name},
                        {"key": "辅导时长", "value": '{}分钟'.format(interview.times)},
                        {"key": "辅导时间", "value": interview_time},
                    ]
                    send_work_wechat_coach_notice(
                        work_wechat_user.wx_user_id,
                        'stakeholder_interview_start_notice_30m',
                        content_item=content_item,
                        interview_id=interview.pk,
                        project_name=public.project.full_name,
                        coachee_id=public.target_user.id,
                        coachee_name=public.target_user.cover_name,
                        coach_id=public.user.id,
                        coach_name=public.user.cover_name
                    )
            elif interview.type == ProjectInterviewTypeEnum.chemical_interview:
                # 化学面谈发送邮件给客户
                message_type = 'chemical_interview_before_30'
                project_member = ProjectMember.objects.filter(
                    user=interview.public_attr.target_user,
                    project=interview.public_attr.project, deleted=False).first()
                manage_list = project_member.project.manager_list

                # 基础跳转页面和跳转参数
                scene = f"id={interview.id}&refer=2"
                page = 'pages_interview/interview/detail'
                # 生成二维码图跳转链接
                url = get_qr_code(f'{settings.SITE_URL}landing/?p=/{page}&{scene}')
                # 生成短链接
                url_state, short_link = WeChatMiniProgram().get_url_link(
                    'pages_interview/interview/detail',
                    f'id={interview.id}&refer=2')

                chemical_interview_time = f"{interview.public_attr.start_time.strftime('%Y.%m.%d %H:%M')}-" \
                                          f"{interview.public_attr.end_time.strftime('%H:%M')}"
                params = {
                    "true_name": project_member.user.cover_name,
                    "coach_name": interview.public_attr.user.cover_name,
                    "chemical_interview_time": chemical_interview_time,
                    "manager_name": manage_list[0]['true_name'] if manage_list else '',
                    "manager_email": manage_list[0]['email'] if manage_list else '',
                    "manager_phone": manage_list[0]['phone'] if manage_list else '',
                    "qr_code_url": url,
                    "short_link": short_link
                }
                email = [project_member.user.email]
                message_send_email_base(message_type=message_type, params=params, to_email=email, project_id=project_member.project_id,
                                       receiver_ids=project_member.user_id)

                # 化学面谈企业微信发送给教练
                if work_wechat_user:
                    user = work_wechat_user.user
                    date = '{}-{}'.format(
                        interview.public_attr.start_time.strftime('%Y-%m-%d %H:%M'),
                        interview.public_attr.end_time.strftime('%H:%M'))
                    content_item = [
                        {"key": "客户名称", "value": interview.public_attr.target_user.cover_name},
                        {"key": "所属企业", "value": interview.public_attr.project.company.real_name},
                        {"key": "所属项目", "value": interview.public_attr.project.name},
                        {"key": "辅导时长", "value": int(interview.times)},
                        {"key": "辅导时间", "value": date},
                    ]
                    send_work_wechat_coach_notice.delay(
                        work_wechat_user.wx_user_id,
                        'chemical_interview_before_30',
                        interview_id=interview.pk,
                        project_name=interview.public_attr.project.full_name,
                        coachee_name=interview.public_attr.target_user.cover_name,
                        content_item=content_item,
                        user_id=user.pk,
                        coachee_id=interview.public_attr.target_user_id,
                        coach_name=user.cover_name,
                        coach_id=work_wechat_user.user_id
                    )

                # 化学面谈发送企业微信给客户
                interview_date = f"{interview.public_attr.start_time.strftime('%Y-%m-%d %H:%M')}~" \
                                 f"{interview.public_attr.start_time.strftime('%H:%M')}"
                text = f'您{interview_date}的化学面谈即将开始，请点击下方链接参加访谈'
                page = f"pages_interview/interview/detail?id={interview.id}&refer=2"
                title = "辅导即将开始"
                msg, sender, external_user_id = get_project_manage_wx_user_id(
                    interview.public_attr.project.pk,
                    interview.public_attr.target_user_id,
                    'send_interview_before_30'
                )
                if msg:
                    sender_user = WorkWechatUser.objects.filter(
                        wx_user_id=sender,
                        user__isnull=False,
                        deleted=False
                    ).first()
                    if sender_user:
                        send_work_wechat_coachee_notice.delay(
                            sender, text, external_user_id, page, title,
                            content_type='add_msg_template',
                            coach_id=None,
                            coach_name=None,
                            project_id=interview.public_attr.project.id,
                            project_name=interview.public_attr.project.name,
                            coachee_id=interview.public_attr.target_user_id,
                            coachee_name=interview.public_attr.target_user.cover_name,
                            forward_by=sender_user.user.cover_name,
                            forward_by_id=sender_user.user.id,
                        )
    except SoftTimeLimitExceeded:
        push_celery_hanging_message('send_interview_before_30')
    except Exception as e:
        LarkMessageCenter().send_other_backend_message(
            'celery任务错误', 'error', {'error': f'send_interview_before_30:{str(e)}'})

@shared_task(queue='task', ignore_result=True, soft_time_limit=10)
def update_interview_status():
    try:
        PublicAttr.objects.filter(end_time__lte=datetime.datetime.now(), status__lt=4, type=1).update(status=4)
    except SoftTimeLimitExceeded:
        push_celery_hanging_message('update_interview_status')
    except Exception as e:
        LarkMessageCenter().send_other_backend_message(
            'celery任务错误', 'error', {'error': f'update_interview_status:{str(e)}'})


@shared_task(queue='task', ignore_result=True, soft_time_limit=10)
def update_evaluation_module_status():
    try:
        EvaluationModule.objects.filter(end_time__lt=datetime.datetime.now().date(), is_submit=False, deleted=False
                                        ).update(send_email=False)
    except SoftTimeLimitExceeded:
        push_celery_hanging_message('update_evaluation_module_status')
    except Exception as e:
        LarkMessageCenter().send_other_backend_message(
            'celery任务错误', 'error', {'error': f'update_evaluation_module_status:{str(e)}'})



@shared_task(queue='task', ignore_result=True, soft_time_limit=20)
def send_user_behavior_data():
    try:
        from file_read_backwards import FileReadBackwards
        # 获取目录下所有文件
        path_list = os.listdir(settings.LOG_PATH)
        # 删除gz结尾的压缩文件和非all_log的其他日志文件
        path_list = [item for item in path_list if 'gz' not in item.split('.')[-1] and 'all_log' in item.split('.')[0]]
        # 排序读取最新文件
        path_list.sort()
        behavior_data = []
        home_error_data = []
        for log_path in path_list:
            # 拼接需要读取的完整文件路径
            file_path = f'{settings.LOG_PATH}/{log_path}'
            with FileReadBackwards(file_path, encoding="utf-8") as BigFile:
                for item in BigFile:
                    try:
                        date, data = pendulum.from_format(item[6:16], 'YYYY-MM-DD', tz='Asia/Shanghai'), eval(item[30:])
                        if pendulum.yesterday() > date:
                            return
                        if pendulum.yesterday().day != date.day:
                            continue
                        url = data.get('url').split('?')[0]
                        if url == '/coapi/api/v2/my/coachee/search/':
                            try:
                                user_data = jwt.decode(data.get('token')[7:], settings.SECRET_KEY, algorithms=['HS256'])
                            except Exception as e:
                                continue
                            user_id = user_data.get('id')
                            data = data.get('request_body', {}).get('keyword')
                            behavior_data.append(UserBehaviorData(
                                type=UserBehaviorDataTypeEnum.search,
                                user_id=user_id,
                                content=data,
                            ))

                        home_api = [
                            '/coapi/api/v2/my/coach/', '/coapi/api/v2/app/project/member/user_coach/',
                            '/coapi/api/v2/article/topic/', '/coapi/api/v2/time',
                            '/coapi/api/v2/my/coachee/home/', 'coapi/api/v2/app/coach/all_coach_list/',
                            '/coapi/api/v2/app/interview/todo_list/', '/coapi/api/v2/my/coach/home/',
                            '/coapi/api/v2/app/interview/interview_list/']
                        if url in home_api:
                            if data.get('response_body', {}).get('retCode') not in [200, 401, None]:
                                try:
                                    user_data = jwt.decode(data.get('token')[7:], settings.SECRET_KEY, algorithms=['HS256'])
                                except Exception as e:
                                    continue
                                user_id = user_data.get('id')
                                home_error_data.append(url)
                                behavior_data.append(UserBehaviorData(
                                    type=UserBehaviorDataTypeEnum.home_error,
                                    user_id=user_id,
                                    content=data,
                                ))
                    except Exception as e:
                        push_wx_error_message(name='日志文件处理错误', level='error', content={
                            'error': str(e),
                            'name': 'send_user_behavior_data',
                            'data': item})
                        continue
        with transaction.atomic():
            UserBehaviorData.objects.bulk_create(behavior_data)
        if home_error_data:
            home_error_data = list(set(home_error_data))
            push_wx_error_message(name='首页接口错误', level='error', content=home_error_data)
    except SoftTimeLimitExceeded:
        push_celery_hanging_message('send_user_behavior_data')
    except Exception as e:
        LarkMessageCenter().send_other_backend_message(
            'celery任务错误', 'error', {'error': f'send_user_behavior_data:{str(e)}'})


# 初次辅导后没设置成长目标，就在第二天早8点通知
@shared_task(queue='task', ignore_result=True, soft_time_limit=120)
def send_work_wechat_growth_goal_reminder_of_8():
    try:
        yesterday = pendulum.yesterday()
        interviews = ProjectInterview.objects.filter(
            public_attr__end_time__year=yesterday.year,
            public_attr__end_time__month=yesterday.month,
            public_attr__end_time__day=yesterday.day,
            public_attr__project__isnull=False,
            place_category=ProjectInterviewPlaceCategoryEnum.online_one_to_one.value,
            type=ProjectInterviewTypeEnum.formal_interview.value, deleted=False
        ).exclude(public_attr__status=ATTR_STATUS_INTERVIEW_CANCEL)

        if interviews.exists():
            interviews = multiple_field_distinct(interviews, ['public_attr.target_user_id']).all()
            for item in interviews:

                interview = ProjectInterview.objects.filter(
                    public_attr__start_time__lte=yesterday.strftime('%Y-%m-%d %H:%M:%S'),
                    public_attr__target_user__id=item.public_attr.target_user.id,
                    public_attr__project__isnull=False,
                    place_category=ProjectInterviewPlaceCategoryEnum.online_one_to_one.value,
                    type=ProjectInterviewTypeEnum.formal_interview.value, deleted=False,
                ).exclude(public_attr__status=ATTR_STATUS_INTERVIEW_CANCEL)
                # 昨天之前没有辅导，说明是第一次辅导
                if not interview.exists():
                    if not GrowthGoals.objects.filter(
                        public_attr__user_id=item.public_attr.target_user.id,
                        deleted=False,
                    ).exists():
                        work_wechat_user = WorkWechatUser.objects.filter(
                            wx_user_id__isnull=False,
                            user_id=item.public_attr.user.id,
                            deleted=False
                        ).first()
                        if work_wechat_user:
                            company = item.public_attr.project.company
                            company_name = company.real_name
                            content_item = [
                                {"key": "客户名称", "value": item.public_attr.target_user.cover_name},
                                {"key": "所属企业", "value": company_name},
                                {"key": "所属项目", "value": item.public_attr.project.name}
                            ]
                            send_work_wechat_coach_notice(
                                work_wechat_user.wx_user_id,
                                'growth_goal_reminder',
                                content_item=content_item,
                                coach_id=item.public_attr.user.id,
                                coachee_name=item.public_attr.target_user.cover_name,
                                project_id=item.public_attr.project_id,
                                project_name=item.public_attr.project.name,
                                coachee_id=item.public_attr.target_user.id,
                                coach_name=item.public_attr.user.cover_name,
                            )
    except SoftTimeLimitExceeded:
        push_celery_hanging_message('send_work_wechat_growth_goal_reminder_of_8')
    except Exception as e:
        LarkMessageCenter().send_other_backend_message(
            'celery任务错误', 'error', {'error': f'send_work_wechat_growth_goal_reminder_of_8:{str(e)}'})


# 未完成辅导记录，早8点通知
@shared_task(queue='task', ignore_result=True, soft_time_limit=120)
def send_work_wechat_undone_interview_reminder_of_8():
    try:
        raw_now = pendulum.now()
        # 辅导完成后的1、2、5天，早8点发送填写提醒
        for item in [1, 2, 5]:
            now = raw_now.subtract(days=item)
            interviews = ProjectInterview.objects.filter(
                public_attr__start_time__year=now.year,
                public_attr__start_time__month=now.month,
                public_attr__start_time__day=now.day,
                public_attr__project__isnull=False,
                coach_record_status=False,
                place_category=ProjectInterviewPlaceCategoryEnum.online_one_to_one.value,
                type=ProjectInterviewTypeEnum.formal_interview.value, deleted=False
            ).exclude(public_attr__status=ATTR_STATUS_INTERVIEW_CANCEL)
            if interviews.exists():
                for interview in interviews:

                    try:
                        user = interview.public_attr.target_user
                        work_wechat_user = WorkWechatUser.objects.filter(
                            wx_user_id__isnull=False,
                            user_id=interview.public_attr.user.id,
                            deleted=False
                        ).first()
                        if work_wechat_user:
                            company = interview.public_attr.project.company
                            company_name = company.real_name
                            content_item = [
                                {"key": "客户名称", "value": user.cover_name},
                                {"key": "所属企业", "value": company_name},
                                {"key": "所属项目", "value": interview.public_attr.project.name},
                                {"key": "辅导时间", "value": '{}-{}'.format(
                                        interview.public_attr.start_time.strftime('%Y-%m-%d %H:%M'),
                                        interview.public_attr.end_time.strftime('%H:%M'))},
                            ]
                            send_work_wechat_coach_notice(
                                work_wechat_user.wx_user_id,
                                'undone_interview',
                                content_item=content_item,
                                interview_id=interview.id,
                                company=company_name,
                                coachee_name=user.cover_name,
                                coach_id=interview.public_attr.user.id,
                                project_id=interview.public_attr.project_id,
                                project_name=interview.public_attr.project.name,
                                coachee_id=user.id,
                                coach_name=interview.public_attr.user.cover_name,

                            )
                    except Exception as e:
                        api_action_logging.info(
                            {'error': str(e), 'interview': interview.id})
    except SoftTimeLimitExceeded:
        push_celery_hanging_message('send_work_wechat_undone_interview_reminder_of_8')
    except Exception as e:
        LarkMessageCenter().send_other_backend_message(
            'celery任务错误', 'error', {'error': f'send_work_wechat_undone_interview_reminder_of_8:{str(e)}'})


# 下次辅导预约提醒
@shared_task(queue='task', ignore_result=True, soft_time_limit=120)
def send_work_wechat_next_interview_reminder_of_8():
    try:
        now = datetime.datetime.now()
        day_after_13 = datetime.date.today() + datetime.timedelta(days=-13)
        reminder_list = []

        projects = Project.objects.filter(
            deleted=False,
            is_sign=True,
            start_time__lte=now,
            end_time__gt=now
        )
        for project in projects:
            project_member = ProjectMember.objects.filter(
                project_id=project.id, deleted=False
            )
            for item in project_member:
                project_bundle = ProjectBundle.objects.filter(
                    project_member_id=item.id, deleted=False
                ).first()
                if project_bundle:
                    try:
                        one_to_one_coach = OneToOneCoach.objects.filter(
                            project_bundle=project_bundle,
                            deleted=False, type=CoachTypeEnum.online.value)
                        # 检查剩余时长限制
                        if one_to_one_coach.exists():
                            # 检查线上一对一时长的被教练者
                            all_times = one_to_one_coach.aggregate(used_times=Sum('online_time')).get('used_times')
                            all_times = all_times if all_times else 0
                            used_time = ProjectInterview.objects.filter(
                                place_category=ProjectInterviewPlaceCategoryEnum.online_one_to_one.value,
                                public_attr__type=ATTR_TYPE_INTERVIEW,
                                type=INTERVIEW_TYPE_COACHING,
                                public_attr__project=item.project,
                                deleted=False,
                                public_attr__target_user=item.user).exclude(
                                public_attr__status=ATTR_STATUS_INTERVIEW_CANCEL).aggregate(used_times=Sum('times'))
                            used_time = used_time.get('used_times', 0) if \
                                used_time.get('used_times', 0) else 0
                            if all_times * 60 > used_time:
                                reminder_list.append([project.id, item.user.id, item.user.cover_name])
                    except Exception:
                        continue

        for project_id, user_id, user_name in reminder_list:
            attr = PublicAttr.objects.filter(
                project_id=project_id,
                target_user_id=user_id,
                type=ATTR_TYPE_INTERVIEW
            ).exclude(status=ATTR_STATUS_INTERVIEW_CANCEL).order_by('end_time').last()
            if attr and attr.end_time.date() < day_after_13:
                work_wechat_user = WorkWechatUser.objects.filter(
                    wx_user_id__isnull=False,
                    user_id=attr.user.id,
                    deleted=False
                ).first()
                if work_wechat_user:
                    company = attr.project.company
                    company_name = company.real_name
                    content_item = [
                        {"key": "客户名称", "value": user_name},
                        {"key": "所属企业", "value": company_name},
                        {"key": "所属项目", "value": attr.project.name},
                    ]
                    send_work_wechat_coach_notice(
                        work_wechat_user.wx_user_id,
                        'next_interview',
                        company=company_name,
                        coachee_name=user_name,
                        content_item=content_item,
                        coach_id=attr.user.id,
                        project_id=project_id,
                        project_name=attr.project.name,
                        coachee_id=user_id,
                        coach_name=attr.user.cover_name,

                    )
    except SoftTimeLimitExceeded:
        push_celery_hanging_message('send_work_wechat_next_interview_reminder_of_8')
    except Exception as e:
        LarkMessageCenter().send_other_backend_message(
            'celery任务错误', 'error', {'error': f'send_work_wechat_next_interview_reminder_of_8:{str(e)}'})


# 预约辅导开始当天八点提醒
@shared_task(queue='task', ignore_result=True, soft_time_limit=120)
def send_work_wechat_interview_reminder_of_8():
    try:
        now = pendulum.today()
        public_query = PublicAttr.objects.filter(
            start_time__year=now.year,
            start_time__month=now.month,
            start_time__day=now.day,
            interview_public_attr__place_category=ProjectInterviewPlaceCategoryEnum.online_one_to_one.value,  # 查询线上一对一的辅导
            type=ATTR_TYPE_INTERVIEW
        ).exclude(status=ATTR_STATUS_INTERVIEW_CANCEL).exclude(project__isnull=True).exclude(
            interview_public_attr__type__in=[ProjectInterviewTypeEnum.chemical_interview,
                                             ProjectInterviewTypeEnum.stakeholder_interview]).distinct().all()  # 排除已取消的和无项目的个人辅导
        for public in public_query:
            work_wechat_user = WorkWechatUser.objects.filter(
                wx_user_id__isnull=False,
                user_id=public.user.id,
                deleted=False
            ).first()
            if work_wechat_user:
                company = public.project.company
                company_name = company.real_name
                send_work_wechat_coach_notice(
                    work_wechat_user.wx_user_id,
                    'interview_day',
                    start_time=public.start_time.strftime('%H:%M'),
                    end_time=public.end_time.strftime('%H:%M'),
                    company=company_name,
                    coachee_name=public.target_user.cover_name,
                    coach_id=public.user.id,
                    project_id=public.project.id,
                    project_name=public.project.name,
                    coachee_id=public.target_user.id,
                    coach_name=public.user.cover_name,
                )

    except SoftTimeLimitExceeded:
        push_celery_hanging_message('send_work_wechat_interview_reminder_of_8')
    except Exception as e:
        LarkMessageCenter().send_other_backend_message(
            'celery任务错误', 'error', {'error': f'send_work_wechat_interview_reminder_of_8:{str(e)}'})


# 客户填写阶段报告后 提醒教练填写，早8点提醒教练填写报告
@shared_task(queue='task', ignore_result=True, soft_time_limit=120)
def send_work_wechat_coach_task_of_8():
    try:
        now = pendulum.yesterday()
        coach_task = CoachTask.objects.filter(
            coach_submit_time__isnull=True,  # '教练未提交任务报告
            coachee_submit_time__year=now.year,
            coachee_submit_time__month=now.month,
            coachee_submit_time__day=now.day,
            public_attr__user_id__isnull=False,
            deleted=False,
        ).all()
        for item in coach_task:
            work_wechat_user = WorkWechatUser.objects.filter(
                wx_user_id__isnull=False,
                user_id=item.public_attr.user.id,
                deleted=False
            ).first()
            if work_wechat_user:
                company = item.public_attr.project.company
                company_name = company.real_name
                content_item = [
                    {"key": "客户名称", "value": item.public_attr.target_user.cover_name},
                    {"key": "所属企业", "value": company_name},
                    {"key": "所属项目", "value": item.public_attr.project.name},
                    {"key": "教练任务", "value": item.template.title,}
                ]
                send_work_wechat_coach_notice(
                    work_wechat_user.wx_user_id,
                    'coach_task',
                    content_item=content_item,
                    task_id=item.id,
                    company=company_name,
                    coachee_name=item.public_attr.target_user.cover_name,
                    task_name=item.template.title,
                    coach_id=item.public_attr.user.id,
                    project_id=item.public_attr.project.id,
                    project_name=item.public_attr.project.name,
                    coachee_id=item.public_attr.target_user.id,
                    coach_name=item.public_attr.user.cover_name,
                )
    except SoftTimeLimitExceeded:
        push_celery_hanging_message('send_work_wechat_coach_task_of_8')
    except Exception as e:
        LarkMessageCenter().send_other_backend_message(
            'celery任务错误', 'error', {'error': f'send_work_wechat_coach_task_of_8:{str(e)}'})


# 满足报告填写条件后7天且客户和教练均未填写阶段报告，早8点提醒教练填写报告
@shared_task(queue='task', ignore_result=True, soft_time_limit=120)
def send_work_wechat_coach_task_7day_of_8():
    try:
        yesterday = datetime.date.today() + datetime.timedelta(days=-7)
        for coach_task in CoachTask.objects.filter(
                deleted=False, public_attr__user_id__isnull=False,
                coach_submit_time__isnull=True,  # '教练未提交任务报告
                template__write_role=InterviewRecordTemplateRoleEnum.coach_student.value):
            hours = float(coach_task.hours)
            interview_time = ProjectInterview.objects.filter(
                public_attr__target_user_id=coach_task.project_bundle.project_member.user_id,
                public_attr__project_id=coach_task.project_bundle.project_id,
                place_category=ProjectInterviewPlaceCategoryEnum.online_one_to_one.value,
                type=ProjectInterviewTypeEnum.formal_interview.value,
                public_attr__end_time__lt=yesterday, deleted=False
            ).exclude(public_attr__status=ATTR_STATUS_INTERVIEW_CANCEL).aggregate(used_times=Sum('times'))
            interview_time = interview_time.get('used_times', 0) if interview_time.get('used_times', 0) else 0
            interview_time = round(interview_time / 60, 1) if interview_time > 0 else 0
            if hours <= interview_time:
                work_wechat_user = WorkWechatUser.objects.filter(
                    wx_user_id__isnull=False,
                    user_id=coach_task.public_attr.user.id,
                    deleted=False
                ).first()
                if work_wechat_user:
                    company = coach_task.public_attr.project.company
                    company_name = company.real_name
                    content_item = [
                        {"key": "客户名称", "value": coach_task.public_attr.target_user.cover_name},
                        {"key": "所属企业", "value": company_name},
                        {"key": "所属项目", "value": coach_task.public_attr.project.name},
                        {"key": "教练任务", "value": coach_task.template.title,}
                    ]

                    send_work_wechat_coach_notice(
                        work_wechat_user.wx_user_id,
                        'coach_task',
                        content_item=content_item,
                        task_id=coach_task.id,
                        company=company_name,
                        coachee_name=coach_task.public_attr.target_user.cover_name,
                        task_name=coach_task.template.title,
                        coach_id=coach_task.public_attr.user.id,
                        project_id=coach_task.public_attr.project.id,
                        project_name=coach_task.public_attr.project.name,
                        coachee_id=coach_task.public_attr.target_user.id,
                        coach_name=coach_task.public_attr.user.cover_name,
                    )

    except SoftTimeLimitExceeded:
        push_celery_hanging_message('send_work_wechat_coach_task_7day_of_8')
    except Exception as e:
        LarkMessageCenter().send_other_backend_message(
            'celery任务错误', 'error', {'error': f'send_work_wechat_coach_task_7day_of_8:{str(e)}'})


@shared_task(queue='default', ignore_result=True, soft_time_limit=120)
def send_coach_add_interview_message(interview_id, project_id):
    """
    辅导预约提醒

    > 触发时机：教练帮客户预约辅导后立即提醒客户

    """
    try:
        interview = ProjectInterview.objects.get(pk=interview_id)
        project = Project.objects.get(pk=project_id)

        msg, sender, external_user_id = get_project_manage_wx_user_id(
            project.pk,
            interview.public_attr.target_user.id,
            'send_coach_add_interview_message'
        )
        if not msg:
            return

        sender_user = WorkWechatUser.objects.filter(
            wx_user_id=sender,
            user__isnull=False,
            deleted=False
        ).first()
        if sender_user:
            interview_time = interview.public_attr.start_time.strftime("%Y-%m-%d %H:%M") + '-' + interview.public_attr.end_time.strftime("%H:%M")
            text = f'咱们下次的教练辅导将在{interview_time}进行，点击下方小程序链接查看详情'
            page = "pages/interview/interview?refer=2"
            title = "辅导预约提醒"
            send_work_wechat_coachee_notice(
                sender, text, external_user_id, page, title, file_name='interview_reserve.jpg',
                coach_id=interview.public_attr.user.id,
                coach_name=interview.public_attr.user.cover_name,
                project_id=interview.public_attr.project.id,
                project_name=interview.public_attr.project.name,
                coachee_id=interview.public_attr.target_user.id,
                coachee_name=interview.public_attr.target_user.cover_name,
                forward_by=sender_user.user.cover_name,
                forward_by_id=sender_user.user.id,
            )

    except SoftTimeLimitExceeded:
        push_celery_hanging_message('send_coach_add_interview_message')
    except Exception as e:
        LarkMessageCenter().send_other_backend_message(
            'celery任务错误', 'error', {'error': f'send_coach_add_interview_message:{str(e)}'})


@shared_task(queue='task', ignore_result=True, soft_time_limit=120)
def send_interview_start_message_of_8():
    """
    辅导开始提醒

    > 触发时机：辅导当天早8点
    """
    try:
        now = datetime.datetime.now()
        interviews = ProjectInterview.objects.filter(
            public_attr__start_time__year=now.year,
            public_attr__start_time__month=now.month,
            public_attr__start_time__day=now.day,
            public_attr__project__isnull=False,
            place_category=ProjectInterviewPlaceCategoryEnum.online_one_to_one.value, # 查询线上一对一辅导
            type=ProjectInterviewTypeEnum.formal_interview.value, deleted=False   # 只查询问答形式的辅导
        ).exclude(public_attr__status=ATTR_STATUS_INTERVIEW_CANCEL)  # 排除已取消的
        if interviews.exists():
            for interview in interviews:
                msg, sender, external_user_id = get_project_manage_wx_user_id(
                    interview.public_attr.project.pk,
                    interview.public_attr.target_user.id,
                    'send_interview_start_message_of_8'
                )
                if not msg:
                    return
                sender_user = WorkWechatUser.objects.filter(
                    wx_user_id=sender,
                    user__isnull=False,
                    deleted=False
                ).first()
                if sender_user:
                    interview_time = interview.public_attr.start_time.strftime(
                        "%H:%M") + '-' + interview.public_attr.end_time.strftime("%H:%M")
                    text = f'我们今天{interview_time}有一对一教练辅导，请准时参加，点击下方小程序链接，回顾上次约谈记录，为本次约谈做好准备吧'
                    title = "辅导开始提醒"
                    project_interview = ProjectInterview.objects.filter(
                        place_category=ProjectInterviewPlaceCategoryEnum.online_one_to_one.value,
                        type=ProjectInterviewTypeEnum.formal_interview.value,
                        public_attr__start_time__lt=now,
                        public_attr__project__isnull=False,
                        public_attr__project=interview.public_attr.project,
                        public_attr__user=interview.public_attr.user,
                        public_attr__target_user=interview.public_attr.target_user,
                        deleted=False
                    ).order_by('-public_attr__start_time').first()
                    if project_interview:
                        interview_id = project_interview.pk
                        page = f"pages_interview/interview/detail?id={interview_id}&refer=2"
                    else:
                        page = f"pages/interview/interview?refer=2"
                    send_work_wechat_coachee_notice(
                        sender, text, external_user_id, page, title, file_name='interview_start.jpg',
                        coach_id=interview.public_attr.user.id,
                        coach_name=interview.public_attr.user.cover_name,
                        project_id=interview.public_attr.project.id,
                        project_name=interview.public_attr.project.name,
                        coachee_id=interview.public_attr.target_user.id,
                        coachee_name=interview.public_attr.target_user.cover_name,
                        forward_by=sender_user.user.cover_name,
                        forward_by_id=sender_user.user.id,
                    )

    except SoftTimeLimitExceeded:
        push_celery_hanging_message('send_interview_start_message_of_8')
    except Exception as e:
        LarkMessageCenter().send_other_backend_message(
            'celery任务错误', 'error', {'error': f'send_interview_start_message_of_8:{str(e)}'})


@shared_task(queue='task', ignore_result=True, soft_time_limit=120)
def send_interview_record_write_message_of_9():
    """
    辅导记录填写提醒

    > 触发时机：辅导完成后的1天的，早9点发送填写提醒
    """
    try:
        yesterday = datetime.date.today() + datetime.timedelta(days=-1)

        project_interviews = ProjectInterview.objects.filter(
            coachee_record_status=False, public_attr__end_time__year=yesterday.year, deleted=False,
            public_attr__project__isnull=False, public_attr__end_time__month=yesterday.month,
            public_attr__end_time__day=yesterday.day).exclude(public_attr__status=ATTR_STATUS_INTERVIEW_CANCEL).exclude(
            chemical_interview__chemical_interview_status=ChemicalInterviewStatusEnum.unselected)

        if project_interviews.exists():
            for interview in project_interviews:
                msg, sender, external_user_id = get_project_manage_wx_user_id(
                    interview.public_attr.project.pk,
                    interview.public_attr.target_user.id,
                    'send_interview_record_write_message_of_9'
                )
                if not msg:
                    return

                sender_user = WorkWechatUser.objects.filter(
                    wx_user_id=sender,
                    user__isnull=False,
                    deleted=False
                ).first()
                if sender_user:
                    text = '昨天的辅导记录还未填写，点击下方链接完成填写吧'
                    title = "辅导记录填写提醒"
                    page = f"pages_interview/record/record_group?interview_id={interview.pk}&refer=2"
                    send_work_wechat_coachee_notice(
                        sender, text, external_user_id, page, title, file_name='interview_record_fill.jpg',
                        coach_id=interview.public_attr.user.id,
                        coach_name=interview.public_attr.user.cover_name,
                        project_id=interview.public_attr.project.id,
                        project_name=interview.public_attr.project.name,
                        coachee_id=interview.public_attr.target_user.id,
                        coachee_name=interview.public_attr.target_user.cover_name,
                        forward_by=sender_user.user.cover_name,
                        forward_by_id=sender_user.user.id,
                    )

    except SoftTimeLimitExceeded:
        push_celery_hanging_message('send_interview_record_write_message_of_9')
    except Exception as e:
        LarkMessageCenter().send_other_backend_message(
            'celery任务错误', 'error', {'error': f'send_interview_record_write_message_of_9:{str(e)}'})


@shared_task(queue='task', ignore_result=True, soft_time_limit=120)
def send_add_interview_gt_14_of_9():
    """
    下次辅导预约提醒

    >   触发时机：所属项目状态时 进行中且有剩余辅导时长的客户，当天距上次辅导时间 大于 14天 提醒客户预约下次辅导时间，当天早9点发送
    """
    try:
        now = datetime.datetime.now()
        day_after_14 = datetime.date.today() + datetime.timedelta(days=-14)

        project_members = ProjectMember.objects.filter(
            project__is_sign=True, deleted=False,
            project__start_time__lt=now, project__end_time__gt=now
        )
        for project_member in project_members:
            online_progress = project_member.online_progress
            if '/' in online_progress:
                used_time, all_times = online_progress.split('/')[0], online_progress.split('/')[1]
                if all_times != '无限制':
                    used_time = float(used_time)
                    all_times = float(all_times.replace('小时', ''))
                    if used_time >= all_times:
                        continue

                project_interview = ProjectInterview.objects.filter(
                    place_category=ProjectInterviewPlaceCategoryEnum.online_one_to_one.value, deleted=False,
                    type=ProjectInterviewTypeEnum.formal_interview.value,
                    public_attr__project=project_member.project, public_attr__target_user=project_member.user,
                    public_attr__user__isnull=False).exclude(public_attr__status=ATTR_STATUS_INTERVIEW_CANCEL
                                                             ).order_by('public_attr__end_time').last()
                if project_interview:
                    interview_date = project_interview.public_attr.end_time.date()
                    if interview_date > day_after_14:
                        continue
                msg, sender, external_user_id = get_project_manage_wx_user_id(
                    project_member.project_id,
                    project_member.user.id,
                    'send_add_interview_gt_14_of_9'
                )
                if not msg:
                    continue

                sender_user = WorkWechatUser.objects.filter(
                    wx_user_id=sender,
                    user__isnull=False,
                    deleted=False
                ).first()
                if sender_user:
                    text = '距离您上次辅导已过去14天，点击下方小程序链接，预约教练辅导吧'
                    title = "下次辅导预约提醒"
                    page = "pages_interview/interview/addinterview?refer=2"
                    send_work_wechat_coachee_notice(
                        sender, text, external_user_id, page, title, file_name='interview_reserve.jpg',
                        coach_id=project_interview.public_attr.user.id,
                        coach_name=project_interview.public_attr.user.cover_name,
                        project_id=project_interview.public_attr.project.id,
                        project_name=project_interview.public_attr.project.name,
                        coachee_id=project_interview.public_attr.target_user.id,
                        coachee_name=project_interview.public_attr.target_user.cover_name,
                        forward_by=sender_user.user.cover_name,
                        forward_by_id=sender_user.user.id,
                    )

    except SoftTimeLimitExceeded:
        push_celery_hanging_message('send_add_interview_gt_14_of_9')
    except Exception as e:
        LarkMessageCenter().send_other_backend_message(
            'celery任务错误', 'error', {'error': f'send_add_interview_gt_14_of_9:{str(e)}'})


@shared_task(queue='default', ignore_result=True, soft_time_limit=120)
def send_interview_view_message(interview_id):
    """
    辅导记录查看提醒

    > 触发时机：教练填写辅导记录后立即提醒客户查看
    """
    try:
        interview = ProjectInterview.objects.get(pk=interview_id)
        msg, sender, external_user_id = get_project_manage_wx_user_id(
            interview.public_attr.project_id,
            interview.public_attr.target_user.id,
            'send_interview_view_message'
        )
        if not msg:
            return

        sender_user = WorkWechatUser.objects.filter(
            wx_user_id=sender,
            user__isnull=False,
            deleted=False
        ).first()
        if sender_user:
            interview_date = interview.public_attr.start_time.strftime("%m月%d日%H:%M")
            text = f'您的教练{interview.public_attr.user.cover_name}已填写{interview_date}的辅导记录，点击下方小程序链接查看吧'
            title = "辅导记录查看提醒"
            page = f"pages_interview/interview/detail?id={interview_id}&refer=2"
            send_work_wechat_coachee_notice(
                sender, text, external_user_id, page, title, file_name='interview_record_check.jpg',
                coach_id=interview.public_attr.user.id,
                coach_name=interview.public_attr.user.cover_name,
                project_id=interview.public_attr.project.id,
                project_name=interview.public_attr.project.name,
                coachee_id=interview.public_attr.target_user.id,
                coachee_name=interview.public_attr.target_user.cover_name,
                forward_by=sender_user.user.cover_name,
                forward_by_id=sender_user.user.id,
            )

    except SoftTimeLimitExceeded:
        push_celery_hanging_message('send_interview_view_message')
    except Exception as e:
        LarkMessageCenter().send_other_backend_message(
            'celery任务错误', 'error', {'error': f'send_interview_view_message:{str(e)}'})


@shared_task(queue='task', ignore_result=True, soft_time_limit=120)
def coach_task_write_remind_of_9():
    """
    教练任务填写提醒

    > 触发时机：满足报告填写条件后1天，早9点提醒客户完成报告填写
    """
    try:
        for coach_task in CoachTask.objects.filter(
                deleted=False, coachee_submit_time__isnull=True,
                template__write_role__in=[InterviewRecordTemplateRoleEnum.student.value,
                                          InterviewRecordTemplateRoleEnum.coach_student.value]).\
                exclude(type=NewCoachTaskTypeEnum.stakeholder_research.value):
            hours = float(coach_task.hours)
            interview_time = ProjectInterview.objects.filter(
                public_attr__target_user_id=coach_task.project_bundle.project_member.user_id,
                public_attr__project_id=coach_task.project_bundle.project_id, deleted=False,
                place_category=ProjectInterviewPlaceCategoryEnum.online_one_to_one.value,
                type=ProjectInterviewTypeEnum.formal_interview.value,
                public_attr__end_time__lt=datetime.date.today()
                ).exclude(public_attr__status=ATTR_STATUS_INTERVIEW_CANCEL).aggregate(used_times=Sum('times'))
            interview_time = interview_time.get('used_times', 0) if interview_time.get('used_times', 0) else 0
            interview_time = round(interview_time / 60, 1) if interview_time > 0 else 0
            if hours <= interview_time:
                msg, sender, external_user_id = get_project_manage_wx_user_id(
                    coach_task.project_bundle.project_id,
                    coach_task.project_bundle.project_member.user.id,
                    'coach_task_write_remind_of_9'
                )
                if not msg:
                    return
                sender_user = WorkWechatUser.objects.filter(
                    wx_user_id=sender,
                    user__isnull=False,
                    deleted=False
                ).first()
                if not sender:
                    continue
                text = f'已完成{coach_task.template.title}辅导，请在3天内点击下方链接填写{coach_task.template.title}报告，' \
                       f'报告内容将分享给上级主管及项目组，期待看到您的进步'
                title = f"{coach_task.template.title}报告填写提醒"
                message = f"教练任务填写提醒提醒"
                page = f"pages_customer/coach_task/coach_task?coach_task_id={coach_task.pk}&refer=2"
                send_work_wechat_coachee_notice(
                    sender, text, external_user_id, page, title,
                    coach_id=coach_task.public_attr.user.id,
                    coach_name=coach_task.public_attr.user.cover_name,
                    project_id=coach_task.project_bundle.project.id,
                    project_name=coach_task.project_bundle.project.name,
                    coachee_id=coach_task.project_bundle.project_member.user.id,
                    coachee_name=coach_task.project_bundle.project_member.user.cover_name,
                    forward_by=sender_user.user.cover_name,
                    forward_by_id=sender_user.user.id,
                    message=message
                )

    except SoftTimeLimitExceeded:
        push_celery_hanging_message('coach_task_write_remind_of_9')
    except Exception as e:
        LarkMessageCenter().send_other_backend_message(
            'celery任务错误', 'error', {'error': f'coach_task_write_remind_of_9:{str(e)}'})


@shared_task(queue='default', ignore_result=True, soft_time_limit=120)
def coach_task_view_message(coach_task_id):
    """
    教练任务查看提醒

    > 触发时机：教练提交阶段报告后，立即提醒客户查看
    """
    try:
        coach_task = CoachTask.objects.get(pk=coach_task_id)
        msg, sender, external_user_id = get_project_manage_wx_user_id(
            coach_task.project_bundle.project_id,
            coach_task.project_bundle.project_member.user.id,
            'coach_task_view_message'
        )
        if not msg:
            return
        sender_user = WorkWechatUser.objects.filter(
            wx_user_id=sender,
            user__isnull=False,
            deleted=False
        ).first()
        if sender_user:
            text = f'教练{coach_task.public_attr.user.cover_name}已填写{coach_task.template.title}辅导报告，' \
                   f'这将是提交给上级主管及项目组的完整版本，您可点击下方链接查看或调整报告内容'
            title = f"{coach_task.template.title}报告查看提醒"
            message = f"教练任务查看提醒"
            page = f"pages_customer/coach_task/coach_task_report?coach_task_id={coach_task.pk}&refer=2"
            send_work_wechat_coachee_notice(
                sender, text, external_user_id, page, title,
                coach_id=coach_task.public_attr.user.id,
                coach_name=coach_task.public_attr.user.cover_name,
                project_id=coach_task.project_bundle.project.id,
                project_name=coach_task.project_bundle.project.name,
                coachee_id=coach_task.project_bundle.project_member.user.id,
                coachee_name=coach_task.project_bundle.project_member.user.cover_name,
                forward_by=sender_user.user.cover_name,
                forward_by_id=sender_user.user.id,
                message=message
            )

    except SoftTimeLimitExceeded:
        push_celery_hanging_message('coach_task_view_message')
    except Exception as e:
        LarkMessageCenter().send_other_backend_message(
            'celery任务错误', 'error', {'error': f'coach_task_view_message:{str(e)}'})


@shared_task(queue='task', ignore_result=True, soft_time_limit=120)
def send_work_wx_message_count():
    """
    每天23：59 发送当前发送给每个客户的消息数量

    """
    try:
        results = []
        for wx_user in WorkWechatUser.objects.filter(external_user_id__isnull=False, deleted=False, user_id__isnull=False):
            count = work_wechat_redis.get(wx_user.external_user_id + '_' + datetime.datetime.now().strftime("%Y-%m-%d"))
            if count:
                name = wx_user.user.cover_name
                user_id = wx_user.user_id
                external_user_id = wx_user.external_user_id
                results.append(f"姓名:{name}  user_id: {user_id}  external_user_id: {external_user_id}  本日发送次数: {count.decode()}\n")
        if results:
            push_send_work_wx_message_count(results)
    except SoftTimeLimitExceeded:
        push_celery_hanging_message('send_work_wx_message_count')
    except Exception as e:
        LarkMessageCenter().send_other_backend_message(
            'celery任务错误', 'error', {'error': f'send_work_wx_message_count:{str(e)}'})


@shared_task(queue='task', ignore_result=True, soft_time_limit=120)
def send_work_wx_start_evaluation_of_9():
    """
    测评开始提醒

    客户有测评，在测评开始日期早9点，提醒客户做测评
    """
    try:
        date = datetime.datetime.today()
        evaluation = EvaluationModule.objects.filter(
            start_time__year=date.year,
            start_time__month=date.month,
            start_time__day=date.day,
            is_submit=False,
            deleted=False
        ).all()
        for item in evaluation:

            msg, sender, external_user_id = get_project_manage_wx_user_id(
                item.project_bundle.project_id,
                item.project_bundle.project_member.user.id,
                'send_work_wx_start_evaluation_of_9'
            )
            if not msg:
                return
            if item.evaluation.code == MANAGE_EVALUATION:
                page = f"pages_evaluation/evaluation_instructions/" \
                       f"evaluation_instructions&id={item.evaluation.id}&title={item.evaluation.name}&refer=2"
            elif item.evaluation.code == LBI_EVALUATION:
                page = f'pages_evaluation/evaluation_guidance/evaluation_guidance?id={item.evaluation.id}&refer=2'
            else:
                continue
            sender_user = WorkWechatUser.objects.filter(
                wx_user_id=sender,
                user__isnull=False,
                deleted=False
            ).first()
            if sender_user:
                project_coach = ProjectCoach.objects.filter(
                    project_id=item.project_bundle.project.id,
                    member_id=item.project_bundle.project_member.user.id,
                    deleted=False,
                ).first()
                coach_id, coach_name = (project_coach.coach.user.id,
                                        project_coach.coach.user.cover_name) if project_coach else (None, None)

                text = f'您的{item.evaluation.name}已开启，请及时完成'
                title = f"{item.evaluation.name}填写提醒"
                message = f"测评开始提醒填写"
                send_work_wechat_coachee_notice(
                    sender, text, external_user_id, page, title, file_name='evaluation_fill.jpg',
                    coach_id=coach_id,
                    coach_name=coach_name,
                    project_id=item.project_bundle.project.id,
                    project_name=item.project_bundle.project.name,
                    coachee_id=item.project_bundle.project_member.user.id,
                    coachee_name=item.project_bundle.project_member.user.cover_name,
                    forward_by=sender_user.user.cover_name,
                    forward_by_id=sender_user.user.id,
                    message=message
                )

    except SoftTimeLimitExceeded:
        push_celery_hanging_message('send_work_wx_start_evaluation_of_9')
    except Exception as e:
        LarkMessageCenter().send_other_backend_message(
            'celery任务错误', 'error', {'error': f'send_work_wx_start_evaluation_of_9:{str(e)}'})


@shared_task(queue='task', ignore_result=True, soft_time_limit=120)
def send_work_wx_undone_evaluation_of_9():
    """
    测评即将结束提醒

    测评结束日期前1天早9点，提醒客户做测评
    """
    try:
        yesterday = datetime.date.today() + datetime.timedelta(days=1)
        evaluation = EvaluationModule.objects.filter(
            end_time__year=yesterday.year,
            end_time__month=yesterday.month,
            end_time__day=yesterday.day,
            is_submit=False,
            deleted=False
        ).all()
        for item in evaluation:
            msg, sender, external_user_id = get_project_manage_wx_user_id(
                item.project_bundle.project_id,
                item.project_bundle.project_member.user.id,
                'send_work_wx_undone_evaluation_of_9'
            )
            if not msg:
                return

            if item.evaluation.code == MANAGE_EVALUATION:
                page = f"pages_evaluation/evaluation_instructions/" \
                       f"evaluation_instructions&id={item.evaluation.id}&title={item.evaluation.name}&refer=2"
            elif item.evaluation.code == LBI_EVALUATION:
                page = f'pages_evaluation/evaluation_guidance/evaluation_guidance?id={item.evaluation.id}&refer=2'
            else:
                continue

            sender_user = WorkWechatUser.objects.filter(
                wx_user_id=sender,
                user__isnull=False,
                deleted=False
            ).first()
            if sender_user:
                project_coach = ProjectCoach.objects.filter(
                    project_id=item.project_bundle.project.id,
                    member_id=item.project_bundle.project_member.user.id,
                    deleted=False,
                ).first()
                coach_id, coach_name = (project_coach.coach.user.id,
                                        project_coach.coach.user.cover_name) if project_coach else (None, None)

                text = f'您的{item.evaluation.name}还未完成，请在今天内完成'
                title = f"{item.evaluation.name}填写提醒"
                message = f"测评结束提醒填写"
                send_work_wechat_coachee_notice(
                    sender, text, external_user_id, page, title, file_name='evaluation_fill.jpg',
                    coach_id=coach_id,
                    coach_name=coach_name,
                    project_id=item.project_bundle.project.id,
                    project_name=item.project_bundle.project.name,
                    coachee_id=item.project_bundle.project_member.user.id,
                    coachee_name=item.project_bundle.project_member.user.cover_name,
                    forward_by=sender_user.user.cover_name,
                    forward_by_id=sender_user.user.id,
                    message=message
                )

    except SoftTimeLimitExceeded:
        push_celery_hanging_message('send_work_wx_undone_evaluation_of_9')
    except Exception as e:
        LarkMessageCenter().send_other_backend_message(
            'celery任务错误', 'error', {'error': f'send_work_wx_undone_evaluation_of_9:{str(e)}'})


@shared_task(queue='task', ignore_result=True, soft_time_limit=120)
def send_work_wx_reading_of_9():
    """
    线上阅读提醒

    客户的服务配置-文章模块 配置了文章  且已经配置第一次线下集体辅导，在第1次线下集体辅导开始前3天早9点
    """
    try:
        date = datetime.date.today() + datetime.timedelta(days=3)
        user_ids = list(set(ProjectInterview.objects.filter(
            place_category=ProjectInterviewPlaceCategoryEnum.offline_group_coach.value,
            type=INTERVIEW_TYPE_COACHING, deleted=False,
            record_type=InterviewRecordTypeEnum.questionnaire.value,
            # coach_group_module__project_group_coach__type=GroupCoachTypeEnum.collective_tutoring,
            public_attr__start_time__date=date,
        ).exclude(
            public_attr__status=ATTR_STATUS_INTERVIEW_CANCEL).distinct().values_list('public_attr__target_user_id',
                                                                                     flat=True)))

        for item in user_ids:
            interview = ProjectInterview.objects.filter(
                place_category=ProjectInterviewPlaceCategoryEnum.offline_group_coach.value,
                type=INTERVIEW_TYPE_COACHING, deleted=False,
                # coach_group_module__project_group_coach__type=GroupCoachTypeEnum.collective_tutoring,
                record_type=InterviewRecordTypeEnum.questionnaire.value,
                public_attr__target_user_id=item
            ).exclude(public_attr__status=ATTR_STATUS_INTERVIEW_CANCEL).order_by('public_attr__start_time').first()

            article = ArticleModule.objects.filter(
                project_bundle__project_member__user_id=interview.public_attr.target_user_id,
                project_bundle__project_id=interview.public_attr.project_id, deleted=False
            )
            if interview.public_attr.start_time.date() == date and article.exists():
                msg, sender, external_user_id = get_project_manage_wx_user_id(
                    interview.public_attr.project_id,
                    interview.public_attr.target_user.id,
                    'send_work_wx_reading_of_9'
                )
                if not msg:
                    return
                text = f'请您阅读以下资料，在{date.month}月{date.day}日前完成课前学习'
                title = f"线上阅读提醒"
                sender_user = WorkWechatUser.objects.filter(
                    wx_user_id=sender,
                    user__isnull=False,
                    deleted=False
                ).first()
                if sender_user:
                    page = "pages/article/index?type=6&index=1&refer=2"
                    send_work_wechat_coachee_notice(
                        sender, text, external_user_id, page, title, file_name='reading.jpg',
                        coach_id=interview.public_attr.user.id,
                        coach_name=interview.public_attr.user.name,
                        project_id=interview.public_attr.project.id,
                        project_name=interview.public_attr.project.name,
                        coachee_id=interview.public_attr.target_user.id,
                        coachee_name=interview.public_attr.target_user.cover_name,
                        forward_by=sender_user.user.cover_name,
                        forward_by_id=sender_user.user.id,
                    )
    except SoftTimeLimitExceeded:
        push_celery_hanging_message('send_work_wx_reading_of_9')
    except Exception as e:
        LarkMessageCenter().send_other_backend_message(
            'celery任务错误', 'error', {'error': f'send_work_wx_reading_of_9:{str(e)}'})


@shared_task(queue='task', ignore_result=True, soft_time_limit=120)
def send_work_wx_offline_group_coach_of_20():
    """
    线下集体辅导上课提醒

    线下集体辅导开始前一天晚8点
    """
    try:
        date = datetime.date.today() + datetime.timedelta(days=1)
        interviews = ProjectInterview.objects.filter(
            place_category=ProjectInterviewPlaceCategoryEnum.offline_group_coach.value,
            # coach_group_module__project_group_coach__type=GroupCoachTypeEnum.collective_tutoring,
            type=INTERVIEW_TYPE_COACHING, deleted=False,
            record_type=InterviewRecordTypeEnum.questionnaire.value,
            public_attr__start_time__date=date,
        ).exclude(public_attr__status=ATTR_STATUS_INTERVIEW_CANCEL).all()

        for item in interviews:
            title = f"工作坊上课提醒"
            group_coach = item.coach_group_module.filter(deleted=False).first()
            if group_coach:
                if group_coach.project_group_coach.type == GroupCoachTypeEnum.group_tutoring:
                    title = '小组辅导上课提醒'
            msg, sender, external_user_id = get_project_manage_wx_user_id(
                item.public_attr.project_id,
                item.public_attr.target_user.id,
                'send_work_wx_offline_group_coach_of_20'
            )
            if not msg:
                continue
            text = f'您的课程将于明天{item.public_attr.start_time.hour}点在{item.place}开始，请准时参加'
            page = None
            sender_user = WorkWechatUser.objects.filter(
                wx_user_id=sender,
                user__isnull=False,
                deleted=False
            ).first()
            if sender_user:
                send_work_wechat_coachee_notice(
                    sender, text, external_user_id, page, title, content_type='add_msg_template_content', file_name='interview_start.jpg',
                    coach_id=item.public_attr.user.id,
                    coach_name=item.public_attr.user.name,
                    project_id=item.public_attr.project.id,
                    project_name=item.public_attr.project.name,
                    coachee_id=item.public_attr.target_user.id,
                    coachee_name=item.public_attr.target_user.cover_name,
                    forward_by=sender_user.user.cover_name,
                    forward_by_id=sender_user.user.id,
                )
    except SoftTimeLimitExceeded:
        push_celery_hanging_message('send_work_wx_offline_group_coach_of_20')
    except Exception as e:
        LarkMessageCenter().send_other_backend_message(
            'celery任务错误', 'error', {'error': f'send_work_wx_offline_group_coach_of_20:{str(e)}'})


def send_lbi_evaluation_remind(public_attr, report_id):
    """
    LBI客户报告查看提醒

    """
    try:
        msg, sender, external_user_id = get_project_manage_wx_user_id(
            public_attr.project_id,
            public_attr.target_user.id,
            'send_lbi_evaluation_remind'
        )
        if not msg:
            return
        text = f'您的LBI测评报告已生成，点击下方链接查看'
        page = f'pages_action/evaluation_report/evaluation_report_lbi?id={report_id}&refer=2'
        title = f"LBI客户报告查看提醒"

        sender_user = WorkWechatUser.objects.filter(
            wx_user_id=sender,
            user__isnull=False,
            deleted=False
        ).first()
        if sender_user:
            project_coach = ProjectCoach.objects.filter(
                project_id=public_attr.project_id,
                member_id=public_attr.user.id,
                deleted=False,
            ).first()
            coach_id, coach_name = (project_coach.coach.user.id,
                                    project_coach.coach.user.cover_name) if project_coach else (None, None)
            send_work_wechat_coachee_notice(
                sender, text, external_user_id, page, title,
                content_type='add_msg_template', file_name='evaluation_fill.jpg',
                coach_id=coach_id,
                coach_name=coach_name,
                project_id=public_attr.project.id,
                project_name=public_attr.project.name,
                coachee_id=public_attr.target_user.id,
                coachee_name=public_attr.target_user.cover_name,
                forward_by=sender_user.user.cover_name,
                forward_by_id=sender_user.user.id,
            )
    except SoftTimeLimitExceeded:
        push_celery_hanging_message('send_lbi_evaluation_remind')
    except Exception as e:
        LarkMessageCenter().send_other_backend_message(
            'celery任务错误', 'error', {'error': f'send_lbi_evaluation_remind:{str(e)}'})


@shared_task(queue='task', ignore_result=True, soft_time_limit=120)
def save_lbi_evaluation(evaluation, public_attr):
    try:
        state, msg = lbi_evaluation_action.lbi_evaluation(evaluation, public_attr)
        if not state:
            push_wx_error_message(name='LBI测评报告错误提醒', level='warning', content=msg)

    except SoftTimeLimitExceeded:
        push_celery_hanging_message('save_lbi_evaluation')
    except Exception as e:
        LarkMessageCenter().send_other_backend_message(
            'celery任务错误', 'error', {'error': f'save_lbi_evaluation:{str(e)}'})


@shared_task(queue='task', ignore_result=True)
def get_user_pdf_url(obj_id, file_name, pdf_type, data_type='report', start_date=None, end_date=None):
    """
    data_type: 管理后台的截图页面，report-报告，agreement-教练简历
    """
    try:
        report_url = f"{str(settings.SITE_URL).replace('www', 'admin')}#/{data_type}"
        text = f'?id={obj_id}&type={pdf_type}'
        params = {
            'url': report_url + text,
            'filename': file_name
        }
        if pdf_type == PdfReportTypeEnum.project_progress_report:
            params['url'] = f"{params['url']}&pid={obj_id}&start={start_date}&end={end_date}"
        raw = requests.get(settings.PDF_URL, params)
        try:
            if raw.ok:
                # pdf文件使用中文名，返回的时候文件名会出现乱码，由本地文件名替换返回的文件名。
                raw_url = str(raw.text).split('/')
                raw_url[-1] = file_name
                pdf_url = '/'.join(raw_url)

                # lbi测评个人报告
                if pdf_type in [PdfReportTypeEnum.lbi_personal_report.value,
                                PdfReportTypeEnum.lbi_self_evaluation_report.value]:
                    evaluation_report = EvaluationReport.objects.get(pk=obj_id)
                    evaluation_report.pdf_url = pdf_url
                    evaluation_report.save()
                elif pdf_type == PdfReportTypeEnum.lbi_personal_report_company_manager.value:
                    evaluation_report = EvaluationReport.objects.get(pk=obj_id)
                    evaluation_report.company_manager_pdf_url = pdf_url
                    evaluation_report.save()
                # lbi项目报告，教练型管理者项目报告
                elif pdf_type in [PdfReportTypeEnum.lbi_project_report.value,
                                PdfReportTypeEnum.manage_evaluation_report.value]:
                    project_evaluation_report = ProjectEvaluationReport.objects.get(pk=obj_id)
                    project_evaluation_report.pdf_url = pdf_url
                    project_evaluation_report.save()
                # 个人报告（改变观察反馈）
                elif pdf_type == PdfReportTypeEnum.change_observation_report.value:
                    personal_report = PersonalReport.objects.get(pk=obj_id)
                    personal_report.pdf_url = pdf_url
                    personal_report.save()
                # 利益相关者调研, 教练任务
                elif pdf_type in [PdfReportTypeEnum.stakeholder_report.value, PdfReportTypeEnum.default_coach_task.value]:
                    coach_task = CoachTask.objects.get(pk=obj_id)
                    coach_task.report_url = pdf_url
                    coach_task.save()
                # 教练合同生成
                elif pdf_type == PdfReportTypeEnum.coach_contract.value:
                    user_contract = UserContract.objects.get(pk=obj_id)
                    user_contract.contract_url = pdf_url
                    user_contract.save()
                    work_wechat_user = WorkWechatUser.objects.filter(user=user_contract.user, deleted=False).first()
                    if work_wechat_user and work_wechat_user.wx_user_id:
                        #2.14.5移除教练飞书填写身份证+银行卡
                        # send_work_wechat_coach_notice(
                        #     work_wechat_user.wx_user_id,
                        #     'coach_add_materials',
                        #     coach_id=work_wechat_user.user.id,
                        # )
                        # time.sleep(3)
                        resume = Resume.objects.filter(coach__user=user_contract.user, is_customization=False, deleted=False).first()
                        send_work_wechat_coach_notice(
                            work_wechat_user.wx_user_id,
                            'coach_update_resume',
                            coach_id=work_wechat_user.user.id,
                            coach_name=work_wechat_user.user.cover_name,
                            resume_id=resume.id if resume else None
                        )
                # 项目进度报告
                elif pdf_type == PdfReportTypeEnum.project_progress_report:
                    return pdf_url
                else:
                    pass
                api_action_logging.info({
                    'code': raw.status_code,
                    'msg': f'报告{obj_id}获取转pdf文件链接成功：{raw.text}'})
            else:
                push_wx_error_message(name='pdf文件生成失败提醒', level='warning', content=raw.text)
                api_action_logging.info({
                    'code': raw.status_code,
                    'msg': f'报告{obj_id}获取转pdf文件链接失败：{raw.text}'})
                return
        except Exception as e:
            push_wx_error_message(name='pdf文件生成失败提醒', level='warning', content=raw.text)
            api_action_logging.info({
                'code': raw.status_code,
                'error': str(e),
                'msg': f'报告{obj_id}获取转pdf文件链接失败：{raw.text}'})
    except SoftTimeLimitExceeded:
        push_celery_hanging_message('get_user_pdf_url')
    except Exception as e:
        LarkMessageCenter().send_other_backend_message(
            'celery任务错误', 'error', {'error': f'get_user_pdf_url:{str(e)}'})


@shared_task(queue='default', ignore_result=True, soft_time_limit=20)
def check_celery_health():
    """
    触发检查celery健康状态

    """
    try:
        pass
    except SoftTimeLimitExceeded:
        push_celery_hanging_message('check_celery_health')


@shared_task(queue='default', ignore_result=True, soft_time_limit=60)
def generate_manage_project_report(project_id):
    try:
        json_data = {}
        project = Project.objects.get(pk=project_id)
        json_data['project_name'] = project.name
        # ====================== 完成情况
        # 已归档用户id
        exclude_user_id = list(ProjectMember.objects.filter(
            is_forbidden=True, project_id=project_id, deleted=False).values_list('user_id', flat=True))
        complete_report = EvaluationReport.objects.filter(deleted=False, evaluation__code=MANAGE_EVALUATION,
                                                          public_attr__project_id=project_id).exclude(
            public_attr__user_id__in=exclude_user_id)
        all_report = EvaluationModule.objects.filter(project_bundle__project_id=project_id,
                                                     evaluation__code=MANAGE_EVALUATION, deleted=False).exclude(
            project_bundle__project_member__user_id__in=exclude_user_id)

        finished_users = list(complete_report.values_list('public_attr__user__true_name', flat=True))

        not_complete_users = list(all_report.exclude(is_submit=1).values_list(
            'project_bundle__project_member__user__true_name', flat=True))

        completion = {'complete_count': complete_report.count(), 'all_count': all_report.count()}

        completion['finished_users'] = finished_users
        completion['not_complete_users'] = not_complete_users
        json_data['completion'] = completion

        # ====================== 综合分析
        # comprehensive_analysis
        comprehensive_analysis = {}
        x_data = ['未知区', '潜能区', '常用区', '宝藏区']
        y_data = [0, 0, 0, 0]
        complete_score = list(complete_report.values('public_attr__user__true_name', 'score'))
        unknown_area_users, potential_area_users, common_area_users, treasure_area_users = [], [], [], []

        for score in complete_score:
            if 20 <= score['score'] <= 40:
                y_data[0] += 1
                unknown_area_users.append(score['public_attr__user__true_name'])
            elif 41 <= score['score'] <= 60:
                y_data[1] += 1
                potential_area_users.append(score['public_attr__user__true_name'])
            elif 61 <= score['score'] <= 80:
                y_data[2] += 1
                common_area_users.append(score['public_attr__user__true_name'])
            else:
                y_data[3] += 1
                treasure_area_users.append(score['public_attr__user__true_name'])

        unknown_area_percentage = str(round(y_data[0] / sum(y_data) * 100, 2)) + '%' if y_data[0] != 0 else '0%'
        potential_area_percentage = str(round(y_data[1] / sum(y_data) * 100, 2)) + '%' if y_data[1] != 0 else '0%'
        common_area_percentage = str(round(y_data[2] / sum(y_data) * 100, 2)) + '%' if y_data[2] != 0 else '0%'
        treasure_area_percentage = str(round(y_data[3] / sum(y_data) * 100, 2)) + '%' if y_data[3] != 0 else '0%'
        comprehensive_analysis['x_data'] = x_data
        comprehensive_analysis['y_data'] = y_data
        comprehensive_analysis['detail_data'] = []
        unknown_area = {}
        unknown_area['title'] = '未知区'
        unknown_area['percentage'] = unknown_area_percentage
        unknown_area['content'] = '团队成员的自主担责不够理想，自己事无巨细都需要花时间参与决策，为了达成目标要付出很多精力和时间。'
        unknown_area['customer'] = '、'.join(unknown_area_users) if unknown_area_users else ''
        comprehensive_analysis['detail_data'].append(unknown_area)

        potential_area = {}
        potential_area['title'] = '潜能区'
        potential_area['percentage'] = potential_area_percentage
        potential_area['content'] = '有效的互动行为可能是无意识做出的，缺乏进行高效管理对话的思路或能力。' \
                                    '尤其身处压力或繁忙状态时，难以让自己的教练潜能在关键时刻发挥影响力。'
        potential_area['customer'] = '、'.join(potential_area_users) if potential_area_users else ''
        comprehensive_analysis['detail_data'].append(potential_area)

        common_area = {}
        common_area['title'] = '常用区'
        common_area['percentage'] = common_area_percentage
        common_area['content'] = '对有效管理对话的关键能力掌握程度有深有浅，由于这些短板或盲区，面对复杂情境或有挑战的员工时，' \
                                 '达不到理想的管理效果。'
        common_area['customer'] = '、'.join(common_area_users) if common_area_users else ''
        comprehensive_analysis['detail_data'].append(common_area)

        treasure_area = {}
        treasure_area['title'] = '宝藏区'
        treasure_area['percentage'] = treasure_area_percentage
        treasure_area['content'] = '将行为和能力沉淀为管理理念和可复制的对话方法，成为教练型管理者的先行者和倡导者，' \
                                   '在更大的组织范围内协助教练文化的建立，发挥更大的影响力。'
        treasure_area['customer'] = '、'.join(treasure_area_users) if treasure_area_users else ''
        comprehensive_analysis['detail_data'].append(treasure_area)

        json_data['comprehensive_analysis'] = comprehensive_analysis

        # ====================== 管理行为分析
        management_analysis = {}
        # result_sort_var = report_result.values('tIndex').annotate(score_var=Variance('Score'),
        #                                                           score_avg=Avg('Score')).order_by('score_var')
        # 柱状图
        histogram_list = []
        k_text = {0: '了解人性，具有同理心，能建立起人与人之间的信任|理解人', 1: '设定清晰的目标，并能够将目标达成共识|共识目标',
                  2: '支持下属在解决问题，完成任务过程中获得成长|启发人', 3: '把想法变成落地可行的行动与任务|坚定行动',
                  4: '在解决问题、完成任务的过程中促进个人的成长|发展人'}
        for i in [2, 0, 4, 1, 3]:
            data = {}
            score = EvaluationReportScore.objects.filter(
                report__evaluation__code=MANAGE_EVALUATION, deleted=False,
                report__public_attr__project_id=project_id, section=i)
            if exclude_user_id:
                score = score.exclude(report__public_attr__user_id__in=exclude_user_id)
            avg_score = score.aggregate(avg_score=Avg('score'))['avg_score']
            x_data, y_data = get_histogram_data(score)

            text, title = k_text[i].split('|')
            data['title'] = title
            data['avg_score'] = round(avg_score / 20, 1),
            data['text'] = text
            data['x_data'] = x_data
            data['y_data'] = y_data
            histogram_list.append(data)
        management_analysis['histogram_list'] = histogram_list

        # 优势 提升 差异
        question_content = []
        answers_sort_var = EvaluationAnswer.objects.filter(
            public_attr__project_id=project_id,
            option__question__evaluation__code=MANAGE_EVALUATION)
        if exclude_user_id:
            answers_sort_var = answers_sort_var.exclude(public_attr__user_id__in=exclude_user_id)
        answers_sort_var = answers_sort_var.values('option__question__order').annotate(
            score_var=Variance('option__score'),
            score_avg=Avg('option__score')
        ).order_by('score_var')

        # 优势
        advantage_list = sorted((answers_sort_var), key=lambda item: item['score_avg'], reverse=True)

        question_text_list = [QUESTION_TEXT[order['option__question__order']] for order in advantage_list[0:3]]

        advantage_data = {}
        advantage_data['text'] = '群体优势体现在这些方面：'
        advantage_data['title'] = question_text_list
        question_content.append(advantage_data)

        # 提升
        question_text_list = [QUESTION_TEXT[order['option__question__order']] for order in advantage_list[-3:]]


        ascension_data = {}
        ascension_data['text'] = '群体在这些方面需要提升：'
        ascension_data['title'] = question_text_list
        question_content.append(ascension_data)

        # 差异
        question_text_list = [QUESTION_TEXT[order['option__question__order']] for order in list(answers_sort_var)[-3:]]

        differences_data = {}
        differences_data['text'] = '群体在这些方面的表现差异最大：'
        differences_data['title'] = question_text_list
        question_content.append(differences_data)

        management_analysis['question_content'] = question_content
        json_data['management_analysis'] = management_analysis

        # ====================== 教练能力分析
        ability_to_coach = {}
        # 网状图
        avg_build_trust_score = complete_report.aggregate(
            avg_build_trust_score=Avg('build_trust_score'))['avg_build_trust_score']

        avg_promote_action_score = complete_report.aggregate(
            avg_promote_action_score=Avg('promote_action_score'))['avg_promote_action_score']

        avg_consensus_target_score = complete_report.aggregate(
            avg_consensus_target_score=Avg('consensus_target_score'))['avg_consensus_target_score']

        avg_tutor_feedback_score = complete_report.aggregate(
            avg_tutor_feedback_score=Avg('tutor_feedback_score'))['avg_tutor_feedback_score']

        avg_inspired_question_score = complete_report.aggregate(
            avg_inspired_question_score=Avg('inspired_question_score'))['avg_inspired_question_score']

        avg_active_listen_score = complete_report.aggregate(
            avg_active_listen_score=Avg('active_listen_score'))['avg_active_listen_score']
        spider_map = {}
        spider_map['avg_build_trust_score'] = round(avg_build_trust_score, 2)
        spider_map['avg_promote_action_score'] = round(avg_promote_action_score, 2)
        spider_map['avg_consensus_target_score'] = round(avg_consensus_target_score, 2)
        spider_map['avg_tutor_feedback_score'] = round(avg_tutor_feedback_score, 2)
        spider_map['avg_inspired_question_score'] = round(avg_inspired_question_score, 2)
        spider_map['avg_active_listen_score'] = round(avg_active_listen_score, 2)
        ability_to_coach['spider_map'] = spider_map

        # 饼图
        pie_chart_list = []
        for a_text in ['build_trust_score|建立信任', 'promote_action_score|促进行动', 'active_listen_score|积极聆听',
                       'consensus_target_score|共识目标', 'inspired_question_score|启发提问',
                       'tutor_feedback_score|辅导反馈']:
            text, title = a_text.split('|')
            pie_data = {}
            t_data = get_pie_chart(complete_report, text)
            pie_data['avg_score'] = spider_map['avg_' + text]
            pie_data['pie_data'] = t_data
            pie_data['title'] = title
            pie_chart_list.append(pie_data)


        ability_to_coach['pie_chart_list'] = pie_chart_list
        json_data['ability_to_coach'] = ability_to_coach

        # 报告数据计算完成==========================
        report, created = ProjectEvaluationReport.objects.get_or_create(
            project_id=project_id, type=ProjectEvaluationReportTypeEnum.MANAGE_EVALUATION.value)
        report.lbi_analysis = json_data
        report.save()
        get_user_pdf_url(report.pk, f'{project.name}_教练型管理者测评项目报告_{report.id}.pdf',
                         pdf_type=PdfReportTypeEnum.manage_evaluation_report.value)
        if complete_report.count() >= all_report.count():
            # 获取一对一辅导教练user_id列表
            formal_interview_queryset = interview_public.get_formal_interview(
                None, None, project_id, None, None)
            formal_user_ids = list(formal_interview_queryset.values_list('public_attr__user_id', flat=True))

            # 获取集体辅导教练user_id列表
            offline_group_interview_queryset = interview_public.get_offline_group_coach(
                None, None, project_id, None, None)
            offline_group_user_ids = list(offline_group_interview_queryset.values_list('public_attr__user_id', flat=True))

            # 合并去重
            user_ids = list(set(formal_user_ids + offline_group_user_ids))

            wx_user_ids = list(WorkWechatUser.objects.filter(user_id__in=user_ids, deleted=False).values_list('wx_user_id', flat=True))

            if wx_user_ids:
                content_item = [
                    {"key": "测评名称", "value": '教练型管理者测评项目报告'},
                    {"key": "所属企业", "value": project.company.real_name},
                    {"key": "所属项目", "value": project.name},
                ]
                wx_user_ids = '|'.join(wx_user_ids)
                send_work_wechat_coach_notice(
                    wx_user_ids,
                    'manage_evaluation_report',
                    project_name=project.full_name,
                    report_name='教练型管理者测评项目报告',
                    report_id=report.id,
                    project_id=project_id,
                    content_item=content_item
                )

    except SoftTimeLimitExceeded:
        push_celery_hanging_message('generate_manage_project_report')
    except Exception as e:
        LarkMessageCenter().send_other_backend_message(
            'celery任务错误', 'error', {'error': f'generate_manage_project_report:{str(e)}'})


@shared_task(queue='default', ignore_result=True)
def get_coach_interview_data_xlsx(params, key, user):
    try:
        error = None
        coach_interview_queryset, project_interview_queryset, public_courses_coach_queryset = coach_interview_public.get_coach_interview_list(params)
        try:
            file, file_base_name, file_url = coach_interview_public.coach_interview_data_save_excel(
                coach_interview_queryset, project_interview_queryset, public_courses_coach_queryset, user)

            work_wechat_user = WorkWechatUser.objects.filter(
                wx_user_id__isnull=False, user_id=user.id, deleted=False).first()

            if work_wechat_user:
                file_type = 'file'
                msg, media_id = work_wechat.WorkWechat().upload_media_file((file_base_name, file), file_type)
                if not msg:
                    return False, '企业微信发送教练辅导文件失败：未获取到素材id'

                content = {file_type: {'media_id': media_id}}
                file_is_success, file_msg = work_wechat.WorkWechat().send_message(work_wechat_user.wx_user_id, file_type, content)
                sls_data = {
                    'content': content,
                    'user_type': 'coach',
                    'user_id': user.id,
                    'user_name': user.true_name,
                    'file_msg': file_msg,
                    'event_id': 'Send_Coach_WechatMessage',
                    'message': '发送教练辅导数据文件',
                }
                AliyunSlsLogLayout().send_work_wechat_log_data(**sls_data)
                if not file_is_success:
                    return False, '企业微信发送教练辅导文件失败'
            else:
                LarkMessageCenter().send_other_backend_message(
                    '企业微信发送教练辅导文件错误', 'error', {'error': f'教练{user.id}没有绑定企业微信'})

        except Exception as e:
            task_id = data_redis.get(key)
            LarkMessageCenter().send_other_backend_message(
                '企业微信发送教练辅导文件失败', 'error', { 'params': params, 'error': str(e)})
            data_redis.set(task_id.decode(), f'False&{error}', ex=3600)
            return False, '企业微信发送教练辅导文件失败'

        task_id = data_redis.get(key)
        if not task_id:
            LarkMessageCenter().send_other_backend_message(
                '企业微信发送教练辅导文件失败', 'error',
                {'error': f'{key}:异步任务id丢失，请排查redis是否出现故障',
                'name': 'send_interview_detail_list',
                'data': params})
            return False, '异步任务id丢失'
        if error:
            LarkMessageCenter().send_other_backend_message(
                '企业微信发送教练辅导文件失败', 'error', {'params': params, 'error': error})
            data_redis.set(task_id.decode(), f'False&{error}', ex=3600)
            return False, '企业微信发送教练辅导文件失败'
        else:
            data_redis.set(task_id.decode(), f'True&{file_url}', ex=3600)
            return True, '企业微信发送教练辅导文件失败'

    except SoftTimeLimitExceeded:
        push_celery_hanging_message('get_coach_interview_data_xlsx')
    except Exception as e:
        LarkMessageCenter().send_other_backend_message(
            'celery任务错误', 'error', {'error': f'get_coach_interview_data_xlsx:{str(e)}'})


@shared_task(queue='default', ignore_result=True)
def get_business_settlement_order_xlsx(params, key):
    try:

        error = None
        sorted_orders = business_order_public.get_business_settlement_to_order(params)
        try:
            file_url = business_order_public.business_settlement_order_to_xlsx(sorted_orders)
        except Exception as e:
            task_id = data_redis.get(key)
            LarkMessageCenter().send_other_backend_message(
                '结算订单下载失败', 'error', { 'params': params, 'error': str(e)})
            data_redis.set(task_id.decode(), f'False&{error}', ex=3600)
            return False, '结算订单下载失败'

        task_id = data_redis.get(key)
        if not task_id:
            LarkMessageCenter().send_other_backend_message(
                '结算订单下载失败', 'error',
                {'error': f'{key}:异步任务id丢失，请排查redis是否出现故障',
                'name': 'send_interview_detail_list',
                'data': params})
            return False, '异步任务id丢失'
        if error:
            LarkMessageCenter().send_other_backend_message(
                '结算订单下载失败', 'error', {'params': params, 'error': error})
            data_redis.set(task_id.decode(), f'False&{error}', ex=3600)
            return False, '结算订单下载失败'
        else:
            data_redis.set(task_id.decode(), f'True&{file_url}', ex=3600)
            return True, '结算订单下载成功'

    except SoftTimeLimitExceeded:
        push_celery_hanging_message('get_business_settlement_order_xlsx')
    except Exception as e:
        LarkMessageCenter().send_other_backend_message(
            'celery任务错误', 'error', {'error': f'get_business_settlement_order_xlsx:{str(e)}'})


@shared_task(queue='default', ignore_result=True)
def get_project_coach_report(project, key):
    try:
        error = None
        try:
            file_url = project_public.get_project_coach_report_to_xlsx(project)
        except Exception as e:
            task_id = data_redis.get(key)
            LarkMessageCenter().send_other_backend_message(
                '项目教练报告下载失败', 'error', {'params': project.id, 'error': str(e)})
            data_redis.set(task_id.decode(), f'False&{error}', ex=3600)
            return False, '项目教练报告下载失败'

        task_id = data_redis.get(key)
        if not task_id:
            LarkMessageCenter().send_other_backend_message(
                '项目教练报告下载失败', 'error',
                {'error': f'{key}:异步任务id丢失，请排查redis是否出现故障',
                 'name': 'send_interview_detail_list',
                 'data': project.id})
            return False, '异步任务id丢失'
        if error:
            LarkMessageCenter().send_other_backend_message(
                '项目教练报告下载失败', 'error', {'params': project.id, 'error': error})
            data_redis.set(task_id.decode(), f'False&{error}', ex=3600)
            return False, '项目教练报告下载失败'
        else:
            data_redis.set(task_id.decode(), f'True&{file_url}', ex=3600)
            return True, '项目教练报告下载成功'
    except SoftTimeLimitExceeded:
        push_celery_hanging_message('get_project_coach_report')
    except Exception as e:
        LarkMessageCenter().send_other_backend_message(
            'celery任务错误', 'error', {'error': f'get_project_coach_report:{str(e)}'})


@shared_task(queue='default', ignore_result=True)
def send_interview_detail_list(params, key):
    try:

        error = None
        file_url = None
        interview_detail_state, content = interview_public.get_project_interview_details(params)
        if interview_detail_state:
            try:
                file_url = interview_detail_xlsx.get_interview_detail_xlsx(content, params)
            except Exception as e:
                task_id = data_redis.get(key)
                LarkMessageCenter().send_other_backend_message(
                    '辅导明细下载失败', 'error', { 'params': params, 'error': str(e)})
                data_redis.set(task_id.decode(), f'False&{error}', ex=3600)
                return False, '辅导明细下载失败'
        else:
            error = content

        task_id = data_redis.get(key)
        if not task_id:
            LarkMessageCenter().send_other_backend_message(
                '辅导明细下载失败', 'error',
                {'error': f'{key}:异步任务id丢失，请排查redis是否出现故障',
                'name': 'send_interview_detail_list',
                'data': params})
            return False, '异步任务id丢失'
        if error:
            LarkMessageCenter().send_other_backend_message(
                '辅导明细下载失败', 'error', {'params': params, 'error': error})
            data_redis.set(task_id.decode(), f'False&{error}', ex=3600)
            return False, '辅导明细下载失败'
        else:
            data_redis.set(task_id.decode(), f'True&{file_url}', ex=3600)
            return True, '辅导明细下载成功'

    except SoftTimeLimitExceeded:
        push_celery_hanging_message('send_interview_detail_list')
    except Exception as e:
        LarkMessageCenter().send_other_backend_message(
            'celery任务错误', 'error', {'error': f'send_interview_detail_list:{str(e)}'})


@shared_task(queue='default', ignore_result=True, soft_time_limit=120)
def generate_change_observation_report(change_observation_id):
    try:
        change_observation = ChangeObservation.objects.get(pk=change_observation_id, deleted=False)
        json_data = {}
        json_data['created_at'] = datetime.datetime.now().strftime('%Y-%m-%d')

        # 查询该项目下用户一对一辅导的数据
        all_interview_queryset = interview_public.get_formal_interview(
            None, [change_observation.project_member.user_id], change_observation.project_member.project_id, None, None)
        # 通过辅导获取教练的用户信息
        if all_interview_queryset.exists():
            coach_user = all_interview_queryset.last().public_attr.user
        else:
            coach_user = None

        json_data['coach_name'] = coach_user.cover_name if coach_user else ''

        answers = ChangeObservationAnswer.objects.filter(change_observation=change_observation)

        project_interested_ids = list(MultipleAssociationRelation.objects.filter(
            type=MultipleAssociationRelationTypeEnum.stakeholder_change_observation.value, deleted=False,
            main_id=change_observation.pk).values_list('secondary_id', flat=True))
        project_interested = ProjectInterested.objects.filter(id__in=project_interested_ids, deleted=False)
        json_data['stakeholders'] = list(project_interested.values_list('interested__true_name', flat=True))

        # 同事参与情况
        participation = {}
        participation['x_data'] = ["上级", "下级", "平级"]
        participation['y_data'] = [project_interested.filter(relation=1).count(),
                                   project_interested.filter(relation=3).count(),
                                   project_interested.filter(relation=2).count()]
        json_data['participation'] = participation

        # 沟通频率的反馈
        feedback = {}
        feedback['communication_x_data'] = ["经常沟通", "偶尔沟通", "从来没有"]
        communication_y_data = [0, 0, 0]
        answers_content = list(answers.values_list('content', flat=True))
        for content in answers_content:
            if content['change_choice'] == 1:
                communication_y_data[2] += 1
            elif content['change_choice'] == 2:
                communication_y_data[1] += 1
            else:
                communication_y_data[0] += 1
        feedback['communication_y_data'] = communication_y_data
        json_data['feedback'] = feedback

        # 将每个人回答的同一个成长目标内容放到同一个列表中
        tmp_growth_goals = []
        for i in range(len(answers_content[0]['growth_goals'])):
            tmp_growth_goals.append([])
        for content in answers_content:
            for index, growth in enumerate(content['growth_goals']):
                tmp_growth_goals[index].append(growth)

        # 计算
        # tmp_growth_goals [[g1, g1, g1, g1], [g2, g2, g2, g2], [g3, g3, g3,g3], [g4, g4, g4, g4]]
        growth_goal = []
        x_data = [f"相关方{i+1}" for i in range(answers.count())]
        for index, growths in enumerate(tmp_growth_goals):
            g_data = {}
            g_data['content'] = GrowthGoals.objects.get(pk=growths[0]['id']).content
            g_data['title'] = f'目标{index + 1}'
            # growths [g1, g1, g1, g1]
            g_data['change'] = [{'content': f"这段时间你在“{GrowthGoalsChange.objects.get(pk=i['id']).content}”"
                                            f"的行为上的进步：", "x_data": x_data, "y_data": []}
                                for i in growths[0]['change']]
            tmp_is_change = []
            tmp_not_change = []
            for growth in growths:
                for c_index, change in enumerate(growth['change']):
                    g_data['change'][c_index]['y_data'].append(change['choice'])
                for text in growth['first_text']:
                    tmp_is_change.append(text)
                for text_2 in growth['second_text']:
                    tmp_not_change.append(text_2)

            g_data['is_change'] = [f'{t_index+1}、{text}' for t_index, text in enumerate(tmp_is_change)]
            g_data['not_change'] = [f'{t_index+1}、{text}' for t_index, text in enumerate(tmp_not_change)]

            growth_goal.append(g_data)

        # 报告数据计算完成
        json_data['growth_goal'] = growth_goal

        # 没有新建，有则更新
        personal_report = PersonalReport.objects.filter(
            object_id=change_observation.pk,
            user=change_observation.project_member.user
        ).first()
        if not personal_report:
            personal_report = PersonalReport.objects.create(
                name=f'{change_observation.name}总结',
                content=json_data, type=PersonalReportTypeEnum.change_observation_report.value,
                object_id=change_observation.pk, user=change_observation.project_member.user,
                project=change_observation.project_member.project)
        else:
            personal_report.content = json_data
            personal_report.save()

        # 生成pdf
        get_user_pdf_url(
            personal_report.pk, f'{change_observation.project_member.user.cover_name}_{change_observation.name}_{personal_report.id}.pdf',
            pdf_type=PdfReportTypeEnum.change_observation_report.value)

        # 所有人填写完毕，发送消息通知并更新状态。
        if ChangeObservationAnswer.objects.filter(
                change_observation=change_observation).count() == change_observation.max_stakeholders_count:
            change_observation.is_complete = True
            change_observation.save()
            # 通知教练
            project = change_observation.project_member.project
            if coach_user:
                work_wechat_user = WorkWechatUser.objects.filter(
                    wx_user_id__isnull=False,
                    user_id=coach_user.pk,
                    deleted=False
                ).first()
                if work_wechat_user:
                    company_name = project.company.real_name
                    content_item = [
                        {"key": "客户名称", "value": change_observation.project_member.user.cover_name},
                        {"key": "所属企业", "value": company_name},
                        {"key": "所属项目", "value": project.name},
                        {"key": "报告名称", "value": personal_report.name}
                    ]
                    send_work_wechat_coach_notice(
                        work_wechat_user.wx_user_id,
                        'change_observation_report_remind',
                        content_item=content_item,
                        report_id=personal_report.pk,
                        company_name=company_name,
                        user_name=change_observation.project_member.user.cover_name,
                        project_id=project.pk,
                        project_name=project.name,
                        coach_id=work_wechat_user.user_id,
                        coach_name=work_wechat_user.user.cover_name,
                        coachee_id=change_observation.project_member.user_id,
                        coachee_name=change_observation.project_member.user.cover_name
                    )
            # 通知客户
            msg, sender, external_user_id = get_project_manage_wx_user_id(
                project.pk,
                change_observation.project_member.user_id,
                'generate_change_observation_report'
            )
            if not msg:
                return
            text = f'您的改变观察总结已生成，点击下方链接查看'
            title = f"改变观察总结生成提醒"
            page = f"/pages_change/change_report/change_report?report_id={personal_report.pk}&refer=2"
            sender_user = WorkWechatUser.objects.filter(
                wx_user_id=sender,
                user__isnull=False,
                deleted=False
            ).first()
            if sender_user:
                send_work_wechat_coachee_notice(
                    sender, text, external_user_id, page, title,
                    content_type='add_msg_template',
                    project_id=project.id,
                    project_name=project.name,
                    coachee_id=change_observation.project_member.user_id,
                    coachee_name=change_observation.project_member.user.cover_name,
                    forward_by=sender_user.user.cover_name,
                    forward_by_id=sender_user.user.id,
                )

    except SoftTimeLimitExceeded:
        push_celery_hanging_message('generate_change_observation_report')
    except Exception as e:
        LarkMessageCenter().send_other_backend_message(
            'celery任务错误', 'error', {'error': f'generate_change_observation_report:{str(e)}'})

@shared_task(queue='default', ignore_result=True)
def update_change_observation_personal_report():
    """
    改变观察反馈报告定时生成
    """
    try:
        # 达到截止时间的，配置了最大邀请人人数的改变观察反馈
        change_observation_all = ChangeObservation.objects.filter(
            stakeholders_write_end_date=datetime.datetime.now().date() - datetime.timedelta(days=1),
            deleted=False, max_stakeholders_count__isnull=False).all()
        for item in change_observation_all:
            # 如果没有有报告就看一下需不需要生成（截止日期之前有报告=所有人都填写）
            if not PersonalReport.objects.filter(
                type=PersonalReportTypeEnum.change_observation_report.value,
                object_id=str(item.pk),
                user=item.project_member.user, deleted=False).exists():

                # 看一下填写人数，如果没有则不用生成报告，都需要触发飞书通知
                answer_count = ChangeObservationAnswer.objects.filter(
                    change_observation_id=str(item.pk)).count()
                if answer_count != 0:
                    generate_change_observation_report(item.id)
                # 发送通知
                params = {
                    'project_name': item.project_member.project.name,
                    'coachee_name': item.project_member.user.cover_name,
                    'total_count': item.max_stakeholders_count,
                    'completed_count': answer_count,
                }
                send_lark_business_message.delay(LarkMessageTypeEnum.change_observation_personal_report.value, params)
        return

    except SoftTimeLimitExceeded:
        push_celery_hanging_message('update_change_observation_personal_report')
    except Exception as e:
        LarkMessageCenter().send_other_backend_message(
            'celery任务错误', 'error', {'error': f'update_change_observation_personal_report:{str(e)}'})


@shared_task(queue='default', ignore_result=True, soft_time_limit=120)
def send_change_observation_write_message_of_9():
    try:
        # 只有利益相关者类型触发填写通知
        for change_observation in ChangeObservation.objects.filter(
                is_send=False, deleted=False, invite_type=ChangeObservationInviteTypeEnum.stakeholders.value):
            interview_time = interview_public.get_user_interview_time(change_observation.project_member)
            if interview_time >= change_observation.write_condition:
                change_observation_public.send_change_observation_stakeholder_notice(change_observation)
    except SoftTimeLimitExceeded:
        push_celery_hanging_message('send_change_observation_write_message_of_9')
    except Exception as e:
        LarkMessageCenter().send_other_backend_message(
            'celery任务错误', 'error', {'error': f'send_change_observation_write_message_of_9:{str(e)}'})


@shared_task(queue='default', ignore_result=True, soft_time_limit=120)
def send_stakeholder_coach_task_write_message_of_9():
    try:
        for coach_task in CoachTask.objects.filter(
                type=NewCoachTaskTypeEnum.stakeholder_research,
                public_attr__user_id__isnull=False,
                stakeholder_submit_time__isnull=True,
                deleted=False):
            interview_time = interview_public.get_user_interview_time(coach_task.project_bundle.project_member)
            if interview_time >= coach_task.hours:
                query_data = MultipleAssociationRelation.objects.filter(
                    main_id=coach_task.id,
                    deleted=False,
                    public_attr__isnull=True,
                    type=MultipleAssociationRelationTypeEnum.stakeholder_coach_task,
                ).exclude(Q(is_send_sms_notice=True) | Q(is_send_email_notice=True)).values_list('secondary_id', flat=True)
                if query_data:
                    project_interested_objs = list(ProjectInterested.objects.filter(
                        id__in=query_data, deleted=False).all())
                    utils.send_stakeholder_coach_task_sms_notice(project_interested_objs, coach_task)
                    utils.send_stakeholder_coach_task_email_notice(project_interested_objs, coach_task)

    except SoftTimeLimitExceeded:
        push_celery_hanging_message('send_stakeholder_coach_task_write_message_of_9')
    except Exception as e:
        LarkMessageCenter().send_other_backend_message(
            'celery任务错误', 'error', {'error': f'send_stakeholder_coach_task_write_message_of_9:{str(e)}'})


@shared_task(queue='default', ignore_result=True)
def send_user_notice(template_type, channel_type, data_ids, key, sender_id=None):
    try:

        # template_type 发送模板类型 1-账号开通通知 2-改变观察问卷 3-利益相关者调研问卷
        # channel_type 发送渠道 1-企业微信 2-短信 3-邮件
        errmsg = ['企业微信', '短信', '邮件']

        err = []
        # 账号开通通知
        if template_type == NoticeTemplateTypeEnum.create_account:
            # 邮件通知
            if channel_type == constant.ADMIN_SEND_EMAIL:
                for item in data_ids:
                    project_member = ProjectMember.objects.get(id=item)
                    msg = utils.send_project_user_add_notice(project_member, sender_id=sender_id)
                    if msg:
                        err.append(msg)
        # 企业管理员账号开通通知
        elif template_type == NoticeTemplateTypeEnum.company_manage_create_account:
            if channel_type == constant.ADMIN_SEND_EMAIL:
                for item in data_ids:
                    user_backend = UserBackend.objects.get(id=item)
                    msg = utils.send_company_manage_add_notice(user_backend, sender_id=sender_id)
                    if msg:
                        err.append(msg)
        elif template_type == NoticeTemplateTypeEnum.customer_infomation_collection.value:
            if channel_type == constant.ADMIN_SEND_EMAIL:
                for item in data_ids:
                    is_success, msg = project_public.send_customer_info_collection_email(item, key, sender_id=sender_id)
                    if not is_success:
                        err.append(msg)
        # 化学面谈日程设置提醒
        elif template_type == NoticeTemplateTypeEnum.chemical_interview_schedule:
            for item in data_ids:
                project_coach = ProjectCoach.objects.get(pk=item)
                work_wechat_user = WorkWechatUser.objects.filter(user=project_coach.coach.user, deleted=False).first()
                if not work_wechat_user:
                    err.append(project_coach.coach.user.cover_name)
                    continue
                chemical_interview_date = ''
                # 化学面谈起止时间获取项目下第一个配置的化学面谈客户的起止时间，没有时为空
                chemical_interview_module = ChemicalInterviewModule.objects.filter(
                    project_member__project=project_coach.project, deleted=False).first()
                if chemical_interview_module:
                    chemical_interview_date = f"{chemical_interview_module.start_time.strftime('%Y.%m.%d')}-" \
                                              f"{chemical_interview_module.end_time.strftime('%Y.%m.%d')}"

                chemical_interview_time = (datetime.datetime.now() + datetime.timedelta(days=1)).strftime('%Y.%m.%d %H:%M')
                content_is_success, app_is_success = send_work_wechat_coach_notice(
                    work_wechat_user.wx_user_id, 'coach_chemical_interview_schedule',
                    coach_id=work_wechat_user.user.id, project_name=project_coach.project.full_name,
                    chemical_interview_date=chemical_interview_date, chemical_interview_time=chemical_interview_time)
                if content_is_success and app_is_success:
                    UserNoticeRecord.objects.create(project=project_coach.project, user=project_coach.coach.user,
                                                    type=NoticeTemplateTypeEnum.chemical_interview_schedule,
                                                    channel=constant.ADMIN_SEND_WORK_WECHAT)
                else:
                    err.append(project_coach.coach.user.cover_name)
        # 化学面谈预约提醒 企微+邮件
        elif template_type == NoticeTemplateTypeEnum.chemical_interview_appointment:
            for item in data_ids:
                project_member = ProjectMember.objects.get(pk=item)
                chemical_interview_module = project_member.chemical_interview.filter(deleted=False).first()
                if not chemical_interview_module:
                    err.append(project_member.user.cover_name)
                    continue
                interview_count = chemical_interview_module.max_interview_number
                if chemical_interview_module.coach_source == ChemicalInterviewCoachSourceEnum.system_random:
                    coach_count = chemical_interview_module.coaches.filter(deleted=False, coach__isnull=False).count()
                else:
                    coach_count = ProjectCoach.objects.filter(project=project_member.project, deleted=False,
                                                        coach__deleted=False, resume__isnull=False).count()
                duration = chemical_interview_module.duration
                if channel_type == constant.ADMIN_SEND_EMAIL:
                    # 生成短链接
                    url_state, app_short_link = WeChatMiniProgram().get_url_link(
                        'pages_account/login/login',f'refer=2')

                    message_type = 'chemical_interview_appointment'
                    manage_list = project_member.project.manager_list

                    short_link = f'https://static.qzcoach.com/app/miniapp_qrcode.html?qrcode=https://static.qzcoach.com/app/b_login_qrcode.jpg&shortlink={app_short_link}'
                    params = {
                        "true_name": project_member.user.cover_name,
                        "count": coach_count,
                        "account": project_member.user.email,
                        "password": aesdecrypt(project_member.user.password),
                        "manager_name": manage_list[0]['true_name'] if manage_list else '',
                        "manager_email": manage_list[0]['email'] if manage_list else '',
                        "manager_phone": manage_list[0]['phone'] if manage_list else '',
                        "short_link": short_link,
                        "interview_count": interview_count,
                        "duration": duration
                    }
                    email = [project_member.user.email]
                    state = message_send_email_base(message_type=message_type, params=params, to_email=email, 
                                                    project_id=project_member.project_id, receiver_ids=project_member.user_id, 
                                                    sender_id=sender_id)
                    if state:
                        UserNoticeRecord.objects.create(project=project_member.project, user=project_member.user,
                                                        type=NoticeTemplateTypeEnum.chemical_interview_appointment,
                                                        channel=constant.ADMIN_SEND_EMAIL)
                    else:
                        err.append(project_member.user.cover_name)
                elif channel_type == constant.ADMIN_SEND_WORK_WECHAT:
                    msg, sender, external_user_id = get_project_manage_wx_user_id(
                        project_member.project.pk,
                        project_member.user_id,
                        'send_user_notice'
                    )
                    if not msg:
                        err.append(project_member.user.cover_name)
                        continue
                    sender_user = WorkWechatUser.objects.filter(
                        wx_user_id=sender,
                        user__isnull=False,
                        deleted=False
                    ).first()
                    if sender_user:
                        text = f'我们为您推荐了{count}位匹配度较高的教练，请优先选择一位您最感兴趣的教练进行化学面谈吧！' \
                               f'如果您还未登录过小程序，请移步至您的企业邮箱查收账号密码。'
                        page = "pages_account/login/login?refer=2"   # 跳转首页
                        title = "预约化学面谈"

                        state, msg = send_work_wechat_coachee_notice(
                            sender, text, external_user_id, page, title,
                            content_type='add_msg_template',
                            project_id=project_member.project.id,
                            project_name=project_member.project.name,
                            coachee_id=project_member.user_id,
                            coachee_name=project_member.user.cover_name,
                            forward_by=sender_user.user.cover_name,
                            forward_by_id=sender_user.user.id,
                        )
                        if not state:
                            err.append(project_member.user.cover_name)
                        else:
                            UserNoticeRecord.objects.create(project=project_member.project, user=project_member.user,
                                                            type=NoticeTemplateTypeEnum.chemical_interview_appointment,
                                                            channel=constant.ADMIN_SEND_WORK_WECHAT)
        # 化学面谈反馈提醒 企微 + 邮件
        elif template_type == NoticeTemplateTypeEnum.chemical_interview_feedback:
            for item in data_ids:
                project_member = ProjectMember.objects.get(pk=item)
                chemical_interview_module = project_member.chemical_interview.filter(deleted=False).first()
                if not chemical_interview_module:
                    err.append(project_member.user.cover_name)
                    continue
                chemical_interview = chemical_interview_module.coaches.filter(
                    deleted=False, interview__public_attr__end_time__lte=datetime.datetime.now(),
                    chemical_interview_status=ChemicalInterviewStatusEnum.not_feedback)
                if not chemical_interview.exists():
                    err.append(f"{project_member.user.cover_name}(没有可发送的面谈)")
                    err.append(project_member.user.cover_name)
                    continue
                if channel_type == constant.ADMIN_SEND_EMAIL:
                    # 生成短链接
                    url_state, short_link = WeChatMiniProgram().get_url_link(
                        'pages_interview/feedback_interview/feedback_interview',
                        f'interview_id={chemical_interview.first().interview_id}&refer=2')
                    message_type = 'chemical_interview_feedback'
                    manage_list = project_member.project.manager_list
                    state, content = WeChatMiniProgram().get_miniapp_qrcode(
                        scene=f"interview_id={chemical_interview.first().interview_id}&refer=2",
                        page='pages_interview/feedback_interview/feedback_interview')
                    if not state:
                        err.append(project_member.user.cover_name)
                        continue
                    url = get_base_qr_code(content, 'coach_resume')

                    params = {
                        "true_name": project_member.user.cover_name,
                        "coach_name": chemical_interview.first().interview.public_attr.user.cover_name,
                        "manager_name": manage_list[0]['true_name'] if manage_list else '',
                        "manager_email": manage_list[0]['email'] if manage_list else '',
                        "manager_phone": manage_list[0]['phone'] if manage_list else '',
                        "qr_code_url": url,
                        "short_link": short_link
                    }
                    email = [project_member.user.email]
                    state = message_send_email_base(message_type=message_type, params=params, to_email=email, 
                                                    project_id=project_member.project_id, receiver_ids=project_member.user_id, 
                                                    sender_id=sender_id)
                    if state:
                        UserNoticeRecord.objects.create(project=project_member.project, user=project_member.user,
                                                        type=NoticeTemplateTypeEnum.chemical_interview_feedback,
                                                        channel=constant.ADMIN_SEND_EMAIL)
                    else:
                        err.append(project_member.user.cover_name)
                elif channel_type == constant.ADMIN_SEND_WORK_WECHAT:
                    msg, sender, external_user_id = get_project_manage_wx_user_id(
                        project_member.project.pk,
                        project_member.user_id,
                        'send_user_notice'
                    )
                    if not msg:
                        err.append(project_member.user.cover_name)
                        continue
                    sender_user = WorkWechatUser.objects.filter(
                        wx_user_id=sender,
                        user__isnull=False,
                        deleted=False
                    ).first()
                    if sender_user:

                        text = f"您和{chemical_interview.first().public_attr.user.cover_name}教练的化学面谈已结束，" \
                               f"请点击下方小程序链接反馈您是否选择该教练吧"

                        page = f"pages_interview/feedback_interview/feedback_interview?" \
                               f"interview_id={chemical_interview.first().interview_id}&refer=2"   # 访谈反馈结果界面
                        title = "访谈结果反馈"

                        state, msg = send_work_wechat_coachee_notice(
                            sender, text, external_user_id, page, title,
                            content_type='add_msg_template',
                            project_id=project_member.project.id,
                            project_name=project_member.project.name,
                            coachee_id=project_member.user_id,
                            coachee_name=project_member.user.cover_name,
                            forward_by=sender_user.user.cover_name,
                            forward_by_id=sender_user.user.id,
                        )
                        if not state:
                            err.append(project_member.user.cover_name)
                        else:
                            UserNoticeRecord.objects.create(project=project_member.project, user=project_member.user,
                                                            type=NoticeTemplateTypeEnum.chemical_interview_feedback,
                                                            channel=constant.ADMIN_SEND_WORK_WECHAT)

        # 改变观察问卷
        elif template_type == NoticeTemplateTypeEnum.change_observation:

            raw_data = {}
            interested = ProjectInterested.objects.filter(pk__in=data_ids)
            for item in interested:
                # 通过关系表获取所有改变观察反馈
                query_data = MultipleAssociationRelation.objects.filter(
                    secondary_id=item.id,
                    deleted=False,
                    type=MultipleAssociationRelationTypeEnum.stakeholder_change_observation,
                ).values_list('main_id', flat=True)
                if not query_data:
                    err.append(item.interested.cover_name)
                    continue
                # 未完成的发送通知
                query_change_observation = ChangeObservation.objects.filter(
                    pk__in=query_data,
                    deleted=False,
                    is_complete=False
                )
                if query_change_observation.exists():
                    for change_observation in query_change_observation.all():
                        if raw_data.get(item.interested.id):
                            raw_data[item.interested.id]['change_observation'].append(change_observation)
                            raw_data[item.interested.id]['interested'].append(item)
                        else:
                            raw_data[item.interested.id] = {
                                'change_observation': [change_observation],
                                'interested': [item]
                            }
                else:
                    err.append(item.interested.cover_name)

            for k, v in raw_data.items():
                interested = v.get('interested')
                change_observation = v.get('change_observation')
                # 短信通知
                if channel_type == constant.ADMIN_SEND_SMS:
                    msg = change_observation_public.send_change_observation_sms_notice(
                        [interested[0]], change_observation[0],
                        all_change_observation=change_observation,
                        all_interested=interested
                    )
                # 邮件通知
                elif channel_type == constant.ADMIN_SEND_EMAIL:
                    msg = change_observation_public.send_change_observation_email_notice(
                        [interested[0]], change_observation[0],
                        all_change_observation=change_observation,
                        all_interested=interested,
                        sender_id=sender_id
                    )
                else:
                    msg = None
                if msg:
                    err.append(*msg)

        # 利益相关者调研
        elif template_type == NoticeTemplateTypeEnum.stakeholder:

            raw_data = {}
            interested = ProjectInterested.objects.filter(pk__in=data_ids)
            for item in interested:
                # 通过关系表获取所有利益相关者调研
                query_data = MultipleAssociationRelation.objects.filter(
                    secondary_id=item.id,
                    deleted=False,
                    public_attr__isnull=True,
                    type=MultipleAssociationRelationTypeEnum.stakeholder_coach_task,
                ).values_list('main_id', flat=True)
                if not query_data:
                    err.append(item.interested.cover_name)
                    continue

                query_coach_task = CoachTask.objects.filter(
                    pk__in=query_data,
                    public_attr__user_id__isnull=False,
                    deleted=False,
                    stakeholder_submit_time__isnull=True
                )
                if query_coach_task.exists():
                    for coach_task in query_coach_task.all():
                        if raw_data.get(item.interested.id):
                            raw_data[item.interested.id]['coach_task'].append(coach_task)
                            raw_data[item.interested.id]['interested'].append(item)
                        else:
                            raw_data[item.interested.id] = {
                                'coach_task': [coach_task],
                                'interested': [item]
                            }
                else:
                    err.append(item.interested.cover_name)
            for k, v in raw_data.items():
                interested = v.get('interested')
                coach_task = v.get('coach_task')
                # 短信通知
                if channel_type == constant.ADMIN_SEND_SMS:
                    msg = utils.send_stakeholder_coach_task_sms_notice(
                        [interested[0]], coach_task[0], all_coach_task=coach_task, all_interested=interested)
                # 邮件通知
                elif channel_type == constant.ADMIN_SEND_EMAIL:
                    msg = utils.send_stakeholder_coach_task_email_notice(
                        [interested[0]], coach_task[0], all_coach_task=coach_task, all_interested=interested, sender_id=sender_id)
                else:
                    msg = None
                if msg:
                    err.append(*msg)

        # 利益相关者访谈预约提醒
        elif template_type == NoticeTemplateTypeEnum.stakeholder_interview_reservation:
            data_ids = [uuid.UUID(item).hex for item in data_ids]

            # 查询利益相关者关联表
            stakeholder_interview = StakeholderInterview.objects.filter(id__in=data_ids, deleted=False)

            for item in stakeholder_interview.all():

                project_member = item.stakeholder_interview_module.project_member

                path = 'pages_stakeholder/add_stakeholder_interview/add_stakeholder_interview'
                scene = f'id={item.id}&refer=2'
                send_notice_state = False
                if channel_type == constant.ADMIN_SEND_SMS and item.project_interested.interested.phone:
                    if item.project_interested.interested.phone:
                        sms_scene = f'id={item.id}&invite_code={settings.SMS_INVITE_CODE}&refer=3'
                        state, url = WeChatMiniProgram().get_url_link('pages/landing/landing', f'p=/{path}&{sms_scene}')
                        if state:
                            msg = SendSMS().send_stakeholder_interview_reservation(
                                project_member.user.cover_name,
                                item.project_interested.interested.phone,
                                url
                            )
                            if msg.get('status') == 'success':
                                send_notice_state = True

                elif channel_type == constant.ADMIN_SEND_EMAIL and item.project_interested.interested.email:
                    manage_list = project_member.project.manager_list
                    url = get_qr_code(f'{settings.SITE_URL}landing/?p=/{path}&{scene}')
                    # 生成短链接
                    url_state, short_link = WeChatMiniProgram().get_url_link(path, scene)

                    message_type = 'stakeholder_interview_reservation'
                    params = {
                        'stakeholder_name': item.project_interested.interested.cover_name,
                        'coachee_name': item.project_interested.master.cover_name,
                        "manager_name": manage_list[0]['true_name'] if manage_list else '',
                        "manager_email": manage_list[0]['email'] if manage_list else '',
                        "manager_phone": manage_list[0]['phone'] if manage_list else '',
                        "qr_code_url": url,
                        "short_link": short_link
                    }
                    to_email = [item.project_interested.interested.email]
                    state = message_send_email_base(message_type, params, to_email, 
                                                    project_id=project_member.project_id, receiver_ids=item.project_interested.interested_id, 
                                                    sender_id=sender_id)
                    if state:
                        send_notice_state = True

                if send_notice_state:
                    UserNoticeRecord.objects.create(
                        project=project_member.project, user=item.project_interested.interested,
                        type=template_type, channel=channel_type)
                else:
                    err.append(item.project_interested.interested.cover_name)

        # 客户邀请利益相关者预约访谈提醒
        elif template_type == NoticeTemplateTypeEnum.coachee_invite_stakeholder_interview_reservation:
            # 查询利益相关者配置表
            data_ids = [uuid.UUID(item).hex for item in data_ids]
            stakeholder_interview = StakeholderInterviewModule.objects.filter(id__in=data_ids, deleted=False)

            for item in stakeholder_interview.all():
                page = 'pages_stakeholder/inviting_interviews/inviting_interviews'
                scene = f'id={item.pk}&refer=2'
                send_notice_state = False
                if channel_type == constant.ADMIN_SEND_EMAIL and item.project_member.user.email:
                    # 生成短链接
                    url_state, short_link = WeChatMiniProgram().get_url_link(page, scene)

                    manage_list = item.project_member.project.manager_list
                    url = get_qr_code(f'{settings.SITE_URL}landing/?p=/{page}&{scene}')
                    message_type = 'coachee_invite_stakeholder_interview_reservation'
                    params = {
                        'coachee_name': item.project_member.user.cover_name,
                        'interview_number': item.stakeholder_interview_number,
                        'duration': int(item.duration * 60) if item.duration else '',
                        'datetime': pendulum.now().add(days=1).strftime("%Y.%m.%d %H:%M"),
                        'password': aesdecrypt(item.project_member.user.password),
                        'user_name': item.project_member.user.email,  # 登录名使用邮箱
                        "manager_name": manage_list[0]['true_name'] if manage_list else '',
                        "manager_email": manage_list[0]['email'] if manage_list else '',
                        "manager_phone": manage_list[0]['phone'] if manage_list else '',
                        "qr_code_url": url,
                        "short_link": short_link
                    }
                    to_email = [item.project_member.user.email]
                    state = message_send_email_base(message_type, params, to_email, project_id=item.project_member.project_id,
                                                   receiver_ids=item.project_member.user_id, sender_id=sender_id)
                    if state:
                        send_notice_state = True
                elif channel_type == constant.ADMIN_SEND_WORK_WECHAT:
                    msg, sender, external_user_id = get_project_manage_wx_user_id(
                        item.project_member.project.pk, item.project_member.user_id, 'coachee_invite_stakeholder_interview_reservation'
                    )
                    if msg:
                        sender_user = WorkWechatUser.objects.filter(
                            wx_user_id=sender, user__isnull=False, deleted=False).first()

                        text = f"为了让您在后续的教练过程中获得良好的学习体验，" \
                               f"请您邀请{item.stakeholder_interview_number}位您的利益相关方（您的上下平级同事）参与教练访谈，以便教练更好的了解您。"

                        page = f"{page}?{scene}"
                        title = "邀请利益相关者预约访谈"

                        state, msg = send_work_wechat_coachee_notice(
                            sender, text, external_user_id, page, title,
                            project_id=item.project_member.project.id,
                            project_name=item.project_member.project.name,
                            coachee_id=item.project_member.user_id,
                            coachee_name=item.project_member.user.cover_name,
                            forward_by=sender_user.user.cover_name,
                            forward_by_id=sender_user.user.id,
                            file_name='stakeholder_share.png'
                        )
                        if state:
                            send_notice_state = True

                if send_notice_state:
                    UserNoticeRecord.objects.create(
                        project=item.project_member.project, user=item.project_member.user,
                        type=template_type, channel=channel_type)
                else:
                    err.append(item.project_member.user.cover_name)

        # 利益相关者访谈可预约日程设置提醒
        elif template_type == NoticeTemplateTypeEnum.stakeholder_interview_schedule:
            data_ids = [uuid.UUID(item).hex for item in data_ids]
            stakeholder_interview = StakeholderInterviewModule.objects.filter(
                id__in=data_ids, deleted=False, coach_task__coachee_submit_time__isnull=True)
            for item in stakeholder_interview.all():

                send_notice_state = False

                if channel_type == constant.ADMIN_SEND_WORK_WECHAT:
                    coach_work_wechat_user = WorkWechatUser.objects.filter(
                        user=item.coach_task.public_attr.user,
                        wx_user_id__isnull=False,
                        deleted=False,
                    ).first()
                    if coach_work_wechat_user:
                        content_is_success, app_is_success = send_work_wechat_coach_notice(
                            coach_work_wechat_user.wx_user_id,
                            'stakeholder_interview_schedule',
                            project_name=item.project_member.project.full_name,
                            end_time=pendulum.now().add(days=1).strftime('%Y.%m.%d %H:%M'),
                            interview_date=f'{item.start_date.strftime("%Y.%m.%d")}-{item.end_date.strftime("%Y.%m.%d")}',
                            coachee_id=item.coach_task.public_attr.target_user_id,
                            coachee_name=item.coach_task.public_attr.target_user.cover_name,
                            coach_name=item.coach_task.public_attr.user.cover_name,
                            coach_id=item.coach_task.public_attr.user.id,
                        )
                        if all([content_is_success, app_is_success]):
                            send_notice_state = True
                if send_notice_state:
                    UserNoticeRecord.objects.create(
                        project=item.project_member.project, user=item.coach_task.public_attr.user,
                        type=template_type, channel=channel_type)
                else:
                    err.append(item.coach_task.public_attr.user.cover_name)

        # 利益相关者访谈报告填写提醒
        elif template_type == NoticeTemplateTypeEnum.stakeholder_interview_fill_in_report:
            data_ids = [uuid.UUID(item).hex for item in data_ids]
            # 查询利益相关者配置表
            stakeholder_interview = StakeholderInterviewModule.objects.filter(
                id__in=data_ids, deleted=False, coach_task__coachee_submit_time__isnull=True)

            for item in stakeholder_interview.all():
                send_notice_state = False

                if channel_type == constant.ADMIN_SEND_WORK_WECHAT:
                    coach_work_wechat_user = WorkWechatUser.objects.filter(
                        user=item.coach_task.public_attr.user,
                        wx_user_id__isnull=False,
                        deleted=False,
                    ).first()
                    if coach_work_wechat_user:
                        content_item = [
                            {"key": "客户名称", "value": item.coach_task.public_attr.target_user.cover_name},
                            {"key": "所属企业", "value": item.coach_task.public_attr.project.company.real_name},
                            {"key": "所属项目", "value": item.coach_task.public_attr.project.name},
                        ]
                        content_is_success, app_is_success = send_work_wechat_coach_notice(
                            coach_work_wechat_user.wx_user_id,
                            'stakeholder_interview_record_fill_notice',
                            content_item=content_item,
                            project_name=item.project_member.project.full_name,
                            coach_task_id=item.coach_task_id,
                            coachee_id=item.coach_task.public_attr.target_user_id,
                            coachee_name=item.coach_task.public_attr.target_user.cover_name,
                            coach_name=item.coach_task.public_attr.user.cover_name,
                            coach_id=item.coach_task.public_attr.user.id,
                        )
                        if all([content_is_success, app_is_success]):
                            send_notice_state = True
                if send_notice_state:
                    UserNoticeRecord.objects.create(
                        project=item.project_member.project, user=item.coach_task.public_attr.user,
                        type=template_type, channel=channel_type)
                else:
                    err.append(item.coach_task.public_attr.user.cover_name)

        # 改变观察反馈客户邀请利益相关者
        elif template_type == NoticeTemplateTypeEnum.change_observation_customer.value:
            change_observation = ChangeObservation.objects.filter(id__in=data_ids, deleted=False)
            for item in change_observation.all():

                # 如果没有配置需要的利益相关者数量就跳过并给出错误提示
                if not item.max_stakeholders_count:
                    err.append(item.project_member.user.cover_name)
                    continue

                page = 'pages_change/change_observation/inviting_change_observation'
                scene = f'id={item.pk}&refer=2'
                send_notice_state = False

                coachee_name = item.project_member.user.cover_name
                project_name = item.project_member.project.name
                write_end_date = item.stakeholders_write_end_date.strftime('%Y.%m.%d %H:%M') if item.stakeholders_write_end_date else ''
                invite_end_time = item.invite_end_time.strftime('%Y.%m.%d %H:%M') if item.invite_end_time else ''

                if channel_type == constant.ADMIN_SEND_EMAIL and item.project_member.user.email:
                    # 生成短链接
                    url_state, short_link = WeChatMiniProgram().get_url_link(page, scene)

                    manage_list = item.project_member.project.manager_list
                    url = get_qr_code(f'{settings.SITE_URL}landing/?p=/{page}&{scene}')
                    message_type = 'change_observation_customer'
                    params = {
                        'max_stakeholders_count': item.max_stakeholders_count,
                        'coachee_name': coachee_name,
                        'project_name': project_name,
                        'write_end_date': write_end_date,
                        'invite_end_time': invite_end_time,
                        "manager_name": manage_list[0]['true_name'] if manage_list else '',
                        "manager_email": manage_list[0]['email'] if manage_list else '',
                        "qr_code_url": url,
                        "short_link": short_link
                    }
                    to_email = [item.project_member.user.email]
                    state = message_send_email_base(message_type, params, to_email, project_id=item.project_member.project_id,
                                                   receiver_ids=item.project_member.user_id, sender_id=sender_id)
                    if state:
                        send_notice_state = True
                elif channel_type == constant.ADMIN_SEND_WORK_WECHAT:
                    msg, sender, external_user_id = get_project_manage_wx_user_id(
                        item.project_member.project.pk, item.project_member.user_id,
                        'change_observation_customer'
                    )
                    if msg:
                        sender_user = WorkWechatUser.objects.filter(
                            wx_user_id=sender, user__isnull=False, deleted=False).first()
                        text = (f"{project_name}改变观察反馈正在进行中，请您在"
                                f"{invite_end_time}前邀请您的利益相关者对这段时间您的行为改变进行反馈调研")
                        page = f"{page}?{scene}"
                        title = "改变观察反馈开始提醒"
                        state, msg = send_work_wechat_coachee_notice(
                            sender, text, external_user_id, page, title,
                            project_id=item.project_member.project.id,
                            project_name=item.project_member.project.name,
                            coachee_id=item.project_member.user_id,
                            coachee_name=item.project_member.user.cover_name,
                            forward_by=sender_user.user.cover_name,
                            forward_by_id=sender_user.user.id
                        )
                        if state:
                            send_notice_state = True
                if send_notice_state:
                    UserNoticeRecord.objects.create(
                        project=item.project_member.project, user=item.project_member.user,
                        type=template_type, channel=channel_type)
                else:
                    err.append(item.project_member.user.cover_name)


        task_id = data_redis.get(key)
        if not task_id:
            api_action_logging.info({
                'error': f'',
                'name': 'send_user_notice'})
            push_wx_error_message(
                name=f'发送用户通知',
                level='warning', content={
                    'error': f'{key}:异步任务id丢失，请排查redis是否出现故障',
                    'details': f'给{"、".join(data_ids)}的{NoticeTemplateTypeEnum.get_display(template_type)}{errmsg[channel_type - 1]}发送失败'
                })
            return False, '异步任务id丢失'
        if err:
            error = f'给{"、".join(err)}的{errmsg[channel_type - 1]}发送失败'
            data_redis.set(task_id.decode(), f'False&{error}', ex=3600)
            return False, error
        else:
            data_redis.set(task_id.decode(), f'True&发送成功', ex=3600)
            return True, '发送成功'
    except SoftTimeLimitExceeded:
        push_celery_hanging_message('send_user_notice')
    except Exception as e:
        LarkMessageCenter().send_other_backend_message(
            'celery任务错误', 'error', {'error': f'send_user_notice:{str(e)}'})


@shared_task(queue='default', ignore_result=True)
def refresh_coach_list_data(coaches, sort):
    try:
        if sort in ['project_count', 'project_member_count', 'interview_record_count', 'target_progress_score',
                    'harvest_score', 'satisfaction_score', 'recommend_score']:
            if sort == 'project_count':
                tmp_coaches = [[CoachSerializers().get_project_count(tmp_coach), tmp_coach] for tmp_coach in coaches]
            elif sort == 'project_member_count':
                tmp_coaches = [[CoachSerializers().get_project_member_count(tmp_coach), tmp_coach] for tmp_coach in coaches]
            elif sort == 'interview_record_count':
                tmp_coaches = [[CoachSerializers().get_interview_record_count(tmp_coach), tmp_coach] for tmp_coach in coaches]
            elif sort == 'target_progress_score':
                tmp_coaches = [[CoachSerializers().get_target_progress_score(tmp_coach), tmp_coach] for tmp_coach in coaches]
            elif sort == 'harvest_score':
                tmp_coaches = [[CoachSerializers().get_harvest_score(tmp_coach), tmp_coach] for tmp_coach in coaches]
            elif sort == 'satisfaction_score':
                tmp_coaches = [[CoachSerializers().get_satisfaction_score(tmp_coach), tmp_coach] for tmp_coach in coaches]
            elif sort == 'recommend_score':
                tmp_coaches = [[CoachSerializers().get_recommend_score(tmp_coach), tmp_coach] for tmp_coach in coaches]
            else:
                tmp_coaches = None
            cache.set(f'coach_list_{sort}', tmp_coaches, 300)

    except SoftTimeLimitExceeded:
        push_celery_hanging_message('refresh_coach_list_data')
    except Exception as e:
        LarkMessageCenter().send_other_backend_message(
            'celery任务错误', 'error', {'error': f'refresh_coach_list_data:{str(e)}'})


@shared_task(queue='default', ignore_result=True)
def send_coach_resume_notice(resume_ids, project, manager_user_ids, user_id, key, sender_id=None):

    try:
        # 创建发送通知记录
        with transaction.atomic():
            obj = UserNoticeRecord.objects.create(
                project=project,
                user_id=user_id,
                type=NoticeTemplateTypeEnum.coach_resume,
                channel=NoticeChannelTypeEnum.email,
                content={
                    "resume_id": resume_ids,
                    "manager_user_ids": manager_user_ids,
                    "project_id": project.id,
                },
            )
        resume_url = f'{settings.SITE_URL.replace("www", "admin")}#/resume?id={obj.uuid}'
        params = {
            'company_name': project.company.real_name,
            'project_name': project.name,
            'coach_count': len(resume_ids),
            'resume_url': resume_url,
        }
        user_backend = UserBackend.objects.filter(project=project, deleted=False, role__name='客户顾问').first()
        copy_email = [user_backend.user.email] if user_backend else None
        user = User.objects.filter(id__in=manager_user_ids, deleted=False).all()
        err = []
        for item in user:
            state = message_send_email_base('send_coach_resume', params, [item.email], copy_email=copy_email, 
                                           project_id=project.id, receiver_ids=item.id, sender_id=sender_id)

            if not state:
                push_wx_error_message(
                    name=f'发送用户通知',
                    level='warning', content={
                        'error': 'send_coach_resume_notice', 'data': params,
                        'details': f'教练简历发送失败，收件人：{item.email}，抄送人：{user_backend.user.email}',
                    })
                err.append(item.true_name)
        task_id = data_redis.get(key)
        if not task_id:
            push_wx_error_message(
                name=f'发送用户通知',
                level='warning', content={
                    'error': f'{key}:异步任务id丢失，请排查redis是否出现故障',
                    'details': f'教练简历发送失败',
                    'data': params,
                })
            return False, '异步任务id丢失'
        if err:
            error = f'给{"、".join(err)}的邮件发送失败'
            data_redis.set(task_id.decode(), f'False&{error}', ex=3600)
            return False, error
        else:
            data_redis.set(task_id.decode(), f'True&发送成功', ex=3600)
            return True, '发送成功'
    except SoftTimeLimitExceeded:
        push_celery_hanging_message('send_coach_resume_notice')
    except Exception as e:
        LarkMessageCenter().send_other_backend_message(
            'celery任务错误', 'error', {'error': f'send_coach_resume_notice:{str(e)}'})


@shared_task(queue='default', ignore_result=True)
def synchronization_stock_coupon():
    try:
        code, message = wxpay.marketing_favor_stock_list(stock_creator_mchid=settings.MCHID)
        message = eval(message) if type(message) != dict else message
        if code == 200:
            data = message['data']
            total_count = message['total_count']
            count = math.ceil(total_count / 10)
            if count > 1:
                for i in range(1, count):
                    code, message = wxpay.marketing_favor_stock_list(stock_creator_mchid=settings.MCHID, offset=i)
                    message = eval(message) if type(message) != dict else message
                    data += message['data']
            tmp_dict = {"unactivated": 1, "audit": 2, "running": 3, "stoped": 4, "paused": 5}
            tmp_dict_type = {"NORMAL": 1, "DISCOUNT_CUT": 4, "OTHER": 3}
            for s in data:
                stock = Stock.objects.filter(stock_id=s['stock_id']).first()
                if stock:
                    stock.distributed_coupons = s['distributed_coupons']
                    stock.status = tmp_dict[s['status']]
                    stock.save()
                else:
                    create_time = s['create_time'].replace('+08:00', '')
                    create_time = create_time.replace('T', ' ')
                    available_begin_time = s['available_begin_time'].replace('+08:00', '')
                    available_begin_time = available_begin_time.replace('T', ' ')
                    available_end_time = s['available_end_time'].replace('+08:00', '')
                    available_end_time = available_end_time.replace('T', ' ')

                    data = {
                        "stock_id": s['stock_id'],
                        "stock_name": s['stock_name'],
                        "create_time": create_time,
                        "status": tmp_dict[s['status']],
                        "max_coupons": s['stock_use_rule']['max_coupons'],
                        "distributed_coupons": s['distributed_coupons'],
                        "max_amount": s['stock_use_rule']['max_amount'],
                        "max_amount_by_day": s['stock_use_rule']['max_amount_by_day'],
                        "coupon_amount": s['stock_use_rule']['fixed_normal_coupon']['coupon_amount'],
                        "transaction_minimum": s['stock_use_rule']['fixed_normal_coupon']['transaction_minimum'],
                        "available_begin_time": available_begin_time,
                        "available_end_time": available_end_time,
                        "type": tmp_dict_type[s['stock_type']]
                    }
                    if s['description']:
                        data['description'] = s['description']
                    Stock.objects.create(**data)
    except SoftTimeLimitExceeded:
        push_celery_hanging_message('synchronization_stock_coupon')
    except Exception as e:
        LarkMessageCenter().send_other_backend_message(
            'celery任务错误', 'error', {'error': f'synchronization_stock_coupon:{str(e)}'})


@shared_task(queue='default', ignore_result=True)
def close_unpaid_order():
    try:
        orders = Order.objects.filter(status=OrderStatusEnum.pending_pay, deleted=False)
        if orders.exists():
            for order in orders:
                if (datetime.datetime.now() - order.created_at).seconds > 900:
                    # 请求微信支付关闭订单
                    is_success, message = ThirdPartyPayment(
                        channel=1, user_id=order.public_attr.user_id,
                        user_name=order.public_attr.user.cover_name).close_pay(order_no=order.order_no)
                    if is_success:
                        remove_order_data(order, interview=None)
    except SoftTimeLimitExceeded:
        push_celery_hanging_message('close_unpaid_order')
    except Exception as e:
        LarkMessageCenter().send_other_backend_message(
            'celery任务错误', 'error', {'error': f'close_unpaid_order:{str(e)}'})


@shared_task(queue='default', ignore_result=True)
def send_interview_confirmed_or_cancel_message(interview_id, message_type):
    """
    客户付款完成，立刻给教练推送消息
    客户取消预约后，立即给教练发送消息
    """
    try:
        interview = ProjectInterview.objects.get(pk=interview_id)
        work_wechat_user = WorkWechatUser.objects.filter(
            wx_user_id__isnull=False,
            user_id=interview.public_attr.user_id,
            deleted=False
        ).first()
        if work_wechat_user:
            user = work_wechat_user.user
            start_time = datetime.datetime.strftime(interview.public_attr.start_time, '%Y-%m-%d %H:%M')
            end_time = datetime.datetime.strftime(interview.public_attr.end_time, '%H:%M')
            if message_type == 'interview_cancel':
                content_item = [
                    {"key": "客户名称", "value": interview.public_attr.target_user.cover_name},
                    {"key": "辅导时间", "value": f'{start_time}~{end_time}'},
                    {"key": "辅导议题", "value": interview.message_topic},
                    {"key": "取消原因", "value": interview.close_reason},
                ]
                send_work_wechat_coach_notice.delay(
                    work_wechat_user.wx_user_id,
                    message_type,
                    content_item=content_item,
                    interview_id=interview.pk,
                    user_name=user.cover_name,
                    user_id=user.pk,
                    coachee_id=interview.public_attr.target_user_id,
                    coach_name=user.cover_name,
                    coachee_name=interview.public_attr.target_user.cover_name,
                    coach_id=work_wechat_user.user_id
                )
            else:
                content_item = [
                    {"key": "客户名称", "value": interview.public_attr.target_user.cover_name},
                    {"key": "辅导时间", "value": f'{start_time}~{end_time}'},
                    {"key": "辅导议题", "value": interview.message_topic},
                ]
                send_work_wechat_coach_notice.delay(
                    work_wechat_user.wx_user_id,
                    message_type,
                    interview_id=interview.pk,
                    user_name=user.cover_name,
                    content_item=content_item,
                    user_id=user.pk,
                    coachee_id=interview.public_attr.target_user_id,
                    coach_name=user.cover_name,
                    coachee_name=interview.public_attr.target_user.cover_name,
                    coach_id=work_wechat_user.user_id
                )
    except SoftTimeLimitExceeded:
        push_celery_hanging_message('send_interview_confirmed_or_cancel_message')
    except Exception as e:
        LarkMessageCenter().send_other_backend_message(
            'celery任务错误', 'error', {'error': f'send_interview_confirmed_or_cancel_message:{str(e)}'})


@shared_task(queue='default', ignore_result=True)
def send_coach_coachee_record_finish_message(interview_id):
    """
    客户提交辅导记录后立即发送该消息
    """
    try:
        project_interview = ProjectInterview.objects.get(pk=interview_id)
        work_wechat_user = WorkWechatUser.objects.filter(
            wx_user_id__isnull=False,
            user_id=project_interview.public_attr.user.id,
            deleted=False
        ).first()
        if work_wechat_user:
            date = '{}-{}'.format(
                project_interview.public_attr.start_time.strftime('%Y-%m-%d %H:%M'),
                project_interview.public_attr.end_time.strftime('%H:%M'))
            if project_interview.public_attr.project:
                company = project_interview.public_attr.project.company
                company_name = company.real_name
                message_type = 'coachee_record'
                content_item = [
                    {"key": "客户名称", "value": project_interview.public_attr.target_user.cover_name},
                    {"key": "所属企业", "value": company_name},
                    {"key": "所属项目", "value": project_interview.public_attr.project.name},
                    {"key": "辅导时间", "value": date},
                ]
                project_id = project_interview.public_attr.project_id
                project_name = project_interview.public_attr.project.name
            else:
                project_id, company_name, project_name = None, None, None
                message_type = 'customer_coachee_record'
                content_item = [
                    {"key": "客户名称", "value": project_interview.public_attr.target_user.cover_name},
                    {"key": "辅导时间", "value": date},
                    {"key": "辅导议题", "value": project_interview.message_topic}
                ]
            send_work_wechat_coach_notice(
                work_wechat_user.wx_user_id,
                message_type,
                company=company_name,
                coachee_name=project_interview.public_attr.target_user.cover_name,
                interview_id=project_interview.id,
                date=date,
                coach_id=project_interview.public_attr.user.id,
                project_id=project_id,
                project_name=project_name,
                coachee_id=project_interview.public_attr.target_user.id,
                coach_name=project_interview.public_attr.user.cover_name,
                content_item=content_item)

    except SoftTimeLimitExceeded:
        push_celery_hanging_message('send_coach_coachee_record_finish_message')
    except Exception as e:
        LarkMessageCenter().send_other_backend_message(
            'celery任务错误', 'error', {'error': f'send_coach_coachee_record_finish_message:{str(e)}'})


@shared_task(queue='default', ignore_result=True)
def coach_agree_interview_message(interview_id):
    """
        教练同意辅导 给客户和教练发通知
    """
    try:
        interview = ProjectInterview.objects.get(pk=interview_id, deleted=False)
        user = interview.public_attr.target_user
        interview_time = f"{interview.public_attr.start_time.strftime('%Y-%m-%d %H:%M')}~" \
                         f"{interview.public_attr.end_time.strftime('%H:%M')}"
        template_id = settings.PERSONAL_COACH_AGREE_INTERVIEW_TEMPLATE
        value_list = {
            'time5': {'value': interview_time},
            'thing7': {'value': str(interview.topic)[:20]},
        }
        url = f'/pages_interview/interview/detail?id={interview.id}&refer=2'
        if user.openid:
            msg = WeChatMiniProgram().template_send(template_id, user.openid, value_list, url)
            AliyunSlsLogLayout().send_third_api_log(
                user_id=user.id,
                user_name=user.cover_name,
                project_id=None,
                message='教练确认辅导消息提醒',
                content=msg,
            )
        # 给客户发送短信
        if user.phone:
            # 生成短链接
            url_state, short_link = WeChatMiniProgram().get_url_link(
                'pages/landing/landing',
                f'p=/pages_interview/interview/detail&id={interview.id}&invite_code={settings.SMS_INVITE_CODE}&refer=3')
            if url_state:
                SendSMS().send_coach_agree_interview_notice(
                    interview.public_attr.user.cover_name,
                    interview_time, short_link, user.phone,
                )

        work_wechat_user = WorkWechatUser.objects.filter(
            wx_user_id__isnull=False,
            user_id=interview.public_attr.user_id,
            deleted=False
        ).first()
        if work_wechat_user:
            send_work_wechat_coach_notice.delay(
                work_wechat_user.wx_user_id,
                'coach_agree_with_interview',
                coachee_id=user.id,
                coachee_name=user.cover_name,
                coach_id=work_wechat_user.user_id,
                coach_name=work_wechat_user.user.cover_name
            )

    except SoftTimeLimitExceeded:
        push_celery_hanging_message('coach_agree_interview_message')
    except Exception as e:
        LarkMessageCenter().send_other_backend_message(
            'celery任务错误', 'error', {'error': f'coach_agree_interview_message:{str(e)}'})


@shared_task(queue='default', ignore_result=True)
def user_update_interview_time_message(interview_id, before_time, now_time, role=None):
    """
        客户或教练调整时间后，立即给教练推送消息（自己调整也给自己发消息
    """
    try:
        interview = ProjectInterview.objects.get(pk=interview_id)
        coachee_user = interview.public_attr.target_user
        work_wechat_user = WorkWechatUser.objects.filter(
            wx_user_id__isnull=False,
            user_id=interview.public_attr.user_id,
            deleted=False
        ).first()
        if work_wechat_user:
            user = work_wechat_user.user
            content_item = [
                {"key": "客户名称", "value": interview.public_attr.target_user.cover_name},
                {"key": "调整前时间", "value": before_time},
                {"key": "调整后时间", "value": now_time},
                {"key": "辅导议题", "value": interview.message_topic},
            ]
            send_work_wechat_coach_notice.delay(
                work_wechat_user.wx_user_id,
                'coachee_change_interview_time',
                interview_id=interview.pk,
                user_name=user.cover_name,
                content_item=content_item,
                user_id=user.pk,
                coachee_id=interview.public_attr.target_user_id,
                coach_name=user.cover_name,
                coachee_name=interview.public_attr.target_user.cover_name,
                coach_id=work_wechat_user.user_id
            )

        # 如果是一对一辅导
        if interview.type == ProjectInterviewTypeEnum.formal_interview.value:
            # 有手机号,并且是教练修改，给用户发送短信提醒
            if coachee_user.phone and role in [UserRoleEnum.coach.value, UserRoleEnum.trainee_coach.value]:
                # 生成短链接
                url_state, short_link = WeChatMiniProgram().get_url_link(
                    'pages/landing/landing',
                    f'p=/pages_interview/interview/detail&id={interview.id}&invite_code={settings.SMS_INVITE_CODE}&refer=3')
                if url_state:
                    SendSMS().send_update_interview_time_notice(
                        interview.public_attr.user.cover_name,
                        now_time, short_link, coachee_user.phone
                    )

        # 化学面谈和一对一辅导给客户发送微信服务通知和企业微信通知。
        if interview.is_coach_agree and interview.type in [
                ProjectInterviewTypeEnum.formal_interview, ProjectInterviewTypeEnum.chemical_interview]:
            template_id = settings.PERSONAL_INTERVIEW_UPDATE_TIME_TEMPLATE
            value_list = {
                'thing1': {'value': str(interview.topic)[:20]},
                'date3': {'value': interview.public_attr.start_time.strftime('%Y/%m/%d %H:%M')},
                'thing5': {'value': "变更了上课时间请注意"},
            }
            url = f'/pages_interview/interview/detail?id={interview.id}&refer=2'

            if coachee_user.openid:
                msg = WeChatMiniProgram().template_send(template_id, coachee_user.openid, value_list, url)
                AliyunSlsLogLayout().send_third_api_log(
                    user_id=coachee_user.id,
                    user_name=coachee_user.cover_name,
                    project_id=None,
                    message='辅导调整时间发送消息提醒',
                    content=msg,
                )

            if interview.public_attr.project_id:
                msg, sender, external_user_id = get_project_manage_wx_user_id(
                    interview.public_attr.project.pk,
                    interview.public_attr.target_user_id,
                    'coachee_update_interview_time_message'
                )
                page = f"pages_interview/interview/detail"
                scene = f'id={interview.id}&refer=2'
                # 如果用户有企业微信且是项目运营好友则发送企业微信通知，不是则发送邮件通知。
                if msg:
                    text = f'您与{interview.public_attr.user.cover_name}教练的辅导时间从{before_time} 调整为 {now_time}，请您知晓。'
                    title = f"辅导时间已调整"
                    sender_user = WorkWechatUser.objects.filter(
                        wx_user_id=sender, user__isnull=False, deleted=False).first()
                    if sender_user:
                        send_work_wechat_coachee_notice(
                            sender, text, external_user_id, f"{page}?{scene}", title,
                            project_id=interview.public_attr.project.id,
                            project_name=interview.public_attr.project.name,
                            coachee_id=interview.public_attr.target_user_id,
                            coachee_name=interview.public_attr.target_user.cover_name,
                            forward_by=sender_user.user.cover_name,
                            forward_by_id=sender_user.user.id,
                        )
                
                manage_list = interview.public_attr.project.manager_list
                # 生成短链接
                url_state, short_link = WeChatMiniProgram().get_url_link(page, scene)
                url = get_qr_code(f'{settings.SITE_URL}landing/?p=/{page}&{scene}')
                message_type = 'update_interview_time_message'
                params = {
                    'coach_name': interview.public_attr.user.cover_name,
                    'coachee_name': interview.public_attr.target_user.cover_name,
                    'now_time': now_time,
                    'before_time': before_time,
                    "manager_name": manage_list[0]['true_name'] if manage_list else '',
                    "manager_email": manage_list[0]['email'] if manage_list else '',
                    "manager_phone": manage_list[0]['phone'] if manage_list else '',
                    "qr_code_url": url,
                    "short_link": short_link
                }
                to_email = [interview.public_attr.target_user.email]
                message_send_email_base(message_type, params, to_email, project_id=interview.public_attr.project_id,
                                           receiver_ids=interview.public_attr.target_user_id)

        # 利益相关者则发送邮件和短信
        if interview.is_coach_agree and interview.type == ProjectInterviewTypeEnum.stakeholder_interview:

            # 发送邮件通知
            page = f"pages_interview/interview/detail"
            scene = f'id={interview.id}&refer=2'
            # 生成短链接
            url_state, short_link = WeChatMiniProgram().get_url_link(page, scene)
            url = get_qr_code(f'{settings.SITE_URL}landing/?p=/{page}&{scene}')
            manage_list = interview.public_attr.project.manager_list
            message_type = 'update_interview_time_message'
            params = {
                'coach_name': interview.public_attr.user.cover_name,
                'coachee_name': interview.public_attr.target_user.cover_name,
                'now_time': now_time,
                'before_time': before_time,
                "manager_name": manage_list[0]['true_name'] if manage_list else '',
                "manager_email": manage_list[0]['email'] if manage_list else '',
                "manager_phone": manage_list[0]['phone'] if manage_list else '',
                "qr_code_url": url,
                "short_link": short_link
            }
            to_email = [interview.public_attr.target_user.email]
            message_send_email_base(message_type, params, to_email, project_id=interview.public_attr.project_id,
                                    receiver_ids=interview.public_attr.target_user_id)

            # 发送短信通知
            if interview.public_attr.target_user.phone:
                sms_scene = f'id={interview.id}&invite_code={settings.SMS_INVITE_CODE}&refer=3'
                state, url = WeChatMiniProgram().get_url_link("pages/landing/landing", f'p=/{page}&{sms_scene}')
                if state:
                    SendSMS().send_update_interview_time_message(
                        interview.public_attr.user.cover_name,
                        before_time, now_time, url,
                        interview.public_attr.target_user.phone
                    )

    except SoftTimeLimitExceeded:
        push_celery_hanging_message('coachee_update_interview_time_message')
    except Exception as e:
        LarkMessageCenter().send_other_backend_message(
            'celery任务错误', 'error', {'error': f'coachee_update_interview_time_message:{str(e)}'})


@shared_task(queue='default', ignore_result=True)
def send_interview_record_write_remind_message_of_21():
    """
    辅导当天晚上21点，给未完成辅导记录填写的教练发送消息提醒；当天晚上21点后结束的辅导，第二天晚上21点发送

    """
    try:
        now = datetime.datetime.now()
        yesterday = now - datetime.timedelta(days=1)
        interviews = ProjectInterview.objects.filter(
            (Q(public_attr__end_time__year=now.year, public_attr__end_time__month=now.month,
               public_attr__end_time__day=now.day, public_attr__end_time__lt=now) |
             Q(public_attr__end_time__year=yesterday.year, public_attr__end_time__month=yesterday.month,
               public_attr__end_time__day=yesterday.day, public_attr__end_time__gt=yesterday)
             ), order__isnull=False, order__status=OrderStatusEnum.paid, deleted=False, place_category=1,
            coach_record_status=False, is_coach_agree=True, public_attr__project__isnull=True
            ).exclude(public_attr__status=ATTR_STATUS_INTERVIEW_CANCEL)
        if interviews.exists():
            for interview in interviews:
                work_wechat_user = WorkWechatUser.objects.filter(
                    wx_user_id__isnull=False,
                    user_id=interview.public_attr.user_id,
                    deleted=False
                ).first()
                if work_wechat_user:
                    user = work_wechat_user.user
                    date = '{}-{}'.format(
                        interview.public_attr.start_time.strftime('%Y-%m-%d %H:%M'),
                        interview.public_attr.end_time.strftime('%H:%M'))
                    content_item = [
                        {"key": "客户名称", "value": interview.public_attr.target_user.cover_name},
                        {"key": "辅导时间", "value": date},
                        {"key": "辅导议题", "value": interview.message_topic},
                    ]
                    send_work_wechat_coach_notice.delay(
                        work_wechat_user.wx_user_id,
                        'interview_record_write_remind',
                        interview_id=interview.pk,
                        user_name=user.cover_name,
                        content_item=content_item,
                        user_id=user.pk,
                        coachee_id=interview.public_attr.target_user_id,
                        coach_name=user.cover_name,
                        coachee_name=interview.public_attr.target_user.cover_name,
                        coach_id=work_wechat_user.user_id
                    )
    except SoftTimeLimitExceeded:
        push_celery_hanging_message('send_interview_record_write_remind_message_of_21')
    except Exception as e:
        LarkMessageCenter().send_other_backend_message(
            'celery任务错误', 'error', {'error': f'send_interview_record_write_remind_message_of_21:{str(e)}'})


@shared_task(queue='default', ignore_result=True)
def send_interview_record_write_remind_message_2h():
    try:
        """
        辅导结束后2小时 给未完成辅导记录填写的教练发送消息提醒
        """
        two_hours_time = datetime.datetime.now() - datetime.timedelta(hours=2)
        interviews = ProjectInterview.objects.filter(
            order__isnull=False, order__status=OrderStatusEnum.paid, deleted=False, place_category=1,
            public_attr__end_time__year=two_hours_time.year, public_attr__end_time__month=two_hours_time.month,
            public_attr__end_time__day=two_hours_time.day, public_attr__end_time__hour=two_hours_time.hour,
            public_attr__end_time__minute=two_hours_time.minute, is_coach_agree=True, coach_record_status=False,
            public_attr__project__isnull=True
        ).exclude(public_attr__status=ATTR_STATUS_INTERVIEW_CANCEL)
        if interviews.exists():
            for interview in interviews:
                work_wechat_user = WorkWechatUser.objects.filter(
                    wx_user_id__isnull=False,
                    user_id=interview.public_attr.user_id,
                    deleted=False
                ).first()
                if work_wechat_user:
                    user = work_wechat_user.user
                    date = '{}-{}'.format(
                        interview.public_attr.start_time.strftime('%Y-%m-%d %H:%M'),
                        interview.public_attr.end_time.strftime('%H:%M'))
                    content_item = [
                        {"key": "客户名称", "value": interview.public_attr.target_user.cover_name},
                        {"key": "辅导时间", "value": date},
                        {"key": "辅导议题", "value": interview.message_topic},
                    ]
                    send_work_wechat_coach_notice.delay(
                        work_wechat_user.wx_user_id,
                        'interview_record_write_remind',
                        interview_id=interview.pk,
                        user_name=user.cover_name,
                        content_item=content_item,
                        user_id=user.pk,
                        coachee_id=interview.public_attr.target_user_id,
                        coach_name=user.cover_name,
                        coachee_name=interview.public_attr.target_user.cover_name,
                        coach_id=work_wechat_user.user_id
                    )

    except SoftTimeLimitExceeded:
        push_celery_hanging_message('send_interview_record_write_remind_message_2h')
    except Exception as e:
        LarkMessageCenter().send_other_backend_message(
            'celery任务错误', 'error', {'error': f'send_interview_record_write_remind_message_2h:{str(e)}'})


@shared_task(queue='default', ignore_result=True)
def send_interview_start_before_30():
    """
    辅导开始前30分钟发送消息提醒
    """
    try:
        start_date_time = datetime.datetime.now() + datetime.timedelta(minutes=30)
        interviews = ProjectInterview.objects.filter(
            order__isnull=False, order__status=OrderStatusEnum.paid, deleted=False, place_category=1,
            is_coach_agree=True, public_attr__start_time__year=start_date_time.year,
            public_attr__start_time__month=start_date_time.month, public_attr__start_time__day=start_date_time.day,
            public_attr__start_time__hour=start_date_time.hour, public_attr__start_time__minute=start_date_time.minute,
            public_attr__project__isnull=True
        ).exclude(public_attr__status=ATTR_STATUS_INTERVIEW_CANCEL)
        if interviews.exists():
            for interview in interviews:
                work_wechat_user = WorkWechatUser.objects.filter(
                    wx_user_id__isnull=False,
                    user_id=interview.public_attr.user_id,
                    deleted=False
                ).first()
                if work_wechat_user:
                    user = work_wechat_user.user
                    date = '{}-{}'.format(
                        interview.public_attr.start_time.strftime('%Y-%m-%d %H:%M'),
                        interview.public_attr.end_time.strftime('%H:%M'))
                    content_item = [
                        {"key": "客户名称", "value": interview.public_attr.target_user.cover_name},
                        {"key": "辅导时间", "value": date},
                        {"key": "辅导议题", "value": interview.message_topic},
                    ]
                    send_work_wechat_coach_notice.delay(
                        work_wechat_user.wx_user_id,
                        'interview_30_minute',
                        interview_id=interview.pk,
                        user_name=user.cover_name,
                        content_item=content_item,
                        user_id=user.pk,
                        coachee_id=interview.public_attr.target_user_id,
                        coach_name=user.cover_name,
                        coachee_name=interview.public_attr.target_user.cover_name,
                        coach_id=work_wechat_user.user_id
                    )

                    work_wechat_coachee_user = WorkWechatUser.objects.filter(
                        external_user_id__isnull=False, user_id=interview.public_attr.target_user_id,deleted=False).first()
                    is_relevance, external_user_id = coach_public.work_wechat_coach_customer_info(
                        work_wechat_user, work_wechat_coachee_user, interview.public_attr.target_user)

                    if is_relevance:
                        start_time = interview.public_attr.start_time.strftime('%H:%M')
                        end_time = interview.public_attr.end_time.strftime('%H:%M')

                        text = f'我们今天{start_time} - {end_time}的一对一教练辅导即将开始。温馨提醒，在辅导开始前找到一个安静的环境，确认网络流畅哦'
                        title = f"群智企业教练"
                        page = f"pages_interview/interview/detail"
                        scene = f'id={interview.id}&refer=2'
                        send_work_wechat_coachee_notice(
                            work_wechat_user.wx_user_id, text, external_user_id, f"{page}?{scene}", title,
                            file_name="stakeholder_share.png",
                            coach_id=interview.public_attr.user.id,
                            coach_name=interview.public_attr.user.cover_name,
                            coachee_id=interview.public_attr.target_user.id,
                            coachee_name=interview.public_attr.target_user.cover_name
                        )


                user = interview.public_attr.target_user
                interview_time = interview.public_attr.start_time.strftime('%Y年%m月%d日 %H:%M:%S')
                template_id = settings.PERSONAL_BEFORE_INTERVIEW_TEMPLATE
                value_list = {
                    'thing15': {'value': str(interview.topic)[:20]},
                    'time2': {'value': interview_time},
                }
                url = f'/pages_interview/interview/detail?id={interview.id}&refer=2'
                if user.openid:
                    msg = WeChatMiniProgram().template_send(template_id, user.openid, value_list, url)
                    AliyunSlsLogLayout().send_third_api_log(
                        user_id=user.id,
                        user_name=user.cover_name,
                        project_id=None,
                        message='辅导开始前30分钟发送消息提醒',
                        content=msg,
                    )
                if user.phone:
                    # 生成短链接
                    url_state, short_link = WeChatMiniProgram().get_url_link(
                        'pages/landing/landing',
                        f'p=/pages_interview/interview/detail?id={interview.id}&invite_code={settings.SMS_INVITE_CODE}&refer=3')
                    if url_state:
                        SendSMS().send_interview_start_30m_notice(
                            interview.public_attr.user.cover_name, short_link, user.phone,
                        )

    except SoftTimeLimitExceeded:
        push_celery_hanging_message('send_interview_start_before_30')
    except Exception as e:
        LarkMessageCenter().send_other_backend_message(
            'celery任务错误', 'error', {'error': f'send_interview_start_before_30:{str(e)}'})


@shared_task(queue='default', ignore_result=True)
def send_project_interview_start_before_30():
    """
    项目辅导开始前30分钟发送消息提醒
    """
    try:
        start_date_time = datetime.datetime.now() + datetime.timedelta(minutes=30)
        interviews = ProjectInterview.objects.filter(
            deleted=False, place_category=ProjectInterviewPlaceCategoryEnum.online_one_to_one.value,  # 一对辅导
            type=ProjectInterviewTypeEnum.formal_interview.value,  # 正式约谈
            public_attr__start_time__year=start_date_time.year,
            public_attr__start_time__month=start_date_time.month, public_attr__start_time__day=start_date_time.day,
            public_attr__start_time__hour=start_date_time.hour, public_attr__start_time__minute=start_date_time.minute,
            public_attr__project__isnull=False
        ).exclude(public_attr__status=ATTR_STATUS_INTERVIEW_CANCEL)
        if interviews.exists():
            for interview in interviews:
                user = interview.public_attr.target_user
                if user.phone:
                    # 生成短链接
                    url_state, short_link = WeChatMiniProgram().get_url_link(
                        'pages/landing/landing',
                        f'p=/pages_interview/interview/detail&id={interview.id}&invite_code={settings.SMS_INVITE_CODE}&refer=3')
                    if url_state:
                        SendSMS().send_interview_start_30m_notice(
                            interview.public_attr.user.cover_name, short_link, user.phone,
                        )
    except SoftTimeLimitExceeded:
        push_celery_hanging_message('send_project_interview_start_before_30')
    except Exception as e:
        LarkMessageCenter().send_other_backend_message(
            'celery任务错误', 'error', {'error': f'send_project_interview_start_before_30:{str(e)}'})


@shared_task(queue='default', ignore_result=True)
def process_work_wechat_callback(data, msg, domain, params, is_work_wechat_callback):
    """
    企业微信回调处理
    """
    try:
        # 如果是企业微信回调，则分发给其他服务器项目
        if is_work_wechat_callback == 'true':
            params += '&is_work_wechat_callback=false'  # 防止项目间循环回调
            for item in settings.WORK_WECHAT_CALLBACK_URL:
                if domain not in item:
                    url = '{}?{}'.format(item, params)  # 请求参数拼接
                    requests.post(url, data=data)

        xml_tree = ET.fromstring(msg)
        event = xml_tree.find("Event").text if xml_tree.find("Event") is not None else None

        AliyunSlsLogLayout().send_third_api_log(
            massage='企业微信回调处理消息提醒',
            content={'event': event, 'msg':msg})

        # 如果是部门变更事件则更新部门人员缓存信息
        if event and event == settings.DEPARTMENT_EVENT_KEY:
            update_work_wechat_department_users()
            # 企业微信回调不稳定，暂时先不通过回调更新数据
            # change_type = xml_tree.find("ChangeType").text
            # 删除企业微信用户则删除数据库中的用户数据
            # if change_type == 'delete_user':
            #     user_id = xml_tree.find("UserID").text
            #     WorkWechatUser.objects.filter(wx_user_id=user_id).update(deleted=True)
        # 如果是客户更新回调则更新好友列表
        elif event and event == settings.CHANGE_EXTERNAL_USER:

            # 获取基础响应信息
            change_type = xml_tree.find("ChangeType").text if xml_tree.find("ChangeType") is not None else None
            user_id = xml_tree.find("UserID").text if xml_tree.find("UserID") is not None else None
            external_user_id = xml_tree.find("ExternalUserID").text if xml_tree.find("ExternalUserID") is not None else None

            # 如果有缓存的情况下进行数据处理
            customer_dict = third_party_redis.get(f'{user_id}_customer_dict')
            if all([customer_dict, user_id, external_user_id, change_type]):
                customer_dict = json.loads(customer_dict.decode())
                customer_list = customer_dict.keys()

                # 添加事件
                if change_type == 'add_external_contact':
                    # 通过external_user_id置换unionid
                    state, external_contact = work_wechat.WorkWechat().get_external_user_data(external_user_id)
                    # 成功就更新绑定的缓存
                    if state:
                        customer_dict[external_user_id] = external_contact.get('unionid')
                        third_party_redis.set(f'{user_id}_customer_dict', json.dumps(customer_dict))

                        # 查询用户在企业微信信息表中是否有记录
                        user = User.objects.filter(unionid=external_contact.get('unionid')).first()
                        # 有用户的前提下绑定企业微信信息
                        if user:
                            work_wechat_user = WorkWechatUser.objects.filter(user=user, deleted=False).first()
                            # 有就更新
                            if work_wechat_user:
                                work_wechat_user.external_user_id = external_user_id
                                work_wechat_user.save()
                            # 没有就新建
                            else:
                                WorkWechatUser.objects.create(user=user,external_user_id=external_user_id)
                    # 失败调用接口去获取新的数据
                    else:
                        update_work_wechat_user_customer_dict(user_id)

                # 删除事件
                elif change_type == 'del_external_contact':
                    # 如果在列表里面则处理，如果不在，那就不是好友，无需处理
                    if external_user_id in  customer_list:
                        customer_dict.pop(external_user_id)
                        third_party_redis.set(f'{user_id}_customer_dict', json.dumps(customer_dict))
        return
    except SoftTimeLimitExceeded:
        push_celery_hanging_message('process_work_wechat_callback')
    except Exception as e:
        LarkMessageCenter().send_other_backend_message(
            'celery任务错误', 'error', {'error': f'process_work_wechat_callback:{str(e)}'})

@shared_task(queue='default', ignore_result=True)
def lark_send_unconfirmed_interview_message():
    try:
        if settings.SITE_URL != 'https://www.qzcoach.com/':
            return

        # 辅导开始时间<=当前时间，认为是过期辅导，通知只查询开始时间>当前时间的辅导，
        now_date = datetime.datetime.now()
        interview_ids = list(ProjectInterview.objects.filter(
            deleted=False, is_coach_agree=False,
            public_attr__start_time__gt=now_date.strftime('%Y-%m-%d %H:%M:%S'),
            order__status=OrderStatusEnum.paid).exclude(
            public_attr__status=ATTR_STATUS_INTERVIEW_CANCEL
        ).order_by('-public_attr__start_time').values_list('id', flat=True))
        if interview_ids:
            for interview_id in interview_ids:
                send_lark_message([interview_id], 'unconfirmed_interview')
    except SoftTimeLimitExceeded:
        push_celery_hanging_message('lark_send_unconfirmed_interview_message')
    except Exception as e:
        LarkMessageCenter().send_other_backend_message(
            'celery任务错误', 'error', {'error': f'lark_send_unconfirmed_interview_message:{str(e)}'})


@shared_task(queue='default', ignore_result=True)
def send_coach_minaapp_resume_email(resume_id, user):
    try:
        # 获取教练信息
        qrcode_state, content = WeChatMiniProgram().get_miniapp_qrcode(scene=f'resume_id={resume_id}&refer=2')
        if not qrcode_state:
            push_wx_error_message(name='获取微信小程序教练简历接口错误', level='error', content={
                'resume_id': resume_id,
                'content': content
            })
            resume_url = None
        else:
            resume_url = get_base_qr_code(content, 'coach_resume')

        url_state, short_link = WeChatMiniProgram().get_url_link('pages/user/myResume', f'resume_id={resume_id}&refer=2')
        if not url_state:
            push_wx_error_message(name='获取微信小程序教练简历接口错误', level='error', content={
                'resume_id': resume_id,
                'content': short_link
            })
            short_link = ''

        params = {"true_name": user.cover_name, "email": user.email, 'short_link': short_link,
                  "password": aesdecrypt(user.password), "resume_url": resume_url}
        message_send_email_base('remind_resume_email', params, [user.email], receiver_ids=user.id)
        return
    except SoftTimeLimitExceeded:
        push_celery_hanging_message('send_coach_minaapp_resume_email')
    except Exception as e:
        LarkMessageCenter().send_other_backend_message(
            'celery任务错误', 'error', {'error': f'send_coach_minaapp_resume_email:{str(e)}'})


@shared_task(queue='default', ignore_result=True)
def update_work_wechat_user_customer_dict(wx_user_id=None):
    try:
        # 如果有wx_user_id参数就单查
        if wx_user_id:
            wx_user_id_list = [wx_user_id]

        # 没有参数则更新全部用户数据
        else:
            wx_user_id_list = list(WorkWechatUser.objects.filter(
                wx_user_id__isnull=False,
                user__isnull=False,
                deleted=False).values_list('wx_user_id', flat=True))
        # 获取用户的所有客户详情。
        for item in wx_user_id_list:
            is_success, all_external_user_list = work_wechat.WorkWechat().get_external_user_list([item])
            if is_success:
                # 客户信息收集
                redis_data = {}
                for external_user in all_external_user_list:
                    redis_data[external_user.get('external_contact').get('external_userid')] = external_user.get(
                        'external_contact').get('unionid')
                # 缓存数据
                third_party_redis.set(f'{item}_customer_dict', json.dumps(redis_data))
        return True
    except SoftTimeLimitExceeded:
        push_celery_hanging_message('update_work_wechat_user_customer_dict')
    except Exception as e:
        LarkMessageCenter().send_other_backend_message(
            'celery任务错误', 'error', {'error': f'update_work_wechat_user_customer_dict:{str(e)}'})


# 定时更新企业微信部门成员信息
@shared_task(queue='default', ignore_result=True)
def update_work_wechat_department_users(include=None):
    try:
        """
        include: 列表 更新指定部门
        """
        if include:
            department_ids = include
        else:
            state, department_ids = work_wechat.WorkWechat().get_department_ids()
            if not state:
                return False, '企业微信部门列表获取失败'
        # 循环存储
        for item in department_ids:
            item_state, raw_department_users = work_wechat.WorkWechat().get_department_users(item)
            if not item_state:
                continue
            data = [{'work_wechat_user_id': item.get('userid'), 'name': item.get('name')} for item in
                    raw_department_users]
            third_party_redis.set(f'work_wechat_department_{item}', json.dumps(data))
        return
    except SoftTimeLimitExceeded:
        push_celery_hanging_message('get_work_wechat_department_users')
    except Exception as e:
        LarkMessageCenter().send_other_backend_message(
            'celery任务错误', 'error', {'error': f'get_work_wechat_department_users:{str(e)}'})


@shared_task(queue='default', ignore_result=True)
def update_wechat_stable_token():
    access_token_redis = redis.Redis.from_url(settings.WECHAT_ACCESS_TOKEN_URL)
    try:

        url = 'https://api.weixin.qq.com/cgi-bin/stable_token'
        params = {
            "grant_type": "client_credential",
            'appid': settings.APP_ID,
            'secret': settings.APP_SECRET
        }
        resp = requests.post(url, json=params)
        if resp.ok:
            if not resp.json().get('errcode'):
                access_token = resp.json().get('access_token')
                expires_in = resp.json().get('expires_in')
                access_token_redis.set('access_token', access_token, ex=expires_in)
            else:
                push_wx_error_message(name='获取微信小程序长期token', level='error', content={
                    'params': params,
                    'content': resp.json()
                })
        else:
            push_wx_error_message(name='获取微信小程序长期token', level='error', content={
                'params': params,
                'content': resp.text
            })
    except SoftTimeLimitExceeded:
        push_celery_hanging_message('update_wechat_stable_token')
    except Exception as e:
        LarkMessageCenter().send_other_backend_message(
            'celery任务错误', 'error', {'error': f'update_wechat_stable_token:{str(e)}'})



# 异步发送飞书消息  todo zkg 消息抽出后清除
@shared_task(queue='default', ignore_result=True)
def push_lark_message_celery(ids, message_type):
    try:
        send_lark_message(ids, message_type)
    except SoftTimeLimitExceeded:
        push_celery_hanging_message('push_lark_message_celery')
    except Exception as e:
        LarkMessageCenter().send_other_backend_message(
            'celery任务错误', 'error', {'error': f'push_lark_message_celery:{str(e)}'})


@shared_task(queue='default', ignore_result=True)
def update_coach_offer_status():
    try:
        now = datetime.datetime.now()
        CoachOffer.objects.filter(
            deleted=False, status=CoachOfferStatusEnum.not_confirm,
            project_offer__confirm_time__lt=now).update(status=CoachOfferStatusEnum.expired)
    except SoftTimeLimitExceeded:
        push_celery_hanging_message('update_coach_offer_status')
    except Exception as e:
        LarkMessageCenter().send_other_backend_message(
            'celery任务错误', 'error', {'error': f'update_coach_offer_status:{str(e)}'})


@shared_task(queue='default', ignore_result=True)
def send_coach_offer_notice(coach_offer_ids, user_id, key):
    try:
        coach_offers = CoachOffer.objects.filter(id__in=coach_offer_ids)
        wechat_err = []
        account_err = []
        project_offer = coach_offers.first().project_offer
        project = project_offer.project
        company = project.company
        company_name = company.real_name
        project_name = project.name
        confirm_time = project_offer.confirm_time.strftime('%Y-%m-%d %H:%M:%S')
        for coach_offer in coach_offers:
            work_wechat_user = WorkWechatUser.objects.filter(
                wx_user_id__isnull=False,
                user_id=coach_offer.coach.user.id,
                deleted=False
            ).values('wx_user_id').first()
            if work_wechat_user and work_wechat_user['wx_user_id']:
                content_is_success, app_is_success = send_work_wechat_coach_notice(
                    user=work_wechat_user['wx_user_id'],
                    content_type='send_coach_offer',
                    offer_id=coach_offer.id,
                    project_name=project_name,
                    company_name=company_name,
                    confirm_time=confirm_time,
                    user_id=user_id
                )
                if content_is_success and app_is_success:  # 发送成功，修改offer状态
                    if project_offer.status == ProjectOfferStatusEnum.not_sent:  # 修改项目offer发送状态
                        project_offer.status = ProjectOfferStatusEnum.sent
                        project_offer.send_time = datetime.datetime.now()
                        project_offer.save()
                    if not coach_offer.send_time:
                        coach_offer.send_time = datetime.datetime.now()
                        coach_offer.save()
                else:  # 企业微信消息发送失败记录发送失败教练信息
                    wechat_err.append(coach_offer.coach.user.cover_name)
            else:
                account_err.append(coach_offer.coach.user.cover_name)

        task_id = data_redis.get(key)

        if not task_id:
            params = {
                'project_name': project_name,
                'coach_offer_ids': coach_offer_ids,
            }
            push_wx_error_message(
                name=f'发送项目邀请通知',
                level='warning', content={
                    'error': f'{key}:异步任务id丢失，请排查redis是否出现故障',
                    'details': f'项目邀请发送失败',
                    'data': params,
                })
            return False, '异步任务id丢失'
        if wechat_err or account_err:
            account_err_msg, wechat_err_msg, error = None, None, None
            if account_err:
                account_err_msg = f'{"、".join(account_err)}未绑定企微账号'
            if wechat_err:
                wechat_err_msg = f'{"、".join(wechat_err)}企业微信错误'
            if account_err_msg and wechat_err_msg:
                error = f'因{account_err_msg}以及{wechat_err_msg},项目邀请发送失败'
            elif account_err_msg and not wechat_err_msg:
                error = f'因{account_err_msg}，项目邀请发送失败'
            elif not account_err_msg and wechat_err_msg:
                error = f'因{wechat_err_msg}，项目邀请发送失败'
            data_redis.set(task_id.decode(), f'False&{error}', ex=3600)
            return False, error
        else:
            data_redis.set(task_id.decode(), f'True&发送成功', ex=3600)
            return True, '发送成功'
    except SoftTimeLimitExceeded:
        push_celery_hanging_message('send_coach_offer_notice')
    except Exception as e:
        LarkMessageCenter().send_other_backend_message(
            'celery任务错误', 'error', {'error': f'send_coach_offer_notice:{str(e)}'})


@shared_task(queue='default', ignore_result=True)
def send_middleware_500_message(url, user_id, true_name, role, token, params, method, body, error):
    try:
        LarkDocument().err_excel_append_value([url, user_id, true_name, role, token, params, method, body,
                                               "".join(error), datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')])
        LarkMessageCenter().send_server_error(method, url, token, user_id, params, body, error, role, true_name)
    except SoftTimeLimitExceeded:
        push_celery_hanging_message('send_middleware_500_message')
    except Exception as e:
        LarkMessageCenter().send_other_backend_message(
            'celery任务错误', 'error', {'error': f'send_middleware_500_message:{str(e)}'})


@shared_task(queue='default', ignore_result=True)
def send_middleware_400_message(method, url, token, user_id, params, body, msg, role, true_name):
    try:
        LarkMessageCenter().send_business_error(method, url, token, user_id, params, body, msg, role, true_name)
        AliyunSlsLogLayout().send_business_api_log(
            method=method, url=url, token=token, user_id=user_id, params=params, body=body, msg=msg,
            role=role, true_name=true_name)
    except SoftTimeLimitExceeded:
        push_celery_hanging_message('send_middleware_400_message')
    except Exception as e:
        LarkMessageCenter().send_other_backend_message(
            'celery任务错误', 'error', {'error': f'send_middleware_400_message:{str(e)}'})


@shared_task(queue='default', ignore_result=True)
def stakeholder_interview_record_30_notice():
    """
    利益相关者访谈结束后30分钟，教练未填写跟某个利益相关者的访谈记录，提醒教练填写
    """
    try:
        end_date_time = datetime.datetime.now() - datetime.timedelta(minutes=30)
        project_interview = ProjectInterview.objects.filter(
            type=ProjectInterviewTypeEnum.stakeholder_interview, deleted=False,
            public_attr__end_time__date=end_date_time.date(),
            public_attr__end_time__hour=end_date_time.hour,
            public_attr__end_time__minute=end_date_time.minute,
            public_attr__project__isnull=False,
            coach_record_status=False
        ).exclude(public_attr__status=ATTR_STATUS_INTERVIEW_CANCEL)
        for interview in project_interview.all():
            work_wechat_user = WorkWechatUser.objects.filter(
                wx_user_id__isnull=False, user_id=interview.public_attr.user_id,
                deleted=False).first()
            if work_wechat_user:
                user = work_wechat_user.user
                stakeholder_interview = StakeholderInterview.objects.filter(
                    interview=interview, deleted=False).first()
                content_item = [
                    {"key": "客户名称", "value": interview.public_attr.target_user.cover_name},
                    {"key": "所属企业", "value": interview.public_attr.project.company.real_name},
                    {"key": "所属项目", "value": interview.public_attr.project.name},
                ]
                send_work_wechat_coach_notice.delay(
                    work_wechat_user.wx_user_id,
                    'coach_stakeholder_interview_fill_notice',
                    content_item=content_item,
                    interview_id=interview.pk,
                    project_name=interview.public_attr.project.name,
                    stakeholder_name=interview.public_attr.target_user.cover_name,
                    coachee_name=stakeholder_interview.project_interested.master.cover_name,
                    coachee_id=interview.public_attr.target_user_id,
                    coach_name=user.cover_name,
                    coach_id=work_wechat_user.user_id
                )
    except SoftTimeLimitExceeded:
        push_celery_hanging_message('stakeholder_interview_record_30_notice')
    except Exception as e:
        LarkMessageCenter().send_other_backend_message(
            'celery任务错误', 'error', {'error': f'stakeholder_interview_record_30_notice:{str(e)}'})


@shared_task(queue='default', ignore_result=True)
def stakeholder_interview_report_30_notice():
    try:
        """
        最后一个利益相关者访谈结束后30分钟，教练未填写汇总的报告，发送该消息提醒
        """
        end_date_time = datetime.datetime.now() - datetime.timedelta(minutes=30)

        modules = StakeholderInterviewModule.objects.annotate(
            interview_count=Count(
                'stakeholder_interview__interview',
                filter=Q(stakeholder_interview__interview__public_attr__end_time__lte=datetime.datetime.now())
            ),
            latest_interview_time=Max('stakeholder_interview__interview__public_attr__end_time')
        ).filter(
            deleted=False, coach_task__coach_submit_time__isnull=True,
            stakeholder_interview_number=F('interview_count'),
            latest_interview_time__date=end_date_time.date(),
            latest_interview_time__hour=end_date_time.hour,
            latest_interview_time__minute=end_date_time.minute,
        )
        for stakeholder_interview_module in modules:
            coach_task = stakeholder_interview_module.coach_task

            work_wechat_user = WorkWechatUser.objects.filter(
                wx_user_id__isnull=False, user_id=coach_task.public_attr.user_id, deleted=False).first()
            if work_wechat_user:
                user = work_wechat_user.user
                content_item = [
                    {"key": "客户名称", "value": coach_task.public_attr.target_user.cover_name},
                    {"key": "所属企业", "value": coach_task.public_attr.project.company.real_name},
                    {"key": "所属项目", "value": coach_task.public_attr.project.name},
                ]
                send_work_wechat_coach_notice.delay(
                    work_wechat_user.wx_user_id,
                    'stakeholder_interview_record_fill_notice',
                    content_item=content_item,
                    coach_task_id=stakeholder_interview_module.coach_task_id,
                    project_name=coach_task.public_attr.project.full_name,
                    coachee_name=coach_task.public_attr.target_user.cover_name,
                    coachee_id=coach_task.public_attr.target_user_id,
                    coach_name=user.cover_name,
                    coach_id=work_wechat_user.user_id
                )
    except SoftTimeLimitExceeded:
        push_celery_hanging_message('stakeholder_interview_report_30_notice')
    except Exception as e:
        LarkMessageCenter().send_other_backend_message(
            'celery任务错误', 'error', {'error': f'stakeholder_interview_report_30_notice:{str(e)}'})


@shared_task(queue='default', ignore_result=True)
def chemical_interview_later_30():
    # 修改为化学面谈结束后立刻发送
    try:
        end_date_time = datetime.datetime.now()
        interviews = ProjectInterview.objects.filter(
            type=ProjectInterviewTypeEnum.chemical_interview, deleted=False,
            public_attr__end_time__year=end_date_time.year,
            public_attr__end_time__month=end_date_time.month, public_attr__end_time__day=end_date_time.day,
            public_attr__end_time__hour=end_date_time.hour, public_attr__end_time__minute=end_date_time.minute,
            public_attr__project__isnull=False
        ).exclude(chemical_interview__chemical_interview_status=ChemicalInterviewStatusEnum.unselected)
        if interviews.exists():
            for interview in interviews:
                work_wechat_user = WorkWechatUser.objects.filter(
                    wx_user_id__isnull=False,
                    user_id=interview.public_attr.user_id,
                    deleted=False
                ).first()
                if work_wechat_user:
                    user = work_wechat_user.user
                    content_item = [
                        {"key": "客户名称", "value": interview.public_attr.target_user.cover_name},
                        {"key": "所属企业", "value": interview.public_attr.project.company.real_name},
                        {"key": "所属项目", "value": interview.public_attr.project.name},
                    ]
                    send_work_wechat_coach_notice.delay(
                        work_wechat_user.wx_user_id,
                        'chemical_interview_later_30',
                        interview_id=interview.pk,
                        project_name=interview.public_attr.project.full_name,
                        coachee_name=interview.public_attr.target_user.cover_name,
                        content_item=content_item,
                        user_id=user.pk,
                        coachee_id=interview.public_attr.target_user_id,
                        coach_name=user.cover_name,
                        coach_id=work_wechat_user.user_id
                    )
                # 给客户发邮件，企业微信消息，小程序消息
                feedback_message_id = 'VblOei-Z73NHxDorDlxmJ_pJWJRl-nr8pZ85MXOraXI'
                send_wechat_msg.delay(
                    feedback_message_id,
                    interview.public_attr.target_user,
                    value_list={
                        'thing4': {'value': '化学面谈结果反馈'},
                        'thing6': {'value': '请反馈本次化学面谈对教练的印象吧'}})
                
                state, content = WeChatMiniProgram().get_miniapp_qrcode(
                    scene=f"interview_id={interview.id}&refer=2",
                    page='pages_interview/feedback_interview/feedback_interview')
                if state:
                    url_state, short_link = WeChatMiniProgram().get_url_link(
                        'pages_interview/feedback_interview/feedback_interview',
                        f'interview_id={interview.id}&refer=2')

                    try:
                        url = get_base_qr_code(content, 'coach_resume')
                    except:
                        url = ''
                    message_type = 'chemical_interview_feedback'
                    project_member = ProjectMember.objects.filter(
                        user=interview.public_attr.target_user,
                        project=interview.public_attr.project, deleted=False).first()
                    manage_list = project_member.project.manager_list
                    params = {
                        "true_name": project_member.user.cover_name,
                        "coach_name": interview.public_attr.user.cover_name,
                        "manager_name": manage_list[0]['true_name'] if manage_list else '',
                        "manager_email": manage_list[0]['email'] if manage_list else '',
                        "manager_phone": manage_list[0]['phone'] if manage_list else '',
                        "qr_code_url": url,
                        "short_link": short_link
                    }
                    email = [project_member.user.email]
                    message_send_email_base(message_type=message_type, params=params, to_email=email, 
                                            project_id=project_member.project_id, receiver_ids=project_member.user_id)

                # 给客户发企业微信消息
                text = f'您和{interview.public_attr.user.cover_name}教练的化学面谈已结束，请点击下方小程序链接反馈您是否选择该教练吧'
                page = f"pages_interview/feedback_interview/feedback_interview" \
                       f"?interview_id={interview.id}&refer=2"
                title = "访谈结果反馈"
                msg, sender, external_user_id = get_project_manage_wx_user_id(
                    interview.public_attr.project.pk,
                    interview.public_attr.target_user_id,
                    'chemical_interview_later_30'
                )
                if msg:
                    sender_user = WorkWechatUser.objects.filter(
                        wx_user_id=sender,
                        user__isnull=False,
                        deleted=False
                    ).first()
                    if sender_user:
                        send_work_wechat_coachee_notice.delay(
                            sender, text, external_user_id, page, title,
                            content_type='add_msg_template',
                            project_id=interview.public_attr.project.id,
                            project_name=interview.public_attr.project.full_name,
                            coachee_id=interview.public_attr.target_user_id,
                            coachee_name=interview.public_attr.target_user.cover_name,
                            forward_by=sender_user.user.cover_name,
                            forward_by_id=sender_user.user.id,
                        )
    except SoftTimeLimitExceeded:
        push_celery_hanging_message('chemical_interview_later_30')
    except Exception as e:
        LarkMessageCenter().send_other_backend_message(
            'celery任务错误', 'error', {'error': f'chemical_interview_later_30:{str(e)}'})


@shared_task(queue='default', ignore_result=True)
def send_lark_business_message(message_type, data):
    try:
        LarkMessageCenter().send_business_message(data, message_type)
    except SoftTimeLimitExceeded:
        push_celery_hanging_message('send_lark_business_message')
    except Exception as e:
        LarkMessageCenter().send_other_backend_message(
            'celery任务错误', 'error', {'error': f'send_lark_business_message:{str(e)}'})


@shared_task(queue='default', ignore_result=True)
def send_coachee_unselect_message(chemical_interview_id):
    try:
        chemical_interview = ChemicalInterview2Coach.objects.get(id=chemical_interview_id)
        interview = chemical_interview.interview
        chemical_interview_module = chemical_interview.chemical_interview_module
        count = chemical_interview_module.max_interview_number - chemical_interview_module.coaches.filter(
            deleted=False, interview__isnull=False).count()
        # 发邮件
        state, content = WeChatMiniProgram().get_miniapp_qrcode(
            scene="refer=2",
            page='pages/index/index')

        if state:
            url_state, short_link = WeChatMiniProgram().get_url_link(
                'pages/index/index',
                f"refer=2")

            url = get_base_qr_code(content, 'coach_resume')
            message_type = 'chemical_interview_result'
            project_member = ProjectMember.objects.filter(
                user=interview.public_attr.target_user,
                project=interview.public_attr.project, deleted=False).first()
            manage_list = project_member.project.manager_list

            params = {
                "true_name": project_member.user.cover_name,
                "count": count,
                "manager_name": manage_list[0]['true_name'] if manage_list else '',
                "manager_email": manage_list[0]['email'] if manage_list else '',
                "manager_phone": manage_list[0]['phone'] if manage_list else '',
                "qr_code_url": url,
                "short_link": short_link,
            }
            email = [project_member.user.email]
            message_send_email_base(message_type=message_type, params=params, to_email=email, project_id=project_member.project_id,
                                   receiver_ids=project_member.user_id)

        # 发企业微信
        text = f'您在本项目中还有{count}次化学面谈机会，请在剩余的候选教练中选择一位您感兴趣的教练进行化学面谈。'
        page = "pages/index/index?refer=2"
        title = "预约化学面谈"
        msg, sender, external_user_id = get_project_manage_wx_user_id(
            interview.public_attr.project.pk,
            interview.public_attr.target_user_id,
            'send_coachee_unselect_message'
        )
        if msg:
            sender_user = WorkWechatUser.objects.filter(
                wx_user_id=sender,
                user__isnull=False,
                deleted=False
            ).first()
            if sender_user:
                send_work_wechat_coachee_notice.delay(
                    sender, text, external_user_id, page, title,
                    content_type='add_msg_template',
                    project_id=interview.public_attr.project.id,
                    project_name=interview.public_attr.project.name,
                    coachee_id=interview.public_attr.target_user_id,
                    coachee_name=interview.public_attr.target_user.cover_name,
                    forward_by=sender_user.user.cover_name,
                    forward_by_id=sender_user.user.id,
                )
    except SoftTimeLimitExceeded:
        push_celery_hanging_message('send_coachee_unselect_message')
    except Exception as e:
        LarkMessageCenter().send_other_backend_message(
            'celery任务错误', 'error', {'error': f'send_coachee_unselect_message:{str(e)}'})


@shared_task(queue='default', ignore_result=True)
def send_backend_error_message(func_name, content):
    try:
        LarkMessageCenter().send_other_backend_message(f'{func_name}错误', 'error', content)
        AliyunSlsLogLayout().send_third_api_log(content={'error': f'{content}'}, message=func_name)
    except SoftTimeLimitExceeded:
        push_celery_hanging_message('send_backend_error_message')
    except Exception as e:
        LarkMessageCenter().send_other_backend_message(
            'celery任务错误', 'error', {'error': f'send_backend_error_message:{str(e)}'})


@shared_task(queue='default', ignore_result=True)
def send_chemical_interview_not_appointment_coach_message():
    try:
        # 将每个化学面谈配置按项目分组
        # 组成{project_id: [chemical_interview_module_id1, chemical_interview_module_id2,chemical_interview_module_id1]}
        queryset = ChemicalInterviewModule.objects.values('project_member__project_id').filter(
            deleted=False, start_time__lte=datetime.datetime.now().date(),
            end_time__gte=datetime.datetime.now().date()).annotate(module_id=F('id'))
        project_chemical_interview_module_dict = {}
        for item in queryset:
            project_id = item['project_member__project_id']
            module_id = item['module_id']
            if project_id not in project_chemical_interview_module_dict:
                project_chemical_interview_module_dict[project_id] = []
            project_chemical_interview_module_dict[project_id].append(module_id)
        # 分组完成
        for project_id, module_ids in project_chemical_interview_module_dict.items():
            project = Project.objects.get(id=project_id)
            data = {"project_name": project.full_name}
            msg = ''
            for chemical_interview_module_id in module_ids:
                chemical_interview_module = ChemicalInterviewModule.objects.get(id=chemical_interview_module_id)
                selected = chemical_interview_module.coaches.filter(
                        chemical_interview_status=ChemicalInterviewStatusEnum.selected, deleted=False).first()
                if selected:  # 已选定
                    msg += f'客户{chemical_interview_module.project_member.user.cover_name}已选定教练' \
                           f'{selected.coach.user.cover_name}' if not msg else \
                           f'\n客户{chemical_interview_module.project_member.user.cover_name}已选定教练' \
                           f'{selected.coach.user.cover_name}'
                    continue
                # 已预约 查询已预约未反馈
                is_appointment = chemical_interview_module.coaches.filter(interview__isnull=False,
                    chemical_interview_status=ChemicalInterviewStatusEnum.not_feedback, deleted=False).first()
                if is_appointment:  # 已预约
                    msg += f'客户{chemical_interview_module.project_member.user.cover_name}已约教练' \
                           f'{is_appointment.coach.user.cover_name}' if not msg else \
                        f'\n客户{chemical_interview_module.project_member.user.cover_name}已约教练' \
                        f'{is_appointment.coach.user.cover_name}'
                    continue
                # 检查匹配教练类型  系统随机or自主选择

                coach_ids = list(chemical_interview_module.coaches.filter(
                    deleted=False).values_list('coach_id', flat=True)) \
                    if chemical_interview_module.coach_source == ChemicalInterviewCoachSourceEnum.system_random else \
                    list(ProjectCoach.objects.filter(project=project, resume__isnull=False, member__isnull=True,
                            project_group_coach__isnull=True, deleted=False).values_list('coach_id', flat=True))
                can_appointment, full_appointment, appointment = [], [], []
                for coach in Coach.objects.filter(id__in=coach_ids):
                    if chemical_interview_module.coaches.filter(coach=coach, interview__isnull=False,
                                                                deleted=False).exists():
                        appointment.append(coach)
                        continue
                    coach_offer = CoachOffer.objects.filter(project_offer__project_id=project_id, coach=coach,
                                                            status=CoachOfferStatusEnum.joined, deleted=False,
                                                            project_offer__deleted=False).first()
                    if coach_offer:
                        count = ChemicalInterview2Coach.objects.filter(
                            coach=coach, interview__isnull=False, deleted=False,
                            interview__public_attr__project=project). \
                            exclude(chemical_interview_status=ChemicalInterviewStatusEnum.unselected).count()
                        if coach_offer.max_customer_count and count >= coach_offer.max_customer_count:
                            full_appointment.append(coach)
                        else:
                            can_appointment.append(coach)
                    else:
                        can_appointment.append(coach)
                if coach_ids and len(full_appointment) == len(coach_ids):
                    msg += f'客户{chemical_interview_module.project_member.user.cover_name}的教练' \
                           f'{"，".join([c.user.cover_name for c in full_appointment])}都已被约满，请及时调整服务教练' \
                           if not msg else \
                           f'\n客户{chemical_interview_module.project_member.user.cover_name}的教练' \
                           f'{"，".join([c.user.cover_name for c in full_appointment])}都已被约满，请及时调整服务教练'
                    continue
                elif can_appointment:
                    msg += f'客户{chemical_interview_module.project_member.user.cover_name}可预约教练' \
                           f'{"，".join([c.user.cover_name for c in can_appointment])}' if not msg else \
                           f'\n客户{chemical_interview_module.project_member.user.cover_name}可预约教练' \
                           f'{"，".join([c.user.cover_name for c in can_appointment])}'
                    continue
                elif coach_ids and len(appointment) + len(full_appointment) == len(coach_ids):
                    if full_appointment:
                        msg += f'客户{chemical_interview_module.project_member.user.cover_name}的教练' \
                               f'{"，".join([c.user.cover_name for c in full_appointment])}都已被约满，请及时调整服务教练' \
                            if not msg else \
                            f'\n客户{chemical_interview_module.project_member.user.cover_name}的教练' \
                            f'{"，".join([c.user.cover_name for c in full_appointment])}都已被约满，请及时调整服务教练'
                    else:
                        msg += f'客户{chemical_interview_module.project_member.user.cover_name}的教练都已预约过，' \
                               f'请及时调整服务教练' if not msg else \
                               f'\n客户{chemical_interview_module.project_member.user.cover_name}的教练都已预约过，' \
                               f'请及时调整服务教练'
            if msg:
                data['msg'] = msg
                LarkMessageCenter().send_business_message(
                    data, LarkMessageTypeEnum.chemical_interview_customer_appointment)

    except SoftTimeLimitExceeded:
        push_celery_hanging_message('send_chemical_interview_not_appointment_coach_message')
    except Exception as e:
        LarkMessageCenter().send_other_backend_message(
            'celery任务错误', 'error', {'error': f'send_chemical_interview_not_appointment_coach_message:{str(e)}'})


@shared_task(queue='default', ignore_result=True)
def send_chemical_interview_reservation_count_message():
    try:
        project_ids = ChemicalInterviewModule.objects.filter(
            deleted=False, start_time__lte=datetime.datetime.now().date(),
            end_time__gte=datetime.datetime.now().date()).values_list('project_member__project_id', flat=True)
        for project_id in list(set(project_ids)):
            project = Project.objects.get(id=project_id)
            project_coach = ProjectCoach.objects.filter(
                project_id=project_id, status=ProjectCoachStatusEnum.adopt,
                resume__isnull=False, deleted=False)

            data = {"project_name": project.name, "content": ""}
            for item in project_coach:

                offer = CoachOffer.objects.filter(
                    project_offer__project=project, deleted=False, coach=item.coach, status=CoachOfferStatusEnum.joined
                ).first()
                max_customer_count = offer.max_customer_count if offer else 0

                interview_count = ChemicalInterview2Coach.objects.filter(
                    coach=item.coach, deleted=False, interview__public_attr__project=project,
                ).count()

                reservation_count = ChemicalInterview2Coach.objects.filter(
                    coach=item.coach, deleted=False, interview__public_attr__project=project, chemical_interview_status=ChemicalInterviewStatusEnum.selected
                ).count()

                data['content'] += f'{item.coach.user.cover_name} 最大服务客数{max_customer_count}，已被预约次数{interview_count}，已被选定{reservation_count} \n'
            # 有教练数据才发送通知
            if project_coach:
                LarkMessageCenter().send_business_message(
                    data, LarkMessageTypeEnum.chemical_interview_reservation_count)

    except SoftTimeLimitExceeded:
        push_celery_hanging_message('send_chemical_interview_reservation_count_message')
    except Exception as e:
        LarkMessageCenter().send_other_backend_message(
            'celery任务错误', 'error', {'error': f'send_chemical_interview_reservation_count_message:{str(e)}'})


@shared_task(queue='default', ignore_result=True)
def send_not_join_interview_message():
    try:
        now = datetime.datetime.now() - datetime.timedelta(minutes=6)
        interviews = ProjectInterview.objects.filter(
            deleted=False, public_attr__start_time__year=now.year,
            public_attr__start_time__month=now.month,
            public_attr__start_time__day=now.day,
            public_attr__start_time__hour=now.hour,
            public_attr__start_time__minute=now.minute,
            place_category=ProjectInterviewPlaceCategoryEnum.online_one_to_one
        ).exclude(public_attr__status=ATTR_STATUS_INTERVIEW_CANCEL).exclude(
            public_attr__project__isnull=True)
        if interviews.exists():
            for interview in interviews:

                message_data = {"project_name": interview.public_attr.project.full_name,
                                "coachee_name": interview.public_attr.target_user.cover_name,
                                "coach_name": interview.public_attr.user.cover_name,
                                "interview_time": f"{interview.public_attr.start_time.strftime('%Y.%m.%d %H:%M')}-"
                                                  f"{interview.public_attr.end_time.strftime('%H:%M')}",
                                "at_info": interview.public_attr.project.project_manager_feishu_at_info}

                meeting = interview.interview_meeting.filter(deleted=False).first()
                # 只处理腾讯会议场景
                if meeting:
                    meeting_details_status, data = work_wechat.WorkWechat().get_meeting_details(meeting.meeting_id)
                    # 记录data到阿里云sls
                    AliyunSlsLogLayout().send_third_api_log(
                        message='腾讯会议-获取会议详情',
                        content={
                            'interview_id': interview.id,
                            'meeting_id': meeting.meeting_id,
                            'meeting_details': data
                        },
                        user_id=interview.public_attr.user_id,
                        user_name=interview.public_attr.user.cover_name,
                        project_id=interview.public_attr.project_id
                    )
                    attendees = data.get('attendees')

                    # 加入会议的企业成员id列表
                    joined_member_id = []
                    member = attendees.get('member', [])
                    for member_item in member:
                        member_status = member_item.get('status')
                        if member_status == 1:
                            joined_member_id.append(member_item.get('userid'))

                    # 加入会议的外部联系人id列表
                    joined_tmp_external_user_id = []
                    tmp_external_user = attendees.get('tmp_external_user', [])
                    for tmp_external_user_item in tmp_external_user:
                        tmp_external_user_status = tmp_external_user_item.get('status')
                        if tmp_external_user_status == 1:
                            joined_tmp_external_user_id.append(tmp_external_user_item.get('tmp_external_userid'))
                    # 根据加入的数据判断提醒文本
                    if not joined_member_id and not joined_tmp_external_user_id:
                        message_data['msg'] = f'教练和客户都未加入会议，请提醒双方及时加入'
                        LarkMessageCenter().send_business_message(message_data,
                                                                  LarkMessageTypeEnum.chemical_interview_start)
                        return
                    if not joined_member_id:
                        message_data['msg'] = f'客户已进入会议，教练未进入，请提醒教练及时加入'
                        LarkMessageCenter().send_business_message(message_data,
                                                                  LarkMessageTypeEnum.chemical_interview_start)
                        return
                    if not joined_tmp_external_user_id:
                        message_data['msg'] = f'教练已进入会议，客户未进入，请提醒客户及时加入'
                        LarkMessageCenter().send_business_message(message_data,
                                                                  LarkMessageTypeEnum.chemical_interview_start)
                        return
    except SoftTimeLimitExceeded:
        push_celery_hanging_message('send_not_join_interview_message')
    except Exception as e:
        LarkMessageCenter().send_other_backend_message(
            'celery任务错误', 'error', {'error': f'send_not_join_interview_message:{str(e)}'})


@shared_task(queue='default', ignore_result=True)
def update_stakeholder_interview_coach_task_tmp_data(interview_id):
    try:
        stakeholder_interview_public.add_stakeholder_interview_coach_task_record(interview_id)
    except SoftTimeLimitExceeded:
        push_celery_hanging_message('update_stakeholder_interview_coach_task_tmp_data')
    except Exception as e:
        LarkMessageCenter().send_other_backend_message(
            'celery任务错误', 'error', {'error': f'update_stakeholder_interview_coach_task_tmp_data:{str(e)}'})


@shared_task(queue='default', ignore_result=True)
def send_stakeholder_create_interview_notice(stakeholder_interview):
    try:
        interview = stakeholder_interview.interview

        # 给教练发企业微信通知
        work_wechat_user = WorkWechatUser.objects.filter(
            wx_user_id__isnull=False, user_id=interview.public_attr.user.id, deleted=False).first()
        interview_time = f"{interview.public_attr.start_time.strftime('%Y.%m.%d %H:%M')}-{interview.public_attr.end_time.strftime('%H:%M')}"
        if work_wechat_user:
            user = work_wechat_user.user
            content_item = [
                {"key": "利益相关者", "value": stakeholder_interview.project_interested.interested.cover_name},
                {"key": "客户名称", "value": stakeholder_interview.project_interested.master.cover_name},
                {"key": "所属企业", "value": interview.public_attr.project.company.real_name},
                {"key": "所属项目", "value": interview.public_attr.project.name},
                {"key": "辅导时间", "value": interview_time},
            ]
            send_work_wechat_coach_notice.delay(
                work_wechat_user.wx_user_id,
                'stakeholder_interview_details',
                interview_id=interview.pk,
                stakeholder_name=interview.public_attr.target_user.cover_name,
                content_item=content_item,
                coach_name=user.cover_name,
                coach_id=work_wechat_user.user_id
            )

        # 给利益相关者发送邮件通知
        page = f"pages_interview/interview/detail"
        scene = f'id={interview.id}&refer=2'

        # 生成短链接
        url_state, short_link = WeChatMiniProgram().get_url_link(page, scene)
        url = get_qr_code(f'{settings.SITE_URL}landing/?p=/{page}&{scene}')
        manage_list = interview.public_attr.project.manager_list
        message_type = 'stakeholder_create_interview_notice'
        params = {
            'stakeholder_name': interview.public_attr.target_user.cover_name,
            'interview_time': interview_time,
            'coach_name': interview.public_attr.user.cover_name,
            "manager_name": manage_list[0]['true_name'] if manage_list else '',
            "manager_email": manage_list[0]['email'] if manage_list else '',
            "manager_phone": manage_list[0]['phone'] if manage_list else '',
            "qr_code_url": url,
            "short_link": short_link
        }
        to_email = [interview.public_attr.target_user.email]
        message_send_email_base(message_type, params, to_email, project_id=interview.public_attr.project_id,
                               receiver_ids=interview.public_attr.target_user_id)

        # 给利益相关者发送短信通知
        if interview.public_attr.target_user.phone:
            sms_scene = f'id={interview.id}&invite_code={settings.SMS_INVITE_CODE}&refer=3'
            state, url = WeChatMiniProgram().get_url_link("pages/landing/landing", f'p=/{page}&{sms_scene}')
            if state:
                SendSMS().send_stakeholder_create_interview(
                    interview.public_attr.user.cover_name,
                    interview_time, url,
                    interview.public_attr.target_user.phone,
                )
        params = {
            'project_name': interview.public_attr.project.name,
            'coachee_name': stakeholder_interview.project_interested.master.cover_name,
            'stakeholder_name': interview.public_attr.target_user.cover_name,
            'interview_time': interview_time,
        }
        # 给项目运营发通知
        LarkMessageCenter().send_business_message(
            params,  LarkMessageTypeEnum.stakeholder_interview_start)

    except SoftTimeLimitExceeded:
        push_celery_hanging_message('send_stakeholder_create_interview_notice')
    except Exception as e:
        LarkMessageCenter().send_other_backend_message(
            'celery任务错误', 'error', {'error': f'send_stakeholder_create_interview_notice:{str(e)}'})


@shared_task(queue='default', ignore_result=True)
def stakeholder_interview_coach_schedule():
    try:
        notice_date = datetime.datetime.now() - datetime.timedelta(days=1)
        coach_notice = UserNoticeRecord.objects.filter(
            type=NoticeTemplateTypeEnum.stakeholder_interview_schedule,
            channel=constant.ADMIN_SEND_WORK_WECHAT,
            project_id__isnull=False,
            created_at__gte=notice_date,
            deleted=False).order_by('project_id')
        sent_coach_list = []
        content = {}
        for item in coach_notice:
            # 查询利益相关者调研数据
            stakeholder_interview = StakeholderInterview.objects.filter(
                stakeholder_interview_module__coach_task__public_attr__user_id=item.user_id,
                project_interested__project=item.project,
                deleted=False).order_by('-created_at').first()
            if stakeholder_interview:

                # 如果当前项目已发送，跳过当前循环
                if [item.project.id, item.user_id] in sent_coach_list:
                    continue

                project_name = stakeholder_interview.project_interested.project.name
                duration = stakeholder_interview.stakeholder_interview_module.duration

                # 计算项目需要时间，同项目下的化学面谈数据取第一个。
                start_time = stakeholder_interview.stakeholder_interview_module.start_date
                end_time = stakeholder_interview.stakeholder_interview_module.end_date
                coachee_count = StakeholderInterview.objects.filter(
                    project_interested__project=item.project,
                    stakeholder_interview_module__coach_task__public_attr__user_id=item.user_id,
                    deleted=False,
                ).count()
                hours_diff = duration * coachee_count

                total_time, day_time_msg = coach_public.get_coach_available_time_period(
                    item.user_id, start_time, end_time, duration)

                msg = f'\n{item.user.cover_name}可预约时间{total_time}小时，项目需要{hours_diff}小时，教练提供的可预约时间: \n' \
                      f'{day_time_msg}'

                if project_name in content.keys():
                    content[project_name] += msg
                else:
                    content[project_name] = msg

                sent_coach_list.append([item.project.id, item.user_id])
        for k, v in content.items():
            data = {'project_name': k, 'content': v}
            LarkMessageCenter().send_business_message(data, LarkMessageTypeEnum.stakeholder_interview_coach_schedule)

    except SoftTimeLimitExceeded:
        push_celery_hanging_message('stakeholder_interview_coach_schedule')
    except Exception as e:
        LarkMessageCenter().send_other_backend_message(
            'celery任务错误', 'error', {'error': f'stakeholder_interview_coach_schedule:{str(e)}'})

@shared_task(queue='default', ignore_result=True)
def chemical_interview_coach_schedule():
    try:
        notice_date = datetime.datetime.now() - datetime.timedelta(days=1)
        coach_notice = UserNoticeRecord.objects.filter(
            type=NoticeTemplateTypeEnum.chemical_interview_schedule,
            channel=constant.ADMIN_SEND_WORK_WECHAT,
            project_id__isnull=False,
            created_at__gte=notice_date,
            deleted=False).order_by('project_id')

        sent_coach_list = []
        content = {}
        for item in coach_notice:
            # 查询化学面谈数据
            chemical_interview = ChemicalInterview2Coach.objects.filter(
                coach__user_id=item.user_id,
                chemical_interview_module__project_member__project=item.project,
                deleted=False).order_by('-created_at').first()
            if chemical_interview:

                # 如果当前项目已发送，跳过当前循环
                if [item.project.id, item.user_id] in sent_coach_list:
                    continue

                project_name = chemical_interview.chemical_interview_module.project_member.project.name
                duration = chemical_interview.chemical_interview_module.duration

                # 计算项目需要时间，同项目下的化学面谈数据取第一个。
                start_time = chemical_interview.chemical_interview_module.start_time
                end_time = chemical_interview.chemical_interview_module.end_time
                coachee_count = ChemicalInterview2Coach.objects.filter(
                    chemical_interview_module__project_member__project_id=item.project_id,
                    coach__user_id=item.user_id,
                    deleted=False,
                ).count()
                hours_diff = chemical_interview.chemical_interview_module.duration * coachee_count

                total_time, day_time_msg = coach_public.get_coach_available_time_period(
                    item.user_id, start_time, end_time, duration)

                msg = f'\n{chemical_interview.coach.user.cover_name}可预约时间{total_time}小时，项目需要{hours_diff}小时，教练提供的可预约时间: \n' \
                      f'{day_time_msg}'

                if project_name in content.keys():
                    content[project_name] += msg
                else:
                    content[project_name] = msg

                sent_coach_list.append([item.project.id, item.user_id])
        for k, v in content.items():
            data = {'project_name': k, 'content': v}
            LarkMessageCenter().send_business_message(data, LarkMessageTypeEnum.chemical_interview_coach_schedule)

    except SoftTimeLimitExceeded:
        push_celery_hanging_message('chemical_interview_coach_schedule')
    except Exception as e:
        LarkMessageCenter().send_other_backend_message(
            'celery任务错误', 'error', {'error': f'chemical_interview_coach_schedule:{str(e)}'})


@shared_task(queue='default', ignore_result=True)
def generate_lbi_personal_report():
    """
    LBI个人报告，到达报告配置的结束日期后，用当前已完成的测评结果生成报告
    """
    try:
        yesteryday = datetime.datetime.now().date() - datetime.timedelta(days=1)
        evaluation_modules = EvaluationModule.objects.filter(
            deleted=False, evaluation__code=LBI_EVALUATION, is_submit=False, submit_time__isnull=True,
            end_time=yesteryday
        )
        if evaluation_modules.exists():
            for evaluation_module in evaluation_modules:
                evaluation = evaluation_module.evaluation
                public_attr = PublicAttr.objects.filter(
                    project=evaluation_module.project_bundle.project,
                    user=evaluation_module.project_bundle.project_member.user,
                    target_user=evaluation_module.project_bundle.project_member.user,
                    type=ATTR_TYPE_EVALUATION_ANSWER
                ).first()
                if public_attr and evaluation:
                    save_lbi_evaluation(evaluation, public_attr)
    except SoftTimeLimitExceeded:
        push_celery_hanging_message('generate_lbi_personal_report')
    except Exception as e:
        LarkMessageCenter().send_other_backend_message(
            'celery任务错误', 'error', {'error': f'generate_lbi_personal_report:{str(e)}'})


@shared_task(queue='default', ignore_result=True)
def generate_project_progress_report_pdf(project, start_date, end_date, key):

    try:
        # 生成pdf
        pdf_url = get_user_pdf_url(
            project.pk, f"阶段报告-{project.name}{start_date.replace('-', '')}-{end_date.replace('-', '')}.pdf",
            pdf_type=PdfReportTypeEnum.project_progress_report.value, start_date=start_date, end_date=end_date)
        task_id = data_redis.get(key)
        if not task_id:
            push_wx_error_message(
                name=f'发送用户通知',
                level='warning', content={
                    'error': f'{key}:异步任务id丢失，请排查redis是否出现故障',
                    'details': '生成项目进度报告失败',
                    'data': {"project_id": project.pk, "start_date": start_date, "end_date": end_date},
                })
            return False, '异步任务id丢失'
        data_redis.set(task_id.decode(), f'True&{pdf_url}', ex=3600)
        return True, "生成成功"
    except SoftTimeLimitExceeded:
        push_celery_hanging_message('generate_project_progress_report_pdf')
    except Exception as e:
        LarkMessageCenter().send_other_backend_message(
            'celery任务错误', 'error', {'error': f'generate_project_progress_report_pdf:{str(e)}'})


@shared_task(queue='default', ignore_result=True)
def send_coach_settlement_info(coach, id_card, bank_card, front_binary_image, front_file_name, back_binary_image, back_file_name):
    try:
        name = coach.user.cover_name

        # 基础信息写入
        req = LarkDocument().send_excel_coach_info_value(
            [name, id_card, '', '', datetime.datetime.now().strftime('%Y.%m.%d %H:%M'), bank_card])
        table_range = req.get('tableRange')
        # table_range =  '7c36b1!A26:F26'
        # 7c36b1是表格标识，A26是追加数据所占单元格位置的左上角 F26是追加数据所占单元格位置的右下角，右下角为最后一行，优先使用右下角数据
        # table_range.split(':') -> [-1] -> [1:] = ['7c36b1!A26', 'F26'] -> 'F26' -> 26
        sheet_index = table_range.split(':')[-1][1:]

        # 获取的基础数据在表格中的下标后写入图片信息
        # 正面图写入
        LarkDocument().send_excel_value_image(front_binary_image, front_file_name, f'C{sheet_index}:C{sheet_index}')
        # 背面图写入
        LarkDocument().send_excel_value_image(back_binary_image, back_file_name, f'D{sheet_index}:D{sheet_index}')

        coach.is_settlement_info = True
        coach.save()
        return
    except SoftTimeLimitExceeded:
        push_celery_hanging_message('send_coach_settlement_info')
    except Exception as e:
        LarkMessageCenter().send_other_backend_message(
            'celery任务错误', 'error', {'error': f'send_coach_settlement_info:{str(e)}'})


@shared_task(queue='default', ignore_result=True)
def generate_business_order():
    try:
        business_order_public.backend_generate_business_order()
    except SoftTimeLimitExceeded:
        push_celery_hanging_message('generate_business_order')
    except Exception as e:
        LarkMessageCenter().send_other_backend_message(
            'celery任务错误', 'error', {'error': f'generate_business_order:{str(e)}'})


@shared_task(queue='default', ignore_result=True)
def send_coach_settlement_money_info(business_settlements):
    try:

        for business_settlement in business_settlements:
            business_orders = business_settlement.business_order.filter(deleted=False).first()
            if business_orders and business_orders.coach:
                work_wechat_user = WorkWechatUser.objects.filter(
                    wx_user_id__isnull=False, user_id=business_orders.coach.user.id, deleted=False ).first()

                if work_wechat_user:
                    send_work_wechat_coach_notice(
                        work_wechat_user.wx_user_id,
                        'coach_settlement_money_info',
                        coachee_name=business_orders.coach.user.cover_name,
                        coach_id=business_orders.coach.user.id,
                        money=str(Decimal(str(business_settlement.apply_withdrawal_amount)) / Decimal('100')),
                    )
    except SoftTimeLimitExceeded:
        push_celery_hanging_message('send_coach_settlement_money_info')
    except Exception as e:
        LarkMessageCenter().send_other_backend_message(
            'celery任务错误', 'error', {'error': f'send_coach_settlement_money_info:{str(e)}'})


@shared_task(queue='default', ignore_result=True)
def update_tenant_access_token(tenant_type):
    """
    更新第三方的access_token
    : param tenant_type: 第三方类型
        LarkDocumentToken  飞书表单token
    """
    try:
        if tenant_type == "LarkDocumentToken":
            LarkDocument().internal_access_token()
    except SoftTimeLimitExceeded:
        push_celery_hanging_message('update_tenant_access_token')
    except Exception as e:
        LarkMessageCenter().send_other_backend_message(
            'celery任务错误', 'error', {'error': f'update_tenant_access_token:{str(e)}'})


@shared_task(queue='default', ignore_result=True)
def update_activity_coach_status():
    """
    更新活动教练的状态,将超时未回复的标记成已过期
    """
    try:
        activity_coach = ActivityCoach.objects.filter(
            status=ActivityCoachStatusEnum.not_response.value,  # 未回复的
            activity__end_date__lt=datetime.datetime.now().date(),  # 当前日期大于结束日期
            deleted=False
        )
        if activity_coach.exists():
            activity_coach.update(status=ActivityCoachStatusEnum.expired.value)
    except SoftTimeLimitExceeded:
        push_celery_hanging_message('update_activity_coach_status')
    except Exception as e:
        LarkMessageCenter().send_other_backend_message(
            'celery任务错误', 'error', {'error': f'update_activity_coach_status:{str(e)}'})


@shared_task(queue='default', ignore_result=True)
def activity_coach_invite(activity, key):
    try:
        # 获取与该活动相关、尚未响应、且尚未发送邀请的教练
        activity_coaches = ActivityCoach.objects.filter(
            deleted=False,
            status__in=[ActivityCoachStatusEnum.not_response.value, ActivityCoachStatusEnum.not_invitation.value],
            activity=activity,
            is_send_invite=False
        )
        err_name = []  # 用于保存没有企业微信ID的教练真实名字
        # 构建消息内容
        content_item = [
            {"key": "活动名称", "value": activity.theme},
            {"key": "活动时间",
             "value": f"{activity.start_date.strftime('%Y.%m.%d')}~{activity.end_date.strftime('%Y.%m.%d')}"},
            {"key": "活动简介", "value": activity.brief},
        ]
        for item in activity_coaches.all():
            # 尝试获取与当前教练关联的企业微信用户
            work_wechat_user = WorkWechatUser.objects.filter(
                wx_user_id__isnull=False,
                user_id=item.coach.user.id,
                deleted=False
            ).first()
            if work_wechat_user:
                # 发送企业微信通知
                _, app_is_success = send_work_wechat_coach_notice(
                    work_wechat_user.wx_user_id,
                    'activity_invitation_coach',
                    activity_coach_id=str(item.pk),
                    content_item=content_item,
                    coach_id=work_wechat_user.user_id,
                    coach_name=work_wechat_user.user.cover_name,
                )
                # 根据发送结果更新教练的邀请状态
                if not app_is_success:
                    err_name.append(work_wechat_user.user.true_name)
                else:
                    item.is_send_invite=True
                    item.save()
            else:
                err_name.append(item.coach.user.true_name)

        task_id = data_redis.get(key)
        if not task_id:
            push_wx_error_message(
                name=f'发送用户通知',
                level='warning', content={
                    'error': f'{key}:异步任务id丢失，请排查redis是否出现故障',
                    'details': '发送教练邀请通知失败',
                    'data': {"activity_id": activity.pk},
                })
        if err_name:
            error = f"教练{','.join(err_name)}发送失败，请检查绑定的企业微信账号是否正确"
            data_redis.set(task_id.decode(), f'False&{error}', ex=3600)
            return False, error
        else:
            data_redis.set(task_id.decode(), f'True&发送成功', ex=3600)
            return True, '发送成功'
    except SoftTimeLimitExceeded:
        push_celery_hanging_message('update_activity_coach_status')
    except Exception as e:
        LarkMessageCenter().send_other_backend_message(
            'celery任务错误', 'error', {'error': f'update_activity_coach_status:{str(e)}'})


@shared_task(queue='default', ignore_result=True)
def batch_import_coach(file, key):
    """
    管理后台-批量导入教练
    """
    try:
        err_msg = None
        state, coaches_data, error = trainee_coaches_data.get_all_coaches(file)
        if not state:
            err_msg = f'模版错误:{error}'
        elif error:
            err_msg = '\n'.join([f"第{item.get('count')}行：{item.get('error')}" for item in error])
        else:
            work_wechat_error = []
            with transaction.atomic():
                for coach in coaches_data:
                    password = randomPassword()
                    pwd = aesencrypt(password)
                    if coach.get('user'):
                        user = coach.get('user')
                        user.true_name = coach.get('name')
                        user.email = coach.get('email')
                        user.password = pwd
                        user.save()
                    else:
                        user = User.objects.create(
                            name=coach.get('phone'),
                            password=pwd,
                            true_name=coach.get('name'),
                            email=coach.get('email'),
                            phone=coach.get('phone'))
                    coach_data = {"user_id": user.pk, "coach_type": int(coach['coach_type']),
                                  "user_class": coach['user_class']} if 'user_class' in coach \
                        else {"user_id": user.pk, "coach_type": int(coach['coach_type'])}
                    coach_data['personal_name'] = user.true_name
                    if int(coach['coach_type']) == CoachUserTypeEnum.student.value:
                        coach_data['is_share_resume'] = False
                    coach_obj = Coach.objects.create(**coach_data)
                    Resume.objects.create(coach_id=coach_obj.pk, is_customization=False)

                    # 获取教练微信小程序二维码数据，生成教练简历链接，发送通知
                    # 只有见习教练创建通知
                    if int(coach['coach_type']) == CoachUserTypeEnum.student.value:
                        state = utils.add_work_wechat_user(user, settings.WORK_WECHAT_COACH_DEPARTMENT_ID)
                        if state:
                            work_wechat_error.append(coach.get('name'))

            if work_wechat_error:
                err_msg = f"以下教练企业微信账号创建失败：{','.join(work_wechat_error)}"

        task_id = data_redis.get(key)
        if not task_id:
            push_wx_error_message(
                name=f'发送用户通知',
                level='warning', content={
                    'error': f'{key}:异步任务id丢失，请排查redis是否出现故障',
                    'details': '批量导入教练数据失败'
                })
        if error:
            data_redis.set(task_id.decode(), f'False&{err_msg}', ex=3600)
            return False, error
        else:
            data_redis.set(task_id.decode(), f'True&{err_msg or "导入成功"}', ex=3600)
            return True, '导入成功'

    except SoftTimeLimitExceeded:
        push_celery_hanging_message('batch_import_coach')
    except Exception as e:
        LarkMessageCenter().send_other_backend_message(
            'celery任务错误', 'error', {'error': f'batch_import_coach:{str(e)}'})


@shared_task(queue='default', ignore_result=True)
def send_personal_apply_internship():
    """
    申请成为见习教练通知
    """
    try:
        apply_instance_ids = list(PersonalApply.objects.filter(  # 创建新的个人申请记录
            type=PersonalApplyTypeEnum.internship.value, status=PersonalApplyStatusEnum.not_passed.value,
            created_at__date=pendulum.yesterday().date(), deleted=False
        ).values_list('pk', flat=True))
        if apply_instance_ids:
            push_lark_message_celery(apply_instance_ids, 'regular_add_personal_apply')
    except SoftTimeLimitExceeded:
        push_celery_hanging_message('send_personal_apply_internship')
    except Exception as e:
        LarkMessageCenter().send_other_backend_message(
            'celery任务错误', 'error', {'error': f'send_personal_apply_internship:{str(e)}'})


@shared_task(queue='default', ignore_result=True)
def send_change_observation_stakeholder_notice(change_observation, is_click_to_send=False):
    """
    发送改变观察反馈通知
    : param change_observation: 改变观察反馈对象
    : param is_click_to_send: 是否是点击发送 如果是点击发送之前发送过的用户需要再次通知
    ：return: 修改后的改变观察反馈对象
    """
    try:
        new_change_observation = change_observation_public.send_change_observation_stakeholder_notice(
            change_observation, is_click_to_send)
        return new_change_observation
    except SoftTimeLimitExceeded:
        push_celery_hanging_message('send_change_observation_stakeholder_notice')
    except Exception as e:
        LarkMessageCenter().send_other_backend_message(
            'celery任务错误', 'error', {'error': f'send_change_observation_stakeholder_notice:{str(e)}'})


@shared_task(queue='default', ignore_result=True)
def update_coach_resume_share_url(resume_id, head_image_url):
    """
    获取简历分享链接
    :param resume_id: 简历对象id
    :param head_image_url: 用户头像链接
    :return:
    """
    try:
        resume_public.update_resume_share_url(resume_id, head_image_url)
    except SoftTimeLimitExceeded:
        push_celery_hanging_message('update_coach_resume_share_url')
    except Exception as e:
        LarkMessageCenter().send_other_backend_message(
            'celery任务错误', 'error', {'error': f'update_coach_resume_share_url:{str(e)}'})


@shared_task(queue='default', ignore_result=True)
def send_business_api_sls_log(params):
    """
    发送业务日志到sls
    :param params: 日志的参数集 dict
    :return:
    """
    try:
        AliyunSlsLogLayout().send_business_api_log(**params)
    except SoftTimeLimitExceeded:
        push_celery_hanging_message('send_business_api_sls_log')
    except Exception as e:
        LarkMessageCenter().send_other_backend_message(
            'celery任务错误', 'error', {'error': f'send_business_api_sls_log:{str(e)}'})


@shared_task(queue='default', ignore_result=True)
def send_lark_coach_update_resume_msg():
    """飞书发送教练简历更新消息"""
    try:
        data_str = pendulum.yesterday().strftime('%Y_%m_%d')
        resume_public.send_date_coach_update_resume_msg(data_str)
    except SoftTimeLimitExceeded:
        push_celery_hanging_message('send_lark_coach_update_resume_msg')
    except Exception as e:
        LarkMessageCenter().send_other_backend_message(
            'celery任务错误', 'error', {'error': f'send_lark_coach_update_resume_msg:{str(e)}'})


@shared_task(queue='default', ignore_result=True)
def refund_timeout_activity_order_all():
    """
    退款超过可预约时间并且没预约辅导的的小程序活动订单
    """
    # 暂时关闭
    return
    try:
        data_str = pendulum.now().strftime('%Y-%m-%d')
        error = activity_public.refund_timeout_date_activity_order_all(data_str)
        if error:
            LarkMessageCenter().send_other_backend_message(
                '超时公益教练活动订单自动退款', 'error', error)

    except SoftTimeLimitExceeded:
        push_celery_hanging_message('refund_timeout_activity_order_all')
    except Exception as e:
        LarkMessageCenter().send_other_backend_message(
            'celery任务错误', 'error', {'error': f'refund_timeout_activity_order_all:{str(e)}'})


@shared_task(queue='default', ignore_result=True)
def project_end_date_check():
    """
    定时每天查询还有三天就结束的项目
    """
    try:
        end_data = pendulum.now().add(days=3).naive().date()
        project_public.send_projects_by_end_date_remind(end_data)
    except SoftTimeLimitExceeded:
        push_celery_hanging_message('project_end_date_check')
    except Exception as e:
        LarkMessageCenter().send_other_backend_message(
            'celery任务错误', 'error', {'error': f'project_end_date_check:{str(e)}'})


@shared_task(queue='default', ignore_result=True)
def download_chemical_interview(project_id, key):
    """下载化学面谈报告"""
    try:
        try:
            file_url = chemical_interview_public.download_chemical_interview(project_id)
        except Exception as e:
            task_id = data_redis.get(key)
            LarkMessageCenter().send_other_backend_message(
                '化学面谈下载失败', 'error', {'project_id': project_id, 'error': str(e)})
            data_redis.set(task_id.decode(), f'False&化学面谈下载失败', ex=3600)
            return False, '化学面谈下载失败'

        task_id = data_redis.get(key)
        if not task_id:
            LarkMessageCenter().send_other_backend_message(
                '化学面谈下载失败', 'error',
                {'error': f'{key}:异步任务id丢失，请排查redis是否出现故障',
                 'name': 'send_interview_detail_list',
                 'data': {'project_id': project_id}})
            return False, '异步任务id丢失'

        data_redis.set(task_id.decode(), f'True&{file_url}', ex=3600)
        return True, '化学面谈下载失败'

    except SoftTimeLimitExceeded:
        push_celery_hanging_message('download_chemical_interview')
    except Exception as e:
        LarkMessageCenter().send_other_backend_message(
            'celery任务错误', 'error', {'error': f'download_chemical_interview:{str(e)}'})


@shared_task(queue='default', ignore_result=True)
def download_stakeholder_interview_word(coach_task_id, key):
    """利益相关者访谈报告"""
    try:
        coach_task = CoachTask.objects.get(id=coach_task_id, deleted=False)
        if coach_task:
            work_wechat_user = WorkWechatUser.objects.get(
                user_id=coach_task.public_attr.user_id, wx_user_id__isnull=False, deleted=False)
            if work_wechat_user:

                try:
                    file, file_base_name = coach_task_public.stakeholder_task_data_to_word(coach_task_id)
                except Exception as e:
                    task_id = data_redis.get(key)
                    LarkMessageCenter().send_other_backend_message(
                        '利益相关者访谈报告下载失败', 'error', {'coach_task_id': coach_task_id, 'error': str(e)})
                    data_redis.set(task_id.decode(), f'False&利益相关者访谈报告下载失败', ex=3600)
                    return False, '利益相关者访谈报告下载失败'

                if not file:
                    task_id = data_redis.get(key)
                    LarkMessageCenter().send_other_backend_message(
                        '利益相关者访谈报告下载失败,未获取到word文件', 'error', {'coach_task_id': coach_task_id})
                    data_redis.set(task_id.decode(), f'False&利益相关者访谈报告下载失败', ex=3600)
                    return False, '利益相关者访谈报告下载失败'

                file_type = 'file'
                msg, media_id = work_wechat.WorkWechat().upload_media_file((file_base_name, file), file_type)
                if not msg:
                    return False, '企业微信发送教练辅导文件失败：未获取到素材id'

                content = {file_type: {'media_id': media_id}}
                file_is_success, file_msg = work_wechat.WorkWechat().send_message(work_wechat_user.wx_user_id, file_type, content)
                sls_data = {
                    'content': content,
                    'user_type': 'coach',
                    'user_id': coach_task.public_attr.user_id,
                    'user_name': coach_task.public_attr.user.true_name,
                    'file_msg': file_msg,
                    'event_id': 'Send_Coach_WechatMessage',
                    'message': '发送利益相关者访谈总结报告文件',
                }
                AliyunSlsLogLayout().send_work_wechat_log_data(**sls_data)
                if not file_is_success:
                    return False, '企业微信发送利益相关者访谈报告文件失败'

        task_id = data_redis.get(key)
        if not task_id:
            LarkMessageCenter().send_other_backend_message(
                '利益相关者访谈报告下载失败', 'error',
                {'error': f'{key}:异步任务id丢失，请排查redis是否出现故障',
                 'name': 'send_interview_detail_list',
                 'data': {'coach_task_id': coach_task_id}})
            return False, '异步任务id丢失'

        data_redis.set(task_id.decode(), f'True&ok', ex=3600)
        return True, '利益相关者访谈报告下载成功'

    except SoftTimeLimitExceeded:
        push_celery_hanging_message('download_stakeholder_interview_word')
    except Exception as e:
        LarkMessageCenter().send_other_backend_message(
            'celery任务错误', 'error', {'error': f'download_stakeholder_interview_word:{str(e)}'})


@shared_task(queue='default', ignore_result=True)
def to_c_interview_record_notice():
    """未填写辅导记录的用户发送消息提醒"""
    try:

        interviews = interview_public.get_to_c_not_filled_in_interview_record()

        for interview in interviews:
            work_wechat_user = WorkWechatUser.objects.filter(
                wx_user_id__isnull=False, user_id=interview.public_attr.user_id, deleted=False).first()
            if work_wechat_user:
                work_wechat_coachee_user = WorkWechatUser.objects.filter(
                    external_user_id__isnull=False, user_id=interview.public_attr.target_user_id, deleted=False).first()
                is_relevance, external_user_id = coach_public.work_wechat_coach_customer_info(
                    work_wechat_user, work_wechat_coachee_user, interview.public_attr.target_user)

                if is_relevance:
                    text = f'Hi，你可以记录昨天辅导的感受和行动计划等等，帮助落实行动和巩固教练效果，点击下面的链接填写吧。'
                    title = f"{interview.public_attr.start_time.strftime('%m月%d日 %H:%M')} 辅导记录填写入口"
                    page = f"/pages_interview/record/record_group"
                    scene = f'interview_id={interview.id}&refer=2'
                    send_work_wechat_coachee_notice(
                        work_wechat_user.wx_user_id, text, external_user_id, f"{page}?{scene}", title,
                        file_name="interview_record_fill.jpg",
                        coach_id=interview.public_attr.user.id,
                        coach_name=interview.public_attr.user.cover_name,
                        coachee_id=interview.public_attr.target_user.id,
                        coachee_name=interview.public_attr.target_user.cover_name
                    )

    except SoftTimeLimitExceeded:
        push_celery_hanging_message('to_c_interview_record_notice')
    except Exception as e:
        LarkMessageCenter().send_other_backend_message(
            'celery任务错误', 'error', {'error': f'to_c_interview_record_notice:{str(e)}'})


@shared_task(queue='default', ignore_result=True)
def create_project_interview_meeting(interview_id, admin_userid, title, start_time, duration,
        channel_type=InterviewMeetingChannelTypeEnum.tencent_meeting.value):
    """
    创建辅导会议
    """
    try:
        # 创建腾讯会议
        interview_public.create_interview_meeting(
            interview_id, admin_userid, title, start_time, duration, channel_type=channel_type)
    except SoftTimeLimitExceeded:
        push_celery_hanging_message('create_project_interview_meeting')
    except Exception as e:
        LarkMessageCenter().send_other_backend_message(
            'celery任务错误', 'error', {'error': f'create_project_interview_meeting:{str(e)}'})


@shared_task(queue='default', ignore_result=True)
def update_project_interview_meeting(status, meeting_id, title=None, start_time=None, duration=None):
    """
    修改辅导会议
    """
    try:
        interview_public.update_interview_meeting(status, meeting_id, title, start_time, duration)
        if status == UPDATE_INTERVIEW_MEETING:
            message = '企业微信-更新会议'
        elif status == CANCEL_INTERVIEW_MEETING:
            message = '企业微信-取消会议'
        else:
            return
        interview_meeting = InterviewMeeting.objects.filter(meeting_id=meeting_id).first()
        if interview_meeting:
            public_attr = interview_meeting.interview.public_attr
            AliyunSlsLogLayout().send_third_api_log(
                message=message,
                content={"interview_id": interview_meeting.interview_id,
                         "user_id": public_attr.user_id,
                         'start_time': public_attr.start_time.strftime('%Y-%m-%d %H:%M:%S'),
                         'end_time': public_attr.end_time.strftime('%Y-%m-%d %H:%M:%S'),
                         "title": title})
        else:
            LarkMessageCenter().send_other_backend_message(
                f'更新会议信息错误-会议不存在', 'error', f"status：{message}, meeting_id：{meeting_id}")

    except SoftTimeLimitExceeded:
        push_celery_hanging_message('update_project_interview_meeting')
    except Exception as e:
        LarkMessageCenter().send_other_backend_message(
            'celery任务错误', 'error', {'error': f'update_project_interview_meeting:{str(e)}'})


@shared_task(queue='default', ignore_result=True)
def update_work_wechat_user_info(work_wechat_user_id):
    """
    更新企业微信用户信息
    """
    try:
        user_info_state, user_info_msg = work_wechat.WorkWechat().get_contact_way_info(work_wechat_user_id)
        if user_info_state:
            qr_code = user_info_msg.get('qr_code')
            if qr_code:
                work_wechat_user = WorkWechatUser.objects.filter(
                    wx_user_id=work_wechat_user_id, deleted=False).update(qr_code=qr_code)
            else:
                LarkMessageCenter().send_other_backend_message(
                    f'更新企业微信用户二维码信息错误-二维码', 'error',
                    f"status：{user_info_state}, user_info_msg：{user_info_msg}")

        return True
    except SoftTimeLimitExceeded:
        push_celery_hanging_message('update_work_wechat_user_info')
    except Exception as e:
        LarkMessageCenter().send_other_backend_message(
            'celery任务错误', 'error', {'error': f'update_work_wechat_user_info:{str(e)}'})


@shared_task(queue='default', ignore_result=True)
def update_coach_tag(coach_id):
    """
    更新教练简历标签
    """
    try:
        coach_public.update_resume_to_coach_tag(coach_id)

    except SoftTimeLimitExceeded:
        push_celery_hanging_message('update_coach_tag')
    except Exception as e:
        LarkMessageCenter().send_other_backend_message(
            'celery任务错误', 'error', {'error': f'update_coach_tag:{str(e)}'})


@shared_task(queue='default', ignore_result=True)
def interview_record_update_customer_portrait(project_interview_id):
    """
    辅导记录问答同步更新客户信息
    project_interview_id 辅导记录id

    目前只有化学面谈需要更新，将对应问题的回答更新到对应的教练客户信息
    """
    try:
        project_interview = ProjectInterview.objects.filter(id=project_interview_id, deleted=False).first()
        if project_interview:
            if project_interview.type == ProjectInterviewTypeEnum.chemical_interview.value:
                customer_portrait_public.chemical_interview_update_customer_portrait(project_interview)
        return

    except SoftTimeLimitExceeded:
        push_celery_hanging_message('interview_record_update_customer_portrait')
    except Exception as e:
        LarkMessageCenter().send_other_backend_message(
            'celery任务错误', 'error', {'error': f'interview_record_update_customer_portrait:{str(e)}'})


@shared_task(queue='default', ignore_result=True)
def update_project_member_tag_task(user_id):
    """
    用户开屏信息收集，题目的回答同步创建&更新项目下，用户的需求标签
    project_member_id: 项目用户标识
    tag_ids_list: 根据收集的题目信息分析出的需要创建&更新的标签id列表。
    """
    try:
        user_additional_info_public.sync_info_to_tag(user_id)
    except SoftTimeLimitExceeded:
        push_celery_hanging_message('update_project_member_tag_task')
    except Exception as e:
        LarkMessageCenter().send_other_backend_message(
            'celery任务错误', 'error', {'error': f'update_project_member_tag_task:{str(e)}'})


@shared_task(queue='default', ignore_result=True)
def chemical_interview_feedback_notification(chemical_interview_id):
    try:
        chemical_interview_public.feedback_notification(chemical_interview_id)
    except SoftTimeLimitExceeded:
        push_celery_hanging_message('chemical_interview_feedback_notification')
    except Exception as e:
        LarkMessageCenter().send_other_backend_message(
            'celery任务错误', 'error', {'error': f'chemical_interview_feedback_notification:{str(e)}'})
        

@shared_task(queue='default', ignore_result=True)
def company_interview_remind():
    try:
        # Get company interviews starting in 30 minutes
        company_interview_public.send_30_remind()
    except SoftTimeLimitExceeded:
        push_celery_hanging_message('company_interview_remind')
    except Exception as e:
        LarkMessageCenter().send_other_backend_message(
            'celery任务错误', 'error', {'error': f'company_interview_remind:{str(e)}'})