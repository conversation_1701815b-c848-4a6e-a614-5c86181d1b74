from utils.miniapp_version_judge import compare_version
from wisdom_v2.models_file import Tag

lbi_add_user_data_sub = {
    '26': 'working_years',
    '27': 'education',
    '28': 'gender',
}


def get_project_user_data_collection_topic(is_lbi_user, is_user_info, mp=None):
    # 开屏用户信息收集
    # options_number是最大可选数量 0：不限制
    # sub 题目顺序，唯一标识。
    user_data_collection_topic = [
        {
            "topic": "你目前的工作覆盖哪些职能？",
            "options": ["整体业务", "市场", "销售", "研发/设计", "IT", "生产", "财务", "人力资源", "法务", "行政", "项目管理"],
            "options_number": 0,
            "is_write": False,
            "sub": 1
        },
        {
            "topic": "你当前的职位是？",
            "options": ["董事会成员", "C-LEVEL", "VP/副总裁", "事业部总经理/副总经理", "总监/副总监", "经理/高级经理", "一线经理/组长", "员工"],
            "options_number": 0,
            "is_write": False,
            "sub": 2
        },
        {
            "topic": "您的管理经验？",
            "options": ["1年以内", "1-5年", "5-10年", "10-15年", "15年以上"],
            "options_number": 1,
            "is_write": False,
            "sub": 3
        },
        {
            "topic": "你是否刚刚加入一家新公司或过渡到一个新角色？",
            "options": ["是", "否"],
            "options_number": 1,
            "is_write": False,
            "sub": 4
        },
        {
            "topic": "你是否正在协助组织进行重大变革计划？",
            "options": ["是", "否"],
            "options_number": 1,
            "is_write": False,
            "sub": 5
        },
        {
            "topic": "工作中正在发生一些对你影响很大的变化？",
            "options": ["是", "否"],
            "options_number": 1,
            "is_write": False,
            "sub": 6
        },
        {
            "topic": "在你的工作中有很大的不确定性和模糊性？",
            "options": ["是", "否"],
            "options_number": 1,
            "is_write": False,
            "sub": 7
        },
        {
            "topic": "最近你的管理范围或工作量明显增大？",
            "options": ["是", "否"],
            "options_number": 1,
            "is_write": False,
            "sub": 8
        },
        {
            "topic": "你或你的团队的业绩正在经历比较艰难的时期？",
            "options": ["是", "否"],
            "options_number": 1,
            "is_write": False,
            "sub": 9
        },
        {
            "topic": "你能够从遇到的大多数令人失望的人/事中恢复过来？",
            "options": ["非常符合", "符合", "中立", "不符合", "非常不符合"],
            "options_number": 1,
            "is_write": False,
            "sub": 10
        },
        {
            "topic": "你总是挑战自己和主动地寻求挑战性的工作？",
            "options": ["非常符合", "符合", "中立", "不符合", "非常不符合"],
            "options_number": 1,
            "is_write": False,
            "sub": 11
        },
        {
            "topic": "你持续学习和努力来不断完善自己？",
            "options": ["非常符合", "符合", "中立", "不符合", "非常不符合"],
            "options_number": 1,
            "is_write": False,
            "sub": 12
        },
        {
            "topic": "你对自己在工作中的表现评价为？",
            "options": ["非常满意", "满意", "一般", "不太满意", "非常不满意"],
            "options_number": 1,
            "is_write": False,
            "sub": 13
        },
        {
            "topic": "与组织中的其他团队相比，你对自己的团队绩效如何评价？",
            "options": ["领先", "稍好", "相差无几", "稍差", "落后"],
            "options_number": 1,
            "is_write": False,
            "sub": 14
        },
        {
            "topic": "目前你在管理方面最大的挑战是？",
            "options": ["激励下属", "绩效辅导", "有效授权", "果敢决策", "建立团队信任", "提供负面反馈", "向上管理", "多方协同", "战略远见", "系统思考", "领导变革", "跨文化管理", "开拓创新", "应对复杂局面", "人才培养", "其它"],
            "options_number": 0,
            "is_write": True,
            "sub": 15
        },
        {
            "topic": "目前你在工作状态方面最大的挑战是？",
            "options": ["压力管理", "工作与生活的平衡", "倦怠情绪", "职业规划", "时间管理", "自我突破", "其它"],
            "options_number": 0,
            "is_write": True,
            "sub": 16
        },
        # {
        #     "topic": "目前你最希望提升的领导力是？",
        #     "options": ["战略远见", "系统思考", "领导变革", "跨文化管理", "开拓创新", "应对复杂局面", "人才培养", "其它"],
        #     "options_number": 1,
        #     "is_write": True,
        #     "sub": 17
        # },
        {
            "topic": "基于当前的挑战和发展需求，你很清晰自己想实现什么进步？",
            "options": ["非常同意", "同意", "不确定", "不同意", "非常不同意"],
            "options_number": 1,
            "is_write": False,
            "sub": 18
        },
        {
            "topic": "对你而言，实现这个进步有多重要？",
            "options": ["非常重要", "重要", "不确定", "不重要", "完全不重要"],
            "options_number": 1,
            "is_write": False,
            "sub": 19
        },
        {
            "topic": "你了解教练是什么以及它是如何工作的？",
            "options": ["非常了解", "了解", "不确定", "不了解", "完全不了解"],
            "options_number": 1,
            "is_write": False,
            "sub": 20
        },
        {
            "topic": "你相信与教练一起工作可以实现有意义的改变或成长？",
            "options": ["非常同意", "同意", "不确定", "不同意", "非常不同意"],
            "options_number": 1,
            "is_write": False,
            "sub": 21
        },
        {
            "topic": "在这个项目中，你期待教练如何支持你进行探索和尝试？",
            "options": ["多给予支持和鼓励", "鼓励与挑战平衡", "多给予挑战和鞭策"],
            "options_number": 1,
            "is_write": False,
            "sub": 22
        },
        {
            "topic": "您希望和什么性别的教练一起工作？",
            "options": ["男性", "女性", "都可以"],
            "options_number": 1,
            "is_write": False,
            "sub": 23
        },
        {
            "topic": "现在是你开始与教练一起工作的好时机？",
            "options": ["非常同意", "同意", "不确定", "不同意", "非常不同意"],
            "options_number": 1,
            "is_write": False,
            "sub": 24
        },
        # {
        #     "topic": "如果你每周有额外的一小时用于专业或个人发展，你会把时间投资在？",
        #     "options": ["阅读", "听微课", "看视频", "冥想", "向别人请教", "自我反思", "上课", "其他"],
        #     "options_number": 1,
        #     "is_write": True,
        #     "sub": 25
        # }
    ]

    lbi_add_user_data = [
        {
            "topic": "你在当前公司工作多少年？",
            "options": ["不满1年", "1-3年", "3-5年", "5-10年", "10年以上"],
            "options_number": 1,
            "is_write": False,
            "sub": 26,
        },
        {
            "topic": "你的教育程度是？",
            "options": ["专科", "本科", "硕士", "博士"],
            "options_number": 1,
            "is_write": False,
            "sub": 27,
        },
        {
            "topic": "你的性别是？",
            "options": ["男", "女"],
            "options_number": 1,
            "is_write": False,
            "sub": 28,
        }
    ]

    # 将无限制数量的多选改为单选
    def update_options_number(topics):
        for topic in topics:
            if topic.get("options_number") == 0:
                topic["options_number"] = 1

    # 低于2.35.6版本不支持多选，默认单选
    if mp and compare_version(mp.get('version'), '2.35.7') < 0:
        update_options_number(user_data_collection_topic)

    if is_lbi_user:
        if is_user_info:
            user_data_collection_topic = lbi_add_user_data

        else:
            user_data_collection_topic += lbi_add_user_data
    return user_data_collection_topic


sub_to_parent_tag_name = {
    '1': '所负责的职能单元',
    '2': '职位',
    '3': '管理经验',
    '4': '当前工作环境',
    '5': '当前工作环境',
    '6': '当前工作环境',
    '7': '当前工作环境',
    '8': '当前工作环境',
    '9': '当前工作环境',
    '15': '管理挑战',
    '16': '工作状态挑战',
    # '17': '管理挑战',
    '22': '对教练的风格偏好',
    '23': '对教练的性别偏好',
}


def project_user_data_to_tag_list(data):

    all_tag_ids = []
    for item in data:
        # 获取题目标记
        sub = str(item.get('sub'))

        # 有就更新，没有的无需操作
        if sub in sub_to_parent_tag_name.keys():

            # 获取父级标签名称
            parent_tag_name = sub_to_parent_tag_name.get(sub)
            # 特殊题目，进行转换
            if sub in ['4', '5', '6', '7', '8', '9']:
                if item.get('count') == '是':
                    if sub == '4':
                        tag_names = ['加入新公司/过渡到新角色']
                    elif sub == '5':
                        tag_names = ['参与重大变革']
                    elif sub == '6':
                        tag_names = ['应对工作变化']
                    elif sub == '7':
                        tag_names = ['应对模糊和不确定']
                    elif sub == '8':
                        tag_names = ['管理范围/工作量短期激增']
                    elif sub == '9':
                        tag_names = ['业绩不佳']
                    else:
                        continue
                else:
                    continue
            elif sub == '23' and item.get('count') == '都可以':
                tag_names = ["男性", "女性"]
            else:
                if isinstance(item.get('count'), list):
                    tag_names = item.get('count')
                else:
                    tag_names = [item.get('count')]

            tag_id = Tag.objects.filter(
                parent__name=parent_tag_name, deleted=False, name__in=tag_names).values_list('id', flat=True)
            str_tag_id = [str(i) for i in tag_id]
            all_tag_ids += str_tag_id

    return all_tag_ids
