from rest_framework.pagination import PageNumberPagination, LimitOffsetPagination


class StandardResultsSetPagination(PageNumberPagination):
    """ 自写分页，继承PageNumberPagination """
    # 默认每页显示的数据条数
    page_size = 15
    page_size_query_param = 'page_size'
    page_query_param = 'page'
    max_page_size = 1000

    def paginate_queryset(self, queryset, request, view=None):
        """
        将queryset转为list 防止queryset对象切片后排序混乱
        """
        if not isinstance(queryset, list):
            queryset = list(queryset)
        return super().paginate_queryset(queryset, request)
