import io
import re

import openpyxl
from django.db.models import Avg, Sum

from utils import validate
from wisdom_v2.enum.service_content_enum import InterviewRecordTemplateQuestionRatingTypeEnum, \
    InterviewRecordTemplateQuestionTypeEnum
from wisdom_v2.enum.user_enum import CoachUserTypeEnum
from wisdom_v2.models import User, InterviewRecordTemplate, InterviewRecordTemplateAnswer, \
    InterviewRecordTemplateQuestion, ProjectInterviewRecord, Coach, ProjectMember, PersonalUser


def get_all_coaches(coaches_file):
    file_type = coaches_file.name.split('.')[1]
    if file_type != 'xlsx':
        return False, [], '模版格式错误,请上传xlsx格式文件'
    if coaches_file.size > 1048576:
        return False, [], '模版文件大于1MB请重新上传'
    file_xlsx = io.BytesIO(coaches_file.read())

    try:
        raw_trainee_coaches = openpyxl.load_workbook(file_xlsx)['教练']
    except Exception as e:
        return False, [], '模版文件格式错误,请使用正确的模板文件'
    phone_list = []
    email_list = []
    coaches = []
    error = []

    for pos, item in enumerate(raw_trainee_coaches.rows):
        if all(cell.value is None for cell in item):
            continue

        row = pos + 1
        name, phone, email, coach_type, user_class = item[0].value, item[1].value, item[2].value, \
                                                     item[3].value, item[4].value
        if pos == 1:
            if [name, phone, email, coach_type, user_class] != ["*姓名", "*手机号", "*邮箱", "*教练类型", "CITO毕业班级"]:
                return False, [], '模版文件格式错误,请使用正确的模板文件'

        if pos == raw_trainee_coaches.max_row:
            break
        if pos > 1:
            if name:
                name = str(name)
                if len(name) > 20:
                    error.append({'error': '姓名最多20个字', 'count': row})
            else:
                error.append({'error': '请输入姓名', 'count': row})

            personal_user = None
            if phone:
                phone = str(phone)
                if len(phone) > 11:
                    error.append({'error': '手机号最多11位', 'count': row})
                elif not phone.isdigit():
                    error.append({'error': '手机号应为数字', 'count': row})
                elif not re.match(r"^1[3456789][0-9]{9}$", phone):
                    error.append({'error': '手机号格式错误', 'count': row})
                else:
                    if phone not in phone_list:
                        phone_list.append(phone)
                    else:
                        error.append({'error': '该手机号在导入的教练数据中重复存在', 'count': row})
                user = User.objects.filter(phone=phone, deleted=False).first()
                if user:
                    if Coach.objects.filter(user=user, deleted=False).exists():
                        error.append({'error': '该手机号在已有教练中存在', 'count': row})
                    elif ProjectMember.objects.filter(user=user, deleted=False).exists():
                        error.append({'error': '该手机号在被教练者中存在', 'count': row})
                    elif PersonalUser.objects.filter(user=user, deleted=False).exists():
                        personal_user = PersonalUser.objects.filter(user=user, deleted=False).first().user
                    else:
                        error.append({'error': '该手机号已在用户中存在', 'count': row})
                if not personal_user and User.objects.filter(name=phone, deleted=False).exists():
                    error.append({'error': '手机号对应的用户名已存在', 'count': row})

            else:
                error.append({'error': '请输入手机号', 'count': row})

            if email:
                email = str(email)
                if ' ' in email:
                    error.append({'error': '邮箱中存在空格，请修改后再添加。', 'count': row})
                elif not validate.validate_email(email):
                    error.append({'error': '邮箱格式错误', 'count': row})
                elif len(email) > 50:
                    error.append({'error': '邮箱最多50字', 'count': row})
                elif User.objects.filter(email=email, deleted=False).exists():
                    error.append({'error': '该邮箱已在教练中存在', 'count': row})
                else:
                    if email not in email_list:
                        email_list.append(email)
                    else:
                        error.append({'error': '该邮箱已在教练中存在', 'count': row})
            else:
                error.append({'error': '请输入邮箱', 'count': row})

            if coach_type:
                if not str(coach_type).isdigit() or int(coach_type) not in CoachUserTypeEnum.get_describe_keys():
                    error.append({'error': '教练类型输入错误', 'count': row})
            else:
                error.append({'error': '请输入教练类型', 'count': row})

            if user_class:
                user_class = str(user_class)
                if len(user_class) > 10:
                    error.append({'error': '班次最多10位字符', 'count': row})
            data = {
                    "name": name,
                    "phone": phone,
                    "email": email,
                    "coach_type": coach_type,
                    "user": personal_user
                }
            if user_class:
                data['user_class'] = user_class
            coaches.append(data)
    return True, coaches, error


def get_trainee_coaches_user_score(user, score_type, target_user=False, is_customer_detail=False):
    interview_template = InterviewRecordTemplate.objects.filter(name='见习用1').first()
    interview_question = InterviewRecordTemplateQuestion.objects.filter(
        template=interview_template,
        type=4
    )
    if score_type == 'target_progress':
        interview_question = interview_question.filter(
            rating_type=InterviewRecordTemplateQuestionRatingTypeEnum.validity
        ).all()
    elif score_type == 'harvest_score':
        interview_question = interview_question.filter(
            rating_type=InterviewRecordTemplateQuestionRatingTypeEnum.Immersion
        ).all()
    elif score_type == 'satisfaction_score':
        interview_question = interview_question.filter(
            rating_type=InterviewRecordTemplateQuestionRatingTypeEnum.satisfaction
        ).all()
    if not is_customer_detail:
        if target_user:
            avg_score = InterviewRecordTemplateAnswer.objects.filter(
                question__in=interview_question,
                public_attr__user=user,
                public_attr__target_user=target_user,
            ).aggregate(score_avg=Avg('score'))['score_avg']
        else:
            avg_score = InterviewRecordTemplateAnswer.objects.filter(
                question__in=interview_question,
                public_attr__user=user,
            ).aggregate(score_avg=Avg('score'))['score_avg']
        return round(avg_score, 1) if avg_score else 0
    else:
        avg_score = InterviewRecordTemplateAnswer.objects.filter(
                question__in=interview_question,
                public_attr__user=user,
                public_attr__target_user=target_user,
            ).aggregate(score_avg=Avg('score'))['score_avg']
        all_score = InterviewRecordTemplateAnswer.objects.filter(
            question__in=interview_question,
            public_attr__target_user=target_user,
        ).aggregate(score_avg=Avg('score'))['score_avg']
        return round(avg_score, 1) if avg_score else 0, round(all_score, 1) if avg_score else 0
