#! /usr/bin/python
# ! -*- coding: utf-8 -*-

import sys
import os
import time
from .src.RtcTokenBuilder import RtcTokenBuilder, Role_Attendee

sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

appID = "01b00023a71c4b8db8ca707a6e5d070a"
appCertificate = "729668ffb7834f77a5d2799b7664f097"
uid = 0



def get_rtc_token(channel_name):
    expire_time_in_seconds = 259200
    current_timestamp = int(time.time())
    # 设置token72小时过期
    privilege_expired = current_timestamp + expire_time_in_seconds
    token = RtcTokenBuilder.buildTokenWithUid(appID, appCertificate, channel_name, uid, Role_Attendee, privilege_expired)
    return {'token': token, 'channel_name': channel_name, 'expired': privilege_expired}


