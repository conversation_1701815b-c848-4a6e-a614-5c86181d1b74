import logging

from django.conf import settings
from wechatpayv3 import WeChatPay, WeChatPayType


# 商户证书私钥，此文件不要放置在下面设置的CERT_DIR目录里。
with open(settings.APICLIENT_KEY) as f:
    PRIVATE_KEY = f.read()

# 接入模式：False=直连商户模式，True=服务商模式。
wxpay = WeChatPay(
    wechatpay_type=WeChatPayType.JSAPI,
    mchid=settings.MCHID,
    private_key=PRIVATE_KEY,
    cert_serial_no=settings.CERT_SERIAL_NO,
    apiv3_key=settings.APIV3_KEY,
    appid=settings.APP_ID,
    notify_url=settings.JSAPI_NOTIFY_URL,
    cert_dir=settings.CERT_DIR,
    partner_mode=settings.PARTNER_MODE,
    proxy=settings.PROXY)