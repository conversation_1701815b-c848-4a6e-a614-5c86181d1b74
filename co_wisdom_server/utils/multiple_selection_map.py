import copy
from utils.api_response import WisdomValidationError


MULTIPLE_SELECTION_MAP = {
    "language": {
        "content": [
            {
                "name": "中文",
                "id": 1
            },
            {
                "name": "英文",
                "id": 2
            },
            {
                "name": "其它",
                "id": 3
            }
        ],
        "value": [
        ],
        "other_text": None,
        "show_text": None
    },
    "work_industry": {
        "content": [
            {
                "name": "互联网/AI",
                "id": 1
            },
            {
                "name": "电子/通信/半导体",
                "id": 2
            },
            {
                "name": "服务业",
                "id": 3
            },
            {
                "name": "消费品/零售/批发",
                "id": 4
            },
            {
                "name": "房地产/建筑",
                "id": 5
            },
            {
                "name": "教育培训",
                "id": 6
            },
            {
                "name": "广告/传媒/文化/体育",
                "id": 7
            },
            {
                "name": "制造业",
                "id": 8
            },
            {
                "name": "专业服务",
                "id": 9
            },
            {
                "name": "制药/医疗",
                "id": 10
            },
            {
                "name": "汽车",
                "id": 11
            },
            {
                "name": "交通运输/物流",
                "id": 12
            },
            {
                "name": "能源/化工/环保",
                "id": 13
            },
            {
                "name": "金融",
                "id": 14
            },
            {
                "name": "农业/环保",
                "id": 15
            },
            {
                "name": "政府/非盈利机构/其它",
                "id": 16
            }
        ],

        "value": [
        ],
        "other_text": None,
        "show_text": None
    },
    "highest_position": {
        "content": [
            {
                "name": "组长/一线经理",
                "id": 1
            },
            {
                "name": "经理/高级经理",
                "id": 2
            },
            {
                "name": "总监/副总监",
                "id": 3
            },
            {
                "name": "事业部总经理/副总经理",
                "id": 4
            },
            {
                "name": "VP/副总裁",
                "id": 5
            },
            {
                "name": "C-level",
                "id": 6
            },
            {
                "name": "董事会成员",
                "id": 7
            },
            {
                "name": "其他",
                "id": 8
            }
        ],
        "value": [
        ],
        "other_text": None,
        "show_text": None
    },
    "enterprise_attributes": {
        "content": [
            {
                "name": "国企/央企",
                "id": 1
            },
            {
                "name": "合资企业",
                "id": 2
            },
            {
                "name": "外资企业",
                "id": 3
            },
            {
                "name": "大中型民营企业",
                "id": 4
            },
            {
                "name": "互联网企业",
                "id": 5
            },
            {
                "name": "创业型企业",
                "id": 6
            },
            {
                "name": "其它",
                "id": 7
            }
        ],
        "value": [
        ],
        "other_text": None,
        "show_text": None
    },
    "functional_module": {
        "content": [
            {
                "name": "财务",
                "id": 1
            },
            {
                "name": "行政",
                "id": 2
            },
            {
                "name": "IT",
                "id": 3
            },
            {
                "name": "法务",
                "id": 4
            },
            {
                "name": "市场",
                "id": 5
            },
            {
                "name": "销售",
                "id": 6
            },
            {
                "name": "生产",
                "id": 7
            },
            {
                "name": "研发 /  设计",
                "id": 8
            },
            {
                "name": "人力资源",
                "id": 9
            },
            {
                "name": "项目管理",
                "id": 10
            },
            {
                "name": "其他",
                "id": 11
            }
        ],
        "value": [
        ],
        "other_text": None,
        "show_text": None
    },
    "coach_course": {
        "content": [
            {
                "name": "CITO教练认证",
                "id": 1
            },
            {
                "name": "STC团队教练认证",
                "id": 2
            },
            {
                "name": "心理动力学高管教练认证",
                "id": 3
            },
            {
                "name": "5A领导力教练认证",
                "id": 4
            },
            {
                "name": "其它",
                "id": 5
            }
        ],
        "value": [
        ],
        "other_text": None,
        "show_text": None
    },
    "coach_industry": {
        "content": [
            {
                "name": "互联网/AI",
                "id": 1
            },
            {
                "name": "电子/通信/半导体",
                "id": 2
            },
            {
                "name": "服务业",
                "id": 3
            },
            {
                "name": "消费品/零售/批发",
                "id": 4
            },
            {
                "name": "房地产/建筑",
                "id": 5
            },
            {
                "name": "教育培训",
                "id": 6
            },
            {
                "name": "广告/传媒/文化/体育",
                "id": 7
            },
            {
                "name": "制造业",
                "id": 8
            },
            {
                "name": "专业服务",
                "id": 9
            },
            {
                "name": "制药/医疗",
                "id": 10
            },
            {
                "name": "汽车",
                "id": 11
            },
            {
                "name": "交通运输/物流",
                "id": 12
            },
            {
                "name": "能源/化工/环保",
                "id": 13
            },
            {
                "name": "金融",
                "id": 14
            },
            {
                "name": "农业/环保",
                "id": 15
            },
            {
                "name": "政府/非盈利机构/其它",
                "id": 16
            }
        ],

        "value": [
        ],
        "other_text": None,
        "show_text": None
    },
    "coach_customer_level": {
        "content": [
            {
                "name": "组长/一线经理",
                "id": 1
            },
            {
                "name": "经理/高级经理",
                "id": 2
            },
            {
                "name": "总监/副总监",
                "id": 3
            },
            {
                "name": "事业部总经理/副总经理",
                "id": 4
            },
            {
                "name": "VP/副总裁",
                "id": 5
            },
            {
                "name": "C-level",
                "id": 6
            },
            {
                "name": "董事会成员",
                "id": 7
            },
            {
                "name": "其他",
                "id": 8
            }
        ],
        "value": [
        ],
        "other_text": None,
        "show_text": None
    },
    "coach_enterprise_attributes": {
        "content": [
            {
                "name": "国企/央企",
                "id": 1
            },
            {
                "name": "合资企业",
                "id": 2
            },
            {
                "name": "外资企业",
                "id": 3
            },
            {
                "name": "大中型民营企业",
                "id": 4
            },
            {
                "name": "互联网企业",
                "id": 5
            },
            {
                "name": "创业型企业",
                "id": 6
            },
            {
                "name": "其它",
                "id": 7
            }
        ],
        "value": [
        ],
        "other_text": None,
        "show_text": None
    },
    "coach_domain": {
        "content": [
            {
                "name": "战略力",
                "id": 1
            },
            {
                "name": "决策力",
                "id": 2
            },
            {
                "name": "横向影响力",
                "id": 3
            },
            {
                "name": "全局观",
                "id": 4
            },
            {
                "name": "团队管理",
                "id": 5
            },
            {
                "name": "引领变革",
                "id": 6
            },
            {
                "name": "向上管理",
                "id": 7
            },
            {
                "name": "跨部门协同",
                "id": 8
            },
            {
                "name": "职场转身",
                "id": 9
            },
            {
                "name": "跨文化管理",
                "id": 10
            },
            {
                "name": "职业成长",
                "id": 11
            },
            {
                "name": "压力管理",
                "id": 12
            },
            {
                "name": "人际关系",
                "id": 13
            },
            {
                "name": "沟通能力",
                "id": 14
            },
            {
                "name": "情绪管理",
                "id": 15
            },
            {
                "name": "工作成就感",
                "id": 16
            },
            {
                "name": "创业",
                "id": 17
            },
            {
                "name": "跨行业",
                "id": 18
            },
            {
                "name": "其它",
                "id": 19
            }
        ],
        "value": [
        ],
        "other_text": None,
        "show_text": None
    },
    "psychological_counseling_certification": {
        "content": [
            {
                "name": "心理咨询师三级",
                "id": 1
            },
            {
                "name": "心理咨询师二级",
                "id": 2
            },
            {
                "name": "其它",
                "id": 3
            }
        ],
        "value": [
        ],
        "other_text": None,
        "show_text": None
    },
}


def get_multiple_selection_detail(fields, value):
    data = copy.deepcopy(MULTIPLE_SELECTION_MAP.get(fields))
    if not data:
        return None
    content = [dic['name'] for dic in data['content']]
    if not value:
        return data
    for v in value:
        for dic in data['content']:
            if v == dic['name']:
                data['value'].append(dic['id'])
            elif v not in content and data['content'][-1]['id'] not in data['value']:
                data['value'].append(data['content'][-1]['id'])
    other_text = list(set(value).difference(set(content)))
    other_text = other_text[0] if other_text else None
    if not other_text and content[-1] in value:
        other_text = content[-1]
    show_text = ",".join(value) if value else None
    data['other_text'] = other_text
    data['show_text'] = show_text
    return data


def check_multiple_selection_data(fields, value):
    try:
        get_multiple_selection_detail(fields, value)
        return 'correct'
    except:
        return 'error'



def validate_multiple_selection(attrs):
    if 'language' in attrs.keys():
        result = check_multiple_selection_data('language', attrs.get('language'))
        if result == 'error':
            raise WisdomValidationError('语言参数错误')
    if 'work_industry' in attrs.keys():
        result = check_multiple_selection_data('work_industry', attrs.get('work_industry'))
        if result == 'error':
            raise WisdomValidationError('工作行业参数错误')

    if 'highest_position' in attrs.keys():
        result = check_multiple_selection_data('highest_position', attrs.get('highest_position'))
        if result == 'error':
            raise WisdomValidationError('工作最高职位参数错误')

    if 'enterprise_attributes' in attrs.keys():
        result = check_multiple_selection_data('enterprise_attributes', attrs.get('enterprise_attributes'))
        if result == 'error':
            raise WisdomValidationError('工作企业属性参数错误')

    if 'functional_module' in attrs.keys():
        result = check_multiple_selection_data('functional_module', attrs.get('functional_module'))
        if result == 'error':
            raise WisdomValidationError('工作职能模块参数错误')

    if 'coach_course' in attrs.keys():
        result = check_multiple_selection_data('coach_course', attrs.get('coach_course'))
        if result == 'error':
            raise WisdomValidationError('教练课程参数错误')

    if 'coach_industry' in attrs.keys():
        result = check_multiple_selection_data('coach_industry', attrs.get('coach_industry'))
        if result == 'error':
            raise WisdomValidationError('教练行业参数错误')

    if 'coach_customer_level' in attrs.keys():
        result = check_multiple_selection_data('coach_customer_level', attrs.get('coach_customer_level'))
        if result == 'error':
            raise WisdomValidationError('客户最高级别参数错误')

    if 'coach_enterprise_attributes' in attrs.keys():
        result = check_multiple_selection_data('coach_enterprise_attributes',
                                               attrs.get('coach_enterprise_attributes'))
        if result == 'error':
            raise WisdomValidationError('教练企业属性参数错误')

    if 'coach_domain' in attrs.keys():
        result = check_multiple_selection_data('coach_domain', attrs.get('coach_domain'))
        if result == 'error':
            raise WisdomValidationError('教练领域参数错误')

    if 'psychological_counseling_certification' in attrs.keys():
        result = check_multiple_selection_data('psychological_counseling_certification',
                                               attrs.get('psychological_counseling_certification'))
        if result == 'error':
            raise WisdomValidationError('心理咨询认证参数错误')

    return attrs
