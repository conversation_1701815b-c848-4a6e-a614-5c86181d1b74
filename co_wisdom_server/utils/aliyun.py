import oss2

from utils.message.lark_message import LarkMessageCenter


class AliYun(object):

    def __init__(self, bucket, is_internal=False):
        self.location = 'oss-cn-beijing-internal.aliyuncs.com' if is_internal else 'oss-cn-beijing.aliyuncs.com'
        self.auth = oss2.Auth(
            'LTAI5tQ5BdCzTkEZpdyWdiLX',
            '******************************')
        self.bucket = oss2.Bucket(
            self.auth,
            self.location,
            bucket)

    #  校验链接有没有数据
    def exist_file(self, key):
        exist = self.bucket.object_exists(key)
        return exist

    # 生成对应请求方式链接
    def oss_url(self, key, method='GET', ex=1000, headers=None):
        return self.bucket.sign_url(
            method, key, ex, headers=headers).replace('http', 'https')

    # 获取文件
    def get_file(self, key):
        try:
            content = self.bucket.get_object(key)
            return True, content.read()
        except Exception as e:
            LarkMessageCenter().send_other_backend_message(
                '获取阿里云文件出错', 'error', {'key': key, 'error': str(e)})
            return False, e

    # 上传文件
    def send_file(self, key, content, headers=None):
        try:
            self.bucket.put_object(key, content, headers=headers)
            return True,
        except Exception as e:
            LarkMessageCenter().send_other_backend_message(
                '文件上传阿里云出错', 'error', {'key': key, 'error': str(e), 'headers': headers})
            return False, e

    # 查询指定目录文件列表
    def get_oss_file_obj(self, prefix):
        obj = oss2.ObjectIteratorV2(self.bucket, prefix)
        return obj

    # 删除指定文件
    def del_oss2_file(self, key):
        self.bucket.delete_object(key)
