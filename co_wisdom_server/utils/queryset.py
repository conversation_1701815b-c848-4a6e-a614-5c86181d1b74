import random

def combine_values(queryset, pk_field, combine_key, remain_keys=[], new_fields_name=[]):
    if not queryset or queryset.count() == 0:
        return queryset
    ret = []
    pk_list = []
    for item in queryset:
        # return queryset if item does not have remain field
        if remain_keys and remain_keys[0] not in item:
            return queryset
        if item.get(pk_field) not in pk_list:
            pk_list.append(item.get(pk_field))
            new_item = dict(item)

            combine_dict = {}
            for index, key in enumerate(remain_keys):
                combine_dict[new_fields_name[index]] = item.get(key)
                del new_item[key]
            new_item[combine_key] = [combine_dict]
            ret.append(new_item)
        else:
            new_item = dict(ret[-1])
            combine_dict = {}
            for index, key in enumerate(remain_keys):
                combine_dict[new_fields_name[index]] = item.get(key)
            new_item[combine_key].append(combine_dict)
            ret[-1] = new_item

    return ret

def distinct(queryset, field_list):
    if not field_list or len(field_list) == 0 or not queryset or queryset.count() == 0:
        return queryset
    for field_name in field_list:
        value_list = []
        new_list = []
        for item in queryset.all():
            value = item.__getattribute__(field_name)
            if value not in value_list:
                value_list.append(value)
                new_list.append(item.pk)
        queryset = queryset.filter(pk__in=new_list)
    return queryset


def multiple_field_distinct(queryset, field_list):
    lst = []
    pk_lst = []
    for item in queryset:
        args = {}
        for field in field_list:
            if '.' in field:
                value_1 = item.__getattribute__(field.split('.')[0])
                value_2 = value_1.__getattribute__(field.split('.')[1])
                args[field] = value_2
            else:
                value_1 = item.__getattribute__(field)
                args[field] = value_1
        if args not in lst:
            lst.append(args)
            pk_lst.append(item.pk)
    queryset = queryset.filter(pk__in=pk_lst)
    return queryset


def randomSet(queryset, number=3):
    if queryset and queryset.count() <= 3:
        return queryset
    count = queryset.count()
    random_list = []
    while len(random_list) < number:
        item = random.randint(0, count-1)
        pk = queryset[item].pk
        if pk not in random_list:
            random_list.append(pk)
    res = queryset.filter(pk__in=random_list)
    return res

