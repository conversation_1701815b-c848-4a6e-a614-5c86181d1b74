class PermissionNode:
    '''
        将自继承权限数据组装
    '''
    def __init__(self, permission, children):
        self.permission = permission
        self.children = children

    def serialize(self):
        result = {'id': self.permission.id, 'name': self.permission.name, 'text': self.permission.text,
                  'order': self.permission.order}
        submenus = []
        elements = []
        operations = []
        for x in self.children:
            chlid, chlid_operations = x.serialize()
            if x.permission.is_element:
                elements.append(chlid)
            else:
                submenus.append(chlid)
        result['submenus'] = submenus
        result['elements'] = elements
        return result, operations


class PermissionTree:
    '''
        组装权限树
    '''

    def __init__(self, permissions):
        self.roots = []
        self._root_dict = {}
        self._id_dict = {}
        permission_set = set(permissions)
        self._contruct([PermissionNode(x, []) for x in permission_set])

    def _contruct(self, permission_nodes):
        parents = []
        for permission_node in permission_nodes:
            self._id_dict[permission_node.permission.id] = permission_node
        for permission_node in permission_nodes:

            if permission_node.permission.parent is None:
                if self._root_dict.get(permission_node.permission.id) is None:
                    self.roots.append(permission_node)
                    self._root_dict[
                        permission_node.permission.id] = permission_node
            else:
                if permission_node.permission.parent.id in self._id_dict:
                    self._id_dict[
                        permission_node.permission.parent.id
                    ].children.append(permission_node)
                else:
                    parents.append(permission_node.permission.parent)
        if len(parents) > 0:
            parents = set(parents)
            parent_nodes = [
                self._root_dict[x.id] if self._root_dict.get(x.id) is not None
                else PermissionNode(x, []) for x in parents]
            parent_dict = {x.permission.id: x for x in parent_nodes}
            for permission_node in permission_nodes:

                if (permission_node.permission.parent and
                        permission_node.permission.parent.id not in
                        self._id_dict):
                    parent_dict[
                        permission_node.permission.parent.id
                    ].children.append(permission_node)
            self._contruct(parent_nodes)


def get_permission(permissions):
    """
        根据传入权限数据组装权限数据结构
    """
    permission_tree = PermissionTree(permissions)
    permissions_tups = [x.serialize() for x in permission_tree.roots]
    permissions = [tup[0] for tup in permissions_tups]
    for permission in permissions:
        permission.pop('elements')
        if permission['submenus']:
            for info in permission['submenus']:
                info.pop('submenus')
                if info['elements']:
                    for e in info['elements']:
                        e.pop('submenus')
                        e.pop('elements')
    return permissions
