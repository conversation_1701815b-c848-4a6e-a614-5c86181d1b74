import os
import time
from django.test import TestCase

# Create your tests here.

from .task import save_content


class TestReadFileTest(TestCase):
    def setUp(self):
        print('====== Test Start ========')

    def tearDown(self):
        print('====== Test End ========')

    def test_async(self):
        path = '/root/test.py'
        if os.path.exists(path):
            os.remove(path)
        a, b = 2, 4
        save_content.delay(a, b, path)
        time.sleep(3)
        with open(path, 'r') as f:
            res = f.read()
            self.assertEqual(res, str(a+b))

    def test_celery(self):
        path = '/root/today.py'
        with open(path, 'r') as f:
            today = f.read()
            self.assertEqual(today, time.strftime("%Y-%m-%d", time.localtime()))
