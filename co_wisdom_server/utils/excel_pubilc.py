import io
import openpyxl
from openpyxl.drawing.image import Image
from openpyxl.styles import Alignment, Font
from openpyxl.utils import get_column_letter


def save_excel(data, sheet_name, title=None):
    """
    将给定的数据保存到一个Excel文件中，并在顶部添加一个占多个单元格并居中显示的标题。

    参数:
    - data: 一个列表的列表，其中外层列表的每个元素代表Excel中的一行，内层列表的每个元素代表该行中的单元格。
    - sheet_name: 字符串，用于指定创建的工作表的名称。
    - title: 可选，一个字符串，如果提供，将作为占多个单元格的标题，并居中显示在表格顶部。

    返回:
    - file: 一个io.BytesIO对象，包含生成的Excel文件的内容。这允许在内存中操作文件，而无需将其写入磁盘。

    功能描述:
    - 如果提供了title，首先合并顶部的单元格并将标题文本设置到合并后的单元格中，同时确保文本居中显示。
    - 然后，遍历data中的每个元素（每行数据），并使用sheet.append(item)将其添加到工作表中。
    - 使用wb.save(file)方法将工作簿保存到前面创建的二进制流中。
    - 最后，返回包含Excel文件内容的二进制流对象。
    """
    file = io.BytesIO()
    wb = openpyxl.Workbook()
    sheet = wb.active
    sheet.title = sheet_name

    if title:
        # 根据数据计算合并单元格的最大范围
        column_count = max(len(row) for row in data)
        # 使用get_column_letter来获取列号的字母表示
        column_letter = get_column_letter(column_count)
        # 调整合并范围以适应标题
        merge_range = f"A1:{column_letter}1"
        # 合并单元格
        sheet.merge_cells(merge_range)
        # 读取左上角第一个单元格
        merged_cell = sheet.cell(row=1, column=1)
        # 将标题文本放入第一行第一列的单元格中
        merged_cell.value = title
        # 标题文本在合并单元格中水平和垂直居中
        merged_cell.alignment = Alignment(horizontal="center", vertical="center")
        # 调整行高使标题占两行高度
        sheet.row_dimensions[1].height = 40

    # 添加数据行
    for item in data:
        sheet.append(item)

    wb.save(file)
    file.seek(0)
    return file


def calculate_row_height(text):
    """计算行高，基于\n的数量"""
    base_height = 15  # 基础行高
    extra_height_per_line = 12.75  # 每增加一行的额外高度
    return base_height + ((text.count('\n') + 2) * extra_height_per_line)


def calculate_column_width(max_len):
    """根据文本内容估算列宽"""
    # 假设平均每个字符的宽度为2.5
    char_size = 2.5
    return max(10, char_size * len(max_len))


def wrap_text(text, width):
    """将文本按固定宽度换行"""
    return '\n'.join([text[i:i+width] for i in range(0, len(text), width)])


def save_project_coach_report_excel(data):
    """
    将项目辅导报告数据保存到Excel文件中。

    每项数据创建一个新的工作表，工作表的名称为用户名称。在每个工作表中，函数首先填充成长目标数据，
    然后填充项目辅导数据。成长目标数据被合并并居中，项目访谈数据则根据具体内容调整行高和列宽。

    :param data: 包含多个用户的成长目标和项目访谈数据的列表。每个元素是一个字典，
                 包含'user_name'键（用户名称），'growth_goals_data'键（成长目标数据），
                 和'project_interview_data'键（项目访谈数据）。
    :return: 一个BytesIO对象，包含生成的Excel文件内容。
    """

    file = io.BytesIO()
    wb = openpyxl.Workbook()
    first_sheet = True

    for item in data:
        if first_sheet:
            sheet = wb.active
            first_sheet = False
        else:
            sheet = wb.create_sheet()

        user_name = item.get('user_name')
        sheet.title = user_name[:31]

        # 加载并插入图片
        img = Image(open('static/cw_logo.png', 'rb'))
        # 获取图片的像素高度
        width, height = img.width, img.height

        # 图片缩放
        height = int(height/2)
        width = int(width/2)
        img.width = width
        img.height = height

        px_height = height
        # 转换因子，从像素到磅（可能需要根据实际效果调整）
        conversion_factor = 0.75
        # 计算行高（在Excel中以磅为单位）
        row_height = px_height * conversion_factor

        # 设置A1单元格行的高度
        sheet.row_dimensions[1].height = row_height
        sheet.add_image(img, 'A1')
        sheet.merge_cells(start_row=1, start_column=1, end_row=1, end_column=7)

        # 在第三行写用户名
        username_cell = sheet.cell(row=3, column=1)
        username_cell.value = f'{user_name}的教练报告'
        username_cell.font = Font(bold=True)
        username_cell.alignment = Alignment(horizontal='left', vertical='center')
        sheet.row_dimensions[3].height = 30
        sheet.merge_cells(start_row=3, start_column=1, end_row=3, end_column=7)

        row_index = 5  # 由于图片的插入和名称，数据开始的索引从4开始

        # 计算第二、第三、第四、第五列的最大文本长度
        max_length_columns = [10, 0, 0, 0]  # 对应四列

        project_interview_data = item.get('project_interview_data', [])
        for row in project_interview_data[1:]:
            for i, cell_value in enumerate(row[2:5], start=1):  # 从第二个元素开始，到第五个元素
                cell_value_count = max((len(line) for line in cell_value.split('\n')), default=0)
                max_length_columns[i] = max(max_length_columns[i], cell_value_count)

        # 根据最长文本调整第二、第三、第四、第五列的宽度
        for i, max_length in enumerate(max_length_columns, start=2):  # 从第二列开始
            if max_length > 0:
                sheet.column_dimensions[get_column_letter(i)].width = calculate_column_width('M' * max_length)

        # 成长目标数据
        growth_goals_data = item.get('growth_goals_data')
        if growth_goals_data and len(growth_goals_data) > 1:
            # 写入标题到A1单元格并居中对齐
            title_cell = sheet.cell(row=row_index, column=1)
            title_cell.value = growth_goals_data[0]
            title_cell.font = Font(bold=True)  # 设置字体加粗
            title_cell.alignment = Alignment(horizontal='center', vertical='center')

            # 合并所有成长目标数据为一个字符串，并将其写入B单元格
            goals_str = '\n'.join([f'·{goal[0]}' for goal in growth_goals_data[1:]])
            goals_cell = sheet.cell(row=row_index, column=2)
            goals_cell.value = goals_str
            # 设置单元格的文本水平居左、垂直居中
            centered_left_alignment = Alignment(horizontal='left', vertical='center', wrap_text=True)
            goals_cell.alignment = centered_left_alignment

            # 横向合并B1单元格及其后的五个单元格，共六个单元格
            sheet.merge_cells(start_row=row_index, start_column=2, end_row=row_index, end_column=7)

            # 调整行高
            sheet.row_dimensions[row_index].height = calculate_row_height(goals_str)

            row_index += 3  # 成长目标数据写入后空两行

        # 项目访谈数据
        project_interview_data = item.get('project_interview_data')
        if project_interview_data and len(project_interview_data) > 1:
            # 写入标题行
            for col, title in enumerate(project_interview_data[0], start=1):
                cell = sheet.cell(row=row_index, column=col)
                cell.value = title
                cell.font = Font(bold=True)
                cell.alignment = Alignment(horizontal='center', vertical='center')
            row_index += 1  # 移动到下一行

            # 写入数据行
            max_line_count = 1  # 记录C或E列中的最大行数
            for row_data in project_interview_data[1:]:
                max_line_count = max(max_line_count, row_data[2].count('\n') + 1, row_data[4].count('\n') + 1)
                for col, value in enumerate(row_data, start=1):
                    cell = sheet.cell(row=row_index, column=col)
                    cell.value = value
                    alignment = Alignment(horizontal='center' if col in [1, 2] else 'left', vertical='center',
                                          wrap_text=True)
                    cell.alignment = alignment
                row_index += 1

            # 根据C或E列中的值调整行高
            for i in range(row_index - len(project_interview_data[1:]), row_index):
                sheet.row_dimensions[i].height = calculate_row_height(
                    project_interview_data[1:][i - row_index + len(project_interview_data[1:])][2])

    wb.save(file)
    file.seek(0)
    return file


def save_chemical_interview_excel(data, sheet_name):
    file = io.BytesIO()
    wb = openpyxl.Workbook()
    sheet = wb.active
    sheet.title = sheet_name

    # 加载并插入图片
    img = Image(open('static/cw_logo.png', 'rb'))
    # 获取图片的像素高度
    width, height = img.width, img.height

    # 图片缩放
    height = int(height/2)
    width = int(width/2)
    img.width = width
    img.height = height

    px_height = height
    # 转换因子，从像素到磅（可能需要根据实际效果调整）
    conversion_factor = 0.75
    # 计算行高（在Excel中以磅为单位）
    row_height = px_height * conversion_factor

    # 设置A1单元格行的高度
    sheet.row_dimensions[1].height = row_height
    sheet.add_image(img, 'A1')
    sheet.merge_cells(start_row=1, start_column=1, end_row=1, end_column=4)

    row_index = 3  # 由于图片的插入和名称，数据开始的索引从3开始

    # 计算每列的最大宽度
    col_max_widths = [0] * len(data[0])
    for row in data:
        for col_index, value in enumerate(row):
            col_max_widths[col_index] = max(col_max_widths[col_index], len(str(value)))

    # 设置列宽
    for col_index, max_len in enumerate(col_max_widths, start=1):
        column_letter = openpyxl.utils.get_column_letter(col_index)
        if col_index == 5:  # 专门处理第五列的宽度
            sheet.column_dimensions[column_letter].width = calculate_column_width("X" * 50)  # 固定宽度
        else:
            sheet.column_dimensions[column_letter].width = calculate_column_width("X" * max_len)

    # 遍历数据，添加数据行
    for row in data:
        # 将数据写入单元格
        for col_index, value in enumerate(row, start=1):
            if col_index == 5:  # 处理第五列内容换行
                value = wrap_text(value, 50)
            cell = sheet.cell(row=row_index, column=col_index, value=value)
            cell.alignment = Alignment(wrap_text=True, horizontal='center', vertical='center')  # 其他列居中对齐

        # 根据第五个元素（面谈记录）计算行高
        record = row[4]
        record = wrap_text(record, 50)  # 处理换行
        row_height = calculate_row_height(record)
        sheet.row_dimensions[row_index].height = row_height

        row_index += 1

    wb.save(file)
    file.seek(0)
    return file
