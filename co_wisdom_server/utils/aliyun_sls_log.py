import time

import pendulum
from aliyun.log import LogClient, ListLogstoresRequest, PutLogsRequest, LogItem, GetLogsRequest
from django.conf import settings

from utils.message.lark_message import LarkMessageCenter


class AliyunSls(object):
    """
    阿里云sls日志管理
    对应文档：https://help.aliyun.com/document_detail/284632.html
    """

    def __init__(self):
        # 日志服务的服务入口。更多信息，请参见服务入口。
        self.endpoint = settings.SLS_ENDPOINT
        # 阿里云访问密钥AccessKey。
        self.access_key_id = settings.SLS_ACCESS_KEY_ID
        self.access_key = settings.SLS_ACCESS_KEY

        # Project和log_store名称。
        self.project = settings.SLS_PROJECT
        self.log_store = settings.SLS_LOG_STORE

        self.topic = ""
        self.source = ""

        self.client = LogClient(self.endpoint, self.access_key_id, self.access_key)

    def print_log(self):
        req1 = ListLogstoresRequest(self.project)
        res1 = self.client.list_logstores(req1)
        return res1.log_print()

    def send_log(self, contents):
        if isinstance(contents, dict):
            contents = [(k, str(v)) for k, v in contents.items()]
        log_item = LogItem()
        log_item.set_time(int(time.time()))
        log_item.set_contents(contents)
        req2 = PutLogsRequest(self.project, self.log_store, self.topic, self.source, [log_item])
        self.client.put_logs(req2)
        return

    def get_log(self, query, from_time=time.time() - 3600 * 24 * 30, to_time=time.time() + 3600):
        request = GetLogsRequest(
            self.project, self.log_store, from_time, to_time,
            '', query=query, line=999, offset=0, reverse=False)
        response = self.client.get_logs(request)
        return response.get_logs()


class AliyunSlsLogLayout(object):

    def __init__(self):
        self.env_site_url = [
            'https://www.qzcoach.com/',
            'https://www.qzcoachtest.com/',
            'https://www.qzcoach.cn/'
        ]

    def send_work_wechat_log_data(self, **kwargs):
        data = {
            'uid': kwargs.get('user_id'),
            'ts': int(time.time()),
            'type': settings.SLS_SEND_WORK_WECHAT_LOG,
            'pid': kwargs.get('project_id'),
            'username': kwargs.get('user_name'),
            'env': self.env_site_url.index(settings.SITE_URL) + 1,
            'extra': kwargs.get('content'),
            'content': {
                'EventId': kwargs.get('event_id'),
                'Type': 'send',
                'CoachID': kwargs.get('coach_id'),
                'CoachName':  kwargs.get('coach_name'),
                'ProjectID': kwargs.get('project_id'),
                'ProjectName': kwargs.get('project_name'),
                'CoacheeID': kwargs.get('coachee_id'),
                'CoacheeName': kwargs.get('coachee_name'),
                'MessageTitle': kwargs.get('message'),
                'ContentMsg': kwargs.get('content_msg'),
                'AppMsg': kwargs.get('app_msg'),
                'FileMsg': kwargs.get('file_msg'),
                'SendTime': pendulum.now().strftime('%Y/%m/%d %H:%M:%S'),
            },
        }
        try:
            AliyunSls().send_log(data)
        except Exception as e:
            LarkMessageCenter().send_other_backend_message(
                'SLS日志错误', 'error', {'error': f'send_work_wechat_log_data:{str(e)}'})
        return data

    # 第三方api数据拼接
    def send_third_api_log(self, **kwargs):
        data = {
            'uid': kwargs.get('user_id'),
            'ts': int(time.time()),
            'type': settings.SLS_SEND_THIRD_API_LOG,
            'pid': kwargs.get('project_id'),
            'username': kwargs.get('user_name'),
            'extra': None,
            'env': self.env_site_url.index(settings.SITE_URL) + 1,
            'content': {
                'EventId': 'Send_Third_Party_Api_Log',
                'Type': 'send',
                'MessageTitle': kwargs.get('message'),
                'SendTime': pendulum.now().strftime('%Y/%m/%d %H:%M:%S'),
                'Text': kwargs.get('content'),
            },
        }
        try:
            AliyunSls().send_log(data)
        except Exception as e:
            LarkMessageCenter().send_other_backend_message(
                'SLS日志错误', 'error', {'error': f'send_third_api_log:{str(e)}'})
        return data

    # 业务错误
    def send_business_api_log(self, **kwargs):
        data = {
            'ts': int(time.time()),
            'type': settings.SLS_SEND_BUSINESS_API_LOG,
            'extra': None,
            'env': self.env_site_url.index(settings.SITE_URL) + 1,
            'content': {
                'EventId': 'Send_Business_Api_Log',
                'Type': 'send',
                'method': kwargs.get('method'),
                'url': kwargs.get('url'),
                'token': kwargs.get('token'),
                'user_id': kwargs.get('user_id'),
                'params': kwargs.get('params'),
                'body': kwargs.get('body'),
                'msg': kwargs.get('message'),
                'role': kwargs.get('role'),
                'true_name': kwargs.get('true_name'),
            },
        }
        try:
            AliyunSls().send_log(data)
        except Exception as e:
            LarkMessageCenter().send_other_backend_message(
                'SLS日志错误', 'error', {'error': f'send_business_api_log:{str(e)}'})
        return data