import io
import re
import qrcode
import pendulum

from PIL import Image
from utils import randomPassword, aliyun
import redis
from django.conf import settings

# 初始化Redis连接
qr_code_redis = redis.Redis.from_url(settings.WECHAT_ACCESS_TOKEN_URL)

def image2byte(image):
    img_bytes = io.BytesIO()
    image.save(img_bytes, format="JPEG")
    image_bytes = img_bytes.getvalue()
    return image_bytes


def get_qr_code(data):
    # 生成缓存key
    cache_key = f'qr_code:{data}'
    
    # 先尝试从Redis获取缓存
    cached_url = qr_code_redis.get(cache_key)
    if cached_url:
        return cached_url.decode('utf8')

    # 生成二维码
    img_file = io.BytesIO()
    qr = qrcode.QRCode(version=1, error_correction=qrcode.constants.ERROR_CORRECT_H, box_size=10, border=4)
    qr.add_data(data)
    qr.make(fit=True)
    img = qr.make_image()
    img.save(img_file)

    # 上传至阿里云oss
    image_name = 'user/{}/{}.jpg'.format(
        pendulum.now().format('YYYY/MM/DD/HH/mm'),
        randomPassword(length=5)
    )
    image = Image.open(img_file)
    byte_data = image2byte(image)
    aliyun.AliYun('cwcoach').send_file(
        image_name, byte_data, headers={'Content-Type': 'image/jpeg'})
    url = aliyun.AliYun('cwcoach').oss_url(image_name, headers={'Content-Type': 'image/jpeg'})
    url = str(url).split('?')[0]
    url = re.sub(r'.*//(.*)/', 'https://static.qzcoach.com/', url)
    
    # 将结果存入Redis，不设置过期时间
    qr_code_redis.set(cache_key, url)
    
    return url


# 二进制数据获取二维码
def get_base_qr_code(data, data_type):
    img_file = io.BytesIO(data)

    # 上传至阿里云oss
    image_name = 'page/{}/{}/{}.jpg'.format(
        data_type,
        pendulum.now().format('YYYY/MM/DD/HH/mm'),
        randomPassword(length=5)
    )
    image = Image.open(img_file)
    byte_data = image2byte(image)
    aliyun.AliYun('cwcoach').send_file(
        image_name, byte_data, headers={'Content-Type': 'image/jpeg'})
    url = aliyun.AliYun('cwcoach').oss_url(image_name, headers={'Content-Type': 'image/jpeg'})
    url = str(url).split('?')[0]
    url = re.sub(r'.*//(.*)/', 'https://static.qzcoach.com/', url)
    return url
