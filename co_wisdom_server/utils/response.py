from enum import Enum, unique


@unique
class ResponseType(Enum):
    ServerError = 1
    LoginExpiration = 302
    ParametersLack = 303
    TokenExpiration = 304
    PINError = 305
    NoPermissions = 306
    NoRolePermissions = 307
    LoginError = 308
    AccountLocked = 309
    LoginSuccess = 310
    SaveSuccess = 311
    AuditSuccess = 312
    OperSuccess = 313
    RegisterSuccess = 314
    ModifyPwdSuccess = 315
    EidtSuccess = 316
    DelSuccess = 317
    NoKey = 318
    NoKeyDel = 319
    KeyError = 320
    Other = 321

def get_message(response_type):
    message = str(response_type.value)
    if response_type == ResponseType.LoginExpiration:
        message = '登陆已过期,请重新登陆'
    elif response_type == ResponseType.TokenExpiration:
        message = 'Token已过期,请重新登陆'
    elif response_type == ResponseType.AccountLocked:
        message = '帐号已被锁定'
    elif response_type == ResponseType.LoginSuccess:
        message = '登陆成功'
    elif response_type == ResponseType.ParametersLack:
        message = '参数不完整'
    elif response_type == ResponseType.NoPermissions:
        message = '没有权限操作'
    elif response_type == ResponseType.NoRolePermissions:
        message = '角色没有权限操作'
    elif response_type == ResponseType.ServerError:
        message = '服务器好像出了点问题'
    elif response_type == ResponseType.LoginError:
        message = '用户名或密码错误'
    elif response_type == ResponseType.SaveSuccess:
        message = '保存成功'
    elif response_type == ResponseType.NoKey:
        message = '没有主键不能编辑'
    elif response_type == ResponseType.NoKeyDel:
        message = '没有主键不能删除'
    elif response_type == ResponseType.KeyError:
        message = '主键不正确或没有传入主键'
    elif response_type == ResponseType.EidtSuccess:
        message = '编辑成功'
    elif response_type == ResponseType.DelSuccess:
        message = '删除成功'
    elif response_type == ResponseType.RegisterSuccess:
        message = '注册成功'
    elif response_type == ResponseType.AuditSuccess:
        message = '审核成功'
    elif response_type == ResponseType.ModifyPwdSuccess:
        message = '密码修改成功'
    elif response_type == ResponseType.OperSuccess:
        message = '操作成功'
    elif response_type == ResponseType.PINError:
        message = '验证码不正确'
    return message


def page_data(querySet, serializer, status=0, msg=''):
    rows = []
    for row in querySet.all():
        rows.append(serializer(row, many=False).data)
    extra = None
    if hasattr(querySet, 'extra_list'):
        extra = querySet.extra_list
    total = querySet.count()
    if hasattr(querySet, 'total'):
        total = querySet.total
    return {
        "status": status,
        'msg': msg,
        "rows": rows,
        "extra": extra,
        "total": total,
        'summary': None
    }


class Content:
    status = True
    code = 0
    message = None
    data = None
    row = None
    lower_result = 0

    def lower_raw(self):
        return {
            "status": self.status,
            'code': self.code,
            "message": self.message,
            "data": self.data
        }

    def raw(self):
        return self.lower_raw()

    def ok(self, message=None, data=None):
        self.status = True
        if message:
            self.message = message
        if data:
            self.data = data
        return self

    def ok_type(self, response_type):
        self.code = response_type.value
        self.message = get_message(response_type)
        return self.ok(message=self.message, data=self.data)

    def error(self, message=None):
        self.status = False
        if message:
            self.message = message
        return self

    def set(self, response_type, msg, status):
        if type(status) == bool:
            self.status = status
        self.code = str(response_type.value)
        if msg:
            self.message = msg
        return self

    def error_type(self, response_type):
        return self.set(response_type=response_type, msg=get_message(response_type), status=False)
