from datetime import datetime, timedelta
from django.conf import settings

from utils.aliyun_sls_log import AliyunSls
from wisdom_v2.common import project_service_public
from wisdom_v2.enum.chemical_interview_enum import ChemicalInterviewStatusEnum
from wisdom_v2.models import Interview<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>Answer
from wisdom_v2.models_file import ChemicalInterview2Coach


def get_interview_to_lark_data(interview):
    chemical_interview = interview.chemical_interview.filter(
        deleted=False, chemical_interview_status=ChemicalInterviewStatusEnum.selected.value).first()

    select_coach_count = ChemicalInterview2Coach.objects.filter(
            chemical_interview_module=chemical_interview.chemical_interview_module, deleted=False,
            chemical_interview_status=ChemicalInterviewStatusEnum.unselected).count()
    if select_coach_count == 0:
        select_coach_str = '选择首次面谈教练'
    elif select_coach_count >= chemical_interview.chemical_interview_module.max_interview_number:
        select_coach_str = '选择首轮推荐以外的教练'
    else:
        select_coach_str = '选择第二次面谈教练'

    # 面谈日期
    interview_date = int(interview.public_attr.start_time.timestamp()) * 1000
    # 项目名称
    project_name = interview.public_attr.project.name
    # 项目阶段
    project_stage = "化学面谈"
    # 客户姓名
    customer_name = interview.public_attr.target_user.cover_name
    # 教练姓名
    coach_name = interview.public_attr.user.cover_name
    # 选择结果
    choice_result = select_coach_str
    # 反馈途径
    feedback_path = "自主通过平台提供反馈"
    # 计划化学面谈的总时长
    module_delta = chemical_interview.chemical_interview_module.end_time - chemical_interview.chemical_interview_module.start_time
    # 通知客户预约使用的触达渠道
    notify_channel = ['系统邮件']
    # 客户完成预约花费的运营时长
    cost_time = round((datetime.now().date() - chemical_interview.chemical_interview_module.start_time).days)

    fields = {
        "面谈日期": interview_date,
        "项目名称": project_name,
        "项目阶段": project_stage,
        "客户姓名": customer_name,
        "教练姓名": coach_name,
        "选择结果": choice_result,
        "反馈途径": feedback_path,
        "计划化学面谈的总时长（天）": module_delta.days,
        "通知客户预约使用的触达渠道": notify_channel,
        "辅导标识": interview.id,
        "客户完成预约花费的运营时长（天）": cost_time,
    }

    if interview.coachee_record_status:
        update_filter = update_interview_to_lark_filter(interview)
        fields.update(update_filter)
    return fields


def update_interview_to_lark_filter(interview):
    update_data = {}

    tmp_answer = InterviewRecordTemplateAnswer.objects.filter(interview=interview, deleted=False).first()

    if tmp_answer:
        # 教练反馈时长
        cost_time = round((tmp_answer.created_at - interview.public_attr.start_time).total_seconds() / 3600)

        # 客户意愿度
        customer_readiness_answer = project_service_public.get_chemical_interview_coach_question(interview, 2)
        customer_readiness = customer_readiness_answer.option.title
        # 目标清晰度
        goal_clarity_answer = project_service_public.get_chemical_interview_coach_question(interview, 3)
        goal_clarity = goal_clarity_answer.option.title
        # 客户对教练了解度
        coach_knowledge_answer = project_service_public.get_chemical_interview_coach_question(interview, 1)
        coach_knowledge = coach_knowledge_answer.option.title
        # 聚焦主题
        focus_topic_answer = project_service_public.get_chemical_interview_coach_question(interview, 5)
        focus_topic = focus_topic_answer.answer

        update_data['反馈时长（Hour）'] = cost_time
        update_data['客户意愿度'] = customer_readiness
        update_data['客户对教练了解度'] = coach_knowledge
        update_data['聚焦主题'] = focus_topic
        update_data['目标清晰度'] = goal_clarity
    return update_data

