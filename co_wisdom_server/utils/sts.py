# from aliyunsdkcore import client
# from aliyunsdkcore.profile import region_provider
# from aliyunsdksts.request.v20150401 import AssumeRoleRequest
import json, base64, hmac, time, datetime
from hashlib import sha1 as sha


def getStsToken():
  accessid = 'LTAI5tQ5BdCzTkEZpdyWdiLX'
  access_secret = '******************************'
  REGINID = 'cn-beijing'

  # clt = client.AcsClient(accessid, access_secret, REGINID)

  # req = AssumeRoleRequest.AssumeRoleRequest()

  # req.set_RoleArn('acs:ram::1747591201246604:role/aliyunstsrole')

  # req.set_RoleSessionName('external-username')
  #req.set_DurationSeconds(Integer) # past time
  #req.set_Policy('')

  # resp = clt.do_action_with_exception(req)
  # resp = json.loads(resp)
  now = int(time.time())
  expire_syncpoint = now + 30
  expiration = datetime.datetime.utcfromtimestamp(expire_syncpoint).isoformat() + 'Z'

  policy_dict = {}
  policy_dict['expiration'] = expiration
  condition_array = []
  array_item = []
  array_item.append('starts-with')
  array_item.append('$key')
  array_item.append('user/')
  condition_array.append(array_item)
  policy_dict['conditions'] = condition_array
  policy = json.dumps(policy_dict).strip()
  policy_encode = base64.b64encode(policy.encode())
  h = hmac.new(access_secret.encode(), policy_encode, sha)
  sign_result = base64.encodestring(h.digest()).strip()

  token = dict(host='https://cwcoach.oss-cn-beijing.aliyuncs.com', accessid=accessid,
               signature=sign_result.decode(), policy=policy_encode.decode())

  return token