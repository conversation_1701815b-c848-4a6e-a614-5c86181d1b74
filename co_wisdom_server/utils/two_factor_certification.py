
from __future__ import print_function
from django.conf import settings
from django.conf import settings

import json
import ssl, hmac, base64, hashlib
from datetime import datetime as pydatetime

try:
    from urllib import urlencode
    from urllib2 import Request, urlopen
except ImportError:
    from urllib.parse import urlencode
    from urllib.request import Request, urlopen



class TwoFactorCertification:
    """
        腾讯云身份证姓名认证
    """

    def __init__(self):
        self.secretId = 'AKID68L6DQ7pbA1PH5r1eeMy9npP7Cln7bV1urn6'
        self.secretKey = 'HkL74I7h17cmdAtp3ovk9sfNv934hhnwHez9yy'
        self.source = 'market'
        self.method = 'POST'
        self.url = 'https://service-isr6xhvr-1308811306.sh.apigw.tencentcs.com/release/id_name/check'
        self.standby_url = 'https://service-hl92rg03-1301232119.bj.apigw.tencentcs.com/release/id/check'

    def get_headers(self):
        datetime = pydatetime.utcnow().strftime('%a, %d %b %Y %H:%M:%S GMT')
        signStr = "x-date: %s\nx-source: %s" % (datetime, self.source)
        sign = base64.b64encode(
            hmac.new(self.secretKey.encode('utf-8'), signStr.encode('utf-8'), hashlib.sha1).digest())
        auth = 'hmac id="%s", algorithm="hmac-sha1", headers="x-date x-source", signature="%s"' % (
            self.secretId, sign.decode('utf-8'))
        headers = {
            'X-Source': self.source,
            'X-Date': datetime,
            'Authorization': auth,
        }

        return headers

    def get_response(self, body, is_stand_by=False):
        if not is_stand_by:
            request = Request(self.url, headers=self.get_headers())
        else:
            request = Request(self.standby_url, headers=self.get_headers())
        request.get_method = lambda: self.method
        if self.method in ('POST', 'PUT', 'PATCH'):
            request.data = urlencode(body).encode('utf-8')
            request.add_header('Content-Type', 'application/x-www-form-urlencoded')
        ctx = ssl.create_default_context()
        ctx.check_hostname = False
        ctx.verify_mode = ssl.CERT_NONE
        response = urlopen(request, context=ctx)
        content = response.read()
        return json.loads(content.decode('utf-8'))

    def get_result(self, body):
        """
            核验结果“库无”原因有如下几种：
            (1)现役军人、武警官兵、特殊部门人员及特殊级别官员；
            (2)退役不到2年的军人和士兵（根据军衔、兵种不同，时间会有所不同，一般为2年）；
            (3)户口迁出，且没有在新的迁入地迁入 eg：刚上大学或刚毕业的大学生；
            (4)户口迁入新迁入地，当地公安系统未将迁移信息上报到公安部（上报时间地域不同而有所差异）；
            (5)更改姓名，当地公安系统未将更改信息上报到公安部（上报时间因地域不同而有所差异）；
            (6)移民；
            (7)未更换二代身份证；
            (8)死亡。
            (9)身份证号确实不存在
        """
        response = self.get_response(body)
        if response['code'] == 400:
            return False, '身份证号错误'
        elif response['code'] in [500, 1000, 9999]:  # todo zkg 系统错误，服务异常，或核验中心异常切换至第二套身份证验证系统
            body = {'cardNo': body['idcard'], 'realName': body['name']}
            response = self.get_response(body, is_stand_by=True)
            if response['error_code'] == 206501:
                return False, '库无'
            elif response['error_code'] == 0:
                if response['result']['isok'] == True:
                    return True, None
                else:
                    return False, '身份证号与姓名核验不一致'
            else:
                return False, '身份验证系统错误，请联系管理员'
        else:
            # 以上未走入 code为200
            result = response['data']['result']
            if result == 1:
                return True, None
            elif result == 2:
                return False, '身份证号与姓名核验不一致'
            else:
                return False, '库无'
