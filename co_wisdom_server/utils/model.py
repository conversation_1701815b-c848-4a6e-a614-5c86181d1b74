import datetime
import json

from rest_framework.response import Response
from . import response, int_arg


def add_row(data, serializer):
    row = serializer(data=data)
    try:
        if row.is_valid():
            row.save()
        else:
            return response.Content().error('数据校验失败 ' + json.dumps(row.errors, ensure_ascii=False))
    except Exception as e:
        return response.Content().error(e)
    pk_name = serializer.Meta.model._meta.pk.name
    data[pk_name] = row.data.get(pk_name)
    res = response.Content()
    res.lower_result = 1
    res.data = json.dumps({
        'data': row.data
    }, default=str, ensure_ascii=False)
    row_obj = serializer.Meta.model.objects.filter(pk=data[pk_name]).first()
    res.row = row_obj
    res.ok_type(response.ResponseType.SaveSuccess)
    return res


def update_row(data, serializer):
    res = response.Content()
    pk_name = serializer.Meta.model._meta.pk.name
    if not data.get(pk_name):
        return res.error_type(response.ResponseType.NoKey)
    row = serializer.Meta.model.objects.filter(pk=data.get(pk_name)).first()
    if not row:
        return res.error('保存的数据不存在')
    # update
    for key, value in data.items():
        if key == pk_name:
            continue
        setattr(row, key, value)
    try:
        row.save()
    except Exception as e:
        return res.error(e)
    res.lower_result = 1
    res.data = data
    res.row = serializer.Meta.model.objects.filter(pk=data.get(pk_name)).first()
    res.ok_type(response.ResponseType.SaveSuccess)
    return res


def delete_row(data, serializer):
    res = response.Content()
    # pk_name = serializer.Meta.model._meta.pk.name
    # if data.get('keys') == None or len(data.get('keys')) == 0:
    #     return res.error_type(response.ResponseType.NoKeyDel)
    serializer.Meta.model.objects.filter(pk__in=data).all().delete()
    return res.ok_type(response.ResponseType.DelSuccess)


def get_page_rows(data, serializer):
    filter_dict = {}
    exclude_dict = {}
    options = []
    addtion_wheres_list = data.get('addtion_wheres')
    if addtion_wheres_list and len(addtion_wheres_list) > 0:
        options.extend(addtion_wheres_list)
    else:
        for where in json.loads(data["wheres"]):
            options.append({
                'name': where.get("name"),
                'value': where.get("value"),
                'displayType': where.get("displayType", 'like')
            })
    for option in options:
        field = option.get("name")
        value = option.get("value")
        display_type = option.get('displayType').lower()
        if field and value is not None and display_type:
            exclude = False
            if display_type == 'checkbox':
                field += '__in'
            elif display_type == 'thanorequal':
                field += '__gte'
            elif display_type == 'lessorequal':
                field += '__lte'
            elif display_type == 'gt':
                field += '__gt'
            elif display_type == 'lt':
                field += '__lt'
            elif display_type == 'like':
                field += '__icontains'
            elif display_type == '!=':
                exclude = True
            elif display_type == 'notcontains':
                field += '__in'
                exclude = True
            if exclude:
                exclude_dict[str(field)] = value
            else:
                filter_dict[str(field)] = value
    result = serializer.Meta.model.objects.filter(**filter_dict).exclude(**exclude_dict)
    order = data.get('order')
    sort = data.get('sort')
    if order and sort:
        if order == 'desc':
            sort = '-' + sort
        result = result.order_by(sort)
    page = int_arg(data.get('page'), None)
    rows = int_arg(data.get('rows'), None)
    total = result.count()
    if page and rows and total > rows:
        start = (page - 1) * rows
        end = start + rows
        result = result[start:end]
    result.total = total
    return result


def get_option(data, name):
    if data["wheres"]:
        for where in json.loads(data["wheres"]):
            if where["name"] == name and where["value"]:
                return where["value"]
    return None


def add_option(data, name, value, displayType):
    addtion_wheres_list = data.get('addtion_wheres')
    if not addtion_wheres_list:
        addtion_wheres_list = []
    addtion_wheres_list.append({
        'name': name,
        'value': value,
        'displayType': displayType
    })
    data['addtion_wheres'] = addtion_wheres_list
