
import jwt
import time
import logging

import redis
from django.conf import settings
from django.utils import timezone

from rest_framework import exceptions
from rest_framework.authentication import BaseAuthentication
from itsdangerous import SignatureExpired, BadSignature
from itsdangerous import TimedJSONWebSignatureSerializer as Serializer
from wisdom_v2.models import User
from data.models import SysUser

from wisdom_v2.common.user_public import is_user_token_terminated

token_redis = redis.Redis.from_url(settings.TOKEN_LOGIN_REDIS)


def get_token(user, expiration=180 * 24 * 60 * 60, is_backend=False):
    """
    生成token,存入redis
    :param user: user obj
    :param expiration: limit time
    :return: token
    """
    token = jwt.encode({'id': user.pk, 'exp': time.time() + expiration,  "token_type": "access"} if not is_backend else
                       {'id': user.pk, 'exp': time.time() + expiration,  "token_type": "access", "is_backend": 1},
                       settings.SECRET_KEY, algorithm='HS256')
    # todo zkg临时注释
    # token = token.decode()
    return token


def get_key(secret_key, expiration=60 * 60 * 24 * 3, **kwargs):
    """
    生成一个加密key
    :param secret_key: 加密字符串
    :param expiration: 过期时间,设定为3天
    :param kwargs: 需加密的dict
    :return:
    """
    ser = Serializer(secret_key, expires_in=expiration)
    return ser.dumps(kwargs).decode()


def get_key_data(secret_key, key):
    """
    通过加密key解密数据
    :param secret_key: 加密字符串,注意加密和解密的secret_key必须一致
    :param key: 加密过后的key
    :return: data:加密之前的数据
    """

    s = Serializer(secret_key)
    try:
        data = s.loads(key)
    except (SignatureExpired, BadSignature):
        return False
    return data


class Authentication(BaseAuthentication):
    """ 自写 token 认证类 """
    def authenticate(self, request):
        # 1. 验证是否带有token
        try:
            token = request.META['HTTP_AUTHORIZATION']
        except (ValueError, KeyError):
            raise exceptions.AuthenticationFailed({'retCode': 401, 'retMsg': "登录过期 | Fail", 'err': '登录过期'})
        try:
            # token 黑名单
            if token_redis.get(token[7:]):
                raise exceptions.AuthenticationFailed({'retCode': 401, 'retMsg': "登录过期 | Fail", 'err': '登录过期'})
            # 2. 验证token是否能解析
            data = jwt.decode(token[7:], settings.SECRET_KEY, algorithms=['HS256'])

            if 'is_backend' not in data.keys():
                if '/v2/' in request.get_full_path():
                    raise exceptions.AuthenticationFailed({'retCode': 401, 'retMsg': "登录过期 | Fail", 'err': '登录过期'})
                sys_user = SysUser.objects.filter(pk=data['id'])
                # 3. 验证user是否存在
                if sys_user:
                    user = sys_user.first()
                else:
                    user = User.objects.get(pk=data['id'], deleted=False)
            else:
                user_id = data['id']
                exp = data['exp']
                if is_user_token_terminated(user_id, int(exp)):
                    raise exceptions.AuthenticationFailed(
                        {'retCode': 401, 'retMsg': "登录过期 | Fail", 'err': '登录过期'})
                user = User.objects.get(pk=data['id'], deleted=False)
        except Exception as e:
            logging.error(e)
            raise exceptions.AuthenticationFailed({'retCode': 401, 'retMsg': "登录过期 | Fail", 'err': '登录过期'})

        return user, token


__all__ = ('Authentication',)
