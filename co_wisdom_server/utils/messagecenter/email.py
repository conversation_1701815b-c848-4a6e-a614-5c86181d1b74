from django.core.mail import EmailMessage

class Email():

    def __init__(self, subject, body, to_email_list, sender=None, copy_email=None, attachments=None):
        self.subject = subject
        self.body = body
        self.email_list = to_email_list
        self.sender = sender
        self.copy_email = copy_email
        self.attachments = attachments

    def send(self):
        if type(self.email_list) is not list:
            return False
        msg = EmailMessage(self.subject, self.body, self.sender, self.email_list, cc=self.copy_email, attachments=self.attachments)
        msg.content_subtype = 'html'
        return msg.send()

