import datetime

from data import models


class Innermessage():

    def __init__(self, subject, body, type, touser, fromuser=None):
        self.subject = subject
        self.body = body
        self.type = type
        self.touser = touser
        self.fromuser = fromuser

    def send(self):
        message = models.AppMessage()
        message.type = self.type
        message.title = self.subject
        message.msgcontent = self.body
        message.is_read = 0
        message.accept_userid = self.touser.User_Id
        message.accept_user_name = self.touser.UserName
        message.post_time = datetime.datetime.now()
        if self.fromuser:
            message.post_userid = self.fromuser.User_Id
            message.post_user_name = self.fromuser.UserName
        else:
            message.post_userid = 0
            message.post_user_name = '系统'
        message.save()
        return None

