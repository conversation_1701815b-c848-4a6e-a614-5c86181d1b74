from django.conf import settings
from data import models
from utils.encrypt import EncryptData
subject_type = {
        0: "r42",
        1: "r313",
        2: "r311",
        3: "r411",
        4: "r54",
        5: "r421",
        6: "r422"
    }


def get_html_a(url):
    return '<a href={}>{}</a>'.format(url, url)


def get_template_content1():
    return "温馨提醒：请使用PC电脑登录平台，推荐使用谷歌浏览器、360浏览器、Safari 浏览器。"

def get_template_create_company_manage_content(name, password):
    return "您好，您的企业管理员账号已开通：<br /> 账号：{} <br /> 密码：{}".format(name, password)


def get_template_content2(project_id):
    project = models.AppProbject.objects.get(Probject_Id=project_id)
    manager_user = models.SysUser.objects.get(User_Id=project.Relation_UserId)
    manager = manager_user.UserTrueName
    mobile = manager_user.PhoneNo
    email = manager_user.Email
    return "如您需要获得帮助，请通过以下方式联系群智项目经理：<br /> 群智项目管理员：{} <br /> {} <br /> {}".format(manager, mobile, email)


def get_user_token(user_id):
    user_token = EncryptData().get_md5(user_id, flag=True)
    return user_token


def get_pici_link(user_id, interviewsubject, project_id, project_interview_id):
    """ 根据参数生成pici参数链接 """
    try:
        code = subject_type[interviewsubject]
        return str(user_id) + '_' + code + '_' + str(project_id) + '_' + str(project_interview_id)
    except Exception as e:
        print(e)
        return


def get_user_is_student(user_id, project_id):
    """ 用户在项目里是否是被教练者 """
    user_relation = models.AppProbjectrelation.objects.filter(ProbjectId=project_id, UserId=user_id, RolesId=6)
    return user_relation.exists()


def get_user_is_interest(user_id, project_id):
    """ 用户在项目里是否是利益相关者 """
    user_relation = models.AppProbjectrelation.objects.filter(ProbjectId=project_id, UserId=user_id, RolesId=7)
    return user_relation.exists()


def get_exam_r24_link(user_id, project_id, interested_id, flag):
    """ 利益相关者调研链接 flag  0:详情页面 1:列表页面 """
    user_role = get_user_is_student(user_id, project_id)
    if user_role:
        if flag:
            path_link = '{}student/reportevaluation?tabIndexStr=0&islink=true'.format(settings.SITE_URL)
        else:
            path_link = '{}student/reportmodel?modelid=3&pagecode=r24&probjectid={}&userid={}&islink=true'.format(settings.SITE_URL, project_id, interested_id)
    else:
        if flag:
            path_link = '{}interested/coach?islink=true&usertoken={}'.format(settings.SITE_URL, get_user_token(user_id))
        else:
            path_link = '{}interested/reportmodel?modelid=3&pagecode=r24&probjectid={}&userid={}&islink=true&usertoken={}'.format(
                settings.SITE_URL, project_id, interested_id, get_user_token(user_id))
    path_link = get_html_a(path_link)

    return path_link


def get_exam_r45_link(user_id, project_id, interested_id, flag):
    """  改变观察链接 """
    user_role = get_user_is_student(user_id, project_id)
    path_link = settings.SITE_URL
    if user_role:
        if flag:
            path_link = '{}student/reportevaluation?islink=true'.format(settings.SITE_URL)
        else:
            path_link = '{}student/reportmodel?modelid=9&pagecode=r45&probjectid={}&userid={}&islink=true'.format(settings.SITE_URL, project_id, interested_id)
    else:
        if flag:
            path_link = '{}interested/coach?tabIndexStr=0&islink=true&usertoken={}'.format(settings.SITE_URL, get_user_token(user_id))
        else:
            path_link = '{}interested/reportmodel?modelid=9&pagecode=r45&probjectid={}&userid={}&islink=true&usertoken={}'.format(settings.SITE_URL, project_id, interested_id, get_user_token(user_id))
    path_link = get_html_a(path_link)

    return path_link


def get_exam_other_link(user_id, project_id, interested_id, flag):
    """ 他评链接 """
    user_role = get_user_is_student(user_id, project_id)
    path_link = settings.SITE_URL
    # 多用户的利益相关者 https://www.qzcoachtest.com/student/reportevaluations
    if user_role:
        # if flag:
        if get_user_is_interest(user_id, project_id):
            path_link = '{}student/reportevaluation?islink=true'.format(settings.SITE_URL)
        else:
            path_link = '{}student/reportevaluation?tabIndexStr=0&islink=true'.format(settings.SITE_URL)
        # else:
        #     path_link = '{}person/evaluationview?islink=true&evaluationid=18&probjectid={}&userid={}&islink=true'.format(
        #                     settings.SITE_URL, project_id, interested_id)
    else:
        if flag:
            path_link = '{}interested/coach?islink=true&usertoken={}'.format(settings.SITE_URL, get_user_token(user_id))
        else:
            user_token = EncryptData().get_md5(user_id, flag=True)

            path_link = '{}person/evaluationview?islink=true&evaluationid=18&probjectid={}&userid={}&usertoken={}&islink=true'.format(settings.SITE_URL, project_id, interested_id, user_token)
    path_link = get_html_a(path_link)
    return path_link


def get_exam_self_link(project_id):
    """ 自评链接 """
    return get_html_a('{}person/evaluationview?islink=true&evaluationid=17&probjectid={}&islink=true' .format(settings.SITE_URL, project_id))


def get_interview_end_to_coach_type_1_link(probject_id, user_id):
    return get_html_a("{}coach/customerdetail?probjectid={}&userid={}&tabIndexStr=2&islink=true".format(settings.SITE_URL, probject_id, user_id))


def get_interview_end_to_coach_link(interviewsubject, probject_id, pici, student):
    """ 教练填写约谈记录链接-to coach """
    # try:
    #     code = subject_type[interviewsubject]
    # except Exception as e:
    #     print(e)
    #     code = ''
    # return '{}coach/interviewmodel?pagecode={}&probjectid={}&pici={}&userid={}'.format(settings.SITE_URL, code, probject_id, pici, student)

    # 约谈记录暂时先跳转到约谈列表页面 https://www.qzcoachtest.com/coach/customerdetail?probjectid=26&userid=839
    return get_html_a('{}coach/customerdetail?probjectid={}&userid={}&islink=true'.format(settings.SITE_URL, probject_id, student))


def get_interview_end_to_coach_ok_link(interviewsubject, probject_id, pici, student):
    """ 教练填写约谈记录链接-to coach """
    return get_html_a('{}coach/customerdetail?probjectid={}&userid={}&islink=true'.format(settings.SITE_URL, probject_id, student))


def get_interview_end_to_coach_not_link(probject_id, student):
    """ 教练填写约谈记录链接-to coach """
    return get_html_a('{}coach/customerdetail?probjectid={}&userid={}&islink=true'.format(settings.SITE_URL, probject_id, student))


def get_interview_end_to_student_link():
    """ 教练填写约谈记录链接-to student """
    return get_html_a('{}student/coachinterview?islink=true'.format(settings.SITE_URL))


def get_interview_end_ok_to_student_link(interviewsubject, probject_id, pici, student):
    """ 教练填写约谈记录链接-to student """
    return get_html_a('{}student/coachinterview?islink=true'.format(settings.SITE_URL))


def get_exam_r112_not_end_to_student_link(project_id):
    """ 教练者填写未完成的教练准备度链接 """
    return get_html_a('{}student/reportmodel?modelid=2&pagecode=r112&probjectid={}&islink=true'.format(settings.SITE_URL, project_id))


def get_send_email_of_evaluation_end_link(project_id):
    """ 所有人填写完测评，给群智管理员发邮件链接 """
    return get_html_a('{}padmin/probjectinfo?probjectid={}&islink=true&tabIndexStr=6,1'.format(settings.SITE_URL, project_id))


def get_param_h24_to_student_link():
    """ 开始前24小时提醒被教练者链接 """
    return get_html_a("{}student/coachinterview?islink=true".format(settings.SITE_URL))


def get_param_1h_to_coach_link(project_id, student_id):
    """ 开始前1小时提醒教练链接 """
    return get_html_a("{}coach/customerdetail?probjectid={}&userid={}&islink=true".format(settings.SITE_URL, project_id, student_id))


def get_param_1h_to_student_link():
    """ 开始前1小时提醒被教练者链接 """
    return get_html_a("{}student/coachinterview?islink=true".format(settings.SITE_URL))


def get_param_15m_to_interested_default_link():
    """ 开始前1小时提醒利益相关者默认链接 """
    return get_html_a("{}interested/coach?tabIndexStr=1&islink=true".format(settings.SITE_URL))


def get_param_15m_to_interested_link():
    """ 开始前1小时提醒利益相关者（被教练者）链接 """
    return get_html_a("{}student/interested?islink=true".format(settings.SITE_URL))


def get_param_15m_to_coach_link(project_id, student_id):
    """ 开始前15分钟提醒教练者链接 """
    return get_html_a("{}coach/customerdetail?probjectid={}&userid={}&islink=true".format(settings.SITE_URL, project_id, student_id))


def get_param_15m_to_student_link():
    """开始前1小时提醒被教练者链接 """
    return get_html_a("{}student/coachinterview?islink=true".format(settings.SITE_URL))


def get_notice_to_coach_link(userid, project=None):
    """教练收到聊天提醒"""
    if project:
        return get_html_a('{}coach/customerdetail?probjectid={}&userid={}&tabIndexStr=3&islink=true'.format(settings.SITE_URL, project, userid))
    else:
        return get_html_a('{}coach/customer?tabIndexStr=2&islink=true'.format(settings.SITE_URL))


def get_notice_to_student_link():
    """被教练者收到聊天提醒"""
    return get_html_a("{}student/im?islink=true".format(settings.SITE_URL))


def get_newmember_has_link(project_id):
    """ 新建被教练者有化学面谈 """
    return get_html_a("{}student/probject_info?probjectid={}&islink=true".format(settings.SITE_URL, project_id))


def get_newmember_no_link():
    """ 新建被教练者无化学面谈 """
    return get_html_a("{}student/coachinterview?islink=true".format(settings.SITE_URL))


def get_template_to_manage_link(report_id, pid, uid):
    """完成调研通知项目管理员"""

    # return '{}padmin/evaluationresultview?reportid={}&islink=true'.format(settings.SITE_URL, report_id)
    return get_html_a('{}padmin/evaluationresultviewnew?resultid={}&pid={}&uid={}&isDetail=1&islink=true'.format(settings.SITE_URL, report_id, pid, uid))


def get_template_to_project_manage_link():
    """完成调研通知企业管理员"""

    return get_html_a('{}cadmin/modelreportresult?islink=true'.format(settings.SITE_URL))


def get_end_report_to_coach_link(report_id, pid, uid):
    """完成调研通知教练"""
    # return '{}coach/evaluationresultview?reportid={}&islink=true'.format(settings.SITE_URL, report_id)
    return get_html_a('{}coach/evaluationresultviewnew?resultid={}&pid={}&uid={}&isDetail=1&islink=true'.format(settings.SITE_URL, report_id, pid, uid))


def get_end_report_to_student_link(report_id, pid, uid):
    """完成调研通知被教练者"""
    # return '{}student/evaluationresultview?reportid={}&islink=true'.format(settings.SITE_URL, report_id)
    return get_html_a('{}student/evaluationresultviewnew?resultid={}&pid={}&uid={}&isDetail=1&islink=true'.format(settings.SITE_URL, report_id, pid, uid))


def get_end_report_r24_link(probjectid, dataid):
    """部分完成r24提醒教练"""
    return get_html_a('{}coach/customerdetail?probjectid={}&userid={}&tabIndexStr=2&islink=true'.format(settings.SITE_URL, probjectid, dataid))


def get_end_report_link(probjectid, pici, dataid):
    """全部完成提醒"""
    return get_html_a('{}coach/interviewmodel?pagecode=r241&probjectid={}&pici={}&userid={}&mtlyxgz=0&islink=true'.format(
                        settings.SITE_URL, probjectid, pici, dataid))


def get_exam_r112_coach_link(probjectid, studentid):
    """ 被教练者完成r112调研后通知教练查看 """
    return get_html_a('{}coach/reportmodelview?pagecode=r112&probjectid={}&pici={}&islink=true'.format(settings.SITE_URL, probjectid, studentid))


def get_exam_r45_end_to_student_link(probjectid, pici):
    """利益相关者提交改变观察通知被教练者 """
    return get_html_a('{}student/reportmodelview?pagecode=r45&probjectid={}&pici={}&islink=true'.format(settings.SITE_URL, probjectid, pici))


def get_exam_r45_end_to_manage_link(probjectid, pici):
    """利益相关者提交改变观察通知企业管理员 """
    return get_html_a('{}cadmin/reportmodelview?pagecode=r45&probjectid={}&pici={}&islink=true'.format(settings.SITE_URL, probjectid, pici))


def get_exam_r45_end_to_coach_link(probjectid, pici):
    """利益相关者提交改变观察通知教练 """
    return get_html_a('{}coach/reportmodelview?pagecode=r45&probjectid={}&pici={}&islink=true'.format(settings.SITE_URL, probjectid, pici))


def get_exam_r45_end_to_project_manage_link(probjectid, pici):
    """评人填写完后通知项目管理员"""
    # return '{}padmin/reportmodelview?pagecode=r45&probjectid={}&pici={}'.format(settings.SITE_URL, probjectid, pici)
    return get_html_a('{}padmin/probjectinfo?probjectid={}&tabIndexStr=6,1&islink=true'.format(settings.SITE_URL, probjectid))


def get_remindinterviewstudent_link():
    """教练提醒被教练者正式约谈"""
    return get_html_a('{}student/coachinterview?islink=true'.format(settings.SITE_URL))


def get_exam_r25_student_default_link(probjectid, userid):
    """ 教练操作页面按钮提醒用户预约利益相关者访谈"""
    return get_html_a('{}interested/reportmodel?modelid=3&pagecode=r24&probjectid={}&userid={}&islink=true'.format(
                settings.SITE_URL, probjectid, userid))


def get_exam_r25_interested_link(user_id):
    """教练操作页面按钮提醒用户预约利益相关者访谈"""
    return get_html_a("{}interested/coach?tabIndexStr=1&islink=true&usertoken={}".format(settings.SITE_URL, get_user_token(user_id)))


def get_exam_r25_student_link(user_id):
    """教练操作页面按钮提醒被教练者预约"""
    return get_html_a("{}student/interested?islink=true&usertoken={}".format(settings.SITE_URL, user_id))


def get_exam_r112_student(probjectid, userid):
    """邀请被教练者填写教练准备度"""
    return get_html_a('{}student/reportmodel?modelid=2&pagecode=r112&probjectid={}&userid={}&islink=true'.format(settings.SITE_URL, probjectid, userid))


def get_exam_end_to_manage_padmin(probjectid, pici):
    """被教练者完成教练准备度调研后通知-to padmin"""
    return get_html_a('{}padmin/reportmodelview?pagecode=r112&probjectid={}&pici={}&islink=true'.format(settings.SITE_URL, probjectid, pici))


def get_exam_end_to_manage_cadmin(probjectid, pici):
    """被教练者完成教练准备度调研后通知-to cadmin"""
    return get_html_a('{}cadmin/reportmodelview?pagecode=r112&probjectid={}&pici={}&islink=true'.format(settings.SITE_URL, probjectid, pici))


def get_r421_link_to_student(probjectid, pici):
    return get_html_a("{}student/reportmodelview?pagecode=r241&probjectid={}&pici={}&islink=true".format(settings.SITE_URL, probjectid, pici))


def get_r421_link_to_cadmin(probjectid, pici):
    return get_html_a("{}cadmin/reportmodelview?pagecode=r241&probjectid={}&pici={}&islink=true".format(settings.SITE_URL, probjectid, pici))


def get_r421_link_to_padmin(probjectid):
    return get_html_a("{}padmin/probjectinfo?probjectid={}&tabIndexStr=6,1&islink=true".format(settings.SITE_URL, probjectid))


def get_r411_link_to_student(probjectid, pici, coachid):
    """ 被教练者查看阶段回顾 """
    return get_html_a('{}student/reportevaluations?tabIndexStr=1&islink=true'.format(settings.SITE_URL))


def get_r411_link_to_coach(probjectid, pici, studentid):
    return get_html_a('{}coach/customerdetail?probjectid={}&userid={}&tabIndexStr=4,1&islink=true'.format(settings.SITE_URL, probjectid, studentid))


def get_r411_link_to_padmin(probjectid, pici):
    return get_html_a('{}padmin/reportmodelview?pagecode=r410&probjectid={}&pici={}&islink=true'.format(settings.SITE_URL, probjectid, pici))


def get_r411_link_to_cadmin(probjectid, pici):
    return get_html_a('{}cadmin/reportmodelview?pagecode=r410&probjectid={}&pici={}&islink=true'.format(settings.SITE_URL, probjectid, pici))

def get_lbi_report_to_manage(report_id):
    return get_html_a('{}cadmin/evaluationgroupresultview?resultid={}&isDetail=1'.format(settings.SITE_URL, report_id))
