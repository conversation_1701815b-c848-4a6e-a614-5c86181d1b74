import functools
import logging

from utils.aliyun_sls_log import AliyunSlsLogLayout
from utils.feishu_robot import push_wx_error_message

error_logging = logging.getLogger('api_action')


def error_log(view_func):
    try:
        @functools.wraps(view_func)
        def wrapper(*args, **kwargs):
            state, msg = view_func(*args, **kwargs)
            if not state:
                error_logging.info(msg)
                push_wx_error_message(name='企业微信接口错误', level='error', content=msg)
                return state, msg.get('error') if isinstance(msg, dict) else msg
            return state, msg
        return wrapper
    except Exception as e:
        error_logging.info({'name': 'error_log', 'error': str(e)})


def sls_log(view_func):
    # 获取使用装饰器的函数名
    func_name = view_func.__name__

    @functools.wraps(view_func)
    def wrapper(*args, **kwargs):
        try:
            state, msg = view_func(*args, **kwargs)
            AliyunSlsLogLayout().send_third_api_log(
                message='企业微信请求记录',
                content={"state": state, "msg": msg, "func_name": func_name}
            )
            return state, msg
        except Exception as e:
            push_wx_error_message(
                name='企业微信接口sls日志上传错误', level='error', content={'name': f"sls_log_{func_name}", 'error': str(e)})
            return 'error', str(e)

    return wrapper
