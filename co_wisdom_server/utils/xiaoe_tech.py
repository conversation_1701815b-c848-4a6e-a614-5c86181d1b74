import logging
from ast import literal_eval

import redis
import requests
from django.conf import settings
from utils import task

token_redis = redis.Redis.from_url(settings.TOKEN_LOGIN_REDIS)
error_logging = logging.getLogger('api_action')
xiaoe_tech_http = requests.Session()


# 小鹅通自定义requests的hooks，access_token过期后自动重新调用
def xiaoe_tech_hook(response, *args, **kwargs):
    if response.ok:

        # 判断access_token过期进入额外操作
        if response.json().get('code') == 2008 and not literal_eval(response.request.body).get('check'):

            # 清理缓存
            token_redis.delete('XiaoeTechToken')

            # 更新access_token
            url = response.request.url
            params = literal_eval(response.request.body)
            params['access_token'] = XiaoeTech().get_access_token()

            # 次数标记，防止循环调用。
            params['check'] = 'true'

            # 记录access_token过期后重新发起的请求
            error_logging.info({'name': url.split('?')[0],
                                'url': url,
                                'params': params
                                })

            if response.request.method == 'POST':
                return xiaoe_tech_http.post(url, json=params)
            elif response.request.method == 'GET':
                return xiaoe_tech_http.get(url, params=params)


xiaoe_tech_http.hooks["response"] = [xiaoe_tech_hook]


class XiaoeTech(object):
    """
    小鹅通API-https://www.apifox.cn/apidoc/project-701638/doc-682958
    """

    def __init__(self):
        self.base_url = 'https://api.xiaoe-tech.com'
        self.app_id = settings.XIAOE_TECH_APP_ID
        self.client_id = settings.XIAOE_TECH_CLIENT_ID
        self.secret_key = settings.XIAOE_TECH_SECRET_KEY

    def get_access_token(self):

        if token_redis.get('XiaoeTechToken'):
            return token_redis.get('XiaoeTechToken').decode()

        url = self.base_url + '/token'
        params = {
            "app_id": self.app_id,
            "client_id":  self.client_id,
            "secret_key":  self.secret_key,
            "grant_type": "client_credential"
        }
        raw = xiaoe_tech_http.get(url=url, params=params)
        try:
            if raw.ok:
                if raw.json().get('code') == 0:
                    access_token = raw.json().get('data').get('access_token')
                    token_redis.set('XiaoeTechToken', access_token, ex=60*60)
                    return access_token
                error_logging.info({'url': url,
                                    'error': raw.json().get('msg'),
                                    'params': params
                                    })
                return
            error_logging.info({
                'url': url,
                'error': raw.text,
                'params': params
            })
            return
        except Exception as e:
            error_logging.info({
                'url': url,
                'error': str(e),
                'params': params
            })
            task.send_backend_error_message.delay(
                '小鹅通get_access_token',
                f'func_name:get_access_token; error: {str(e)}')
            return

    def get_user_details(self, union_id):
        url = self.base_url + '/xe.user.info.get/1.0.0'
        params = {
            'access_token': self.get_access_token(),
            'data': {
                'wx_union_id': union_id,
                'field_list': [
                    "name", 'gender', 'phone', 'wx_email', 'company', 'city', 'job'
                ]
            }
        }
        raw = xiaoe_tech_http.post(url, json=params)
        try:
            if raw.ok:
                if raw.json().get('code') == 0:
                    data = raw.json().get('data')
                    return True, data
                return False, raw.json().get('msg')
            return False, raw.text
        except Exception as e:
            task.send_backend_error_message.delay(
                '小鹅通get_user_details',
                f'func_name:get_user_details; error: {str(e)}')
            return False, str(e)

    # 获取用户订单列表
    def get_user_orders(self, user_id):
        url = self.base_url + '/xe.order.list.get/1.0.1'
        params = {
            'access_token': self.get_access_token(),
            'user_id': user_id
        }
        raw = xiaoe_tech_http.post(url, json=params)
        try:
            if raw.ok:
                if raw.json().get('code') == 0:
                    data = raw.json().get('data').get('list')
                    return True, data
                return False, raw.json().get('msg')
            return False, raw.text
        except Exception as e:
            task.send_backend_error_message.delay(
                '小鹅通用户订单列表',
                f'func_name:get_user_orders; error: {str(e)}')
            return False, str(e)

    # 获取用户信息采集
    def get_user_information(self, user_id):
        url = self.base_url + '/xe.information.user.result.get/1.0.0'
        params = {
            'access_token': self.get_access_token(),
            'user_id': user_id,
            'page': 1,
            'page_size': 50
        }
        raw = xiaoe_tech_http.post(url, json=params)
        try:
            if raw.ok:
                if raw.json().get('code') == 0:
                    data = raw.json().get('data').get('collection_list')
                    return True, data
                return False, raw.json().get('msg')
            return False, raw.text
        except Exception as e:
            task.send_backend_error_message.delay(
                '小鹅通获取用户信息采集',
                f'func_name:get_user_information; error: {str(e)}')
            return False, str(e)
