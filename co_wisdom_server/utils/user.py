from django.db.models import Q

from data import models

from utils import null_or_blank


def get_user_ids(username):
    if null_or_blank(username):
        return []
    name_list = username
    if type(username) == str and ',' in username:
        name_list = username.split(',')
    ids = models.SysUser.objects.filter(Q(UserName__icontains=name_list) | Q(UserTrueName__icontains=name_list), Enable=1).values_list('User_Id', flat=True)
    return ids
