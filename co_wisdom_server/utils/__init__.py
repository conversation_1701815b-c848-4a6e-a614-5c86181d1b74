import base64
import re
import string

from random import choice
from Crypto.Cipher import AES
from rest_framework.response import Response
from . import response

def lower_dic(dic):
    return {k[0].lower() + k[1:]: v for k, v in dic.items()}

def delete_htmltag(html):
    cleanr = re.compile('<.*?>')
    cleantext = re.sub(cleanr, '', html)
    return cleantext


def delete_blank_args(arg, keys=[]):
    for key in keys:
        if null_or_blank(arg[str(key)]):
            del arg[str(key)]

def value_or_default(arg, default = None):
    if arg:
        return arg
    return default

def int_arg(arg, default = None):
    if type(arg) is int:
        return arg
    if arg and arg.isnumeric():
        return int(arg)
    return default

def bool_arg(arg, default = None):
    if type(arg) is bool:
        return arg
    if arg:
        return bool(arg)
    return default

def float_arg(arg, default = None):
    if type(arg) is float:
        return arg
    if arg:
        return float(arg)
    return default

def null_or_blank(arg):
    return not (arg and arg != '')

def blank_to_none(source):
    if type(source) == str and source == '':
        return None
    else:
        return source

def enter_to_br(input):
    if null_or_blank(input):
        return ''
    return input.replace('\n', '<br>').replace('\t', '&nbsp;&nbsp;')


def randomPassword(length=8, chars=string.ascii_letters + string.digits):
    chars = chars.replace('l', '').replace('I', '')
    return ''.join([choice(chars) for i in range(length)])


class AESCipher:
    def __init__(self, key):
        self.key = key[0:16].encode('utf8')
        self.iv = b'\x00\x01\x02\x03\x04\x05\x06\x07\x08\x09\x0A\x0B\x0C\x0D\x0E\x0F'

    def __pad(self, text):
        text_length = len(text)
        amount_to_pad = AES.block_size - (text_length % AES.block_size)
        if amount_to_pad == 0:
            amount_to_pad = AES.block_size
        pad = chr(amount_to_pad)
        return text + pad * amount_to_pad

    def __unpad(self, text):
        pad = ord(text[-1])
        return text[:-pad]

    def encrypt(self, raw):
        raw = self.__pad(raw).encode("utf8")
        cipher = AES.new(self.key, AES.MODE_CBC, self.iv)
        en = cipher.encrypt(raw)
        return base64.b64encode(en).decode("utf-8")

    def decrypt(self, enc):
        enc = base64.b64decode(enc)
        cipher = AES.new(self.key, AES.MODE_CBC, self.iv)
        return self.__unpad(cipher.decrypt(enc).decode("utf-8"))


def aesencrypt(s):
    if s is None:
        return ''
    aes = AESCipher('9A28E6B02467415084FE773C4730B62E')
    encrypt_str = aes.encrypt(s)
    return encrypt_str.replace('+', '_').replace('/', '~')


def aesdecrypt(s):
    if s is None:
        return ''
    aes = AESCipher('9A28E6B02467415084FE773C4730B62E')
    str = s.replace('_', '+').replace('~', '/')
    return aes.decrypt(str)
