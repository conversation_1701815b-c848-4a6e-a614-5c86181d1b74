
from utils.aliyun_sls_log import AliyunSlsLogLayout
from utils.message.lark_message import LarkMessageCenter

def send_backend_error_message(func_name, content):
    try:
        LarkMessageCenter().send_other_backend_message(f'{func_name}错误', 'error', content)
        AliyunSlsLogLayout().send_third_api_log(content={'error': f'{content}'}, message=func_name)
    except Exception as e:
        LarkMessageCenter().send_other_backend_message(
            '消息发送错误', 'error', {'error': f'send_backend_error_message:{str(e)}'})