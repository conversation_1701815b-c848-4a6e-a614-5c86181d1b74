import requests
from django.core.cache import cache

from utils import randomPassword


class EasemobAPI:
    # server_config = {
    #     "isOpen": 1,
    #     "prefix": "cowisdom_",
    #     "serverUrl": "https://a1.easemob.com/",
    #     "orgName": "1112200318098558",
    #     "appName": "cowisdomtest",
    #     "clientId": "YXA6sgMZRTqZTYuYE-c52xN__A",
    #     "clientSecret": "YXA6UcKkbcIvEsJ6qQf8yQtQkW3VcRQ",
    #     "httpTimeOut": "10000",
    #     "isDebug": "false",
    #     "maxJsonLength": "0"
    # }

    server_config = {
        "isOpen": 1,
        "prefix": "qzcoach_",
        "serverUrl": "https://a1.easemob.com/",
        "orgName": "1112200318098558",
        "appName": "qzcoach",
        "clientId": "YXA6wb7AchHIRNCFtLfqREjOSA",
        "clientSecret": "YXA607EMWWmfjVpLAOOcBSzHT0FEu5Q",
        "httpTimeOut": "10000",
        "isDebug": "false",
        "maxJsonLength": "0"
    }
    base_url = server_config['serverUrl'] + server_config['orgName'] + '/' + server_config['appName'] + '/'
    retry = True

    def get_token(self):
        cache_token = cache.get('easemob_token')
        if cache_token:
            return cache_token
        body = {
            'client_id': self.server_config['clientId'],
            'client_secret': self.server_config['clientSecret'],
            'grant_type': 'client_credentials'
        }
        r = requests.post(self.base_url + 'token', json=body)
        if r.status_code == 200:
            token = r.json().get('access_token')
            expires = r.json().get('expires_in')
            cache.set('easemob_token', token, expires)
            return token
        return None

    def refresh_token(self):
        cache.delete('easemob_token')
        return self.get_token()

    def request(self, url, type='get', json=None):
        r = None
        headers = {
            'Authorization': 'Bearer ' + cache.get('easemob_token', '')
        }
        if type == 'get':
            r = requests.get(url, headers=headers)
        elif type == 'post':
            r = requests.post(url, headers=headers, json=json)
        elif type == 'delete':
            r = requests.delete(url, headers=headers)
        if r.status_code == 200:
            self.retry = True
            return r
        elif r.status_code == 401 and self.retry:
            self.retry = False
            self.refresh_token()
            return self.request(url, type, json)
        return None

    def add_user(self, username, nickname=None, password=None):
        if nickname is None:
            nickname = username
        if password is None:
            password = randomPassword()
        body = {
            'nickname': nickname,
            'username': username,
            'password': password
        }
        url = self.base_url + 'users'
        return self.request(url, 'post', body)

    def delete_user(self, username):
        url = self.base_url + 'users/' + username
        return self.request(url, 'delete')

    def add_friend(self, owner_name, friend_name):
        url = self.base_url + 'users/' + owner_name + '/contacts/users/' + friend_name
        return self.request(url, 'post')

    def user_stat(self, username):
        url = self.base_url + 'users/' + username + '/status'
        return self.request(url, 'get')
