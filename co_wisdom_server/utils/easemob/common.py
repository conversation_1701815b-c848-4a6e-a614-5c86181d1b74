from data import models
from utils import randomPassword, aesencrypt, null_or_blank
from utils.easemob.api import <PERSON>asemobAP<PERSON>


def enable_im(uid):
    user = models.SysUser.objects.filter(User_Id=uid).first()
    if user and null_or_blank(user.MessageId):
        name = EasemobAPI.server_config.get('prefix') + randomPassword(4) + str(user.pk)
        password = randomPassword()
        res = EasemobAPI().add_user(name, password=password)
        if res:
            user.OpenIM = 1
            user.MessageId = name
            user.MessageIdPwd = aesencrypt(password)
            user.save()


def enable_im_and_add_friend(uid_list):
    im_list = []
    for uid in uid_list:
        user = models.SysUser.objects.filter(User_Id=uid).first()
        if user and null_or_blank(user.MessageId):
            name = EasemobAPI.server_config.get('prefix') + randomPassword(4) + str(user.pk)
            password = randomPassword()
            res = EasemobAPI().add_user(username=name, password=password)
            if res:
                user.OpenIM = 1
                user.MessageId = name
                user.MessageIdPwd = aesencrypt(password)
                user.save()
                im_list.append(name)
    if len(im_list) > 1:
        owner_name = im_list.pop(0)
        for name in im_list:
            EasemobAPI().add_friend(owner_name, name)


def get_im_id(uid):
    user = models.SysUser.objects.filter(User_Id=uid).first()
    if user and user.MessageId:
        return user.MessageId
    return ''
