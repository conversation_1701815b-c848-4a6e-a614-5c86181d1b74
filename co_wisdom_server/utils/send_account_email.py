import json
import redis

from utils.aliyun_sls_log import AliyunSlsLogLayout
from wisdom_v2.models import Project, UserBackend, WorkWechatUser, User
from django.conf import settings
from utils import work_wechat

third_party_redis = redis.Redis.from_url(settings.THIRD_PARTY_DATA_REDIS)

def get_project_manage_qr_code(project_id):
    project = Project.objects.get(pk=project_id)
    manage_list = project.manager_list
    if not manage_list:
        return settings.DEFAULT_PROJECT_MANAGER_QRCODE
    user_backend_ids = [i['id'] for i in manage_list]
    user_ids = list(UserBackend.objects.filter(id__in=user_backend_ids).values_list('user_id', flat=True))
    qr_codes = list(WorkWechatUser.objects.filter(user_id__in=user_ids, qr_code__isnull=False, deleted=False).order_by('-created_at').
        values_list('qr_code', flat=True))
    if qr_codes:
        return qr_codes[0] if qr_codes[0] else settings.DEFAULT_PROJECT_MANAGER_QRCODE
    return settings.DEFAULT_PROJECT_MANAGER_QRCODE


def get_project_manage_wx_user_id(project_id, user_id, task_name=None):

    results = {}
    manage_list = Project.objects.get(pk=project_id).manager_list
    if not manage_list:
        return False, None, None
    user_backend_ids = [i['id'] for i in manage_list]
    user_ids = list(UserBackend.objects.filter(id__in=user_backend_ids).values_list('user_id', flat=True))
    wx_user_ids = list(WorkWechatUser.objects.filter(user_id__in=user_ids, wx_user_id__isnull=False, deleted=False).values_list(
        'wx_user_id', flat=True))
    if wx_user_ids:
        for wx_user_id in wx_user_ids:

            customer_dict = third_party_redis.get(f'{wx_user_id}_customer_dict')
            if customer_dict:
                results[wx_user_id] = list(json.loads(customer_dict.decode()).keys())
            else:
                is_success, external_userid = work_wechat.WorkWechat().get_customer_list(wx_user_id)
                if is_success:
                    results[wx_user_id] = external_userid
                    customer_dict = {item: None for item in external_userid}
                    third_party_redis.set(f'{wx_user_id}_customer_dict', json.dumps(customer_dict))
    if not results:
        # msg = f'阶段报告查看提醒 通过企业微信发送客户提醒消息时，' \
        #       f'{coach_task.project_bundle.project.name}项目下的项目' \
        #       f'运营没有绑定企业微信，无法发送'
        # push_wx_error_message(name='消息发送失败提醒', level='warning', content=msg)

        user = User.objects.filter(id=user_id).first()
        AliyunSlsLogLayout().send_third_api_log(
            user_id=user_id,
            user_name=user.cover_name,
            project_id=project_id,
            content={
                'error': f'{project_id}项目下的客户运营没有客户列表'
            },
            message=task_name,
        )
        return False, None, None

    external_user_id = get_user_external_user_id(user_id)
    if not external_user_id:
        # msg = f'阶段报告查看提醒 通过企业微信发送客户提醒消息时，' \
        #       f'{coach_task.project_bundle.project.name}项目下的' \
        #       f'user_id为{coach_task.project_bundle.project_member.user_id}的被教练者没有绑定企业微信，无法发送'
        # push_wx_error_message(name='消息发送失败提醒', level='warning', content=msg)

        user = User.objects.filter(id=user_id).first()
        AliyunSlsLogLayout().send_third_api_log(
            user_id=user_id,
            user_name=user.cover_name,
            project_id=project_id,
            content={
                'error': f'{project_id}项目下的项目运营与客户{user_id}外部联系人id不匹配',
                'external_user_ids': results
            },
            message=task_name,
        )
        return False, None, None
    sender = get_sender(results, external_user_id[0])

    return True, sender, external_user_id


def get_user_external_user_id(user_id):
    external_user_ids = list(WorkWechatUser.objects.filter(
        user_id=user_id,
        deleted=False).values_list('external_user_id', flat=True))
    if external_user_ids:
        return external_user_ids
    return None


def get_sender(data, external_userid):
    sender = [k for k, v in data.items() if external_userid in v]
    if not sender:
        return None
    return sender[0]


def get_project_manager_wx_qr_code(project_id):
    project = Project.objects.get(pk=project_id)
    manage_list = project.manager_list
    if not manage_list:
        return 'https://static.qzcoach.com/app/customer_service_qrcode.jpg'
    user_backend_ids = [i['id'] for i in manage_list]
    user_ids = list(UserBackend.objects.filter(id__in=user_backend_ids).values_list('user_id', flat=True))
    qr_code_list = list(WorkWechatUser.objects.filter(user_id__in=user_ids, wx_user_id__isnull=False, deleted=False).values_list(
        'qr_code', flat=True))
    qr_code_list = list(filter(None, qr_code_list))
    if qr_code_list:
        return qr_code_list[-1]
    return 'https://static.qzcoach.com/app/customer_service_qrcode.jpg'