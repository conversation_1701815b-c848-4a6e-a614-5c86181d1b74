import copy
from datetime import datetime

from django.db.models import Avg, Variance

from utils.messagecenter import getui
from utils import task
from wisdom_v2.models import ProjectInterested, EvaluationQuestion, EvaluationReport, EvaluationOption, WorkWechatUser, \
    PublicAttr, CompanyMember, ProjectCoach, EvaluationModule
from wisdom_v2.views.constant import ATTR_TYPE_EVALUATION_REPORT
from utils.generate_project_evaluation_report import generate_project_evaluation_report
from wisdom_v2.enum.service_content_enum import PdfReportTypeEnum, EvaluationWriteRoleEnum, EvaluationReportTypeEnum

question_sort = {
    '以身作则': [2, 10, 12, 24, 35, 63],
    '抗压复原': [13, 14, 21, 36, 50, 57, 67, 69, 76],
    '锐意进取': [22, 25, 28, 47],
    '有效授权': [8, 18, 43, 45, 52, 61],
    '激励赋能': [7, 16, 32, 40, 46, 58, 72],
    '绩效辅导': [30, 41, 48, 62, 73],
    '敏捷应变': [5, 15, 27, 51, 59, 77],
    '领导变革': [6, 17, 23, 29, 54, 71, 78],
    '战略远见': [3, 4, 9, 20, 26, 37, 53],
    '系统思考': [19, 56, 60, 39, 42, 64, 66, 68, 75],
    '多方协同': [11, 33, 34, 44, 55, 65, 70, 74],
    '果敢决策': [1, 31, 38, 49],
}
# 列表元素为question_sort的k
category_list = [
    {
        'category': '发展自我',
        'description': 'Leading Self',
        'cn_type': [
            ['以身作则', '能够在行为中展现自身价值观，并保持谦逊与成长'],
            ['抗压复原', '可以较快地从挫折中回复，坚韧不拔'],
            ['锐意进取', '对工作有激情和报负，为实现更大的成就而持续突破']
        ]
    },
    {
        'category': '影响他人',
        'description': 'Leading Others',
        'cn_type': [
            ['激励赋能', '令员工积极主动地投入工作，并能从中获得价值感'],
            ['有效授权', '促进团队成员为集体目标担责'],
            ['绩效辅导', '通过辅导与教练支持每个员工追求卓越绩效']
        ]
    },
    {
        'category': '管理业务',
        'description': 'Leading Business',
        'cn_type': [
            ['系统思考', '以多元的视角，立足全局进行思考'],
            ['多方协同', '充分发挥各方优势协同共进，实现多赢的合作'],
            ['果敢决策', '基于分析判断，及时果断做出决策']
        ]
    },
    {
        'category': '引领未来',
        'description': 'Leading Future',
        'cn_type': [
            ['敏捷应变', '在业务和管理上快速迭代，不断创新'],
            ['领导变革', '引领方向，限定策略，促使变革成功'],
            ['战略远见', '目光长远，能够把我机会实现愿景']
        ]
    },
]


def get_question(raw_result):
    result = []
    for item in raw_result:
        result.append(item.get('question'))

    titles = EvaluationQuestion.objects.filter(id__in=result).values_list('title', flat=True)
    return list(titles)


def lbi_evaluation(evaluation, public_attr):
    lbi_analysis = {
        'report_name': evaluation.name,
        'project_name': public_attr.project.name,
        'report_date': datetime.now().strftime("%Y-%m-%d"),
        'coachee_info': {
            'name':  public_attr.user.cover_name

        },
        'self_considence': None,
        'outstanding': None,
        'discuss': None,
    }
    # 参与者
    other_users = ProjectInterested.objects.filter(
        project_id=public_attr.project.pk, deleted=False,
        master_id=public_attr.user.id)
    other_user_ids = [item.interested.id for item in other_users]

    # 利益相关者姓名+身份+岗位
    stakeholder = {
        'superior': [],  # 上级
        'equal': [],  # 平级
        'subordinate': [],  # 下级
    }

    # id 列表
    raw_other_user_relation = {
        '上级': [],
        '平级': [],
        '下级': [],
        '自评': [public_attr.user.id],
    }
    for item in other_users.all():
        if item.relation:
            try:
                # 根据身份放入不同数据列表
                raw_state = ['', 'superior', 'equal', 'subordinate'][int(item.relation)]
                cn_raw_state = ['', '上级', '平级', '下级'][int(item.relation)]
            except Exception as e:
                return False, str(e)

            company_user = CompanyMember.objects.filter(
                user_id=item.interested.id
            ).first()
            stakeholder[raw_state] += [
                {'name': item.interested.cover_name,
                 'position': company_user.position}
            ]

            # 根据relation分组存储用户id  后续使用
            raw_other_user_relation[cn_raw_state].append(item.interested.id)

    lbi_analysis['stakeholder'] = stakeholder

    # 雷达图
    summary = {
        'self_data': [],  # 用户数据
        'other_data': [],  # 利益相关者数据
        'type': list(question_sort.keys())  # 行为项顺序
    }

    raw_type_order = {}
    for k, v in question_sort.items():

        # 利益相关者平均分
        other_score = EvaluationOption.objects.filter(
            question__order__in=v,
            evaluation_answer_option__public_attr__project_id=public_attr.project.id,
            evaluation_answer_option__public_attr__user_id__in=other_user_ids,
            evaluation_answer_option__public_attr__target_user_id=public_attr.user.id
        ).aggregate(score=Avg('score'))['score']
        other_score = round(other_score, 1) if other_score else 0
        # summary['other_data'] += [{'type': k, 'score': other_score}]
        summary['other_data'].append(other_score)

        # 用户平均分
        self_score = EvaluationOption.objects.filter(
            question__order__in=v,
            evaluation_answer_option__public_attr=public_attr
        ).aggregate(score=Avg('score'))['score']
        self_score = round(self_score, 1) if self_score else 0
        # summary['self_data'] += [{'type': k, 'score': self_score}]
        summary['self_data'].append(self_score)

        # 获取平均分差绝对值，数据组合 后续会使用

        raw_type_order[k] = [round(abs(self_score - other_score), 1), [self_score, other_score]]
    lbi_analysis['summary'] = summary

    # 根据平均分排序
    type_order = sorted(raw_type_order.items(), key=lambda x: x[1][0])

    # 一致性
    coherency = {
        'self_data': [],  # 用户数据
        'other_data': [],  # 利益相关者数据
        'type': []  # 行为项顺序
    }
    for k, v in type_order:
        # v = [平均分差值绝对值, [用户分平均分, 利益相关者平均分]]
        # coherency['self_data'] += [{'type': k, 'score': v[1][0]}]
        # coherency['other_data'] += [{'type': k, 'score':  v[1][1]}]
        coherency['self_data'].append(v[1][0])
        coherency['other_data'].append(v[1][1])
        coherency['type'].append(k)
    lbi_analysis['coherency'] = coherency

    feedback = []
    # 行为项分类
    result_sort_var = EvaluationOption.objects.filter(
        question__evaluation_id=evaluation.id,
        evaluation_answer_option__public_attr__project_id = public_attr.project.id,
        evaluation_answer_option__public_attr__target_user_id = public_attr.user.id
    ).values('question').annotate(
        score_var=Variance('score'),
        score_avg=Avg('score')).order_by('score_var')
    list_before = result_sort_var[0:int(result_sort_var.count() / 2)]

    # 优势题数据
    advantage_list = sorted(list_before, key=lambda x: x['score_avg'], reverse=True)
    # 一致优势
    feedback.append({
            'title': '无可争议的优势',
            'subtitle': '你和同事一致认为，你的优势在以下行为得以体现：',
            'content': get_question(advantage_list[0:5])
        })

    # 一致进步
    progress_list = sorted(list_before, key=lambda x: x['score_avg'], reverse=False)
    feedback.append({
            'title': '无可争议的发展要求',
            'subtitle': '你和同事一致认为，你在以下方面有较大提升空间：',
            'content': get_question(progress_list[0:5])})

    # 劣势题数据
    list_after = result_sort_var[int(result_sort_var.count() / 2):]

    # 盲区优势
    unknown_advantage_list = sorted(list_after, key=lambda x: x['score_avg'], reverse=True)
    feedback.append({
            'title': '出人意料的优势',
            'subtitle': '在以下领导行为中，同事看到了被你忽略的优势：',
            'content': get_question(unknown_advantage_list[0:5])})

    # 盲区进步
    unknown_progress_list = sorted(list_after, key=lambda x: x['score_avg'], reverse=False)
    feedback.append({
            'title': '出人意料的发展要求',
            'subtitle': '在以下领导行为中，你的同事看到了未被你发觉的进步空间：',
            'content': get_question(unknown_progress_list[0:5])})
    lbi_analysis['feedback'] = feedback

    # 柱状图
    index_score = []

    for category in copy.deepcopy(category_list):

        # 数据列表
        content = []

        # 循环读取分组对应的数据类型
        for item in category.get('cn_type'):

            name, subtitle = item

            # 基础数据格式
            raw_data = {'title': name, 'data': [], 'subtitle': subtitle}

            # 获取数据类型对应的问题列表
            question_order = question_sort.get(name)

            # 循环根据relation分组的列表
            for cn, user_ids in raw_other_user_relation.items():

                # 获取平均分
                score = EvaluationOption.objects.filter(
                    question__order__in=question_order,
                    evaluation_answer_option__public_attr__project_id=public_attr.project.id,
                    evaluation_answer_option__public_attr__user_id__in=user_ids,
                    evaluation_answer_option__public_attr__target_user_id=public_attr.user.id
                ).aggregate(score=Avg('score'))['score']

                # 数据拼接
                score = round(score, 1) if score else 0
                if score:
                    raw_data['data'].append({'relation': cn, 'score': score})
            content.append(raw_data)

        # 移除临时数据
        category.pop('cn_type')

        category['content'] = content
        index_score.append(category)
    lbi_analysis['index_score'] = index_score

    report = EvaluationReport.objects.filter(evaluation=evaluation, public_attr__user=public_attr.user,
                                             public_attr__project=public_attr.project, deleted=False,
                                             public_attr__type=ATTR_TYPE_EVALUATION_REPORT).first()
    if not report:
        report_public_attr = PublicAttr.objects.create(
            project_id=public_attr.project_id,
            user_id=public_attr.user_id,
            type=ATTR_TYPE_EVALUATION_REPORT)
        report = EvaluationReport.objects.create(
            public_attr=report_public_attr,
            evaluation=evaluation,
            lbi_analysis=lbi_analysis)
    else:
        exists_lbi_analysis = report.lbi_analysis
        self_considence = exists_lbi_analysis.get('self_considence')
        if self_considence:
            lbi_analysis['self_considence'] = self_considence
        outstanding = exists_lbi_analysis.get('outstanding')
        if outstanding:
            lbi_analysis['outstanding'] = outstanding
        discuss = exists_lbi_analysis.get('discuss')
        if discuss:
            lbi_analysis['discuss'] = discuss
        report.lbi_analysis = lbi_analysis
        report.save()

    evaluation_module = EvaluationModule.objects.filter(
                evaluation_id=evaluation.id,
                project_bundle__project_id=public_attr.project,
                project_bundle__project_member__user_id=public_attr.user_id,
                deleted=False
            ).first()
    evaluation_module.is_submit = 1
    evaluation_module.submit_time = datetime.now()
    evaluation_module.save()

    evaluation_report_config = evaluation.evaluation_report_config.filter(deleted=False).first()
    if not evaluation_report_config:
        return False, '未配置报告类型'

    # 生成pdf文件
    if evaluation_report_config.type == EvaluationReportTypeEnum.lbi_self_evaluation_report.value:
        task.get_user_pdf_url.delay(report.id, f'{public_attr.user.cover_name}_{evaluation.name}_{report.id}.pdf',
                                    pdf_type=PdfReportTypeEnum.lbi_self_evaluation_report.value)
    elif evaluation_report_config.type == EvaluationReportTypeEnum.lbi_personal_report.value:
        task.get_user_pdf_url.delay(report.id, f'{public_attr.user.cover_name}_{evaluation.name}_{report.id}.pdf',
                                    pdf_type=PdfReportTypeEnum.lbi_personal_report.value)
    else:
        return False, '配置报告类型错误'
    task.get_user_pdf_url.delay(report.id, f'{public_attr.user.cover_name}_{evaluation.name}_'
                                           f'{report.id}_company_manager.pdf',
                                pdf_type=PdfReportTypeEnum.lbi_personal_report_company_manager.value)

    # 发送通知
    task.send_lbi_evaluation_remind(public_attr, report.id)

    project_coach = ProjectCoach.objects.filter(
        project_id=public_attr.project_id,
        member_id=public_attr.user.id,
        deleted=False,
    ).first()
    if project_coach:
        work_wechat_user = WorkWechatUser.objects.filter(
            wx_user_id__isnull=False,
            user_id=project_coach.coach.user.id,
            deleted=False
        ).first()
        if work_wechat_user:
            company = public_attr.project.company
            company_name = company.real_name
            content_item = [
                {"key": "客户名称", "value": public_attr.target_user.cover_name},
                {"key": "所属企业", "value": company_name},
                {"key": "所属项目", "value": public_attr.project.name},
                {"key": "测评名称", "value": evaluation.name}
            ]
            getui.send_work_wechat_coach_notice(
                work_wechat_user.wx_user_id,
                'evaluation_remind',
                content_item=content_item,
                company=company_name,
                report_id=report.id,
                coachee_name=public_attr.target_user.cover_name,
                user_id=project_coach.coach.user.id,
                project_id=public_attr.project_id,
                project_name=public_attr.project.name,
                coachee_id=public_attr.target_user.id,
                coach_name=project_coach.coach.user.cover_name,
            )
    generate_project_evaluation_report(public_attr.project_id)
    return True, report.id
