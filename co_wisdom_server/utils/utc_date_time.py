#  日期时间转utc时间
import pendulum
import datetime


def datetime_change_utc(date):

    #  转化为字符串
    if not isinstance(date, str):
        date = str(date)

    #  转化为有时区的时间信息
    if date.isdigit():
        time_data = pendulum.from_timestamp(int(date), tz='Asia/Shanghai')
    else:
        date = date.replace('/', '-')

        if len(date) == len('YYYY-MM-DD'):
            time_data = pendulum.from_format(date, 'YYYY-MM-DD', tz='Asia/Shanghai')
        elif len(date) == len('YYYY-MM-DD HH:mm'):
            time_data = pendulum.from_format(date, 'YYYY-MM-DD HH:mm', tz='Asia/Shanghai')
        elif len(date) == len('YYYY-MM-DD HH:mm:ss'):
            time_data = pendulum.from_format(date, 'YYYY-MM-DD HH:mm:ss', tz='Asia/Shanghai')
        else:
            return

    return time_data



def get_total_time(start_time, end_time):
    """
        获取两个时间段差值
    """
    start_time = datetime_change_utc(start_time)
    end_time = datetime_change_utc(end_time)

    class TotalTime(object):
        day = int((end_time - start_time).total_days())
        minute = int((end_time - start_time).total_minutes())
        seconds = int((end_time - start_time).total_seconds())
    return TotalTime()


def get_today_seconds():
    """
        获取当前时间距离当天结束还剩多少秒
    """
    today = datetime.datetime.strptime(str(datetime.date.today()), "%Y-%m-%d")
    tomorrow = today + datetime.timedelta(days=1)
    now_time = datetime.datetime.now()
    seconds = (tomorrow - now_time).seconds
    return seconds


def get_auto_cal_datetime_str(start_time, end_time, delimiter='-'):
    """
    生成一个表示自动校准过程的时间范围的字符串。

    参数:
    start_time: datetime 对象，表示开始时间。
    end_time: datetime 对象，表示结束时间。
    delimiter: 用于分隔开始时间和结束时间的字符串，默认为 '-'。

    返回:
    字符串，格式取决于开始和结束时间是否为同一天。
    如果是同一天，则格式为 'YYYY-MM-DD HH:MM - HH:MM'。
    如果不是同一天，则格式为 'YYYY-MM-DD HH:MM - YYYY-MM-DD HH:MM'。
    """

    # 检查开始时间和结束时间是否在同一天
    if start_time.date() == end_time.date():
        # 如果是同一天，只在结束时间显示小时和分钟
        return f"{start_time.strftime('%Y-%m-%d %H:%M')} {delimiter} {end_time.strftime('%H:%M')}"
    else:
        # 如果不是同一天，显示完整的日期和时间
        return f"{start_time.strftime('%Y-%m-%d %H:%M')}  {delimiter}  {end_time.strftime('%Y-%m-%d %H:%M')}"


