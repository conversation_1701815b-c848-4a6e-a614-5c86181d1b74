import datetime
import traceback

import jwt
import time
import json
import logging
from ast import literal_eval

from utils.encrypt import EncryptData
from rest_framework import status
from django.shortcuts import HttpResponse
from django.utils.deprecation import MiddlewareMixin
from wisdom_v2.models import User
from utils import task

# 需要和settings的TOKEN_LOGIN_REDIS是同一个地址
# token_redis = redis.Redis.from_url('redis://127.0.0.1:6379/1')

_logger = logging.getLogger('all_log')


class BlackListToken(MiddlewareMixin):

    def process_request(self, request):
        try:
            token = request.META.get('HTTP_AUTHORIZATION')
            if token:
                token = jwt.decode(token[7:], verify=False, algorithms=['HS256'])
                encrypt = EncryptData()
                code = encrypt.get_md5(token['id'])
                userdata = encrypt.verify_token(code)

                if userdata and userdata.get('jti') and userdata.get('jti') == token['jti'] and userdata.get('status') == 3:
                    return HttpResponse(status=status.HTTP_401_UNAUTHORIZED)
        except Exception as e:
            print(e)


class LogMiddleware(MiddlewareMixin):
    @classmethod
    def process_request(cls, request):
        try:
            body = json.loads(request.body)
        except Exception:
            body = dict()
        data = dict(request.GET) if request.GET else dict(request.POST)
        # 单独处理传list
        if isinstance(body, list):
            body.append(data)
        else:
            body.update(data)

        user = request.user
        user_id = user.pk if user else None
        user_name = request.user.username if user else None
        request.start_time = time.time()
        log_info = dict()
        log_info.update(url=request.get_full_path(), request_body=body,
                        request_content_length=request.META.get('CONTENT_LENGTH', 0),
                        user_agent=request.META.get('HTTP_USER_AGENT'),
                        token=request.META.get('HTTP_AUTHORIZATION'),
                        user_id=user_id,
                        user_name=user_name,
                        mp=request.headers.get('mp'),
                        role=request.headers.get('role'))
        request.log_info = log_info

    @classmethod
    def process_response(cls, request, response):
        # 匿名用户,不记录
        # if request.user.__str__() == 'AnonymousUser':
        #     return response

        # 序列化层主动抛出异常retCode由str改为int
        try:
            content = json.loads(response.content)
            if 'retCode' in content and type(content['retCode']) == list:
                content['retCode'] = int(content['retCode'][0])
                content['retMsg'] = content['retMsg'][0]
                content['err'] = content['err'][0]
                response.content = json.dumps(content)

            if 'retCode' in content and type(content['retCode']) == str:
                content['retCode'] = int(content['retCode'])
                response.content = json.dumps(content)
            if 'retCode' in content and content['retCode'] == 400:
                msg = content.get('retMsg')
                if filter_send_middleware_400_message(msg):
                    method = request.META['REQUEST_METHOD']
                    url = request.get_full_path()
                    token = request.META.get('HTTP_AUTHORIZATION')
                    user_id = request.user.pk
                    true_name = request.user.cover_name if isinstance(request.user, User) else None
                    params = request.GET if request.GET else {}
                    body = request.body.decode('utf8') if request.body.decode('utf8') else {}
                    body = replace_password(body) if type(body) == str else body
                    role = request.headers.get('role') if request.headers.get('role') else None
                    task.send_middleware_400_message.delay(method, url, token, user_id, params, body, msg, role, true_name)
        except:
            pass

        if 'v1/front/getMessages' in request.get_full_path():
            return response
        try:
            request.log_info.update(response_body=json.loads(response.content))
            request.log_info.update(response_code=response.status_code)
            request.log_info.update(response_content_length=len(str(response.content)) if response.content else 0)
            request.log_info.update(spend_time=int((time.time() - request.start_time) * 1000))
            _logger.debug(request.log_info)
            return response
        except Exception as e:
            return response

    @classmethod
    def process_exception(cls, request, exception):
        request.log_info.update(response_body=exception)
        request.log_info.update(response_code=500)


class ExceptionMessageMiddleware(MiddlewareMixin):

    def process_exception(self, request, exception):
        method = request.META['REQUEST_METHOD']
        url = request.get_full_path()
        token = request.META.get('HTTP_AUTHORIZATION')
        user_id = request.user.pk
        true_name = request.user.cover_name if isinstance(request.user, User) else None
        params = str(request.GET) if request.GET else None
        body = request.body.decode('utf8') if request.body.decode('utf8') else None
        body = replace_password(body) if type(body) == str else body
        tb = exception.__traceback__
        error = traceback.format_exception(type(exception), exception, tb)
        role = request.headers.get('role') if request.headers.get('role') else None
        task.send_middleware_500_message.delay(url, user_id, true_name, role, token, params, method, body, error)


def replace_password(body):
    if body:
        try:
            body_dict = json.loads(body)
            if type(body_dict) == dict:
                if 'password' in body_dict.keys():
                    body_dict['password'] = '******'
            body = json.dumps(body_dict)
        except json.JSONDecodeError:
            pass
    return body


def filter_send_middleware_400_message(msg):
    user_log_on_err = [
        '用户名或密码错误，请重新输入',
        '未查询到当前用户',
        '您未绑定群智账号，请先绑定后登录',
        '账户不存在，请仔细检查',
        # '您的报名已达上限~'
    ]

    if msg in user_log_on_err:
        return False
    return True
