import re


def is_valid_phone(phone):
    """
    检查提供的电话号码是否为有效的手机号格式。

    参数:
        phone (str): 待检查的电话号码字符串。

    返回:
        bool: 如果电话号码格式正确，返回True，否则返回False。
    """
    return re.match(r"^1[3456789][0-9]+$", phone) is not None


def mask_phone_number(phone):
    """
    掩盖手机号码的中间四位数字为"****"。

    参数:
        phone (str): 待掩盖的手机号码字符串。

    返回:
        str: 如果手机号码有效，返回掩盖后的手机号码，否则返回原手机号码。
    """
    if is_valid_phone(phone):
        return re.sub(r'(?<=\d{3})\d{4}(?=\d{4})', '****', phone)
    return phone
