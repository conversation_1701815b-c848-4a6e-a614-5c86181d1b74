import redis
import hashlib
from django.conf import settings

token_redis = redis.Redis.from_url(settings.TOKEN_LOGIN_REDIS)


class EncryptData(object):

    def __init__(self):
        self.SECRET_KEY = settings.SECRET_KEY
        self.EXP = 60 * 60 * 24 * 90

    def get_md5(self, user_id, flag=None):
        """
        flag: true: 在redis保存， false：不在redis保存
        """
        obj = hashlib.md5(b'co_coach')
        key = {"uid": user_id}
        obj.update(('{%s}' % key + self.SECRET_KEY).encode("utf-8"))
        secret = obj.hexdigest()
        if flag:
            token_redis.set(secret, str(key), ex=self.EXP)
        return secret

    def update_md5(self, user, jti=None, status=None):
        """
        status 2: 已登录状态 3: 删除状态
        """
        code = self.get_md5(user.pk)
        user_data = token_redis.get(code)
        if user_data:
            user_data = eval(user_data.decode('utf8'))
            if jti:
                user_data['jti'] = jti
            user_data['status'] = status
            token_redis.set(code, str(user_data))
            return True

    def verify_token(self, secret):
        try:
            user_data = token_redis.get(secret).decode('utf8')
            if user_data:
                return eval(user_data)
        except Exception as e:
            pass



