import json

import requests
import logging
import redis

from django.conf import settings
from utils.message.lark_message import LarkMessageCenter
from utils import task

error_logging = logging.getLogger('api_action')
access_token_redis = redis.Redis.from_url(settings.TOKEN_LOGIN_REDIS)


class LarkDocument:
    """
    飞书云文档相关
    """
    def __init__(self):
        self.app_id = settings.LARK_DOCUMENT_APP_ID
        self.app_secret = settings.LARK_DOCUMENT_APP_SECRET
        self.base_url = 'https://open.feishu.cn/open-apis/'
        self.tenant_access_token_url = 'https://open.feishu.cn/open-apis/auth/v3/tenant_access_token/internal'
        self.append_value_url = 'https://open.feishu.cn/open-apis/sheets/v2/spreadsheets/err_excel_token/values_append'
        self.append_image_value_url = 'https://open.feishu.cn/open-apis/sheets/v2/spreadsheets/excel_token/values_image'
        self.err_excel_msg = settings.LARK_DOCUMENT_ERR_EXCEL
        self.interview_table_msg = settings.LARK_DOCUMENT_INTERVIEW_TABLE
        self.coach_info_excel_msg = settings.LARK_DOCUMENT_COACH_INFO_EXCEL


    def internal_access_token(self):
        params = {
            "app_id": self.app_id,
            "app_secret": self.app_secret
        }
        raw = requests.post(url=self.tenant_access_token_url, params=params)
        if raw.json().get('code') == 0:
            tenant_access_token = raw.json().get('tenant_access_token')
            expire = raw.json().get('expire')
            access_token_redis.set('LarkDocumentToken', tenant_access_token, ex=expire)
            return tenant_access_token
        else:
            error_logging.info({'url': self.tenant_access_token_url, 'error': raw.text, 'params': params})
            LarkMessageCenter().send_other_backend_message('飞书错误表格', 'error',
                                                           f'url:{self.tenant_access_token_url}; '
                                                           f'error: {raw.text}; params: {params}')
            return None

    def get_tenant_access_token(self):
        """
        获取接口调用凭证 tenant_access_token
        优先读取缓存的，缓存剩余时间小于30分钟，则返回后更新缓存的token。
        没有缓存就获取最新的token。
        """

        # 查看是否缓存token
        if access_token_redis.get('LarkDocumentToken'):
            if access_token_redis.ttl('LarkDocumentToken') < 1800:  # 小于半小时则更新
                task.update_tenant_access_token.delay('LarkDocumentToken')
            return access_token_redis.get('LarkDocumentToken').decode('utf8')
        else:
            return self.internal_access_token()

    def get_table_value_data(self, data_filter):
        interview_table_msg = self.interview_table_msg
        tenant_access_token = self.get_tenant_access_token()
        if not tenant_access_token:
            return False
        headers = {
            "Content-Type": "application/json; charset=utf-8",
            "Authorization": f"Bearer {tenant_access_token}"
        }

        url = f'{self.base_url}bitable/v1/apps/{interview_table_msg.get("token")}/tables/{interview_table_msg.get("table")}/records'
        params = {
            'filter': data_filter,
        }
        try:
            raw = requests.get(url=url, params=params, headers=headers)
            if raw.json().get('code') == 0:
                return raw.json()
            else:
                error_logging.info({'url': self.tenant_access_token_url, 'error': raw.text, 'params': params})
                LarkMessageCenter().send_other_backend_message('飞书错误表格', 'error',
                                                               f'url:{self.tenant_access_token_url}; '
                                                               f'error: {raw.text}; params: {params}')
                return None
        except Exception as e:
            error_logging.info({'url': self.tenant_access_token_url, 'error': str(e), 'params': params})
            LarkMessageCenter().send_other_backend_message('飞书错误表格', 'error',
                                                           f'url:{self.tenant_access_token_url}; '
                                                           f'error: {e}; params: {params}')
            return None

    def update_value_table(self, fields, record_id):
        interview_table_msg = self.interview_table_msg
        tenant_access_token = self.get_tenant_access_token()
        if not tenant_access_token:
            return False
        headers = {
            "Content-Type": "application/json; charset=utf-8",
            "Authorization": f"Bearer {tenant_access_token}"
        }
        url = f'{self.base_url}bitable/v1/apps/{interview_table_msg.get("token")}/tables/{interview_table_msg.get("table")}/records/{record_id}'

        data = {"fields": fields}
        try:
            raw = requests.put(url=url, json=data, headers=headers)
            print(raw.json())
            if raw.json().get('code') == 0:
                return True
            else:
                error_logging.info({'url': self.tenant_access_token_url, 'error': raw.text, 'params': data})
                LarkMessageCenter().send_other_backend_message('飞书错误表格', 'error',
                                                               f'url:{self.tenant_access_token_url}; '
                                                               f'error: {raw.text}; params: {data}')
                return None
        except Exception as e:
            error_logging.info({'url': self.tenant_access_token_url, 'error': str(e), 'params': data})
            LarkMessageCenter().send_other_backend_message('飞书错误表格', 'error',
                                                           f'url:{self.tenant_access_token_url}; '
                                                           f'error: {e}; params: {data}')
            return None

    def append_value_table(self, fields):
        interview_table_msg = self.interview_table_msg
        tenant_access_token = self.get_tenant_access_token()
        if not tenant_access_token:
            return False
        headers = {
            "Content-Type": "application/json; charset=utf-8",
            "Authorization": f"Bearer {tenant_access_token}"
        }
        url = f'{self.base_url}bitable/v1/apps/{interview_table_msg.get("token")}/tables/{interview_table_msg.get("table")}/records'

        data = {"fields": fields}
        try:
            raw = requests.post(url=url, json=data, headers=headers)
            if raw.json().get('code') == 0 and raw.json().get('msg') == 'success':
                return True
            else:
                error_logging.info({'url': self.tenant_access_token_url, 'error': raw.text, 'params': data})
                LarkMessageCenter().send_other_backend_message('飞书错误表格', 'error',
                                                               f'url:{self.tenant_access_token_url}; '
                                                               f'error: {raw.text}; params: {data}')
                return None
        except Exception as e:
            error_logging.info({'url': self.tenant_access_token_url, 'error': str(e), 'params': data})
            LarkMessageCenter().send_other_backend_message('飞书错误表格', 'error',
                                                           f'url:{self.tenant_access_token_url}; '
                                                           f'error: {e}; params: {data}')
            return None

    # 往表格中追加数据行
    def append_value_excel(self, excel_msg, value):
        tenant_access_token = self.get_tenant_access_token()
        if not tenant_access_token:
            return False
        headers = {
            "Content-Type": "application/json; charset=utf-8",
            "Authorization": f"Bearer {tenant_access_token}"
        }
        url = self.append_value_url.replace("err_excel_token", excel_msg['token'])
        data = {"valueRange": {"range": f"{excel_msg['sheet_id']}!{excel_msg['range']}", "values": [value]}}
        try:
            raw = requests.post(url=url, json=data, headers=headers)
            if raw.json().get('code') == 0 and raw.json().get('msg') == 'success':
                return raw.json().get('data')
            else:
                error_logging.info({'url': url, 'error': raw.text, 'headers': headers, 'data': data})
                LarkMessageCenter().send_other_backend_message('飞书错误表格', 'error',
                                                               f'url:{url}; '
                                                               f'error: {raw.text}; data: {data}')
                return False
        except Exception as e:
            error_logging.info({'url': url, 'error': str(e), 'headers': headers, 'data': data})
            LarkMessageCenter().send_other_backend_message('飞书错误表格', 'error',
                                                           f'url:{url}; '
                                                           f'error: {e}; data: {data}')
            return False

    def append_value_excel_image(self, excel_msg, base_image, image_name):
        tenant_access_token = self.get_tenant_access_token()
        if not tenant_access_token:
            return False
        headers = {
            "Content-Type": "application/json; charset=utf-8",
            "Authorization": f"Bearer {tenant_access_token}"
        }
        url = self.append_image_value_url.replace("excel_token", excel_msg['token'])
        data = {
            "range": f"{excel_msg['sheet_id']}!{excel_msg['range']}",
            "image": base_image,
            "name": image_name
        }
        raw = requests.post(url=url, json=data, headers=headers)
        try:
            if raw.json().get('code') == 0 and raw.json().get('msg') == 'success':
                return True
            else:
                error_logging.info({'url': url, 'error': raw.text, 'headers': headers, 'data': data})
                LarkMessageCenter().send_other_backend_message('飞书错误表格', 'error',
                                                               f'url:{url}; '
                                                               f'error: {raw.text}; data: {data}')
                return False
        except Exception as e:
            error_logging.info({'url': url, 'error': str(e), 'headers': headers, 'data': data})
            LarkMessageCenter().send_other_backend_message('飞书错误表格', 'error',
                                                           f'url:{url}; '
                                                           f'error: {e}; data: {data}')
            return False

    # 错误列表追加行
    def err_excel_append_value(self, value):
        if settings.SITE_URL == 'https://www.qzcoach.com/':
            result = self.append_value_excel(self.err_excel_msg, value)
            return result
        return

    def send_excel_coach_info_value(self, value):
        result = self.append_value_excel(self.coach_info_excel_msg, value)
        return result

    def send_excel_value_image(self, base_image, image_name, value_range):
        coach_info_excel_msg = self.coach_info_excel_msg.copy()
        coach_info_excel_msg['range'] = value_range
        result = self.append_value_excel_image(coach_info_excel_msg, base_image, image_name)
        return result

