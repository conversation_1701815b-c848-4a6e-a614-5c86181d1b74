import json
import numpy as np

from django.db.models import <PERSON>, <PERSON><PERSON><PERSON>, Avg
from collections import Counter

from wisdom_v2.models import ProjectEvaluationReport, EvaluationAnswer, \
    PublicAttr, Project, UserAdditionalInfo, ProjectInterested, EvaluationQuestion, EvaluationModule
from wisdom_v2.views.constant import LBI_EVALUATION, ATTR_TYPE_EVALUATION_ANSWER
from wisdom_v2.enum.project_enum import ProjectEvaluationReportTypeEnum
from utils import task
from wisdom_v2.enum.service_content_enum import PdfReportTypeEnum

def generate_project_evaluation_report(project_id):
    report, created = ProjectEvaluationReport.objects.get_or_create(
        project_id=project_id, type=ProjectEvaluationReportTypeEnum.LBI_EVALUATION.value)
    project = Project.objects.get(pk=project_id)
    # 所有测评
    public_attrs = PublicAttr.objects.filter(project_id=project_id, target_user__isnull=False, user__isnull=False,
                                             type=ATTR_TYPE_EVALUATION_ANSWER)
    # 他评
    other_public_attr = PublicAttr.objects.filter(project_id=project_id, target_user__isnull=False,
                                                  user__isnull=False, type=ATTR_TYPE_EVALUATION_ANSWER).\
        exclude(user_id=F('target_user_id'))

    # 自评
    oneself_public_attr = PublicAttr.objects.filter(project_id=project_id, target_user__isnull=False,
                                                    user__isnull=False, type=ATTR_TYPE_EVALUATION_ANSWER,
                                                    user_id=F('target_user_id'))

    # 铁律 # 自评 user与target_user都是自己     他评 user是利益相关者  target_user是被教练者

    # 测评总人数
    self_users = oneself_public_attr.count()

    completion = {}
    completion['complete_count'] = self_users
    all_count = EvaluationModule.objects.filter(project_bundle__project_id=project_id, evaluation__code=LBI_EVALUATION,
                                                deleted=False).count()
    completion['all_count'] = all_count

    # 工作年限分类    改为管理年限
    year_5 = 0
    year_6_10 = 0
    year_11_20 = 0
    year_21 = 0
    year_count = 0

    # 司龄年限分类
    user_company_working_1 = 0
    user_company_working_3 = 0
    user_company_working_5 = 0
    user_company_working_10 = 0
    user_company_working_11 = 0
    user_company_working_count = 0

    # 性别分类
    sex_1 = 0
    sex_2 = 0
    sex_count = 0

    # 管理幅度
    employee_10 = 0
    employee_10_50 = 0
    employee_50_100 = 0
    employee_100_1000 = 0
    employee_1001 = 0
    employee_count = 0


    # 管理层级 1:一线经理 2:部门经理 3:事业部经理 4:事业部总经理 5:集团高管 6:首席执行官
    manage_role_1 = 0
    manage_role_2 = 0
    manage_role_3 = 0
    manage_role_4 = 0
    manage_role_5 = 0
    manage_role_6 = 0
    manage_role_count = 0

    # 学历
    junior = 0
    undergraduate = 0
    master = 0
    doctor = 0
    education_count = 0

    for one in oneself_public_attr:

        data = UserAdditionalInfo.objects.filter(user=one.user).first()
        if not data:  # 没有开屏测评数据 跳过 并且参与计算饼图的人数减1
            self_users -= 1
            continue
        # 工作年限  改为管理年限
        if data.often:
            year_count += 1
            user_working_time = data.often
            if user_working_time == '0-5年':
                year_5 += 1
            elif user_working_time == '6-10年':
                year_6_10 += 1
            elif user_working_time == '10-20年':
                year_11_20 += 1
            elif user_working_time == '20年以上':
                year_21 += 1
            else:
                year_5 += 1
        else:
            year_count += 1
            year_5 += 1

        # 学历分布
        if data.education:
            education_count += 1
            education = data.education
            if education == '专科':
                junior += 1
            elif education == '本科':
                undergraduate += 1
            elif education == '硕士':
                master += 1
            else:
                doctor += 1

        # 司龄年限
        if data.working_years:
            user_company_working_count += 1
            if data.working_years == '不满1年':
                user_company_working_1 += 1
            elif data.working_years == '1-3年':
                user_company_working_3 += 1
            elif data.working_years == '3-5年':
                user_company_working_5 += 1
            elif data.working_years == '5-10年':
                user_company_working_10 += 1
            elif data.working_years == '10年以上':
                user_company_working_11 += 1
        else:
            user_company_working_count += 1
            user_company_working_1 += 1

        # 性别分布
        user_sex = data.gender
        if user_sex is not None:
            sex_count += 1
            if user_sex == '男':
                sex_1 += 1
            else:
                sex_2 += 1

        # 管理幅度
        if data.personnel is not None:
            user_employee = data.personnel
            employee_count += 1
            if user_employee == '10人以内':
                employee_10 += 1
            elif user_employee == '10-50人':
                employee_10_50 += 1
            elif user_employee == '50-100人':
                employee_50_100 += 1
            elif user_employee == '100-1000人':
                employee_100_1000 += 1
            else:
                employee_1001 += 1

        # 管理层级
        if data.position:
            position = data.position
            manage_role_count += 1
            if position == '组长':
                manage_role_1 += 1
            elif position == '经理/高级经理':
                manage_role_2 += 1
            elif position == '总监/副总监':
                manage_role_3 += 1
            elif position == '事业部总经理/副总经理':
                manage_role_4 += 1
            elif position == 'VP/副总裁':
                manage_role_5 += 1
            elif position == 'C-level':
                manage_role_6 += 1

    data = {'report_name': '领导力行为指数评估', 'company_name': project.company.real_name, 'project_name': project.name,
            'report_date': report.created_at.strftime("%Y-%m-%d"), 'user_count': oneself_public_attr.count(),
            'is_push_manager': report.is_push_manager, 'user_group': '', 'report_id': report.pk,
            'completion': completion}

    # 优势和挑战
    pros_and_challenge = []

    # 测评群体-6个饼图组对象
    exam_user_list = []

    # 管理年限对象
    exam_user_working = {
        "class": "circle_list",
        "circle": [],
        "capacity": 1,
        "title": '管理年限分布',
    }
    working_circle = get_circle(title='管理年限分布', user_count=year_count,
                                sub_title=[{'label': '0-5年', 'value': year_5},
                                           {'label': '5-10年', 'value': year_6_10},
                                           {'label': '10-20年', 'value': year_11_20},
                                           {'label': '20年以上', 'value': year_21}]
                                )
    exam_user_working['circle'].append(working_circle)
    exam_user_list.append(exam_user_working)

    # 学历分布
    exam_user_education = {
        "class": "circle_list",
        "circle": [],
        "capacity": 1,
        "title": '学历分布',
    }
    education_circle = get_circle(title='学历分布', user_count=education_count,
                                  sub_title=[{'label': '专科', 'value': junior},
                                             {'label': '本科', 'value': undergraduate},
                                             {'label': '硕士', 'value': master},
                                             {'label': '博士', 'value': doctor}]
                                  )
    exam_user_education['circle'].append(education_circle)
    exam_user_list.append(exam_user_education)

    # 司龄年限对象
    exam_user_company_working = {
        "class": "circle_list",
        "circle": [],
        "capacity": 1,
        "title": '司龄年限分布',
    }

    company_working_circle = get_circle(title='司龄年限分布', user_count=user_company_working_count,
                                        sub_title=[{'label': '不满1年', 'value': user_company_working_1},
                                                   {'label': '1-3年', 'value': user_company_working_3},
                                                   {'label': '3-5年', 'value': user_company_working_5},
                                                   {'label': '5-10年', 'value': user_company_working_10},
                                                   {'label': '10年以上', 'value': user_company_working_11}]
                                        )
    exam_user_company_working['circle'].append(company_working_circle)
    exam_user_list.append(exam_user_company_working)

    # 性别分布
    exam_user_sex = {
        "class": "circle_list",
        "circle": [],
        "capacity": 1,
        "title": '性别分布',
    }

    sex_circle = get_circle(title='性别分布', user_count=sex_count,
                            sub_title=[{'label': '男', 'value': sex_1},
                                       {'label': '女', 'value': sex_2}]
                            )
    exam_user_sex['circle'].append(sex_circle)
    exam_user_list.append(exam_user_sex)

    # 管理幅度
    exam_user_underling = {
        "class": "circle_list",
        "circle": [],
        "capacity": 1,
        "title": '管理幅度分布',
    }
    underling_circle = get_circle(title='管理幅度分布 ', user_count=employee_count,
                                  sub_title=[{'label': '10人以内', 'value': employee_10},
                                             {'label': '10-50人', 'value': employee_10_50},
                                             {'label': '50-100人', 'value': employee_50_100},
                                             {'label': '100-1000人', 'value': employee_100_1000},
                                             {'label': '1000人以上', 'value': employee_1001}]
                                  )
    exam_user_underling['circle'].append(underling_circle)
    exam_user_list.append(exam_user_underling)

    # 管理层级
    exam_user_pyramid = {
        "class": "circle_list",
        "circle": [],
        "capacity": 1,
        "title": '管理层级分布',
    }
    pyramid_circle = get_circle(title='管理层级分布', user_count=manage_role_count,
                                sub_title=[{'label': '组长', 'value': manage_role_1},
                                           {'label': '经理/高级经理', 'value': manage_role_2},
                                           {'label': '总监/副总监', 'value': manage_role_3},
                                           {'label': '事业部总经理/副总经理', 'value': manage_role_4},
                                           {'label': 'VP/副总裁', 'value': manage_role_5},
                                           {'label': 'C-level', 'value': manage_role_6}]
                                )
    exam_user_pyramid['circle'].append(pyramid_circle)
    exam_user_list.append(exam_user_pyramid)

    # 测评群体信息
    data['exam_user'] = exam_user_list

    # 效能总览--网状态图
    spider_data = {'fixed_text': '这个部分展示了对12项领导力的群体认知对比。基于测评对象的自我评价，可以看到12项领导力之间的表现差距。',
                   'values_list': []}

    SPIDER_MAP = {
        'A': '以身作则',
        'B': '抗压复原',
        'C': '锐意进取',
        'D': '有效授权',
        'E': '激励赋能',
        'F': '绩效辅导',
        'G': '敏捷应变',
        'H': '领导变革',
        'J': '战略远见',
        'K': '系统思考',
        'L': '多方协同',
        'M': '果敢决策',
    }


    self_dic = get_json(oneself_public_attr)
    # 按照前端给的数据排序 ["以身作则", "抗压复原", "锐意进取", "有效授权", "激励赋能", "绩效辅导", "敏捷应变", "领导变革", "战略远见", "系统思考", "多方协同", "果敢决策"]
    spider_list = []
    for spider, score in self_dic.items():
        spider_list.append({'title': SPIDER_MAP[spider], 'values': round(np.mean(score['score']), 1) if score['score'] else 0})
        self_dic[spider]['avg'] = round(np.mean(score['score']), 2) if score['score'] else 0

    sort_spider = [{'title': '以身作则', 'values': ''}, {'title': '抗压复原', 'values': ''},
                   {'title': '锐意进取', 'values': ''}, {'title': '有效授权', 'values': ''},
                   {'title': '激励赋能', 'values': ''}, {'title': '绩效辅导', 'values': ''},
                   {'title': '敏捷应变', 'values': ''}, {'title': '领导变革', 'values': ''},
                   {'title': '战略远见', 'values': ''},
                   {'title': '系统思考', 'values': ''}, {'title': '多方协同', 'values': ''},
                   {'title': '果敢决策', 'values': ''}]
    for i in spider_list:
        for s in sort_spider:
            if i['title'] == s['title']:
                s['values'] = [i['values']]
    spider_data['values_list'] = sort_spider

    # 领导力效能总览
    leadership_efficiency = []

    # 添加网状图数据
    leadership_efficiency.append({'class': 'spider', 'values': spider_data})
    # ============================================= 柱状图（趋同度） =================================================
    # 效能总览--柱状图（趋同度）
    # top_text_list 上方文字 steps
    # bar value 是从下往上的y值（条行上的值） label是条行上的文字

    histogram_data = {'class': 'bar', 'title': '',
                      'splitArea': False, 'is_overlap': True,
                      'fixed_text': '本部分展示了所有测评对象基于自我评价视角的领导力有效性分布，按照群体趋同度由高到低进行排序，反应了管理者视角下的群体领导力有效性现状。',
                      'top_text_list': [],
                      'steps': [{'title': '效能不佳', 'color': '#EE6666'},
                                {'title': '相对有效', 'color': 'pink'},
                                {'title': '稳定发挥', 'color': '#5470c6'},
                                {'title': '充分发挥', 'color': '#91cc75'}], 'axisx': [], 'axisy': [], 'bar': []
                      }

    bar_list = []
    histogram_data_axisy_label = []
    histogram_label = []
    histogram = {'class': 'bar', 'title': '',
                 'splitArea': True, 'is_overlap': False,
                 'fixed_text': '本部分展示了所有测评对象视角和视角对比之下的领导力有效性评价，按照自评和他评的一致性由高到低进行排序，从不同维度反应了群体领导力有效性现状。',
                 'top_text_list': ['效能不佳', '', '相对有效', '稳定发挥', '充分发挥'],
                 'steps': [{'title': '自评', 'color': '#EE6666'},
                           {'title': '他评', 'color': '#5470c6'}], 'axisx': [], 'axisy': [], 'bar': []
                 }

    histogram_axisx = {'label': '', 'bar_label': [1, 2, 3, 4, 5, 6], 'range': [1, 6], 'hideLable': True, 'interval': 1}

    for n, s in self_dic.items():
        cnt = dict(Counter(s['score']))
        for k, v in cnt.items():
            cnt[k] = v / len(s['score'])
        # 把小于3的加在3上
        if cnt.get(1):
            if cnt.get(3):
                cnt[3] += cnt.get(1)
            else:
                cnt[3] = cnt.get(1)
            cnt.pop(1)

        if cnt.get(2):
            if cnt.get(3):
                cnt[3] += cnt.get(2)
            else:
                cnt[3] = cnt.get(2)
            cnt.pop(2)

        self_dic[n]['percentage'] = cnt
    for k, v in self_dic.items():
        if v['score']:
            self_dic[k]['var'] = np.var(v['score'])
        else:
            self_dic[k]['var'] = 0

    # 按照自评方差排序
    sort_data = dict(sorted(self_dic.items(), key=lambda item: item[1]['var'], reverse=True))
    dic_3 = {'value': [], 'color': [], 'label': []}
    dic_4 = {'value': [], 'color': [], 'label': []}
    dic_5 = {'value': [], 'color': [], 'label': []}
    dic_6 = {'value': [], 'color': [], 'label': []}
    for _key, _val in sort_data.items():
        # y 轴名称排序 '%.2f%%' % (a * 100)
        histogram_data_axisy_label.append(SPIDER_MAP[_key] + str(_val['avg']))
        dic_3['value'].append(_val['percentage'].get(3))
        dic_3['label'].append(str(int(_val['percentage'].get(3, 0) * 100 + 0.5)) + '%')
        dic_3['color'].append('#ffc684')
        dic_4['value'].append(_val['percentage'].get(4))
        dic_4['label'].append(str(int(_val['percentage'].get(4, 0) * 100 + 0.5)) + '%')
        dic_4['color'].append('#97b3ee')
        dic_5['value'].append(_val['percentage'].get(5))
        dic_5['label'].append(str(int(_val['percentage'].get(5, 0) * 100 + 0.5)) + '%')
        dic_5['color'].append('#97d2ee')
        dic_6['value'].append(_val['percentage'].get(6))
        dic_6['label'].append(str(int(_val['percentage'].get(6, 0) * 100 + 0.5)) + '%')
        dic_6['color'].append('#a591f1')

    bar_list.append(dic_3)
    bar_list.append(dic_4)
    bar_list.append(dic_5)
    bar_list.append(dic_6)

    # 柱状图bar
    histogram_data['bar'] = bar_list

    histogram_data_axisx = {'label': '', 'bar_label': [1, 2, 3, 4, 5, 6], 'range': [0], 'interval': True,
                            'hideLable': True, }
    histogram_data_axisy = {'label': '', 'bar_label': []}
    histogram_data_axisy['bar_label'] = histogram_data_axisy_label

    # 柱状图x
    histogram_data['axisx'].append(histogram_data_axisx)
    # 柱状图y
    histogram_data['axisy'].append(histogram_data_axisy)
    # 添加柱状图-趋同度
    leadership_efficiency.append({'class': 'histogram_1', 'values': histogram_data})

    # ============================================= 柱状图（一致性） =================================================
    # 临时存储他评平均值
    other_data = get_json(other_public_attr)

    for s, t in other_data.items():
        if t['score']:
            other_data[s]['avg'] = round(np.mean(t['score']), 1)
            other_data[s]['var'] = round(np.var(t['score']), 1)
        else:
            other_data[s]['avg'] = 0
            other_data[s]['var'] = 0

    histogram_bar_list = []

    # 按照自评+他评方差排序
    for sk, sv in self_dic.items():
        for _k, _v in other_data.items():
            if sk == _k:
                self_dic[sk]['all_var'] = round(np.var(self_dic[sk]['score'] + other_data[_k]['score']), 1)
                other_data[_k]['all_var'] = round(np.var(self_dic[sk]['score'] + other_data[_k]['score']), 1)

    # 自评 他评排序取值
    self_dic = dict(sorted(self_dic.items(), key=lambda item: item[1]['all_var'], reverse=True))
    other_data = dict(sorted(other_data.items(), key=lambda item: item[1]['all_var'], reverse=True))
    dic_self = {'height': 15, 'value': [], 'color': [], 'label': []}
    dic_other = {'height': 15, 'value': [], 'color': [], 'label': []}
    for k, v in other_data.items():
        histogram_label.append(SPIDER_MAP[k])
        dic_other['value'].append(v['avg'])
        dic_other['label'].append(v['avg'])
        dic_other['color'].append('#EE6666')

    for value in self_dic.values():
        dic_self['value'].append(value['avg'])
        dic_self['label'].append(value['avg'])
        dic_self['color'].append('#5470c6')

    histogram_bar_list.append(dic_self)
    histogram_bar_list.append(dic_other)
    # 柱状图（自评 他评）bar
    histogram['bar'] = histogram_bar_list
    # 柱状图（自评 他评）x
    histogram['axisx'].append(histogram_axisx)
    # 柱状图（自评 他评）y
    histogram_axisy = {'label': '', 'bar_label': histogram_label}
    histogram['axisy'].append(histogram_axisy)
    # 添加柱转图- 一致性
    leadership_efficiency.append({'class': 'histogram_2', 'values': histogram})

    # 领导效能
    data['leadership_efficiency'] = leadership_efficiency

    # ===================================================================

    self_dic_all = sorted(self_dic.items(), key=lambda item: item[1]['all_var'], reverse=False)
    dic_var_6 = dict(self_dic_all[0:6])
    for k, v in dic_var_6.items():
        v['all_avg'] = self_dic.get(k)['avg'] + other_data.get(k)['avg']
    # reverse=True 从大到小
    dic_var_6_sort = sorted(dic_var_6.items(), key=lambda item: item[1]['all_avg'], reverse=True)

    # 优势
    advantage = dict(dic_var_6_sort[0:3])
    # advantage_histogram_list = get_default_histogram(advantage, other_data, project_id)
    advantage_histogram_list = get_line_chart_data(advantage, project_id)
    advantage_data = {'title': '群体优势', 'fixed_text': '在群体评价高度趋同的领导能力项中，以下三项领导能力项的自评和他评一致性高且得分最高，说明该群体在这三项领导力中呈现出了明显优势：',
                      'histogram_list': advantage_histogram_list, 'question': {}}

    # 挑战
    challenge = dict(dic_var_6_sort[3:6])
    # challenge_histogram_list = get_default_histogram(challenge, other_data, project_id)
    challenge_histogram_list = get_line_chart_data(challenge, project_id)
    challenge_data = {'title': '群体挑战', 'fixed_text': '在群体评价高度趋同的领导能力项中，以下三项领导能力项的自评和他评一致性高且得分最低，说明该群体在这三项领导力中呈现出了明显挑战：',
                      'histogram_list': challenge_histogram_list, 'question': {}}

    # 盲区
    unknown = dict(self_dic_all[6:9])
    # unknown_histogram_list = get_default_histogram(unknown, other_data, project_id)
    unknown_histogram_list = get_line_chart_data(unknown, project_id)
    unknown_data = {'title': '群体盲区', 'fixed_text': '在群体评价高度趋同的领导能力项中，以下三项领导能力项的自我评价和他人评价差异较大，说明以下该群体在这三项领导力中存在盲区：',
                    'histogram_list': unknown_histogram_list, 'question': {}}

    # 发展
    progress = dict(self_dic_all[9:12])
    # progress_histogram_list = get_default_histogram(progress, other_data, project_id)
    progress_histogram_list = get_line_chart_data(progress, project_id)
    progress_data = {'title': '个性化发展项', 'fixed_text': '在群体自我评价差异较大的领导能力项中，该群体对以下三项领导力评价的趋同度最低：',
                     'histogram_list': progress_histogram_list, 'question': {}}

    pros_and_challenge.append(advantage_data)
    pros_and_challenge.append(challenge_data)
    pros_and_challenge.append(unknown_data)
    pros_and_challenge.append(progress_data)
    data['pros_and_challenge'] = pros_and_challenge

    one_self_uuids = list(oneself_public_attr.values_list('uuid', flat=True))
    other_uuids = list(other_public_attr.values_list('uuid', flat=True))
    one_self_answer = EvaluationAnswer.objects.filter(public_attr_id__in=one_self_uuids)
    other_answer = EvaluationAnswer.objects.filter(public_attr_id__in=other_uuids)

    self_sort = one_self_answer.values('option__question_id', 'option__question__order').annotate(score_var=Variance('option__score')).distinct()
    other_sort = other_answer.values('option__question_id', 'option__question__order').annotate(score_var=Variance('option__score')).distinct()
    for self_obj in self_sort:
        for other_obj in other_sort:
            if self_obj['option__question__order'] == other_obj['option__question__order']:
                self_obj['score_var'] += other_obj['score_var']
                break
    self_sort_data = sorted(self_sort, key=lambda item: item['score_var'], reverse=False)
    # 发展-问题
    progress_list = self_sort_data[len(self_sort_data) - 5:]
    progress_question_dic = {'fixed_text': '在这三项领导力中，该群体自我评价差异最大的五项领导行为是：', 'question_value': []}
    for pro in progress_list:
        # title = models.AppEvaluationquestion.objects.get(pk=pro['EqId']).Title
        title = EvaluationQuestion.objects.get(pk=pro['option__question_id']).title
        progress_question_dic['question_value'].append({'question_content': title + '。', 'background_color': '#ee6666'})
    # 盲区-问题 self_sort前50%计算自评和他评分数合并后的方差
    res = self_sort_data[0:int(len(self_sort_data) / 2)]
    index_ids = [i['option__question__order'] for i in res]
    all_uuids = list(public_attrs.values_list('uuid', flat=True))
    all_answers = EvaluationAnswer.objects.filter(public_attr_id__in=all_uuids)
    all_res = all_answers.filter(option__question__order__in=index_ids).values('option__question__order').annotate(t_var=Variance('option__score')).order_by('t_var')
    unknown_list = all_res[len(all_res) - 5:]
    unknown_question_dic = {'fixed_text': '在这三项领导力中，该群体自我评价和他人评价偏差最大的五项领导行为是：', 'question_value': []}
    for unk in unknown_list:
        title = EvaluationQuestion.objects.filter(order=unk['option__question__order'], evaluation__code=LBI_EVALUATION).first().title
        unknown_question_dic['question_value'].append({'question_content': title + '。', 'background_color': '#ee6666'})
    last_list = all_res[0:int(len(all_res) / 2)]
    index_ids_last = [i['option__question__order'] for i in last_list]
    all_res_last = all_answers.filter(option__question__order__in=index_ids_last).values('option__question__order').annotate(t_avg=Avg('option__score')).order_by(
        '-t_avg')

    # 优势-问题
    advantage_question_dic = {'fixed_text': '在这三项领导力中，该群体表现最佳的五项领导行为是：', 'question_value': []}
    for height_avg in all_res_last[0:5]:
        title = EvaluationQuestion.objects.filter(order=height_avg['option__question__order'], evaluation__code=LBI_EVALUATION).first().title
        advantage_question_dic['question_value'].append({'question_content': title + '。', 'background_color': '#ee6666'})
    # 挑战-问题
    challenge_question_dic = {'fixed_text': '在这三项领导力中，该群体最需要进步的五项领导行为是：', 'question_value': []}
    for low_avg in all_res_last[len(all_res_last) - 5:]:
        title = EvaluationQuestion.objects.filter(order=low_avg['option__question__order'], evaluation__code=LBI_EVALUATION).first().title
        challenge_question_dic['question_value'].append(
            {'question_content': title + '。', 'background_color': '#ee6666'})
    progress_data['question'] = progress_question_dic
    unknown_data['question'] = unknown_question_dic
    challenge_data['question'] = challenge_question_dic
    advantage_data['question'] = advantage_question_dic
    task.get_user_pdf_url.delay(report.pk, f'{project.name}_LBI领导力行为指数评估群体报告_{report.id}.pdf', pdf_type=PdfReportTypeEnum.lbi_project_report.value)
    data['status'] = 1
    report.lbi_analysis = data
    report.save()


def get_circle(title, user_count, sub_title, is_line=None):
    """
    title: 圆主题
    user_count 测评人数
    sub_title 圆下方小标题 list 0: 颜色 1: 数量（求百分比用）
    is_line 是否带引线的圆
    示例：{'5年以内': ['#EE6666',  3], '10年以内': ['#5470c6',  4], '15年以内': ['#fac858',  5], '超过15年': ['#91cc75',  6]}
    """
    values_content = []
    subtitle = []
    color_set = ['#a591f1', '#97d2ee', '#97b3ee', '#ffc684']
    for item in sub_title:
        dic_content = {}
        dic_obj = {}
        if item['value']:
            ratio = str(int(item['value'] / user_count * 100 + 0.5)) if item['value'] else '0'
            dic_obj['color'] = color_set[len(subtitle) % len(color_set)]
            dic_obj['value'] = ratio
            dic_content['color'] = color_set[len(subtitle) % len(color_set)]
            circle_value = item['label'] + '\n' + ratio + '%'
            dic_content['value'] = ratio
            dic_content['label'] = circle_value
            subtitle.append(dic_obj)
            values_content.append(dic_content)
    dic = {'class': 'circle', 'title': '', 'subtitle': '',
           'values': values_content, 'value_desc': subtitle}
    return dic


def get_json(queryset):
    # A以身作则 B抗压复原 C锐意进取 D有效授权 E激励赋能 F绩效辅导 G敏捷应变 H领导变革 J战略远见 K系统思考 L多方协同 M果敢决策
    dic = {
        'A': {'sort': [2, 10, 12, 14, 35, 63], 'score': [], 'user_id': []},
        'B': {'sort': [13, 14, 21, 36, 50, 57, 67, 69, 76], 'score': [], 'user_id': []},
        'C': {'sort': [22, 25, 28, 47], 'score': [], 'user_id': []},
        'D': {'sort': [8, 18, 43, 45, 52, 61], 'score': [], 'user_id': []},
        'E': {'sort': [7, 1, 32, 40, 46, 58, 72], 'score': [], 'user_id': []},
        'F': {'sort': [30, 41, 48, 62, 73], 'score': [], 'user_id': []},
        'G': {'sort': [5, 15, 27, 51, 59, 77], 'score': [], 'user_id': []},
        'H': {'sort': [6, 17, 23, 29, 54, 71, 78], 'score': [], 'user_id': []},
        'J': {'sort': [3, 4, 9, 20, 26, 37, 53], 'score': [], 'user_id': []},
        'K': {'sort': [19, 56, 60, 39, 42, 64, 66, 68, 75], 'score': [], 'user_id': []},
        'L': {'sort': [11, 33, 34, 44, 55, 65, 70, 74], 'score': [], 'user_id': []},
        'M': {'sort': [1, 31, 38, 49], 'score': [], 'user_id': []},
    }
    ids_list = list(queryset.values_list('uuid', flat=True))
    evaluation_answer = EvaluationAnswer.objects.filter(public_attr_id__in=ids_list)
    for r in evaluation_answer:
        for _k, _v in dic.items():
            if r.option.question.order in _v['sort']:
                dic[_k]['score'].append(r.option.score)
                dic[_k]['user_id'].append(r.public_attr.user_id)
    return dic


def get_default_histogram(query_dict, other_dict, project_id):
    default_histogram_list = []

    # 获取全部他评
    other_public_attr = PublicAttr.objects.filter(project_id=project_id, target_user__isnull=False,
                                                  user__isnull=False, type=ATTR_TYPE_EVALUATION_ANSWER). \
        exclude(user_id=F('target_user_id'))
    ids_list = list(other_public_attr.values_list('uuid', flat=True))
    evaluation_answer = EvaluationAnswer.objects.filter(public_attr_id__in=ids_list)


    for adv_k, adv in query_dict.items():
        default_histogram = {'class': 'bar', 'title': SPIDER_MAP[adv_k],
                             'subtitle': POWER_TAG_SUBTITLE[SPIDER_MAP[adv_k]],
                             'splitArea': False, 'is_overlap': False,
                             'fixed_text': '', 'top_text_list': '',
                             'steps': [],
                             'axisx': [{'label': '', 'bar_label': [1, 2, 3, 4, 5, 6], 'range': [-2, 3]}],
                             'axisy': [{'label': '', 'bar_label': ['自评', '下级', '平级', '上级']}],
                             'bar': []
                             }
        default_bar = {'value': [], 'color': [], 'label': []}
        default_bar['value'].append(round(adv['avg'] - 3, 1))
        default_bar['label'].append(adv['avg'])
        default_bar['color'].append('#a591f0')
        bar_label = ['自评']

        for other_k, other_v in other_dict.items():
            if adv_k == other_k:
                # 先获取该领导力对应的所有问题
                # 再获取问题的所有他评回答
                # 根据回答者和测评对象的关系做分组
                # 计算各分组的平均分
                # eq_id_list = models.AppEvaluationquestion.objects.filter(EvaluationId=18, behaviour=adv_k).values_list('EQid', flat=True)
                # res = models.AppEvaluationresult.objects.filter(Userid__in=other_v['user_id'],
                #                                                 EvaluationId=18,
                #                                                 Interested_Id__in=adv['user_id'],
                #                                                 EqId__in=eq_id_list).values('Userid', 'Score', 'Interested_Id')

                first_list = []
                second_list = []
                third_list = []
                for item in evaluation_answer.filter(option__question__order__in=other_v['sort']):
                    member = ProjectInterested.objects.filter(interested=item.public_attr.user,
                                                              master=item.public_attr.target_user,
                                                              project_id=project_id, deleted=False).first()
                    if member:
                        if member.relation == '3':
                            third_list.append(item.option.score)

                        if member.relation == '2':
                            second_list.append(item.option.score)

                        if member.relation == '1':
                            first_list.append(item.option.score)

                # 根据插入顺序决定页面显示的顺序
                if third_list:
                    default_bar['value'].append(round(np.mean(third_list) - 3, 1))
                    default_bar['color'].append('#97d2ee')
                    default_bar['label'].append(round(np.mean(third_list), 1))
                    bar_label.append('下级')

                if second_list:
                    default_bar['value'].append(round(np.mean(second_list) - 3, 1))
                    default_bar['color'].append('#97b2ed')
                    default_bar['label'].append(round(np.mean(second_list), 1))
                    bar_label.append('平级')

                if first_list:
                    default_bar['value'].append(round(np.mean(first_list) - 3, 1))
                    default_bar['color'].append('#ffc682')
                    default_bar['label'].append(round(np.mean(first_list), 1))
                    bar_label.append('上级')


        default_histogram['bar'].append(default_bar)
        # 根据实际关系设置轴上的文案
        default_histogram['axisy'][0]['bar_label'] = bar_label
        default_histogram_list.append(default_histogram)
    return default_histogram_list


SPIDER_MAP = {
        'A': '以身作则',
        'B': '抗压复原',
        'C': '锐意进取',
        'D': '有效授权',
        'E': '激励赋能',
        'F': '绩效辅导',
        'G': '敏捷应变',
        'H': '领导变革',
        'J': '战略远见',
        'K': '系统思考',
        'L': '多方协同',
        'M': '果敢决策',
    }


POWER_TAG_SUBTITLE = {
        '以身作则': '能够在行为中展现自身价值观，并保持谦逊与成长',
        '抗压复原': '可以较快地从挫折中回复，坚韧不拔',
        '锐意进取': '对工作有激情和报负，为实现更大的成就而持续突破',
        '有效授权': '促进团队成员为集体目标担责',
        '激励赋能': '令员工积极主动地投入工作，并能从中获得价值感',
        '绩效辅导': '通过辅导与教练支持每个员工追求卓越绩效',
        '敏捷应变': '在业务和管理上快速迭代，不断创新',
        '领导变革': '引领方向，限定策略，促使变革成功',
        '战略远见': '目光长远，能够把我机会实现愿景',
        '系统思考': '以多元的视角，立足全局进行思考',
        '多方协同': '充分发挥各方优势协同共进，实现多赢的合作',
        '果敢决策': '基于分析判断，及时果断做出决策',
}


def get_line_chart_data(query_dict, project_id):
    # 以员工为单位获取每个员工的自评， 上级， 下级， 平级的平均分
    results = []
    # 自评的所有回答
    self_evaluation_answer = EvaluationAnswer.objects.filter(
        public_attr__project_id=project_id, public_attr__target_user__isnull=False,
        public_attr__user__isnull=False, public_attr__type=ATTR_TYPE_EVALUATION_ANSWER,
        public_attr__user_id=F('public_attr__target_user_id'))

    oneself_public_attr = PublicAttr.objects.filter(project_id=project_id, target_user__isnull=False,
                                                    user__isnull=False, type=ATTR_TYPE_EVALUATION_ANSWER,
                                                    user_id=F('target_user_id'))

    # 他评的所有回答
    other_evaluation_answer = EvaluationAnswer.objects.filter(
        public_attr__project_id=project_id, public_attr__target_user__isnull=False,
        public_attr__user__isnull=False, public_attr__type=ATTR_TYPE_EVALUATION_ANSWER).exclude(
        public_attr__user_id=F('public_attr__target_user_id'))

    for adv_k, adv_value in query_dict.items():
        # 自评平均分
        self_avg_score = self_evaluation_answer.filter(option__question__order__in=adv_value['sort']).\
            aggregate(score=Avg('option__score'))['score']
        self_avg_score = round(self_avg_score, 2) if self_avg_score else 0

        # 他评平均分
        other_evaluation_answer_adv_k = other_evaluation_answer.filter(option__question__order__in=adv_value['sort'])
        superior_avg_lst = []
        junior_avg_lst = []
        peers_avg_lst = []
        for item in other_evaluation_answer_adv_k:
            member = ProjectInterested.objects.filter(interested=item.public_attr.user,
                                                      master=item.public_attr.target_user,
                                                      project_id=project_id, deleted=False).first()
            if member:
                if member.relation == '1':
                    superior_avg_lst.append(item.option.score)

                if member.relation == '3':
                    junior_avg_lst.append(item.option.score)

                if member.relation == '2':
                    peers_avg_lst.append(item.option.score)
        superior_avg_score = round(sum(superior_avg_lst) / len(superior_avg_lst), 2) if\
            sum(superior_avg_lst) != 0 and len(superior_avg_lst) != 0 else 0
        junior_avg_score = round(sum(junior_avg_lst) / len(junior_avg_lst), 2) if\
            sum(junior_avg_lst) != 0 and len(junior_avg_lst) != 0 else 0
        peers_avg_score = round(sum(peers_avg_lst) / len(peers_avg_lst), 2) if\
            sum(peers_avg_lst) != 0 and (len(peers_avg_lst)) != 0 else 0

        # 折线图数据
        self_avg_score_lst = []
        superior_avg_score_lst = []
        peers_avg_score_lst = []
        junior_avg_score_lst = []
        user_name_lst = []

        for public_attr in oneself_public_attr:
            # 每个用户的自评平均分列表
            score = self_evaluation_answer.filter(
                       public_attr_id=public_attr.uuid,
                       option__question__order__in=adv_value['sort']).aggregate(score=Avg('option__score'))['score']
            score = round(score, 2) if score else None
            self_avg_score_lst.append(score)

            # 用户姓名
            user_name_lst.append(public_attr.user.cover_name)

            # 每个用户的他评
            other_answer = other_evaluation_answer.filter(public_attr__target_user_id=public_attr.user_id,
                                                          option__question__order__in=adv_value['sort'])
            superior_score_lst = []
            junior_score_lst = []
            peers_score_lst = []
            for item in other_answer:
                member = ProjectInterested.objects.filter(interested=item.public_attr.user,
                                                          master=item.public_attr.target_user,
                                                          project_id=project_id, deleted=False).first()
                if member:
                    if member.relation == '1':
                        superior_score_lst.append(item.option.score)

                    if member.relation == '3':
                        junior_score_lst.append(item.option.score)

                    if member.relation == '2':
                        peers_score_lst.append(item.option.score)
            superior_avg_score_lst.append(round(sum(superior_score_lst)/len(superior_score_lst), 2) if
                                          sum(superior_score_lst) != 0 and len(superior_score_lst) else None)

            peers_avg_score_lst.append(round(sum(peers_score_lst) / len(peers_score_lst), 2) if
                                       sum(peers_score_lst) != 0 and len(peers_score_lst) else None)

            junior_avg_score_lst.append(round(sum(junior_score_lst) / len(junior_score_lst), 2) if
                                        sum(junior_score_lst) != 0 and len(junior_score_lst) else None)

        data = {
            'title': SPIDER_MAP[adv_k],
            'subtitle': POWER_TAG_SUBTITLE[SPIDER_MAP[adv_k]],
            'self_avg_score': self_avg_score,
            'superior_avg_score': superior_avg_score,
            'junior_avg_score': junior_avg_score,
            'peers_avg_score': peers_avg_score,
            'self_avg_score_list': self_avg_score_lst,
            'superior_avg_score_list': superior_avg_score_lst,
            'peers_avg_score_list': peers_avg_score_lst,
            'junior_avg_score_list': junior_avg_score_lst,
            'user_name_list': user_name_lst
        }
        results.append(data)

    return results