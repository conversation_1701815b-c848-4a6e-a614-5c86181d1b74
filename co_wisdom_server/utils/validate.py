import re
import jwt

from django.conf import settings
from rest_framework import exceptions


def validate_email(email):
    if not email:
        return False
    if not re.match(r'^[A-Za-z0-9]+([-_.][A-Za-z0-9]+)*@([A-Za-z0-9]+[-.])+[A-Za-z0-9]{2,5}$', email):
        return False
    return True


def validate_phone(phone):
    if not phone:
        return False
    if not re.match(r"^1[3456789][0-9]{9}$", phone):
        return False
    return True


def get_not_token_user_id(request):
    token = request.headers.get('Authorization')
    if token:
        try:
            data = jwt.decode(token[7:], settings.SECRET_KEY, algorithms=['HS256'])
            return data.get('id')
        except:
            raise exceptions.AuthenticationFailed({'retCode': 401, 'retMsg': "登录过期 | Fail", 'err': '登录过期'})
    return