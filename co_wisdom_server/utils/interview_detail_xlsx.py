import pendulum
from django.conf import settings

from utils import randomPassword, aliyun, excel_pubilc
from wisdom_v2.views import constant
from wisdom_v2.views.constant import ADMIN_PROJECT_USER, ADMIN_PERSONAL_USER
from wisdom_v2.views.project_interview_action import ProjectInterviewDetailListSerializer


def get_interview_detail_xlsx(queryset, params):

    title = [
        "客户姓名",
        "客户手机号",
        "项目名称",
        "教练姓名",
        "支付状态",
        "活动主题",
        "辅导次数",
        "辅导议题",
        "用户类型",
        "预约时间",
        "辅导时间",
        "辅导状态",
        "填写状态",
        "结算状态",
        "实付金额",
        "辅导时长",
        "投入度",
        "有效度",
        "满意度",
        "推荐度",
    ]
    data = [title]
    for item in queryset:
        try:
            raw_data = ProjectInterviewDetailListSerializer(item).data
            user_phone = item.public_attr.target_user.phone
            is_settlement = raw_data.get('is_settlement')
            is_settlement = '已结算' if is_settlement else '未结算'

            # 客户类型计算
            coachee_type = raw_data.get('coachee_type')
            if coachee_type == ADMIN_PROJECT_USER:
                coachee_type = "企业客户"
            elif coachee_type == ADMIN_PERSONAL_USER:
                coachee_type = "个人客户"

            # 订单状态
            order_state = raw_data.get('order_state')
            if order_state == constant.ADMIN_ORDER_STATE_PENDING_PAY:
                order_state = '待付款'
            elif order_state == constant.ADMIN_ORDER_STATE_CLOSURE:
                order_state = '已关闭'
            elif order_state == constant.ADMIN_ORDER_STATE_COMPLETE:
                order_state = '已支付'
            elif order_state == constant.ADMIN_ORDER_STATE_REFUND:
                order_state = '已退款'
            data.append([
                raw_data.get('coachee_name'),
                user_phone,
                raw_data.get('project_name'),
                raw_data.get('coach_name'),
                order_state,
                raw_data.get("activity_theme"),
                raw_data.get("interview_number"),
                raw_data.get('topic'),
                coachee_type,
                raw_data.get('created_time'),
                raw_data.get('interview_time'),
                raw_data.get('interview_status'),
                raw_data.get('interview_record_status'),
                is_settlement,
                raw_data.get('payer_amount'),
                raw_data.get('times'),
                raw_data.get('target_progress_score'),
                raw_data.get('harvest_score'),
                raw_data.get('satisfaction_score'),
                raw_data.get('recommend_score'),
            ])
        except Exception as e:
            data.append([])

    if params.get('start_time') or params.get('end_time'):
        str_msg = f"{params.get('start_time') }-{params.get('end_time') }"
    else:
        str_msg = 'all'

    #  文件名
    name = f'admin/interview_detail/{pendulum.now().to_date_string()}/{str_msg}{randomPassword()}.xlsx'

    # 获取文件数据
    file = excel_pubilc.save_excel(data, 'interview_detail')

    # 上传阿里云
    aliyun.AliYun('cwcoach').send_file(name, file.getvalue())

    return f'{settings.ALIYUN_SDN_BASE_URL}/{name}'
