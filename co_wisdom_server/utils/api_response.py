from rest_framework import status
from rest_framework.response import Response

from wisdom_v2.enum.user_enum import UserR<PERSON>Enum
from wisdom_v2.models import ProjectInterview
import datetime
from rest_framework.exceptions import ValidationError, _get_error_details


def success_response(data=None, request=None, kwargs: dict = None, business=None):
    response_data = {'retCode': 200,
                     'retMsg': "成功 | Success",
                     'business': {},
                     'data': {}}
    if data is not None:
        response_data['data'] = data
    if kwargs:
        response_data.update(kwargs)
    if business:
        response_data['business'].update(business)
    if isinstance(data, Response):
        results = data.data.pop('results')
        data.data.update(response_data)
        data.data['data'] = results
        return data

    if request and request.user.pk:
        try:
            role = request.headers.get('role')
            role = int(role)
        except Exception:
            return Response(response_data, status=status.HTTP_200_OK)

        # 查询大于当前时间且在十分钟内的辅导
        # 教练已同意的辅导
        queryset = ProjectInterview.objects.filter(
            public_attr__start_time__gt=datetime.datetime.now(),
            is_coach_agree=True,
            public_attr__start_time__lte=datetime.datetime.now() + datetime.timedelta(minutes=10),
            public_attr__status=3,
            deleted=False,
            public_attr__type=1,
        ).order_by('public_attr__start_time')
        if role == UserRoleEnum.coach:
            # B端教练辅导列表B&C都展示，不做项目判断
            interview = queryset.filter(public_attr__user=request.user).first()
        elif role == UserRoleEnum.trainee_coach:
            # C端教练只查询C端辅导
            interview = queryset.filter(public_attr__user=request.user, public_attr__project__isnull=True).first()
        elif role == UserRoleEnum.coachee:
            # B端被教只查询B端辅导
            interview = queryset.filter(public_attr__target_user=request.user,
                                        public_attr__project__isnull=False).first()
        elif role == UserRoleEnum.trainee_coachee:
            # C端被教只查询C端辅导
            interview = queryset.filter(public_attr__target_user=request.user,
                                        public_attr__project__isnull=True).first()
        else:
            interview = None
        if interview:
            meeting = interview.interview_meeting.filter(deleted=False).first()
            if meeting:
                # 返回会议信息
                meeting_info = {
                    'id': str(meeting.id),
                    'type': str(meeting.channel_type),
                    'meeting_id': meeting.meeting_id,
                    'meeting_code': meeting.meeting_code,
                }
            else:
                meeting_info = None

            response_data['business'].update({
                'interview_id': interview.pk,
                'start_time': interview.public_attr.start_time.strftime('%Y-%m-%d %H:%M'),
                'place_category': interview.place_category,
                "meeting_info": meeting_info
            })
    return Response(response_data, status=status.HTTP_200_OK)


def parameter_error_response(err=None, kwargs: dict = None):
    response_data = {'retCode': 400,
                     'retMsg': u"参数错误 | Fail"}
    if err is not None:
        # 2.12.1保留err参数，后续版本再移除
        response_data['retMsg'] = err
        response_data['err'] = err
    if kwargs:
        response_data.update(kwargs)
    return Response(response_data, status=status.HTTP_200_OK)

def biz_error_response(code, msg, kwargs: dict = None):
    response_data = {'retCode': code,
                     'retMsg': msg}
    if kwargs:
        response_data.update(kwargs)
    return Response(response_data, status=status.HTTP_200_OK)


class WisdomValidationError(ValidationError):
    '''
        继承ValidationError，在序列化方法中使用向前端抛出响应信息
    '''
    status_code = status.HTTP_200_OK
    default_detail = {'retCode': 400,
                      'retMsg': u"参数错误 | Fail"}
    default_code = 'invalid'

    def __init__(self, detail=None, code=None):
        if detail is None:
            detail = self.default_detail
        if code is None:
            code = self.default_code
        if detail is not None:
            # 2.12.1保留err参数，后续版本再移除
            self.default_detail['retMsg'] = detail
            self.default_detail['err'] = detail
            detail = self.default_detail

        self.detail = _get_error_details(detail, code)


class WorkWechatUserError(ValidationError):
    '''
        继承ValidationError，在序列化方法中使用向前端抛出响应信息
    '''
    status_code = status.HTTP_200_OK
    default_detail = {'retCode': 200,
                      'retMsg': u"失败 | 账户已被绑定"}
    default_code = 'invalid'

    def __init__(self, detail=None, code=None):
        if detail is None:
            detail = self.default_detail
        if code is None:
            code = self.default_code
        if detail is not None:
            self.default_detail['data'] = detail
            detail = self.default_detail

        self.detail = _get_error_details(detail, code)
