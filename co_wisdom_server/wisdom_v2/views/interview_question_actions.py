from rest_framework import serializers

from wisdom_v2.common import interview_public
from wisdom_v2.enum.service_content_enum import InterviewRecordTemplateQuestionTypeEnum as QuestionTypeEnum, \
    InterviewRecordTemplateRoleEnum, InterviewRecordTemplateQuestionAnswerTypeEnum
from wisdom_v2.models import InterviewRecordTemplateQuestion, InterviewRecordTemplateOption, RelatedQuestion


class InterviewRecordTemplateOptionSerializers(serializers.ModelSerializer):
    class Meta:
        model = InterviewRecordTemplateOption
        fields = ['id', 'title', 'user_defined']


class InterviewRecordTemplateQuestionSerializers(serializers.ModelSerializer):

    option = serializers.SerializerMethodField()
    related_question = serializers.SerializerMethodField()
    answer_type = serializers.SerializerMethodField()

    class Meta:
        model = InterviewRecordTemplateQuestion
        exclude = ('created_at', 'updated_at')

    def get_option(self, obj):
        try:
            if obj.type in [QuestionTypeEnum.single, QuestionTypeEnum.multiple]:
                option = InterviewRecordTemplateOption.objects.filter(question_id=obj.pk).order_by('order').all()
                option = InterviewRecordTemplateOptionSerializers(option, many=True)
                return option.data
        except InterviewRecordTemplateOption.DoesNotExist:
            return

    def get_related_question(self, obj):
        if obj.template.role == InterviewRecordTemplateRoleEnum.stakeholder:
            return None

        if obj.template.role == 1:  # 教练的题
            r_question = RelatedQuestion.objects.filter(coach_question_id=obj.pk, template_type=1, deleted=False)
            if r_question.exists():
                r_question = r_question.first()
                question = r_question.coachee_question
                return {
                    "id": question.pk,
                    "title": question.title,
                    "description": r_question.description,
                }
        else:  # 被教练的题
            r_question = RelatedQuestion.objects.filter(coachee_question_id=obj.pk, template_type=2, deleted=False)
            if r_question.exists():
                r_question = r_question.first()
                question = r_question.coach_question
                return {
                    "id": question.pk,
                    "title": question.title,
                    "description": r_question.description,
                }
        return None

    def get_answer_type(self, obj):
        # 后端接口返回时，将指定题目id转成普通问答文本类型。
        # 版本兼容处理，详情查看question_id_to_answer_type方法
        answer_type = interview_public.question_id_to_answer_type(obj.id)
        if answer_type:
            return InterviewRecordTemplateQuestionAnswerTypeEnum.normal_text.value
        return obj.answer_type

    def update(self, instance, validated_data):
        # InterviewRecordTemplateQuestionSerializers序列化中做版本兼容，重写了一个get_answer_type的方法
        # 重写后的answer_type是SerializerMethodField类型，is_valid无法自动更新。
        # 所以需要手动更新answer_type
        if 'answer_type' in validated_data:
            instance.answer_type = validated_data['answer_type']
        return super().update(instance, validated_data)
