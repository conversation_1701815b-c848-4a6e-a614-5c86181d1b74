from rest_framework import serializers
from wisdom_v2.models import Theme, Article, ArticleThemeRelation, Resume, Coach


class ThemeSerializer(serializers.ModelSerializer):
    id = serializers.CharField(read_only=True)
    name = serializers.CharField(help_text='名称')
    brief = serializers.CharField(help_text='主题简介')
    image_url = serializers.CharField(help_text='主题图片', read_only=True)
    coach_count = serializers.SerializerMethodField(help_text='教练数量', read_only=True)
    article_count = serializers.SerializerMethodField(help_text='文章数量', read_only=True)
    created_at = serializers.DateTimeField(help_text='创建时间', format='%Y-%m-%d %H:%M:%S', read_only=True)

    class Meta:
        model = Theme
        fields = ('id', 'name', 'brief', 'coach_count', 'article_count', 'created_at', 'image_url')

    def get_coach_count(self, obj):
        return obj.theme_article_relation.filter(deleted=False, coach__isnull=False).distinct().count()

    def get_article_count(self, obj):
        return obj.theme_article_relation.filter(deleted=False, article__isnull=False).distinct().count()


class ArticleThemeSerializer(serializers.ModelSerializer):
    id = serializers.CharField(read_only=True)
    title = serializers.CharField(help_text='文章标题', source='article.title')
    category = serializers.IntegerField(help_text='文章分类', source='article.category')
    enabled = serializers.BooleanField(help_text='是否启用', source='article.enabled')
    weight = serializers.IntegerField(help_text='权重')
    article_id = serializers.IntegerField(help_text='文章id')

    class Meta:
        model = ArticleThemeRelation
        fields = ('id', 'title', 'category', 'enabled', 'weight', 'article_id')


class ArticleListSerializer(serializers.ModelSerializer):
    id = serializers.IntegerField(help_text='id')
    title = serializers.CharField(help_text='文章标题')

    class Meta:
        model = Article
        fields = ('id', 'title')


class CoachThemeSerializer(serializers.ModelSerializer):
    id = serializers.CharField(read_only=True)
    coach_id = serializers.IntegerField(help_text='教练id')
    true_name = serializers.CharField(help_text='教练姓名', source='coach.user.cover_name')
    personal_name = serializers.CharField(help_text='对个人用户可见姓名', source='coach.personal_name')
    resume = serializers.SerializerMethodField(help_text='教练简历信息')
    domain = serializers.SerializerMethodField(help_text='擅长领域')
    weight = serializers.IntegerField(help_text='权重')
    gender = serializers.IntegerField(source='coach.user.gender')

    class Meta:
        model = ArticleThemeRelation
        fields = ('id', 'coach_id', 'true_name', 'resume', 'domain', 'weight', 'gender', 'personal_name')

    def get_resume(self, obj):
        resume = Resume.objects.filter(coach_id=obj.coach_id, deleted=False, is_customization=False).first()
        data = {}
        data['coach_auth'] = resume.coach_auth if resume.coach_auth else None
        data['working_years'] = resume.working_years if resume.working_years else None
        return data

    def get_domain(self, obj):
        resume = Resume.objects.filter(coach_id=obj.coach_id, deleted=False, is_customization=False).first()
        if resume:
            if resume.coach_domain:
                domain = resume.coach_domain
                return domain[:3]
        return []


class ThemeCoachListSerializer(serializers.ModelSerializer):
    id = serializers.IntegerField(help_text='教练id')
    true_name = serializers.CharField(help_text='教练姓名', source='user.cover_name')
    resume = serializers.SerializerMethodField(help_text='教练简历信息')
    domain = serializers.SerializerMethodField(help_text='擅长领域')
    gender = serializers.IntegerField(source='user.gender')

    class Meta:
        model = Coach
        fields = ('id', 'true_name', 'resume', 'domain', 'gender')

    def get_resume(self, obj):
        resume = Resume.objects.filter(coach=obj, deleted=False, is_customization=False).first()
        data = {}
        data['coach_auth'] = resume.coach_auth if resume.coach_auth else None
        data['working_years'] = resume.working_years if resume.working_years else None
        return data

    def get_domain(self, obj):
        resume = Resume.objects.filter(coach=obj, deleted=False, is_customization=False).first()
        if resume:
            if resume.coach_domain:
                domain = resume.coach_domain
                return domain[:3]
        return []
