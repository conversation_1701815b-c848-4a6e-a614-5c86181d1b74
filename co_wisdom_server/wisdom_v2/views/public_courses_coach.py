from datetime import datetime

import pendulum
from django.db import transaction
from django.db.models import Q
from drf_yasg import openapi
from drf_yasg.utils import swagger_auto_schema
from rest_framework.decorators import action
from rest_framework import viewsets

from utils.api_response import success_response, parameter_error_response
from utils.pagination import StandardResultsSetPagination
from wisdom_v2.common import public_courses_public, business_order_public
from wisdom_v2.enum.business_order_enum import BusinessOrderTypeEnum, WorkTypeEnum
from wisdom_v2.models import Coach, Resume, UserBackend
from wisdom_v2.models_file import PublicCourses, PublicCoursesCoach
from wisdom_v2.views import constant
from wisdom_v2.views.public_courses_coach_actions import AdminPublicCoursesCoachListSerializer


class AdminPublicCoursesCoachViewSet(viewsets.ModelViewSet):
    queryset = PublicCoursesCoach.objects.filter(deleted=False).order_by('-start_time')
    serializer_class = AdminPublicCoursesCoachListSerializer

    @swagger_auto_schema(
        operation_id='获取教练公开课列表',
        operation_summary='获取教练公开课列表',
        manual_parameters=[
            openapi.Parameter('status', openapi.IN_QUERY, description='活动状态 1-未开始 2-进行中 3-已结束', type=openapi.TYPE_NUMBER,
                              required=True),
            openapi.Parameter('class_name', openapi.IN_QUERY, description='班级名',
                              type=openapi.TYPE_STRING, required=True),
            openapi.Parameter('coach_name', openapi.IN_QUERY, description='教练名',
                              type=openapi.TYPE_STRING, required=True),
            openapi.Parameter('start_time', openapi.IN_QUERY, description='开始时间',
                              type=openapi.TYPE_STRING, required=True),
            openapi.Parameter('end_time', openapi.IN_QUERY, description='结束时间',
                              type=openapi.TYPE_STRING, required=True),
            openapi.Parameter('type', openapi.IN_QUERY, description='工作形式 1-授课 2-一对一辅导 3-小组辅导 4-口试', type=openapi.TYPE_NUMBER,
                              required=True),
        ],
        tags=['教练公开课']
    )
    def list(self, request, *args, **kwargs):

        try:
            status = request.query_params.get('status')
            class_name = request.query_params.get('class_name')
            coach_name = request.query_params.get('coach_name')
            start_time = request.query_params.get('start_time')
            end_time = request.query_params.get('end_time')
            public_course_type = request.query_params.get('type')
            user = request.user
            user_backend = UserBackend.objects.filter(user=user, deleted=False).first()
        except Exception as e:
            return parameter_error_response()
        queryset = self.get_queryset()

        try:
            if start_time:
                start_time = pendulum.parse(start_time)
            if end_time:
                end_time = pendulum.parse(end_time)
        except ValueError:
            return parameter_error_response("无效的时间格式")

        # 如果是不是管理员，只能看见当前登录用户创建的公开课信息。
        if user_backend.role_id != constant.ADMIN:
            queryset = queryset.filter(public_courses__creator=user)

        if status:
            current_date = datetime.now().date()
            current_hour = datetime.now().hour

            # 未开始 开始时间>当前时间
            if status == '1':
                queryset = queryset.filter(
                    # 开始日期大于当前日期 或 (开始日期等于当前日期且开始时间的小时大于当前小时)
                    Q(start_time__date__gt=current_date) | (
                                Q(start_time__date=current_date) & Q(start_time__hour__gt=current_hour))
                )
            # 进行中 开始时间<=当前时间，结束时间>=当前时间
            elif status == '2':
                current_date = datetime.now().date()
                current_hour = datetime.now().hour
                queryset = queryset.filter(
                    # 开始日期小于当前日期 或 (开始日期等于当前日期且开始小时小于当前小时)
                    Q(start_time__date__lt=current_date) | (
                            Q(start_time__date=current_date) & Q(start_time__hour__lte=current_hour)),
                    # 结束日期大于当前日期 或 (结束日期等于当前日期且结束小时大于等于当前小时)
                    Q(end_time__date__gt=current_date) | (
                            Q(end_time__date=current_date) & Q(end_time__hour__gt=current_hour))
                )
            # 已结束 结束时间<当前时间
            elif status == '3':
                queryset = queryset.filter(
                    # 结束日期小于当前日期 或 (结束日期等于当前日期且结束时间的小时小于等于当前小时)
                    Q(end_time__date__lt=current_date) | (
                                Q(end_time__date=current_date) & Q(end_time__hour__lte=current_hour))
                )
        if class_name:
            queryset = queryset.filter(class_name__icontains=class_name)
        if coach_name:
            queryset = queryset.filter(
               coach__user__true_name__icontains=coach_name, deleted=False).distinct()
        if start_time:
            queryset = queryset.filter(
                Q(start_time__date__gt=start_time.date()) | (
                                Q(start_time__date=start_time.date()) & Q(start_time__hour__gt=start_time.hour)))
        if end_time:
            queryset = queryset.filter(Q(end_time__date__lt=end_time.date()) | (
                                Q(end_time__date=end_time.date()) & Q(end_time__hour__lt=end_time.hour)))
        if public_course_type:
            queryset = queryset.filter(type=public_course_type)

        # 分页
        paginator = StandardResultsSetPagination()
        page_list = paginator.paginate_queryset(queryset, self.request)
        serializer = AdminPublicCoursesCoachListSerializer(page_list, many=True)
        response = paginator.get_paginated_response(serializer.data)
        return success_response(response)

    @swagger_auto_schema(
        operation_id='获取教练公开课详情',
        operation_summary='获取教练公开课详情',
        manual_parameters=[
            openapi.Parameter('id', openapi.IN_QUERY, description='教练公开课标识', type=openapi.TYPE_NUMBER,
                              required=True),
        ],
        tags=['教练公开课']
    )
    @action(methods=['get'], detail=False, url_path='detail')
    def get_public_course_coach_detail(self, request, *args, **kwargs):
        try:
            public_course_coach_id = request.query_params.get('id')
            public_course_coach = PublicCoursesCoach.objects.get(id=public_course_coach_id, deleted=False)
        except PublicCoursesCoach.DoesNotExist:
            return parameter_error_response('教练公开课不存在')
        except Exception as e:
            return parameter_error_response()
        data = {
            'class_name': public_course_coach.class_name,
            'coach_content': [
                {
                    'coach_id': public_course_coach.coach_id,
                    'coach_name': public_course_coach.coach.user.cover_name,
                    'courses': [{
                        'id': str(public_course_coach.id),
                        'start_time': public_course_coach.start_time.strftime('%Y-%m-%d %H:%M:%S'),
                        'end_time': public_course_coach.end_time.strftime('%Y-%m-%d %H:%M:%S'),
                        'student_name': public_course_coach.student_name,
                        'type': public_course_coach.type
                    }]
                }
            ]
        }
        return success_response(data)

    @swagger_auto_schema(
        operation_id='创建教练公开课',
        operation_summary='创建教练公开课',
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'class_name': openapi.Schema(type=openapi.TYPE_STRING, description='班级名称'),
                'coach_content': openapi.Schema(type=openapi.TYPE_STRING, description='教练数据'),
            }),
        tags=['教练公开课']
    )
    @action(methods=['post'], detail=False, url_path='add')
    def add_public_course_coach(self, request, *args, **kwargs):
        try:
            class_name = request.data['class_name']
            coach_content = request.data['coach_content']
            creator = request.user
        except Exception as e:
            return parameter_error_response()
        if not isinstance(coach_content, list):
            return parameter_error_response()

        public_courses_coach = []
        for item in coach_content:
            coach_id = item.get('coach_id')
            if not coach_id:
                return parameter_error_response('请选择教练')
            coach = Coach.objects.filter(deleted=False, pk=coach_id).first()
            if not coach:
                return parameter_error_response('教练信息不正确')
            resume = Resume.objects.filter(deleted=False, is_customization=False, coach_id=coach_id).first()
            if not resume:
                return parameter_error_response('教练简历信息不正确')

            courses = item.get('courses')
            if not courses:
                return parameter_error_response('未获取到课程信息')
            if not isinstance(courses, list):
                return parameter_error_response()
            for course in courses:
                start_time = course.get('start_time')
                end_time = course.get('end_time')
                course_type = course.get('type')
                student_name = course.get('student_name')

                if not all([course_type, start_time, end_time]):
                    return parameter_error_response('请提供完整的工作类型、开始时间和结束时间')

                if course_type in [WorkTypeEnum.one_to_one.value, WorkTypeEnum.group_counseling.value] and not student_name:
                    return parameter_error_response('学员姓名不能为空')
                if start_time > end_time:
                    return parameter_error_response('开始时间不能大于结束时间')
                public_courses_coach.append(
                    PublicCoursesCoach(
                        start_time=start_time,
                        class_name=class_name,
                        end_time=end_time,
                        type=course_type,
                        student_name=student_name,
                        coach_id=coach_id,
                        resume_id=resume.id
                    )
                )
        if public_courses_coach:
            with transaction.atomic():
                public_courses = PublicCourses.objects.create(
                    class_name=class_name, type=None, creator=creator)
                for i in public_courses_coach:
                    i.public_courses_id = public_courses.id
                PublicCoursesCoach.objects.bulk_create(public_courses_coach)
        return success_response()

    @swagger_auto_schema(
        operation_id='修改教练公开课',
        operation_summary='修改教练公开课',
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'class_name': openapi.Schema(type=openapi.TYPE_STRING, description='班级名称'),
                'coach_content': openapi.Schema(type=openapi.TYPE_STRING, description='教练数据'),
            }),
        tags=['教练公开课']
    )
    @action(methods=['post'], detail=False, url_path='update')
    def update_public_course_coach(self, request, *args, **kwargs):
        try:
            # 获取请求中的课程班级名和教练内容
            class_name = request.data['class_name']
            coach_content = request.data['coach_content']
        except Exception as e:
            return parameter_error_response()
        if not isinstance(coach_content, list):
            return parameter_error_response('教练课程信息数据格式错误')

        for item in coach_content:
            # 获取教练的课程信息
            courses = item.get('courses')
            if not courses:
                return parameter_error_response('未获取到课程信息')
            if not isinstance(courses, list):
                return parameter_error_response('教练课程信息数据格式错误')

            for course in courses:
                # 获取课程相关信息
                public_courses_coach_id = course.get('id')
                if not public_courses_coach_id:
                    return parameter_error_response('未获取到教练课程标识')

                # 查询对应ID的公共课程教练对象
                public_courses_coach = PublicCoursesCoach.objects.filter(
                    pk=public_courses_coach_id, deleted=False).first()
                if not public_courses_coach:
                    return parameter_error_response('未获取到教练课程信息')

                # 检查是否已发起提现
                if business_order_public.get_object_settlement_status(
                        [str(public_courses_coach.pk)], BusinessOrderTypeEnum.public_course.value,
                        public_courses_coach.type):
                    return parameter_error_response('已发起提现不可修改')

                # 获取课程的其他信息
                start_time = course.get('start_time')
                end_time = course.get('end_time')
                course_type = course.get('type')
                student_name = course.get('student_name')

                # 检查必填信息是否完整
                if not all([course_type, start_time, end_time]):
                    return parameter_error_response('请提供完整的工作类型、开始时间和结束时间')

                # 如果工作类型是一对一或小组辅导，检查学员姓名是否为空
                if course_type in [WorkTypeEnum.one_to_one.value,
                                   WorkTypeEnum.group_counseling.value] and not student_name:
                    return parameter_error_response('学员姓名不能为空')

                # 检查开始时间是否大于结束时间
                if start_time > end_time:
                    return parameter_error_response('开始时间不能大于结束时间')

                # 判断是否需要更新订单数据
                is_update_data = False
                # 开始时间是否需要更新
                if start_time != public_courses_coach.start_time.strftime('%Y-%m-%d %H:%M:%S'):
                    start_time = datetime.strptime(start_time, "%Y-%m-%d %H:%M:%S")
                    public_courses_coach.start_time = start_time
                    is_update_data = True

                # 结束时间是否需要更新
                if end_time != public_courses_coach.end_time.strftime('%Y-%m-%d %H:%M:%S'):
                    end_time = datetime.strptime(end_time, "%Y-%m-%d %H:%M:%S")
                    public_courses_coach.end_time = end_time
                    is_update_data = True

                # 公开课类型是否需要更新
                old_course_type = None
                if course_type != public_courses_coach.type:
                    old_course_type = public_courses_coach.type
                    public_courses_coach.type = course_type
                    is_update_data = True

                # 更新公共课程教练信息
                public_courses_coach.class_name = class_name
                public_courses_coach.student_name = student_name
                public_courses_coach.save()

                if is_update_data:
                    # 如果更新了数据，更新相应的业务订单信息
                    object_ids = [str(public_courses_coach.pk)]
                    # 如果更新了工作类型，需要使用之前的工作类型查询
                    update_data_type = old_course_type or public_courses_coach.type

                    # 如果修改了时间，并且状态变成了未开始或进行中，删除原来订单数据
                    if public_courses_public.get_status_display(public_courses_coach) in [1, 2]:
                        business_order_public.del_business_order(
                            object_ids, BusinessOrderTypeEnum.public_course.value, update_data_type)
                    # 如果状态没变化，更新原有数据
                    else:
                        # 更新对应数据
                        update_data = {
                            'type': course_type,
                            'end_time': public_courses_coach.end_time,
                            'start_time': public_courses_coach.start_time,
                        }
                        business_order_public.update_business_order(
                            object_ids, BusinessOrderTypeEnum.public_course.value, update_data_type, update_data)

        return success_response()

    @swagger_auto_schema(
        operation_id='删除教练公开课',
        operation_summary='删除教练公开课',
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'id': openapi.Schema(type=openapi.TYPE_NUMBER, description='标识'),
                'class_name': openapi.Schema(type=openapi.TYPE_STRING, description='班级名称'),
                'start_time': openapi.Schema(type=openapi.TYPE_STRING, description='公开课开始时间'),
                'end_time': openapi.Schema(type=openapi.TYPE_STRING, description='公开课结束时间'),
                'coach_ids': openapi.Schema(type=openapi.TYPE_STRING, description='教练id列表'),
            }),
        tags=['教练公开课']
    )
    @action(methods=['post'], detail=False, url_path='del')
    def del_public_course_coach(self, request, *args, **kwargs):
        try:
            public_course_coach_id = request.data.get('id')
            public_course_coach = PublicCoursesCoach.objects.get(id=public_course_coach_id, deleted=False)
        except PublicCoursesCoach.DoesNotExist:
            return parameter_error_response('教练公开课不存在')
        except Exception as e:
            return parameter_error_response()
        business_order_public.del_business_order(
            [str(public_course_coach.pk)], BusinessOrderTypeEnum.public_course.value, public_course_coach.type)
        public_course_coach.deleted = True
        public_course_coach.save()
        return success_response()
