from decimal import Decimal

from rest_framework import serializers

from wisdom_v2.common import coach_public
from wisdom_v2.enum.pay_enum import OrderStatusEnum
from wisdom_v2.enum.project_interview_enum import ProjectInterviewPlaceCategoryEnum
from wisdom_v2.models import PersonalUser, Order, ProjectInterview
from wisdom_v2.views import constant
from wisdom_v2.views.constant import ATTR_STATUS_INTERVIEW_CANCEL


class PersonalUserViewSetSerializers(serializers.ModelSerializer):
    """管理后台-个人用户列表"""
    user_id = serializers.IntegerField(source='user.id', help_text='用户id')
    user_name = serializers.CharField(source='user.cover_name', help_text='用户名')
    phone = serializers.IntegerField(source='user.phone', help_text='手机号')
    interview_count = serializers.SerializerMethodField(help_text='辅导次数')
    created_at = serializers.SerializerMethodField(help_text='创建时间')
    referrer_name = serializers.SerializerMethodField(help_text='推荐人姓名')

    def get_referrer_name(self, obj):
        if obj.invite and obj.invite.referrer:
            return obj.invite.referrer.true_name

    def get_created_at(self, obj):
        return obj.created_at.strftime('%Y-%m-%d %H:%M:%S')

    def get_interview_count(self, obj):
        count = obj.user.get_all_interview_count(
            coachee_user_id=obj.user_id,
            place_category=ProjectInterviewPlaceCategoryEnum.online_one_to_one
        )
        return count

    class Meta:
        model = PersonalUser
        fields = ['id', 'user_id', 'user_name', 'phone', 'source', 'referrer_name', 'interview_count', 'created_at',
                  'nickname', 'email']


class PersonalUserOrderViewSetSerializers(serializers.ModelSerializer):
    """管理后台-个人用户订单列表"""
    id = serializers.CharField(source='order_no', help_text='订单号')
    user_id = serializers.CharField(source='public_attr.user_id', help_text='用户id')
    user_name = serializers.CharField(source='public_attr.user.cover_name', help_text='用户名')
    phone = serializers.IntegerField(source='public_attr.user.phone', help_text='手机号')
    order_count = serializers.IntegerField(source='count', help_text='购买次数')
    created_at = serializers.DateTimeField(format='%Y-%m-%d %H:%M:%S', help_text='创建时间')
    interview_count = serializers.SerializerMethodField(help_text='辅导次数')
    coach_name = serializers.SerializerMethodField(help_text='教练姓名')
    activity_name = serializers.SerializerMethodField(help_text='活动主题')
    status = serializers.SerializerMethodField(help_text='订单状态')
    nickname = serializers.SerializerMethodField(help_text='昵称')
    email = serializers.SerializerMethodField(help_text='邮箱')
    payer_amount = serializers.SerializerMethodField(help_text='支付金额')

    def get_status(self, obj):
        if obj.status == OrderStatusEnum.pending_pay.value and obj.deleted == False:
            return constant.ADMIN_ORDER_STATE_PENDING_PAY
        elif obj.status == OrderStatusEnum.pending_pay.value and obj.deleted == True:
            return constant.ADMIN_ORDER_STATE_CLOSURE
        elif obj.status == OrderStatusEnum.paid.value and obj.deleted == False:
            return constant.ADMIN_ORDER_STATE_COMPLETE
        elif obj.status in [OrderStatusEnum.under_refund.value, OrderStatusEnum.refunded.value]:
            return constant.ADMIN_ORDER_STATE_REFUND
        return

    def get_interview_count(self, obj):
        return ProjectInterview.objects.filter(
            order=obj, deleted=False).exclude(public_attr__status=ATTR_STATUS_INTERVIEW_CANCEL).count()

    def get_coach_name(self, obj):
        if obj.public_attr.target_user:
            return obj.public_attr.target_user.cover_name
        return '--'

    def get_activity_name(self, obj):
        if obj.activity:
            return obj.activity.theme
        return '--'

    def get_nickname(self, obj):
        personal_user = PersonalUser.objects.filter(user_id=obj.public_attr.user_id, deleted=False).first()
        if personal_user:
            return personal_user.nickname
        return '--'
    def get_email(self, obj):
        personal_user = PersonalUser.objects.filter(user_id=obj.public_attr.user_id, deleted=False).first()
        if personal_user:
            return personal_user.email
        return '--'

    def get_payer_amount(self, obj):
        # 只有未支付和已支付的展示支付金额
        if obj.status == OrderStatusEnum.pending_pay.value:
            if obj.total_amount:
                return Decimal(str(obj.total_amount)) / Decimal('100')
            else:
                return 0

        elif obj.status == OrderStatusEnum.paid.value:
            if obj.payer_amount:
                return Decimal(str(obj.payer_amount)) / Decimal('100')
            else:
                return 0

        return '--'

    class Meta:
        model = Order
        fields = ['id', 'user_id', 'user_name', 'phone', 'coach_name', 'activity_name', 'nickname', 'email',
                  'order_count', 'interview_count', 'created_at', 'status', 'payer_amount']
