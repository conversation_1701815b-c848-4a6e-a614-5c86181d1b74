import pendulum
import datetime

from django.conf import settings
from django.db.models import Sum, Avg, Q
from rest_framework import serializers
from django.db import transaction

from utils.api_response import WorkWechatUserError
from wisdom_v2 import utils
from wisdom_v2.app_views.app_coach_actions import get_time_point
from wisdom_v2.common import coach_public
from wisdom_v2.enum.service_content_enum import ProjectCoachStatusEnum, CoachAuthEnum, ScheduleApplyTypeEnum
from wisdom_v2.enum.user_enum import UserTmpEnum
from wisdom_v2.models import TraineeCoach, Coach, ProjectInterview, Resume, WorkWechatUser, UserTmp, ProjectCoach, \
    Theme, ArticleThemeRelation
from utils import task
from utils.multiple_selection_map import get_multiple_selection_detail, validate_multiple_selection
from wisdom_v2.enum.project_interview_enum import ProjectInterviewPlaceCategoryEnum
from wisdom_v2.views.constant import ATTR_STATUS_INTERVIEW_CANCEL, INTERVIEW_TYPE_COACHING
from utils.api_response import WisdomValidationError


class TraineeCoachResumeBaseSerializers(serializers.ModelSerializer):
    user_id = serializers.IntegerField(source='user.id', read_only=True)
    name = serializers.CharField(source='user.cover_name', help_text='用户名', read_only=True)
    email = serializers.CharField(source='user.email', help_text='邮箱', read_only=True)
    phone = serializers.CharField(source='user.phone', help_text='手机号', read_only=True)
    gender = serializers.IntegerField(source='user.gender', help_text='性别', read_only=True)
    birthday = serializers.DateField(source='user.birthday', help_text='生日', read_only=True, format='%Y-%m-%d')
    english_name = serializers.CharField(source='user.english_name', help_text='英文名', read_only=True)
    head_image_url = serializers.CharField(source='user.head_image_url', help_text='头像', read_only=True)
    tutoring_time = serializers.SerializerMethodField(help_text='平台一对一辅导时常')
    latest_schedule = serializers.SerializerMethodField(help_text='最近可预约时间')
    user_class = serializers.CharField(help_text='班次', read_only=True)
    working_years = serializers.IntegerField(help_text='工作年限 | 1：一年以上，2：三年以上，3：五年以上', read_only=True,
                                             source='resume.working_years')
    language = serializers.CharField(help_text='语言｜ 1：中文，2：英语，3：法语，4：日语', read_only=True,
                                     source='resume.language')
    brief = serializers.CharField(help_text='教练简介', read_only=True, source='resume.brief')
    work_experience = serializers.CharField(help_text='工作经历', read_only=True, source='resume.work_experience')
    customer_evaluate = serializers.CharField(help_text='客户评价', read_only=True, source='resume.customer_evaluate')
    style = serializers.CharField(help_text='教练风格', read_only=True, source='resume.style')
    qualification = serializers.JSONField(help_text='资质', read_only=True, source='resume.qualification')
    industry = serializers.CharField(help_text='教练过的行业', read_only=True, source='resume.industry')
    domain = serializers.CharField(help_text='教练领域', read_only=True, source='resume.domain')
    extra_time = serializers.IntegerField(help_text='平台外教练时间', read_only=True, source='resume.extra_time')
    coach_auth = serializers.IntegerField(
        help_text='教练认证 ｜ 1：ICF-ACC, 2：ICF-PCC, 3：ICF-MCC, 4：准ICF-PCC, 5：个人教练, 6:ICF-CITO企业教练',
        read_only=True, source='resume.coach_auth')

    def get_tutoring_time(self, obj):
        tutoring_time = ProjectInterview.objects.filter(
            deleted=False, public_attr__user=obj.user,
            public_attr__target_user__unionid__isnull=False,
        ).aggregate(tutoring_time=Sum('times'))['tutoring_time']
        return tutoring_time if tutoring_time else 0

    def get_latest_schedule(self, obj):
        time = pendulum.now()
        if time.minute >= 30:
            time = time.add(hours=1).set(minute=0)
        elif 30 > time.minute > 0:
            time = time.set(minute=30)
        # 如果是当天时间，往后推迟6个小时， 如果是明天 就直接从8点开始算
        if time.add(hours=6).day == pendulum.tomorrow().day:
            start_date = pendulum.tomorrow().add(hours=8)
        else:
            start_date = time.add(hours=6)
        latest_schedule = get_time_point(
            start_date.strftime('%Y-%m-%d %H:%M:00'), 30, obj.user.id, apply_type=ScheduleApplyTypeEnum.personal.value)
        if latest_schedule:
            date = pendulum.parse(latest_schedule)
            if date.day == pendulum.today().day:
                latest_schedule = '今日{}'.format(date.format('HH:mm'))
            elif date.day == pendulum.tomorrow().day:
                latest_schedule = '明日{}'.format(date.format('HH:mm'))
            elif date.day == pendulum.today().add(days=2).day:
                latest_schedule = '后天{}'.format(date.format('HH:mm'))
            elif date.day == pendulum.today().add(days=3).day:
                latest_schedule = date.format('YYYY.MM.DD HH:mm')
            return latest_schedule
        return

    class Meta:
        model = TraineeCoach
        fields = ('user_id', 'name', 'email', 'phone', 'head_image_url', 'tutoring_time', 'latest_schedule', 'user_class',
                  'extra_time', 'working_years', 'language', 'brief', 'work_experience', 'customer_evaluate', 'style',
                  'qualification', 'industry', 'domain', 'coach_auth', 'gender', 'english_name', 'birthday')


class CoachResumeBaseSerializers(serializers.ModelSerializer):
    user_id = serializers.IntegerField(source='user.id', read_only=True)
    name = serializers.CharField(source='user.cover_name', help_text='用户名', read_only=True)
    email = serializers.CharField(source='user.email', help_text='邮箱', read_only=True)
    phone = serializers.CharField(source='user.phone', help_text='手机号', read_only=True)
    gender = serializers.IntegerField(source='user.gender', help_text='性别', read_only=True)
    birthday = serializers.DateField(source='user.birthday', help_text='生日', read_only=True, format='%Y-%m-%d')
    english_name = serializers.CharField(source='user.english_name', help_text='英文名', read_only=True)
    head_image_url = serializers.CharField(source='user.head_image_url', help_text='头像', read_only=True)
    tutoring_time = serializers.SerializerMethodField()
    user_class = serializers.SerializerMethodField()
    latest_schedule = serializers.SerializerMethodField()
    working_years = serializers.IntegerField(help_text='工作年限 | 1：一年以上，2：三年以上，3：五年以上', read_only=True,
                                             source='resume.working_years')
    language = serializers.SerializerMethodField(help_text='语言｜ 1：中文，2：英语，3：法语，4：日语', read_only=True)
    brief = serializers.SerializerMethodField(help_text='教练简介', read_only=True)
    work_experience = serializers.SerializerMethodField(help_text='工作经历', read_only=True)
    job_profile = serializers.SerializerMethodField(help_text='一句话介绍工作经历', read_only=True)
    customer_evaluate = serializers.SerializerMethodField(help_text='客户评价', read_only=True)
    style = serializers.SerializerMethodField(help_text='教练风格', read_only=True)
    qualification = serializers.SerializerMethodField(help_text='资质', read_only=True)
    industry = serializers.SerializerMethodField(help_text='教练过的行业', read_only=True)
    domain = serializers.SerializerMethodField(help_text='教练领域', read_only=True)
    extra_time = serializers.SerializerMethodField(help_text='平台外教练时间', read_only=True)
    coach_auth = serializers.SerializerMethodField(
        help_text='教练认证 ｜ 1：ICF-ACC, 2：ICF-PCC, 3：ICF-MCC, 4：准ICF-PCC, 5：个人教练, 6:ICF-CITO企业教练',
        read_only=True)
    work_wechat_user_id = serializers.SerializerMethodField(help_text='企业微信用户id')


    def get_language(self, obj):
        resume = Resume.objects.filter(coach=obj, deleted=False).order_by('created_at').first()
        if resume:
            return resume.language

    def get_brief(self, obj):
        resume = Resume.objects.filter(coach=obj, deleted=False).order_by('created_at').first()
        if resume:
            return resume.brief

    def get_work_experience(self, obj):
        resume = Resume.objects.filter(coach=obj, deleted=False).order_by('created_at').first()
        if resume:
            return resume.work_experience

    def get_customer_evaluate(self, obj):
        resume = Resume.objects.filter(coach=obj, deleted=False).order_by('created_at').first()
        if resume:
            return resume.customer_evaluate

    def get_style(self, obj):
        resume = Resume.objects.filter(coach=obj, deleted=False).order_by('created_at').first()
        if resume:
            return resume.style

    def get_qualification(self, obj):
        resume = Resume.objects.filter(coach=obj, deleted=False).order_by('created_at').first()
        if resume:
            return resume.qualification

    def get_industry(self, obj):
        resume = Resume.objects.filter(coach=obj, deleted=False).order_by('created_at').first()
        if resume:
            return resume.industry

    def get_domain(self, obj):
        resume = Resume.objects.filter(coach=obj, deleted=False).order_by('created_at').first()
        if resume:
            return resume.domain

    def get_extra_time(self, obj):
        resume = Resume.objects.filter(coach=obj, deleted=False).order_by('created_at').first()
        if resume:
            return resume.extra_time

    def get_coach_auth(self, obj):
        resume = Resume.objects.filter(coach=obj, deleted=False).order_by('created_at').first()
        if resume:
            return resume.coach_auth

    def get_work_wechat_user_id(self, obj):
        wx_user = WorkWechatUser.objects.filter(user_id=obj.user.id, deleted=False).first()
        if wx_user:
            return wx_user.wx_user_id
        return

    def get_latest_schedule(self, obj):
        return None

    def get_user_class(self, obj):
        return None

    def get_tutoring_time(self, obj):
        # TODO: 还需要加上v1中辅导的时间
        times = ProjectInterview.objects.filter(public_attr__user_id=obj.user.pk, public_attr__status__in=[3, 4, 5],
                                                deleted=False, public_attr__type=1,
                                                type=INTERVIEW_TYPE_COACHING,
                                                public_attr__end_time__lt=datetime.datetime.now()
                                                ).aggregate(times_minute=Sum('times'))['times_minute']
        return times if times else 0

    class Meta:
        model = Coach
        fields = ('user_id', 'name', 'email', 'phone', 'head_image_url',  'working_years', 'language', 'brief',
                  'work_experience', 'customer_evaluate', 'style', 'qualification', 'industry', 'domain', 'coach_auth',
                  'tutoring_time', 'gender', 'english_name', 'birthday', 'extra_time', 'latest_schedule',
                  'user_class', 'work_wechat_user_id', 'job_profile')


class ResumeEditSerializer(serializers.Serializer):
    type = serializers.IntegerField(help_text='教练类型， 1-签约教练 2-个人教练', required=True)
    user_id = serializers.IntegerField(help_text='教练用户id', required=True)
    birthday = serializers.CharField(help_text='生日', required=False)
    name = serializers.CharField(help_text='用户名', required=False)
    email = serializers.CharField(help_text='邮箱', required=False)
    phone = serializers.CharField(help_text='手机号', required=False)
    gender = serializers.IntegerField(help_text='性别', required=False)
    english_name = serializers.CharField(help_text='英文名', required=False)
    user_class = serializers.CharField(help_text='班次', required=False)
    extra_time = serializers.IntegerField(help_text='平台外一对一辅导时常', required=False)
    head_image_url = serializers.CharField(help_text='头像', required=False)
    working_years = serializers.IntegerField(help_text='工作年限 | 1：一年以上，2：三年以上，3：五年以上', required=False)
    language = serializers.CharField(help_text='语言｜ 1：中文，2：英语，3：法语，4：日语', required=False)
    brief = serializers.CharField(help_text='教练简介', required=False)
    work_experience = serializers.CharField(help_text='工作经历', required=False)
    job_profile = serializers.CharField(help_text='一句话介绍工作经历', required=False)
    customer_evaluate = serializers.CharField(help_text='客户评价', required=False)
    style = serializers.CharField(help_text='教练风格', required=False)
    qualification = serializers.JSONField(help_text='资质', required=False)
    industry = serializers.CharField(help_text='教练过的行业', required=False)
    domain = serializers.CharField(help_text='教练领域', required=False)
    work_wechat_user_id = serializers.CharField(help_text='企业微信用户id', required=False, write_only=True)
    is_add_work_wechat_user = serializers.BooleanField(help_text='是否是新建企业微信账号', required=False, write_only=True)
    coach_auth = serializers.IntegerField(
        help_text='教练认证 ｜ 1：ICF-ACC, 2：ICF-PCC, 3：ICF-MCC, 4：准ICF-PCC, 5：个人教练, 6:ICF-CITO企业教练',
        required=False)

    def update_resume(self, validated_data):
        type, user_id = int(validated_data['type']), validated_data['user_id']
        validated_data.pop('type')
        validated_data.pop('user_id')
        coach = Coach.objects.get(user_id=user_id) if type == 1 else TraineeCoach.objects.get(user_id=user_id)
        user = coach.user

        with transaction.atomic():
            validated_data = update_resume_user(validated_data, user)
            if type == 2:
                if 'user_class' in validated_data.keys():
                    coach.user_class = validated_data['user_class']
                    validated_data.pop('user_class')

            # 教练简历已存在
            if coach.resume_id:
                Resume.objects.filter(pk=coach.resume_id).update(**validated_data)
            else:
                resume = Resume.objects.create(**validated_data)
                coach.resume_id = resume.pk
            coach.save()
            data = CoachResumeBaseSerializers(coach).data if type == 1 else TraineeCoachResumeBaseSerializers(coach).data
        return data

    def update_admin_resume(self, validated_data):
        type, user_id = int(validated_data['type']), validated_data['user_id']
        validated_data.pop('type')
        validated_data.pop('user_id')
        coach = Coach.objects.get(user_id=user_id) if type == 1 else TraineeCoach.objects.get(user_id=user_id)
        user = coach.user

        # 查询后台账户对应企业微信信息
        is_add_work_wechat_user = validated_data.get('is_add_work_wechat_user')
        if 'is_add_work_wechat_user' in validated_data.keys():
            validated_data.pop('is_add_work_wechat_user')
        work_wechat_user_id = validated_data.get('work_wechat_user_id')
        if 'work_wechat_user_id' in validated_data.keys():
            validated_data.pop('work_wechat_user_id')
        if work_wechat_user_id:
            wx_user = WorkWechatUser.objects.filter(
                user__isnull=False,
                wx_user_id=work_wechat_user_id, deleted=False).first()
            if wx_user and wx_user.user.id != user.id:
                raise WorkWechatUserError({'is_bind': True, 'user_name': wx_user.user.cover_name})
        if is_add_work_wechat_user:
            wx_user = WorkWechatUser.objects.filter(
                user__isnull=False,
                wx_user_id=user.phone, deleted=False).first()
            if wx_user:
                raise WisdomValidationError('手机号对应的企业微信账号已存在')

        with transaction.atomic():
            validated_data = update_resume_user(validated_data, user)
            if type == 2:
                if 'user_class' in validated_data.keys():
                    coach.user_class = validated_data['user_class']
                    validated_data.pop('user_class')

            # 教练简历已存在
            if coach.resume_id:
                Resume.objects.filter(pk=coach.resume_id).update(**validated_data)
            else:
                resume = Resume.objects.create(**validated_data)
                coach.resume_id = resume.pk
            coach.save()
            data = CoachResumeBaseSerializers(coach).data if type == 1 else TraineeCoachResumeBaseSerializers(coach).data

            if is_add_work_wechat_user:
                state = utils.add_work_wechat_user(user, settings.WORK_WECHAT_COACH_DEPARTMENT_ID)
                if state:
                    raise WorkWechatUserError(state)
            else:
                utils.update_work_wechat_user(work_wechat_user_id, user)

        # 更新教练标签
        task.update_coach_tag.delay(resume.coach_id)
        return data


class ResumeBaseSerializer(serializers.ModelSerializer):
    working_years = serializers.IntegerField(help_text='工作年限 | 1：一年以上，2：三年以上，3：五年以上', read_only=True,
                                             source='resume.working_years')
    language = serializers.CharField(help_text='语言｜ 1：中文，2：英语，3：法语，4：日语', read_only=True,
                                     source='resume.language')
    brief = serializers.CharField(help_text='教练简介', read_only=True, source='resume.brief')
    work_experience = serializers.CharField(help_text='工作经历', read_only=True, source='resume.work_experience')
    customer_evaluate = serializers.CharField(help_text='客户评价', read_only=True, source='resume.customer_evaluate')
    style = serializers.CharField(help_text='教练风格', read_only=True, source='resume.style')
    qualification = serializers.JSONField(help_text='资质', read_only=True, source='resume.qualification')
    industry = serializers.CharField(help_text='教练过的行业', read_only=True, source='resume.industry')
    domain = serializers.CharField(help_text='教练领域', read_only=True, source='resume.domain')
    coach_auth = serializers.IntegerField(
        help_text='教练认证 ｜ 1：ICF-ACC, 2：ICF-PCC, 3：ICF-MCC, 4：准ICF-PCC, 5：个人教练, 6:ICF-CITO企业教练',
        read_only=True, source='resume.coach_auth')

    class Meta:
        model = Resume
        fields = ('working_years', 'language', 'brief', 'work_experience',
                  'customer_evaluate', 'style', 'qualification', 'industry', 'domain', 'coach_auth')


def update_resume_user(validated_data, user):
    if 'name' in validated_data.keys():
        user.true_name = validated_data['name']
        validated_data.pop('name')
    if 'birthday' in validated_data.keys():
        user.birthday = validated_data['birthday']
        validated_data.pop('birthday')
    if 'email' in validated_data.keys():
        user.email = validated_data['email']
        validated_data.pop('email')
    if 'phone' in validated_data.keys():
        user.phone = validated_data['phone']
        validated_data.pop('phone')
    if 'gender' in validated_data.keys():
        user.gender = validated_data['gender']
        validated_data.pop('gender')
    if 'english_name' in validated_data.keys():
        user.english_name = validated_data['english_name']
        validated_data.pop('english_name')
    if 'head_image_url' in validated_data.keys():
        user.head_image_url = validated_data['head_image_url']
        validated_data.pop('head_image_url')
    user.save()

    return validated_data


class ResumeDetailSerializer(serializers.ModelSerializer):
    id = serializers.IntegerField(read_only=True)
    name = serializers.CharField(read_only=True, help_text='简历名称')
    user_id = serializers.IntegerField(read_only=True, source='coach.user_id')
    work_industry = serializers.SerializerMethodField(help_text='个人工作过的行业')
    highest_position = serializers.SerializerMethodField(help_text='曾担任的最高职位')
    enterprise_attributes = serializers.SerializerMethodField(help_text='工作过的企业属性')
    functional_module = serializers.SerializerMethodField(help_text='工作过的职能模块')
    work_experience = serializers.CharField(help_text='工作经验')
    job_profile = serializers.CharField(help_text='一句话介绍工作经验')
    coach_course = serializers.SerializerMethodField(help_text='受训经验')
    working_years = serializers.IntegerField(read_only=True, help_text='工作年限')
    coach_auth = serializers.IntegerField(read_only=True, help_text='教练资质')
    coach_industry = serializers.SerializerMethodField(help_text='作为教练服务过的行业')
    coach_customer_level = serializers.SerializerMethodField(help_text='曾服务过的客户的最高级别')
    coach_enterprise_attributes = serializers.SerializerMethodField(help_text='曾服务过的企业属性')
    coach_domain = serializers.SerializerMethodField(help_text='擅长教练领域')
    psychological_counseling_certification = serializers.SerializerMethodField(help_text='心理咨询认证')
    one_to_one_interview_time = serializers.IntegerField(read_only=True, help_text='目前积累的1对1辅导时长')
    group_interview_time = serializers.IntegerField(read_only=True, help_text='当前积累的团队辅导时长')
    platform_one_to_one_interview_time = serializers.SerializerMethodField(help_text='平台内积累的1对1辅导时长')
    platform_group_interview_time = serializers.SerializerMethodField(help_text='平台内团队辅导时长')
    coach_experience = serializers.CharField(help_text='教练经验', read_only=True)
    coach_style = serializers.CharField(help_text='教练风格', read_only=True)
    customer_evaluate = serializers.CharField(help_text='客户反馈', read_only=True)
    coach_record = serializers.ListField(help_text='教练履历', read_only=True)
    qualification = serializers.ListField(help_text='教练证书', read_only=True)
    evaluation_certification = serializers.ListField(help_text='测评认证', read_only=True)
    other_certification = serializers.ListField(help_text='其它证书', read_only=True)
    language = serializers.SerializerMethodField(help_text='语言', read_only=True)
    head_image_url = serializers.CharField(help_text='头像', read_only=True)
    english_name = serializers.CharField(help_text='英文名', read_only=True)
    true_name = serializers.CharField(source='coach.user.cover_name', read_only=True)
    posters_text = serializers.CharField(source='coach.posters_text', read_only=True)
    city = serializers.CharField(source='coach.city', read_only=True)
    price = serializers.SerializerMethodField(help_text='价格', read_only=True)
    not_show = serializers.SerializerMethodField(help_text='不展示的字段名称,前端只在客户/hr视角简历中用')
    customer_coach_course = serializers.SerializerMethodField(help_text='受训经验，客户/hr视角简历用')
    theme = serializers.SerializerMethodField(help_text='主题', read_only=True)
    coach_course_image = serializers.SerializerMethodField(help_text='受训经验的图片展示', read_only=True)
    coach_auth_image = serializers.SerializerMethodField(help_text='教练资质的图片展示', read_only=True)

    class Meta:
        model = Resume
        fields = ('id', 'name', 'user_id', 'work_industry', 'highest_position', 'enterprise_attributes',
                  'functional_module', 'work_experience', 'coach_course', 'working_years', 'coach_auth',
                  'coach_industry', 'coach_customer_level', 'coach_enterprise_attributes', 'coach_domain',
                  'psychological_counseling_certification', 'one_to_one_interview_time', 'group_interview_time',
                  'platform_one_to_one_interview_time', 'platform_group_interview_time', 'coach_experience',
                  'coach_style', 'customer_evaluate', 'customer_evaluate', 'coach_record', 'qualification',
                  'evaluation_certification', 'other_certification', 'language', 'head_image_url', 'english_name',
                  'true_name', 'price', 'posters_text', 'city', 'not_show', 'customer_coach_course', 'theme',
                  'job_profile', 'coach_course_image', 'coach_auth_image', 'share_image_url')

    def get_coach_course_image(self, obj):
        if obj.coach_course:
            return coach_public.get_coach_course_to_image(obj.coach_course)

    def get_coach_auth_image(self, obj):
        if obj.coach_auth:
            image_dict = CoachAuthEnum.get_image()
            return image_dict.get(str(obj.coach_auth))


    def get_price(self, obj):
        return obj.coach.display_price

    def get_language(self, obj):
        data = get_multiple_selection_detail('language', obj.coach_language)
        return data

    def get_work_industry(self, obj):
        data = get_multiple_selection_detail('work_industry', obj.work_industry)
        return data

    def get_highest_position(self, obj):
        data = get_multiple_selection_detail('highest_position', obj.highest_position)
        return data

    def get_enterprise_attributes(self, obj):
        data = get_multiple_selection_detail('enterprise_attributes', obj.enterprise_attributes)
        return data

    def get_functional_module(self, obj):
        data = get_multiple_selection_detail('functional_module', obj.functional_module)
        return data

    def get_coach_course(self, obj):
        data = get_multiple_selection_detail('coach_course', obj.coach_course)
        return data

    def get_customer_coach_course(self, obj):
        data = get_multiple_selection_detail('coach_course', obj.coach_course)
        other = data['content'][-1]['id']
        if other in data['value']:
            if len(data['value']) > 1:
                data['value'].remove(other)
                show_list = data['show_text'].split(',')
                show_list.remove(data['other_text'])
                data['show_text'] = ",".join(show_list)
                data['other_text'] = None
        return data

    def get_not_show(self, obj):
        not_show_list = []
        data = get_multiple_selection_detail('coach_course', obj.coach_course)
        other = data['content'][-1]['id']
        if other in data['value'] and len(data['value']) == 1:
            not_show_list.append('coach_course')
        if not obj.coach_auth:
            not_show_list.append('coach_auth')
        return not_show_list

    def get_coach_industry(self, obj):
        data = get_multiple_selection_detail('coach_industry', obj.coach_industry)
        return data

    def get_coach_customer_level(self, obj):
        data = get_multiple_selection_detail('coach_customer_level', obj.coach_customer_level)
        return data

    def get_coach_enterprise_attributes(self, obj):
        data = get_multiple_selection_detail('coach_enterprise_attributes', obj.coach_enterprise_attributes)
        return data

    def get_coach_domain(self, obj):
        data = get_multiple_selection_detail('coach_domain', obj.coach_domain)
        return data

    def get_psychological_counseling_certification(self, obj):
        data = get_multiple_selection_detail('psychological_counseling_certification',
                                             obj.psychological_counseling_certification)
        return data

    def get_theme(self, obj):
        coach = obj.coach
        content = [{"name": theme.name, "id": theme.id} for theme in Theme.objects.filter(deleted=False)]
        value, show_text = [], []
        for a_relation in ArticleThemeRelation.objects.filter(coach=coach, deleted=False):
            if a_relation.theme_id:
                value.append(a_relation.theme_id)
                show_text.append(a_relation.theme.name)
        if show_text:
            show_text = ','.join(show_text)
        else:
            show_text = None
        data = {"content": content, "value": value, "other_text": None, "show_text": show_text}
        return data

    def get_platform_one_to_one_interview_time(self, obj):
        times = ProjectInterview.objects.filter(
            place_category=ProjectInterviewPlaceCategoryEnum.online_one_to_one.value,
            type=INTERVIEW_TYPE_COACHING,
            public_attr__type=1, deleted=False,
            public_attr__end_time__lt=datetime.datetime.now(),
            public_attr__user_id=obj.coach.user_id).exclude(
            public_attr__status=ATTR_STATUS_INTERVIEW_CANCEL).aggregate(times_minute=Sum('times'))
        if times['times_minute']:
            interview_hour = round(times['times_minute'] / 60, 1)
        else:
            interview_hour = 0
        return interview_hour or 0

    def get_platform_group_interview_time(self, obj):
        times = ProjectInterview.objects.filter(
            place_category=ProjectInterviewPlaceCategoryEnum.offline_group_coach.value,
            public_attr__type=1, deleted=False,
            type=INTERVIEW_TYPE_COACHING,
            public_attr__end_time__lt=datetime.datetime.now(),
            public_attr__user_id=obj.coach.user_id).exclude(
            public_attr__status=ATTR_STATUS_INTERVIEW_CANCEL).aggregate(times_minute=Sum('times'))
        if times['times_minute']:
            interview_hour = round(times['times_minute'] / 60, 1)
        else:
            interview_hour = 0
        return interview_hour or 0


class ResumeUpdateSerializer(serializers.Serializer):
    id = serializers.IntegerField(write_only=True, required=True)
    name = serializers.CharField(write_only=True, help_text='简历名称', required=False, allow_null=True, allow_blank=True)
    user_id = serializers.IntegerField(write_only=True, required=False)
    english_name = serializers.CharField(write_only=True, required=False, allow_null=True, allow_blank=True)
    language = serializers.ListField(write_only=True, required=False, allow_null=True)
    head_image_url = serializers.CharField(write_only=True, required=False, allow_null=True, allow_blank=True)
    work_industry = serializers.ListField(write_only=True, help_text='个人工作过的行业', required=False, allow_null=True)
    highest_position = serializers.ListField(write_only=True, help_text='曾担任的最高职位', required=False, allow_null=True)
    enterprise_attributes = serializers.ListField(write_only=True, help_text='工作过的企业属性', required=False, allow_null=True)
    functional_module = serializers.ListField(write_only=True, help_text='工作过的职能模块', required=False, allow_null=True)
    work_experience = serializers.CharField(write_only=True, help_text='工作经验', required=False, allow_null=True, allow_blank=True)
    job_profile = serializers.CharField(write_only=True, help_text='一句话介绍工作经验', required=False, allow_null=True, allow_blank=True)
    coach_course = serializers.ListField(write_only=True, help_text='受训经验', required=False, allow_null=True)
    working_years = serializers.IntegerField(write_only=True, help_text='工作年限', required=False, allow_null=True)
    coach_auth = serializers.IntegerField(write_only=True, help_text='教练资质', required=False, allow_null=True)
    coach_industry = serializers.ListField(write_only=True, required=False, help_text='作为教练服务过的行业', allow_null=True)
    coach_customer_level = serializers.ListField(write_only=True, required=False, help_text='曾服务过的客户的最高级别', allow_null=True)
    coach_enterprise_attributes = serializers.ListField(write_only=True, required=False, help_text='曾服务过的企业属性', allow_null=True)
    coach_domain = serializers.ListField(write_only=True, required=False, help_text='擅长教练领域', allow_null=True)
    psychological_counseling_certification = serializers.ListField(write_only=True, required=False, help_text='心理咨询认证', allow_null=True)
    one_to_one_interview_time = serializers.FloatField(write_only=True, required=False, help_text='目前积累的1对1辅导时长', allow_null=True)
    group_interview_time = serializers.FloatField(write_only=True, required=False, help_text='当前积累的团队辅导时长', allow_null=True)
    coach_experience = serializers.CharField(write_only=True, required=False, help_text='教练经验', allow_null=True, allow_blank=True)
    coach_style = serializers.CharField(write_only=True, required=False, help_text='教练风格', allow_null=True, allow_blank=True)
    customer_evaluate = serializers.CharField(write_only=True, required=False, help_text='客户反馈', allow_null=True, allow_blank=True)
    coach_record = serializers.ListField(write_only=True, required=False, help_text='教练履历', allow_null=True)
    qualification = serializers.ListField(write_only=True, required=False, help_text='教练证书', allow_null=True)
    evaluation_certification = serializers.ListField(write_only=True, required=False, help_text='测评认证', allow_null=True)
    other_certification = serializers.ListField(write_only=True, required=False, help_text='其它证书', allow_null=True)
    theme = serializers.ListField(write_only=True, required=False, help_text='主题名称', allow_null=True)

    def edit_resume_msg(self, validated_data):
        resume = Resume.objects.get(pk=validated_data.pop('id'))
        user = resume.coach.user
        if 'user_id' in validated_data.keys():
            validated_data.pop('user_id')
        if not resume.is_customization:  # 主简历
            if 'english_name' in validated_data:
                user.english_name = validated_data.get('english_name')
            user.save()
        if 'language' in validated_data.keys():
            validated_data['coach_language'] = validated_data.pop('language')
        theme = None
        if 'theme' in validated_data.keys():
            theme = validated_data.pop('theme')
        with transaction.atomic():
            Resume.objects.filter(pk=resume.pk).update(**validated_data)
            UserTmp.objects.filter(type__in=[UserTmpEnum.resume.value, UserTmpEnum.entrant.value], data_id=resume.pk).delete()
            if theme:
                exists_theme_ids = resume.coach.coach_article_relation.filter(deleted=False).values_list(
                    'theme_id', flat=True)
                theme_ids = Theme.objects.filter(name__in=theme).values_list('id', flat=True)
                deleted_list = list(set(exists_theme_ids).difference(set(theme_ids)))
                add_lst = list(set(theme_ids).difference(set(exists_theme_ids)))
                if deleted_list:
                    ArticleThemeRelation.objects.filter(coach_id=resume.coach.id,
                                                        theme_id__in=deleted_list).update(deleted=True)
                if add_lst:
                    for theme_id in add_lst:
                        if not ArticleThemeRelation.objects.filter(coach_id=resume.coach.id, theme_id=theme_id,
                                                                   deleted=False).exists():
                            ArticleThemeRelation.objects.create(coach_id=resume.coach.id, theme_id=theme_id)

        resume = Resume.objects.filter(pk=resume.pk).first()
        if 'head_image_url' in validated_data.keys():
            task.update_coach_resume_share_url.delay(resume.pk, resume.head_image_url)  # 更新小程序教练简历分享的头像
        # 更新教练标签
        task.update_coach_tag.delay(resume.coach_id)
        data = ResumeDetailSerializer(resume).data
        return data

    def validate(self, attrs):
        attrs = validate_multiple_selection(attrs)
        return attrs


class ResumeCreateSerializer(serializers.Serializer):
    name = serializers.CharField(write_only=True, help_text='简历名称', required=False, allow_null=True, allow_blank=True)
    coach_id = serializers.IntegerField(write_only=True, required=True)
    english_name = serializers.CharField(write_only=True, required=False, allow_null=True, allow_blank=True)
    language = serializers.ListField(write_only=True, required=False, allow_null=True)
    head_image_url = serializers.CharField(write_only=True, required=False, allow_null=True, allow_blank=True)
    work_industry = serializers.ListField(write_only=True, help_text='个人工作过的行业', required=False, allow_null=True)
    highest_position = serializers.ListField(write_only=True, help_text='曾担任的最高职位', required=False, allow_null=True)
    enterprise_attributes = serializers.ListField(write_only=True, help_text='工作过的企业属性', required=False, allow_null=True)
    functional_module = serializers.ListField(write_only=True, help_text='工作过的职能模块', required=False, allow_null=True)
    work_experience = serializers.CharField(write_only=True, help_text='工作经验', required=False, allow_null=True, allow_blank=True)
    job_profile = serializers.CharField(write_only=True, help_text='一句话介绍', required=False, allow_null=True, allow_blank=True)
    coach_course = serializers.ListField(write_only=True, help_text='受训经验', required=False, allow_null=True)
    working_years = serializers.IntegerField(write_only=True, help_text='工作年限', required=False, allow_null=True)
    coach_auth = serializers.IntegerField(write_only=True, help_text='教练资质', required=False, allow_null=True)
    coach_industry = serializers.ListField(write_only=True, required=False, help_text='作为教练服务过的行业', allow_null=True)
    coach_customer_level = serializers.ListField(write_only=True, required=False, help_text='曾服务过的客户的最高级别', allow_null=True)
    coach_enterprise_attributes = serializers.ListField(write_only=True, required=False, help_text='曾服务过的企业属性', allow_null=True)
    coach_domain = serializers.ListField(write_only=True, required=False, help_text='擅长教练领域', allow_null=True)
    psychological_counseling_certification = serializers.ListField(write_only=True, required=False, help_text='心理咨询认证', allow_null=True)
    one_to_one_interview_time = serializers.FloatField(write_only=True, required=False, help_text='目前积累的1对1辅导时长', allow_null=True)
    group_interview_time = serializers.FloatField(write_only=True, required=False, help_text='当前积累的团队辅导时长', allow_null=True)
    coach_experience = serializers.CharField(write_only=True, required=False, help_text='教练经验', allow_null=True, allow_blank=True)
    coach_style = serializers.CharField(write_only=True, required=False, help_text='教练风格', allow_null=True, allow_blank=True)
    customer_evaluate = serializers.CharField(write_only=True, required=False, help_text='客户反馈', allow_null=True, allow_blank=True)
    coach_record = serializers.ListField(write_only=True, required=False, help_text='教练履历', allow_null=True)
    qualification = serializers.ListField(write_only=True, required=False, help_text='教练证书', allow_null=True)
    evaluation_certification = serializers.ListField(write_only=True, required=False, help_text='测评认证', allow_null=True)
    other_certification = serializers.ListField(write_only=True, required=False, help_text='其它证书', allow_null=True)
    creator_id = serializers.IntegerField(write_only=True, required=False, help_text='创建人')
    theme = serializers.ListField(write_only=True, required=False, help_text='主题名称', allow_null=True)

    def add_resume_msg(self, validated_data):
        if 'language' in validated_data.keys():
            validated_data['coach_language'] = validated_data.pop('language')
        theme = None
        if 'theme' in validated_data.keys():
            theme = validated_data.pop('theme')

        with transaction.atomic():
            if 'project_id' in validated_data.keys():
                project_id = validated_data.pop('project_id')
            else:
                project_id = None

            validated_data['is_customization'] = True
            resume = Resume.objects.create(**validated_data)

            if project_id:
                coach_id = validated_data.get('coach_id')
                project_coach = ProjectCoach.objects.filter(
                    project_id=project_id,
                    coach_id=coach_id,
                    deleted=False,
                    status=ProjectCoachStatusEnum.adopt,
                    member__isnull=True,
                    project_group_coach__isnull=True
                ).first()
                if project_coach:
                    project_coach.resume.append(resume.id)
                    project_coach.save()
            if theme:
                exists_theme_ids = resume.coach.coach_article_relation.filter(deleted=False).values_list(
                    'theme_id', flat=True)
                theme_ids = Theme.objects.filter(name__in=theme).values_list('id', flat=True)
                deleted_list = list(set(exists_theme_ids).difference(set(theme_ids)))
                add_lst = list(set(theme_ids).difference(set(exists_theme_ids)))
                if deleted_list:
                    ArticleThemeRelation.objects.filter(coach_id=resume.coach.id,
                                                        theme_id__in=deleted_list).update(deleted=True)
                if add_lst:
                    for theme_id in add_lst:
                        if not ArticleThemeRelation.objects.filter(coach_id=resume.coach.id, theme_id=theme_id,
                                                                   deleted=False).exists():
                            ArticleThemeRelation.objects.create(coach_id=resume.coach.id, theme_id=theme_id)
            if 'head_image_url' in validated_data.keys():
                task.update_coach_resume_share_url.delay(resume.pk, resume.head_image_url)  # 更新小程序教练简历分享的头像
        # 更新教练标签
        task.update_coach_tag(resume.coach_id)
        data = ResumeDetailSerializer(resume).data
        return data

    def validate(self, attrs):
        attrs = validate_multiple_selection(attrs)
        return attrs