import datetime

from rest_framework.decorators import action
from rest_framework.viewsets import GenericViewSet

from utils.messagecenter import getui
from utils.pagination import StandardResultsSetPagination
from utils.api_response import success_response, parameter_error_response
from drf_yasg.utils import swagger_auto_schema
from drf_yasg import openapi
from django.db import transaction
from django.db.models import Q

from utils.send_account_email import get_project_manage_wx_user_id
from wisdom_v2.common import coach_task_public
from wisdom_v2.enum.message_type_enum import LarkMessageTypeEnum
from wisdom_v2.models import CoachTask, InterviewRecordTemplateAnswer, QuestionObjectRelationship, ActionPlan, Diary, \
    Habit, PublicAttr, ProjectInterested, MultipleAssociationRelation, WorkWechatUser, UserTmp
from wisdom_v2.enum.service_content_enum import InterviewRecordTemplateQuestionTypeEnum, \
    InterviewRecordTemplateQuestionAnswerTypeEnum, NewCoachTaskTypeEnum, MultipleAssociationRelationType<PERSON>num, \
    PdfReportTypeEnum
from wisdom_v2.enum.project_interview_enum import ObjectTypeEnum
from wisdom_v2.views.coach_task_action import CoachTaskSerlizer, check_answer_data, CoachTaskQuestionnaireSerializer, \
    CoachTaskReportDetailSerializer, CoachTaskStakeholderReportDetailSerializer
from wisdom_v2.app_views.app_interview_offline_record import save_public_attr, save_trainee_member_public_attr
from wisdom_v2.views.constant import ATTR_TYPE_DIARY, ATTR_TYPE_HABIT, ATTR_TYPE_ACTIONPLAN, \
    ATTR_TYPE_STAKEHOLDER_COACH_TASK
from utils.task import coach_task_view_message, get_user_pdf_url, send_lark_business_message
from wisdom_v2.enum.user_enum import UserRoleEnum, UserTmpEnum


class CoachTaskViewSet(GenericViewSet):
    queryset = CoachTask.objects.filter(
        Q(coach_submit_time__isnull=False) | Q(coachee_submit_time__isnull=False), deleted=False).order_by(
        '-coach_submit_time')
    serializer_class = CoachTaskSerlizer

    @swagger_auto_schema(
        operation_id='教练任务列表',
        operation_summary='教练任务列表',
        manual_parameters=[
            openapi.Parameter('user_id', openapi.IN_QUERY, description='用户id', type=openapi.TYPE_NUMBER,
                              required=False),
            openapi.Parameter('project_id', openapi.IN_QUERY, description='项目id', type=openapi.TYPE_NUMBER,
                              required=False),
            openapi.Parameter('name', openapi.IN_QUERY, description='姓名', type=openapi.TYPE_STRING, required=False)
        ],
        tags=['教练任务相关']
    )
    @action(methods=['get'], detail=False, url_path='coach_task_report_list')
    def coach_task_report_list(self, request, *args, **kwargs):
        queryset = self.get_queryset()
        user_id = request.query_params.get('user_id')
        project_id = request.query_params.get('project_id')
        name = request.query_params.get('name')
        if user_id:
            queryset = queryset.filter(Q(public_attr__user_id=user_id) | Q(public_attr__target_user_id=user_id))
        if project_id:
            queryset = queryset.filter(public_attr__project_id=project_id)
        if name:
            queryset = queryset.filter(public_attr__target_user__true_name__icontains=name)
        # 分页
        paginator = StandardResultsSetPagination()
        page_list = paginator.paginate_queryset(queryset, self.request)
        serializer = self.get_serializer(page_list, many=True)
        response = paginator.get_paginated_response(serializer.data)
        return success_response(response)

    @swagger_auto_schema(
        operation_id='教练任务报告详情',
        operation_summary='教练任务报告详情',
        manual_parameters=[
            openapi.Parameter('coach_task_id', openapi.IN_QUERY, description='教练任务id', type=openapi.TYPE_NUMBER,
                              required=True)
        ],
        tags=['教练任务相关']
    )
    @action(methods=['get'], detail=False, url_path='coach_task_report_detail')
    def coach_task_report_detail(self, request, *args, **kwargs):
        try:
            coach_task_id = request.query_params.get('coach_task_id')
            coach_task = CoachTask.objects.get(id=coach_task_id)
        except CoachTask.DoesNotExist:
            return parameter_error_response()
        serializer = CoachTaskReportDetailSerializer(coach_task)
        data = serializer.data
        return success_response(data)

    @swagger_auto_schema(
        operation_id='教练任务问答详情',
        operation_summary='教练任务问答详情',
        manual_parameters=[
            openapi.Parameter('coach_task_id', openapi.IN_QUERY, description='教练任务id', type=openapi.TYPE_NUMBER,
                              required=True)
        ],
        tags=['教练任务相关']
    )
    @action(methods=['get'], detail=False, url_path='coach_task_question_detail', authentication_classes=[])
    def coach_task_question_detail(self, request, *args, **kwargs):
        try:
            coach_task_id = request.query_params.get('coach_task_id')
            coach_task = CoachTask.objects.get(id=coach_task_id)
        except CoachTask.DoesNotExist:
            return parameter_error_response()
        role = request.headers.get('role')
        role = int(role) if role else 0
        serializer = CoachTaskQuestionnaireSerializer(coach_task, context={'role': role})
        data = serializer.data
        return success_response(data)

    @swagger_auto_schema(
        operation_id='填写教练任务报告',
        operation_summary='填写教练任务报告',
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'coach_task_id': openapi.Schema(type=openapi.TYPE_NUMBER, description='教练任务id'),
                'stakeholder_id': openapi.Schema(type=openapi.TYPE_NUMBER, description='利益相关者id'),
                'is_single': openapi.Schema(type=openapi.TYPE_NUMBER, description='是否单步提交 1-是 0-否'),
                'question_detail': openapi.Schema(type=openapi.TYPE_ARRAY, items=openapi.TYPE_OBJECT,
                                                  description='回答问题详情'),
            }
        ),
        tags=['教练任务相关']
    )
    @action(methods=['post'], detail=False, url_path='edit_coach_task', authentication_classes=[])
    def edit_coach_task(self, request, *args, **kwargs):
        try:
            coach_task_id = request.data['coach_task_id']
            coach_task = CoachTask.objects.get(pk=coach_task_id)
            question_detail = request.data['question_detail']
            is_single = request.data.get('is_single', 0)
            stakeholder_id = request.data.get('stakeholder_id')
            role = request.headers.get('role')
            role = int(role) if role else 0
        except:
            return parameter_error_response()
        err = check_answer_data(question_detail, coach_task_id, is_single)
        if err:
            return success_response({'status': 1, 'msg': err})
        required_list = list(set([question['id'] for question in question_detail if question['required']]))
        question_list = []
        with transaction.atomic():
            if stakeholder_id:
                answer_public_attr = PublicAttr.objects.create(
                    user_id=stakeholder_id,
                    target_user=coach_task.public_attr.target_user,
                    project=coach_task.public_attr.project,
                    type=ATTR_TYPE_STAKEHOLDER_COACH_TASK
                )

                # 查询利益相关者关联表id
                # 绑定利益相关者答题记录
                interested = ProjectInterested.objects.get(
                    interested_id=stakeholder_id,
                    master=coach_task.public_attr.target_user,
                    project=coach_task.public_attr.project,
                    deleted=False
                    )
                MultipleAssociationRelation.objects.filter(
                    main_id=coach_task.id,
                    secondary_id=interested.id,
                    deleted=False,
                    type=MultipleAssociationRelationTypeEnum.stakeholder_coach_task
                ).update(public_attr=answer_public_attr)
            else:
                answer_public_attr = coach_task.public_attr
            for question in question_detail:
                # 填空题判断
                if question['type'] == InterviewRecordTemplateQuestionTypeEnum.blank.value:
                    # 普通文本框/多条文本框，多条文本框时content字段格式为列表
                    if question['answer_type'] in [
                            InterviewRecordTemplateQuestionAnswerTypeEnum.normal_text.value,
                            InterviewRecordTemplateQuestionAnswerTypeEnum.multiple_text.value]:
                        if 'answer' in question.keys() and question['answer']:
                            content = question['answer']['content']
                            if 'id' in question['answer'].keys():
                                InterviewRecordTemplateAnswer.objects.filter(pk=question['answer']['id']).update(
                                    answer=content)
                            else:
                                InterviewRecordTemplateAnswer.objects.create(
                                    public_attr=answer_public_attr,
                                    question_id=question['id'], answer=content)
                            if question['required']:
                                question_list.append(question['id'])

                    # 能力标签
                    if question['answer_type'] == InterviewRecordTemplateQuestionAnswerTypeEnum.power_tag.value:
                        # edit : {"id":1, "content" : "xxx"}
                        if 'answer' in question.keys() and question['answer']:
                            if 'id' in question['answer'].keys():
                                InterviewRecordTemplateAnswer.objects.filter(
                                    pk=question['answer']['id']).update(
                                    answer=str(question['answer']['content']))
                            else:
                                InterviewRecordTemplateAnswer.objects.create(
                                    public_attr=answer_public_attr,
                                    question_id=question['id'], answer=str(question['answer']['content']))
                            if question['required']:
                                question_list.append(question['id'])

                    # 行动计划
                    if question['answer_type'] == InterviewRecordTemplateQuestionAnswerTypeEnum.action_plan.value:
                        # 根据传入id 和 现有问题 判断是否又删除的行动计划
                        if 'answer' in question.keys():
                            now_answer_ids = [i['id'] for i in question['answer'] if 'id' in i.keys()]
                            exists_answer_ids = list(InterviewRecordTemplateAnswer.objects.filter(
                                public_attr=answer_public_attr, question_id=question['id'],
                                question__type=InterviewRecordTemplateQuestionTypeEnum.blank.value,
                                question__answer_type=InterviewRecordTemplateQuestionAnswerTypeEnum.action_plan.value).\
                                values_list('id', flat=True))
                            deleted_list = list(set(exists_answer_ids).difference(set(now_answer_ids)))
                            if deleted_list:
                                del_action_plan_lst = list(QuestionObjectRelationship.objects.filter(
                                    answer_id__in=deleted_list, deleted=False).values_list('obj_id', flat=True))
                                ActionPlan.objects.filter(id__in=del_action_plan_lst).delete()
                                QuestionObjectRelationship.objects.filter(answer_id__in=deleted_list).delete()
                                InterviewRecordTemplateAnswer.objects.filter(id__in=deleted_list).delete()

                        if 'answer' in question.keys() and question['answer']:
                            for answer_f in question['answer']:
                                if 'id' in answer_f.keys():
                                    answer = InterviewRecordTemplateAnswer.objects.get(pk=answer_f['id'])
                                    relationship = answer.object_relationship.filter(
                                        type=ObjectTypeEnum.action_plan.value, deleted=False).first()
                                    obj_id = relationship.obj_id
                                    ActionPlan.objects.filter(pk=obj_id).update(content=answer_f['content'],
                                                                                end_date=answer_f['end_date'])


                                else:
                                    answer = InterviewRecordTemplateAnswer.objects.create(
                                        public_attr=answer_public_attr,
                                        question_id=question['id'])
                                    user = coach_task.public_attr.user if role in [1, 3] else \
                                        coach_task.public_attr.target_user
                                    if role in [1, 2]:
                                        public_attr = save_public_attr(project=coach_task.public_attr.project,
                                                                       user=user,
                                                                       save_type=ATTR_TYPE_ACTIONPLAN)
                                    else:
                                        public_attr = save_trainee_member_public_attr(user=user,
                                                                                      save_type=ATTR_TYPE_ACTIONPLAN)

                                    action_plan = ActionPlan.objects.create(
                                        coach_task=coach_task, public_attr=public_attr,
                                        content=answer_f['content'], end_date=answer_f['end_date'],
                                        creator_role=6 if role in [UserRoleEnum.coachee.value, UserRoleEnum.trainee_coachee.value] else 4)

                                    QuestionObjectRelationship.objects.create(
                                        type=ObjectTypeEnum.action_plan.value, obj_id=action_plan.pk, answer=answer)
                            if question['required']:
                                question_list.append(question['id'])

                    # 习惯养成
                    if question['answer_type'] == InterviewRecordTemplateQuestionAnswerTypeEnum.habit_formation.value:
                        # 根据传入id 和 现有问题 判断是否又删除的行动计划
                        if 'answer' in question.keys():
                            now_answer_ids = [i['id'] for i in question['answer'] if 'id' in i.keys()]
                            exists_answer_ids = list(InterviewRecordTemplateAnswer.objects.filter(
                                public_attr=answer_public_attr, question_id=question['id'],
                                question__type=InterviewRecordTemplateQuestionTypeEnum.blank.value,
                                question__answer_type=InterviewRecordTemplateQuestionAnswerTypeEnum.habit_formation.value).\
                                                     values_list('id', flat=True))
                            deleted_list = list(set(exists_answer_ids).difference(set(now_answer_ids)))
                            if deleted_list:
                                del_habit_lst = list(QuestionObjectRelationship.objects.filter(
                                    answer_id__in=deleted_list, deleted=False).values_list('obj_id', flat=True))
                                Habit.objects.filter(id__in=del_habit_lst).delete()
                                QuestionObjectRelationship.objects.filter(answer_id__in=deleted_list).delete()
                                InterviewRecordTemplateAnswer.objects.filter(id__in=deleted_list).delete()

                        if 'answer' in question.keys() and question['answer']:
                            for answer_f in question['answer']:
                                if 'id' in answer_f.keys():
                                    answer = InterviewRecordTemplateAnswer.objects.get(pk=answer_f['id'])
                                    relationship = answer.object_relationship.filter(
                                        type=ObjectTypeEnum.habit.value, deleted=False).first()
                                    obj_id = relationship.obj_id
                                    Habit.objects.filter(pk=obj_id).update(
                                        when=answer_f['when'], stop=answer_f['stop'],
                                        change=answer_f['change'], start_date=answer_f['start_date'],
                                        end_date=answer_f['end_date']
                                    )
                                else:
                                    answer = InterviewRecordTemplateAnswer.objects.create(
                                        public_attr=answer_public_attr,
                                        question_id=question['id'])
                                    user = coach_task.public_attr.user if role in [1, 3] else \
                                        coach_task.public_attr.target_user

                                    if role in [1, 2]:
                                        public_attr = save_public_attr(project=coach_task.public_attr.project,
                                                                       user=user,
                                                                       save_type=ATTR_TYPE_HABIT)
                                    else:
                                        public_attr = save_trainee_member_public_attr(save_type=ATTR_TYPE_HABIT,
                                                                                      user=user)

                                    habit = Habit.objects.create(
                                        coach_task=coach_task, public_attr=public_attr,
                                        stop=answer_f['stop'], when=answer_f['when'],
                                        change=answer_f['change'], start_date=answer_f['start_date'],
                                        end_date=answer_f['end_date'], creator_role=6 if
                                        role in [UserRoleEnum.coachee.value, UserRoleEnum.trainee_coachee.value] else 4
                                    )
                                    QuestionObjectRelationship.objects.create(
                                        type=ObjectTypeEnum.habit.value, obj_id=habit.pk, answer=answer)
                            if question['required']:
                                question_list.append(question['id'])

                    # 成长笔记
                    if question['answer_type'] == InterviewRecordTemplateQuestionAnswerTypeEnum.growth_notes.value:
                        if 'answer' in question.keys() and question['answer']:
                            # {"id": answer.id, "content": diary.content}
                            if 'id' in question['answer'].keys():
                                answer = InterviewRecordTemplateAnswer.objects.get(pk=question['answer']['id'])
                                relationship = answer.object_relationship.filter(
                                    type=ObjectTypeEnum.diary.value, deleted=False).first()
                                obj_id = relationship.obj_id
                                Diary.objects.filter(pk=obj_id).update(content=question['answer']['content'])
                            else:
                                answer = InterviewRecordTemplateAnswer.objects.create(
                                    public_attr=answer_public_attr,
                                    question_id=question['id'])
                                user = coach_task.public_attr.user if role in [1, 3] else \
                                    coach_task.public_attr.target_user

                                if role in [1, 2]:
                                    public_attr = save_public_attr(project=coach_task.public_attr.project,
                                                                   user=user,
                                                                   save_type=ATTR_TYPE_DIARY)
                                else:
                                    public_attr = save_trainee_member_public_attr(save_type=ATTR_TYPE_DIARY,
                                                                                  user=user)

                                diary = Diary.objects.create(
                                    coach_task=coach_task, public_attr=public_attr,
                                    content=question['answer']['content'], type=2,
                                    creator_role=6 if role in [UserRoleEnum.coachee.value,
                                                               UserRoleEnum.trainee_coachee.value] else 4
                                )
                                QuestionObjectRelationship.objects.create(
                                    type=ObjectTypeEnum.diary.value, obj_id=diary.pk, answer=answer)
                            if question['required']:
                                question_list.append(question['id'])

                # 单选
                if question['type'] == InterviewRecordTemplateQuestionTypeEnum.single.value:
                    # option : [{'id':1, 'option_custom': 'xxx'}]
                    if 'answer' in question.keys() and question['answer']:
                        # {'option': answer.option_id, 'option_custom': answer.option_custom}

                        answer = InterviewRecordTemplateAnswer.objects.filter(
                            question__type=InterviewRecordTemplateQuestionTypeEnum.single.value,
                            question_id=question['id'],
                            public_attr=answer_public_attr).first()
                        if answer:
                            if answer.option_id != question['answer'][0]['option']:
                                answer.option_id = question['answer'][0]['option']
                            if 'option_custom' in question['answer'][0] and \
                                    question['answer'][0]['option_custom']:
                                answer.option_custom = question['answer'][0]['option_custom']
                            answer.save()
                        else:
                            if 'option_custom' in question['answer'][0] and \
                                    question['answer'][0]['option_custom']:
                                InterviewRecordTemplateAnswer.objects.create(
                                    public_attr=answer_public_attr,
                                    question_id=question['id'], option_id=question['answer'][0]['option'],
                                    option_custom=question['answer'][0]['option_custom']
                                    )
                            else:
                                InterviewRecordTemplateAnswer.objects.create(
                                    public_attr=answer_public_attr,
                                    question_id=question['id'], option_id=question['answer'][0]['option'])
                        if question['required']:
                            question_list.append(question['id'])
                # 多选
                if question['type'] == InterviewRecordTemplateQuestionTypeEnum.multiple.value:
                    if 'answer' in question.keys() and question['answer']:
                        exists_option_ids = [answer.option_id for answer in InterviewRecordTemplateAnswer.objects.
                            filter(public_attr=answer_public_attr,
                                   question__type=InterviewRecordTemplateQuestionTypeEnum.multiple.value,
                                   question_id=question['id'])]

                        now_option_ids = [option['option'] for option in question['answer']]

                        deleted_list = list(set(exists_option_ids).difference(set(now_option_ids)))

                        if deleted_list:
                            InterviewRecordTemplateAnswer.objects.filter(
                                public_attr=answer_public_attr,
                                question__type=InterviewRecordTemplateQuestionTypeEnum.multiple.value,
                                question_id=question['id'], option_id__in=deleted_list).delete()
                        if question['answer']:
                            for option in question['answer']:
                                answer = InterviewRecordTemplateAnswer.objects.filter(
                                    public_attr=answer_public_attr,
                                    option_id=option['option'], question_id=question['id'],
                                    question__type=InterviewRecordTemplateQuestionTypeEnum.multiple.value).first()
                                if answer:
                                    if 'option_custom' in option and option['option_custom']:
                                        answer.option_custom = option['option_custom']
                                        answer.save()
                                else:
                                    if 'option_custom' in option and option['option_custom']:
                                        InterviewRecordTemplateAnswer.objects.create(
                                            public_attr=answer_public_attr,
                                            question_id=question['id'], option_id=option['option'],
                                            option_custom=option['option_custom']
                                        )
                                    else:
                                        InterviewRecordTemplateAnswer.objects.create(
                                            public_attr=answer_public_attr,
                                            question_id=question['id'], option_id=option['option'])
                            if question['required']:
                                question_list.append(question['id'])

                # 评分
                if question['type'] == InterviewRecordTemplateQuestionTypeEnum.rating.value:
                    if 'answer' in question.keys() and question['answer']:

                        if 'id' in question['answer'].keys():
                            InterviewRecordTemplateAnswer.objects.filter(pk=question['answer']['id']).update(
                                score=question['answer']['score'])
                        else:
                            InterviewRecordTemplateAnswer.objects.create(
                                public_attr=answer_public_attr,
                                question_id=question['id'], score=question['answer']['score']
                            )
                        if question['required']:
                            question_list.append(question['id'])

        question_list = list(set(question_list))
        params = {
            'coach_task_name': coach_task.template.title,
            'project_name': coach_task.public_attr.project.name,
            'coachee_name': coach_task.public_attr.target_user.cover_name,
            'coach_name': coach_task.public_attr.user.cover_name
        }

        # 是否是利益相关者填写
        if stakeholder_id:
            # 申请pdf文件
            get_user_pdf_url.delay(
                coach_task.pk,
                f'{coach_task.project_bundle.project_member.user.cover_name}_{coach_task.template.title}_{coach_task.pk}.pdf',
                pdf_type=PdfReportTypeEnum.stakeholder_report.value)

            other_users = ProjectInterested.objects.filter(
                project_id=coach_task.public_attr.project.pk,
                deleted=False,
                master_id=coach_task.public_attr.target_user.id).count()

            attr_count = MultipleAssociationRelation.objects.filter(
                main_id=coach_task.id,
                deleted=False,
                type=MultipleAssociationRelationTypeEnum.stakeholder_coach_task,
                public_attr__isnull=False,
            ).count()

            # 是否所有利益相关者都填写
            # 利益相关者 = 需要人数
            if other_users <= attr_count:
                coach_task.stakeholder_submit_time = datetime.datetime.now()
                coach_task.save()

                project = coach_task.project_bundle.project
                project_member = coach_task.project_bundle.project_member
                # 通知教练
                work_wechat_user = WorkWechatUser.objects.filter(
                    wx_user_id__isnull=False,
                    user_id=coach_task.public_attr.user_id,
                    deleted=False
                ).first()
                if work_wechat_user:
                    company_name = project.company.real_name
                    content_item = [
                        {"key": "客户名称", "value": project_member.user.cover_name},
                        {"key": "所属企业", "value": company_name},
                        {"key": "所属项目", "value": project.name},
                        {"key": "报告名称", "value": coach_task.template.title}
                    ]
                    getui.send_work_wechat_coach_notice.delay(
                        work_wechat_user.wx_user_id,
                        'stakeholder_coach_task_report_remind',
                        content_item=content_item,
                        report_id=coach_task.pk,
                        company_name=company_name,
                        user_name=project_member.user.cover_name,
                        project_id=project.pk,
                        project_name=project.name,
                        coach_id=work_wechat_user.user_id,
                        coach_name=work_wechat_user.user.cover_name,
                        coachee_id=project_member.user_id,
                        coachee_name=project_member.user.cover_name
                    )
                # 通知客户
                msg, sender, external_user_id = get_project_manage_wx_user_id(
                    project.pk,
                    project_member.user_id,
                    'stakeholder_coach_task_report_remind'
                )
                if sender and external_user_id:
                    text = f'您的{coach_task.template.title}报告已生成，点击下方链接查看'
                    title = f"{coach_task.template.title}报告生成提醒".encode('utf-8')
                    title = title.decode('utf-8')
                    if len(title) > 32:
                        title = title[:28] + '...'
                    page = f"pages_customer/coach_task/coach_task_stakeholder_report?report_id={coach_task.pk}&refer=2"
                    sender_user = WorkWechatUser.objects.filter(
                        wx_user_id=sender,
                        user__isnull=False,
                        deleted=False
                    ).first()
                    if sender_user:
                        getui.send_work_wechat_coachee_notice.delay(
                            sender, text, external_user_id, page, title,
                            content_type='add_msg_template',
                            coach_id=coach_task.public_attr.user_id,
                            coach_name=coach_task.public_attr.user.cover_name,
                            project_id=project.id,
                            project_name=project.name,
                            coachee_id=project_member.user_id,
                            coachee_name=project_member.user.cover_name,
                            forward_by=sender_user.user.cover_name,
                            forward_by_id=sender_user.user.id,
                            sender_id=sender_user.user.id
                        )
                send_lark_business_message.delay(LarkMessageTypeEnum.coach_task_report, params)
        else:
            if question_list == required_list:
                if role == 1:
                    coach_task.coach_submit_time = datetime.datetime.now()
                    # 教练提交阶段报告后，立即提醒客户查看
                    coach_task_view_message.delay(coach_task.pk)
                    UserTmp.objects.filter(
                        data_id=coach_task.id,
                        type=UserTmpEnum.coach_tasks,
                        user_id=coach_task.public_attr.user_id
                    ).delete()
                else:
                    coach_task.coachee_submit_time = datetime.datetime.now()
                    UserTmp.objects.filter(
                        data_id=coach_task.id,
                        type=UserTmpEnum.coach_tasks,
                        user_id=coach_task.public_attr.target_user_id
                    ).delete()
                coach_task.save()

            # “利益相关者访谈总结报告，不用生成pdf文件，改为教练上传”
            is_summary_report_status = coach_task_public.check_coach_task_is_stakeholder_interview_summary(coach_task_id)
            if not is_summary_report_status:
                get_user_pdf_url.delay(
                    coach_task.pk,
                    f'{coach_task.project_bundle.project_member.user.cover_name}_{coach_task.template.title}_{coach_task.pk}.pdf',
                    pdf_type=PdfReportTypeEnum.default_coach_task.value)
                send_lark_business_message.delay(LarkMessageTypeEnum.coach_task_report, params)
        return success_response()

    @swagger_auto_schema(
        operation_id='利益相关者调研报告详情',
        operation_summary='利益相关者调研报告详情',
        manual_parameters=[
            openapi.Parameter('coach_task_id', openapi.IN_QUERY, description='教练任务id', type=openapi.TYPE_NUMBER,
                              required=True)
        ],
        tags=['教练任务相关']
    )
    @action(methods=['get'], detail=False, url_path='stakeholder_report_detail', authentication_classes=[])
    def stakeholder_report_detail(self, request, *args, **kwargs):
        try:
            coach_task_id = request.query_params.get('coach_task_id')
            coach_task = CoachTask.objects.get(id=coach_task_id)
        except CoachTask.DoesNotExist:
            return parameter_error_response()

        if coach_task.type != NewCoachTaskTypeEnum.stakeholder_research:
            return parameter_error_response('教练任务类型错误')

        relation = MultipleAssociationRelation.objects.filter(
            main_id=coach_task.id,
            deleted=False,
            public_attr__isnull=False,
            type=MultipleAssociationRelationTypeEnum.stakeholder_coach_task
        )
        serializer = CoachTaskStakeholderReportDetailSerializer(
            coach_task,
            context={
                'relation': relation
            })
        return success_response(serializer.data)
