from drf_yasg import openapi
from drf_yasg.utils import swagger_auto_schema


from rest_framework import viewsets

from utils.pagination import StandardResultsSetPagination
from ..models import ContentOperationArticle
from utils.api_response import success_response
from .content_operation_article_actions import ContentOperationArticleSerializers


class ContentOperationArticleViewSet(viewsets.ModelViewSet):
    queryset = ContentOperationArticle.objects.filter().order_by('-article__top_at', '-article__created_at')
    serializer_class = ContentOperationArticleSerializers

    @swagger_auto_schema(
        operation_id='文章数据列表',
        operation_summary='文章数据列表',
        manual_parameters=[
            openapi.Parameter('keyword', openapi.IN_QUERY, description='关键字（文章标题）', type=openapi.TYPE_STRING),
            openapi.Parameter('category', openapi.IN_QUERY, description='文章类型 ｜  1:经验, 2:关于教练, 3:工具', type=openapi.TYPE_NUMBER),
            openapi.Parameter('page', openapi.TYPE_ARRAY, description='第几页', type=openapi.TYPE_NUMBER),
            openapi.Parameter('page_size', openapi.IN_QUERY, description='每页显示条数默认15条', type=openapi.TYPE_NUMBER),
        ],
        tags=['内容管理数据相关']
    )
    def list(self, request, *args, **kwargs):

        content_article = self.get_queryset()
        if request.query_params.get('keyword', 0):
            content_article = content_article.filter(article__title__contains=request.query_params.get('keyword'))
        if request.query_params.get('category', 0):
            content_article = content_article.filter(article__category=request.query_params.get('category'))

        # 分页
        paginator = StandardResultsSetPagination()
        page_list = paginator.paginate_queryset(content_article, self.request)
        serializer = self.get_serializer(page_list, many=True)
        response = paginator.get_paginated_response(serializer.data)
        return success_response(response)
