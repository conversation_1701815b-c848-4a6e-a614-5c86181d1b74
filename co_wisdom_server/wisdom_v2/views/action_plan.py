from drf_yasg import openapi
from rest_framework.decorators import action

from drf_yasg.utils import swagger_auto_schema

from rest_framework import mixins
from rest_framework import serializers

from utils.pagination import StandardResultsSetPagination
from utils.api_response import success_response, parameter_error_response
from rest_framework.viewsets import GenericViewSet
from rest_framework.response import Response
from wisdom_v2.models import User, Project, Company, ActionPlan, ProjectInterview
from .action_plan_actions import ActionPlanSerializers


class ActionPlanViewSet(GenericViewSet):

    queryset = ActionPlan.objects.all().order_by('-created_at')
    serializer_class = ActionPlanSerializers

    @swagger_auto_schema(
        operation_id='行动计划列表',
        operation_summary='行动计划列表',
        manual_parameters=[
            openapi.Parameter('interview_id', openapi.IN_QUERY, description='约谈id', type=openapi.TYPE_NUMBER,
                              required=True)
        ],
        tags=['行动计划相关']
    )
    def list(self, request, *args, **kwargs):
        queryset = self.get_queryset()
        # 分页
        paginator = StandardResultsSetPagination()
        page_list = paginator.paginate_queryset(queryset, self.request)
        serializer = self.serializer_class(page_list, many=True, context={'queryset': queryset})
        response = paginator.get_paginated_response(serializer.data)
        return success_response(response)

    @swagger_auto_schema(
        operation_id='行动计划详情',
        operation_summary='行动计划详情',
        manual_parameters=[
        ],
        tags=['行动计划相关']
    )
    def retrieve(self, request, *args, **kwargs):
        instance = self.get_object()
        serializer = self.get_serializer(instance)
        return success_response(serializer.data)
    @swagger_auto_schema(
        operation_id='创建行动计划',
        operation_summary='创建行动计划',
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'interview_id': openapi.Schema(type=openapi.TYPE_NUMBER, description='约谈id'),
                'status': openapi.Schema(type=openapi.TYPE_NUMBER, description='状态 1: 进行中 2:已完成 3:已取消'),
                'content': openapi.Schema(type=openapi.TYPE_STRING, description='内容'),
                'notes': openapi.Schema(type=openapi.TYPE_STRING, description='注释'),
            }
        ),
        tags=['行动计划相关']
    )
    def create(self, request, *args, **kwargs):
        try:
            ProjectInterview.objects.get(pk=int(request.data.get('interview_id', 0)))
        except ProjectInterview.DoesNotExist:
            return parameter_error_response('请求参数错误')
        data = request.data.copy()
        serializer = ActionPlanSerializers(data=data, context={'request': request})
        serializer.is_valid(raise_exception=True)
        serializer.save()
        return success_response(serializer.data)

    @swagger_auto_schema(
        operation_id='修改行动计划',
        operation_summary='修改行动计划',
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'action_plan_id': openapi.Schema(type=openapi.TYPE_NUMBER, description='行动计划id'),
                'status': openapi.Schema(type=openapi.TYPE_NUMBER, description='状态 1: 进行中 2:已完成 3:已取消'),
                'content': openapi.Schema(type=openapi.TYPE_STRING, description='内容'),
                'notes': openapi.Schema(type=openapi.TYPE_STRING, description='注释'),
            }
        ),
        tags=['行动计划相关']
    )
    def update(self, request, *args, **kwargs):
        data = request.data.copy()
        instance = self.get_object()
        serializer = self.get_serializer(instance, data=data, partial=True)
        serializer.is_valid(raise_exception=True)
        serializer.save()
        return success_response(serializer.data)
