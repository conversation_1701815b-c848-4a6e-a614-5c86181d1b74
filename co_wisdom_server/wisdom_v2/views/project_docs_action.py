from rest_framework import serializers
from django.db import transaction

from wisdom_v2.common import interview_public
from wisdom_v2.enum.service_content_enum import ProjectCoachStatusEnum
from wisdom_v2.models import ProjectDocs, ProjectMember, Docs, ProjectInterested, ProjectEvaluationReport, ProjectCoach, \
    Project, WorkWechatUser
from wisdom_v2.enum.project_enum import ProjectDocsTypeEnum, ProjectEvaluationReportTypeEnum
from utils.api_response import WisdomValidationError
from utils.messagecenter import getui


class ProjectDocsListSerializer(serializers.ModelSerializer):
    id = serializers.IntegerField(help_text='id')
    file_name = serializers.CharField(source='file.file_name', help_text='报告名称')
    project_member_name = serializers.SerializerMethodField(help_text='被教练者')
    stakeholder_name = serializers.SerializerMethodField(help_text='利益相关者')
    file_path = serializers.CharField(source='file.file_path', help_text='oss地址')

    class Meta:
        model = ProjectDocs
        fields = ['id', 'file_name', 'project_member_name', 'file_name', 'stakeholder_name', 'file_path']

    def get_project_member_name(self, obj):
        if obj.project_member:
            return obj.project_member.user.name
        if obj.project_interested:
            return obj.project_interested.master.name
        return ''

    def get_stakeholder_name(self, obj):
        if obj.project_interested:
            return obj.project_interested.interested.cover_name
        return ''


class ProjectDocsCreateSerializer(serializers.ModelSerializer):
    role = serializers.IntegerField(write_only=True, help_text='角色', required=True)
    file = serializers.ListField(write_only=True, help_text='oss地址列表', required=True)
    project_id = serializers.IntegerField(write_only=True, help_text='项目id', required=True)
    project_member_id = serializers.IntegerField(write_only=True, help_text='被教练者id', required=False)
    project_interested_id = serializers.IntegerField(write_only=True, help_text='利益相关关系id', required=False)

    class Meta:
        model = ProjectDocs
        fields = ['role', 'file', 'project_id', 'project_member_id', 'project_interested_id']

    def create(self, validated_data):
        role = validated_data['role']
        project_id = validated_data['project_id']
        file_list = validated_data['file']
        if role not in [1, 2]:
            raise WisdomValidationError('角色错误')
        # 被教练者
        with transaction.atomic():
            if role == 1:  # 被教练者
                project_member_id = validated_data.get('project_member_id', None)
                project_member = ProjectMember.objects.filter(pk=project_member_id, deleted=False).first()
                project_docs_list = []
                raw_project_members = [project_member]
                # 指定了被教练者
                if project_member_id:
                    for file in file_list:
                        doc = Docs.objects.create(**file)
                        project_docs_dict = {"project_member_id": project_member_id, "file_id": doc.id,
                                             "project_docs_type": ProjectDocsTypeEnum.report.value,
                                             'project_id': project_member.project_id, }
                        project_docs_list.append(ProjectDocs(**project_docs_dict))
                # 当前项目全体被教练者
                else:
                    project_members = ProjectMember.objects.filter(project_id=project_id, deleted=False)
                    for file in file_list:
                        doc = Docs.objects.create(**file)
                        for project_member in project_members:
                            project_docs_dict = {"project_member_id": project_member.id, "file_id": doc.id,
                                                 "project_docs_type": ProjectDocsTypeEnum.report.value,
                                                 'project_id': project_member.project_id, }
                            project_docs_list.append(ProjectDocs(**project_docs_dict))
                    raw_project_members = project_members.all()
                if project_docs_list:
                    ProjectDocs.objects.bulk_create(project_docs_list)

                project = Project.objects.filter(pk=project_id, deleted=False).first()

                for project_member in raw_project_members:
                    project_name = project.name
                    coachee_name = project_member.user.cover_name
                    coachee_id = project_member.user_id

                    coach_data = []
                    formal_interview_query = interview_public.get_formal_interview(
                        None, [project_member.user_id], project_id, None, None)
                    if formal_interview_query.exists():
                        formal_interview = formal_interview_query.last()
                        work_wechat_user = WorkWechatUser.objects.filter(
                            wx_user_id__isnull=False, user_id=formal_interview.public_attr.user_id,
                            deleted=False).first()
                        if work_wechat_user:
                            coach_data.append(
                                [formal_interview.public_attr.user.cover_name, formal_interview.public_attr.user_id, work_wechat_user.wx_user_id])
                    if coach_data:
                        for item in coach_data:
                            coach_name, coach_id, wx_user_id = item
                            content_item = [
                                {"key": "项目名称", "value": project_name},
                                {"key": "客户名称", "value": coachee_name},
                            ]
                            getui.send_work_wechat_coach_notice.delay(
                                wx_user_id,
                                'upload_project_docs_evaluation',
                                content_item=content_item,
                                coachee_name=coachee_name,
                                coachee_id=coachee_id,
                                coach_name=coach_name,
                                coach_id=coach_id
                            )

            else:  # 利益相关者
                project_interested_id = validated_data['project_interested_id']
                project_interested = ProjectInterested.objects.filter(pk=project_interested_id, deleted=False).first()
                project_docs_list = []
                for file in file_list:
                    doc = Docs.objects.create(**file)
                    project_docs_dict = {"project_interested_id": project_interested.id, "file_id": doc.id,
                                         "project_docs_type": ProjectDocsTypeEnum.report.value,
                                         'project_id': project_id}
                    project_docs_list.append(ProjectDocs(**project_docs_dict))
                if project_docs_list:
                    ProjectDocs.objects.bulk_create(project_docs_list)

        return 'success'


class ProjectEvaluationReportSerializer(serializers.ModelSerializer):
    id = serializers.IntegerField(read_only=True)
    lbi_analysis = serializers.JSONField(read_only=True, help_text='报告内容')
    is_push_manager = serializers.IntegerField(read_only=True, help_text='是否推送')
    name = serializers.SerializerMethodField()
    project_id = serializers.IntegerField(read_only=True, help_text='项目id')
    pdf_url = serializers.CharField(read_only=True, help_text='pdf')
    created_at = serializers.DateTimeField(format='%Y-%m-%d %H:%M', read_only=True)
    project_name = serializers.CharField(source='project.name', read_only=True)
    type = serializers.IntegerField(read_only=True)

    def get_name(self, obj):
        if obj.type == ProjectEvaluationReportTypeEnum.LBI_EVALUATION.value:
            return 'LBI领导力行为指数测评项目报告'
        else:
            return '教练型管理者测评项目报告'

    class Meta:
        model = ProjectEvaluationReport
        fields = ['id', 'lbi_analysis', 'is_push_manager', 'name', 'project_id', 'pdf_url', 'created_at',
                  'project_name', 'type']

