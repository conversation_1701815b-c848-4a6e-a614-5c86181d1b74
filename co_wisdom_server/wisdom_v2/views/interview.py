from datetime import datetime
from drf_yasg import openapi
from rest_framework.decorators import action
from django.db.models import Count
from drf_yasg.utils import swagger_auto_schema
from ..models import ProjectInterview, Project, UserAnswer
from .interview_actions import InterviewSerializers, ProjectSerializer, InterviewListSerializers
from rest_framework.viewsets import GenericViewSet

from utils.pagination import StandardResultsSetPagination
from utils.api_response import success_response, parameter_error_response


class InterviewViewSet(GenericViewSet):
    queryset = ProjectInterview.objects.filter(deleted=False).all().order_by('-created_at')
    serializer_class = InterviewSerializers

    @swagger_auto_schema(
        operation_id='约谈项目列表',
        operation_summary='约谈项目列表',
        manual_parameters=[
            openapi.Parameter('user_id', openapi.IN_QUERY, description='用户id', type=openapi.TYPE_NUMBER, required=True)
        ],
        tags=['约谈相关']
    )
    @action(methods=['get'], detail=False)
    def interview_project_list(self, request, *args, **kwargs):

        project_ids = ProjectInterview.objects.filter(public_attr__user_id=request.query_params.get('user_id', 0),
                                                      deleted=False)\
            .values('public_attr__project').annotate(count=Count('public_attr__project')
                                                     ).values_list('public_attr__project', flat=True)
        projects = Project.objects.filter(pk__in=project_ids, deleted=False).order_by('-created_at')

        # 分页
        paginator = StandardResultsSetPagination()
        page_list = paginator.paginate_queryset(projects, self.request)
        serializer = ProjectSerializer(page_list, many=True, context={'queryset': projects})
        response = paginator.get_paginated_response(serializer.data)
        return success_response(response)

    @swagger_auto_schema(
        operation_id='约谈列表',
        operation_summary='约谈列表',
        manual_parameters=[
            openapi.Parameter('user_id', openapi.IN_QUERY, description='用户id', type=openapi.TYPE_NUMBER,
                              required=True),
            openapi.Parameter('project_id', openapi.IN_QUERY, description='项目id', type=openapi.TYPE_NUMBER,
                              required=True),
            openapi.Parameter('property', openapi.IN_QUERY, description='搜索类型 coach_name：教练名 project_name: 项目名',
                              type=openapi.TYPE_STRING, required=False),
            openapi.Parameter('keyword', openapi.IN_QUERY, description='搜索关键字', type=openapi.TYPE_STRING,
                              required=False),
            openapi.Parameter('page', openapi.IN_QUERY, description='第几页', type=openapi.TYPE_NUMBER,
                              required=False),
            openapi.Parameter('page_size', openapi.IN_QUERY, description='每页显示条数默认15条', type=openapi.TYPE_NUMBER,
                              required=False),
        ],
        tags=['约谈相关']
    )
    @action(methods=['get'], detail=False)
    def interview_list(self, request, *args, **kwargs):
        try:
            user_id = int(request.query_params.get('user_id', 0))
            project_id = int(request.query_params.get('project_id', 0))
            property = request.query_params.get('property', None)
            keyword = request.query_params.get('keyword', None)
        except (TypeError, ValueError):
            return parameter_error_response('请求参数错误')
        queryset = self.get_queryset().filter(public_attr__project=project_id, public_attr__user_id=user_id)
        if property and keyword:
            if property == 'coach_name':
                queryset = queryset.filter(public_attr__target_user__name__icontains=keyword)
            if property == 'project_name':
                queryset = queryset.filter(public_attr__project__name__icontains=keyword)

        # 分页
        paginator = StandardResultsSetPagination()
        page_list = paginator.paginate_queryset(queryset, self.request)
        serializer = InterviewListSerializers(page_list, many=True, context={'queryset': queryset})
        response = paginator.get_paginated_response(serializer.data)
        return success_response(response)

    @swagger_auto_schema(
        operation_id='约谈详情',
        operation_summary='约谈详情',
        manual_parameters=[
            openapi.Parameter('interview_id', openapi.IN_QUERY, description='约谈id', type=openapi.TYPE_NUMBER, required=True)
        ],
        tags=['约谈相关']
    )
    @action(methods=['get'], detail=False)
    def detail_info(self, request, *args, **kwargs):
        try:
            instance = ProjectInterview.objects.get(pk=request.query_params.get('interview_id', 0))
        except ProjectInterview.DoesNotExist:
            return parameter_error_response('约谈id错误')
        serializer = self.get_serializer(instance)
        return success_response(serializer.data)

    @swagger_auto_schema(
        operation_id='修改约谈信息',
        operation_summary='修改约谈信息',
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'interview_id': openapi.Schema(type=openapi.TYPE_NUMBER, description='约谈id'),
                'start_time': openapi.Schema(type=openapi.TYPE_STRING, description='约谈开始时间'),
                'end_time': openapi.Schema(type=openapi.TYPE_STRING, description='约谈结束时间'),
                'category': openapi.Schema(type=openapi.TYPE_STRING, description='约谈类型'),
                'answer': openapi.Schema(type=openapi.TYPE_ARRAY,
                                         description='修改约谈记录回答{"answer_id":1 , "answer": "回答内容", "order": 1}',
                                         items=openapi.Schema(type=openapi.TYPE_OBJECT)),
            }
        ),
        tags=['约谈相关']
    )
    @action(methods=['post'], detail=False)
    def interview_update(self, request, *args, **kwargs):
        try:
            interview_id = int(request.data.get('interview_id', 0))
            start_time = request.data.get('start_time', None)
            end_time = request.data.get('end_time', None)
            category = request.data.get('category', None)
            answer = request.data.get('answer', [])
            instance = ProjectInterview.objects.get(pk=interview_id)
        except (TypeError, ValueError, ProjectInterview.DoesNotExist):
            return parameter_error_response('请求参数错误')
        if start_time and end_time:
            instance.public_attr.start_time = datetime.strptime(start_time, '%Y-%m-%d %H:%M')
            instance.public_attr.end_time = datetime.strptime(end_time, '%Y-%m-%d %H:%M')
            instance.public_attr.save()
        if category:
            instance.interview_subject = category
        if answer:
            for item in answer:
                UserAnswer.objects.filter(pk=item['pk'], public_attr=instance.public_attr).update(answer=item['answer'],
                                                                                                  order=item['order'])
        instance.save()
        return success_response()

    @swagger_auto_schema(
        operation_id='取消约谈',
        operation_summary='取消约谈',
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'interview_id': openapi.Schema(type=openapi.TYPE_NUMBER, description='约谈id'),
                'reason': openapi.Schema(type=openapi.TYPE_NUMBER, description='取消原因'),
            }
        ),
        tags=['约谈相关']
    )
    @action(methods=['post'], detail=False)
    def interview_cancel(self, request, *args, **kwargs):
        try:
            interview_id = int(request.data.get('interview_id', 0))
            reason = request.data.get('reason', None)
            instance = ProjectInterview.objects.get(pk=interview_id)
        except (TypeError, ValueError, ProjectInterview.DoesNotExist):
            return parameter_error_response('请求参数错误')
        instance.public_attr.status = 4
        if reason:
            instance.close_reason = reason

        instance.public_attr.save()
        instance.save()
        return success_response()

