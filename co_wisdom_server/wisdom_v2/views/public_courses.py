from datetime import datetime

import pendulum
from django.db.models import Q
from drf_yasg import openapi
from drf_yasg.utils import swagger_auto_schema
from rest_framework.decorators import action
from rest_framework import viewsets

from utils.api_response import success_response, parameter_error_response
from utils.pagination import StandardResultsSetPagination
from wisdom_v2.common.business_order_public import get_object_settlement_status
from wisdom_v2.enum.business_order_enum import BusinessOrderTypeEnum
from wisdom_v2.models_file import PublicCourses
from wisdom_v2.views.public_courses_actions import AdminPublicCoursesDetailSerializer, AdminPublicCoursesListSerializer


class AdminPublicCoursesViewSet(viewsets.ModelViewSet):
    queryset = PublicCourses.objects.filter(deleted=False).order_by('-start_time')
    serializer_class = AdminPublicCoursesDetailSerializer

    @swagger_auto_schema(
        operation_id='获取公开课列表',
        operation_summary='获取公开课列表',
        manual_parameters=[
            openapi.Parameter('status', openapi.IN_QUERY, description='活动状态 1-未开始 2-进行中 3-已结束', type=openapi.TYPE_NUMBER,
                              required=True),
            openapi.Parameter('class_name', openapi.IN_QUERY, description='班级名',
                              type=openapi.TYPE_STRING, required=True),
            openapi.Parameter('coach_name', openapi.IN_QUERY, description='教练名',
                              type=openapi.TYPE_STRING, required=True),
            openapi.Parameter('start_time', openapi.IN_QUERY, description='开始时间',
                              type=openapi.TYPE_STRING, required=True),
            openapi.Parameter('end_time', openapi.IN_QUERY, description='结束时间',
                              type=openapi.TYPE_STRING, required=True),
            openapi.Parameter('type', openapi.IN_QUERY, description='工作形式 1-授课 2-一对一辅导 3-小组辅导 4-口试', type=openapi.TYPE_NUMBER,
                              required=True),
        ],
        tags=['公开课']
    )
    def list(self, request, *args, **kwargs):

        try:
            status = request.query_params.get('status')
            class_name = request.query_params.get('class_name')
            coach_name = request.query_params.get('coach_name')
            start_time = request.query_params.get('start_time')
            end_time = request.query_params.get('end_time')
            public_course_type = request.query_params.get('type')
        except Exception as e:
            return parameter_error_response()
        queryset = self.get_queryset()

        try:
            if start_time:
                start_time = pendulum.parse(start_time)
            if end_time:
                end_time = pendulum.parse(end_time)
        except ValueError:
            return parameter_error_response("无效的时间格式")

        if status:
            current_date = datetime.now().date()
            current_hour = datetime.now().hour

            # 未开始 开始时间>当前时间
            if status == '1':
                queryset = queryset.filter(
                    # 开始日期大于当前日期 或 (开始日期等于当前日期且开始时间的小时大于当前小时)
                    Q(start_time__date__gt=current_date) | (
                                Q(start_time__date=current_date) & Q(start_time__hour__gt=current_hour))
                )
            # 进行中 开始时间<=当前时间，结束时间>=当前时间
            elif status == '2':
                current_date = datetime.now().date()
                current_hour = datetime.now().hour
                queryset = queryset.filter(
                    # 开始日期小于当前日期 或 (开始日期等于当前日期且开始小时小于当前小时)
                    Q(start_time__date__lt=current_date) | (
                            Q(start_time__date=current_date) & Q(start_time__hour__lte=current_hour)),
                    # 结束日期大于当前日期 或 (结束日期等于当前日期且结束小时大于等于当前小时)
                    Q(end_time__date__gt=current_date) | (
                            Q(end_time__date=current_date) & Q(end_time__hour__gt=current_hour))
                )
            # 已结束 结束时间<当前时间
            elif status == '3':
                queryset = queryset.filter(
                    # 结束日期小于当前日期 或 (结束日期等于当前日期且结束时间的小时小于等于当前小时)
                    Q(end_time__date__lt=current_date) | (
                                Q(end_time__date=current_date) & Q(end_time__hour__lte=current_hour))
                )
        if class_name:
            queryset = queryset.filter(class_name__icontains=class_name)
        if coach_name:
            queryset = queryset.filter(
                public_courses_coach__coach__user__true_name__icontains=coach_name, public_courses_coach__deleted=False).distinct()
        if start_time:
            queryset = queryset.filter(
                Q(start_time__date__gt=start_time.date()) | (
                                Q(start_time__date=start_time.date()) & Q(start_time__hour__gt=start_time.hour)))
        if end_time:
            queryset = queryset.filter(Q(end_time__date__lt=end_time.date()) | (
                                Q(end_time__date=end_time.date()) & Q(end_time__hour__lt=end_time.hour)))
        if public_course_type:
            queryset = queryset.filter(type=public_course_type)

        # 分页
        paginator = StandardResultsSetPagination()
        page_list = paginator.paginate_queryset(queryset, self.request)
        serializer = AdminPublicCoursesListSerializer(page_list, many=True)
        response = paginator.get_paginated_response(serializer.data)
        return success_response(response)


    @swagger_auto_schema(
        operation_id='获取公开课详情',
        operation_summary='获取公开课详情',
        manual_parameters=[
            openapi.Parameter('id', openapi.IN_QUERY, description='公开课标识', type=openapi.TYPE_NUMBER,
                              required=True),
        ],
        tags=['公开课']
    )
    @action(methods=['get'], detail=False, url_path='details')
    def get_public_course_detail(self, request, *args, **kwargs):
        try:
            public_course_id = request.query_params.get('id')
            public_course = PublicCourses.objects.get(id=public_course_id, deleted=False)
        except PublicCourses.DoesNotExist:
            return parameter_error_response('公开课不存在')
        except Exception as e:
            return parameter_error_response()
        serializer = self.get_serializer(public_course)
        return success_response(serializer.data)

    @swagger_auto_schema(
        operation_id='创建公开课',
        operation_summary='创建公开课',
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'class_name': openapi.Schema(type=openapi.TYPE_STRING, description='活动主题'),
                'type': openapi.Schema(type=openapi.TYPE_NUMBER, description='公开课类型 1-工作形式1-授课 2-一对一辅导 3-小组辅导 4-口试'),
                'start_time': openapi.Schema(type=openapi.TYPE_STRING, description='公开课开始时间'),
                'end_time': openapi.Schema(type=openapi.TYPE_STRING, description='公开课结束时间'),
                'coach_ids': openapi.Schema(type=openapi.TYPE_STRING, description='教练id列表'),
            }),
        tags=['公开课']
    )
    @action(methods=['post'], detail=False, url_path='add')
    def add_public_course(self, request, *args, **kwargs):
        try:
            class_name = request.data.get('class_name')
            activity_type = request.data.get('type')
            start_time = request.data.get('start_time')
            end_time = request.data.get('end_time')
            coach_ids = request.data.get('coach_ids')
        except Exception as e:
            return parameter_error_response()
        if not coach_ids:
            return parameter_error_response('请选择教练')
        if not class_name:
            return parameter_error_response('请输入班级名称')
        if not activity_type:
            return parameter_error_response('请选择工作类型')
        if not start_time:
            return parameter_error_response('请选择开始时间')
        if not end_time:
            return parameter_error_response('请选择结束时间')
        if start_time > end_time:
            return parameter_error_response('开始时间不能大于结束时间')
        return success_response(super(AdminPublicCoursesViewSet, self).create(request, *args, **kwargs).data)

    @swagger_auto_schema(
        operation_id='修改公开课',
        operation_summary='修改公开课',
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'id': openapi.Schema(type=openapi.TYPE_NUMBER, description='标识'),
                'class_name': openapi.Schema(type=openapi.TYPE_STRING, description='班级名称'),
                'start_time': openapi.Schema(type=openapi.TYPE_STRING, description='公开课开始时间'),
                'end_time': openapi.Schema(type=openapi.TYPE_STRING, description='公开课结束时间'),
                'coach_ids': openapi.Schema(type=openapi.TYPE_STRING, description='教练id列表'),
            }),
        tags=['公开课']
    )
    @action(methods=['post'], detail=False, url_path='update')
    def update_public_course(self, request, *args, **kwargs):
        try:
            public_course_id = request.data.get('id')
            public_course = PublicCourses.objects.get(id=public_course_id, deleted=False)
        except PublicCourses.DoesNotExist:
            return parameter_error_response('公开课不存在')
        except Exception as e:
            return parameter_error_response()

        object_ids = list(public_course.public_courses_coach.filter(deleted=False).values_list('id', flat=True))
        if get_object_settlement_status(object_ids, BusinessOrderTypeEnum.public_course.value, public_course.type):
            return parameter_error_response('已发起提现不可修改')

        self.get_serializer().update(public_course, request.data)
        return success_response(self.get_serializer(public_course).data)
