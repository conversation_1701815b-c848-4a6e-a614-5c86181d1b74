from datetime import datetime

from wisdom_v2.enum.service_content_enum import ScheduleApplyTypeEnum
import pendulum
from django.db.models.functions import TruncDate
from drf_yasg import openapi
from drf_yasg.utils import swagger_auto_schema
from rest_framework.decorators import action
from rest_framework import viewsets

from wisdom_v2.models import Schedule, User, Project
from utils.api_response import success_response, parameter_error_response
from wisdom_v2.views.constant import ATTR_STATUS_INTERVIEW_CANCEL, ATTR_TYPE_INTERVIEW, SCHEDULE_INTERVAL_MINUTES
from wisdom_v2.views.schedule_actions import AdminScheduleSerializer
from wisdom_v2.common import interview_public, schedule_public, stakeholder_interview_public, chemical_interview_public


class AdminScheduleViewSet(viewsets.ModelViewSet):
    queryset = Schedule.objects.filter(deleted=False).exclude(
        public_attr__status=ATTR_STATUS_INTERVIEW_CANCEL, public_attr__type=ATTR_TYPE_INTERVIEW
    ).order_by('public_attr__start_time')
    serializer_class = AdminScheduleSerializer

    @swagger_auto_schema(
        operation_id='获取指定日程间隔是否有数据',
        operation_summary='获取指定日程间隔是否有数据',
        manual_parameters=[
            openapi.Parameter('project_id', openapi.IN_QUERY, description='项目id', type=openapi.TYPE_NUMBER,
                              required=True),
            openapi.Parameter('coachee_user_ids', openapi.IN_QUERY, description='项目用户信息',
                              type=openapi.TYPE_STRING, required=True),
            openapi.Parameter('coach_user_ids', openapi.IN_QUERY, description='教练用户信息', type=openapi.TYPE_STRING,
                              required=True),
            openapi.Parameter('stakeholder_ids', openapi.IN_QUERY, description='利益相关者信息',
                              type=openapi.TYPE_STRING, required=True),
            openapi.Parameter('reservation_status', openapi.IN_QUERY, description='预约状态 1-已预约 2-可预约',
                              type=openapi.TYPE_STRING, required=True),
            openapi.Parameter('start_date', openapi.IN_QUERY, description='开始日期', type=openapi.TYPE_STRING,
                              required=True),
            openapi.Parameter('end_date', openapi.IN_QUERY, description='结束日期', type=openapi.TYPE_STRING,
                              required=True),
        ],
        tags=['用户日历']
    )
    @action(methods=['get'], detail=False, url_path='info')
    def get_schedule_info(self, request, *args, **kwargs):
        try:
            query_data = {k: v for k, v in request.query_params.items() if v and v != ''}
            project_id = query_data.get('project_id')
            coachee_user_ids = query_data.get('coachee_user_ids')
            coach_user_ids = query_data.get('coach_user_ids')
            stakeholder_ids = query_data.get('stakeholder_ids')
            reservation_status = query_data.get('reservation_status')
            start_date_str = query_data.get('start_date')
            end_date_str = query_data.get('end_date')
            Project.objects.get(id=project_id, deleted=False)
        except Project.DoesNotExist:
            return parameter_error_response('项目信息错误')
        except Exception as e:
            return parameter_error_response(str(e))
        try:
            start_date = pendulum.parse(start_date_str)
            end_date = pendulum.parse(end_date_str)
        except ValueError:
            return parameter_error_response("无效的日期格式")

        if start_date > end_date:
            return parameter_error_response("开始日期不应晚于结束日期")

        if not reservation_status:
            return parameter_error_response('预约状态不能为空')
        reservation_status = reservation_status.split(',')

        if coachee_user_ids:
            coachee_user_ids = coachee_user_ids.split(',')
            coachee_user = User.objects.filter(id__in=coachee_user_ids, deleted=False).all()
            if not coachee_user:
                return parameter_error_response('项目用户信息错误')
        else:
            coachee_user = None

        if stakeholder_ids:
            stakeholder_ids = stakeholder_ids.split(',')
            stakeholder_user = User.objects.filter(id__in=stakeholder_ids, deleted=False).all()
            if not stakeholder_user:
                return parameter_error_response('利益相关者信息错误')
        else:
            stakeholder_user = None

        if coach_user_ids:
            coach_user_ids = coach_user_ids.split(',')
            coach_user = User.objects.filter(id__in=coach_user_ids, deleted=False).all()
            if not coach_user:
                return parameter_error_response('教练用户信息错误')
        else:
            coach_user = None

        data = []
        all_date = []
        if '1' in reservation_status:
            # 利益相关者调研
            all_date += stakeholder_interview_public.get_stakeholder_interview(
                coach_user, coachee_user, stakeholder_user, project_id, start_date_str, end_date_str).annotate(
                date_only=TruncDate('interview__public_attr__start_time')).values_list('date_only', flat=True).distinct()

            if not stakeholder_ids:
                # 一对一辅导
                all_date += interview_public.get_formal_interview(
                    coach_user, coachee_user, project_id, start_date_str, end_date_str).annotate(
                    date_only=TruncDate('public_attr__start_time')).values_list('date_only', flat=True).distinct()
                # 化学面谈
                all_date += chemical_interview_public.get_chemical_interview(
                    coach_user, coachee_user, project_id, start_date_str, end_date_str).annotate(
                    date_only=TruncDate('interview__public_attr__start_time')).values_list('date_only', flat=True).distinct()
                # 线下集体辅导
                all_date += interview_public.get_offline_group_coach(
                    coach_user, coachee_user, project_id, start_date_str, end_date_str).annotate(
                    date_only=TruncDate('public_attr__start_time')).values_list('date_only', flat=True).distinct()
        period = pendulum.period(start_date, end_date)
        now_date = pendulum.now().date()

        for dt in period.range('days'):
            is_schedule = False

            if dt.date() in all_date:
                is_schedule = True
            else:
                if '2' in reservation_status:
                    # 当前时间大于等于开始时间
                    if dt.date() >= now_date:
                        # 有没有可预约日程
                        for c in coach_user:
                            time_slots = schedule_public.get_schedule_day_list(c.id, dt, apply_type=ScheduleApplyTypeEnum.project.value)
                            if True in time_slots:
                                is_schedule = True
                                break
            day_data = {
                'date': dt.to_date_string(),
                'is_schedule': is_schedule,
            }
            data.append(day_data)
        return success_response(data)

    @swagger_auto_schema(
        operation_id='获取日程详情数据',
        operation_summary='获取日程详情数据',
        manual_parameters=[
            openapi.Parameter('project_id', openapi.IN_QUERY, description='项目id', type=openapi.TYPE_NUMBER,
                              required=True),
            openapi.Parameter('coachee_user_ids', openapi.IN_QUERY, description='项目用户信息',
                              type=openapi.TYPE_STRING, required=True),
            openapi.Parameter('coach_user_ids', openapi.IN_QUERY, description='教练用户信息', type=openapi.TYPE_STRING,
                              required=True),
            openapi.Parameter('stakeholder_ids', openapi.IN_QUERY, description='利益相关者信息',
                              type=openapi.TYPE_STRING, required=True),
            openapi.Parameter('reservation_status', openapi.IN_QUERY, description='预约状态 1-已预约 2-可预约',
                              type=openapi.TYPE_STRING, required=True),
            openapi.Parameter('start_date', openapi.IN_QUERY, description='开始日期', type=openapi.TYPE_STRING,
                              required=True),
            openapi.Parameter('end_date', openapi.IN_QUERY, description='结束日期', type=openapi.TYPE_STRING,
                              required=True),
        ],
        tags=['用户日历']
    )
    @action(methods=['get'], detail=False, url_path='details')
    def get_schedule_details(self, request, *args, **kwargs):
        try:
            query_data = {k: v for k, v in request.query_params.items() if v and v != ''}
            project_id = query_data.get('project_id')
            coachee_user_ids = query_data.get('coachee_user_ids')
            coach_user_ids = query_data.get('coach_user_ids')
            stakeholder_ids = query_data.get('stakeholder_ids')
            reservation_status = query_data.get('reservation_status')
            start_date_str = query_data.get('start_date')
            end_date_str = query_data.get('end_date')
            Project.objects.get(id=project_id, deleted=False)
        except Project.DoesNotExist:
            return parameter_error_response('项目信息错误')
        except Exception as e:
            return parameter_error_response(str(e))

        try:
            start_date = pendulum.parse(start_date_str)
            end_date = pendulum.parse(end_date_str)
        except ValueError:
            return parameter_error_response("无效的日期格式")

        if start_date > end_date:
            return parameter_error_response("开始日期不应晚于结束日期")

        if not reservation_status:
            return parameter_error_response('预约状态不能为空')
        reservation_status = reservation_status.split(',')

        if coachee_user_ids:
            coachee_user_ids = coachee_user_ids.split(',')
            coachee_user = User.objects.filter(id__in=coachee_user_ids, deleted=False).all()
            if not coachee_user:
                return parameter_error_response('项目用户信息错误')
        else:
            coachee_user = None

        if stakeholder_ids:
            stakeholder_ids = stakeholder_ids.split(',')
            stakeholder_user = User.objects.filter(id__in=stakeholder_ids, deleted=False).all()
            if not stakeholder_user:
                return parameter_error_response('利益相关者信息错误')
        else:
            stakeholder_user = None

        if coach_user_ids:
            coach_user_ids = coach_user_ids.split(',')
            coach_user = User.objects.filter(id__in=coach_user_ids, deleted=False).all()
            if not coach_user:
                return parameter_error_response('教练用户信息错误')
        else:
            coach_user = None

        data = []
        # 查询已预约
        if '1' in reservation_status:
            data = interview_public.get_admin_interview_schedules(
                coach_user, coachee_user, stakeholder_user, project_id, start_date_str, end_date_str)

        # 查询可预约
        if '2' in reservation_status:
            start_date = pendulum.now()
            period = pendulum.period(start_date, end_date)
            for dt in period.range('days'):
                # 有没有可预约日程
                for c in coach_user:
                    time_slots = schedule_public.get_schedule_day_list(c.id, dt, apply_type=ScheduleApplyTypeEnum.project.value)
                    raw_schedule_data = schedule_public.get_schedule_intervals_detailed(
                        time_slots, datetime(dt.year, dt.month, dt.day), SCHEDULE_INTERVAL_MINUTES)

                    for schedule_data in raw_schedule_data:
                        schedule_data['type'] = 5  # 可预约日程
                        schedule_data['details']['title'] = '可预约日程'
                        schedule_data['details']['coach_name'] = c.true_name
                    data += raw_schedule_data

        # 根据data中的start字段从小到大排序
        # start是字符串
        data = sorted(data, key=lambda x: x['start'])
        return success_response(data)
