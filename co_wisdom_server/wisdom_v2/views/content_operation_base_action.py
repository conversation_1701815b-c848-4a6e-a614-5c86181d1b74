from rest_framework import serializers

from wisdom_v2.models import ContentOperationBase


class ContentOperationBaseSerializers(serializers.ModelSerializer):
    active_users = serializers.SerializerMethodField()
    use_time = serializers.SerializerMethodField()
    date = serializers.SerializerMethodField()

    class Meta:
        model = ContentOperationBase
        fields = ['active_users', 'use_time', 'date']

    def get_active_users(self, obj):
        if not isinstance(obj.id, dict):
            return obj.__dict__.get('active_users')
        else:
            return obj.__dict__.get('id').get('active_users')

    def get_date(self, obj):
        if not isinstance(obj.id, dict):
            return obj.__dict__.get('date')
        else:
            return obj.__dict__.get('id').get('date')

    def get_use_time(self, obj):
        if not isinstance(obj.id, dict):
            use_time = [
                *obj.android_use_time.items(),
                *obj.ios_use_time.items(),
                *obj.applets_use_time.items()]
        else:
            use_time = [
                *obj.id.get('android_use_time').items(),
                *obj.id.get('ios_use_time').items(),
                *obj.id.get('applets_use_time').items()]
        raw_use_time = []
        for k, v in use_time:
            raw_use_time += [int(k) for x in range(v)]
        if raw_use_time:
            size = len(raw_use_time)
            raw_use_time.sort()
            if size % 2 == 0:
                median = int((raw_use_time[size // 2] + raw_use_time[size // 2 - 1]) / 2 + 0.5)
            else:
                median = raw_use_time[(size - 1) // 2]
        else:
            median = 0
        return median
