
from rest_framework import serializers

from utils.api_response import WisdomValidationError
from utils.multiple_selection_map import get_multiple_selection_detail
from wisdom_v2.common import customer_portrait_public
from wisdom_v2.enum.service_content_enum import PortraitInterviewTypeEnum, CustomerPortraitTypeEnum
from wisdom_v2.models import CustomerPortrait, User, CompanyMember


# 数据创建/更新序列化
class CustomerPortraitBaseSerializers(serializers.ModelSerializer):
    project_id = serializers.IntegerField(help_text='项目id', required=False, allow_null=True)
    user_id = serializers.IntegerField(help_text='用户id', required=False, allow_null=True)
    gender = serializers.IntegerField(help_text='性别', required=False, allow_null=True)
    customer_name = serializers.CharField(help_text='客户姓名', required=False, allow_null=True, allow_blank=True)
    customer_position = serializers.Char<PERSON>ield(required=False, help_text='客户职位', allow_null=True, allow_blank=True)
    customer_age = serializers.CharField(required=False, help_text='客户年龄', allow_null=True, allow_blank=True)
    customer_style = serializers.CharField(required=False, help_text='客户风格及特点', allow_null=True, allow_blank=True)
    customer_experience = serializers.CharField(required=False, help_text='客户管理经验', allow_null=True, allow_blank=True)
    type = serializers.IntegerField(required=False, help_text='客户类型 1-个人画像 2-群体画像', allow_null=True)
    city = serializers.CharField(required=False, help_text='所在城市', allow_null=True, allow_blank=True)
    group_features = serializers.CharField(required=False, help_text='群体特点', allow_null=True, allow_blank=True)
    challenge = serializers.CharField(required=False, help_text='当前面临的挑战', allow_null=True, allow_blank=True)
    coach_expect = serializers.CharField(required=False, help_text='对教练认识和期待', allow_null=True, allow_blank=True)
    language = serializers.ListField(required=False, help_text='语言', allow_null=True)
    coach_experience = serializers.CharField(required=False, help_text='教练经验', allow_null=True, allow_blank=True)
    interview_type = serializers.IntegerField(required=False, help_text='教练类型 1-线上 2-线下 3-线上+线下', allow_null=True)
    coach_ability = serializers.CharField(required=False, help_text='教练能力', allow_null=True, allow_blank=True)
    coach_style = serializers.CharField(required=False, help_text='教练风格及特点', allow_null=True, allow_blank=True)
    coach_extra = serializers.CharField(required=False, help_text='可供教练参考的其他信息', allow_null=True, allow_blank=True)
    group_target = serializers.CharField(required=False, help_text='组织目标', allow_null=True, allow_blank=True)
    personal_target = serializers.CharField(required=False, help_text='教练目的', allow_null=True, allow_blank=True)
    expected_interview_start_time = serializers.DateField(required=False, help_text='预约化学面谈开始时间', allow_null=True)
    expected_interview_end_time = serializers.DateField(required=False, help_text='预约化学面谈结束时间', allow_null=True)

    class Meta:
        model = CustomerPortrait
        exclude = ('created_at', 'updated_at', 'user')

    def update(self, instance, validated_data):
        super().update(instance=instance, validated_data=validated_data)
        return instance

    def create(self, validated_data):
        if not validated_data.get('project_id'):
            raise WisdomValidationError('未获取到项目id信息')
        error = customer_portrait_public.customer_portrait_data_check(validated_data)
        if error:
            raise WisdomValidationError(error)

        # 群体画像同项目下只可创建一个
        if validated_data.get('type') == CustomerPortraitTypeEnum.group:
            customer_portrait = CustomerPortrait.objects.filter(
                deleted=False,
                project_id=validated_data.get('project_id'),
                type=CustomerPortraitTypeEnum.group
            ).exists()
            if customer_portrait:
                raise WisdomValidationError('群体画像同项目下只可创建一个')

        instance = CustomerPortrait(**validated_data)
        instance.save()
        return instance


# 数据响应序列化
class CustomerPortraitDetailsSerializers(serializers.ModelSerializer):
    project_id = serializers.IntegerField(help_text='项目id')
    customer_name = serializers.SerializerMethodField()
    customer_position = serializers.SerializerMethodField()
    customer_age = serializers.SerializerMethodField()
    language = serializers.SerializerMethodField()
    user_id = serializers.SerializerMethodField()
    expected_interview_start_time = serializers.SerializerMethodField(help_text='预约化学面谈开始时间')
    expected_interview_end_time = serializers.SerializerMethodField(help_text='预约化学面结束始时间')

    def get_expected_interview_start_time(self, obj):
        if obj.expected_interview_start_time:
            return obj.expected_interview_start_time
        return

    def get_expected_interview_end_time(self, obj):
        if obj.expected_interview_end_time:
            return obj.expected_interview_end_time
        return

    def get_user_id(self, obj):
        return obj.user_id

    def get_language(self, obj):
        return get_multiple_selection_detail('language', obj.language)

    def get_customer_name(self, obj):
        if obj.user_id:
            return obj.user.cover_name
        return obj.customer_name

    def get_customer_age(self, obj):
        if obj.user_id:
            return obj.user.age if obj.user.age else obj.customer_age
        return obj.customer_age

    def get_customer_position(self, obj):
        if obj.user_id:
            company_member = CompanyMember.objects.filter(
                user_id=obj.user_id,
                company=obj.project.company
            ).first()
            if company_member:
                return company_member.position
        return obj.customer_position

    class Meta:
        model = CustomerPortrait
        exclude = ('created_at', 'updated_at', 'user', 'project')
