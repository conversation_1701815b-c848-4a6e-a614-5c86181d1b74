import datetime
import json
import redis
import TLSSigAPIv2
from drf_yasg import openapi
from drf_yasg.utils import swagger_auto_schema
from rest_framework.decorators import action
from django.db.models import Q
from django.conf import settings

from rest_framework import viewsets

from utils.queryset import multiple_field_distinct
from wisdom_v2.models import User, User2Project, UserBackend, Coach, ProjectMember, \
    FeedBack, ProjectInterview, PersonalUser
from .constant import BIZ_CODE_USER_DISABLE_LOGIN

from .user_actions import update_user_pwd, FeedBackSerializers

from utils.pagination import StandardResultsSetPagination
from utils.api_response import success_response, parameter_error_response, biz_error_response
from utils import aesencrypt
from utils.authentication import get_token
from wisdom_v2.views.userinfo_actions import UserInfoSerializer
from wisdom_v2.views.user_actions import UserSerializer, ProjectUserListSerializer
from utils.rtc_utils.rtc_token import get_rtc_token
from wisdom_v2.common import user_public, coachee_public
from wisdom_v2.enum.user_enum import UserRoleEnum

code_redis = redis.Redis.from_url('redis://127.0.0.1:6379/3')


class UserViewSet(viewsets.ModelViewSet):
    queryset = User.objects.filter(deleted=False).order_by('-created_at')
    serializer_class = UserSerializer

    @swagger_auto_schema(
        operation_id='用户列表',
        operation_summary='用户列表',
        manual_parameters=[
            openapi.Parameter('page', openapi.IN_QUERY, description='第几页', type=openapi.TYPE_NUMBER),
            openapi.Parameter('page_size', openapi.IN_QUERY, description='每页显示条数默认15条', type=openapi.TYPE_NUMBER)
        ],
        tags=['用户相关']
    )
    def list(self, request, *args, **kwargs):
        queryset = self.get_queryset()

        # 分页
        paginator = StandardResultsSetPagination()
        page_list = paginator.paginate_queryset(queryset, self.request)
        serializer = self.serializer_class(page_list, many=True, context={'queryset': queryset})
        response = paginator.get_paginated_response(serializer.data)
        return success_response(response)

    @swagger_auto_schema(
        operation_id='创建用户',
        operation_summary='创建用户',
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'name': openapi.Schema(type=openapi.TYPE_STRING, description='用户名'),
                'true_name': openapi.Schema(type=openapi.TYPE_STRING, description='用户真名'),
                'password': openapi.Schema(type=openapi.TYPE_STRING, description='用户密码'),
                'phone': openapi.Schema(type=openapi.TYPE_STRING, description='用户手机号'),
                'gender': openapi.Schema(type=openapi.TYPE_NUMBER, description='性别1: 男 2:女'),
                'email': openapi.Schema(type=openapi.TYPE_STRING, description='邮箱'),
                'head_image_url': openapi.Schema(type=openapi.TYPE_STRING, description='头像地址'),
                'age': openapi.Schema(type=openapi.TYPE_NUMBER, description='年龄'),
                'birthday': openapi.Schema(type=openapi.TYPE_STRING, description='生日'),
                'message_id': openapi.Schema(type=openapi.TYPE_STRING, description='IM ID'),
                'message_pwd': openapi.Schema(type=openapi.TYPE_STRING, description='IM 密码'),
                'send_email': openapi.Schema(type=openapi.TYPE_BOOLEAN, description='是否发送邮件'),
                'send_msg': openapi.Schema(type=openapi.TYPE_BOOLEAN, description='是否发送短信'),
                'user_type': openapi.Schema(type=openapi.TYPE_NUMBER,
                                            description='1:普通用户 2:网站管理员 3:项目管理员（只有网站管理员/项目管理员才能新建项目）'),
            }
        ),
        tags=['用户相关']
    )
    def create(self, request, *args, **kwargs):
        return success_response(super(UserViewSet, self).create(request, *args, **kwargs).data)

    @swagger_auto_schema(
        operation_id='用户详情',
        operation_summary='用户详情',
        manual_parameters=[
        ],
        tags=['用户相关']
    )
    def retrieve(self, request, *args, **kwargs):
        instance = self.get_object()
        serializer = self.get_serializer(instance)
        return success_response(serializer.data)

    @swagger_auto_schema(
        operation_id='修改用户',
        operation_summary='修改用户',
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'name': openapi.Schema(type=openapi.TYPE_STRING, description='用户名'),
                'true_name': openapi.Schema(type=openapi.TYPE_STRING, description='用户真名'),
                'phone': openapi.Schema(type=openapi.TYPE_STRING, description='用户手机号'),
                'gender': openapi.Schema(type=openapi.TYPE_NUMBER, description='性别1: 男 2:女'),
                'email': openapi.Schema(type=openapi.TYPE_STRING, description='邮箱'),
                'head_image_url': openapi.Schema(type=openapi.TYPE_STRING, description='头像地址'),
                'age': openapi.Schema(type=openapi.TYPE_NUMBER, description='年龄'),
                'birthday': openapi.Schema(type=openapi.TYPE_STRING, description='生日'),
                'message_id': openapi.Schema(type=openapi.TYPE_STRING, description='IM 密码'),
                'message_pwd': openapi.Schema(type=openapi.TYPE_STRING, description='IM 密码'),
                'send_email': openapi.Schema(type=openapi.TYPE_BOOLEAN, description='是否发送邮件'),
                'send_msg': openapi.Schema(type=openapi.TYPE_BOOLEAN, description='是否发送短信'),
                'user_type': openapi.Schema(type=openapi.TYPE_NUMBER,
                                            description='1:普通用户 2:网站管理员 3:项目管理员（只有网站管理员/项目管理员才能新建项目）'),
                'deleted': openapi.Schema(type=openapi.TYPE_BOOLEAN, description='设置删除'),
            }
        ),
        tags=['用户相关']
    )
    def update(self, request, *args, **kwargs):
        data = request.data.copy()
        instance = self.get_object()
        serializer = self.get_serializer(instance, data=data, partial=True)
        serializer.is_valid(raise_exception=True)
        serializer.save()
        return success_response(serializer.data)

    @swagger_auto_schema(
        operation_id='用户登录',
        operation_summary='用户登录',
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'name': openapi.Schema(type=openapi.TYPE_STRING, description='用户名/手机号/邮箱'),
                'password': openapi.Schema(type=openapi.TYPE_STRING, description='用户密码'),
            }
        ),
        tags=['用户相关']
    )
    @action(methods=['post'], detail=False, authentication_classes=[])
    def login(self, request, *args, **kwargs):
        try:
            name = request.data['name']
            password = request.data['password']
            encrypted_password = aesencrypt(password)
        except KeyError:
            return parameter_error_response('请输入用户名或密码')
        except ValueError:
            return parameter_error_response('请检查密码格式')

        user = User.objects.filter(Q(name=name) | Q(email=name) | Q(phone=name), deleted=False)
        # 验证用户是否存在，密码是否正确
        if not user.exists():
            return parameter_error_response('账户不存在，请仔细检查')
        # 验证密码
        if encrypted_password != user.first().password:
            return parameter_error_response('用户名或密码错误，请重新输入')
        user_obj = user[0]
        status = coachee_public.user_is_login_allowed(user_obj.id)
        if not status:
            return biz_error_response(
                BIZ_CODE_USER_DISABLE_LOGIN,
                '您的企业已关闭该服务。如果您仍需要教练服务，可以通过个人账号登录预约更多教练。'
            )
        cid = request.headers.get('Cid', None)
        if cid:
            same_cid_user = User.objects.filter(user_cid=cid).first()
            if same_cid_user:
                same_cid_user.user_cid = None
                same_cid_user.save()
            user_obj.user_cid = cid
        token = get_token(user_obj, is_backend=True)
        user_data = UserSerializer(user_obj).data
        # 角色处理
        # 1. 排除个人用户角色。个人用户通过微信授权接口登录
        # 2. 微信中排除教练角色
        # 3. 企业微信中排除客户角色
        app_type = request.headers.get('App-Type', '')

        if app_type == 'WeChat':
            role_exclude = [UserRoleEnum.trainee_coachee.value, UserRoleEnum.coach.value, UserRoleEnum.trainee_coach.value]
        elif app_type == 'wxwork':
            role_exclude = [UserRoleEnum.trainee_coachee.value, UserRoleEnum.coachee.value]
        else:
            role_exclude = [UserRoleEnum.trainee_coachee.value]

        user_role = user_public.del_specified_user_permissions(
            user_data.get('role'), role_exclude)

        if not user_role:
            return success_response({'status': 1, 'msg': '当前用户无登录权限'})
        user_data['role'] = user_role
        user_obj.last_active = datetime.datetime.now()
        user_obj.save()

        # 当前用户没有手机号，并且没有其他用户使用该手机号，则返回绑定手机号字段
        # 绑定之前不知道手机号是多少，所以用openid判断客户有没有注册过个人账号
        user_data['bind_phone'] = False
        if not user_obj.phone:
            if User.objects.filter(openid=user_obj.openid, phone__isnull=False).exists():
                alternative_user = User.objects.filter(openid=user_obj.openid, phone__isnull=False).first()
                user_data['phone'] = alternative_user.phone
            else:
                user_data['bind_phone'] = True
        return success_response({'token': token, 'user': user_data})

    @swagger_auto_schema(
        operation_id='后台用户登录',
        operation_summary='后台用户登录',
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'name': openapi.Schema(type=openapi.TYPE_STRING, description='用户名/手机号/邮箱'),
                'password': openapi.Schema(type=openapi.TYPE_STRING, description='用户密码'),
            }
        ),
        tags=['用户相关']
    )
    @action(methods=['post'], detail=False, authentication_classes=[])
    def backend_login(self, request, *args, **kwargs):
        try:
            name = request.data['name']
            password = request.data['password']
            aesencrypt(password)
        except KeyError:
            return parameter_error_response('请输入用户名或密码')
        except ValueError:
            return parameter_error_response('请检查密码格式')

        user_backend = UserBackend.objects.filter(Q(user__name=name) | Q(user__email=name) | Q(user__phone=name),
                                                  deleted=False)
        # 验证用户是否存在，密码是否正确
        if not user_backend.exists():
            return parameter_error_response('账户不存在，请仔细检查')
        user = user_backend.first().user
        if aesencrypt(password) != user.password:
            return parameter_error_response('用户名或密码错误，请重新输入')
        cid = request.headers.get('Cid', None)
        if cid:
            same_cid_user = User.objects.filter(user_cid=cid).first()
            if same_cid_user:
                same_cid_user.user_cid = None
                same_cid_user.save()
            user.user_cid = cid
        token = get_token(user, is_backend=True)
        user.last_active = datetime.datetime.now()
        user.save()
        return success_response({'token': token, 'user': UserSerializer(user).data})

    @swagger_auto_schema(
        operation_id='用户修改密码',
        operation_summary='用户修改密码',
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'user_id': openapi.Schema(type=openapi.TYPE_INTEGER, description='用户id'),
                'password': openapi.Schema(type=openapi.TYPE_STRING, description='用户密码'),
                'new_password': openapi.Schema(type=openapi.TYPE_STRING, description='用户新密码'),
                'confirm_password': openapi.Schema(type=openapi.TYPE_STRING, description='确认密码'),
            }
        ),
        tags=['用户相关']
    )
    @action(methods=['post'], detail=False)
    def modify_pwd(self, request, *args, **kwargs):
        try:
            user_id = request.data['user_id']
            password = request.data['password']
            new_password = request.data['new_password']
            confirm_password = request.data['confirm_password']
            aesencrypt(password)
            aesencrypt(new_password)
            aesencrypt(confirm_password)
            user = User.objects.get(pk=user_id, deleted=False)
        except KeyError:
            return parameter_error_response()
        except ValueError:
            return parameter_error_response('请检查输入密码格式')
        except User.DoesNotExist:
            return parameter_error_response('用户id错误')
        if confirm_password != new_password:
            return parameter_error_response('两次输入的新密码不一致')

        # 验证用户是否存在，密码是否正确
        if aesencrypt(password) != user.password:
            return parameter_error_response('用户原密码错误')
        update_user_pwd(user, new_password)
        return success_response('修改密码成功')

    @swagger_auto_schema(
        operation_id='用户信息',
        operation_summary='用户信息',
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'user_id': openapi.Schema(type=openapi.TYPE_INTEGER, description='用户id')
            }
        ),
        tags=['用户相关']
    )
    @action(methods=['post'], detail=False)
    def userinfo(self, request, *args, **kwargs):
        try:
            uid = request.data.get('user_id')
            user = User.objects.get(pk=uid, deleted=False)
        except (KeyError, ValueError):
            return parameter_error_response('用户编号错误')

        return success_response({'userinfo': UserInfoSerializer(user).data})

    @swagger_auto_schema(
        operation_id='二要素认证用用户信息',
        operation_summary='二要素认证用用户信息',
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'user_id': openapi.Schema(type=openapi.TYPE_INTEGER, description='用户id')
            }
        ),
        tags=['用户相关']
    )
    @action(methods=['post'], detail=False, url_path='two_cert_user_info')
    def two_cert_user_info(self, request, *args, **kwargs):
        try:
            uid = request.data.get('user_id')
            user = User.objects.get(pk=uid, deleted=False)
        except (KeyError, ValueError):
            return parameter_error_response('用户编号错误')
        token = get_token(user, is_backend=True)
        return success_response({'token': token, 'user': UserSerializer(user).data})

    @swagger_auto_schema(
        operation_id='用户设置密码',
        operation_summary='用户设置密码',
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'phone': openapi.Schema(type=openapi.TYPE_STRING, description='用户手机号'),
                'email': openapi.Schema(type=openapi.TYPE_STRING, description='用户邮箱'),
                'code': openapi.Schema(type=openapi.TYPE_NUMBER, description='验证码'),
                'new_password': openapi.Schema(type=openapi.TYPE_STRING, description='用户新密码'),
                'confirm_password': openapi.Schema(type=openapi.TYPE_STRING, description='确认密码'),
            }
        ),
        tags=['用户相关']
    )
    @action(methods=['post'], detail=False, authentication_classes=[])
    def set_pwd(self, request, *args, **kwargs):
        try:
            phone = request.data.get('phone')
            email = request.data.get('email')
            code = int(request.data.get('code', None))
            new_password = request.data.get('new_password')
            confirm_password = request.data.get('confirm_password')
            if phone:
                user = User.objects.get(phone=phone, deleted=False)
            elif email:
                user = User.objects.get(email=email, deleted=False)
            else:
                return parameter_error_response()

        except (KeyError, ValueError):
            return parameter_error_response()
        except User.DoesNotExist:
            return parameter_error_response('用户手机号错误')

        if phone:
            values = code_redis.get('code_' + str(phone))

            if not values:
                return parameter_error_response('请先获取验证码')

            values_dict = json.loads(values)

            if code != int(values_dict['sms_code']):
                return parameter_error_response('验证码错误，请输入最新发送的验证码')

        elif email:
            email_redis_key = 'code_' + email + '_' + str(code)
            if not code_redis.get(email_redis_key):
                return parameter_error_response('验证码错误，请重新输入')
        else:
            return parameter_error_response()

        if confirm_password != new_password:
            return parameter_error_response('两次输入的密码不一致')

        update_user_pwd(user, new_password)
        return success_response('设置成功')


    @swagger_auto_schema(
        operation_id='添加项目中关联项目顾问列表',
        operation_summary='添加项目中关联项目顾问列表',
        manual_parameters=[
            openapi.Parameter('page', openapi.IN_QUERY, description='第几页', type=openapi.TYPE_NUMBER),
            openapi.Parameter('page_size', openapi.IN_QUERY, description='每页显示条数默认15条', type=openapi.TYPE_NUMBER),
            openapi.Parameter('keyword', openapi.IN_QUERY, description='关键字（姓名）', type=openapi.TYPE_STRING),
            openapi.Parameter('project_id', openapi.IN_QUERY, description='项目id(编辑项目顾问列表必传)', type=openapi.TYPE_NUMBER)

        ],
        tags=['用户相关']
    )
    @action(methods=['get'], detail=False, url_path='project_user_list', serializer_class=ProjectUserListSerializer)
    def project_user_list(self, request, *args, **kwargs):
        queryset = self.get_queryset().filter(deleted=False).order_by('-id')
        if request.query_params.get('keyword', None):
            queryset = queryset.filter(true_name__icontains=request.query_params.get('keyword'))

        # 分页
        paginator = StandardResultsSetPagination()
        page_list = paginator.paginate_queryset(queryset, self.request)
        serializer = self.serializer_class(page_list, many=True, context={'queryset': queryset})
        # 替换是否关联值
        if request.query_params.get('project_id', None):
            user_ids = User2Project.objects.filter(project_id=request.query_params.get('project_id', None), deleted=False
                                                  ).values_list('user_id')
            if user_ids:
                user_ids = [i[0] for i in user_ids]
                for data in serializer.data:
                    if data['id'] in user_ids:
                        data['is_relevance'] = 1

        response = paginator.get_paginated_response(serializer.data)
        return success_response(response)

    @swagger_auto_schema(
        operation_id='用户姓名查询列表',
        operation_summary='用户姓名查询列表',
        manual_parameters=[
            openapi.Parameter('type', openapi.IN_QUERY, description='用户类型 1-教练 2-客户', type=openapi.TYPE_NUMBER),
            openapi.Parameter('name', openapi.IN_QUERY, description='用户姓名(必传)', type=openapi.TYPE_NUMBER),
        ],
        tags=['用户相关']
    )
    @action(methods=['get'], detail=False, url_path='user_info', serializer_class=ProjectUserListSerializer)
    def get_user_info_list(self, request, *args, **kwargs):
        user_type = request.query_params.get('type', 1)
        name = request.query_params.get('name')
        if not name:
            return parameter_error_response('未获取到用户姓名参数')

        if int(user_type) == 1:
            raw_coach = Coach.objects.filter(user__true_name__icontains=name).all()
            data = []
            for item in raw_coach:
                data.append({
                    'id': item.user.id,
                    'name': item.user.cover_name,
                })
        else:
            raw_user = ProjectMember.objects.filter(user__true_name__icontains=name, deleted=False).all() or\
                       PersonalUser.objects.filter(user__true_name__icontains=name).all()
            data = []

            raw_user = multiple_field_distinct(raw_user, ['user_id'])
            for item in raw_user:
                data.append({
                    'id': item.user.id,
                    'name': item.user.cover_name,
                })
        return success_response(data)

    @swagger_auto_schema(
        operation_id='意见反馈列表',
        operation_summary='意见反馈列表',
        manual_parameters=[
            openapi.Parameter('page', openapi.IN_QUERY, description='第几页', type=openapi.TYPE_NUMBER),
            openapi.Parameter('page_size', openapi.IN_QUERY, description='每页显示条数默认15条', type=openapi.TYPE_NUMBER)
        ],
        tags=['用户相关']
    )
    @action(methods=['get'], detail=False, url_path='feedback', serializer_class=FeedBackSerializers)
    def get_user_feedback(self, request, *args, **kwargs):

        feed_back = FeedBack.objects.filter(deleted=False).order_by('-created_at')
        # 分页
        paginator = StandardResultsSetPagination()
        page_list = paginator.paginate_queryset(feed_back, self.request)
        serializer = self.get_serializer(page_list, many=True)
        response = paginator.get_paginated_response(serializer.data)
        return success_response(response)

    @swagger_auto_schema(
        operation_id='腾讯云获取后台计算UserSig',
        operation_summary='腾讯云获取后台计算UserSig',
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'user_id': openapi.Schema(type=openapi.TYPE_STRING, description='用户id'),
            }
        ),
        tags=['用户相关']
    )
    @action(methods=['post'], detail=False)
    def get_user_sig(self, request, *args, **kwargs):
        try:
            user_id = request.data['user_id']
            interview_id = request.data['interview_id']
            project_interview = ProjectInterview.objects.get(pk=interview_id)
        except:
            return parameter_error_response()
        api = TLSSigAPIv2.TLSSigAPIv2(settings.SDK_APP_ID, settings.SDK_APP_SECRET)
        sig = api.gen_sig(user_id)
        results = {"user_sig": sig}
        res = get_rtc_token(str(project_interview.public_attr_id))
        results['channel_id'] = res['channel_name']
        return success_response(results)