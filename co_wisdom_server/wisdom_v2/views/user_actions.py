from django.contrib.auth import authenticate, login

from wisdom_v2.common import user_public
from wisdom_v2.models import User, ProjectMember, ProjectCoach, WorkWechatUser, \
    Coach, PersonalUser, FeedBack
from utils import aesencrypt
from data.models import SysUser
from rest_framework import serializers
from wisdom_v2.enum.user_enum import User<PERSON><PERSON><PERSON>num, CoachUserTypeEnum

from wisdom_v2.enum.project_member_enum import ProjectMemberRoleEnum


class UserSerializer(serializers.ModelSerializer):
    birthday = serializers.DateField(format='%Y-%m-%d', read_only=True)
    last_active = serializers.DateTimeField(format='%Y-%m-%d %H:%M', read_only=True)
    user_role = serializers.SerializerMethodField(read_only=True)
    role = serializers.SerializerMethodField(read_only=True)
    true_name = serializers.SerializerMethodField(help_text='用户名')
    is_revise_date = serializers.SerializerMethodField(read_only=True, help_text='用户是否需要更新基础数据')
    qr_code = serializers.SerializerMethodField(read_only=True, help_text='企业微信用户二维码')
    real_name = serializers.SerializerMethodField(read_only=True, help_text='真实姓名-展示用')
    nickname = serializers.SerializerMethodField(read_only=True, help_text='个人用户邮箱')
    personal_email = serializers.SerializerMethodField(read_only=True, help_text='个人用户昵称')
    coach_type = serializers.SerializerMethodField()

    class Meta:
        model = User
        exclude = ('created_at', 'updated_at')
        extra_kwargs = {'password': {'write_only': True}}

    def get_nickname(self, obj):
        if PersonalUser.objects.filter(user_id=obj.id, deleted=False).exists():
            personal_user = PersonalUser.objects.filter(user_id=obj.id, deleted=False).first()
            return personal_user.nickname
        return

    def get_personal_email(self, obj):
        if PersonalUser.objects.filter(user_id=obj.id, deleted=False).exists():
            personal_user = PersonalUser.objects.filter(user_id=obj.id, deleted=False).first()
            return personal_user.email
        return

    def get_true_name(self, obj):
        return obj.cover_name

    def get_real_name(self, obj):
        return obj.cover_name

    def get_coach_type(self, obj):
        coach = Coach.objects.filter(user_id=obj.id, deleted=False).first()
        if coach:
            return coach.coach_type
        return

    def get_qr_code(self, obj):
        if WorkWechatUser.objects.filter(user=obj, deleted=False).exists():
            return WorkWechatUser.objects.filter(user=obj, deleted=False).first().qr_code

    def get_is_revise_date(self, obj):
        # 2.10.3移除判断
        # if str(obj.true_name)[:2] == '用户' and len(str(obj.true_name)) == 6:
        #     return True
        # if not obj.head_image_url:
        #     return True
        return False

    def get_user_role(self, obj):
        project_member = user_public.get_user_to_project_member_all(obj.id)
        data_list = []
        for member in project_member:
            data_list.append({'user_id': member.user_id, 'project_id': member.project_id, 'role_id': member.role})
        # 项目教练
        project_coach = ProjectCoach.objects.filter(coach__user=obj, deleted=False)
        for coach in project_coach:
            data_list.append({'user_id': coach.coach.user.id, 'project_id': coach.project_id if coach.project_id else None, 'role_id': ProjectMemberRoleEnum.coach.value})
        return data_list

    def get_role(self, obj):
        role_list = []
        # 普通被教练者
        if ProjectMember.objects.filter(user=obj, role=ProjectMemberRoleEnum.coachee.value, deleted=False, is_forbidden=False).exists():
            role_list.append(UserRoleEnum.coachee.value)

        # 签约教练
        coach = Coach.objects.filter(user=obj, deleted=False).first()
        if coach and coach.coach_type in  CoachUserTypeEnum.enterprise_coach_value():
            role_list.append(UserRoleEnum.coach.value)

        # 个人教练
        if coach and coach.coach_type in CoachUserTypeEnum.trainee_coach_value():
            role_list.append(UserRoleEnum.trainee_coach.value)

        # 个人用户
        if PersonalUser.objects.filter(user=obj, deleted=False).exists():
            role_list.append(UserRoleEnum.trainee_coachee.value)
        return role_list

    def create(self, validated_data):
        instance = User(**validated_data)
        instance.password = aesencrypt(validated_data['password'])
        instance.save()
        return instance

    def update(self, instance, validated_data):
        # 对于 true_name 字段，我们需要手动从原始输入数据中获取它
        # 因为 'true_name' 是用 get_true_name 方法处理的，它不会直接出现在 validated_data 中
        true_name = self.context['request'].data.get('true_name')
        if true_name is not None:
            instance.true_name = true_name

        # 更新剩余的字段
        for attr, value in validated_data.items():
            setattr(instance, attr, value)
        instance.save()
        return instance

    # 由于 'true_name' 字段现在是一个方法字段，我们不需要在这里对其进行处理
    # 因此，我们可以从 validated_data 中移除 true_name
    def to_internal_value(self, data):
        ret = super().to_internal_value(data)
        ret.pop('true_name', None)  # 确保不会处理 true_name 字段
        return ret


def update_user_pwd(user, new_password):
    user.password = aesencrypt(new_password)
    user.save()
    sys_user = SysUser.objects.filter(UserTrueName=user.true_name).first()
    if sys_user:
        sys_user.UserPwd = aesencrypt(new_password)
        sys_user.save()
    return user


def sys_login(request, user):
    s_user = authenticate(username=user.name, password=user.password)
    if s_user is not None:
        login(request, s_user)
        return s_user
    return None


class ProjectUserListSerializer(serializers.ModelSerializer):
    id = serializers.IntegerField(help_text='用户id')
    true_name = serializers.CharField(help_text='用户姓名')
    is_relevance = serializers.SerializerMethodField(help_text='是否关联, 1是， 0否')

    class Meta:
        fields = ['id', 'true_name', 'is_relevance']
        model = User

    def get_is_relevance(self, obj):
        return 0


class FeedBackSerializers(serializers.ModelSerializer):
    created_at = serializers.SerializerMethodField(help_text='用户名')
    user_name = serializers.SerializerMethodField(help_text='用户名')
    phone = serializers.SerializerMethodField(help_text='创建时间')

    def get_created_at(self, obj):
        return obj.created_at.strftime('%Y-%m-%d %H:%M:%S')

    def get_phone(self, obj):
        if obj.creator_user:
            return obj.creator_user.phone
        return
    def get_user_name(self, obj):
        if obj.creator_user:
            return obj.creator_user.cover_name
        return

    class Meta:
        model = FeedBack
        fields = ['id', 'user_name', 'phone', 'content', 'created_at']
