from datetime import datetime, timedelta
import traceback
import time
import redis
import os
from drf_yasg import openapi
from drf_yasg.utils import swagger_auto_schema

from django.conf import settings
from django.db import transaction
from rest_framework.views import APIView

from wisdom_v2.models import User
from utils.api_response import success_response, parameter_error_response
from utils.task import check_celery_health
from file_read_backwards import FileReadBackwards
from utils.feishu_robot import push_wx_error_message
from django.shortcuts import HttpResponse
from rest_framework import status


work_wechat_redis = redis.Redis.from_url(settings.WORK_WECHAT_REDIS)


class HealthCheckView(APIView):
    authentication_classes = []

    def get(self, request, *args, **kwargs):
        # 数据库
        try:
            user = User.objects.last()
            name = user.name
        except:
            # 推送数据库错误信息
            push_wx_error_message(name='系统健康监测', level='error', content='mysql服务错误')
            return HttpResponse(status=status.HTTP_500_INTERNAL_SERVER_ERROR)
        # redis
        try:
            work_wechat_redis.set('health', 'health_check', ex=60)
            msg = work_wechat_redis.get('health')
        except:
            push_wx_error_message(name='系统健康监测', level='error', content='redis服务错误')
            return HttpResponse(status=status.HTTP_500_INTERNAL_SERVER_ERROR)
        # celery
        try:
            check_celery_health.delay()
            start_time = datetime.now() - timedelta(minutes=6)
            file_path = '/root/Co-Wisdom-BE/co_wisdom_server/run/default.log'
            if os.path.exists(file_path):
                with FileReadBackwards(file_path, encoding="utf-8") as f:
                    text = f.readline()
                date_str = text[1:20]
                date_log = datetime.strptime(date_str, '%Y-%m-%d %H:%M:%S')
                end_time = datetime.now() + timedelta(minutes=6)
                if not start_time < date_log < end_time:
                    push_wx_error_message(name='系统健康监测', level='error', content='celery触发任务错误')
                    return HttpResponse(status=status.HTTP_500_INTERNAL_SERVER_ERROR)
            else:
                push_wx_error_message(name='系统健康监测', level='error', content='celery触发任务错误-未检测到日志')
                return HttpResponse(status=status.HTTP_500_INTERNAL_SERVER_ERROR)

        except:
            # 推送celery失败消息
            push_wx_error_message(name='系统健康监测', level='error', content='celery触发任务错误')
            return HttpResponse(status=status.HTTP_500_INTERNAL_SERVER_ERROR)
        # 定时任务
        try:
            start_time = datetime.now() - timedelta(minutes=2)
            file_path = '/root/Co-Wisdom-BE/co_wisdom_server/run/celery.log'
            if os.path.exists(file_path):
                with FileReadBackwards(file_path, encoding="utf-8") as f:
                    text = f.readline()
                date_str = text[1:20]
                date_log = datetime.strptime(date_str, '%Y-%m-%d %H:%M:%S')
                end_time = datetime.now() + timedelta(minutes=2)
                if not start_time < date_log < end_time:
                    # 推送失败消息
                    push_wx_error_message(name='系统健康监测', level='error', content='celery定时任务错误')
                    return HttpResponse(status=status.HTTP_500_INTERNAL_SERVER_ERROR)
            else:
                push_wx_error_message(name='系统健康监测', level='error', content='celery定时任务错误-未检测到日志')
                return HttpResponse(status=status.HTTP_500_INTERNAL_SERVER_ERROR)

        except:
            push_wx_error_message(name='系统健康监测', level='error', content='celery定时任务错误')
            return HttpResponse(status=status.HTTP_500_INTERNAL_SERVER_ERROR)
        return success_response()