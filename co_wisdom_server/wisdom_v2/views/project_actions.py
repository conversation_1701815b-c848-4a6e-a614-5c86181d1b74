import datetime

from rest_framework import serializers
from django.db.models import Av<PERSON>, <PERSON>, Su<PERSON>, Q
from django.utils import timezone

from wisdom_v2.views.customer_portrait_actions import CustomerPortraitDetailsSerializers
from wisdom_v2.common import project_service_public, tag_public
from wisdom_v2.enum.chemical_interview_enum import ChemicalInterviewStatusEnum
from wisdom_v2.enum.company_enum import CompanyAttrEnum, CompanyScaleEnum
from wisdom_v2.enum.project_enum import ProjectStatusEnum
from wisdom_v2.enum.project_interview_enum import ProjectInterviewTypeEnum, ProjectInterviewPlaceCategoryEnum
from wisdom_v2.enum.service_content_enum import CustomerPortraitTypeEnum
from wisdom_v2.models import Project, ProjectInterested, ProjectInterview, ProjectMember, ProjectCoach, User, \
    ProjectInterviewRecord, ActionPlan, Habit, LearnArticle, Diary, SignIn, Company, CustomerPortrait
from wisdom_v2.views.user import UserSerializer
from wisdom_v2.views.coach_actions import CoachSerializers
from wisdom_v2.views.constant import ATTR_TYPE_INTERVIEW, ATTR_STATUS_INTERVIEW_CANCEL, INTERVIEW_TYPE_COACHING
from wisdom_v2.models_file import ChemicalInterview2Coach, StakeholderInterview, ProjectServiceStage, \
    StakeholderInterviewModule, ProjectTagGroup


class InterviewDetailSerializers(serializers.ModelSerializer):
    interview_date = serializers.DateTimeField(format='%Y-%m-%d %H:%M', source='public_attr.start_time')
    user_name = serializers.CharField(source='public_attr.target_user.cover_name')
    times = serializers.SerializerMethodField()
    status = serializers.SerializerMethodField()
    coach_name = serializers.CharField(source='public_attr.user.cover_name')
    project_name = serializers.CharField(source='public_attr.project.full_name')

    class Meta:
        model = ProjectInterview
        fields = ('id', 'topic', 'user_name', 'times', 'status', 'coach_name', 'project_name', 'interview_date')

    def get_status(self, obj):
        now_time = timezone.now()
        if obj.public_attr.start_time > now_time and obj.public_attr.end_time > now_time and obj.public_attr.status != ATTR_STATUS_INTERVIEW_CANCEL:
            return '未开始'
        elif obj.public_attr.start_time < now_time < obj.public_attr.end_time and obj.public_attr.status != ATTR_STATUS_INTERVIEW_CANCEL:
            return '进行中'
        elif obj.public_attr.start_time < now_time and obj.public_attr.status != ATTR_STATUS_INTERVIEW_CANCEL:
            return '完成'
        elif obj.public_attr.status == ATTR_STATUS_INTERVIEW_CANCEL:
            return '取消'
        else:
            return None


    def get_times(self, obj):
        if obj.times:
            return round(float(obj.times) / 60, 1)
        else:
            return 0




class ProjectMemberSerializers(serializers.ModelSerializer):
    user = UserSerializer()

    class Meta:
        model = ProjectMember
        exclude = ('coachee_first_msg_status', 'created_at', 'updated_at', 'deleted')


# 2.13版本修改项目模块，项目基础信息序列化。
class ProjectBaseSerializers(serializers.ModelSerializer):
    name = serializers.CharField(help_text='项目名称', allow_null=True)
    start_time = serializers.DateField(help_text='项目开始时间', required=False, allow_null=True)
    end_time = serializers.DateField(help_text='项目结束时间', required=False, allow_null=True)
    company_id = serializers.IntegerField(help_text='企业id')
    all_times = serializers.FloatField(help_text='线上一对一项目辅导总时长（小时）', required=False, allow_null=True)
    offline_time = serializers.FloatField(help_text='线下一对一项目辅导总时长（小时）注：只有5ALC模式才会有当前字段值', required=False, allow_null=True)
    max_cost = serializers.IntegerField(help_text='最大费用', required=False, allow_null=True)
    min_cost = serializers.IntegerField(help_text='最小费用', required=False, allow_null=True)
    expect_coach_count = serializers.IntegerField(help_text='教练数量', required=False, allow_null=True)
    expect_coachee_count = serializers.IntegerField(help_text='被教练着数量', required=False, allow_null=True)
    promoter = serializers.CharField(help_text='由谁发起', required=False, allow_null=True, allow_blank=True)
    reason = serializers.CharField(help_text='为什么发起', required=False, allow_null=True, allow_blank=True)
    coach_content = serializers.CharField(help_text='包含的教练工作', required=False, allow_null=True, allow_blank=True)
    providing_coach_list_time = serializers.DateField(help_text='最晚推送教练时间', required=False, allow_null=True)
    is_action_plan_to_report = serializers.BooleanField(help_text='行动计划是否写入教练报告', required=False)

    class Meta:
        model = Project
        exclude = ()


# 2.13版本修改项目模块，项目信息的返回格式修改。
class ProjectDetailsSerializers(serializers.ModelSerializer):
    providing_coach_list_time = serializers.SerializerMethodField()
    manager = serializers.ListField(source='manager_list', required=False, read_only=True)
    admin = serializers.ListField(source='admin_list', required=False, read_only=True)
    company = serializers.SerializerMethodField()
    background = serializers.SerializerMethodField()
    portrait = serializers.SerializerMethodField()
    is_stakeholder_interview = serializers.SerializerMethodField(help_text='项目可否预约利益相关者访谈')
    is_chemical_interview = serializers.SerializerMethodField(help_text='项目可否预约化学面谈')
    project_service = serializers.SerializerMethodField(help_text='项目服务')
    require_tag_group = serializers.SerializerMethodField(help_text='项目标签')

    def get_project_service(self, obj):
        data = project_service_public.get_project_path_diagram(obj)

        return data

    def get_is_chemical_interview(self, obj):
        if ChemicalInterview2Coach.objects.filter(
                deleted=False, interview__isnull=True, chemical_interview_module__project_member__project=obj,
                chemical_interview_status=ChemicalInterviewStatusEnum.not_feedback,
                chemical_interview_module__deleted=False,
                chemical_interview_module__start_time__lte=datetime.datetime.now().date(),
                chemical_interview_module__end_time__gte=datetime.datetime.now().date()).exists():
            return True
        return False

    def get_is_stakeholder_interview(self, obj):
        if StakeholderInterview.objects.filter(
                deleted=False, stakeholder_interview_module__deleted=False,
                stakeholder_interview_module__project_member__project=obj,
                stakeholder_interview_module__start_date__lte=datetime.datetime.now().date(),
                stakeholder_interview_module__end_date__gte=datetime.datetime.now().date(),
                interview__isnull=True).exists():
            return True
        return False

    def get_providing_coach_list_time(self, obj):
        if obj.providing_coach_list_time:
            return obj.providing_coach_list_time.strftime("%Y-%m-%d")
        return None

    def get_company(self, obj):

        company = Company.objects.filter(id=obj.company_id, deleted=False).first()
        if company:
            return {
                'id': company.id,
                'name': company.real_name,
                'attr': CompanyAttrEnum.get_display(company.company_attr),
                'scale': CompanyScaleEnum.get_display(company.scale),
                'brief': company.brief,
            }
        return None

    def get_background(self, obj):

        if obj.start_time and obj.end_time:
            obj_time = f'{obj.start_time.strftime("%Y.%m.%d")}~{obj.end_time.strftime("%Y.%m.%d")}'
        else:
            obj_time = None

        if obj.cost:
            obj_cost = obj.cost
        elif obj.min_cost and obj.max_cost:
            obj_cost = f'{obj.min_cost}-{obj.max_cost}元/小时'
        else:
            obj_cost = None

        return {
            'promoter': obj.promoter,
            'reason': obj.reason,
            'coach_content': obj.coach_content,
            'time': obj_time,
            'required_coach_count': obj.required_coach_count,
            'cost': obj_cost,
            'required_coachee_count': obj.required_coachee_count,
            'initiator': obj.initiator,
            'internal_reference_info': obj.internal_reference_info
        }

    def get_portrait(self, obj):
        data = []
        customer_portrait = CustomerPortrait.objects.filter(
            project_id=obj.id, deleted=False).order_by(
            '-type', '-created_at'
        ).all()
        for item in customer_portrait:
            data.append(CustomerPortraitDetailsSerializers(item).data)
        return data

    def get_require_tag_group(self, obj):
        project_tag_group = ProjectTagGroup.objects.filter(deleted=False, project_id=obj.id).order_by('-created_at').all()
        require_tag_group = []
        for item in project_tag_group:
            require_tag_group.append({
                "id": str(item.id),
                "title": item.title,
                "coach_content": item.coach_content,
                "project_member_name": item.project_member.user.cover_name if item.project_member else None,
                "stakeholder_feedback_and_expectations": item.stakeholder_feedback_and_expectations,
                "require_tag": tag_public.get_project_tag_list(item.id),
                "other_info": item.other_info
            })
        return require_tag_group

    class Meta:
        model = Project
        fields = ('id', 'name', 'status', 'all_times', 'offline_time', 'account_manager',
                  'start_time', 'end_time', 'is_stakeholder_interview', 'is_chemical_interview',
                  'providing_coach_list_time', 'company', 'background', 'portrait', 'admin', 'manager', 'project_service',
                  'require_tag_group', 'is_show_path_diagram_hour', 'cost')


class ProjectSerializers(serializers.ModelSerializer):
    name = serializers.CharField(help_text='项目名称')
    start_time = serializers.DateField(help_text='项目开始时间')
    # start_time = serializers.SerializerMethodField(help_text='项目开始时间')
    end_time = serializers.DateField(help_text='项目结束时间')
    # end_time = serializers.SerializerMethodField(help_text='项目结束时间')
    company_id = serializers.IntegerField(help_text='企业id')
    manager_list = serializers.ListField(help_text="""项目顾问列表 [
                {
                    "id": 1179,
                    "true_name": "小灰灰真名"
                }
            ],""", required=False, read_only=True)
    all_times = serializers.FloatField(help_text='线上一对一项目辅导总时长（小时）', required=False)
    offline_time = serializers.FloatField(help_text='线下一对一项目辅导总时长（小时）备注：只有5ALC模式才会有当前字段值', required=False)
    background_requirement = serializers.CharField(help_text='客户需求', required=False)
    background_expectation = serializers.CharField(help_text='对教练期望', required=False)
    background_coacheeinfo = serializers.CharField(help_text='被教练者信息', required=False)
    background_remark = serializers.CharField(help_text='备注', required=False)
    is_enable = serializers.IntegerField(help_text='是否启用 0已停用，1已启用', required=False)
    company_short_name = serializers.CharField(source='company.short', help_text='企业简称', required=False, read_only=True)
    project_progress = serializers.CharField(help_text='项目进展', required=False, read_only=True)
    status = serializers.IntegerField(help_text='项目状态，1:打单中 2:进行中 3:已停用 4:已完成 5:已输单', required=False, read_only=True)
    project_member_count = serializers.IntegerField(help_text='被教练者', required=False, read_only=True)
    coach_count = serializers.CharField(help_text='教练', required=False, read_only=True)
    project_interview_count = serializers.CharField(help_text='辅导记录', required=False, read_only=True)
    project_evaluation_count = serializers.CharField(help_text='测评', required=False, read_only=True)
    project_report = serializers.CharField(help_text='报告', required=False, read_only=True)
    project_material = serializers.ListField(help_text="""项目资料 样式：
                                                                    {   "file_id": 1,
                                                                        "user_id": 1179,
                                                                        "file_type": "xlsx",
                                                                        "file_name": "测试文件1",
                                                                        "file_path": "我是oss地址",
                                                                        "created_at": "2022-03-04T18:45:00"
                                                                    }""", required=False)

    class Meta:
        model = Project
        exclude = ('created_at', 'updated_at', 'deleted', 'company')

    def get_start_time(self, obj):
        return obj.start_time.strftime("%Y.%m.%d")

    def get_end_time(self, obj):
        return obj.end_time.strftime("%Y.%m.%d")

    def get_remain_interview_hours(self, obj):
        try:
            times_minute = ProjectMember.objects.filter(project=obj, deleted=False,
                                                        all_interview_time__isnull=False
                                                        ).exclude(all_interview_time=-1).aggregate(
                times_minute=Sum('all_interview_time'))['times_minute']
            if times_minute and obj.all_times:
                return round((obj.all_times - times_minute) / 60, 1)
        except Exception as e:
            print(e)
            return 0


# 校验创建/编辑项目字段
def valid_project_data(data):
    if 'name' not in data.keys():
        return '请输入项目名称'
    else:
        if len(data.get('name')) > 50:
            return '项目名称最多50个字'
    if 'status' not in data.keys():
        return '请输入项目状态'
    elif int(data.get('status')) not in ProjectStatusEnum.get_describe_keys():
        return '是否签约输入值错误'

    if 'start_time' in data.keys() or 'end_time' in data.keys():
        try:
            start_time = data.get('start_time')
            end_time = data.get('end_time')
            start_time = datetime.datetime.strptime(start_time, '%Y-%m-%d')
            end_time = datetime.datetime.strptime(end_time, '%Y-%m-%d')
        except:
            return '开始时间/结束时间格式错误'
        now = datetime.datetime.now()
        if end_time < now:
            return '项目结束时间不能早与当前时间'
        if end_time < start_time:
            return '项目结束时间不能早与项目开始时间'

    if 'promoter' in data.keys() and len(data.get('promoter')) > 500:
        return '发起人最多500个字'

    if 'reason' in data.keys() and len(data.get('reason')) > 500:
        return '发起原因最多500个字'

    if 'coach_content' in data.keys() and len(data.get('coach_content')) > 500:
        return '教练工作内容最多500个字'

    if 'max_cost' in data.keys() or 'min_cost' in data.keys():
        err = get_int_detection(data['max_cost'])
        if err:
            return f'最大费用{err}'
        err = get_int_detection(data['min_cost'])
        if err:
            return f'最小费用{err}'
        if data['min_cost'] < data['min_cost']:
            return f'最大费用不可以小于最小费用'

    if 'coach_count' in data.keys():
        err = get_int_detection(data['coach_count'])
        if err:
            return f'教练数量{err}'

    if 'coachee_count' in data.keys():
        err = get_int_detection(data['coachee_count'])
        if err:
            return f'被教练者数量{err}'

    return


def get_time_detection(time):
    if type(time) != float:
        return '辅导辅导总时长输入格式错误'
    if str(time).split('.')[1] not in ['0', '5']:
        return '辅导总时长小数位应为0或5'
    if time > 999:
        return '辅导总时长不能超过999小时'
    if time < 0:
        return '辅导总时长应为正数'
    return


def get_int_detection(item):
    if not isinstance(item, int):
        return '参数类型错误'
    if len(str(item)) > 5:
        return '只能输入输入5位以内的数字'
    return


class ProjectInterestedSerializers(serializers.ModelSerializer):
    interested = ProjectMemberSerializers()
    master = ProjectMemberSerializers()

    class Meta:
        model = ProjectInterested
        exclude = ('id', 'created_at', 'updated_at', 'deleted')


class ProjectCoachSerializers(serializers.ModelSerializer):
    member = UserSerializer()
    coach = CoachSerializers()

    class Meta:
        model = ProjectCoach
        exclude = ('id', 'created_at', 'updated_at', 'deleted')


def get_time_hours(project=None):
    interview_query = ProjectInterview.objects.filter(
        public_attr__type=ATTR_TYPE_INTERVIEW,
        type=INTERVIEW_TYPE_COACHING,
        public_attr__project=project, deleted=False
    ).exclude(public_attr__status=6)
    times = interview_query.aggregate(times_minute=Sum('times'))
    if times['times_minute']:
        interview_hour = round(times['times_minute'] / 60, 1)
    else:
        interview_hour = 0
    return interview_hour or 0, interview_query.count()


def project_progress_time_hours(project):
    interview_query = ProjectInterview.objects.filter(
        Q(coach_record_status=True) | Q(coachee_record_status=True),
        public_attr__type=ATTR_TYPE_INTERVIEW,
        type__in=[ProjectInterviewTypeEnum.formal_interview, ProjectInterviewTypeEnum.stakeholder_interview],
        public_attr__project=project, deleted=False, place_category=ProjectInterviewPlaceCategoryEnum.online_one_to_one,
    ).exclude(public_attr__status=6)
    times = interview_query.aggregate(times_minute=Sum('times'))
    if times['times_minute']:
        interview_hour = round(times['times_minute'] / 60, 1)
    else:
        interview_hour = 0
    return interview_hour or 0


def project_progress_all_hours(project):
    interview_hours = project.all_times if project.all_times else 0
    for stakeholder_interview in StakeholderInterviewModule.objects.filter(project_member__project=project,
                                                                           deleted=False):
        hours = stakeholder_interview.stakeholder_interview_number * stakeholder_interview.duration
        interview_hours += hours
    return interview_hours


def get_query_times_count(query):
    res = query.aggregate(interview_times=Sum('times'))['interview_times']
    interview_times = round(res / 60, 1) if res else 0
    interview_count = query.count()
    return interview_times, interview_count


def get_project_member_count(project, start_time, end_time, all_data=False, all_time=None):
    if all_data:
        args = {'created_at__range': [start_time, end_time]}
        active_args = {'last_active__date__range': [start_time, end_time]}
    else:
        args = {'created_at__range': [all_time, end_time]}
        active_args = {'last_active__date': start_time}

    project_member = set(ProjectMember.objects.filter(deleted=False, role__in=[6, 7],
                                                      project=project,
                                                      **args
                                                      ).values_list('user_id', flat=True))
    # 激活员工
    login_user_ids = set(User.objects.filter(pk__in=project_member, **active_args).values_list('pk', flat=True))
    # 活跃员工
    active_count = set(ProjectInterview.objects.filter(public_attr__project=project,
                                                       public_attr__target_user_id__in=project_member,
                                                       deleted=False, public_attr__type=ATTR_TYPE_INTERVIEW
                                                       ).values_list('public_attr__target_user_id', flat=True))
    return len(project_member), len(login_user_ids-active_count), len(active_count)


def get_project_score(project, start_time, end_time, all_data=False):
    if all_data:
        args = {'created_at__date__range': [start_time, end_time]}
    else:
        args = {'created_at__date': start_time}
    record = ProjectInterviewRecord.objects.filter(deleted=False, interview__public_attr__project=project,
                                                   **args)
    # 评分的平均分
    score_avg = record.aggregate(target_progress_avg=Avg('target_progress'),
                                 harvest_score_avg=Avg('harvest_score'),
                                 satisfaction_score_avg=Avg('satisfaction_score'))
    avg_data = {}
    for key in score_avg.keys():
        avg_data[key] = round(score_avg[key], 1) if score_avg[key] else 0
    # 有效度次数/分数
    target_progress_count = record.values('target_progress').annotate(
        target_progress_count=Count('target_progress'))
    # 投入度次数/分数
    harvest_score_count = record.values('harvest_score').annotate(harvest_score_count=Count('harvest_score'))
    # 满意度次数/分数
    satisfaction_score_count = record.values('satisfaction_score').annotate(
        satisfaction_score_count=Count('satisfaction_score'))
    return avg_data, target_progress_count, harvest_score_count, satisfaction_score_count


def get_member_growth(project, start_time, end_time, member_growth, all_data=False):
    if all_data:
        args = {'created_at__date__range': [start_time, end_time]}
    else:
        args = {'created_at__date': start_time}
    # {'action': [], 'learn': [], 'change': [], 'analyse': []}
    # 1:行动力 2:学习力 3:改变力 4:思辨力
    if member_growth == 1:
        query = ActionPlan.objects.filter(public_attr__project=project, deleted=False, **args)
        return query.count(), query.filter(status=2).count()
    elif member_growth == 2:
        query = LearnArticle.objects.filter(public_attr__project=project, deleted=False, **args)
        return query.count(), query.filter(status=2).count()

    elif member_growth == 3:
        query = Diary.objects.filter(public_attr__project=project, deleted=False, **args)
        return query.count(), 0
    else:
        query = Habit.objects.filter(public_attr__project=project, deleted=False, **args )
        return query.count(), SignIn.objects.filter(habit__in=query).count()


class ProjectInfoListSerializer(serializers.ModelSerializer):
    id = serializers.IntegerField(help_text='项目id')
    name = serializers.CharField(help_text='项目名')
    customer_portrait = serializers.SerializerMethodField(help_text='客户画像')
    full_name = serializers.SerializerMethodField(help_text='全称：项目名+企业简称')

    class Meta:
        fields = ['id', 'name', 'full_name', 'customer_portrait']
        model = Project

    def get_full_name(self, obj):
        return f"{obj.name} - {obj.company.real_name}"

    def get_customer_portrait(self, obj):
        # 优先取群体画像 无 取第一个个人画像
        data = {"interview_time": None, "personal_target": None, 'project_cycle': None, 'project_msg': None}
        data['personal_target'] = obj.coach_content
        if obj.start_time and obj.end_time:
            project_cycle = f"{obj.start_time.strftime('%y.%m.%d')} ~ {obj.end_time.strftime('%y.%m.%d')} " \
                            f"(预估{(obj.end_time - obj.start_time).days + 1}天)"
            data['project_cycle'] = project_cycle
        if obj.background_remark:
            data['project_msg'] = f"{obj.background_remark}-{obj.coach_content}"
        else:
            data['project_msg'] = f"{obj.coach_content}"
        customer_portrait = obj.project_customer_portrait.filter(deleted=False)
        if customer_portrait.filter(type=CustomerPortraitTypeEnum.group).exists():
            customer_portrait = customer_portrait.filter(type=CustomerPortraitTypeEnum.group).first()
        elif customer_portrait.filter(type=CustomerPortraitTypeEnum.personal).exists():
            customer_portrait = customer_portrait.filter(type=CustomerPortraitTypeEnum.personal).first()
        else:
            return data
        if customer_portrait.expected_interview_start_time and customer_portrait.expected_interview_end_time:
            interview_time = f"{customer_portrait.expected_interview_start_time.strftime('%y.%m.%d')} ~ " \
                             f"{customer_portrait.expected_interview_end_time.strftime('%y.%m.%d')}"
            data['interview_time'] = interview_time
        return data