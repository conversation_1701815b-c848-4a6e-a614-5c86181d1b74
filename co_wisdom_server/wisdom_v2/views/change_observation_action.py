from rest_framework import serializers

from wisdom_v2.common import project_coach_public
from wisdom_v2.models import MultipleAssociationRelation, ProjectInterested, ChangeObservation, \
    ProjectMember, GrowthGoals2ChangeObservation, GrowthGoals, \
    ChangeObservationAnswer, ProjectCoach

from wisdom_v2.enum.service_content_enum import MultipleAssociationRelationTypeEnum
from wisdom_v2.views.constant import ATTR_TYPE_GROWTH_GOALS
from wisdom_v2.views.project_interested_action import AdminProjectInterestedSerializer


class AdminChangeObservationSerializer(serializers.ModelSerializer):
    id = serializers.IntegerField(help_text='id', read_only=True)
    name = serializers.CharField(help_text='改变观察反馈名称', required=False)
    write_condition = serializers.FloatField(help_text='填写条件', required=False)
    project_member_id = serializers.IntegerField(help_text='被教练者id', write_only=True, required=False)
    growth_goals = serializers.ListField(help_text='成长目标数据', write_only=True, required=False)
    project_interested = serializers.ListField(help_text='利益相关者数据', write_only=True, required=False)
    remind_type = serializers.ListField(help_text='提醒方式', write_only=True, required=False)
    member_count = serializers.SerializerMethodField(help_text='填写人数', read_only=True)
    target_count = serializers.SerializerMethodField(help_text='调研目标数量', read_only=True)
    created_at = serializers.DateTimeField(format='%Y-%m-%d %H:%M:%S', help_text='配置时间', read_only=True)

    class Meta:
        model = ChangeObservation
        fields = ('id', 'name', 'write_condition', 'member_count', 'target_count', 'created_at', 'project_member_id',
                  'growth_goals', 'project_interested', 'remind_type')

    def get_member_count(self, obj):
        return MultipleAssociationRelation.objects.filter(
            main_id=obj.pk, type=MultipleAssociationRelationTypeEnum.stakeholder_change_observation.value,
            deleted=False).count()

    def get_target_count(self, obj):
        return GrowthGoals2ChangeObservation.objects.filter(change_observation=obj, deleted=False).count()


class ChangeObservationDetailSerializer(serializers.ModelSerializer):
    id = serializers.IntegerField(help_text='id', read_only=True)
    name = serializers.CharField(help_text='改变观察名称', read_only=True)
    created_at = serializers.DateTimeField(help_text='创建时间', read_only=True, format="%Y-%m-%d")
    all_growth_goals = serializers.BooleanField(help_text='成长目标是否全选', read_only=True)
    growth_goals = serializers.SerializerMethodField(help_text='成长目标列表', read_only=True)
    project_interested = serializers.SerializerMethodField(help_text='利益相关者列表', read_only=True)
    remind_type = serializers.ListField(help_text='提醒方式', read_only=True)
    write_condition = serializers.FloatField(help_text='填写条件', read_only=True)
    is_write = serializers.SerializerMethodField(help_text='当前改变观察反馈是否已填写', read_only=True)
    coach_name = serializers.SerializerMethodField(help_text='教练名称', read_only=True)
    coachee_name = serializers.SerializerMethodField(help_text='客户名称', read_only=True)
    rate = serializers.SerializerMethodField(help_text='反馈进度', read_only=True)
    invite_end_time = serializers.DateTimeField(help_text='邀请截止时间', read_only=True, format="%Y-%m-%d %H:%M:%S")
    stakeholders_write_end_date = serializers.DateField(help_text='利益相关者填写截止时间', read_only=True, format="%Y-%m-%d")
    participants_count = serializers.SerializerMethodField(help_text='参与人员数量', read_only=True)

    class Meta:
        model = ChangeObservation
        fields = ('id', 'name', 'all_growth_goals', 'growth_goals', 'project_interested', 'remind_type',
                  'write_condition', 'is_write', 'coach_name', 'coachee_name', 'rate', 'created_at', 'invite_type',
                  'max_stakeholders_count', 'stakeholders_write_end_date', 'invite_end_time', 'participants_count')

    def get_rate(self, obj):
        answer_count = ChangeObservationAnswer.objects.filter(
            change_observation_id=obj.id).values_list('public_attr__user_id', flat=True).distinct().count()
        interested_count = MultipleAssociationRelation.objects.filter(
            main_id=obj.id, deleted=False,
            type=MultipleAssociationRelationTypeEnum.stakeholder_change_observation).count()
        return f'{answer_count}/{interested_count}'

    def get_all_growth_goals(self, obj):
        set_count = GrowthGoals2ChangeObservation.objects.filter(change_observation=obj, deleted=False).count()
        all_count = GrowthGoals.objects.filter(
            public_attr__user_id=obj.project_member.user_id, public_attr__type=ATTR_TYPE_GROWTH_GOALS,
            deleted=False).count()
        if set_count == all_count:
            return True
        return False

    def get_growth_goals(self, obj):
        growth_goals = GrowthGoals.objects.filter(
            public_attr__user_id=obj.project_member.user_id,
            public_attr__type=ATTR_TYPE_GROWTH_GOALS, deleted=False).order_by('is_finish', '-created_at')
        if not growth_goals.exists():
            return []
        return AdminChangeObservationDetailGrowthSerializer(growth_goals,
                                                            many=True, context={'change_observation_id': obj.pk}).data

    def get_project_interested(self, obj):
        project_interested = ProjectInterested.objects.filter(
            master_id=obj.project_member.user_id, deleted=False,
            project_id=obj.project_member.project_id).order_by('-created_at')
        if not project_interested.exists():
            return []
        return AdminProjectInterestedSerializer(project_interested, many=True,
                                                context={'change_observation_id': obj.pk}).data

    def get_participants_count(self, obj):
        project_interested = MultipleAssociationRelation.objects.filter(
            main_id=obj.id, deleted=False,
            type=MultipleAssociationRelationTypeEnum.stakeholder_change_observation).count()
        return project_interested

    def get_is_write(self, obj):
        if ChangeObservationAnswer.objects.filter(change_observation=obj).exists():
            return True
        return False

    def get_coach_name(self, obj):
        project_coaches = project_coach_public.get_project_user_coach(obj.project_member)
        if project_coaches and project_coaches.exists():
            return project_coaches.first().coach.user.cover_name
        return

    def get_coachee_name(self, obj):
        return obj.project_member.user.cover_name


class AdminChangeObservationDetailGrowthSerializer(serializers.ModelSerializer):
    coach_user_id = serializers.SerializerMethodField(help_text='教练id')
    coachee_user_id = serializers.SerializerMethodField(help_text='被教练者id')
    change = serializers.SerializerMethodField(help_text='达成目标后发生的改变')
    start_time = serializers.DateTimeField(format='%Y.%m.%d', source='public_attr.start_time', required=False)
    end_time = serializers.DateTimeField(format='%Y.%m.%d', source='public_attr.end_time', required=False)
    created_at = serializers.DateTimeField(format='%Y-%m-%d %H:%M:%S', required=False, read_only=True)
    is_set = serializers.SerializerMethodField(help_text='是否以当前改变观察关联')
    all_change = serializers.SerializerMethodField(help_text='衡量指标是否全选')

    class Meta:
        model = GrowthGoals
        exclude = ('updated_at', 'public_attr')

    def get_coach_user_id(self, obj):
        if obj.public_attr.target_user:
            return obj.public_attr.target_user.id
        return

    def get_coachee_user_id(self, obj):
        if obj.public_attr.user:
            return obj.public_attr.user.id
        return

    def get_change(self, obj):
        if obj.growth_goals_change.filter(deleted=False).exists():
            change_observation_id = self.context.get('change_observation_id')
            results = []
            for change in obj.growth_goals_change.filter(deleted=False):
                data = {}
                data['id'], data['content'] = change.pk, change.content
                is_set = False
                growth_goals2change_observation = GrowthGoals2ChangeObservation.objects.filter(
                    change_observation_id=change_observation_id, growth_goals_id=obj.pk, deleted=False).first()
                if growth_goals2change_observation and change.pk in growth_goals2change_observation.change:
                    is_set = True
                data['is_set'] = is_set
                results.append(data)
            return results

    def get_is_set(self, obj):
        change_observation_id = self.context.get('change_observation_id')
        if GrowthGoals2ChangeObservation.objects.filter(
                change_observation_id=change_observation_id, growth_goals_id=obj.pk, deleted=False).exists():
            return True
        return False

    def get_all_change(self, obj):
        change_observation_id = self.context.get('change_observation_id')
        growth_goals2change_observation = GrowthGoals2ChangeObservation.objects.filter(
            change_observation_id=change_observation_id, growth_goals_id=obj.pk, deleted=False).first()
        if growth_goals2change_observation:
            if growth_goals2change_observation.change:
                if len(growth_goals2change_observation.change) == obj.growth_goals_change.filter(deleted=False).count():
                    return True
        return False


class ChangeObservationUpdateSerializer(serializers.ModelSerializer):
    id = serializers.IntegerField(help_text='改变观察id', write_only=True, required=True)
    growth_goals = serializers.ListField(help_text='成长目标数据', write_only=True, required=False)
    project_interested = serializers.ListField(help_text='利益相关者数据', write_only=True, required=False)
    write_condition = serializers.FloatField(help_text='填写条件', write_only=True, required=False)
    remind_type = serializers.ListField(help_text='提醒方式', write_only=True, required=False)
    deleted = serializers.BooleanField(help_text='是否删除', write_only=True, required=False)

    class Meta:
        model = ChangeObservation
        fields = ['id', 'growth_goals', 'project_interested', 'write_condition', 'remind_type', 'deleted']

