from drf_yasg import openapi
from drf_yasg.utils import swagger_auto_schema
from rest_framework.decorators import action
from django.db import transaction

from wisdom_v2.views.role_action import RoleListSerializer
from wisdom_v2.models import Role, Permission, Role2Permission
from rest_framework import viewsets

from utils.pagination import StandardResultsSetPagination
from utils.api_response import success_response, parameter_error_response
from utils.permission import get_permission


class RoleViewSet(viewsets.ModelViewSet):
    queryset = Role.objects.filter(deleted=False).order_by('-id')
    serializer_class = RoleListSerializer

    @swagger_auto_schema(
        operation_id='角色列表',
        operation_summary='角色列表',
        manual_parameters=[
            openapi.Parameter('page', openapi.IN_QUERY, description='第几页', type=openapi.TYPE_NUMBER),
            openapi.Parameter('page_size', openapi.IN_QUERY, description='每页显示条数默认15条', type=openapi.TYPE_NUMBER),
        ],
        tags=['权限管理相关']
    )
    def list(self, request, *args, **kwargs):
        roles = self.get_queryset().exclude(name='企业管理员')
        # 分页
        paginator = StandardResultsSetPagination()
        page_list = paginator.paginate_queryset(roles, self.request)
        serializer = self.get_serializer(page_list, many=True)
        response = paginator.get_paginated_response(serializer.data)
        return success_response(response)

    @swagger_auto_schema(
        operation_id='角色权限',
        operation_summary='角色权限',
        manual_parameters=[
            openapi.Parameter('role_id', openapi.IN_QUERY, description='权限id', type=openapi.TYPE_NUMBER)
        ],
        tags=['权限管理相关']
    )
    @action(methods=['get'], detail=False, url_path='role_permissions')
    def role_permissions(self, request, *args, **kwargs):
        role_id = request.query_params.get('role_id', None)
        if not role_id:
            return parameter_error_response('请选择角色')
        role = Role.objects.filter(id=role_id).first()
        all_permission = Permission.objects.filter(deleted=False)
        all_permissions = get_permission(all_permission)
        # if role.name == '超级管理员':
        #     role_permissions = all_permissions
        # else:
        role_permissions = Permission.objects.filter(deleted=False, permission_2_role__role_id=role_id,
                                                     permission_2_role__deleted=False)
        role_permissions = get_permission(role_permissions)
        data = {'all_permission': all_permissions, 'role_permissions': role_permissions}
        return success_response(data)

    @swagger_auto_schema(
        operation_id='角色配置权限',
        operation_summary='角色配置权限',
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'role_id': openapi.Schema(type=openapi.TYPE_NUMBER, description='角色id'),
                'permission_ids': openapi.Schema(type=openapi.TYPE_ARRAY,
                                                 items=openapi.Schema(type=openapi.TYPE_NUMBER))
            }
        ),
        tags=['权限管理相关']
    )
    @action(methods=['post'], detail=False, url_path='role_set_permission')
    def role_set_permission(self, request, *args, **kwargs):
        role_id = request.data.get('role_id', None)
        permission_ids = request.data.get('permission_ids', None)
        if not role_id:
            return parameter_error_response('请选择角色')
        if not permission_ids:
            return parameter_error_response('请选择权限')
        if role_id == 1 or role_id == '1':
            return parameter_error_response('无法编辑超级管理员权限')
        old_permission_list = [info.permission_id for info in
                               Role2Permission.objects.filter(role_id=role_id, deleted=False)]
        deleted_list = list(set(old_permission_list).difference(set(permission_ids)))
        add_lst = list(set(permission_ids).difference(set(old_permission_list)))
        with transaction.atomic():
            if deleted_list:
                Role2Permission.objects.filter(permission_id__in=deleted_list, deleted=False, role_id=role_id) \
                    .update(deleted=True)

            if add_lst:
                permission_list = []
                for p_id in add_lst:
                    permission_list.append(Role2Permission(role_id=role_id, permission_id=p_id))
                if permission_list:
                    Role2Permission.objects.bulk_create(permission_list)

        return success_response()
