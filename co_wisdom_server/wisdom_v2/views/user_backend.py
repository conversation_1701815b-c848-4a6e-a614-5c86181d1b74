import re

from django.conf import settings
from drf_yasg import openapi
from drf_yasg.utils import swagger_auto_schema
from rest_framework.decorators import action
from django.db import transaction


from wisdom_v2 import utils
from wisdom_v2.views.constant import PROJECT_COMPANY_ADMIN
from wisdom_v2.views.user_backend_action import UserBackendSerializer, UserBackendListSerializer
from wisdom_v2.models import UserBackend, User, WorkWechatUser, Project
from rest_framework import viewsets

from utils.pagination import StandardResultsSetPagination
from utils.api_response import success_response, parameter_error_response, WorkWechatUserError
from utils.queryset import distinct
from utils.validate import validate_email


class UserBackendViewSet(viewsets.ModelViewSet):
    queryset = UserBackend.objects.filter(deleted=False).order_by('-id')
    serializer_class = UserBackendSerializer
    authentication_classes = []

    @swagger_auto_schema(
        operation_id='后台用户列表',
        operation_summary='后台用户列表',
        manual_parameters=[
            openapi.Parameter('page', openapi.IN_QUERY, description='第几页', type=openapi.TYPE_NUMBER),
            openapi.Parameter('true_name', openapi.IN_QUERY, description='用户姓名', type=openapi.TYPE_STRING),
            openapi.Parameter('page_size', openapi.IN_QUERY, description='每页显示条数默认15条', type=openapi.TYPE_NUMBER),
        ],
        tags=['权限管理相关']
    )
    def list(self, request, *args, **kwargs):
        self.serializer_class = UserBackendListSerializer
        user_backends = self.get_queryset()
        user_backends = user_backends.exclude(role__name='企业管理员')

        if request.query_params.get('true_name', None):
            user_backends = user_backends.filter(user__true_name__icontains=request.query_params.get('true_name'))
        # 去重
        user_backends = distinct(user_backends, ['user_id'])

        # 分页
        paginator = StandardResultsSetPagination()
        page_list = paginator.paginate_queryset(user_backends, self.request)
        serializer = self.get_serializer(page_list, many=True)
        response = paginator.get_paginated_response(serializer.data)
        return success_response(response)

    @swagger_auto_schema(
        operation_id='后台用户详情页',
        operation_summary='后台用户详情页',
        manual_parameters=[
            openapi.Parameter('user_backend_id', openapi.IN_QUERY, description='后台用户id', type=openapi.TYPE_NUMBER),
        ],
        tags=['权限管理相关']
    )
    @action(methods=['get'], detail=False, url_path='detail', authentication_classes=[])
    def user_detail(self, request, *args, **kwargs):
        user_backend_id = request.query_params.get('user_backend_id', None)
        if not user_backend_id:
            return parameter_error_response('请选择用户')
        self.serializer_class = UserBackendListSerializer
        instance = UserBackend.objects.filter(pk=user_backend_id).first()
        if not instance:
            return parameter_error_response()
        serializer = self.get_serializer(instance)
        return success_response(serializer.data)

    @swagger_auto_schema(
        operation_id='添加后台账户',
        operation_summary='添加后台账户',
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'name': openapi.Schema(type=openapi.TYPE_STRING, description='用户名'),
                'true_name': openapi.Schema(type=openapi.TYPE_STRING, description='姓名'),
                'phone': openapi.Schema(type=openapi.TYPE_STRING, description='手机号'),
                'email': openapi.Schema(type=openapi.TYPE_STRING, description='邮箱'),
                'work_wechat_user_id': openapi.Schema(type=openapi.TYPE_STRING, description='企业微信用户id'),
                'is_add_work_wechat_user': openapi.Schema(type=openapi.TYPE_BOOLEAN, description='是否是新建企业微信账号'),
                'role_id': openapi.Schema(type=openapi.TYPE_ARRAY,items=openapi.Schema(type=openapi.TYPE_NUMBER),
                                          description='角色id'),
            }
        ),
        tags=['权限管理相关']
    )
    def create(self, request, *args, **kwargs):
        self.serializer_class = UserBackendSerializer
        return success_response(super().create(request, *args, **kwargs).data)

    @swagger_auto_schema(
        operation_id='编辑后台账户',
        operation_summary='编辑后台账户',
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'id': openapi.Schema(type=openapi.TYPE_NUMBER, description='id'),
                'name': openapi.Schema(type=openapi.TYPE_STRING, description='用户名'),
                'true_name': openapi.Schema(type=openapi.TYPE_STRING, description='姓名'),
                'phone': openapi.Schema(type=openapi.TYPE_STRING, description='手机号'),
                'email': openapi.Schema(type=openapi.TYPE_STRING, description='邮箱'),
                'role_id': openapi.Schema(type=openapi.TYPE_NUMBER, description='角色id'),
                'work_wechat_user_id': openapi.Schema(type=openapi.TYPE_STRING, description='企业微信用户id'),
                'is_add_work_wechat_user': openapi.Schema(type=openapi.TYPE_STRING, description='是否是新建企业微信账号'),
            }
        ),
        tags=['权限管理相关']
    )
    @action(methods=['post'], detail=False, url_path='edit_user_backend')
    def edit_user_backend(self, request, *args, **kwargs):
        data = request.data
        user_backend_id = data.get('id', None)
        if not user_backend_id:
            return parameter_error_response('请选择用户')
        work_wechat_user_id = request.data.get('work_wechat_user_id')
        is_add_work_wechat_user = request.data.get('is_add_work_wechat_user')
        name, true_name, phone, email, role_id = data['name'], data['true_name'], data['phone'], data['email'], \
                                                 data['role_id']
        instance = UserBackend.objects.filter(pk=user_backend_id).first()
        if not instance:
            return parameter_error_response()
        user = instance.user
        if name != user.name and User.objects.filter(name=name, deleted=False).exists():
            return parameter_error_response('当前用户名已存在')
        if email != user.email:
            if ' ' in email:
                return parameter_error_response('邮箱中存在空格，请修改后再添加。')
            if not validate_email(email):
                return parameter_error_response('当前邮箱错误')
            if User.objects.filter(email=email, deleted=False).exists():
                return parameter_error_response('当前邮箱已存在')
        if phone != user.phone:
            if not re.match(r"^1[3456789][0-9]{9}$", phone):
                return parameter_error_response('手机号错误')
            if User.objects.filter(phone=phone, deleted=False).exists():
                return parameter_error_response('当前手机号已存在')

        if work_wechat_user_id:
            wx_user = WorkWechatUser.objects.filter(
                user__isnull=False,
                wx_user_id=work_wechat_user_id, deleted=False).first()
            if wx_user and wx_user.user.id != user.id:
                return success_response({'is_bind': True, 'user_name': wx_user.user.cover_name})
        if is_add_work_wechat_user:
            wx_user = WorkWechatUser.objects.filter(
                user__isnull=False,
                wx_user_id=user.phone, deleted=False).first()
            if wx_user:
                return parameter_error_response('手机号对应的企业微信账号已存在')
        with transaction.atomic():
            user.name = name
            user.phone = phone
            user.email = email
            user.true_name = true_name
            user.save()
            instance.role_id = role_id
            instance.save()
            if is_add_work_wechat_user:
                state = utils.add_work_wechat_user(user, settings.WORK_WECHAT_MANAGE_DEPARTMENT_ID, 'add_user')
                if state:
                    return success_response(state)
            else:
                utils.update_work_wechat_user(work_wechat_user_id, user)

        return success_response()

    @swagger_auto_schema(
        operation_id='删除后台账户',
        operation_summary='删除后台账户',
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'id': openapi.Schema(type=openapi.TYPE_NUMBER, description='id'),
            }
        ),
        tags=['权限管理相关']
    )
    @action(methods=['post'], detail=False, url_path='del_user_backend')
    def del_user_backend(self, request, *args, **kwargs):
        user_backend_id = request.data.get('id', None)
        if not user_backend_id:
            return parameter_error_response('请选择用户')
        instance = UserBackend.objects.filter(pk=user_backend_id).first()
        if not instance:
            return parameter_error_response()
        UserBackend.objects.filter(user=instance.user, deleted=False).update(deleted=True)

        # 清理用户关联的企业微信账号。
        work_wechat_user = WorkWechatUser.objects.filter(user=instance.user, deleted=False).first()
        if work_wechat_user:
            if work_wechat_user.external_user_id:
                work_wechat_user.wx_user_id = None
                work_wechat_user.qr_code = None
                work_wechat_user.save()
            else:
                work_wechat_user.deleted = True
                work_wechat_user.save()

        return success_response()

    @swagger_auto_schema(
        operation_id='查询企业管理员',
        operation_summary='查询企业管理员',
        manual_parameters=[
            openapi.Parameter('project_id', openapi.IN_QUERY, description='项目id', type=openapi.TYPE_NUMBER),
            openapi.Parameter('company_id', openapi.IN_QUERY, description='企业id', type=openapi.TYPE_NUMBER),
            openapi.Parameter('page', openapi.IN_QUERY, description='第几页', type=openapi.TYPE_NUMBER),
            openapi.Parameter('page_size', openapi.IN_QUERY, description='每页显示条数默认15条', type=openapi.TYPE_NUMBER)
        ],
        tags=['权限管理相关']
    )
    @action(methods=['get'], detail=False, url_path='get_company_admin')
    def get_company_admin(self, request, *args, **kwargs):
        try:
            self.serializer_class = UserBackendListSerializer

            project_id = request.query_params.get('project_id')
            company_id = request.query_params.get('company_id')

            user_backend = self.get_queryset().filter(
                deleted=False,
                role_id=PROJECT_COMPANY_ADMIN
            )
            if company_id:
                user_backend = user_backend.filter(
                    company_id=company_id)
            if project_id:
                user_backend = user_backend.filter(
                    project_id=project_id)
                if not user_backend.exists():
                    project = Project.objects.filter(id=project_id, deleted=False).first()
                    user_backend = self.get_queryset().filter(
                        deleted=False,
                        role_id=PROJECT_COMPANY_ADMIN,
                        company_id=project.company.id)
        except Exception as e:
            return parameter_error_response(str(e))

        # 分页
        paginator = StandardResultsSetPagination()
        page_list = paginator.paginate_queryset(user_backend, self.request)
        serializer = self.get_serializer(page_list, many=True)
        response = paginator.get_paginated_response(serializer.data)
        return success_response(response)



