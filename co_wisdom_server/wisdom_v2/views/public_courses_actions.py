from datetime import datetime

from django.db import transaction
from rest_framework import serializers

from wisdom_v2.common import public_courses_public, public_courses_coach_public
from wisdom_v2.common import business_order_public
from wisdom_v2.enum.business_order_enum import BusinessOrderTypeEnum
from wisdom_v2.models_file import PublicCourses, PublicCoursesCoach


class AdminPublicCoursesListSerializer(serializers.ModelSerializer):
    """
    公开课列表序列化
    """
    status = serializers.SerializerMethodField(help_text='公开课状态')
    class_time = serializers.SerializerMethodField(help_text='活动时间')
    coach_name = serializers.SerializerMethodField(help_text='教练名称')

    def get_status(self, obj):
        return public_courses_public.get_status_display(obj)

    def get_class_time(self, obj):
        return f'{obj.start_time.strftime("%Y-%m-%d %H:%M")} ~ {obj.end_time.strftime("%Y-%m-%d %H:%M")}'

    def get_coach_name(self, obj):
        coach_name = obj.public_courses_coach.filter(deleted=False).values_list('coach__user__true_name', flat=True)
        str_coach_name = '；'.join(coach_name)
        return str_coach_name

    class Meta:
        model = PublicCourses
        fields = ('id', 'class_name', 'status', 'type', 'class_time', 'coach_name')


class AdminPublicCoursesDetailSerializer(serializers.ModelSerializer):
    """
    活动详情序列化
    """
    start_time = serializers.DateTimeField(help_text='开始日期', required=False, format='%Y-%m-%d %H:%M:%S')
    end_time = serializers.DateTimeField(help_text='结束日期', required=False, format='%Y-%m-%d %H:%M:%S')
    coach_ids = serializers.ListField(child=serializers.IntegerField(), write_only=True)
    coach_content = serializers.SerializerMethodField(help_text='教练数据')

    def get_coach_content(self, obj):
        return public_courses_public.get_coach_content(obj)


    def create(self, validated_data):

        with transaction.atomic():
            coach_ids = validated_data.pop('coach_ids')

            instance = PublicCourses.objects.create(**validated_data)
            instance.save()

            # 绑定教练信息
            public_courses_coach_public.add_public_courses_coach(instance, coach_ids)

        return instance

    def update(self, instance, validated_data):
        with transaction.atomic():
            if 'deleted' in validated_data and validated_data['deleted']:
                instance.deleted = True
                instance.save()

                object_ids = list(instance.public_courses_coach.filter(deleted=False).values_list('id', flat=True))
                business_order_public.del_business_order(
                    object_ids, BusinessOrderTypeEnum.public_course.value, instance.type)
                PublicCoursesCoach.objects.filter(public_courses_id=instance.id).update(deleted=True)
                return instance
            else:
                if 'coach_ids' in validated_data:
                    coach_ids = validated_data.pop('coach_ids')
                    old_coach_ids = instance.public_courses_coach.filter(deleted=False).values_list('coach_id', flat=True)

                    # 删除教练列表
                    del_coach_list = list(set(old_coach_ids).difference(set(coach_ids)))
                    # 新增教练列表
                    add_coach_list = list(set(coach_ids).difference(set(old_coach_ids)))

                    if del_coach_list:
                        PublicCoursesCoach.objects.filter(
                            public_courses_id=instance.id, coach_id__in=del_coach_list).update(deleted=True)
                    if add_coach_list:
                        public_courses_coach_public.add_public_courses_coach(instance, add_coach_list)

                if validated_data.get('start_time'):
                    start_time = datetime.strptime(validated_data.get('start_time'), "%Y-%m-%d %H:%M:%S")
                else:
                    start_time = instance.start_time

                if validated_data.get('end_time'):
                    end_time = datetime.strptime(validated_data.get('end_time'), "%Y-%m-%d %H:%M:%S")
                else:
                    end_time = instance.end_time

                old_instance_type = None
                if validated_data.get('type'):
                    old_instance_type = instance.type
                    instance.type = validated_data.get('type')

                instance.class_name = validated_data.get('class_name', instance.class_name)
                instance.start_time = start_time
                instance.end_time = end_time

                instance.save()

                # 1-未开始 2-进行中 3-已结束 4-已删除
                status = public_courses_public.get_status_display(instance)
                update_type = old_instance_type if old_instance_type else instance.type

                if status in [1, 2]:
                    object_ids = list(instance.public_courses_coach.filter(deleted=False).values_list('id', flat=True))
                    business_order_public.del_business_order(
                        object_ids, BusinessOrderTypeEnum.public_course.value, update_type)
                elif validated_data.get('start_time') or validated_data.get('end_time') or old_instance_type:
                    object_ids = list(instance.public_courses_coach.filter(deleted=False).values_list('id', flat=True))
                    business_order_public.update_business_order(
                        object_ids, BusinessOrderTypeEnum.public_course.value, update_type,{
                            'start_time': start_time,
                            'end_time': end_time,
                            'type': instance.type
                        })

    class Meta:
        model = PublicCourses
        fields = ('id', 'class_name', 'type', 'start_time', 'end_time','coach_content', 'coach_ids')
