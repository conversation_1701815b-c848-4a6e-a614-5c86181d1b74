from datetime import datetime

from django.db import transaction
from django.db.models import Q
from drf_yasg import openapi
from drf_yasg.utils import swagger_auto_schema
from rest_framework.decorators import action
from rest_framework import viewsets

from utils.api_response import success_response, parameter_error_response
from utils.pagination import StandardResultsSetPagination
from wisdom_v2.common import activity_public
from wisdom_v2.enum.service_content_enum import ActivityTypeEnum
from wisdom_v2.models_file import Activity
from wisdom_v2.views.activity_actions import AdminActivityListSerializer, AdminActivityDetailSerializer, \
    AdminActivityDataDetailsSerializer


class AdminActivityViewSet(viewsets.ModelViewSet):
    queryset = Activity.objects.filter(deleted=False).order_by('-created_at')
    serializer_class = AdminActivityDetailSerializer

    @swagger_auto_schema(
        operation_id='获取活动列表',
        operation_summary='获取活动列表',
        manual_parameters=[
            openapi.Parameter('status', openapi.IN_QUERY, description='活动状态 1-未开始 2-进行中 3-已结束', type=openapi.TYPE_NUMBER,
                              required=True),
            openapi.Parameter('theme', openapi.IN_QUERY, description='活动主题',
                              type=openapi.TYPE_STRING, required=True),
            openapi.Parameter('type', openapi.IN_QUERY, description='活动类型 1-公益教练', type=openapi.TYPE_NUMBER,
                              required=True),
        ],
        tags=['活动运营']
    )
    def list(self, request, *args, **kwargs):

        try:
            status = request.query_params.get('status')
            theme = request.query_params.get('theme')
            activity_type = request.query_params.get('type')
        except Exception as e:
            return parameter_error_response()
        queryset = self.get_queryset()

        if status:
            status = status.strip(',')
            tmp_q = Q()
            for item in status:
                # 未开始 开始时间>当前时间
                if str(item) == '1':
                    tmp_q.add(Q(start_date__gt=datetime.now().date()), Q.OR)

                # 进行中 开始时间<=当前时间，结束时间>=当前时间
                elif str(item) == '2':
                    tmp_q.add(Q(start_date__lte=datetime.now().date(), end_date__gte=datetime.now().date()), Q.OR)

                # 已结束 结束时间<当前时间
                elif str(item) == '3':
                    tmp_q.add(Q(end_date__lt=datetime.now().date()), Q.OR)
            queryset = queryset.filter(tmp_q)

        if theme:
            queryset = queryset.filter(theme__icontains=theme)
        if activity_type and activity_type in ActivityTypeEnum.get_describe_keys():
            queryset = queryset.filter(type=activity_type)

        # 分页
        paginator = StandardResultsSetPagination()
        page_list = paginator.paginate_queryset(queryset, self.request)
        serializer = AdminActivityListSerializer(page_list, many=True)
        response = paginator.get_paginated_response(serializer.data)
        return success_response(response)


    @swagger_auto_schema(
        operation_id='获取活动详情',
        operation_summary='获取活动详情',
        manual_parameters=[
            openapi.Parameter('activity_id', openapi.IN_QUERY, description='活动标识', type=openapi.TYPE_STRING,
                              required=True),
        ],
        tags=['活动运营']
    )
    @action(methods=['get'], detail=False, url_path='details')
    def get_activity_detail(self, request, *args, **kwargs):
        try:
            activity_id = request.query_params.get('activity_id')
            activity = Activity.objects.get(id=activity_id, deleted=False)
        except Activity.DoesNotExist:
            return parameter_error_response('活动不存在')
        except Exception as e:
            return parameter_error_response()
        serializer = self.get_serializer(activity)
        return success_response(serializer.data)

    @swagger_auto_schema(
        operation_id='创建活动',
        operation_summary='创建活动',
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'theme': openapi.Schema(type=openapi.TYPE_STRING, description='活动主题'),
                'type': openapi.Schema(type=openapi.TYPE_NUMBER, description='活动类型 1-公益教练'),
                'start_date': openapi.Schema(type=openapi.TYPE_STRING, description='活动开始时间'),
                'end_date': openapi.Schema(type=openapi.TYPE_STRING, description='活动结束时间'),
                'interview_start_date': openapi.Schema(type=openapi.TYPE_STRING, description='可预约辅导开始时间'),
                'interview_end_date': openapi.Schema(type=openapi.TYPE_STRING, description='可预约辅导结束时间'),
                'price': openapi.Schema(type=openapi.TYPE_NUMBER, description='活动金额'),
                'poster_image_url': openapi.Schema(type=openapi.TYPE_STRING, description='海报链接'),
                'limit_count': openapi.Schema(type=openapi.TYPE_NUMBER, description='单个用户限制次数'),
                'coach_ids': openapi.Schema(type=openapi.TYPE_STRING, description='教练id列表'),
                'notes': openapi.Schema(type=openapi.TYPE_STRING, description='活动额外描述'),
            }),
        tags=['活动运营']
    )
    @action(methods=['post'], detail=False, url_path='add')
    def add_activity_detail(self, request, *args, **kwargs):
        try:
            theme = request.data.get('theme')
            activity_type = request.data.get('type')
            start_date = request.data.get('start_date')
            end_date = request.data.get('end_date')
            limit_count = request.data.get('limit_count')
            accum_limit_count = request.data.get('accum_limit_count')
            activity_coach = request.data.get('activity_coach')
            interview_start_date = request.data.get('interview_start_date')
            interview_end_date = request.data.get('interview_end_date')
            price = request.data.get('price')

            # 如果没有配置辅导开始时间/结束时间 默认使用活动时间
            if not interview_start_date:
                interview_start_date = start_date
                request.data['interview_start_date'] = start_date
            if not interview_end_date:
                interview_end_date = end_date
                request.data['interview_end_date'] = end_date
        except Exception as e:
            return parameter_error_response()
        # if not activity_coach:
        #     return parameter_error_response('请选择教练')
        if not theme:
            return parameter_error_response('请输入活动主题')
        if not activity_type:
            return parameter_error_response('请选择活动类型')
        if not start_date:
            return parameter_error_response('请选择活动开始时间')
        if not end_date:
            return parameter_error_response('请选择活动结束时间')
        if start_date > end_date:
            return parameter_error_response('开始时间不能大于结束时间')
        if interview_start_date > interview_end_date:
            return parameter_error_response('辅导开始时间不能大于结束时间')
        if price is None:
            return parameter_error_response('请输入活动价格')
        if not limit_count and not accum_limit_count:
            return parameter_error_response('请输入用户最大报名数')

        with transaction.atomic():
            activity = activity_public.create_activity(request.data)
        serializer = self.get_serializer(activity)
        return success_response(serializer.data)

    @swagger_auto_schema(
        operation_id='修改活动',
        operation_summary='修改活动',
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'activity_id': openapi.Schema(type=openapi.TYPE_NUMBER, description='活动标识'),
                'theme': openapi.Schema(type=openapi.TYPE_STRING, description='活动主题'),
                'start_date': openapi.Schema(type=openapi.TYPE_STRING, description='活动开始时间'),
                'end_date': openapi.Schema(type=openapi.TYPE_STRING, description='活动结束时间'),
                'interview_start_date': openapi.Schema(type=openapi.TYPE_STRING, description='可预约辅导开始时间'),
                'interview_end_date': openapi.Schema(type=openapi.TYPE_STRING, description='可预约辅导结束时间'),
                'price': openapi.Schema(type=openapi.TYPE_NUMBER, description='活动金额'),
                'poster_image_url': openapi.Schema(type=openapi.TYPE_STRING, description='海报链接'),
                'limit_count': openapi.Schema(type=openapi.TYPE_NUMBER, description='单个用户限制次数'),
                'coach_ids': openapi.Schema(type=openapi.TYPE_STRING, description='教练id列表'),
                'notes': openapi.Schema(type=openapi.TYPE_STRING, description='活动额外描述'),
            }),
        tags=['活动运营']
    )
    @action(methods=['post'], detail=False, url_path='update')
    def update_activity_detail(self, request, *args, **kwargs):
        try:
            activity_id = request.data.get('id')
            activity = Activity.objects.get(id=activity_id, deleted=False)
        except Exception as e:
            return parameter_error_response()
        activity = activity_public.update_activity(activity, request.data)
        return success_response(self.get_serializer(activity).data)


    @swagger_auto_schema(
        operation_id='获取活动数据详情',
        operation_summary='获取活动数据详情',
        manual_parameters=[
            openapi.Parameter('activity_id', openapi.IN_QUERY, description='活动状态 1-未开始 2-进行中 3-已结束', type=openapi.TYPE_NUMBER,
                              required=True),
        ],
        tags=['活动运营']
    )
    @action(methods=['get'], detail=False, url_path='data_details')
    def get_activity_data_details(self, request, *args, **kwargs):
        try:
            activity_id = request.query_params.get('activity_id')
            activity = Activity.objects.get(id=activity_id, deleted=False)
        except Activity.DoesNotExist:
            return parameter_error_response('活动不存在')
        except Exception as e:
            return parameter_error_response()
        serializer = AdminActivityDataDetailsSerializer(activity)
        return success_response(serializer.data)
