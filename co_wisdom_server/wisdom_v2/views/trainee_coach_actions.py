import pendulum

from django.db.models import Sum, Avg
from rest_framework import serializers

from utils.trainee_coaches_data import get_trainee_coaches_user_score
from wisdom_v2.app_views.app_coach_actions import get_time_point
from wisdom_v2.enum.service_content_enum import ScheduleApplyTypeEnum
from wisdom_v2.models import TraineeCoach, PublicAttr, ProjectInterview, User, CoachAppraise, ProjectInterviewRecord
from wisdom_v2.views.constant import ATTR_TYPE_INTERVIEW


class TraineeCoachSerializers(serializers.ModelSerializer):
    name = serializers.CharField(source='user.cover_name', help_text='用户名')
    user_id = serializers.IntegerField(source='user.id', help_text='教练用户id')
    user_class = serializers.CharField(help_text='班次')
    tutoring_count = serializers.SerializerMethodField(help_text='一对一辅导次数')
    tutoring_time = serializers.SerializerMethodField(help_text='一对一辅导时常')
    user_count = serializers.SerializerMethodField(help_text='客户数量')
    target_progress_score = serializers.SerializerMethodField(help_text='有效度')
    harvest_score = serializers.SerializerMethodField(help_text='投入度')
    satisfaction_score = serializers.SerializerMethodField(help_text='满意度')
    recommend_score = serializers.SerializerMethodField(help_text='推荐度')

    def get_tutoring_count(self, obj):
        time = pendulum.now().strftime('%Y-%m-%d %H:%M:%S')
        tutoring_count = ProjectInterview.objects.filter(
            deleted=False, public_attr__start_time__lt=time,
            public_attr__user=obj.user).count()
        return tutoring_count

    def get_tutoring_time(self, obj):
        time = pendulum.now().strftime('%Y-%m-%d %H:%M:%S')
        tutoring_time = ProjectInterview.objects.filter(
            deleted=False, public_attr__start_time__lt=time,
            public_attr__user=obj.user).aggregate(tutoring_time=Sum('times'))['tutoring_time']
        return tutoring_time if tutoring_time else 0

    def get_user_count(self, obj):
        time = pendulum.now().strftime('%Y-%m-%d %H:%M:%S')
        coachee_id_list = set(PublicAttr.objects.filter(
            user=obj.user,
            start_time__lt=time,
            type=ATTR_TYPE_INTERVIEW).values_list(
            'target_user_id',
            flat=True))
        return len(coachee_id_list)

    def get_target_progress_score(self, obj):
        return get_trainee_coaches_user_score(obj.user, 'target_progress')

    def get_harvest_score(self, obj):
        return get_trainee_coaches_user_score(obj.user, 'harvest_score')

    def get_satisfaction_score(self, obj):
        return get_trainee_coaches_user_score(obj.user, 'satisfaction_score')

    def get_recommend_score(self, obj):
        avg_score = CoachAppraise.objects.filter(
            interview__public_attr__target_user__unionid__isnull=False,
            interview__public_attr__user=obj.user, deleted=False
        ).aggregate(score_avg=Avg('score'))['score_avg']
        return round(avg_score, 1) if avg_score else 0

    class Meta:
        model = TraineeCoach
        fields = ['id', 'name', 'user_id', 'user_class', 'tutoring_count', 'tutoring_time', 'user_count',
                  'target_progress_score', 'harvest_score', 'satisfaction_score', 'recommend_score']


class TraineeCoachBaseSerializers(serializers.ModelSerializer):
    name = serializers.CharField(source='user.cover_name', help_text='用户名', read_only=True)
    email = serializers.CharField(source='user.email', help_text='邮箱', read_only=True)
    phone = serializers.CharField(source='user.phone', help_text='手机号', read_only=True)
    head_image_url = serializers.CharField(source='user.head_image_url', help_text='头像', read_only=True)
    tutoring_time = serializers.SerializerMethodField(help_text='平台一对一辅导时常')
    latest_schedule = serializers.SerializerMethodField(help_text='最近可预约时间')

    def get_tutoring_time(self, obj):
        tutoring_time = ProjectInterview.objects.filter(
            deleted=False, public_attr__user=obj.user,
            public_attr__target_user__unionid__isnull=False,
        ).aggregate(tutoring_time=Sum('times'))['tutoring_time']
        return tutoring_time if tutoring_time else 0

    def get_latest_schedule(self, obj):
        time = pendulum.now()
        if time.minute >= 30:
            time = time.add(hours=1).set(minute=0)
        elif 30 > time.minute > 0:
            time = time.set(minute=30)
        # 如果是当天时间，往后推迟6个小时， 如果是明天 就直接从8点开始算
        if time.add(hours=6).day == pendulum.tomorrow().day:
            start_date = pendulum.tomorrow().add(hours=8)
        else:
            start_date = time.add(hours=6)
        latest_schedule = get_time_point(
            start_date.strftime('%Y-%m-%d %H:%M:00'), 30, obj.user.id, apply_type=ScheduleApplyTypeEnum.personal.value)
        if latest_schedule:
            date = pendulum.parse(latest_schedule)
            if date.day == pendulum.today().day:
                latest_schedule = '今日{}'.format(date.format('HH:mm'))
            elif date.day == pendulum.tomorrow().day:
                latest_schedule = '明日{}'.format(date.format('HH:mm'))
            elif date.day == pendulum.today().add(days=2).day:
                latest_schedule = '后天{}'.format(date.format('HH:mm'))
            elif date.day == pendulum.today().add(days=3).day:
                latest_schedule = date.format('YYYY.MM.DD HH:mm')
            return latest_schedule
        return

    class Meta:
        model = TraineeCoach
        exclude = ('updated_at', 'created_at')


class TraineeCoachClientCommentSerializers(serializers.ModelSerializer):
    name = serializers.CharField(source='interview.public_attr.target_user.cover_name', help_text='用户名')
    coach_score = serializers.CharField(source='score', help_text='推荐度')
    coach_comment = serializers.CharField(source='appraise', help_text='评论')
    interview_date = serializers.SerializerMethodField(help_text='辅导日期')
    project_id = serializers.SerializerMethodField(help_text='项目id')
    project_name = serializers.SerializerMethodField(source='项目名称')
    created_at = serializers.DateTimeField(format='%Y-%m-%d %H:%M:%S')

    def get_interview_date(self, obj):
        interview_date = obj.interview.public_attr.start_time
        return interview_date.strftime('%Y.%m.%d')

    def get_project_id(self, obj):
        project = obj.interview.public_attr.project
        if project:
            return project.pk

    def get_project_name(self, obj):
        project = obj.interview.public_attr.project
        if project:
            return project.name

    class Meta:
        model = CoachAppraise
        fields = ['id', 'name', 'interview_date', 'coach_score', 'coach_comment', 'project_id', 'project_name',
                  'created_at']


class ProjectInterviewRecordAppraiseSerializer(serializers.ModelSerializer):
    name = serializers.CharField(source='interview.public_attr.target_user.cover_name', help_text='用户名')
    coach_score = serializers.IntegerField(help_text='推荐度')
    coach_comment = serializers.CharField(help_text='评论')
    interview_date = serializers.SerializerMethodField(help_text='辅导日期')
    project_id = serializers.SerializerMethodField(help_text='项目id')
    project_name = serializers.SerializerMethodField(source='项目名称')
    created_at = serializers.DateTimeField(format='%Y-%m-%d %H:%M:%S')

    def get_interview_date(self, obj):
        interview_date = obj.interview.public_attr.start_time
        return interview_date.strftime('%Y.%m.%d')

    def get_project_id(self, obj):
        project = obj.interview.public_attr.project
        if project:
            return project.pk

    def get_project_name(self, obj):
        project = obj.interview.public_attr.project
        if project:
            return project.name

    class Meta:
        model = ProjectInterviewRecord
        fields = ['id', 'name', 'interview_date', 'coach_score', 'coach_comment', 'project_id', 'project_name',
                  'created_at']


class TraineeCoachUserSerializers(serializers.ModelSerializer):
    user_id = serializers.CharField(source='pk', help_text='用户id')
    name = serializers.CharField(source='true_name', help_text='用户名')
    phone = serializers.CharField(help_text='手机号')
    head_image_url = serializers.CharField(help_text='头像')

    tutoring_count = serializers.SerializerMethodField(help_text='一对一辅导次数')
    tutoring_time = serializers.SerializerMethodField(help_text='一对一辅导时常')
    target_progress_score = serializers.SerializerMethodField(help_text='有效度')
    harvest_score = serializers.SerializerMethodField(help_text='投入度')
    satisfaction_score = serializers.SerializerMethodField(help_text='满意度')
    recommend_score = serializers.SerializerMethodField(help_text='推荐度')

    def get_tutoring_count(self, obj):
        tutoring_count = ProjectInterview.objects.filter(
            deleted=False, public_attr__user=self.context,
            public_attr__target_user=obj
        ).count()
        return tutoring_count

    def get_tutoring_time(self, obj):
        tutoring_time = ProjectInterview.objects.filter(
            deleted=False, public_attr__user=self.context,
            public_attr__target_user=obj
        ).aggregate(tutoring_time=Sum('times'))['tutoring_time']
        return tutoring_time if tutoring_time else 0

    def get_target_progress_score(self, obj):
        return get_trainee_coaches_user_score(
            self.context, 'target_progress', target_user=obj)

    def get_harvest_score(self, obj):
        return get_trainee_coaches_user_score(
            self.context, 'harvest_score', target_user=obj)

    def get_satisfaction_score(self, obj):
        return get_trainee_coaches_user_score(
            self.context, 'satisfaction_score', target_user=obj)

    def get_recommend_score(self, obj):
        avg_score = CoachAppraise.objects.filter(
            interview__public_attr__target_user=obj,
            interview__public_attr__target_user__unionid__isnull=False,
            interview__public_attr__user=self.context, deleted=False
        ).aggregate(score_avg=Avg('score'))['score_avg']
        return round(avg_score, 1) if avg_score else 0

    class Meta:
        model = User
        fields = ['user_id', 'name', 'phone', 'tutoring_count', 'tutoring_time', 'head_image_url',
                  'target_progress_score', 'harvest_score', 'satisfaction_score', 'recommend_score']
