# 辅导渠道分类
# 群智平台：1
# 其他平台：2
# 线下：3
INTERVIEW_CATEGORY_QZ = 1
INTERVIEW_CATEGORY_ONLINE_OTHER = 2
INTERVIEW_CATEGORY_OFFLINE = 3


# 辅导类型
# 教练辅导： 1
# 利益相关访谈： 4
# 化学面谈：3
# 企业面谈：2
INTERVIEW_TYPE_COACHING = 1
INTERVIEW_TYPE_STAKEHOLDER = 4
INTERVIEW_TYPE_CHEMISTRY = 3
INTERVIEW_TYPE_COMPANY = 2


# 公共数据-类型
# 辅导：1
# 日程：2
# 笔记： 3
# 习惯：4
# 行动计划： 5
# 项目笔记： 6
# 学习记录：7
# 测评结果：8
# 测评报告：9
# 成长目标: 10
# 教练任务: 11
# 改变观察反馈 12
# 利益相关者调研 13
ATTR_TYPE_INTERVIEW = 1
ATTR_TYPE_SCHEDULE = 2
ATTR_TYPE_DIARY = 3
ATTR_TYPE_HABIT = 4
ATTR_TYPE_ACTIONPLAN = 5
ATTR_TYPE_PROJECTNOTE = 6
ATTR_TYPE_LEARN = 7
ATTR_TYPE_EVALUATION_ANSWER = 8
ATTR_TYPE_EVALUATION_REPORT = 9
ATTR_TYPE_GROWTH_GOALS = 10
ATTR_TYPE_COACH_TASK = 11
ATTR_TYPE_CHANGE_OBSERVATION = 12
ATTR_TYPE_STAKEHOLDER_COACH_TASK = 13
ATTR_TYPE_ORDER = 14


# 公共数据-辅导类型-状态
# 已确认：3
# 待记录： 4
# 完成辅导：5
# 取消： 6
ATTR_STATUS_INTERVIEW_CONFIRM = 3
ATTR_STATUS_INTERVIEW_RECORD_NEEDED = 4
ATTR_STATUS_INTERVIEW_COACHING_FINISHED = 5
ATTR_STATUS_INTERVIEW_CANCEL = 6

# 管理后台-辅导状态
# 未开始：1
# 进行中： 2
# 完成： 3
# 取消：4
# 教练未确认：5
ADMIN_INTERVIEW_NOT_START = 1
ADMIN_INTERVIEW_ONGOING = 2
ADMIN_INTERVIEW_END = 3
ADMIN_INTERVIEW_CANCEL = 4
ADMIN_COACH_AGREE = 5


# 日程类型
# 0：正式约谈
# 1：企业面试
# 2：化学面试
# 3：公开课
# 4：企业服务
# 5：其他活动
# 6：忙碌，教练其他日程
# 7: 利益相关者约谈
SCHEDULE_TYPE_INTERVIEW_COACHING = 0
SCHEDULE_TYPE_INTERVIEW_COMPANY = 1
SCHEDULE_TYPE_INTERVIEW_CHEMISTRY = 2
SCHEDULE_TYPE_INTERVIEW_STAKEHOLDER = 3
SCHEDULE_TYPE_OPEN_CLASS = 4
SCHEDULE_TYPE_COMPANY_BUSINESS = 5
SCHEDULE_TYPE_OTHER_ACTIVITY = 6
SCHEDULE_TYPE_BUSY = 7


# 项目成员角色
# 3：项目管理员
# 4：教练
# 5：企业管理员
# 6：被教练者
ROLE_PROJECT_ADMIN = 3
ROLE_COACH = 4
ROLE_COMPANY_MANAGER = 5
ROLE_COACHEE = 6


# 笔记填写来源类型
# 1：自己添加
# 2：辅导记录中添加
DIARY_FROM_SELF = 1
DIARY_FROM_RECORD = 2

# 文章类型
# 1：学习文章
# 2：说明文章 没有对应标签，不显示学习时长
ARTICLE_CATEGORY_STUDY = 1
ARTICLE_CATEGORY_INTRO = 2

# 日程类型 1:日程 2:约谈 3:忙碌
SCHEDULE_TYPE_SCHEDULE = 1
SCHEDULE_TYPE_INTERVIEW = 2
SCHEDULE_TYPE_OTHER = 3

# 日程来源 1：移动端 2：v1版本网站 3：用户手机日历
SCHEDULE_PLATFORM_DEFAULT = 1
SCHEDULE_PLATFORM_V1 = 2
SCHEDULE_PLATFORM_USER = 3


# 标签选择类型
# 1：单选
# 2：多选
TAG_SELECT_TYPE_SINGLE = 1
TAG_SELECT_TYPE_MULTI = 2

# 标签来源类型
# 1：系统自带
# 2：自定义
TAG_SOURCE_SYSTEM = 1
TAG_SOURCE_CUSTOM = 2

# 项目功能配置中辅导类型
# 0：线上集体辅导； 1: 线上1v1；2：线下1v1；3：线下集体辅导
BUNDLE_INTEWVIEW_TYPE_ONLINE_GROUP = 0
BUNDLE_INTEWVIEW_TYPE_ONLINE_1V1 = 1
BUNDLE_INTEWVIEW_TYPE_OFFLINE_1V1 = 2
BUNDLE_INTEWVIEW_TYPE_OFFLINE_GROUP = 3

BIZ_CODE_EVALUATION_REPORT_COUNT_LIMIT = 901
BIZ_CODE_USER_DISABLE_LOGIN = 902


# 企业微信部门类型
# 1：教练
# 2：项目运营
WORK_WECHAT_COACH = 1
WORK_WECHAT_CONSULTANT = 2

# 用户角色
# 1：超级管理员
# 2：公开课运营
# 3：项目运营
# 4：内容运营
# 5：企业管理员
# 6：客户顾问
# 7：财务
ADMIN = 1
PUBLIC_COURSES = 2
PROJECT_OPERATION = 3
PROJECT_COMPANY_ADMIN = 5
PROJECT_ACCOUNT_MANAGER = 6
FINANCE = 7


# 测评模板编号
# 101 管理型测评
# 102 LBI测评
MANAGE_EVALUATION = 101
LBI_EVALUATION = 102

# 管理后台-发送通知-渠道类型
# 发送渠道 1-企业微信 2-短信 3-邮件
ADMIN_SEND_WORK_WECHAT = 1
ADMIN_SEND_SMS = 2
ADMIN_SEND_EMAIL = 3

# 辅导明细-用户类型
# 1：个人用户
# 2：项目用户
ADMIN_PERSONAL_USER = 1
ADMIN_PROJECT_USER = 2

# 管理后台订单状态
# 1：待付款
# 2：已关闭
# 3：已完成
# 4：已退款
ADMIN_ORDER_STATE_PENDING_PAY = 1
ADMIN_ORDER_STATE_CLOSURE = 2
ADMIN_ORDER_STATE_COMPLETE = 3
ADMIN_ORDER_STATE_REFUND = 4

# 日程时间间隔
SCHEDULE_INTERVAL_MINUTES = 15

# 管理后台，自动生成订单时的默认订单税点, 单位：%
ORDER_TAX_POINT = 3
NON_ORDER_TAX_POINT = 0

# 管理后台-自动生成C端订单时的平台服务费, 单位：%
PLATFORM_SERVICE_SCALE = 22
NON_PLATFORM_SERVICE_SCALE = 0


# 管理后台，问卷选择题题目对应分数
TITLE_TO_SCORE = {
    "非常棒": 10,
    "很好": 8,
    "还不错": 6,
    "尚可": 4,
    "一般": 2,
    "完全同意": 10,
    "比较同意": 8,
    "中立": 6,
    "不太同意": 4,
    "不同意": 2,
}

# 开屏信息收集-用户选项转成对应评分
USER_INFO_TO_SCORE = {
    "非常了解": 5,
    "了解": 4,
    "不确定": 3,
    "不了解": 2,
    "完全不了解": 1,
    "非常重要": 5,
    "重要": 4,
    "不重要": 2,
    "完全不重要": 1,
    "非常同意": 5,
    "同意": 4,
    "不同意": 2,
    "非常不同意": 1,
    "非常满意": 5,
    "满意": 4,
    "一般": 3,
    "不太满意": 2,
    "非常不满意": 1,
    "领先": 5,
    "稍好": 4,
    "相差无几": 3,
    "稍差": 2,
    "落后": 1,
    "非常符合": 5,
    "符合": 4,
    "中立": 3,
    "不符合": 2,
    "非常不符合": 1
}

# 获取不同分数对应的星星字符串
INT_TO_SYMBOL_STR = {
    1: "★☆☆☆☆",
    2: '★★☆☆☆',
    3: '★★★☆☆',
    4: '★★★★☆',
    5: '★★★★★'
}

# 辅导会议状态
UPDATE_INTERVIEW_MEETING = 1
CANCEL_INTERVIEW_MEETING = 2

GENDER_TYPE = {
    1: "男性",
    2: "女性"
}

# 测试用户名称
TEST_USER_NAME = 'testuser__'

CUSTOMIZED_PROJECT_IDS = [169, 189]

# 管理后台展示需要排除的教练任务，不显示
# 小程序需要特殊处理的教练任务
EXCLUDE_COACH_TASK_TEMPLATE_NAME = "利益相关者访谈总结"
