from rest_framework import serializers
from wisdom_v2.models import PersonalReport


class PersonalReportSerializer(serializers.ModelSerializer):
    id = serializers.IntegerField(help_text='报告id')
    name = serializers.CharField(help_text='报告名称')
    type = serializers.IntegerField(help_text='报告类型')
    object_id = serializers.IntegerField(help_text='报告对象id')
    user_id = serializers.IntegerField(help_text='用户id')
    project_id = serializers.IntegerField(help_text='项目id')
    pdf_url = serializers.CharField(help_text='报告pdf链接')
    content = serializers.JSONField(help_text='报告内容')

    class Meta:
        model = PersonalReport
        fields = ['id', 'name', 'type', 'object_id', 'user_id', 'project_id', 'pdf_url', 'content']
