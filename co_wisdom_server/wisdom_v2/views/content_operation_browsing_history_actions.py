from rest_framework import serializers
from wisdom_v2.models import ContentOperationBrowsingHistory


class ContentOperationBrowsingHistorySerializers(serializers.ModelSerializer):

    name = serializers.CharField(source='user.name', help_text='用户名', read_only=True)
    true_name = serializers.CharField(source='user.cover_name', help_text='姓名', read_only=True)
    project_name = serializers.CharField(source='project.name', help_text='项目名', read_only=True)
    short = serializers.CharField(source='project.company.short', help_text='公司简称', read_only=True)

    class Meta:
        model = ContentOperationBrowsingHistory
        exclude = ('updated_at', 'created_at', 'user', 'article', 'project')
