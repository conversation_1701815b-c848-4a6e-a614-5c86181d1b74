from drf_yasg import openapi
from rest_framework.decorators import action

from drf_yasg.utils import swagger_auto_schema

from rest_framework.views import APIView
from rest_framework import mixins
from rest_framework import serializers
from rest_framework.viewsets import GenericViewSet
from rest_framework.response import Response


class QuestionViewSet(GenericViewSet):

    @swagger_auto_schema(
        operation_id='问题列表',
        operation_summary='问题列表',
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'param': openapi.Schema(type=openapi.TYPE_NUMBER, description=''),
            }
        ),
        tags=['问题相关']
    )
    @action(methods=['post'], detail=False, url_path='list')
    def question_list(self, request, *args, **kwargs):
        return Response('ok')

    @swagger_auto_schema(
        operation_id='问题详情',
        operation_summary='问题详情',
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'question_id': openapi.Schema(type=openapi.TYPE_NUMBER, description='问题id'),
            }
        ),
        tags=['问题相关']
    )
    def create(self, request, *args, **kwargs):
        return Response('ok')

    @swagger_auto_schema(
        operation_id='修改问题信息',
        operation_summary='修改问题信息',
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'question_id': openapi.Schema(type=openapi.TYPE_NUMBER, description='问题id'),
            }
        ),
        tags=['问题相关']
    )
    @action(methods=['post'], detail=False)
    def interview_update(self, request, *args, **kwargs):
        return Response('ok')

