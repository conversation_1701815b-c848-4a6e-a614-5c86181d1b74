import xlrd
import openpyxl
import re

from rest_framework import serializers
from django.db.models import Sum
from ..models import CompanyMember, ProjectMember, Project, User, Company
from .user_actions import UserSerializer
from .company_actions import CompanySerializers
from wisdom_v2.enum.user_enum import area_code_dic


class CompanyMemberSerializers(serializers.ModelSerializer):

    user_id = serializers.IntegerField(required=False)
    company_id = serializers.IntegerField(required=False)
    user = UserSerializer(read_only=True)
    company = CompanySerializers(read_only=True)
    project_info = serializers.SerializerMethodField(read_only=True)

    class Meta:
        model = CompanyMember
        exclude = ('created_at', 'updated_at')

    def get_project_info(self, obj):
        try:
            project_ids = ProjectMember.objects.filter(user=obj.user, deleted=False).values_list('project_id', flat=True)
            project_data = []
            for pid in project_ids:
                project = Project.objects.get(pk=pid)
                member = ProjectMember.objects.get(user=obj.user, project_id=pid)
                times_minute = ProjectMember.objects.filter(project=project, deleted=False,
                                                            all_interview_time__isnull=False
                                                            ).exclude(all_interview_time=-1).aggregate(
                    times_minute=Sum('all_interview_time'))['times_minute']
                if times_minute and project.all_times:
                    remain_hours = round((project.all_times - times_minute) / 60, 1)
                else:
                    remain_hours = 0
                project_data.append(
                    {'week_interview_count': member.week_interview_count,
                     'one_interview_time': member.one_interview_time,
                     'all_interview_time': member.all_interview_time,
                     'project_name': project.name,
                     'all_times': project.all_times,
                     'remain_hours': remain_hours,
                     'project_id': project.pk,
                     'member_id': member.pk})
            return project_data
        except Exception as e:
            print(e)
            return []

def id_number_check(id_number):
    if id_number is None:
        return False
    if len(id_number) != 18:
        return False
    if not (id_number[0:17].isdigit()):
        return False

    if (int(id_number[6:10]) % 4 == 0 or (int(id_number[6:10]) % 100 == 0 and int(id_number[6:10]) % 4 == 0)):
        # 出生日期闰年时合法性正则表达式
        birthday = re.compile(
            '[1-9][0-9]{5}(19[0-9]{2}|20[0-9]{2})((01|03|05|07|08|10|12)(0[1-9]|[1-2][0-9]|3[0-1])|(04|06|09|11)(0[1-9]|[1-2][0-9]|30)|02(0[1-9]|[1-2][0-9]))[0-9]{3}[0-9Xx]$')
    else:
        # 出生日期平年时合法性正则表达式
        birthday = re.compile(
            '[1-9][0-9]{5}(19[0-9]{2}|20[0-9]{2})((01|03|05|07|08|10|12)(0[1-9]|[1-2][0-9]|3[0-1])|(04|06|09|11)(0[1-9]|[1-2][0-9]|30)|02(0[1-9]|1[0-9]|2[0-8]))[0-9]{3}[0-9Xx]$')
    if not (re.match(birthday, id_number)):
        return False

    mod = [7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2]
    jym = ['1', '0', 'X', '9', '8', '7', '6', '5', '4', '3', '2']
    sum = 0
    for i in range(0, 17):
        sum += int(id_number[i]) * mod[i]
    sum %= 11
    if (jym[sum]) == id_number[17]:
        return True
    else:
        return False