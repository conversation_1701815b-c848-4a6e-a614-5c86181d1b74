import random
from datetime import datetime

from django.db.models import Sum
from rest_framework import serializers
from django.db import transaction

from utils import multiple_selection_map
from utils.api_response import WisdomValidationError
from wisdom_v2.enum.chemical_interview_enum import ChemicalInterviewCoachSourceEnum, ChemicalInterviewStatusEnum
from wisdom_v2.enum.project_interview_enum import ProjectInterviewPlaceCategoryEnum, ProjectInterviewTypeEnum
from wisdom_v2.enum.service_content_enum import CoachOffer<PERSON>tatusEnum
from wisdom_v2.models_file import ChemicalInterviewModule, ChemicalInterview2Coach
from wisdom_v2.models import ProjectInterview, CoachOffer, ProjectCoach, ProjectMember, Coach, Resume, ProjectBundle
from wisdom_v2.views.constant import ATTR_STATUS_INTERVIEW_CANCEL, INTERVIEW_TYPE_COACHING


class ChemicalInterview2CoachSerializer(serializers.ModelSerializer):
    id = serializers.CharField(read_only=True)
    coachee_name = serializers.Char<PERSON>ield(source='interview.public_attr.target_user.cover_name', read_only=True)
    coach_name = serializers.CharField(source='interview.public_attr.user.cover_name', read_only=True)
    interview_time = serializers.SerializerMethodField(read_only=True)
    chemical_interview_status = serializers.IntegerField(help_text='状态')
    coach_aspect = serializers.CharField(help_text='教练特点')
    coach_impression = serializers.CharField(help_text='教练反馈')

    class Meta:
        model = ChemicalInterview2Coach
        fields = ('id', 'coachee_name', 'coach_name', 'interview_time', 'chemical_interview_status',
                  'coach_aspect', 'coach_impression')

    def get_interview_time(self, obj):
        return f"{obj.interview.public_attr.start_time.strftime('%Y.%m.%d %H:%M')}-{obj.interview.public_attr.end_time.strftime('%H:%M')}"


class ChemicalInterviewModuleRandomCoachListSerializer(serializers.ModelSerializer):
    id = serializers.IntegerField(read_only=True)
    true_name = serializers.CharField(source='user.cover_name', read_only=True)
    is_appointment = serializers.SerializerMethodField(help_text='是否预约过', read_only=True)
    customer_count = serializers.SerializerMethodField(help_text='剩余可服务客户数量', read_only=True)

    class Meta:
        model = Coach
        fields = ('id', 'true_name', 'is_appointment', 'customer_count')

    def get_is_appointment(self, obj):
        user_id = self.context.get('user_id')
        project_id = self.context.get('project_id')
        return get_chemical_interview_is_appointment(obj.user_id, user_id, project_id)

    def get_customer_count(self, obj):
        project_id = self.context.get('project_id')
        return get_coach_customer_count(obj.pk, project_id)


class ChemicalInterviewModuleCoachListSerializer(serializers.ModelSerializer):
    id = serializers.IntegerField(read_only=True)
    user_id = serializers.IntegerField(read_only=True)
    true_name = serializers.CharField(source='user.cover_name')
    coach_type = serializers.IntegerField(read_only=True, help_text='教练类型')
    order_receiving_status = serializers.BooleanField(read_only=True, help_text='接单状态')
    gender = serializers.IntegerField(source='user.gender', help_text='性别', read_only=True)
    coach_info = serializers.SerializerMethodField(read_only=True, help_text='简历信息')
    one_to_one_interview_time = serializers.SerializerMethodField(read_only=True)
    is_set = serializers.SerializerMethodField(read_only=True, help_text='是否绑定项目')
    is_appointment = serializers.SerializerMethodField(read_only=True, help_text='是否预约过')
    customer_count = serializers.SerializerMethodField(read_only=True, help_text='剩余可预约客户数量')

    class Meta:
        model = Coach
        fields = ('id', 'user_id', 'coach_type', 'order_receiving_status', 'true_name',
                  'gender', 'coach_info', 'one_to_one_interview_time', 'is_set', 'is_appointment', 'customer_count')

    def get_coach_info(self, obj):
        resume = Resume.objects.filter(coach_id=obj.pk, deleted=False, is_customization=False).first()
        data = {}
        data['coach_auth'] = resume.coach_auth if resume.coach_auth else None
        data['working_years'] = resume.working_years if resume.working_years else None
        data['resume_id'] = resume.pk
        data['coach_domain'] = multiple_selection_map.get_multiple_selection_detail('coach_domain',
                                                                                    resume.coach_domain)['show_text']
        return data

    def get_one_to_one_interview_time(self, obj):
        times = ProjectInterview.objects.filter(
            type=INTERVIEW_TYPE_COACHING,
            place_category=ProjectInterviewPlaceCategoryEnum.online_one_to_one.value,
            public_attr__type=1, deleted=False,
            public_attr__end_time__lt=datetime.now(),
            public_attr__user_id=obj.user_id).exclude(
            public_attr__status=ATTR_STATUS_INTERVIEW_CANCEL).aggregate(times_minute=Sum('times'))
        if times['times_minute']:
            interview_hour = round(times['times_minute'] / 60, 1)
        else:
            interview_hour = 0
        resume = Resume.objects.filter(coach=obj, deleted=False, is_customization=False).first()
        one_to_one_interview_time = resume.one_to_one_interview_time if resume.one_to_one_interview_time else 0
        return interview_hour + one_to_one_interview_time

    def get_is_set(self, obj):
        project_member_id = self.context.get('project_member_id')
        project_member = ProjectMember.objects.get(pk=project_member_id)
        chemical_interview_module = project_member.chemical_interview.filter(deleted=False).first()
        if chemical_interview_module:
            if chemical_interview_module.coaches.filter(coach_id=obj.id, deleted=False).exists():
                return True
        return False

    def get_is_appointment(self, obj):
        user_id = self.context.get('user_id')
        project_id = self.context.get('project_id')
        return get_chemical_interview_is_appointment(obj.user_id, user_id, project_id)

    def get_customer_count(self, obj):
        project_id = self.context.get('project_id')
        return get_coach_customer_count(obj.pk, project_id)


class ChemicalInterviewModuleUpdateSerializer(serializers.ModelSerializer):
    id = serializers.CharField(help_text='id', write_only=True, required=True)
    max_interview_number = serializers.IntegerField(help_text='最大面谈次数', write_only=True, required=False)
    duration = serializers.FloatField(help_text='面谈时长', write_only=True, required=False)
    start_time = serializers.DateField(help_text='开始日期', write_only=True, required=False)
    end_time = serializers.DateField(help_text='结束日期', write_only=True, required=False)
    coach_source = serializers.IntegerField(help_text='教练来源', write_only=True, required=False)
    coach = serializers.ListField(help_text='教练id列表', write_only=True, required=False)
    deleted = serializers.BooleanField(help_text='是否删除', write_only=True, required=False)

    class Meta:
        model = ChemicalInterviewModule
        fields = ['id', 'max_interview_number', 'duration', 'start_time', 'end_time', 'coach_source', 'coach',
                  'deleted']

    def update(self, instance, validated_data):
        validated_data.pop('id')
        if 'coach' in validated_data.keys():
            coach = validated_data.pop('coach')
        else:
            coach = None
        deleted = validated_data.get('deleted')
        with transaction.atomic():
            if deleted:
                if instance.coaches.filter(deleted=False, interview__isnull=False).exists():
                    raise WisdomValidationError(f'用户已预约化学面谈，不可删除')
                instance.coaches.filter(deleted=False).update(deleted=True)
            else:
                if isinstance(coach, list):
                    exists_coach_ids = list(instance.coaches.filter(deleted=False).values_list('coach_id', flat=True))
                    deleted_list = list(set(exists_coach_ids).difference(set(coach)))
                    add_list = list(set(coach).difference(set(exists_coach_ids)))
                    if deleted_list:
                        for coach_id in deleted_list:
                            chemical_interview = ChemicalInterview2Coach.objects.filter(
                                coach_id=coach_id, chemical_interview_module=instance,
                                deleted=False, interview__isnull=False).first()
                            if chemical_interview:
                                raise WisdomValidationError(f'用户已与{chemical_interview.coach.user.cover_name}教练预约化学面谈，'
                                                            f'不可删除')
                        ChemicalInterview2Coach.objects.filter(coach_id__in=deleted_list, chemical_interview_module=instance,
                                                               deleted=False, interview__isnull=True).update(deleted=True)

                    if add_list:
                        with transaction.atomic():
                            for coach_id in add_list:
                                ChemicalInterview2Coach.objects.create(
                                    chemical_interview_module=instance, coach_id=coach_id,
                                    chemical_interview_status=ChemicalInterviewStatusEnum.not_feedback)

            super().update(instance=instance, validated_data=validated_data)
        return instance


class ChemicalInterviewModuleCreateSerializer(serializers.ModelSerializer):
    project_member_id = serializers.IntegerField(help_text='被教练者id', write_only=True, required=True)
    max_interview_number = serializers.IntegerField(help_text='最大面谈次数', write_only=True, required=False)
    duration = serializers.FloatField(help_text='面谈时长', write_only=True, required=True)
    start_time = serializers.DateField(help_text='开始日期', write_only=True, required=True)
    end_time = serializers.DateField(help_text='结束日期', write_only=True, required=True)
    coach_source = serializers.IntegerField(help_text='教练来源', write_only=True, required=True)
    coach = serializers.ListField(help_text='教练id列表', write_only=True, required=False)

    class Meta:
        model = ChemicalInterviewModule
        fields = ['id', 'max_interview_number', 'duration', 'start_time', 'end_time', 'coach_source', 'coach',
                  'project_member_id']

    def create(self, validated_data):
        project_member_id = validated_data.get('project_member_id')
        project_member = ProjectMember.objects.filter(pk=project_member_id).first()

        if not project_member:
            raise WisdomValidationError('未获取到被教练者信息')
        if project_member.chemical_interview.filter(deleted=False).exists():
            raise WisdomValidationError('当前被教练者已配置化学面谈，请勿重复配置')
        max_interview_number = validated_data.get('max_interview_number')
        if max_interview_number and max_interview_number not in [1, 2, 3, 4, 5]:
            raise WisdomValidationError('最多面谈5次最少1次')
        duration = validated_data.get('duration')
        if duration not in [0.5, 1, 1.5, 2]:
            raise WisdomValidationError('化学面谈时长仅支持 0.5, 1, 1.5, 2小时')
        coach_source = validated_data.get('coach_source')
        coach = validated_data.get('coach')
        if coach_source == ChemicalInterviewCoachSourceEnum.system_random:
            if not coach:
                raise WisdomValidationError('系统随机教练来源时必须选择教练')
        start_time = validated_data.get('start_time')
        end_time = validated_data.get('end_time')
        with transaction.atomic():
            instance = ChemicalInterviewModule.objects.create(
                project_member=project_member, max_interview_number=max_interview_number, duration=duration,
                start_time=start_time, end_time=end_time, coach_source=coach_source)
            if coach:
                for c in coach:
                    # 状态为待定，辅导时间接结束后修改为未反馈
                    ChemicalInterview2Coach.objects.create(
                        chemical_interview_module=instance, coach_id=c,
                        chemical_interview_status=ChemicalInterviewStatusEnum.not_feedback)
            if not project_member.project_bundle.filter(deleted=False).exists():
                ProjectBundle.objects.create(project=project_member.project, project_member=project_member)
        return instance


class ChemicalInterviewModuleSerializer(serializers.ModelSerializer):
    id = serializers.CharField(help_text='id')
    project_member_id = serializers.IntegerField(help_text='被教练者id')
    max_interview_number = serializers.IntegerField(help_text='最多面谈次数')
    duration = serializers.FloatField(help_text='化学面谈时长 只能填 0.5,1,1.5,2')
    start_time = serializers.DateField(format='%Y-%m-%d')
    end_time = serializers.DateField(format='%Y-%m-%d')
    coach_source = serializers.IntegerField(help_text='教练来源 1-系统随机 2-自主选择')
    coach = serializers.SerializerMethodField(help_text='匹配教练信息列表')

    class Meta:
        model = ChemicalInterviewModule
        fields = ('id', 'project_member_id', 'max_interview_number', 'duration', 'start_time', 'end_time',
                  'coach_source', 'coach')

    def get_coach(self, obj):
        coach_list = []
        if obj.coaches.filter(deleted=False).exists():
            coaches = obj.coaches.filter(deleted=False)
            for coach in coaches:
                coach_offer = CoachOffer.objects.filter(coach_id=coach.coach_id, status=CoachOfferStatusEnum.joined, deleted=False,
                                            project_offer__project_id=obj.project_member.project_id, project_offer__deleted=False).first()
                max_customer_count = coach_offer.max_customer_count if coach_offer else None
                # 查询该教练在当前项目中已配置的化学面谈客户数量
                chemical_interview_count = ChemicalInterview2Coach.objects.filter(
                    coach_id=coach.coach_id,
                    chemical_interview_module__project_member__project_id=obj.project_member.project_id,
                    deleted=False,
                    chemical_interview_module__deleted=False
                ).values('chemical_interview_module__project_member_id').distinct().count()
                # 可服务客户数 = 最大服务客户数 - 已匹配客户数
                customer_count = get_coach_customer_count(coach.coach_id, obj.project_member.project_id)
                # 当前可服务客户数 = 可服务客户数 - 可被预约客户数
                # 可被预约客户 = 没有匹配教练，并且可以预约当前教练
                can_be_appointment_project_member_ids = ChemicalInterview2Coach.objects.filter(
                    coach_id=coach.coach_id, interview__isnull=True, deleted=False,
                    chemical_interview_module__project_member__project_id=obj.project_member.project_id,
                    chemical_interview_module__deleted=False
                ).values('chemical_interview_module__project_member_id')
                # 已匹配的客户
                matched_project_member_ids = ProjectCoach.objects.filter(
                    project_id=obj.project_member.project_id,
                    member_id__in=can_be_appointment_project_member_ids, deleted=False, project_group_coach__isnull=True).values_list('member_id', flat=True)
                if customer_count.isdigit():
                    current_customer_count = int(customer_count) - (can_be_appointment_project_member_ids.count() - matched_project_member_ids.count())
                else:
                    current_customer_count = '未设置'
                
                data = {
                    "id": coach.coach_id,
                    "true_name": coach.coach.user.cover_name,
                    "is_appointment": get_chemical_interview_is_appointment(coach.coach.user_id,
                                                                            obj.project_member.user_id,
                                                                            obj.project_member.project_id),
                    "customer_count": customer_count, 
                    "max_customer_count": max_customer_count,  # 教练最大服务客户数
                    "config_count": chemical_interview_count,
                    "current_customer_count": current_customer_count
                }
                coach_list.append(data)
        return coach_list


def get_chemical_interview_is_appointment(coach_user_id, user_id, project_id):
    """
    获取当前用户与当前教练在当前项目下是否约过化学面谈
    coach_user_id: 教练用户id
    user_id: 用户id
    project_id: 项目ID
    """
    return ProjectInterview.objects.filter(
        place_category=ProjectInterviewPlaceCategoryEnum.online_one_to_one,
        type=ProjectInterviewTypeEnum.chemical_interview, deleted=False, public_attr__project_id=project_id,
        public_attr__user_id=coach_user_id, public_attr__target_user_id=user_id).exclude(
        public_attr__status=ATTR_STATUS_INTERVIEW_CANCEL).exists()


def get_coach_customer_count(coach_id, project_id):
    """
    获取教练在项目下剩余可服务的客户数
    coach_id: 教练id
    project_id: 项目id
    """
    coach_offer = CoachOffer.objects.filter(coach_id=coach_id, status=CoachOfferStatusEnum.joined, deleted=False,
                                            project_offer__project_id=project_id, project_offer__deleted=False,
                                            project_offer__project__deleted=False).first()
    if coach_offer and coach_offer.max_customer_count:
        matched_count = ProjectCoach.objects.filter(project_id=project_id, coach_id=coach_id,
                                                    member_id__isnull=False, deleted=False,
                                                    project_group_coach__isnull=True).count()
        return str(coach_offer.max_customer_count - matched_count)
    else:
        return '未设置'


def get_random_coach(project_member_id, project_id, max_interview_number):
    coach_ids = list(ChemicalInterview2Coach.objects.filter(
        chemical_interview_module__project_member_id=project_member_id, deleted=False,
        chemical_interview_module__deleted=False).values_list('coach_id', flat=True))
    max_interview_number = int(max_interview_number)
    coach = Coach.objects.filter(
        deleted=False, user_coach__project_id=project_id,
        user_coach__deleted=False,
        user_coach__resume__isnull=False).exclude(id__in=coach_ids)
    coach_list = []
    for c in coach:
        if c.offers.exists():
            coach_offer = CoachOffer.objects.filter(project_offer__project_id=project_id, coach=c,
                                                    status=CoachOfferStatusEnum.joined, deleted=False,
                                                    project_offer__deleted=False).first()
            if coach_offer:
                count = ChemicalInterview2Coach.objects.filter(
                    coach=c, deleted=False, chemical_interview_module__project_member__project_id=project_id). \
                    exclude(chemical_interview_status=ChemicalInterviewStatusEnum.unselected).count()
                if not coach_offer.max_customer_count or count < coach_offer.max_customer_count:
                    coach_list.append(c)
            else:
                coach_list.append(c)
        else:
            coach_list.append(c)
    if len(coach_list) <= max_interview_number:
        return coach_list

    random.shuffle(coach_list)
    coach_list = coach_list[:max_interview_number]
    return coach_list

