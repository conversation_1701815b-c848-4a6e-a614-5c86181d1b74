from drf_yasg import openapi
from drf_yasg.utils import swagger_auto_schema

from wisdom_v2.views.interview_answer_actions import InterviewRecordTemplateAnswerSerializers
from wisdom_v2.models import InterviewRecordTemplateAnswer
from rest_framework import viewsets

from utils.api_response import success_response


class InterviewRecordTemplateAnswerViewSet(viewsets.ModelViewSet):
    queryset = InterviewRecordTemplateAnswer.objects.filter().order_by('-id')
    serializer_class = InterviewRecordTemplateAnswerSerializers

    @swagger_auto_schema(
        operation_id='创建辅导记录回答',
        operation_summary='创建辅导记录回答',
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'interview': openapi.Schema(type=openapi.TYPE_NUMBER, description='用户id'),
                'public_attr': openapi.Schema(type=openapi.TYPE_NUMBER, description='基础数据id'),
                'option': openapi.Schema(type=openapi.TYPE_NUMBER, description='选择题代表被选择，输入题答案记录在answer字段'),
                'question': openapi.Schema(type=openapi.TYPE_NUMBER, description='填空和评价没有option，只有question'),
                'required': openapi.Schema(type=openapi.TYPE_NUMBER, description='评分类型|1:有效度，2:投入度，3:满意度'),
                'answer': openapi.Schema(type=openapi.TYPE_STRING, description='填空类型问题的回答'),
                'option_custom': openapi.Schema(type=openapi.TYPE_STRING, description='单选多选类型问题的自定义内容'),
                'score': openapi.Schema(type=openapi.TYPE_NUMBER, description='打分类型问题的分数'),
            }
        ),
        tags=['辅导记录回答相关']
    )
    def create(self, request, *args, **kwargs):
        return success_response(super(InterviewRecordTemplateAnswerViewSet, self).create(request, *args, **kwargs).data)
