import time, datetime
from rest_framework.views import APIView

from drf_yasg.utils import swagger_auto_schema
from utils.api_response import success_response

import os

from cozepy import COZE_COM_BASE_URL
from cozepy.auth import JWTAuth

# The default access is api.coze.com, but if you need to access api.coze.cn,
# please use base_url to configure the api endpoint to access
coze_api_base = 'https://api.coze.cn'

# client ID
jwt_oauth_client_id = os.getenv("COZE_JWT_OAUTH_CLIENT_ID")
# private key
# jwt_oauth_private_key = os.getenv("COZE_JWT_OAUTH_PRIVATE_KEY")
# path to the private key file (usually with .pem extension)
jwt_oauth_private_key_file_path = os.getenv("COZE_JWT_OAUTH_PRIVATE_KEY_FILE_PATH")
# public key id
jwt_oauth_public_key_id = os.getenv("COZE_JWT_OAUTH_PUBLIC_KEY_ID")


if jwt_oauth_private_key_file_path:
    with open(jwt_oauth_private_key_file_path, "r") as f:
        jwt_oauth_private_key = f.read()





class CozeView(APIView):
    authentication_classes = []

    def get(self, request, *args, **kwargs):
        # The sdk offers the JWTOAuthApp class to establish an authorization for Service OAuth.
        # Firstly, it is required to initialize the JWTOAuthApp.
        # Get session_name from request parameters
        session_name = request.GET.get('session_name', None)

        from cozepy import Coze, TokenAuth, JWTOAuthApp  # noqa
        # print(coze_api_base)
        # print(jwt_oauth_client_id)
        # print(jwt_oauth_private_key)
        # print(jwt_oauth_public_key_id)
        jwt_oauth_app = JWTOAuthApp(
            client_id=jwt_oauth_client_id,
            private_key=jwt_oauth_private_key,
            public_key_id=jwt_oauth_public_key_id,
            base_url=coze_api_base,
        )

        # The jwt oauth type requires using private to be able to issue a jwt token, and through
        # the jwt token, apply for an access_token from the coze service. The sdk encapsulates
        # this procedure, and only needs to use get_access_token to obtain the access_token under
        # the jwt oauth process.

        # Generate the authorization token
        # The default ttl is 900s, and developers can customize the expiration time, which can be
        # set up to 24 hours at most.
        oauth_token = jwt_oauth_app.get_access_token(ttl=3600, session_name=session_name)
        return success_response({'token': oauth_token}, request=request)

