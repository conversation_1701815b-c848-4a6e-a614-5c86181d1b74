from rest_framework import viewsets
from drf_yasg import openapi

from drf_yasg.utils import swagger_auto_schema

from .content_operation_project_actions import ContentProjectSerializers
from ..models import Project
from utils.pagination import StandardResultsSetPagination
from utils.api_response import success_response


class ContentProjectViewSet(viewsets.ModelViewSet):
    queryset = Project.objects.filter(deleted=False).order_by('-created_at')
    serializer_class = ContentProjectSerializers

    @swagger_auto_schema(
        operation_id='项目列表',
        operation_summary='项目列表',
        manual_parameters=[
            openapi.Parameter('page', openapi.IN_QUERY, description='第几页', type=openapi.TYPE_NUMBER),
            openapi.Parameter('page_size', openapi.IN_QUERY, description='每页显示条数默认15条', type=openapi.TYPE_NUMBER),
            openapi.Parameter('project_name', openapi.IN_QUERY, description='项目名称', type=openapi.TYPE_STRING)
        ],
        tags=['内容管理数据相关']
    )
    def list(self, request, *args, **kwargs):
        project = self.get_queryset()
        if request.query_params.get('project_name', None):
            project = project.filter(name__contains=request.query_params.get('project_name'))

        # 分页
        paginator = StandardResultsSetPagination()
        page_list = paginator.paginate_queryset(project, self.request)
        serializer = self.get_serializer(page_list, many=True)
        response = paginator.get_paginated_response(serializer.data)
        return success_response(response)
