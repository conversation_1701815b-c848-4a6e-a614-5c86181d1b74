from rest_framework.response import Response
from rest_framework.views import APIView

from django.conf import settings
from drf_yasg import openapi
from drf_yasg.utils import swagger_auto_schema

from django.db import transaction
from ..models import User, Project, Company, PublicAttr, ProjectInterview, CompanyMember, Coach
from data.models import SysUser, AppProbject, AppCompany, AppProbjectinterview, AppMember, AppCoach
from bs4 import BeautifulSoup


class UpdateModel(APIView):
    authentication_classes = []

    @swagger_auto_schema(
        operation_id='更新',
        operation_summary='更新',
        manual_parameters=[
            openapi.Parameter('update', openapi.IN_QUERY, description='update', type=openapi.TYPE_STRING),
        ],
        tags=['更新']
    )
    def get(self, request, *args, **kwargs):
        if request.query_params.get('update', 0) == '888888':
            try:
                with transaction.atomic():
                    coach = AppCoach.objects.all()
                    for co in coach:
                        sys_user = SysUser.objects.get(User_Id=co.User_Id)

                        new_user = User.objects.create(
                            name=sys_user.UserName,
                            true_name=sys_user.UserTrueName,
                            password=sys_user.UserPwd,
                            phone=sys_user.PhoneNo,
                            gender=sys_user.Gender,
                            email=sys_user.Email,
                            head_image_url=settings.SITE_URL + 'coapi/' + sys_user.HeadImageUrl
                        )
                        if co.overallIntroduction is not None:
                            brief = BeautifulSoup(co.overallIntroduction, 'html.parser').get_text()
                        else:
                            brief = None

                        if co.gzjl is not None:
                            work_experience = BeautifulSoup(co.gzjl, 'html.parser').get_text()
                        else:
                            work_experience = None
                        if co.coachstyle is not None:
                            style = BeautifulSoup(co.coachstyle, 'html.parser').get_text()
                        else:
                            style = None
                        Coach.objects.create(
                            user=new_user,
                            coach_experience=int(co.jlxssOne) + int(co.jlxssTearm),
                            working_years=co.cynx,
                            brief=brief,
                            work_experience=work_experience,
                            domain=co.scly,
                            style=style,
                            industry=co.jlhy,
                            qualification=co.jlzz
                        )

            except Exception as e:
                print('error', e)
            return Response('---end---')
        return Response('---ok---')
