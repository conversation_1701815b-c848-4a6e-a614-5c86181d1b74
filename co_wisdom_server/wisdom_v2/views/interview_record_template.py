from django.db import transaction
from django.db.models import When, Case, Q
from drf_yasg import openapi
from rest_framework.decorators import action
from drf_yasg.utils import swagger_auto_schema
from rest_framework import viewsets

from wisdom_v2.enum.service_content_enum import InterviewR<PERSON>ordTemplateQuestionTypeEnum as QuestionTypeEnum
from wisdom_v2.views.interview_record_template_action import InterViewRecordTemplateSerializers, \
    InterViewRecordTemplateSerializersCreate, InterViewRecordTemplateSerializersDetail
from wisdom_v2.models import InterviewRecordTemplate, InterviewRecordTemplateQuestion, InterviewRecordTemplateOption, \
    TotalTemplate
from utils.pagination import StandardResultsSetPagination
from utils.api_response import success_response, parameter_error_response


class InterViewRecordTemplateViewSet(viewsets.ModelViewSet):
    queryset = InterviewRecordTemplate.objects.filter(deleted=False).order_by('-created_at', '-updated_at')
    serializer_class = InterViewRecordTemplateSerializers
    authentication_classes = []

    @swagger_auto_schema(
        operation_id='辅导记录模版列表',
        operation_summary='辅导记录模版列表',
        manual_parameters=[
            openapi.Parameter('keyword', openapi.IN_QUERY, description='关键字（模版标题）', type=openapi.TYPE_STRING),
            openapi.Parameter('available', openapi.IN_QUERY, description='是否上下架', type=openapi.TYPE_BOOLEAN),
            openapi.Parameter('role', openapi.IN_QUERY, description='填写记录的角色 | 1：教练；2：被教练者，3：利益相关者', type=openapi.TYPE_STRING),
            openapi.Parameter('type', openapi.IN_QUERY, description='1-线下集体辅导 2-见习一对一 3-教练任务 4-化学面谈 5-利益相关者访谈', type=openapi.TYPE_NUMBER),
            openapi.Parameter('page', openapi.IN_QUERY, description='第几页', type=openapi.TYPE_NUMBER),
            openapi.Parameter('page_size', openapi.IN_QUERY, description='每页显示条数默认15条', type=openapi.TYPE_NUMBER),
        ],
        tags=['辅导记录模版相关']
    )
    def list(self, request, *args, **kwargs):
        interview_record_template = self.get_queryset()
        if request.query_params.get('keyword', None):
            interview_record_template = interview_record_template.filter(
                name__icontains=request.query_params.get('keyword'))
        if request.query_params.get('role', None):
            interview_record_template = interview_record_template.filter(
                role=request.query_params.get('role'))
        if request.query_params.get('available'):
            available = True if str(request.query_params.get('available')).lower() == 'true' else False
            interview_record_template = interview_record_template.filter(available=available)
        if request.query_params.get('type'):
            interview_record_template = interview_record_template.filter(type=request.query_params.get('type'))

        # 分页
        paginator = StandardResultsSetPagination()
        page_list = paginator.paginate_queryset(interview_record_template, self.request)
        serializer = self.get_serializer(page_list, many=True)
        response = paginator.get_paginated_response(serializer.data)
        return success_response(response)

    @swagger_auto_schema(
        operation_id='辅导记录模版详情',
        operation_summary='辅导记录模版详情',
        manual_parameters=[
            openapi.Parameter('interview_record_id', openapi.IN_QUERY, description='辅导记录模版id', type=openapi.TYPE_NUMBER),
        ],
        tags=['辅导记录模版相关']
    )
    @action(methods=['get'], detail=False, url_path='detail', authentication_classes=[])
    def article_detail(self, request, *args, **kwargs):
        try:
            interview_record = InterviewRecordTemplate.objects.get(pk=request.query_params.get('interview_record_id'))
        except InterviewRecordTemplate.DoesNotExist as e:
            return parameter_error_response()
        self.serializer_class = InterViewRecordTemplateSerializersDetail
        serializer = self.get_serializer(interview_record)
        return success_response(serializer.data)

    @swagger_auto_schema(
        operation_id='修改辅导记录模板信息',
        operation_summary='修改辅导记录模板信息',
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'interview_record_id': openapi.Schema(type=openapi.TYPE_STRING, description='辅导记录模版ID'),
                'name': openapi.Schema(type=openapi.TYPE_STRING, description='辅导记录模板名称'),
                'preface': openapi.Schema(type=openapi.TYPE_STRING, description='前言'),
                'type': openapi.Schema(type=openapi.TYPE_NUMBER, description='教练形式 | 1：线下集体辅导'),
                'role': openapi.Schema(type=openapi.TYPE_NUMBER, description='填写记录的角色 | 1：教练；2：被教练者, 3：利益相关者'),
                'available': openapi.Schema(type=openapi.TYPE_BOOLEAN, description='是否上架'),
                'deleted': openapi.Schema(type=openapi.TYPE_BOOLEAN, description='设置删除'),
            }
        ),
        tags=['辅导记录模版相关']
    )
    @action(methods=['post'], detail=False, url_path='update')
    def interview_update(self, request, *args, **kwargs):
        try:
            interview_record_template = InterviewRecordTemplate.objects.get(pk=request.data.get('interview_record_id'))

            if 'name' in request.data.keys():
                if not request.data.get('name'):
                    return parameter_error_response('标题不可为空')
                if len(request.data.get('name')) > 20:
                    return parameter_error_response('标题不可超过20个字')
                # 如果排除自身以后，模板名称出现重复，触发错误提示。
                if InterviewRecordTemplate.objects.filter(
                        name=request.data.get('name'), deleted=False).exclude(pk=interview_record_template.pk).exists():
                    return parameter_error_response('模版名称重复，请重新输入。')
        except InterviewRecordTemplate.DoesNotExist as e:
            return parameter_error_response()

        self.serializer_class = InterViewRecordTemplateSerializersCreate
        data = request.data.copy()
        if 'name' in data.keys():
            total_template = TotalTemplate.objects.filter(Q(coach_template_id=interview_record_template.pk) |
                                                          Q(coachee_template_id=interview_record_template.pk)|
                                                          Q(stakeholder_template_id=interview_record_template.pk)
                                                          )
            if total_template.exists():
                total_template = total_template.first()
                total_template.title = data['name']
                total_template.save()
                data.pop('name')
        serializer = self.get_serializer(interview_record_template, data=data, partial=True)
        serializer.is_valid(raise_exception=True)
        serializer.save()
        return success_response(serializer.data)

    @swagger_auto_schema(
        operation_id='复制辅导记录模板信息',
        operation_summary='复制辅导记录模板信息',
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'interview_record_id': openapi.Schema(type=openapi.TYPE_NUMBER, description='辅导记录模版ID'),
                'name': openapi.Schema(type=openapi.TYPE_STRING, description='模板名称'),
                'role': openapi.Schema(type=openapi.TYPE_NUMBER, description='填写记录的角色 | 1：教练；2：被教练者'),
            }
        ),
        tags=['辅导记录模版相关']
    )
    @action(methods=['post'], detail=False, url_path='copy')
    def interview_copy(self, request, *args, **kwargs):
        self.serializer_class = InterViewRecordTemplateSerializersCreate
        try:
            interview_record_template = InterviewRecordTemplate.objects.get(pk=request.data.get('interview_record_id'))
        except InterviewRecordTemplate.DoesNotExist as e:
            return parameter_error_response('主模版不存在')
        name = InterviewRecordTemplate.objects.filter(name=request.data.get('name'))
        if name:
            return parameter_error_response('模版名称重复，请重新输入。')
        # 创建新的元模板
        new_interview_record_template = InterviewRecordTemplate(
            name=request.data.get('name'),
            role=interview_record_template.role,
            type=interview_record_template.type,
            preface=interview_record_template.preface
        )
        new_interview_record_template.save()

        # 查询所有问题并按指定顺序排序
        order = Case(*[When(pk=order, then=pos) for pos, order in enumerate(eval(interview_record_template.questions_order))])
        questions = InterviewRecordTemplateQuestion.objects.filter(
            template_id=interview_record_template.pk, deleted=False).order_by(order).all()

        questions_order = []
        # 循环问题列表/增加事物控制
        with transaction.atomic():
            for question in questions:

                # 新建一个问题
                new_question = InterviewRecordTemplateQuestion(
                    template_id=new_interview_record_template.pk,
                    answer_type=question.answer_type,
                    rating_type=question.rating_type,
                    type=question.type,
                    title=question.title,
                    required=question.required
                )
                new_question.save()
                questions_order.append(new_question.pk)

                # 如果是选择题查询问题下所有选项
                if question.type in [QuestionTypeEnum.single, QuestionTypeEnum.multiple]:
                    options = InterviewRecordTemplateOption.objects.filter(question_id=question.pk).all()
                    # 批量添加新的选择题
                    new_options = []
                    for option in options:
                        new_options.append(InterviewRecordTemplateOption(
                            question_id=new_question.pk,
                            title=option.title,
                            user_defined=option.user_defined,
                            deleted=option.deleted,
                            order=option.order
                        ))
                    InterviewRecordTemplateOption.objects.bulk_create(new_options)
            new_interview_record_template.questions_order = str(questions_order)
            new_interview_record_template.save()

            serializer = self.get_serializer(new_interview_record_template)
        return success_response(serializer.data)

    @swagger_auto_schema(
        operation_id='创建辅导记录模板',
        operation_summary='创建辅导记录模板',
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'name': openapi.Schema(type=openapi.TYPE_STRING, description='辅导记录模板名称'),
                'preface': openapi.Schema(type=openapi.TYPE_STRING, description='前言'),
                'type': openapi.Schema(type=openapi.TYPE_NUMBER, description='教练形式 | 1-线下集体辅导 2-见习一对一 3-教练任务 4-化学面谈'),
                'role': openapi.Schema(type=openapi.TYPE_NUMBER, description='填写记录的角色 | 1：教练；2：被教练者,3：利益相关者'),
                'available': openapi.Schema(type=openapi.TYPE_BOOLEAN, description='是否上架'),
                'deleted': openapi.Schema(type=openapi.TYPE_BOOLEAN, description='设置删除'),
            }
        ),
        tags=['辅导记录模版相关']
    )
    def create(self, request, *args, **kwargs):
        self.serializer_class = InterViewRecordTemplateSerializersCreate
        name = request.data.get('name')
        if not name:
            return parameter_error_response('未获取到辅导模板名称')
        if InterviewRecordTemplate.objects.filter(name=name, deleted=False).exists():
            return parameter_error_response('模版名称重复，请重新输入。')
        return success_response(super(InterViewRecordTemplateViewSet, self).create(request, *args, **kwargs).data)
