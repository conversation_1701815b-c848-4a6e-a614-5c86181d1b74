import re

from django.conf import settings
from rest_framework import serializers
from django.db import transaction

from utils.message.email import message_send_email_base
from wisdom_v2 import utils
from wisdom_v2.models import UserBackend, User, WorkWechatUser
from wisdom_v2.enum.user_backend_enum import UserBackendTypeEnum
from utils.api_response import WisdomValidationError, WorkWechatUserError
from utils import aesencrypt, randomPassword, validate


class UserBackendListSerializer(serializers.ModelSerializer):
    id = serializers.IntegerField(help_text='id', read_only=True)
    name = serializers.CharField(source='user.name', read_only=True, help_text='用户名')
    user_id = serializers.IntegerField(source='user.id', read_only=True, help_text='用户名')
    true_name = serializers.CharField(source='user.cover_name', read_only=True, help_text='用户姓名')
    phone = serializers.CharField(source='user.phone', read_only=True, help_text='手机号')
    email = serializers.CharField(source='user.email', read_only=True, help_text='邮箱')
    role_id = serializers.IntegerField(read_only=True, help_text='角色')
    role_name = serializers.CharField(source='role.name', help_text='角色名称', read_only=True)
    project_id = serializers.IntegerField(help_text='项目id', read_only=True)
    work_wechat_user_id = serializers.SerializerMethodField(help_text='企业微信用户id', required=False)

    def get_work_wechat_user_id(self, obj):
        wx_user = WorkWechatUser.objects.filter(user_id=obj.user.id, deleted=False).first()
        if wx_user:
            return wx_user.wx_user_id
        return

    class Meta:
        model = UserBackend
        fields = ['id', 'name', 'user_id', 'true_name', 'phone', 'email', 'role_id', 'role_name', 'project_id', 'work_wechat_user_id']


class UserBackendSerializer(serializers.ModelSerializer):
    name = serializers.CharField(help_text='用户名', required=True, write_only=True)
    true_name = serializers.CharField(help_text='姓名', required=True, write_only=True)
    phone = serializers.CharField(help_text='手机号', required=True, write_only=True)
    email = serializers.CharField(help_text='邮箱', required=True, write_only=True)
    role_id = serializers.IntegerField(help_text='角色id', required=True, write_only=True)
    work_wechat_user_id = serializers.CharField(help_text='企业微信用户id', required=False, write_only=True)
    is_add_work_wechat_user = serializers.BooleanField(help_text='是否是新建企业微信账号', required=False, write_only=True)

    class Meta:
        model = UserBackend
        fields = ['name', 'true_name', 'phone', 'email', 'role_id', 'work_wechat_user_id', 'is_add_work_wechat_user']

    def create(self, validated_data):
        name, true_name, phone, email, role_id,\
        work_wechat_user_id, is_add_work_wechat_user = validated_data['name'], validated_data['true_name'], \
                                                 validated_data['phone'], validated_data['email'], \
                                                 validated_data['role_id'], validated_data.get('work_wechat_user_id'),\
                                                 validated_data.get('is_add_work_wechat_user')
        if User.objects.filter(name=name, deleted=False).exists():
            raise WisdomValidationError('当前用户名已存在')
        if User.objects.filter(phone=phone, deleted=False).exists():
            raise WisdomValidationError('当前手机号已存在')
        if User.objects.filter(email=email, deleted=False).exists():
            raise WisdomValidationError('当前邮箱错误')
        if work_wechat_user_id:
            wx_user = WorkWechatUser.objects.filter(
                user__isnull=False,
                wx_user_id=work_wechat_user_id, deleted=False).first()
            if wx_user:
                raise WorkWechatUserError({'is_bind': True, 'user_name': wx_user.user.cover_name})
        if is_add_work_wechat_user:
            wx_user = WorkWechatUser.objects.filter(
                user__isnull=False,
                wx_user_id=phone, deleted=False).first()
            if wx_user:
                raise WisdomValidationError('手机号对应的企业微信账号已存在')

        if not re.match(r"^1[3456789][0-9]{9}$", phone):
            raise WisdomValidationError('手机号错误')
        if ' ' in email:
            raise WisdomValidationError('邮箱中存在空格，请修改后再添加。')
        if not validate.validate_email(email):
            raise WisdomValidationError('邮箱错误')
        with transaction.atomic():
            pwd = randomPassword()
            user = User.objects.create(name=name, phone=phone, email=email, true_name=true_name,
                                       password=aesencrypt(pwd))
            user_backend = UserBackend.objects.create(user_id=user.id, role_id=role_id, user_type=UserBackendTypeEnum.admin.value)
            params = {
                "name": name,
                "password": pwd,
                "title_name": user_backend.role.name,
            }
            message_send_email_base.delay('add_admin_user', params, [user.email], receiver_ids=user.id)

            if is_add_work_wechat_user:
                state = utils.add_work_wechat_user(user, settings.WORK_WECHAT_MANAGE_DEPARTMENT_ID, 'add_user')
                if state:
                    raise WorkWechatUserError(state)
            else:
                utils.update_work_wechat_user(work_wechat_user_id, user)

        return 'success'
