from wisdom_v2.common import schedule_public
from wisdom_v2.enum.service_content_enum import ScheduleTypeEnum
from rest_framework import serializers
from django.db import transaction

from utils.api_response import WisdomValidationError
from wisdom_v2.models_file.company_interview import CompanyInterview
from wisdom_v2.models import Project, CompanyMember, Coach, Schedule, PublicAttr, ProjectMember
from wisdom_v2.views.constant import ATTR_TYPE_SCHEDULE


class CompanyInterviewSerializer(serializers.ModelSerializer):
    id = serializers.UUIDField(help_text='面试id', required=False)
    # Get start_time and end_time from schedule
    start_time = serializers.DateTimeField(help_text='开始时间', source='schedule.public_attr.start_time', format='%Y-%m-%d %H:%M', required=False)
    end_time = serializers.DateTimeField(help_text='结束时间', source='schedule.public_attr.end_time', format='%Y-%m-%d %H:%M', required=False)

    remark = serializers.Char<PERSON>ield(help_text='备注', required=False, allow_blank=True, allow_null=True)
    project_id = serializers.IntegerField(help_text='项目id', required=False)
    coach_id = serializers.IntegerField(help_text='教练id', required=False)
    customer_id = serializers.IntegerField(help_text='客户id', required=False, allow_null=True)
    interviewer_id = serializers.IntegerField(help_text='面试官id', required=False)
    deleted = serializers.BooleanField(help_text='是否删除', required=False)

    coach_name = serializers.SerializerMethodField(help_text='教练姓名')
    interviewer_name = serializers.SerializerMethodField(help_text='面试官姓名')
    customer_name = serializers.SerializerMethodField(help_text='客户姓名')
    interview_time = serializers.SerializerMethodField(help_text='面试时间')

    # 前端需要展示项目名称，教练姓名，客户姓名，面试官姓名
    project_name = serializers.SerializerMethodField(help_text='项目名称')
    company_name = serializers.SerializerMethodField(help_text='公司名称')
    company_id = serializers.SerializerMethodField(help_text='公司id')
    interviewer_position = serializers.SerializerMethodField(help_text='面试官职位')
    customer_position = serializers.SerializerMethodField(help_text='客户职位')
    customer_user_id = serializers.SerializerMethodField(help_text='如果客户已经配置在项目中，返回客户项目成员用户id，教练可以查看客户资料')

    class Meta:
        model = CompanyInterview
        fields = ['id', 'start_time', 'end_time', 'remark', 'coach_id',
                   'project_id', 'deleted', 'customer_id', 'interviewer_id',
                   'coach_name', 'interviewer_name', 'customer_name', 'interview_time',
                   'project_name', 'company_name', 'company_id', 'interviewer_position', 'customer_position', 'customer_user_id']

    def get_company_id(self, obj):
        return obj.project.company.id if obj.project and obj.project.company else None
    
    def get_project_name(self, obj):
        return obj.project.name if obj.project else None
    
    def get_company_name(self, obj):
        return obj.project.company.name if obj.project and obj.project.company else None
    
    def get_coach_name(self, obj):
        return obj.coach.user.true_name if obj.coach and obj.coach.user else None

    def get_interviewer_name(self, obj):
        return obj.interviewer.user.true_name if obj.interviewer and obj.interviewer.user else None

    def get_customer_name(self, obj):
        return obj.customer.user.true_name if obj.customer and obj.customer.user else None
    
    def get_interview_time(self, obj):
        start = obj.schedule.public_attr.start_time.strftime('%Y-%m-%d %H:%M') if obj.schedule.public_attr.start_time else None
        end = obj.schedule.public_attr.end_time.strftime('%H:%M') if obj.schedule.public_attr.end_time else None
        return f"{start} - {end}"

    def get_interviewer_position(self, obj):
        return obj.interviewer.position if obj.interviewer else None

    def get_customer_position(self, obj):
        return obj.customer.position if obj.customer else None
    
    def get_customer_user_id(self, obj):
        if obj.customer:
            customer_project_member = ProjectMember.objects.filter(
                project_id=obj.project_id, user_id=obj.customer.user_id, deleted=False).first()
            if customer_project_member:
                return customer_project_member.user_id
        return None

    def create(self, validated_data):
        project_id = validated_data.get('project_id')
        project = Project.objects.filter(pk=project_id).first()
        if not project:
            raise WisdomValidationError('未获取到项目信息')
        
        coach_id = validated_data.get('coach_id')
        coach = Coach.objects.filter(pk=coach_id).first()
        if not coach:
            raise WisdomValidationError('未获取到教练信息')
        
        interviewer_id = validated_data.get('interviewer_id')
        interviewer = CompanyMember.objects.filter(pk=interviewer_id).first()
        if not interviewer:
            raise WisdomValidationError('未获取到面试官信息')
        
        customer_id = validated_data.get('customer_id')
        # 客户id选填
        if customer_id:
            customer = CompanyMember.objects.filter(pk=customer_id).first()
            if not customer:
                raise WisdomValidationError('未获取到客户信息')
        
        start_time = validated_data.get('schedule', {}).get('public_attr', {}).get('start_time')
        end_time = validated_data.get('schedule', {}).get('public_attr', {}).get('end_time')
        if not start_time or not end_time:
            raise WisdomValidationError('面试时间不能为空')
        # 不校验时间是否可用，前端会校验，并且支持强行预约
        
        remark = validated_data.get('remark')
        with transaction.atomic():
            # create schedule
            schedule_attr = PublicAttr.objects.create(
                start_time=start_time, end_time=end_time,
                project_id=project_id, user_id=coach.user.id, target_user_id=interviewer.user.id,
                type=ATTR_TYPE_SCHEDULE)
            schedule = Schedule.objects.create(
                type=ScheduleTypeEnum.company_interview,
                public_attr=schedule_attr, title='企业面试')
            instance = CompanyInterview.objects.create(
                project_id=project_id, coach_id=coach_id, interviewer_id=interviewer_id, customer_id=customer_id,
                schedule=schedule, remark=remark)
        return instance
    

    def update(self, instance, validated_data):
        # 面试时间需要更新schedule
        schedule_data = validated_data.pop('schedule', {})
        if schedule_data:
            public_attr_data = schedule_data.get('public_attr', {})
            start_time = public_attr_data.get('start_time')
            end_time = public_attr_data.get('end_time')
            if start_time and end_time:
                instance.schedule.public_attr.start_time = start_time
                instance.schedule.public_attr.end_time = end_time
                instance.schedule.public_attr.save()

        # 如果取消面试，同步删除日程
        if validated_data.get('deleted'):
            instance.schedule.deleted = True
            instance.schedule.save()
        # 如果更新了教练，同步更新日程
        if validated_data.get('coach_id') and instance.coach_id!= validated_data.get('coach_id'):
            coach = Coach.objects.filter(pk=validated_data.get('coach_id')).first()
            if not coach:
                raise WisdomValidationError('未获取到教练信息')
    
            instance.schedule.user_id = coach.user.id
            instance.schedule.save()
            
        # 如果更新了面试官，同步更新日程
        if validated_data.get('interviewer_id') and instance.interviewer_id!= validated_data.get('interviewer_id'):
            interviewer = CompanyMember.objects.filter(pk=validated_data.get('interviewer_id')).first()
            if not interviewer:
                raise WisdomValidationError('未获取到面试官信息')
            instance.schedule.target_user_id = interviewer.user.id
            instance.schedule.save()
        
        # 如果更新了客户信息
        if 'customer_id' in validated_data:
            customer_id = validated_data.get('customer_id')
            if customer_id:
                customer = CompanyMember.objects.filter(pk=customer_id).first()
                if not customer:
                    raise WisdomValidationError('未获取到客户信息')
            validated_data['customer_id'] = customer_id if customer_id else None

        super().update(instance=instance, validated_data=validated_data)
        return instance

