import redis
from django.conf import settings
from drf_yasg import openapi
from rest_framework.decorators import action
from drf_yasg.utils import swagger_auto_schema
from rest_framework import viewsets

from co_wisdom_server.celery import app_celery
from utils import task, randomPassword
from utils.api_response import success_response, parameter_error_response
from wisdom_v2 import utils
from wisdom_v2.enum.project_interview_enum import ProjectInterviewTypeEnum
from wisdom_v2.enum.service_content_enum import CoachOfferStatusEnum, NoticeTemplateTypeEnum
from wisdom_v2.models import Project, ProjectOffer, CoachOffer, User, Coach, ProjectInterview, CoachTask, WorkWechatUser
from wisdom_v2.models_file import Activity, ChemicalInterview2Coach
from wisdom_v2.views.constant import ATTR_STATUS_INTERVIEW_CANCEL

data_redis = redis.Redis.from_url(settings.DATA_REDIS)


class TasksViewSet(viewsets.ModelViewSet):
    queryset = None
    serializer_class = None

    @swagger_auto_schema(
        operation_id='异步任务详情',
        operation_summary='异步任务详情',
        manual_parameters=[
            openapi.Parameter('task_id', openapi.IN_QUERY, description='异步任务id', type=openapi.TYPE_NUMBER),
        ],
        tags=['异步任务相关']
    )
    @action(methods=['get'], detail=False, url_path='detail')
    def get_celery_message(self, request, *args, **kwargs):
        try:
            task_id = request.query_params.get('task_id')
            if not task_id:
                return parameter_error_response('未获取到任务id参数信息')
            redis_data = data_redis.get(task_id).decode() if data_redis.get(task_id) else None
            result = app_celery.AsyncResult(id=task_id)
            data = utils.get_tasks_state(result, redis_data)
            return success_response(data)
        except Exception as e:
            return parameter_error_response(str(e))

    @swagger_auto_schema(
        operation_id='辅导明细详情下载',
        operation_summary='辅导明细详情下载',
        manual_parameters=[
            openapi.Parameter('project_name', openapi.IN_QUERY, description='项目名称', type=openapi.TYPE_STRING),
            openapi.Parameter('coachee_name', openapi.IN_QUERY, description='客户名称', type=openapi.TYPE_STRING),
            openapi.Parameter('coach_name', openapi.IN_QUERY, description='教练名称', type=openapi.TYPE_STRING),
            openapi.Parameter('start_time', openapi.IN_QUERY, description='开始时间', type=openapi.FORMAT_DATETIME),
            openapi.Parameter('end_time', openapi.IN_QUERY, description='结束时间', type=openapi.FORMAT_DATETIME),
            openapi.Parameter('interview_status', openapi.IN_QUERY, description='辅导状态', type=openapi.TYPE_NUMBER),
            openapi.Parameter('interview_record_status', openapi.IN_QUERY, description='填写状态', type=openapi.TYPE_NUMBER),
        ],
        tags=['异步任务相关']
    )
    @action(methods=['post'], detail=False, url_path='generate_interview_record_file')
    def get_interview_detail_tasks(self, request, *args, **kwargs):
        try:
            key = randomPassword(16)
            interview_detail_task = task.send_interview_detail_list.delay(request.data, key)
            data_redis.set(key, str(interview_detail_task.id), ex=3600)
            return success_response({'task_id': f'{interview_detail_task.id}'})
        except Exception as e:
            return parameter_error_response(str(e))

    @swagger_auto_schema(
        operation_id='教练辅导信息下载',
        operation_summary='教练辅导信息下载',
        manual_parameters=[
            openapi.Parameter('project_name', openapi.IN_QUERY, description='项目名称', type=openapi.TYPE_STRING),
            openapi.Parameter('coachee_name', openapi.IN_QUERY, description='客户名称', type=openapi.TYPE_STRING),
            openapi.Parameter('coach_name', openapi.IN_QUERY, description='教练名称', type=openapi.TYPE_STRING),
            openapi.Parameter('start_time', openapi.IN_QUERY, description='开始时间', type=openapi.FORMAT_DATETIME),
            openapi.Parameter('end_time', openapi.IN_QUERY, description='结束时间', type=openapi.FORMAT_DATETIME),
            openapi.Parameter('interview_status', openapi.IN_QUERY, description='辅导状态', type=openapi.TYPE_NUMBER),
            openapi.Parameter('interview_record_status', openapi.IN_QUERY, description='填写状态', type=openapi.TYPE_NUMBER),
        ],
        tags=['异步任务相关']
    )
    @action(methods=['post'], detail=False, url_path='generate_coach_interview_data')
    def get_coach_interview_data_tasks(self, request, *args, **kwargs):
        try:
            coach_user_id = request.data.get('coach_user_id')
            user = User.objects.get(id=coach_user_id)
            Coach.objects.get(user_id=coach_user_id)

            key = randomPassword(16)
            coach_interview_data_task = task.get_coach_interview_data_xlsx.apply_async(
                kwargs=dict(params=request.data, key=key, user=user), countdown=1)
            data_redis.set(key, str(coach_interview_data_task.id), ex=3600)
            return success_response({'task_id': f'{coach_interview_data_task.id}'})
        except (User.DoesNotExist, Coach.DoesNotExist):
            return parameter_error_response('教练不存在')
        except Exception as e:
            return parameter_error_response(str(e))

    @swagger_auto_schema(
        operation_id='结算订单列表下载',
        operation_summary='结算订单列表下载',
        manual_parameters=[],
        tags=['异步任务相关']
    )
    @action(methods=['post'], detail=False, url_path='generate_business_settlement_order_data')
    def get_business_settlement_order_data_tasks(self, request, *args, **kwargs):
        try:
            key = randomPassword(16)
            coach_interview_data_task = task.get_business_settlement_order_xlsx.apply_async(
                kwargs=dict(params=request.data, key=key), countdown=1)
            data_redis.set(key, str(coach_interview_data_task.id), ex=3600)
            return success_response({'task_id': f'{coach_interview_data_task.id}'})
        except (User.DoesNotExist, Coach.DoesNotExist):
            return parameter_error_response('教练不存在')
        except Exception as e:
            return parameter_error_response(str(e))


    @swagger_auto_schema(
        operation_id='项目教练报告下载',
        operation_summary='项目教练报告下载',
        manual_parameters=[
            openapi.Parameter('project_id', openapi.IN_QUERY, description='项目标识', type=openapi.TYPE_STRING),
            openapi.Parameter('user_id', openapi.IN_QUERY, description='用户标识', type=openapi.TYPE_STRING),
        ],
        tags=['异步任务相关']
    )
    @action(methods=['post'], detail=False, url_path='generate_project_coach_report')
    def get_generate_project_coach_report_tasks(self, request, *args, **kwargs):
        try:
            project_id = request.data.get('project_id')
            project = Project.objects.get(id=project_id, deleted=False)
            user_id = request.data.get('user_id')
            User.objects.get(id=user_id, deleted=False)
            key = randomPassword(16)
            coach_interview_data_task = task.get_project_coach_report.apply_async(
                kwargs=dict(project=project, key=key), countdown=1)
            data_redis.set(key, str(coach_interview_data_task.id), ex=3600)
            return success_response({'task_id': f'{coach_interview_data_task.id}'})
        except (User.DoesNotExist, Project.DoesNotExist):
            return parameter_error_response('教练不存在')
        except Exception as e:
            return parameter_error_response(str(e))

    @swagger_auto_schema(
        operation_id='项目发送用户通知',
        operation_summary='项目发送用户通知',
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'template_type': openapi.Schema(description='发送模板类型 '
                                                            '1-被教练者账号开通通知 2-改变观察问卷 3-利益相关者调研问卷 '
                                                            '4-教练简历发送通知 5-企业管理员账号开通通知',
                                                type=openapi.TYPE_NUMBER),
                'channel_type': openapi.Schema(type=openapi.TYPE_NUMBER, description='发送渠道 1-企业微信 2-短信 3-邮件'),
                'data_ids': openapi.Schema(type=openapi.TYPE_STRING, description='数据标识id'),
            },
        ),
        tags=['异步任务相关']

    )
    @action(methods=['post'], detail=False, url_path='generate_user_notice')
    def send_user_notice(self, request, *args, **kwargs):
        try:
            template_type = int(request.data['template_type'])
            channel_type = int(request.data['channel_type'])
            data_ids = request.data.get('data_ids')
            if template_type in [NoticeTemplateTypeEnum.change_observation, NoticeTemplateTypeEnum.stakeholder]:
                data_ids = [str(item).split('-')[0] for item in data_ids]
        except Exception as e:
            return parameter_error_response(str(e))
        try:
            key = randomPassword(16)
            send_user_notice_task = task.send_user_notice.delay(template_type, channel_type, data_ids, key, sender_id=request.user.id)
            data_redis.set(key, str(send_user_notice_task.id), ex=3600)
            return success_response({'task_id': f'{send_user_notice_task.id}'})
        except Exception as e:
            return parameter_error_response(str(e))

    @swagger_auto_schema(
        operation_id='项目发送教练简历',
        operation_summary='项目发送教练简历',
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'template_type': openapi.Schema(description='发送模板类型 1-账号开通通知 2-改变观察问卷 3-利益相关者调研问卷',
                                                type=openapi.TYPE_NUMBER),
                'channel_type': openapi.Schema(type=openapi.TYPE_NUMBER, description='发送渠道 1-企业微信 2-短信 3-邮件'),
                'data_ids': openapi.Schema(type=openapi.TYPE_STRING, description='数据标识id'),
            },
        ),
        tags=['异步任务相关']

    )
    @action(methods=['post'], detail=False, url_path='generate_send_coach_resume_notice')
    def send_coach_resume(self, request, *args, **kwargs):
        try:
            resume_ids = request.data['resume_ids']
            project_id = request.data.get('project_id')
            manager_user_ids = request.data['manager_user_ids']
            project = Project.objects.get(pk=project_id, deleted=False)
        except Project.DoesNotExist:
            return parameter_error_response('未获取到对应项目信息')
        except Exception:
            return parameter_error_response('缺少关键参数信息')

        if not isinstance(resume_ids, list):
            return parameter_error_response('教练简历id参数格式错误')
        if not isinstance(manager_user_ids, list):
            return parameter_error_response('项目运营id数格式错误')

        try:
            key = randomPassword(16)
            send_coach_resume_notice_task = task.send_coach_resume_notice.delay(
                resume_ids, project, manager_user_ids, request.user.id, key, sender_id=request.user.id)
            data_redis.set(key, str(send_coach_resume_notice_task.id), ex=3600)
            return success_response({'task_id': f'{send_coach_resume_notice_task.id}'})
        except Exception as e:
            return parameter_error_response(str(e))

    @swagger_auto_schema(
        operation_id='项目发送教练offer',
        operation_summary='项目发送教练offer',
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'coach_offer_ids': openapi.Schema(type=openapi.TYPE_STRING, description='教练offer_id列表'),
                'project_offer_id': openapi.Schema(type=openapi.TYPE_NUMBER, description='项目offer_id'),
            },
        ),
        tags=['异步任务相关']

    )
    @action(methods=['post'], detail=False, url_path='send_coach_offer_notice')
    def send_coach_offer(self, request, *args, **kwargs):
        coach_offer_ids = request.data.get('coach_offer_ids')
        project_offer_id = request.data.get('project_offer_id')
        coach_offers = None
        if not coach_offer_ids and not project_offer_id:
            return parameter_error_response()
        if coach_offer_ids:
            coach_offers = CoachOffer.objects.filter(id__in=coach_offer_ids, deleted=False,
                                                     status=CoachOfferStatusEnum.not_confirm)
        if project_offer_id:
            project_offer = ProjectOffer.objects.filter(id=project_offer_id, deleted=False).first()
            coach_offers = CoachOffer.objects.filter(project_offer=project_offer, deleted=False,
                                                     status=CoachOfferStatusEnum.not_confirm)
        if not coach_offers:
            return parameter_error_response('未查询到可发送邀请的教练，请检查确认截止时间及教练状态')
        coach_offer_ids = list(coach_offers.values_list('id', flat=True))
        try:
            key = randomPassword(16)
            send_coach_offer_notice_task = task.send_coach_offer_notice.delay(
                coach_offer_ids, request.user.id, key)
            data_redis.set(key, str(send_coach_offer_notice_task.id), ex=3600)
            return success_response({'task_id': f'{send_coach_offer_notice_task.id}'})
        except Exception as e:
            return parameter_error_response(str(e))

    @swagger_auto_schema(
        operation_id='生成项目进度阶段报告',
        operation_summary='生成项目进度阶段报告',
        manual_parameters=[
            openapi.Parameter('project_id', openapi.IN_QUERY, description='项目名称', type=openapi.TYPE_STRING),
            openapi.Parameter('start_date', openapi.IN_QUERY, description='客户名称', type=openapi.TYPE_STRING),
            openapi.Parameter('end_date', openapi.IN_QUERY, description='教练名称', type=openapi.TYPE_STRING),
        ],
        tags=['异步任务相关']
    )
    @action(methods=['post'], detail=False, url_path='generate_project_progress_report')
    def generate_project_progress_report(self, request, *args, **kwargs):
        try:
            project_id = request.data['project_id']
            start_date = request.data['start_date']
            end_date = request.data['end_date']
            project = Project.objects.get(pk=project_id, deleted=False)
        except (KeyError, Project.DoesNotExist):
            return parameter_error_response('未获取到对应项目信息')
        try:
            key = randomPassword(16)
            project_progress_report_task = task.generate_project_progress_report_pdf.delay(project, start_date,
                                                                                           end_date, key)
            data_redis.set(key, str(project_progress_report_task.id), ex=3600)
            return success_response({'task_id': f'{project_progress_report_task.id}'})
        except Exception as e:
            return parameter_error_response(str(e))

    @swagger_auto_schema(
        operation_id='给教练发送活动邀请通知',
        operation_summary='给教练发送活动邀请通知',
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'activity_id': openapi.Schema(type=openapi.TYPE_NUMBER, description='活动标识'),
            }),
        tags=['异步任务相关']
    )
    @action(methods=['post'], detail=False, url_path='activity_coach_invite')
    def send_activity_coach_invite(self, request, *args, **kwargs):
        try:
            activity_id = request.data.get('activity_id')
            activity = Activity.objects.get(id=activity_id, deleted=False)
        except Activity.DoesNotExist:
            return parameter_error_response('活动教练不存在')
        try:
            key = randomPassword(16)
            project_progress_report_task = task.activity_coach_invite.apply_async(
                kwargs=dict(activity=activity, key=key), countdown=3)
            data_redis.set(key, str(project_progress_report_task.id), ex=3600)
            return success_response({'task_id': f'{project_progress_report_task.id}'})
        except Exception as e:
            return parameter_error_response(str(e))

    @swagger_auto_schema(
        operation_id='管理后台批量导入教练信息',
        operation_summary='管理后台批量导入教练信息',
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'activity_id': openapi.Schema(type=openapi.TYPE_NUMBER, description='活动标识'),
            }),
        tags=['异步任务相关']
    )
    @action(methods=['post'], detail=False, url_path='batch_import_coach')
    def generate_batch_import_coach(self, request, *args, **kwargs):
        try:
            file = request.FILES.get('file')
        except Exception as e:
            return parameter_error_response()
        try:
            key = randomPassword(16)
            batch_import_coach = task.batch_import_coach.delay(file, key)
            data_redis.set(key, str(batch_import_coach.id), ex=3600)
            return success_response({'task_id': f'{batch_import_coach.id}'})
        except Exception as e:
            return parameter_error_response(str(e))

    @swagger_auto_schema(
        operation_id='管理后台下载项目化学面谈信息',
        operation_summary='管理后台下载项目化学面谈信息',
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'project_id': openapi.Schema(type=openapi.TYPE_NUMBER, description='项目标识'),
            }),
        tags=['异步任务相关']
    )
    @action(methods=['post'], detail=False, url_path='generate_chemical_interview')
    def generate_chemical_interview(self, request, *args, **kwargs):
        try:
            project_id = request.data.get('project_id')
        except Exception as e:
            return parameter_error_response()

        is_chemical_interview = ChemicalInterview2Coach.objects.filter(
            interview__public_attr__project_id=project_id, deleted=False, interview__deleted=False,
            interview__type=ProjectInterviewTypeEnum.chemical_interview.value).exclude(
            interview__public_attr__status=ATTR_STATUS_INTERVIEW_CANCEL).exists()
        if not is_chemical_interview:
            return success_response({'is_chemical_interview': is_chemical_interview})

        try:
            key = randomPassword(16)
            batch_import_coach = task.download_chemical_interview.apply_async(
                kwargs=dict(project_id=project_id, key=key), countdown=1)
            data_redis.set(key, str(batch_import_coach.id), ex=3600)
            return success_response({'task_id': f'{batch_import_coach.id}'})
        except Exception as e:
            return parameter_error_response(str(e))


    @swagger_auto_schema(
        operation_id='小程序教练下载利益相关者访谈报告',
        operation_summary='小程序教练下载利益相关者访谈报告',
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'coach_task_id': openapi.Schema(type=openapi.TYPE_NUMBER, description='教练任务id'),
            }),
        tags=['异步任务相关']
    )
    @action(methods=['post'], detail=False, url_path='generate_stakeholder_interview_word')
    def generate_stakeholder_interview_word(self, request, *args, **kwargs):
        try:
            coach_task_id = request.data.get('coach_task_id')
            coach_task = CoachTask.objects.get(id=coach_task_id, deleted=False)
            WorkWechatUser.objects.get(
                user_id=coach_task.public_attr.user_id, wx_user_id__isnull=False, deleted=False)
        except CoachTask.DoesNotExist:
            return parameter_error_response('教练任务不存在')
        except WorkWechatUser.DoesNotExist:
            return parameter_error_response('教练还未绑定企业微信账号')
        except Exception as e:
            return parameter_error_response()

        try:
            key = randomPassword(16)
            batch_import_coach = task.download_stakeholder_interview_word.apply_async(
                kwargs=dict(coach_task_id=coach_task_id, key=key), countdown=1)
            data_redis.set(key, str(batch_import_coach.id), ex=3600)
            return success_response({'task_id': f'{batch_import_coach.id}'})
        except Exception as e:
            return parameter_error_response(str(e))