import pendulum
from django.db.models import F

from django.db.models.functions import TruncDay
from django.forms import model_to_dict
from drf_yasg import openapi
from drf_yasg.utils import swagger_auto_schema

from utils.pagination import StandardResultsSetPagination
from utils.utc_date_time import datetime_change_utc
from .content_operation_base_action import ContentOperationBaseSerializers
from rest_framework import viewsets
from utils.api_response import success_response
from ..models import ContentOperationBase


class ContentOperationBaseViewSet(viewsets.ModelViewSet):
    queryset = ContentOperationBase.objects

    serializer_class = ContentOperationBaseSerializers

    @swagger_auto_schema(
        operation_id='基础数据列表',
        operation_summary='基础数据列表',
        manual_parameters=[
            openapi.Parameter('start_date', openapi.IN_QUERY, description='起止时间（大于等于）| yyyy-mm-dd', type=openapi.FORMAT_DATE),
            openapi.Parameter('end_date', openapi.IN_QUERY, description='结束时间（小于）| yyyy-mm-dd', type=openapi.FORMAT_DATE),
            openapi.Parameter('data_type', openapi.IN_QUERY, description='日期查询类型 ｜ 1:按月查询，2:按天查询', type=openapi.TYPE_NUMBER),
            openapi.Parameter('channel', openapi.IN_QUERY, description='产品渠道(逗号分隔) | 1:安卓，2:IOS，3:小程序', type=openapi.TYPE_STRING),
            openapi.Parameter('page', openapi.TYPE_ARRAY, description='第几页', type=openapi.TYPE_NUMBER),
            openapi.Parameter('page_size', openapi.IN_QUERY, description='每页显示条数默认15条', type=openapi.TYPE_NUMBER),
        ],
        tags=['内容管理数据相关']
    )
    def list(self, request, *args, **kwargs):
        # 获取当前日期
        utc_date = pendulum.today()
        start_date = request.query_params.get('start_date')
        end_date = request.query_params.get('end_date')
        data_type = request.query_params.get('data_type', '1')
        # 日期参数转换
        if start_date:
            start_date = datetime_change_utc(start_date)
        else:
            start_date = utc_date.subtract(days=30)

        if end_date:
            end_date = datetime_change_utc(end_date)
        else:
            end_date = utc_date
        # 默认全部渠道
        channels = request.query_params.get('channel', '1,2,3').split(',')
        # 渠道sql拼接
        channel = {
            '1': 'android_user',
            '2': 'ios_user',
            '3': 'applets_user',
        }
        usr_time_sql = '({}'.format(channel[channels.pop(0)])
        for item in channels:
            usr_time_sql += ' + {}'.format(channel[item])
        usr_time_sql += ')'
        content_base = self.get_queryset()

        if data_type == '1':
            # sql查询
            content_base_sql = """SELECT id, date, android_use_time, ios_use_time, applets_use_time, {} AS 
            active_users FROM v2_content_operation_base WHERE (date >= '{}' AND date < '{}') order by date desc""".format(
                usr_time_sql, start_date.strftime('%Y-%m-%d'), end_date.strftime('%Y-%m-%d'))
            raw_content_bases = content_base.raw(content_base_sql)
        else:
            raw_content_bases = []
            # 时间按月循环
            period = pendulum.period(start_date, end_date)
            for start_dt in period.range('months'):
                end_dt = start_dt.add(months=1)
                content_base_sql = """SELECT id, date, android_use_time, ios_use_time, applets_use_time, {} AS
                active_users FROM v2_content_operation_base WHERE (date >= '{}' AND date < '{}')""".format(
                    usr_time_sql, start_dt.strftime('%Y-%m-%d'), end_dt.strftime('%Y-%m-%d'))
                content_bases = content_base.raw(content_base_sql)

                # 基础数据格式
                base = {
                    'date': start_dt.strftime('%Y-%m'),
                    'android_user': 0,
                    'ios_user': 0,
                    'applets_user': 0,
                    'active_users': 0
                }
                android_use_time = {}
                ios_use_time = {}
                applets_use_time = {}

                # 每月数据拼接
                for item in content_bases:
                    base['android_user'] += item.android_user
                    base['ios_user'] += item.ios_user
                    base['applets_user'] += item.applets_user
                    base['active_users'] += item.active_users
                    android_use_time = get_use_time(android_use_time, item.android_use_time)
                    ios_use_time = get_use_time(ios_use_time, item.android_use_time)
                    applets_use_time = get_use_time(applets_use_time, item.android_use_time)
                base['android_use_time'] = android_use_time
                base['ios_use_time'] = ios_use_time
                base['applets_use_time'] = applets_use_time
                # 反序列化进行分页处理
                raw_content_bases.append(ContentOperationBase(base))
        paginator = StandardResultsSetPagination()
        page_list = paginator.paginate_queryset(raw_content_bases, self.request)
        serializer = self.get_serializer(page_list, many=True)
        response = paginator.get_paginated_response(serializer.data)
        return success_response(response)


def get_use_time(old_use_time, use_time):

    for k, v in use_time.items():
        if old_use_time.get(k):
            old_use_time[k] += v
        else:
            old_use_time[k] = v
    return old_use_time
