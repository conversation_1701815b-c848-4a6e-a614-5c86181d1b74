import datetime

from django.db.models import Q
from rest_framework import viewsets
from drf_yasg import openapi
from drf_yasg.utils import swagger_auto_schema
from rest_framework.decorators import action
from django.db import transaction

from wisdom_v2.enum.service_content_enum import ScheduleTypeEnum, ProjectCoachStatusEnum
from wisdom_v2.models import ProjectMember, ProjectCoach, Coach, Project, ProjectGroupCoach, ProjectInterview, \
    PublicAttr, Schedule, InterviewRecordTemplateAnswer
from wisdom_v2.views.constant import ATTR_TYPE_COACH_TASK
from wisdom_v2.views.project_coach_action import FiveALCProjectCoachSerializer, ProjectGroupCoachListSerializer, CoachListSerializer
from wisdom_v2.enum.project_enum import ProjectTypeEnum
from utils.api_response import success_response, parameter_error_response
from utils.pagination import StandardResultsSetPagination
from wisdom_v2.enum.project_interview_enum import ProjectInterviewPlaceCategoryEnum


class FiveALCProjectCoachViewSet(viewsets.ModelViewSet):
    queryset = ProjectMember.objects.filter(deleted=False).order_by('-created_at')
    serializer_class = FiveALCProjectCoachSerializer

    @swagger_auto_schema(
        operation_id='教练列表',
        operation_summary='教练列表',
        manual_parameters=[
            openapi.Parameter('page', openapi.IN_QUERY, description='第几页', type=openapi.TYPE_NUMBER),
            openapi.Parameter('page_size', openapi.IN_QUERY, description='每页显示条数默认15条', type=openapi.TYPE_NUMBER),
            openapi.Parameter('project_id', openapi.IN_QUERY, description='项目id（必传）', type=openapi.TYPE_NUMBER),

        ],
        tags=['教练管理相关']
    )
    @action(methods=['get'], detail=False, url_path='coach_list', serializer_class=CoachListSerializer)
    def coach_list(self, request, *args, **kwargs):
        try:
            project_id = request.query_params.get('project_id', None)
            name = request.query_params.get('name', None)
        except:
            return parameter_error_response()
        if not project_id:
            return parameter_error_response('请选择项目')
        project = Project.objects.filter(id=project_id, deleted=False).first()
        if not project:
            return parameter_error_response('当前项目不存在')
        coaches = Coach.objects.filter(
            deleted=False,
            user_coach__project=project,
            user_coach__deleted=False,
            user_coach__project_group_coach__isnull=True,
            user_coach__member__isnull=True
        )
        if name:
            coaches = coaches.filter(user__true_name__icontains=name)
        # 分页
        page_size = coaches.count()
        request.GET._mutable = True
        request.query_params['page_size'] = page_size
        paginator = StandardResultsSetPagination()
        page_list = paginator.paginate_queryset(coaches.order_by('-created_at'), self.request)
        serializer = self.get_serializer(page_list, many=True)
        response = paginator.get_paginated_response(serializer.data)
        return success_response(response)

    @swagger_auto_schema(
        operation_id='5ALC一对一辅导匹配教练',
        operation_summary='5ALC一对一辅导匹配教练',
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'project_member_id': openapi.Schema(type=openapi.TYPE_NUMBER, description='项目成员id'),
                'coach_id': openapi.Schema(type=openapi.TYPE_NUMBER, description='教练id'),
            }
        ),
        tags=['教练管理相关']
    )
    @action(methods=['post'], detail=False, url_path='5alc_match_coach')
    def five_alc_match_coach(self, request, *args, **kwargs):
        project_member_id = request.data.get('project_member_id', None)
        coach_id = request.data.get('coach_id', None)
        if not project_member_id or not coach_id:
            return parameter_error_response()
        project_member = ProjectMember.objects.filter(id=project_member_id,deleted=False).first()
        coach = Coach.objects.filter(id=coach_id, deleted=False).first()
        if not project_member:
            return parameter_error_response('当前被教练者不存在')
        if not coach:
            return parameter_error_response('当前教练不存在')
        if project_member.chemical_interview.filter(deleted=False).exists():
            return parameter_error_response('修改失败，请在化学面谈记录中修改')
        if ProjectCoach.objects.filter(member_id=project_member.user_id, project_id=project_member.project_id,
                                       coach_id=coach.id, status=1, deleted=False, project_group_coach__isnull=True
                                       ).exists():
            return parameter_error_response('当前教练已与该被教练者匹配')
        with transaction.atomic():
            ProjectCoach.objects.create(
                member_id=project_member.user_id, project_id=project_member.project_id,
                coach_id=coach.id, status=ProjectCoachStatusEnum.adopt)
            PublicAttr.objects.filter(
                user_id__isnull=True,
                target_user_id=project_member.user_id,
                project_id=project_member.project_id,
                type=ATTR_TYPE_COACH_TASK,
            ).update(user_id=coach.user_id)
        return success_response()

    @swagger_auto_schema(
        operation_id='5ALC一对一变更教练',
        operation_summary='5ALC一对一变更教练',
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'project_member_id': openapi.Schema(type=openapi.TYPE_NUMBER, description='项目成员id'),
                'coach_id': openapi.Schema(type=openapi.TYPE_NUMBER, description='原教练id'),
                'new_coach_id': openapi.Schema(type=openapi.TYPE_NUMBER, description='现变更教练id'),
            }
        ),
        tags=['教练管理相关']
    )
    @action(methods=['post'], detail=False, url_path='five_alc_change_coach')
    def five_alc_change_coach(self, request, *args, **kwargs):
        project_member_id = request.data.get('project_member_id', None)
        coach_id = request.data.get('coach_id', None)
        new_coach_id = request.data.get('new_coach_id', None)
        if not project_member_id or not coach_id or not new_coach_id:
            return parameter_error_response()
        project_member = ProjectMember.objects.filter(id=project_member_id, deleted=False).first()
        coach = Coach.objects.filter(id=coach_id, deleted=False).first()
        new_coach = Coach.objects.filter(id=new_coach_id,deleted=False).first()
        if not project_member:
            return parameter_error_response('当前被教练者不存在')
        if project_member.chemical_interview.filter(deleted=False).exists():
            return parameter_error_response('修改失败，请在化学面谈记录中修改')
        if not coach:
            return parameter_error_response('原教练不存在')
        if not new_coach:
            return parameter_error_response('当前匹配教练不存在')
        if ProjectCoach.objects.filter(member_id=project_member.user_id, project_id=project_member.project_id,
                                       coach_id=new_coach.id, status=1, deleted=False,
                                       project_group_coach_id__isnull=True).exists():
            return parameter_error_response('当前教练已与该被教练者匹配')
        # 辅导记录判断
        if ProjectInterview.objects.filter(
                (Q(public_attr__start_time__gt=datetime.datetime.now()) |
                 Q(public_attr__start_time__lt=datetime.datetime.now(), public_attr__end_time__gt=datetime.datetime.now())),
                deleted=False, public_attr__project=project_member.project, public_attr__user=coach.user,
                public_attr__target_user=project_member.user,
                place_category=ProjectInterviewPlaceCategoryEnum.online_one_to_one.value).exists():
            return parameter_error_response('该被教练者与其原教练有未开始或进行中的辅导，暂时不可变更')
        # 利益相关者访谈配置判断
        stakeholder_interview_module = project_member.stakeholder_interview_module.filter(deleted=False).first()
        if stakeholder_interview_module:
            # 利益相关者已预约辅导、教练已填写访谈报告不可修改
            if stakeholder_interview_module.stakeholder_interview.filter(interview__isnull=False,
                                                                         deleted=False).exists():
                return parameter_error_response('当前教练与被教练者的利益相关者已预约利益相关者访谈，不可变更')
            if InterviewRecordTemplateAnswer.objects.filter(
                    public_attr=stakeholder_interview_module.coach_task.public_attr, deleted=False).exists():
                return parameter_error_response('当前教练已填写被教练者的利益相关者访谈报告，不可变更')

        with transaction.atomic():
            ProjectCoach.objects.filter(member_id=project_member.user_id, project_id=project_member.project_id,
                                        coach_id=coach.id, status=1, deleted=False, project_group_coach_id__isnull=True
                                        ).update(coach_id=new_coach_id)
            # ProjectCoach.objects.create(member_id=project_member.user_id, project_id=project_member.project_id,
            #                             coach_id=coach.id, status=1)
            # 变更教练后更新匹配前配置的教练任务
            PublicAttr.objects.filter(
                user_id=coach.user.id,
                target_user_id=project_member.user_id,
                project_id=project_member.project_id,
                type=ATTR_TYPE_COACH_TASK,
            ).update(user_id=new_coach.user.id)

        return success_response()

    @swagger_auto_schema(
        operation_id='MOP一对一匹配教练',
        operation_summary='MOP一对一匹配教练',
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'project_id': openapi.Schema(type=openapi.TYPE_NUMBER, description='项目id'),
                'coach_id': openapi.Schema(type=openapi.TYPE_NUMBER, description='原教练id'),
            }
        ),
        tags=['教练管理相关']
    )
    @action(methods=['post'], detail=False, url_path='mop_match_coach')
    def mop_match_coach(self, request, *args, **kwargs):
        project_id = request.data.get('project_id', None)
        coach_id = request.data.get('coach_id', None)
        if not project_id or not coach_id:
            return parameter_error_response()
        project = Project.objects.filter(id=project_id, deleted=False).first()
        coach = Coach.objects.filter(id=coach_id, deleted=False).first()
        if not project:
            return parameter_error_response('当前项目不存在')
        if not coach:
            return parameter_error_response('当前匹配教练不存在')
        if ProjectCoach.objects.filter(member__isnull=True, project_id=project_id, project_group_coach_id__isnull=True,
                                       coach_id=coach.id, status=1, deleted=False).exists():
            return parameter_error_response('当前教练已与该项目匹配')
        with transaction.atomic():
            ProjectCoach.objects.create(project_id=project_id, coach_id=coach.id, status=1)
        return success_response()

    @swagger_auto_schema(
        operation_id='MOP一对一移除教练',
        operation_summary='MOP一对一移除教练',
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'project_coach_id': openapi.Schema(type=openapi.TYPE_NUMBER, description='当前匹配教练信息id'),
                'project_id': openapi.Schema(type=openapi.TYPE_NUMBER, description='项目id')
            }
        ),
        tags=['教练管理相关']
    )
    @action(methods=['post'], detail=False, url_path='mop_del_coach')
    def mop_del_coach(self, request, *args, **kwargs):
        project_id = request.data.get('project_id', None)
        project_coach_id = request.data.get('project_coach_id', None)
        if not project_id or not project_coach_id:
            return parameter_error_response()
        project = Project.objects.filter(id=project_id, deleted=False).first()
        project_coach = ProjectCoach.objects.filter(id=project_coach_id, deleted=False, project_id=project_id).first()
        if not project:
            return parameter_error_response('当前项目不存在')
        if not project_coach:
            return parameter_error_response('当前匹配教练关系不存在')

        # 辅导判断
        if PublicAttr.objects.filter(project_id=project_id, user=project_coach.coach.user, status=3, type=1).exists():
            return parameter_error_response('该教练在本项目中有未开始或进行中的辅导，不可移除')
        with transaction.atomic():
            ProjectCoach.objects.filter(project_id=project_id, id=project_coach_id, status=1).update(deleted=True)
        return success_response()

    @swagger_auto_schema(
        operation_id='集体辅导匹配教练',
        operation_summary='集体辅导匹配教练',
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'project_group_coach_id': openapi.Schema(type=openapi.TYPE_NUMBER, description='当前集体辅导id'),
                'project_id': openapi.Schema(type=openapi.TYPE_NUMBER, description='项目id'),
                'coach_id': openapi.Schema(type=openapi.TYPE_NUMBER, description='教练id')
            }
        ),
        tags=['教练管理相关']
    )
    @action(methods=['post'], detail=False, url_path='match_group_coach')
    def match_group_coach(self, request, *args, **kwargs):
        project_id = request.data.get('project_id', None)
        coach_id = request.data.get('coach_id', None)
        project_group_coach_id = request.data.get('project_group_coach_id', None)
        if not project_id or not project_group_coach_id or not coach_id:
            return parameter_error_response()
        project = Project.objects.filter(id=project_id, deleted=False).first()
        project_group_coach = ProjectGroupCoach.objects.filter(id=project_group_coach_id, deleted=False,
                                                               project_id=project_id).first()
        coach = Coach.objects.filter(id=coach_id, deleted=False).first()
        if not project:
            return parameter_error_response('当前项目不存在')
        if not project_group_coach:
            return parameter_error_response('当前集体辅导不存在')
        if not coach:
            return parameter_error_response('当前教练不存在')
        if ProjectCoach.objects.filter(project_id=project_id, project_group_coach_id=project_group_coach_id,
                                       coach_id=coach_id, status=1, deleted=False).exists():
            return parameter_error_response('当前集体辅导已关联当前用户')
        with transaction.atomic():
            project_coach = ProjectCoach.objects.create(coach_id=coach_id,
                                                        project_group_coach_id=project_group_coach_id,
                                                        project_id=project_id, status=1)
            # 匹配了教练，将当前集体辅导下所有的被教练者辅导的教练同步为当前集体辅导教练
            all_group_coach = project_coach.project_group_coach.coach_group_module.filter(deleted=False).all()
            for index, group_coach in enumerate(all_group_coach):
                project_interview = group_coach.interview
                # project_interview = ProjectInterview.objects.filter(
                #     topic=group_coach.theme, place_category=3, place=group_coach.course_place, type=1,
                #     deleted=False, interview_subject=5,
                #     public_attr__target_user=group_coach.project_bundle.project_member.user, public_attr__type=1,
                #     public_attr__start_time=group_coach.start_course_time,
                #     public_attr__end_time=group_coach.end_course_time).first()
                if project_interview:
                    public_attr = project_interview.public_attr
                    public_attr.user = coach.user
                    public_attr.save()

                # 添加教练日程
                if index == 0:
                    schedule = Schedule.objects.create(public_attr_id=public_attr.pk, type=ScheduleTypeEnum.interview)
                    schedule.save()

        return success_response()

    @swagger_auto_schema(
        operation_id='集体辅导变更教练',
        operation_summary='集体辅导变更教练',
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'project_group_coach_id': openapi.Schema(type=openapi.TYPE_NUMBER, description='当前集体辅导id'),
                'project_id': openapi.Schema(type=openapi.TYPE_NUMBER, description='项目id'),
                'coach_id': openapi.Schema(type=openapi.TYPE_NUMBER, description='原教练id'),
                'new_coach_id': openapi.Schema(type=openapi.TYPE_NUMBER, description='现教练id')
            }
        ),
        tags=['教练管理相关']
    )
    @action(methods=['post'], detail=False, url_path='change_group_coach')
    def change_group_coach(self, request, *args, **kwargs):
        project_id = request.data.get('project_id', None)
        coach_id = request.data.get('coach_id', None)
        new_coach_id = request.data.get('new_coach_id', None)
        project_group_coach_id = request.data.get('project_group_coach_id', None)
        if not project_id or not project_group_coach_id or not coach_id or not new_coach_id:
            return parameter_error_response()
        project = ProjectMember.objects.filter(id=project_id, deleted=False).first()
        project_group_coach = ProjectGroupCoach.objects.filter(id=project_group_coach_id, deleted=False,
                                                               project_id=project_id).first()
        coach = Coach.objects.filter(id=coach_id, deleted=False).first()
        new_coach = Coach.objects.filter(id=new_coach_id, deleted=False).first()
        if not project:
            return parameter_error_response('当前项目不存在')
        if not project_group_coach:
            return parameter_error_response('当前集体辅导不存在')
        if not coach:
            return parameter_error_response('当前教练不存在')
        if not new_coach:
            return parameter_error_response('当前匹配教练不存在')
        if not ProjectCoach.objects.filter(project_id=project_id, project_group_coach_id=project_group_coach_id,
                                           coach_id=coach_id, status=1, deleted=False).exists():
            return parameter_error_response('现集体辅导匹配关系不存在')
        if ProjectCoach.objects.filter(project_id=project_id, project_group_coach_id=project_group_coach_id,
                                       coach_id=new_coach_id, status=1, deleted=False).exists():
            return parameter_error_response('当前集体辅导已与当前教练匹配')
        with transaction.atomic():
            project_coach = ProjectCoach.objects.filter(project_id=project_id, deleted=False,
                                                        project_group_coach_id=project_group_coach_id,
                                                        coach_id=coach_id, status=1).first()
            project_coach.coach_id = new_coach_id
            project_coach.save()

            # 修改了教练，将当前集体辅导下所有的被教练者辅导的教练同步为当前集体辅导教练
            all_group_coach = project_coach.project_group_coach.coach_group_module.all()
            for index, group_coach in enumerate(all_group_coach):
                project_interview = group_coach.interview
                # project_interview = ProjectInterview.objects.filter(
                #     topic=group_coach.theme, place_category=3, place=group_coach.course_place, type=1,
                #     deleted=False, interview_subject=5,
                #     public_attr__target_user=group_coach.project_bundle.project_member.user, public_attr__type=1,
                #     public_attr__start_time=group_coach.start_course_time,
                #     public_attr__end_time=group_coach.end_course_time).first()
                if project_interview:
                    public_attr = project_interview.public_attr
                    public_attr.user = new_coach.user
                    public_attr.save()
                if index == 0:
                    schedule = Schedule.objects.filter(public_attr_id=public_attr.pk, type=ScheduleTypeEnum.interview)
                    if schedule.exists():
                        schedule.first().delete()
                    Schedule.objects.create(public_attr_id=public_attr.pk, type=ScheduleTypeEnum.interview)

        return success_response()

    @swagger_auto_schema(
        operation_id='修改项目教练信息',
        operation_summary='修改项目教练信息',
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'project_coach_id': openapi.Schema(type=openapi.TYPE_NUMBER, description='项目教练标识'),
                'show_resume_id': openapi.Schema(type=openapi.TYPE_NUMBER, description='展示的简历id')
            }
        ),
        tags=['教练管理相关']
    )
    @action(methods=['post'], detail=False, url_path='update')
    def update_project_coach(self, request, *args, **kwargs):

        try:
            coach_id = request.data.get('coach_id')
            project_id = request.data.get('project_id')
            show_resume_id = request.data.get('show_resume_id')
            project_coach = ProjectCoach.objects.get(
                coach_id=coach_id, project_id=project_id, resume__isnull=False,
                deleted=False)
        except ProjectCoach.DoesNotExist:
            return parameter_error_response('项目教练不存在')
        except:
            return parameter_error_response()
        if show_resume_id:
            project_coach.show_resume_id = show_resume_id
            project_coach.save()
        return success_response()
