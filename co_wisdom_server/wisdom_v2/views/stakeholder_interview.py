from drf_yasg import openapi
from rest_framework import viewsets
from drf_yasg.utils import swagger_auto_schema
from rest_framework.decorators import action

from utils.api_response import parameter_error_response, success_response
from utils.pagination import StandardResultsSetPagination

from wisdom_v2.models_file import StakeholderInterviewModule
from wisdom_v2.models import ProjectMember, ProjectInterested
from wisdom_v2.views.stakeholder_interview_action import StakeholderInterviewModuleSerializer, \
    StakeholderListSerializer, StakeholderInterviewModuleCreateSerializer, StakeholderInterviewModuleUpdateSerializer


class StakeholderInterviewModuleViewSet(viewsets.ModelViewSet):
    queryset = StakeholderInterviewModule.objects.filter(deleted=False)
    serializer_class = StakeholderInterviewModuleSerializer

    @swagger_auto_schema(
        operation_id='后台新增利益相关者访谈配置利益相关者列表',
        operation_summary='后台新增利益相关者访谈配置利益相关者列表',
        manual_parameters=[
            openapi.Parameter(
                'project_member_id	', openapi.IN_QUERY, description='被教练者id', type=openapi.TYPE_NUMBER,
                required=True),
        ],
        tags=['后台利益相关者访谈配置相关']
    )
    @action(methods=['get'], detail=False, url_path='stakeholder_list', serializer_class=StakeholderListSerializer)
    def stakeholder_list(self, request, *args, **kwargs):
        try:
            project_member_id = request.query_params['project_member_id']
            project_member = ProjectMember.objects.get(pk=project_member_id, deleted=False)
        except (KeyError, ProjectMember.DoesNotExist):
            return parameter_error_response()
        stakeholders = ProjectInterested.objects.filter(
            master_id=project_member.user_id, project_id=project_member.project_id,
            deleted=False).order_by('-created_at')
        request.GET._mutable = True
        self.request.query_params['page_size'] = stakeholders.count()
        # 分页
        paginator = StandardResultsSetPagination()
        page_list = paginator.paginate_queryset(stakeholders, self.request)
        serializer = self.get_serializer(page_list, many=True)
        response = paginator.get_paginated_response(serializer.data)
        return success_response(response)

    @swagger_auto_schema(
        operation_id='后台利益相关者访谈配置详情',
        operation_summary='后台利益相关者访谈配置详情',
        manual_parameters=[
            openapi.Parameter(
                'project_member_id	', openapi.IN_QUERY, description='被教练者id', type=openapi.TYPE_NUMBER,
                required=True),
        ],
        tags=['后台利益相关者访谈配置相关']
    )
    @action(methods=['get'], detail=False, url_path='detail')
    def stakeholder_interview_module_detail(self, request, *args, **kwargs):
        try:
            project_member_id = request.query_params['project_member_id']
            project_member = ProjectMember.objects.get(pk=project_member_id, deleted=False)
        except (KeyError, ProjectMember.DoesNotExist):
            return parameter_error_response()
        stakeholder_interview_module = project_member.stakeholder_interview_module.filter(deleted=False).first()
        if not stakeholder_interview_module:
            return success_response()
        serializer = self.get_serializer(stakeholder_interview_module)
        return success_response(serializer.data)

    @swagger_auto_schema(
        operation_id='创建利益相关者访谈配置',
        operation_summary='创建利益相关者访谈配置',
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'project_member_id': openapi.Schema(type=openapi.TYPE_NUMBER, description='被教练者id'),
                'duration': openapi.Schema(type=openapi.TYPE_NUMBER, description='访谈时长 默认0.5，支持设置为0.5、1、1.5、2，4个值'),
                'start_date': openapi.Schema(type=openapi.TYPE_STRING, description='开始日期'),
                'end_date': openapi.Schema(type=openapi.TYPE_NUMBER, description='结束日期'),
                'stakeholder_interview_number': openapi.Schema(type=openapi.TYPE_NUMBER, description='访谈人数'),
                'coach_template_id': openapi.Schema(type=openapi.TYPE_NUMBER, description='访谈记录模版id'),
                'total_template_id': openapi.Schema(type=openapi.TYPE_NUMBER, description='访谈报告模版id'),
                'stakeholder': openapi.Schema(type=openapi.TYPE_NUMBER, description='参与访谈的人员')
            }
        ),
        tags=['后台利益相关者访谈配置相关']
    )
    def create(self, request, *args, **kwargs):
        data = request.data
        self.serializer_class = StakeholderInterviewModuleCreateSerializer
        serializer = self.get_serializer(data=data)
        serializer.is_valid(raise_exception=True)
        validated_data = serializer.validated_data
        return success_response(serializer.create(validated_data))

    @swagger_auto_schema(
        operation_id='修改利益相关者访谈配置',
        operation_summary='修改利益相关者访谈配置',
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'id': openapi.Schema(type=openapi.TYPE_NUMBER, description='化学面谈id'),
                'max_interview_number': openapi.Schema(type=openapi.TYPE_NUMBER, description='最大面谈次数'),
                'duration': openapi.Schema(type=openapi.TYPE_NUMBER, description='面谈时长'),
                'start_time': openapi.Schema(type=openapi.TYPE_STRING, description='开始日期'),
                'end_time': openapi.Schema(type=openapi.TYPE_STRING, description='结束日期'),
                'coach_source': openapi.Schema(type=openapi.TYPE_NUMBER, description='教练来源'),
                'coach': openapi.Schema(type=openapi.TYPE_ARRAY, description='教练',
                                        items=openapi.Schema(type=openapi.TYPE_NUMBER)),
                'deleted': openapi.Schema(type=openapi.TYPE_NUMBER, description='是否删除')
            }
        ),
        tags=['后台利益相关者访谈配置相关']
    )
    @action(methods=['post'], detail=False, url_path='update',
            serializer_class=StakeholderInterviewModuleUpdateSerializer)
    def update_stakeholder_interview_module(self, request, *args, **kwargs):
        try:
            stakeholder_interview_module_id = request.data['id']
            stakeholder_interview_module = StakeholderInterviewModule.objects.get(
                pk=stakeholder_interview_module_id, deleted=False)
        except (KeyError, StakeholderInterviewModule.DoesNotExist):
            return parameter_error_response()
        data = request.data.copy()
        serializer = self.get_serializer(data=data)
        serializer.is_valid(raise_exception=True)
        stakeholder_interview_module = serializer.update(stakeholder_interview_module, data)
        serializer = StakeholderInterviewModuleSerializer(stakeholder_interview_module)
        return success_response(serializer.data)