import datetime

from rest_framework import serializers

from wisdom_v2.enum.user_enum import PersonalApplyTypeEnum, CoachUserTypeEnum
from wisdom_v2.models import PersonalApply, UserContract


class PersonalApplySerializer(serializers.ModelSerializer):
    id = serializers.IntegerField(help_text='id')
    true_name = serializers.CharField(help_text='姓名')
    user_class = serializers.CharField(help_text='班次')
    email = serializers.CharField(help_text='邮箱')
    phone = serializers.CharField(help_text='手机号')
    coach_id = serializers.IntegerField(help_text='教练id')
    status = serializers.IntegerField(help_text='状态')
    auditor = serializers.SerializerMethodField(help_text='审核人')
    rejection_reason = serializers.CharField(help_text='拒绝原因')
    resume_status = serializers.SerializerMethodField(help_text='简历审核状态')
    resume_id = serializers.SerializerMethod<PERSON>ield(help_text='简历id')
    apply_date = serializers.SerializerMethodField(help_text='申请日期')
    user_id = serializers.SerializerMethodField(help_text='用户id')
    is_sign = serializers.SerializerMethodField(help_text='是否签订合同')

    class Meta:
        model = PersonalApply
        fields = ('id', 'true_name', 'user_class', 'email', 'phone', 'coach_id', 'status', 'auditor',
                  'rejection_reason', 'resume_status', 'resume_id', 'apply_date', 'user_id', 'is_sign', 'type',
                  'coach_type')

    def get_is_sign(self, obj):
        if obj.coach:
            # 如果是个人教练申请，查询个人教练是否授权
            if obj.type == PersonalApplyTypeEnum.personal.value:
                user_contract = UserContract.objects.filter(
                    user_id=obj.coach.user_id, deleted=False, personal_empower_at__isnull=False).exists()
            # 如果是见习教练申请，查询见习教练是否授权
            elif obj.type == PersonalApplyTypeEnum.internship.value:
                user_contract = UserContract.objects.filter(
                    user_id=obj.coach.user_id, deleted=False, internship_empower_at__isnull=False).exists()

            # 如果是用户转教练申请，根据申请的教练类型，查询不同的授权时间
            elif obj.type == PersonalApplyTypeEnum.user_to_coach.value:
                # 22年2月份之前的通过数据没有coach_type
                # 如果coach_type存在，使用coach_type，否则使用coach的coach_type
                if obj.coach_type:
                    coach_type = obj.coach_type
                elif obj.coach:
                    coach_type = obj.coach.coach_type
                else:
                    coach_type = None

                # 根据coach_type判断教练类型，查询对应类型的是否授权
                if coach_type == CoachUserTypeEnum.student.value:
                    user_contract = UserContract.objects.filter(
                        user_id=obj.coach.user_id, deleted=False, internship_empower_at__isnull=False).exists()
                elif coach_type in CoachUserTypeEnum.personal_coach_value():
                    user_contract = UserContract.objects.filter(
                        user_id=obj.coach.user_id, deleted=False, personal_empower_at__isnull=False).exists()
                else:
                    user_contract = False

            else:
                user_contract = False
            return user_contract
        return False

    def get_user_id(self, obj):
        if obj.coach:
            return obj.coach.user_id

    def get_resume_status(self, obj):
        if obj.coach and obj.coach.platform_order_receiving_status:
            return 2
        return 1

    def get_resume_id(self, obj):
        if obj.coach:
            return obj.coach.resumes.filter(is_customization=False, deleted=False).first().pk

    def get_auditor(self, obj):
        if obj.auditor:
            return obj.auditor.true_name

    def get_apply_date(self, obj):
        return datetime.datetime.strftime(obj.created_at, '%Y-%m-%d')
