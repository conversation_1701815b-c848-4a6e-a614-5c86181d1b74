from django.db import transaction
from drf_yasg import openapi
from drf_yasg.utils import swagger_auto_schema
from rest_framework import viewsets
from rest_framework.decorators import action

from utils.api_response import parameter_error_response, success_response
from wisdom_v2.common import project_service_public
from wisdom_v2.models import ProjectMember, Project
from wisdom_v2.models_file import ProjectServiceStage, ProjectServiceContent, ProjectMemberServiceContent, \
    ProjectServiceMember, ServiceStage
from wisdom_v2.views.project_member_service_action import ProjectMemberServiceSerializer


class ProjectMemberServiceViewSet(viewsets.ModelViewSet):
    queryset = ProjectMemberServiceContent.objects.filter(deleted=False).order_by('-created_at')
    serializer_class = ProjectMemberServiceSerializer

    @swagger_auto_schema(
        operation_id='修改项目用户服务',
        operation_summary='修改项目用户服务',
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'project_member_id': openapi.Schema(type=openapi.TYPE_NUMBER, description='项目id'),
                'is_stage': openapi.Schema(type=openapi.TYPE_NUMBER, description='服务配置类型 1-分阶段 2-不分阶段')
            }
        ),
        tags=['服务配置']
    )
    @action(methods=['post'], detail=False, url_path='update')
    def update_project_member_service(self, request, *args, **kwargs):
        try:
            project_member_id = request.data.get('project_member_id')
            is_stage = request.data.get('is_stage')
            project_member = ProjectMember.objects.get(id=project_member_id, deleted=False)
        except ProjectMember.DoesNotExist:
            return parameter_error_response('项目不存在')
        except Exception as e:
            return parameter_error_response()

        service_stage = ServiceStage.objects.filter(project_member_id=project_member_id, deleted=False).first()
        if service_stage and is_stage == service_stage.is_stage:
            return parameter_error_response('服务配置类型一致，无需修改')

        with transaction.atomic():
            if service_stage:
                if is_stage:
                    stage = ProjectServiceStage.objects.create(project_member=project_member, stage_name='阶段名称')
                    service_content = ProjectMemberServiceContent.objects.filter(project_member=project_member,
                                                                                 deleted=False)
                    service_content.update(service_stage=stage)
                else:
                    project_member.project_service_stage.filter(deleted=False).update(deleted=True)
                    service_content = ProjectMemberServiceContent.objects.filter(project_member=project_member,
                                                                                 deleted=False)
                    service_content.update(service_stage=None)
                service_stage.is_stage = is_stage
                service_stage.save()
                # 和项目服务配置解绑
                ProjectServiceMember.objects.filter(member_service__project_member=project_member,
                                                    deleted=False).update(deleted=True)
            else:
                ServiceStage.objects.create(project_member_id=project_member_id, is_stage=is_stage)
        return success_response()

    @swagger_auto_schema(
        operation_id='移除项目用户服务配置模块',
        operation_summary='移除项目用户服务配置模块',
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'model_id': openapi.Schema(type=openapi.TYPE_NUMBER, description='服务模块id')
            }
        ),
        tags=['服务配置']
    )
    @action(methods=['post'], detail=False, url_path='model/del')
    def del_project_member_service_modele(self, request, *args, **kwargs):
        try:
            model_id = request.data.get('model_id')
            service_content = ProjectMemberServiceContent.objects.get(id=model_id, deleted=False)
        except ProjectMemberServiceContent.DoesNotExist:
            return parameter_error_response('服务模块不存在')
        except Exception as e:
            return parameter_error_response()

        with transaction.atomic():
            if service_content.object_ids:
                object_ids = service_content.object_ids
                for item in object_ids:
                    project_service_public.del_project_member_service(service_content, item)
            service_content.deleted = True
            service_content.save()
            # 和项目服务配置解绑
            ProjectServiceMember.objects.filter(member_service=service_content, deleted=False).update(deleted=True)
        return success_response()

    @swagger_auto_schema(
        operation_id='项目用户复制项目服务配置',
        operation_summary='项目用户复制项目服务配置',
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'project_member_id': openapi.Schema(type=openapi.TYPE_NUMBER, description='项目用户id')
            }
        ),
        tags=['服务配置']
    )
    @action(methods=['post'], detail=False, url_path='service/config')
    def post_project_member_copy_service(self, request, *args, **kwargs):
        try:
            project_member_id = request.data.get('project_member_id')
            project_member = ProjectMember.objects.get(id=project_member_id, deleted=False)
        except ProjectMember.DoesNotExist:
            return parameter_error_response('项目用户不存在')
        except Exception as e:
            return parameter_error_response()

        if ProjectMemberServiceContent.objects.filter(project_member=project_member, deleted=False).exists():
            return parameter_error_response('项目用户已配置服务')
        if ProjectServiceStage.objects.filter(project_member=project_member, deleted=False).exists():
            return parameter_error_response('项目用户已配置服务')
        if (not ProjectServiceContent.objects.filter(project=project_member.project, deleted=False).exists() and
                not ProjectServiceStage.objects.filter(project=project_member.project, deleted=False).exists()):
            return parameter_error_response('项目没有配置服务')

        with transaction.atomic():
            project_service_public.create_project_member_service_all(project_member)
            stage = ServiceStage.objects.filter(project_id=project_member.project_id, deleted=False).first()
            user_stage = ServiceStage.objects.filter(project_member=project_member, deleted=False).first()
            if user_stage:
                user_stage.is_stage = stage.is_stage
                user_stage.save()
            else:
                ServiceStage.objects.create(project_member=project_member, is_stage=stage.is_stage, deleted=False)
        return success_response()

    @swagger_auto_schema(
        operation_id='修改项目用户服务配置模块数据',
        operation_summary='修改项目用户服务配置模块数据',
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'model_id': openapi.Schema(type=openapi.TYPE_NUMBER, description='服务模块id'),
                'content_id': openapi.Schema(type=openapi.TYPE_NUMBER, description='数据标识'),
                'content': openapi.Schema(type=openapi.TYPE_OBJECT, description='具体内容'),
            }
        ),
        tags=['服务配置']
    )
    @action(methods=['post'], detail=False, url_path='content/update')
    def update_project_service_content(self, request, *args, **kwargs):

        try:
            model_id = request.data.get('model_id')
            content_id = request.data.get('content_id')
            deleted = request.data.get('deleted')
            content = request.data.get('content')
            service_model = ProjectMemberServiceContent.objects.get(id=model_id, deleted=False)
        except ProjectMemberServiceContent.DoesNotExist:
            return parameter_error_response('服务模块不存在')
        except Exception as e:
            return parameter_error_response()
        if content and not isinstance(content, dict):
            return parameter_error_response('参数格式错误,需要字典格式')
        with transaction.atomic():
            if deleted:
                project_service_public.del_project_member_service(service_model, content_id)
                service_model.object_ids.remove(str(content_id))
                service_model.save()
                disable_relation = True
            else:
                state = project_service_public.project_service_content_validate(
                    service_model, [content], service_model.project_member, False)
                if state:
                    return parameter_error_response(state)
                project_service_public.project_member_service_content_update(service_model, content_id, content, False)

                # 改变观察反馈，化学面谈，利益相关者访谈，教练任务改变用户侧特有数据不取消关联
                # disable_relation = project_service_public.is_member_update_content_unlink_existing(service_model, content_id, content)
                # 只是更新个人的服务配置，不用解除关联
                disable_relation = False
            if disable_relation:
                ProjectServiceMember.objects.filter(member_service=service_model, deleted=False).update(deleted=True)

        return success_response()

    @swagger_auto_schema(
        operation_id='添加项目用户服务配置模块数据',
        operation_summary='添加项目用户服务配置模块数据',
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'model_id': openapi.Schema(type=openapi.TYPE_NUMBER, description='服务模块id'),
                'model_data': openapi.Schema(type=openapi.TYPE_OBJECT, description='服务模块数据'),
            }
        ),
        tags=['服务配置']
    )
    @action(methods=['post'], detail=False, url_path='content/add')
    def add_project_member_service_content(self, request, *args, **kwargs):
        try:
            model_id = request.data.get('model_id')
            model_content = request.data.get('model_content')
            service_model = ProjectMemberServiceContent.objects.get(id=model_id, deleted=False)
        except ProjectMemberServiceContent.DoesNotExist:
            return parameter_error_response('服务模块不存在')
        except Exception as e:
            return parameter_error_response()
        if not model_content:
            return parameter_error_response('模块数据不能为空')
        if not isinstance(model_content, list):
            return parameter_error_response('模块数据必须为列表')
        state = project_service_public.project_service_content_validate(service_model, model_content, service_model.project_member)
        if state:
            return parameter_error_response(state)
        with transaction.atomic():
            object_ids = project_service_public.project_member_service_content_create(model_content, service_model, is_project=False)
            object_ids = [str(object_id) for object_id in object_ids]
            service_model_object_ids = service_model.object_ids
            service_model.object_ids = (service_model_object_ids if service_model_object_ids else []) + object_ids
            service_model.save()
            ProjectServiceMember.objects.filter(member_service=service_model, deleted=False).update(deleted=True)
        return success_response()
