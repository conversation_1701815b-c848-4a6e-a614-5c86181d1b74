from rest_framework import serializers

from ..app_views.app_coachee_action_plan import ActionPlanSerializer
from ..app_views.app_coachee_diary import DiarySerializer
from ..app_views.app_coachee_habit import HabitSerializer
from ..models import Article, CapacityTag, Habit, Diary, ActionPlan, Theme


class ArticleSerializers(serializers.ModelSerializer):
    capacity = serializers.SerializerMethodField()
    top = serializers.SerializerMethodField(help_text='是否置顶')
    # topic_top = serializers.SerializerMethodField(help_text='主题是否置顶')
    created_at = serializers.SerializerMethodField(help_text='创建时间', read_only=True)
    action = serializers.SerializerMethodField(help_text='文章关联的用户行为', read_only=True)
    theme = serializers.SerializerMethodField(help_text='文章关联的主题', read_only=True)

    class Meta:
        model = Article
        exclude = ('updated_at',)

    def get_theme(self, obj):
        themes = Theme.objects.filter(theme_article_relation__article=obj, theme_article_relation__deleted=False,
                                      deleted=False).distinct()
        if themes.exists():
            data = [{"id": theme.id, "name": theme.name, "is_relation": True} for theme in themes]
            return data
        return []

    def get_capacity(self, obj):
        try:
            if obj.capacity_tag_id:
                capacity = CapacityTag.objects.get(pk=obj.capacity_tag_id)
                return capacity.title
        except CapacityTag.DoesNotExist:
            return

    def get_created_at(self, obj):
        return obj.created_at.strftime('%Y-%m-%d %H:%M:%S')

    def get_top(self, obj):
            return obj.get_is_top

    # def get_topic_top(self, obj):
    #         return obj.get_is_topic_top

    def create(self, validated_data):
        instance = Article(**validated_data)
        instance.save()
        return instance

    def get_action(self, obj):
        if self.context.get('user'):
            raw_habit = []
            raw_action_plan = []

            habit = Habit.objects.filter(
                public_attr__article_user_public_attr__article=obj,
                public_attr__user=self.context.get('user'),
                deleted=False
            ).all()
            for item in habit:
                raw_habit.append(HabitSerializer(item).data)

            diary = Diary.objects.filter(
                public_attr__article_user_public_attr__article=obj,
                public_attr__user=self.context.get('user'),
                deleted=False
            ).first()
            if diary:
                raw_diary = DiarySerializer(diary).data
            else:
                raw_diary = None

            action_plan = ActionPlan.objects.filter(
                public_attr__article_user_public_attr__article=obj,
                public_attr__user=self.context.get('user'),
                deleted=False
            ).all()
            for item in action_plan:
                raw_action_plan.append(ActionPlanSerializer(item).data)

            return {'habit': raw_habit, 'diary': raw_diary, 'action_plan': raw_action_plan}
        else:
            return None


class ArticleTopicSerializers(serializers.ModelSerializer):
    count = serializers.SerializerMethodField(help_text='数量')
    topic = serializers.SerializerMethodField(help_text='主题')

    class Meta:
        model = Article
        fields = ['count', 'topic']

    def get_count(self, obj):
        return obj.id.get('count')

    def get_topic(self, obj):
        return obj.id.get('topic')
