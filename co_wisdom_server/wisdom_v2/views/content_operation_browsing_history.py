from drf_yasg import openapi
from drf_yasg.utils import swagger_auto_schema

from rest_framework import viewsets

from utils.pagination import StandardResultsSetPagination
from .content_operation_browsing_history_actions import ContentOperationBrowsingHistorySerializers
from utils.api_response import success_response, parameter_error_response
from ..models import ContentOperationBrowsingHistory


class ContentOperationBrowsingHistoryViewSet(viewsets.ModelViewSet):
    queryset = ContentOperationBrowsingHistory.objects.filter().order_by('pk')
    serializer_class = ContentOperationBrowsingHistorySerializers

    @swagger_auto_schema(
        operation_id='浏览记录列表',
        operation_summary='浏览记录列表',
        manual_parameters=[
            openapi.Parameter('article_id', openapi.IN_QUERY, description='内容展示文章ID', type=openapi.TYPE_STRING),
            openapi.Parameter('project_id', openapi.IN_QUERY, description='项目id(逗号分隔)｜ 1,2,3',
                              type=openapi.TYPE_STRING),
            openapi.Parameter('page', openapi.TYPE_ARRAY, description='第几页', type=openapi.TYPE_NUMBER),
            openapi.Parameter('page_size', openapi.IN_QUERY, description='每页显示条数默认15条', type=openapi.TYPE_NUMBER),
        ],
        tags=['内容管理数据相关']
    )
    def list(self, request, *args, **kwargs):
        content_browsing_history = self.get_queryset()

        if not request.query_params.get('article_id'):
            return parameter_error_response()

        if request.query_params.get('project_id'):
            content_browsing_history = content_browsing_history.filter(
                project__pk__in=request.query_params.get('project_id').split(','))

        content_browsing_history = content_browsing_history.filter(
            article_id=request.query_params.get('article_id'))

        # 分页
        paginator = StandardResultsSetPagination()
        page_list = paginator.paginate_queryset(content_browsing_history, self.request)
        serializer = self.get_serializer(page_list, many=True)
        response = paginator.get_paginated_response(serializer.data)

        return success_response(response)
