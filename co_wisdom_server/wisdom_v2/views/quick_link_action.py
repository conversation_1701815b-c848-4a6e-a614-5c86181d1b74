from rest_framework import serializers

from wisdom_v2.enum.service_content_enum import AssociationTypeEnum
from wisdom_v2.models import QuickLink
from wisdom_v2.views.article_actions import ArticleSerializers
from wisdom_v2.views.theme_actions import ThemeSerializer


class QuickLinkSerializer(serializers.ModelSerializer):
    id = serializers.IntegerField(read_only=True)
    name = serializers.CharField(help_text='名称', max_length=32)
    image_url = serializers.CharField(help_text='图片地址', max_length=256, allow_blank=True, allow_null=True)
    display_position = serializers.IntegerField(help_text='展示位置')
    creator = serializers.Char<PERSON>ield(read_only=True, source='creator.true_name')
    created_at = serializers.SerializerMethodField(help_text='创建时间', read_only=True)
    page_text = serializers.SerializerMethodField(help_text='跳换页面描述')
    article = serializers.SerializerMethodField(help_text='文章信息')
    article_id = serializers.IntegerField(help_text='文章标识')
    status = serializers.IntegerField(help_text='上架状态')
    suitable_object = serializers.IntegerField(help_text='适用对象', allow_null=True)
    association_type = serializers.IntegerField(help_text='关联类型')
    theme = serializers.SerializerMethodField(help_text='主题详情')
    deleted = serializers.BooleanField(help_text='是否删除')
    page_url = serializers.CharField(help_text='跳转链接', max_length=256, allow_blank=True, allow_null=True)

    class Meta:
        model = QuickLink
        fields = ('id', 'name', 'image_url', 'display_position', 'creator', 'created_at', 'deleted',
                  'page_text', 'article', 'status', 'suitable_object', 'association_type', 'theme', 'page_url', 'article_id')

    def get_article(self, obj):
        if obj.association_type == AssociationTypeEnum.article:
            article_data = ArticleSerializers(obj.article).data
            return article_data

    def get_theme(self, obj):
        if obj.association_type == AssociationTypeEnum.theme:
            theme_data = ThemeSerializer(obj.theme).data
            return theme_data

    def get_created_at(self, obj):
        return obj.created_at.strftime('%Y-%m-%d %H:%M:%S')

    def get_page_text(self, obj):
        if obj.association_type == AssociationTypeEnum.article:
            return f'文章详情/{obj.article.title}'
        elif obj.association_type == AssociationTypeEnum.theme:
            return f'主题详情/{obj.theme.name}'
        elif obj.association_type == AssociationTypeEnum.external_link:
            return '外部链接'
        elif obj.association_type == AssociationTypeEnum.interview:
            return '预约辅导'
        else:
            return '无跳转'