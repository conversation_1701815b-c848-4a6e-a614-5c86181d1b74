from django.db.models import Case, When, Q
from rest_framework import serializers

from wisdom_v2.models import InterviewRecordTemplate, InterviewRecordTemplateQuestion, InterviewRecordTemplateAnswer, \
    TotalTemplate
from wisdom_v2.views.interview_question_actions import InterviewRecordTemplateQuestionSerializers


class InterViewRecordTemplateSerializers(serializers.ModelSerializer):
    id = serializers.IntegerField(read_only=True, help_text='id')
    name = serializers.CharField(read_only=True, help_text='名称')
    preface = serializers.CharField(read_only=True, help_text='前言')
    type = serializers.Char<PERSON>ield(read_only=True, help_text='教练形式 | 1：线下集体辅导')
    role = serializers.CharField(read_only=True, help_text='填写记录的角色 | 1：教练；2：被教练者')
    created_at = serializers.DateTimeField(help_text='创建时间', format='%Y-%m-%d %H:%M', read_only=True)
    available = serializers.IntegerField(read_only=True, help_text='是否上架')
    modify = serializers.SerializerMethodField(read_only=True, help_text='是否可修改')

    class Meta:
        model = InterviewRecordTemplate
        fields = ['id', 'name', 'type', 'role', 'available', 'modify', 'preface', 'created_at']

    def get_modify(self, obj):
        if InterviewRecordTemplateAnswer.objects.filter(question__template=obj).exists():
            return False
        return True


class InterViewRecordTemplateSerializersCreate(serializers.ModelSerializer):
    class Meta:
        model = InterviewRecordTemplate
        exclude = ('created_at', 'updated_at')


class InterViewRecordTemplateSerializersDetail(serializers.ModelSerializer):
    question = serializers.SerializerMethodField()
    name = serializers.SerializerMethodField()

    class Meta:
        model = InterviewRecordTemplate
        exclude = ('created_at', 'updated_at')

    def get_question(self, obj):
        try:
            order = Case(*[When(pk=order, then=pos) for pos, order in enumerate(eval(obj.questions_order))])
            question = InterviewRecordTemplateQuestion.objects.filter(
                template_id=obj.pk, deleted=False).order_by(order).all()
            question = InterviewRecordTemplateQuestionSerializers(question, many=True)
            return question.data
        except InterviewRecordTemplateQuestion.DoesNotExist:
            return

    def get_name(self, obj):
        total_template = TotalTemplate.objects.filter(
            Q(coach_template_id=obj.pk) |
            Q(coachee_template_id=obj.pk) |
            Q(stakeholder_template_id=obj.pk))
        if total_template.exists():
            return total_template.first().title
        else:
            return obj.name
