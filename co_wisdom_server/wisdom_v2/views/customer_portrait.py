from drf_yasg import openapi
from rest_framework import viewsets
from drf_yasg.utils import swagger_auto_schema
from rest_framework.decorators import action

from utils.pagination import StandardResultsSetPagination
from wisdom_v2.models import CustomerPortrait
from utils.api_response import success_response, parameter_error_response
from wisdom_v2.views.customer_portrait_actions import CustomerPortraitBaseSerializers, \
    CustomerPortraitDetailsSerializers


class CustomerPortraitBaseViewSet(viewsets.ModelViewSet):
    queryset = CustomerPortrait.objects.filter(deleted=False).order_by('-created_at')
    serializer_class = CustomerPortraitBaseSerializers
    authentication_classes = []

    @swagger_auto_schema(
        operation_id='查看项目客户画像列表',
        operation_summary='查看项目客户画像列表',
        manual_parameters=[
            openapi.Parameter(
                'project_id', openapi.IN_QUERY, description='项目id', type=openapi.TYPE_NUMBER, required=True),
            openapi.Parameter('page', openapi.IN_QUERY, description='第几页', type=openapi.TYPE_NUMBER),
            openapi.Parameter('page_size', openapi.IN_QUERY, description='每页显示条数默认15条', type=openapi.TYPE_NUMBER)
        ],
        tags=['客户画像相关']
    )
    def list(self, request, *args, **kwargs):
        try:
            project_id = request.query_params.get('project_id')
            customer_portrait = CustomerPortrait.objects.filter(
                project_id=project_id, deleted=False).order_by('-type', '-created_at').all()
        except:
            return parameter_error_response()
        # 分页
        paginator = StandardResultsSetPagination()
        page_list = paginator.paginate_queryset(customer_portrait, self.request)
        serializer = CustomerPortraitDetailsSerializers(page_list, many=True)
        response = paginator.get_paginated_response(serializer.data)
        return success_response(response)

    @swagger_auto_schema(
        operation_id='修改客户画像',
        operation_summary='修改客户画像',
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'portrait_id': openapi.Schema(type=openapi.TYPE_STRING, description='客户画像标识'),
                }
        ),
        tags=['客户画像相关']
    )
    @action(methods=['post'], detail=False, url_path='update')
    def update_customer_portrait(self, request, *args, **kwargs):
        try:
            customer_portrait_id = request.data.get('portrait_id')
            customer_portrait = CustomerPortrait.objects.get(pk=customer_portrait_id)
        except Exception:
            return parameter_error_response('未获取到客户画像信息')
        data = request.data.copy()
        serializer = self.get_serializer(data=data)
        serializer.is_valid(raise_exception=True)
        portrait = serializer.update(customer_portrait, data)
        serializer = CustomerPortraitDetailsSerializers(portrait)
        return success_response(serializer.data)

    @swagger_auto_schema(
        operation_id='创建客户画像',
        operation_summary='创建客户画像',
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'name': openapi.Schema(type=openapi.TYPE_STRING, description='客户姓名'),
                }
        ),
        tags=['客户画像相关']
    )
    @action(methods=['post'], detail=False, url_path='add')
    def add_customer_portrait(self, request, *args, **kwargs):
        return success_response(super(CustomerPortraitBaseViewSet, self).create(request, *args, **kwargs).data)

    @swagger_auto_schema(
        operation_id='删除客户画像',
        operation_summary='删除客户画像',
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'portrait_id': openapi.Schema(type=openapi.TYPE_STRING, description='客户画像id'),
            }
        ),
        tags=['客户画像相关']
    )
    @action(methods=['post'], detail=False, url_path='deleted')
    def deleted_customer_portrait(self, request, *args, **kwargs):

        try:
            change_observation_id = request.data.get('portrait_id')

            CustomerPortrait.objects.filter(
                pk=change_observation_id,
                deleted=False,
            ).update(deleted=True)
        except Exception:
            return parameter_error_response('客户画像信息修改失败')

        return success_response()
