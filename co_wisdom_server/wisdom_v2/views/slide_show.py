from drf_yasg import openapi
from rest_framework.decorators import action
from drf_yasg.utils import swagger_auto_schema
from rest_framework import viewsets

from wisdom_v2.views.slide_show_actions import SlideShowSerializers
from wisdom_v2.models import SlideShow
from utils.pagination import StandardResultsSetPagination
from utils.api_response import success_response, parameter_error_response


class SlideShowViewSet(viewsets.ModelViewSet):
    queryset = SlideShow.objects.filter(deleted=False).order_by('-enabled', 'order', '-created_at')
    serializer_class = SlideShowSerializers

    @swagger_auto_schema(
        operation_id='轮播图列表',
        operation_summary='轮播图列表',
        manual_parameters=[
            openapi.Parameter('keyword', openapi.IN_QUERY, description='关键字（轮播图的文字）', type=openapi.TYPE_STRING),
            openapi.Parameter('suitable_object', openapi.IN_QUERY, description='适用对象', type=openapi.TYPE_NUMBER),
            openapi.Parameter('status	', openapi.IN_QUERY, description='状态 1-上架 2-下架', type=openapi.TYPE_NUMBER),
            openapi.Parameter('page', openapi.IN_QUERY, description='第几页', type=openapi.TYPE_NUMBER),
            openapi.Parameter('page_size', openapi.IN_QUERY, description='每页显示条数默认15条', type=openapi.TYPE_NUMBER),
        ],
        tags=['轮播图相关']
    )
    def list(self, request, *args, **kwargs):

        slide_show = self.get_queryset()
        if request.query_params.get('keyword', None):
            slide_show = slide_show.filter(title__icontains=request.query_params.get('keyword').lower())
        if request.query_params.get('suitable_object', None):
            slide_show = slide_show.filter(suitable_object=request.query_params.get('suitable_object'))
        if request.query_params.get('status'):
            enabled = True if str(request.query_params.get('status')) == '1' else False
            slide_show = slide_show.filter(enabled=enabled)

        # 分页
        paginator = StandardResultsSetPagination()
        page_list = paginator.paginate_queryset(slide_show, self.request)
        serializer = self.get_serializer(page_list, many=True)
        response = paginator.get_paginated_response(serializer.data)
        return success_response(response)

    @swagger_auto_schema(
        operation_id='轮播图详情',
        operation_summary='轮播图详情',
        manual_parameters=[
            openapi.Parameter('slide_show_id', openapi.IN_QUERY, description='轮播图id', type=openapi.TYPE_NUMBER),
        ],
        tags=['轮播图相关']
    )
    @action(methods=['get'], detail=False, url_path='detail')
    def article_detail(self, request, *args, **kwargs):
        try:
            instance = SlideShow.objects.get(pk=request.query_params.get('slide_show_id', 0))
        except SlideShow.DoesNotExist as e:
            return parameter_error_response()

        serializer = self.get_serializer(instance)
        return success_response(serializer.data)

    @swagger_auto_schema(
        operation_id='修改轮播图信息',
        operation_summary='修改轮播图信息',
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'article_id': openapi.Schema(type=openapi.TYPE_NUMBER, description='文章ID'),
                'slide_show_id': openapi.Schema(type=openapi.TYPE_NUMBER, description='轮播图ID'),
                'url_type': openapi.Schema(type=openapi.TYPE_NUMBER, description='链接类型 | 1:视野拓展 2:关于教练 3:预约辅导'),
                'title': openapi.Schema(type=openapi.TYPE_STRING, description='轮播图上文字'),
                'image_url': openapi.Schema(type=openapi.TYPE_STRING, description='图片url'),
                'url': openapi.Schema(type=openapi.TYPE_STRING, description='跳转url'),
                'order': openapi.Schema(type=openapi.TYPE_STRING, description='排序'),
                'enabled': openapi.Schema(type=openapi.TYPE_BOOLEAN, description='是否启用'),
                'deleted': openapi.Schema(type=openapi.TYPE_BOOLEAN, description='设置删除'),
                'theme_id': openapi.Schema(type=openapi.TYPE_NUMBER, description='主题ID')
            }
        ),
        tags=['轮播图相关']
    )
    @action(methods=['post'], detail=False, url_path='update')
    def company_update(self, request, *args, **kwargs):
        try:
            instance = SlideShow.objects.get(pk=request.data.get('slide_show_id', 0))
            # if request.data.get('enabled', None):
            #     instance_count = SlideShow.objects.filter(enabled=True).all().count()
            #     if instance_count >= 6:
            #         return parameter_error_response('最多允许6条轮播图上架，请先取消已上架的轮播图')
        except SlideShow.DoesNotExist as e:
            return parameter_error_response()

        if request.data.get('article_id', 0):
            instance.article_id = request.data.get('article_id', 0)
            instance.save()
        if request.data.get('theme_id', 0):
            instance.theme_id = request.data.get('theme_id', 0)
            instance.save()
        data = request.data.copy()
        serializer = self.get_serializer(instance, data=data, partial=True)
        serializer.is_valid(raise_exception=True)
        serializer.save()
        return success_response(serializer.data)

    @swagger_auto_schema(
        operation_id='创建轮播图',
        operation_summary='创建轮播图',
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'article_id': openapi.Schema(type=openapi.TYPE_NUMBER, description='文章ID'),
                'theme_id': openapi.Schema(type=openapi.TYPE_NUMBER, description='主题ID'),
                'title': openapi.Schema(type=openapi.TYPE_NUMBER, description='轮播图上文字'),
                'image_url': openapi.Schema(type=openapi.TYPE_NUMBER, description='图片url'),
                'url': openapi.Schema(type=openapi.TYPE_STRING, description='跳转url'),
                'url_type': openapi.Schema(type=openapi.TYPE_NUMBER, description='链接类型 | 1:视野拓展 2:关于教练 3:预约辅导 4:外部链接 5:无跳转'),
                'order': openapi.Schema(type=openapi.TYPE_STRING, description='排序'),
                'enabled': openapi.Schema(type=openapi.TYPE_BOOLEAN, description='是否启用'),
                'deleted': openapi.Schema(type=openapi.TYPE_BOOLEAN, description='设置删除'),
                'suitable_object': openapi.Schema(type=openapi.TYPE_STRING, description='适用对象'),
            }
        ),
        tags=['轮播图相关']
    )
    def create(self, request, *args, **kwargs):
        question = SlideShow(
            theme_id=request.data.get('theme_id'),
            article_id=request.data.get('article_id'),
            url_type=request.data.get('url_type'),
            title=request.data.get('title'),
            image_url=request.data.get('image_url'),
            url=request.data.get('url'),
            order=request.data.get('order', 6),
            suitable_object=request.data.get('suitable_object')
        )
        question.save()
        serializer = self.get_serializer(question)
        return success_response(serializer.data)
