from django.db.models import Q
from rest_framework import serializers

from utils.queryset import multiple_field_distinct
from ..common import company_public
from ..models import Company, UserBackend, User
from wisdom_v2.enum.user_backend_enum import UserBackendTypeEnum


class CompanySerializers(serializers.ModelSerializer):
    company_manage = serializers.SerializerMethodField()
    project_count = serializers.SerializerMethodField()
    company_member_count = serializers.SerializerMethodField()
    created_at = serializers.DateTimeField(format="%Y-%m-%d %H:%M:%S", required=False, read_only=True, help_text='创建时间')

    # project = serializers.SerializerMethodField()

    class Meta:
        model = Company
        exclude = ('updated_at',)

    def create(self, validated_data):
        instance = Company(**validated_data)
        instance.save()
        company_public.update_company_tag(instance.id)
        return instance

    def get_company_manage(self, obj):
        user_backend = UserBackend.objects.filter(
            (Q(company=obj.id) |
             Q(project__company_id=obj.id)),
            role__name='企业管理员', deleted=False)
        user_backend = multiple_field_distinct(user_backend, ['user_id'])

        data = [{"id": i.id, "true_name": i.user.cover_name, "name": i.user.name, "email": i.user.email} for i in user_backend if i]
        return data

    def get_project_count(self, obj):
        return obj.company_project.filter(deleted=False).count()

    def get_company_member_count(self, obj):
        return obj.company_user.count()

    # def get_project(self, obj):
    #     data = obj.company_project.values_list('id', 'name', 'type', 'start_time', 'end_time')
        # 辅导记录，报告，测评
        # 辅导记录  projectinterviewrecord


class ProjectCompanyListSerializer(serializers.ModelSerializer):
    id = serializers.IntegerField(help_text='企业id')
    short = serializers.CharField(help_text='企业简称')

    class Meta:
        fields = ['id', 'short']
        model = Company
