from drf_yasg import openapi
from rest_framework.decorators import action
from rest_framework import viewsets
from drf_yasg.utils import swagger_auto_schema

from wisdom_v2.common import company_interview_public
from utils.pagination import StandardResultsSetPagination
from utils.api_response import success_response, parameter_error_response
from wisdom_v2.models_file.company_interview import CompanyInterview
from .company_interview_action import CompanyInterviewSerializer


class CompanyInterviewViewSet(viewsets.ModelViewSet):

    queryset = CompanyInterview.objects.all().order_by('-created_at')
    serializer_class = CompanyInterviewSerializer

    @swagger_auto_schema(
        operation_id='企业面试列表',
        operation_summary='企业面试列表',
        manual_parameters=[
            openapi.Parameter('project_id', openapi.IN_QUERY, description='项目id', type=openapi.TYPE_NUMBER,
                              required=True)
        ],
        tags=['企业面试相关']
    )
    def list(self, request, *args, **kwargs):
        project_id = request.query_params.get('project_id')
        if project_id:
            queryset = self.get_queryset().filter(project_id=project_id, deleted=False)
        else:
            queryset = self.get_queryset().filter(deleted=False)
        # 分页
        paginator = StandardResultsSetPagination()
        page_list = paginator.paginate_queryset(queryset, self.request)
        serializer = self.serializer_class(page_list, many=True)
        response = paginator.get_paginated_response(serializer.data)
        return success_response(response)


    @swagger_auto_schema(
        operation_id='企业面试详情',
        operation_summary='企业面试详情',
        manual_parameters=[
            openapi.Parameter('interview_id', openapi.IN_QUERY, description='面试id', type=openapi.TYPE_NUMBER,
                              required=True),
        ],
        tags=['企业面试相关']
    )
    @action(methods=['get'], detail=False, url_path='detail')
    def get_company_interview_detail(self, request, *args, **kwargs):
        try:
            interview_id = request.query_params.get('interview_id')
            instance = self.get_queryset().get(id=interview_id)
        except CompanyInterview.DoesNotExist:
            return parameter_error_response('面试不存在')
        serializer = self.get_serializer(instance)
        return success_response(serializer.data)
    
    @swagger_auto_schema(
        operation_id='创建企业面试',
        operation_summary='创建企业面试',
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'project_id': openapi.Schema(type=openapi.TYPE_NUMBER, description='项目id'),
                'company_id': openapi.Schema(type=openapi.TYPE_NUMBER, description='公司id'),
                'coach_id': openapi.Schema(type=openapi.TYPE_NUMBER, description='教练id'),
                'remark': openapi.Schema(type=openapi.TYPE_STRING, description='备注'),
                'start_time': openapi.Schema(type=openapi.TYPE_STRING, description='开始时间'),
                'end_time': openapi.Schema(type=openapi.TYPE_STRING, description='结束时间'),
            }
        ),
        tags=['企业面试相关']
    )
    def create(self, request, *args, **kwargs):
        return success_response(super().create(request, *args, **kwargs).data)


    @swagger_auto_schema(
        operation_id='修改企业面试',
        operation_summary='修改企业面试',
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'remark': openapi.Schema(type=openapi.TYPE_STRING, description='备注'),
                'start_time': openapi.Schema(type=openapi.TYPE_STRING, description='开始时间'),
                'end_time': openapi.Schema(type=openapi.TYPE_STRING, description='结束时间'),
            }
        ),
        tags=['企业面试相关']
    )
    def update(self, request, *args, **kwargs):
        return success_response(super().update(request, *args, **kwargs).data)


    # 发送消息提醒
    @swagger_auto_schema(
        operation_id='发送面试通知',
        operation_summary='发送面试通知',
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'interview_id': openapi.Schema(type=openapi.TYPE_NUMBER, description='面试id')
            }
        ),
        tags=['企业面试相关']
    )
    @action(methods=['post'], detail=False, url_path='remind')
    def send_notification(self, request, *args, **kwargs):
        try:
            interview_id = request.data.get('interview_id')
            
            if not interview_id:
                return parameter_error_response('面试id不能为空')
            
            interview = self.get_queryset().get(id=interview_id)
            ret = company_interview_public.send_remind(interview_id)
            
            return success_response({'message': '提醒发送成功' if ret else '提醒发送出错'})
            
        except CompanyInterview.DoesNotExist:
            return parameter_error_response('面试不存在')
        except Exception as e:
            return parameter_error_response(f'发送通知失败: {str(e)}')
