from datetime import datetime
from drf_yasg import openapi
from rest_framework.decorators import action
from drf_yasg.utils import swagger_auto_schema

from django.db.models import Q, Count

from utils import get_use_time
from .article_actions import ArticleSerializers, ArticleTopicSerializers
from ..enum.service_content_enum import ArticleTopicEnum
from ..models import Article, User, ArticleModule, ArticleThemeRelation, Theme
from rest_framework import viewsets

from utils.pagination import StandardResultsSetPagination
from utils.api_response import success_response, parameter_error_response


class ArticleViewSet(viewsets.ModelViewSet):
    queryset = Article.objects.filter(deleted=False).order_by('-top_at', '-created_at')
    serializer_class = ArticleSerializers

    @swagger_auto_schema(
        operation_id='文章列表',
        operation_summary='文章列表',
        manual_parameters=[
            openapi.Parameter('keyword', openapi.IN_QUERY, description='关键字（文章标题）', type=openapi.TYPE_STRING),
            openapi.Parameter('enabled', openapi.IN_QUERY, description='是否上架（默认全部）', type=openapi.TYPE_BOOLEAN),
            openapi.Parameter('category', openapi.IN_QUERY, description='文章类型 ｜  1:经验, 2:关于教练, 3:工具', type=openapi.TYPE_NUMBER),
            openapi.Parameter('topic', openapi.IN_QUERY,
                              description='文章主题 | 1:辅导下属, 2:向上管理, 3:横向协作,'
                                          ' 4:领导风格, 5:有效授权, 6:果敢决策, 7:释放压力', type=openapi.TYPE_NUMBER),
            openapi.Parameter('page', openapi.IN_QUERY, description='第几页', type=openapi.TYPE_NUMBER),
            openapi.Parameter('page_size', openapi.IN_QUERY, description='每页显示条数默认15条', type=openapi.TYPE_NUMBER),
        ],
        tags=['文章相关']
    )
    def list(self, request, *args, **kwargs):
        try:
            topic = request.query_params.get('topic')
            keyword = request.query_params.get('keyword')
            category = request.query_params.get('category')
            enabled = request.query_params.get('enabled')
            coachee_user_id = request.query_params.get('coachee_user_id', None)
            theme_id = request.query_params.get('theme_id', None)
            search_type = request.query_params.get('type', 0)
            mp = True if request.headers.get('mp') else None
            if coachee_user_id:
                user = User.objects.get(id=coachee_user_id)
            else:
                user = request.user
            article = self.get_queryset()

            # 因为该接口小程序和管理后台共用
            # mp参数用来区分请求的来源是小程序还是管理后台
            # 小程序请求文章列表数据，默认返回已上架的文章
            # 管理后台请求文章数据，默认返回全部文章数据
            if mp:
                enabled = True
            else:
                enabled = True if str(enabled).lower() == 'true' else False if enabled else None

            if topic:
                article = article.filter(topic=topic, deleted=False).order_by('-created_at')

            if theme_id:
                article = article.filter(
                    article_theme_relation__theme_id=theme_id, deleted=False,
                    article_theme_relation__deleted=False).order_by('-article_theme_relation__weight', '-created_at')
            else:
                if keyword:
                    article = article.filter(title__icontains=keyword)
                if category:
                    article = article.filter(category=category)
            if enabled:
                article = article.filter(enabled=enabled)

            if topic and int(topic) == ArticleTopicEnum.reading:
                # 目前设定一个用户只有一个项目，不做多项目判断
                # Article外键反向查询ArticleModule表，获取用户配置的课前学习文章数据
                member_article = Article.objects.filter(
                    article_module__project_bundle__project_member__user_id=user.id,
                    article_module__deleted=False,
                    article_module__end_time__gte=datetime.now(),
                    deleted=False
                ).order_by('article_module__created_at')
                if member_article:
                    article = member_article
            # 0: 普通搜索 1:项目文章学习
            if int(search_type) == 1:
                # 目前设定一个用户只有一个项目，不做多项目判断
                # Article外键反向查询ArticleModule表，获取用户配置的课前学习文章数据
                member_article = Article.objects.filter(
                    article_module__project_bundle__project_member__user_id=user.id,
                    article_module__deleted=False,
                    article_module__end_time__gte=datetime.now(),
                    deleted=False
                ).order_by('article_module__created_at')
                if member_article:
                    article = member_article

        except Exception:
            return parameter_error_response()

        # 分页
        paginator = StandardResultsSetPagination()
        page_list = paginator.paginate_queryset(article, self.request)
        serializer = self.get_serializer(page_list, many=True)
        response = paginator.get_paginated_response(serializer.data)
        return success_response(response)

    @swagger_auto_schema(
        operation_id='文章详情',
        operation_summary='文章详情',
        manual_parameters=[
            openapi.Parameter('article_id', openapi.IN_QUERY, description='文章id', type=openapi.TYPE_NUMBER),
            openapi.Parameter('project_id', openapi.IN_QUERY, description='项目id', type=openapi.TYPE_NUMBER),
            openapi.Parameter('user_id', openapi.IN_QUERY, description='用户id（获取用户对应文章的行为记录时传递）', type=openapi.TYPE_NUMBER),
        ],
        tags=['文章相关']
    )
    @action(methods=['get'], detail=False, url_path='detail', authentication_classes=[])
    def article_detail(self, request, *args, **kwargs):

        try:
            user = request.query_params.get('user_id')
            instance = Article.objects.get(pk=request.query_params.get('article_id', 0))
            if user:
                user = User.objects.filter(pk=user).first()
                if not user:
                    return parameter_error_response('用户不存在')
        except Article.DoesNotExist as e:
            return parameter_error_response('文章不存在')
        except Exception as e:
            return parameter_error_response(str(e))
        if user:
            ArticleModule.objects.filter(project_bundle__project_member__user=user, article=instance,
                                         deleted=False).update(is_complete=True)
        serializer = self.get_serializer(instance, context={'user': user})
        return success_response(serializer.data)

    @swagger_auto_schema(
        operation_id='文章主题数量列表',
        operation_summary='文章主题数量列表',
        manual_parameters=[
            openapi.Parameter('page', openapi.IN_QUERY, description='第几页', type=openapi.TYPE_NUMBER),
            openapi.Parameter('page_size', openapi.IN_QUERY, description='每页显示条数默认15条', type=openapi.TYPE_NUMBER),
        ],
        tags=['文章相关']
    )
    @action(methods=['get'], detail=False, url_path='topic')
    def topic_article_list(self, request, *args, **kwargs):
        # 关键字分组查询
        article = Article.objects.filter(
            ~Q(topic=None), deleted=False
        ).values('topic').annotate(raw_count=Count('title')).order_by('-raw_count')
        # 反序列化为分页作准备
        articles = []
        for item in article:
            articles.append(Article({
                'count': item['raw_count'],
                'topic': item['topic'],
            }))

        self.serializer_class = ArticleTopicSerializers
        paginator = StandardResultsSetPagination()
        page_list = paginator.paginate_queryset(articles, self.request)
        serializer = self.get_serializer(page_list, many=True)
        response = paginator.get_paginated_response(serializer.data)
        return success_response(response)

    @swagger_auto_schema(
        operation_id='修改文章信息',
        operation_summary='修改文章信息',
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'article_id': openapi.Schema(type=openapi.TYPE_NUMBER, description='文章ID'),
                'capacity_tag_id': openapi.Schema(type=openapi.TYPE_NUMBER, description='标签ID'),
                'tag': openapi.Schema(type=openapi.TYPE_STRING, description='能力标签'),
                'order': openapi.Schema(type=openapi.TYPE_NUMBER, description='文章排序'),
                'category': openapi.Schema(type=openapi.TYPE_NUMBER, description='文章类型 ｜  1:经验, 2:关于教练, 3:工具'),
                'title': openapi.Schema(type=openapi.TYPE_STRING, description='文章标题'),
                'topic': openapi.Schema(type=openapi.TYPE_NUMBER,
                                        description='文章主题 | 1:辅导下属, 2:向上管理, 3:横向协作,'
                                                    ' 4:领导风格, 5:有效授权, 6:果敢决策, 7:释放压力'),
                'brief': openapi.Schema(type=openapi.TYPE_STRING, description='文章简介'),
                'content': openapi.Schema(type=openapi.TYPE_STRING, description='文章内容'),
                'image_url': openapi.Schema(type=openapi.TYPE_STRING, description='文章头图'),
                'top': openapi.Schema(type=openapi.TYPE_BOOLEAN, description='文章置顶'),
                'topic_top': openapi.Schema(type=openapi.TYPE_BOOLEAN, description='文章主题分类置顶'),
                'enabled': openapi.Schema(type=openapi.TYPE_BOOLEAN, description='是否启用'),
                'deleted': openapi.Schema(type=openapi.TYPE_BOOLEAN, description='设置删除'),
            }
        ),
        tags=['文章相关']
    )
    @action(methods=['post'], detail=False, url_path='update')
    def company_update(self, request, *args, **kwargs):
        try:
            instance = Article.objects.get(pk=request.data.get('article_id', 0))
        except Article.DoesNotExist as e:
            return parameter_error_response()

        if request.data.get('top') is not None:
            if request.data.get('top'):
                if instance.top_at:
                    request.data['top_at'] = datetime.now()
                else:
                    try:
                        instance_count = Article.objects.filter(~Q(top_at=None)).all().count()
                        if instance_count >= 10:
                            return parameter_error_response('最多允许10条文章置顶，请先取消已置顶的文章')
                        request.data['top_at'] = datetime.now()
                    except Article.DoesNotExist as e:
                        return parameter_error_response()
            else:
                request.data['top_at'] = None

        # if request.data.get('topic_top') is not None:
        #     if request.data.get('topic_top'):
        #         if instance.top_at:
        #             request.data['topic_top_at'] = datetime.now()
        #         else:
        #             try:
        #                 instance_count = Article.objects.filter(~Q(topic_top_at=None)).all().count()
        #                 if instance_count >= 10:
        #                     return parameter_error_response('最多允许10条文章置顶，请先取消已置顶的文章')
        #                 request.data['topic_top_at'] = datetime.now()
        #             except Article.DoesNotExist as e:
        #                 return parameter_error_response()
        #     else:
        #         request.data['topic_top_at'] = None

        if request.data.get('content'):
            raw_content = request.data.get('content') + request.data.get('brief', '') + request.data.get('title', '')
            duration = get_use_time.get_duration(raw_content)
            request.data['duration'] = duration

        data = request.data.copy()
        if 'theme_ids' in data.keys():
            theme_ids = data.pop('theme_ids')
            exists_theme_ids = instance.article_theme_relation.filter(deleted=False).values_list('theme_id', flat=True)
            deleted_list = list(set(exists_theme_ids).difference(set(theme_ids)))
            add_lst = list(set(theme_ids).difference(set(exists_theme_ids)))
            if deleted_list:
                ArticleThemeRelation.objects.filter(article_id=instance.id,
                                                    theme_id__in=deleted_list).update(deleted=True)
            if add_lst:
                for theme_id in add_lst:
                    if not ArticleThemeRelation.objects.filter(article_id=instance.id, theme_id=theme_id,
                                                               deleted=False).exists():
                        ArticleThemeRelation.objects.create(article_id=instance.id, theme_id=theme_id)
        serializer = self.get_serializer(instance, data=data, partial=True)
        serializer.is_valid(raise_exception=True)
        serializer.save()
        return success_response(serializer.data)

    @swagger_auto_schema(
        operation_id='创建文章',
        operation_summary='创建文章',
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'capacity_tag_id': openapi.Schema(type=openapi.TYPE_NUMBER, description='标签ID'),
                'tag': openapi.Schema(type=openapi.TYPE_STRING, description='能力标签'),
                'order': openapi.Schema(type=openapi.TYPE_NUMBER, description='文章排序'),
                'category': openapi.Schema(type=openapi.TYPE_NUMBER, description='文章类型 ｜ 1:经验, 2:关于教练, 3:工具'),
                'title': openapi.Schema(type=openapi.TYPE_STRING, description='文章标题'),
                'topic': openapi.Schema(
                    type=openapi.TYPE_NUMBER,
                    description='文章主题 | 1:辅导下属, 2:向上管理, 3:横向协作,'
                                ' 4:领导风格, 5:有效授权, 6:果敢决策, 7:释放压力'),
                'brief': openapi.Schema(type=openapi.TYPE_STRING, description='文章简介'),
                'content': openapi.Schema(type=openapi.TYPE_STRING, description='文章内容'),
                'image_url': openapi.Schema(type=openapi.TYPE_STRING, description='文章头图'),
                'top': openapi.Schema(type=openapi.TYPE_BOOLEAN, description='文章置顶'),
                'topic_top': openapi.Schema(type=openapi.TYPE_BOOLEAN, description='文章主题分类置顶'),
            }
        ),
        tags=['文章相关']
    )
    def create(self, request, *args, **kwargs):
        if request.data.get('top', 0):
            request.data['top_at'] = datetime.now()
        raw_content = request.data.get('content', '') + request.data.get('brief', '') + request.data.get('title', '')
        duration = get_use_time.get_duration(raw_content)
        article = Article(
            order=request.data.get('order'),
            category=request.data.get('category'),
            title=request.data.get('title'),
            brief=request.data.get('brief'),
            content=request.data.get('content'),
            tag=request.data.get('tag'),
            image_url=request.data.get('image_url'),
            top_at=request.data.get('top_at'),
            duration=duration
        )
        article.save()
        if request.data.get('theme_ids'):
            theme_ids = request.data.get('theme_ids')
            for theme_id in theme_ids:
                ArticleThemeRelation.objects.create(article=article, theme_id=theme_id)
        serializer = self.get_serializer(article)

        return success_response(serializer.data)
