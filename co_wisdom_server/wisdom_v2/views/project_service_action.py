from rest_framework import serializers

from wisdom_v2.models_file import ProjectServiceStage, ProjectServiceContent


class ProjectServiceStageSerializer(serializers.ModelSerializer):
    """
    项目服务阶段
    """
    id = serializers.Char<PERSON>ield(help_text='id')
    stage_name = serializers.Char<PERSON>ield(help_text='阶段名称')
    project_id = serializers.CharField(help_text='项目id')
    project_member_id = serializers.CharField(help_text='项目成员id')


    class Meta:
        model = ProjectServiceStage
        fields = ('id', 'stage_name', 'project_id', 'project_member_id')


class ProjectServiceContentSerializer(serializers.ModelSerializer):
    """
    项目服务内容
    """
    id = serializers.CharField(help_text='id')
    content_type = serializers.IntegerField(help_text='内容类型')
    content = serializers.CharField(help_text='内容')
    project_id = serializers.Char<PERSON>ield(help_text='项目id')

    class Meta:
        model = ProjectServiceContent
        fields = ('id', 'content_type', 'content', 'project_id')


