from rest_framework import serializers

from wisdom_v2.models import ContentOperationArticle


class ContentOperationArticleSerializers(serializers.ModelSerializer):
    article_created_at = serializers.CharField(source='article.created_at', help_text='文章创建时间', read_only=True)
    title = serializers.CharField(source='article.title', help_text='文章标题', read_only=True)
    article_duration = serializers.CharField(source='article.duration', help_text='学习时长', read_only=True)

    class Meta:
        model = ContentOperationArticle
        exclude = ('updated_at', 'created_at', 'article')
