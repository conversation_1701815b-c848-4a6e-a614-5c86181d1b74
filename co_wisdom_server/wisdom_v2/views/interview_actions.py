from rest_framework import serializers
from ..models import ProjectInterview, PublicAttr, UserAnswer, ActionPlan, Question, Project, ProjectMember, EventType


def get_public_attr(public_attr_id):
    try:
        public_attr = PublicAttr.objects.get(pk=public_attr_id)
    except PublicAttr.DoesNotExist:
        return

    public_data = {
        'id': public_attr.pk,
        'project': public_attr.project.name,
        'coach_name': public_attr.user.name,
        'coachee_name': public_attr.target_user.name,
        'status': public_attr.status,
        'category': public_attr.type,
        'start_time': public_attr.start_time.strftime('%Y-%m-%d %H:%M'),
        'end_time': public_attr.end_time.strftime('%Y-%m-%d %H:%M'),
    }

    return public_data


def get_user_answer(public_attr_id):
    user_answer = UserAnswer.objects.filter(public_attr_id=public_attr_id).order_by('question__sort')
    user_answer_list = []
    for answer in user_answer:
        data = {
            'answer_id': answer.pk,
            'question_id': answer.question.pk,
            'question': answer.question.title,
            'question_sort': answer.question.sort,
            'question_hint_text': answer.question.hint_text,
            'question_option': answer.question.option,
            'question_subs': answer.question.subs,
            'question_editable': answer.question.editable,
            'question_require': answer.question.require,
            'question_option_title': answer.question.option_title,
            'question_answer_type': answer.question.answer_type,
            'question_answer_hint_info': answer.question.hint_info,
        }
        user_answer_list.append(data)

    return user_answer_list


def get_action_plan(interview_id):
    try:
        action_plan = ActionPlan.objects.get(interview_id=interview_id)
    except ActionPlan.DoesNotExist:
        return None

    actions = {
        'public_attr_id': action_plan.public_attr.pk,
        'status': action_plan.status,
        'content': action_plan.content,
        'notes': action_plan.notes
    }
    return actions


class QuestionSerializers(serializers.ModelSerializer):
    class Meta:
        model = Question
        exclude = ('deleted', 'created_at', 'updated_at')


class AnswerSerializers(serializers.ModelSerializer):

    class Meta:
        model = UserAnswer
        exclude = ('created_at', 'updated_at')


class InterviewListSerializers(serializers.ModelSerializer):
    created_at = serializers.DateTimeField(format='%Y-%m-%d %H:%M')
    interview_info = serializers.SerializerMethodField()
    project_name = serializers.CharField(source='public_attr.project.name')
    result = serializers.CharField(source='public_attr.result')
    project_id = serializers.IntegerField(source='public_attr.project.pk')
    date = serializers.SerializerMethodField()
    role = serializers.SerializerMethodField()

    def get_interview_info(self, obj):
        return get_public_attr(obj.public_attr.pk)

    def get_date(self, obj):
        return obj.public_attr.start_time.strftime('%Y-%m-%d') + ' ~ ' + obj.public_attr.end_time.strftime('%Y-%m-%d')

    def get_role(self, obj):
        user = ProjectMember.objects.filter(user=obj.public_attr.user, project=obj.public_attr.project, deleted=False).first()
        if user:
            return user.role

    class Meta:
        model = ProjectInterview
        fields = ('type', 'interview_subject', 'close_reason', 'times', 'interview_number', 'created_at',
                  'interview_info',
                  'project_name', 'project_id', 'result', 'close_reason', 'date', 'role')


class InterviewSerializers(serializers.ModelSerializer):
    created_at = serializers.DateTimeField(format='%Y-%m-%d %H:%M')
    interview_info = serializers.SerializerMethodField()
    interview_related = serializers.SerializerMethodField()
    interview_record = serializers.SerializerMethodField()
    project_name = serializers.CharField(source='public_attr.project.name')
    result = serializers.CharField(source='public_attr.result')
    project_id = serializers.IntegerField(source='public_attr.project.pk')
    date = serializers.SerializerMethodField()
    role = serializers.SerializerMethodField()
    action_plan = serializers.SerializerMethodField()

    def get_action_plan(self, obj):
        return get_action_plan(obj.pk)

    def get_interview_info(self, obj):
        return get_public_attr(obj.public_attr.pk)

    def get_interview_record(self, obj):
        # event_type = EventType.objects.filter(code='r3')
        # questions = Question.objects.filter(event=event_type, event__deleted=False)
        # answer = UserAnswer.objects.filter(public_attr=obj.public_attr)
        # return {"question":  QuestionSerializers(questions, many=True).data,
        #         "answer": AnswerSerializers(answer, many=True).data}

        event_type = EventType.objects.filter(code='r3')
        questions = Question.objects.filter(event__in=event_type)
        answer = UserAnswer.objects.filter(public_attr=obj.public_attr)
        return {"question": QuestionSerializers(questions, many=True).data,
                "answer": AnswerSerializers(answer, many=True).data}

    def get_interview_related(self, obj):
        return {"report": obj.public_attr.result}

    def get_date(self, obj):
        return obj.public_attr.start_time.strftime('%Y-%m-%d') + ' ~ ' + obj.public_attr.end_time.strftime('%Y-%m-%d')

    def get_role(self, obj):
        user = ProjectMember.objects.filter(user=obj.public_attr.user, project=obj.public_attr.project, deleted=False).first()
        if user:
            return user.role
    class Meta:
        model = ProjectInterview
        fields = ('type', 'interview_subject', 'close_reason', 'times', 'interview_number', 'created_at',
                  'interview_info', 'interview_related', 'interview_record',
                  'project_name', 'project_id', 'result', 'close_reason', 'is_settlement',
                  'date', 'role', 'action_plan')


class ProjectSerializer(serializers.ModelSerializer):
    period = serializers.SerializerMethodField()
    category_list = serializers.SerializerMethodField()
    step = serializers.SerializerMethodField()

    def get_period(self, obj):
        return obj.start_time.strftime('%Y-%m-%d') + ' ~ ' + obj.end_time.strftime('%Y-%m-%d')

    def get_category_list(self, obj):
        return []

    def get_step(self, obj):
        return '化学面谈'

    class Meta:
        model = Project
        fields = ('id', 'period', 'category_list', 'name', 'step')
