import datetime

from django.db.models import Sum
from django.db import transaction
from rest_framework import serializers

from utils import multiple_selection_map
from utils.api_response import WisdomValidationError
from wisdom_v2.enum.project_interview_enum import ProjectInterviewPlaceCategoryEnum
from wisdom_v2.models import <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Coach, Resume, ProjectInterview, ProjectCoach, ProjectOfferPrice
from wisdom_v2.enum.service_content_enum import CoachOfferStatusEnum, ProjectOfferStatusEnum, CustomerPortraitTypeEnum, \
    ProjectCoachStatusEnum
from wisdom_v2.views.coach_offer_actions import CoachOfferSerializer
from wisdom_v2.views.constant import ATTR_STATUS_INTERVIEW_CANCEL, INTERVIEW_TYPE_COACHING


class ProjectOfferSerializer(serializers.ModelSerializer):
    id = serializers.CharField(help_text='id')
    offer_name = serializers.CharField(help_text='offer名称')
    project_name = serializers.CharField(source='project.name', help_text='项目名称')
    company_name = serializers.CharField(source='project.company.real_name', help_text='企业名称')
    project_cycle = serializers.SerializerMethodField(help_text='项目周期')
    confirm_time = serializers.DateTimeField(format='%Y-%m-%d %H:%M:%S', help_text='offer确认截止时间')
    status = serializers.IntegerField(help_text='发送状态')
    send_time = serializers.DateTimeField(format='%Y-%m-%d %H:%M:%S', help_text='发送时间')
    coach_count = serializers.SerializerMethodField(help_text='加入情况')

    class Meta:
        model = ProjectOffer
        fields = ('id', 'offer_name', 'project_name', 'project_cycle', 'confirm_time', 'status', 'send_time',
                  'coach_count', 'company_name')

    def get_project_cycle(self, obj):
        project = obj.project
        if project.start_time and project.end_time:
            return f"{project.start_time.strftime('%Y.%m.%d')}-{project.end_time.strftime('%Y.%m.%d')}"

    def get_coach_count(self, obj):
        coach_offers = obj.coach_offers.filter(deleted=False)
        if coach_offers.exists():
            return f'{coach_offers.filter(status=CoachOfferStatusEnum.joined).count()}/{coach_offers.count()}'


class ProjectOfferCreateSerializer(serializers.Serializer):
    offer_name = serializers.CharField(required=True, write_only=True, help_text='offer名称')
    project_id = serializers.IntegerField(required=True, write_only=True, help_text='项目ID')
    project_msg = serializers.CharField(required=True, write_only=True, help_text='项目描述')
    confirm_time = serializers.CharField(required=True, write_only=True, help_text='确认截止时间')
    customer_count = serializers.IntegerField(required=True, write_only=True, allow_null=True, help_text='最大客户数量')
    coach_ids = serializers.ListField(required=False, write_only=True, help_text='关联教练id列表')
    settlement_price = serializers.ListField(required=False, write_only=True, help_text='结算金额')

    def add_offer(self, validated_data):
        coach_ids = None
        settlement_price = None
        if 'settlement_price' in validated_data.keys():
            settlement_price = validated_data.pop('settlement_price')

        if 'coach_ids' in validated_data.keys():
            coach_ids = validated_data.pop('coach_ids')
        if coach_ids:
            coach_ids = list(set(coach_ids))
            # exists_coach_ids = list(ProjectCoach.objects.filter(
            #     coach_id__in=coach_ids,
            #     deleted=False,
            #     status=ProjectCoachStatusEnum.adopt,
            #     project_id=validated_data.get('project_id'),
            #     member__isnull=True,
            #     project_group_coach__isnull=True
            # ).values_list('coach_id', flat=True))
            # if exists_coach_ids:
            #     coach_name_list = list(Coach.objects.filter(
            #         id__in=exists_coach_ids).values_list('user__true_name', flat=True))
            #     raise WisdomValidationError(f"教练{'，'.join(coach_name_list)}已在项目中")
        with transaction.atomic():
            project_offer = ProjectOffer.objects.create(**validated_data)
            if coach_ids:
                coach_list = [CoachOffer(project_offer=project_offer, coach_id=coach_id) for coach_id in coach_ids]
                CoachOffer.objects.bulk_create(coach_list)
            if settlement_price:
                offer_price_list = [ProjectOfferPrice(
                    project_offer=project_offer, work_type=item.get('work_type'),
                    time_type=item.get('time_type'), price=item.get('price') * 100,
                    place_type=item.get('place_type')) for item in settlement_price]
                ProjectOfferPrice.objects.bulk_create(offer_price_list)
        data = ProjectOfferDetailSerializer(project_offer).data
        return data


class ProjectOfferUpdateSerializer(serializers.Serializer):
    id = serializers.CharField(required=True, write_only=True, help_text='offer_id')
    offer_name = serializers.CharField(required=False, write_only=True, help_text='offer名称')
    project_id = serializers.IntegerField(required=False, write_only=True, help_text='项目ID')
    project_msg = serializers.CharField(required=False, write_only=True, help_text='项目描述')
    confirm_time = serializers.CharField(required=False, write_only=True, help_text='确认截止时间')
    customer_count = serializers.IntegerField(required=False, allow_null=True, write_only=True, help_text='最大客户数量')
    coach_ids = serializers.ListField(required=False, write_only=True, help_text='关联教练id列表')
    settlement_price = serializers.ListField(required=False, write_only=True, help_text='结算金额')
    deleted = serializers.BooleanField(required=False, write_only=True, help_text='是否删除')

    def update_offer(self, validated_data):
        project_offer = ProjectOffer.objects.get(id=validated_data.pop('id'))
        coach_ids = None
        if 'coach_ids' in validated_data.keys():
            coach_ids = validated_data.pop('coach_ids')
        exists_coach_ids = list(project_offer.coach_offers.filter(deleted=False).values_list('coach_id', flat=True))

        settlement_price = None
        if 'settlement_price' in validated_data.keys():
            settlement_price = validated_data.pop('settlement_price')
        exists_offer_price_ids = list(project_offer.offer_price.filter(deleted=False).values_list('id', flat=True))

        if 'confirm_time' in validated_data.keys():
            validated_data['confirm_time'] = datetime.datetime.strptime(validated_data['confirm_time'],
                                                                        '%Y-%m-%d %H:%M:%S')

        exist_confirm_time = project_offer.confirm_time

        with transaction.atomic():
            ProjectOffer.objects.filter(id=project_offer.id).update(**validated_data)
            # 如果offer被移除了同步移除这个offer下的所有教练offer
            project_offer = ProjectOffer.objects.get(id=project_offer.id)
            if project_offer.deleted:  
                CoachOffer.objects.filter(project_offer=project_offer).update(deleted=True)

            # 修改了确认截止时间，同步修改coach_offer的时间状态
            if project_offer.confirm_time != exist_confirm_time:
                coach_offer = project_offer.coach_offers.filter(
                    deleted=False, status__in=[CoachOfferStatusEnum.not_confirm, CoachOfferStatusEnum.expired])
                if project_offer.confirm_time > datetime.datetime.now():
                    coach_offer.update(status=CoachOfferStatusEnum.not_confirm)
                else:
                    coach_offer.update(status=CoachOfferStatusEnum.expired)

            # 查看关联教练是否有变化
            if isinstance(coach_ids, list) and not project_offer.deleted:
                add_list = list(set(coach_ids).difference(set(exists_coach_ids)))
                deleted_list = list(set(exists_coach_ids).difference(set(coach_ids)))
                if add_list:  # 有新增的教练
                    coach_offer_list = [CoachOffer(project_offer=project_offer,
                                                   coach_id=coach_id) for coach_id in add_list]
                    CoachOffer.objects.bulk_create(coach_offer_list)
                if deleted_list:
                    CoachOffer.objects.filter(project_offer=project_offer,
                                              coach_id__in=deleted_list).update(deleted=True)

            if isinstance(settlement_price, list) and not project_offer.deleted:
                offer_price_ids = []
                for item in settlement_price:
                    offer_price = ProjectOfferPrice.objects.filter(
                        work_type=item.get('work_type'), place_type=item.get('place_type'), deleted=False, project_offer=project_offer).first()
                    if offer_price:
                        offer_price.time_type = item.get('time_type')
                        offer_price.price = item.get('price') * 100
                        offer_price.save()
                        offer_price_ids.append(offer_price.id)
                    else:
                        ProjectOfferPrice.objects.create(
                            project_offer=project_offer, work_type=item.get('work_type'),
                            place_type=item.get('place_type'),
                            time_type=item.get('time_type'), price=item.get('price') * 100)
                deleted_list = list(set(exists_offer_price_ids).difference(set(offer_price_ids)))
                if deleted_list:
                    ProjectOfferPrice.objects.filter(project_offer=project_offer, id__in=deleted_list).update(deleted=True)

        data = ProjectOfferDetailSerializer(project_offer).data
        return data


class ProjectOfferInfoListSerializer(serializers.ModelSerializer):
    id = serializers.IntegerField(help_text='offer_id')
    offer_name = serializers.CharField(help_text='offer名称')

    class Meta:
        fields = ['id', 'offer_name']
        model = ProjectOffer


class ProjectOfferDetailSerializer(serializers.ModelSerializer):
    id = serializers.CharField(help_text='id')
    offer_name = serializers.CharField(help_text='offer名称')
    project_name = serializers.CharField(source='project.name', help_text='项目名称')
    confirm_time = serializers.DateTimeField(format='%Y-%m-%d %H:%M:%S', help_text='offer确认截止时间')
    status = serializers.IntegerField(help_text='发送状态')
    price = serializers.CharField(help_text='教练费用描述')
    project_msg = serializers.CharField(help_text='项目描述')
    customer_count = serializers.IntegerField(help_text='服务客户数量')
    coach_offer = serializers.SerializerMethodField(help_text='教练offer列表')
    customer_portrait = serializers.SerializerMethodField(help_text='客户画像')
    project_id = serializers.IntegerField(help_text='项目id')
    settlement_price = serializers.SerializerMethodField(help_text='结算金额信息')

    class Meta:
        model = ProjectOffer
        fields = ('id', 'offer_name', 'project_name', 'confirm_time', 'status', 'price', 'project_msg',
                  'customer_count', 'coach_offer', 'customer_portrait', 'project_id', 'settlement_price')

    def get_customer_portrait(self, obj):
        # 优先取群体画像 无 取第一个个人画像
        project = obj.project
        data = {"interview_time": None, "personal_target": None, 'project_cycle': None}
        data['personal_target'] = project.coach_content
        if project.start_time and project.end_time:
            project_cycle = f"{project.start_time.strftime('%Y.%m.%d')} ~ {project.end_time.strftime('%Y.%m.%d')} " \
                            f"(预估{(project.end_time - project.start_time).days + 1}天)"
            data['project_cycle'] = project_cycle
        customer_portrait = project.project_customer_portrait.filter(deleted=False)
        if customer_portrait.filter(type=CustomerPortraitTypeEnum.group).exists():
            customer_portrait = customer_portrait.filter(type=CustomerPortraitTypeEnum.group).first()
        elif customer_portrait.filter(type=CustomerPortraitTypeEnum.personal).exists():
            customer_portrait = customer_portrait.filter(type=CustomerPortraitTypeEnum.personal).first()
        else:
            return data
        if customer_portrait.expected_interview_start_time and customer_portrait.expected_interview_end_time:
            interview_time = f"{customer_portrait.expected_interview_start_time.strftime('%Y.%m.%d')} ~ " \
                             f"{customer_portrait.expected_interview_end_time.strftime('%Y.%m.%d')}"
            data['interview_time'] = interview_time
        return data

    def get_coach_offer(self, obj):
        coach_offer = obj.coach_offers.filter(deleted=False).order_by('-created_at')
        serializer = CoachOfferSerializer(coach_offer, many=True)
        return serializer.data

    def get_settlement_price(self, obj):
        return obj.get_settlement_price


class ProjectOfferCoachListSerializer(serializers.ModelSerializer):
    id = serializers.IntegerField(read_only=True)
    user_id = serializers.IntegerField(read_only=True)
    coach_type = serializers.IntegerField(read_only=True, help_text='教练类型')
    order_receiving_status = serializers.BooleanField(read_only=True, help_text='接单状态')
    gender = serializers.IntegerField(source='user.gender', help_text='性别', read_only=True)
    coach_info = serializers.SerializerMethodField(read_only=True, help_text='简历信息')
    one_to_one_interview_time = serializers.SerializerMethodField(read_only=True)
    is_set = serializers.SerializerMethodField(read_only=True, help_text='是否绑定项目')

    class Meta:
        model = Coach
        fields = ('id', 'user_id', 'coach_type', 'order_receiving_status',
                  'gender', 'coach_info', 'one_to_one_interview_time', 'is_set')

    def get_coach_info(self, obj):
        resume = Resume.objects.filter(coach_id=obj.pk, deleted=False, is_customization=False).first()
        data = {}
        data['coach_auth'] = resume.coach_auth if resume.coach_auth else None
        data['working_years'] = resume.working_years if resume.working_years else None
        data['resume_id'] = resume.pk
        data['coach_domain'] = multiple_selection_map.get_multiple_selection_detail('coach_domain',
                                                                                    resume.coach_domain)['show_text']
        data['coach_name'] = obj.user.cover_name
        return data

    def get_one_to_one_interview_time(self, obj):
        times = ProjectInterview.objects.filter(
            type=INTERVIEW_TYPE_COACHING,
            place_category=ProjectInterviewPlaceCategoryEnum.online_one_to_one.value,
            public_attr__type=1, deleted=False,
            public_attr__end_time__lt=datetime.datetime.now(),
            public_attr__user_id=obj.user_id).exclude(
            public_attr__status=ATTR_STATUS_INTERVIEW_CANCEL).aggregate(times_minute=Sum('times'))
        if times['times_minute']:
            interview_hour = round(times['times_minute'] / 60, 1)
        else:
            interview_hour = 0
        resume = Resume.objects.filter(coach=obj, deleted=False, is_customization=False).first()
        one_to_one_interview_time = resume.one_to_one_interview_time if resume.one_to_one_interview_time else 0
        return interview_hour + one_to_one_interview_time

    def get_is_set(self, obj):
        offer_id = self.context.get('project_offer_id')
        if offer_id:
            # 查看是否加入项目邀请
            project_offer = ProjectOffer.objects.get(id=offer_id)
            coach_ids = list(project_offer.coach_offers.filter(deleted=False).values_list('coach_id', flat=True))
            if obj.pk in coach_ids:
                return True
        return False
