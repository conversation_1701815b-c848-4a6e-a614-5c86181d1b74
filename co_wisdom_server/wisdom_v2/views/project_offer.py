from drf_yasg import openapi
from rest_framework.decorators import action
from drf_yasg.utils import swagger_auto_schema
from rest_framework import viewsets
from django.db.models import Q

from wisdom_v2.enum.user_enum import CoachUserTypeEnum
from wisdom_v2.views.project_offer_actions import ProjectOfferSerializer, ProjectOfferInfoListSerializer, \
    ProjectOfferDetailSerializer, ProjectOfferCoachListSerializer, ProjectOfferCreateSerializer, \
    ProjectOfferUpdateSerializer
from wisdom_v2.models import ProjectOffer, Project, Coach
from utils.pagination import StandardResultsSetPagination
from utils.api_response import success_response, parameter_error_response


class ProjectOfferViewSet(viewsets.ModelViewSet):
    queryset = ProjectOffer.objects.filter(deleted=False).order_by('-created_at')
    serializer_class = ProjectOfferSerializer

    @swagger_auto_schema(
        operation_id='后台项目offer列表',
        operation_summary='后台项目offer列表',
        manual_parameters=[
            openapi.Parameter('offer_name', openapi.IN_QUERY, description='offer名称', type=openapi.TYPE_STRING),
            openapi.Parameter('project_name', openapi.IN_QUERY, description='项目名称', type=openapi.TYPE_STRING),
            openapi.Parameter('project_ids', openapi.IN_QUERY, description='项目id', type=openapi.TYPE_STRING),
            openapi.Parameter('offer_ids', openapi.IN_QUERY, description='offer_id', type=openapi.TYPE_STRING),
            openapi.Parameter('status', openapi.IN_QUERY, description='1-未发送 2-已发送', type=openapi.TYPE_STRING),
            openapi.Parameter('page', openapi.IN_QUERY, description='第几页', type=openapi.TYPE_NUMBER),
            openapi.Parameter('page_size', openapi.IN_QUERY, description='每页显示条数默认15条', type=openapi.TYPE_NUMBER),
        ],
        tags=['后台项目offer']
    )
    def list(self, request, *args, **kwargs):
        project_offer = self.get_queryset()
        if request.query_params.get('offer_name', None):
            project_offer = project_offer.filter(offer_name__icontains=request.query_params.get('offer_name'))
        if request.query_params.get('project_name'):
            project_offer = project_offer.filter(project__name__icontains=request.query_params.get('project_name'))
        if request.query_params.get('status'):
            project_offer = project_offer.filter(status__in=request.query_params.get('status').split(','))
        if request.query_params.get('project_ids'):
            project_offer = project_offer.filter(project_id__in=request.query_params.get('project_ids').split(','))
        if request.query_params.get('offer_ids'):
            project_offer = project_offer.filter(id__in=request.query_params.get('offer_ids').split(','))
        if request.query_params.get('company_id'):
            project_offer = project_offer.filter(project__company_id=request.query_params.get('company_id'))
        # 分页
        paginator = StandardResultsSetPagination()
        page_list = paginator.paginate_queryset(project_offer, self.request)
        serializer = self.get_serializer(page_list, many=True)
        response = paginator.get_paginated_response(serializer.data)
        return success_response(response)

    @swagger_auto_schema(
        operation_id='后台项目offer列表offer名称查询',
        operation_summary='后台项目offer列表offer名称查询',
        manual_parameters=[
            openapi.Parameter('offer_name', openapi.IN_QUERY, description='offer名称', type=openapi.TYPE_STRING),
        ],
        tags=['后台项目offer']
    )
    @action(methods=['get'], detail=False, url_path='info', serializer_class=ProjectOfferInfoListSerializer)
    def get_project_offer_list_by_name(self, request, *args, **kwargs):
        offer_name = request.query_params.get('offer_name', None)
        project_offer = self.get_queryset()
        if offer_name:
            project_offer = project_offer.filter(offer_name__icontains=offer_name)
        serializer = self.get_serializer(project_offer, many=True)
        return success_response(serializer.data)

    @swagger_auto_schema(
        operation_id='后台项目offer详情',
        operation_summary='后台项目offer详情',
        manual_parameters=[
            openapi.Parameter('id', openapi.IN_QUERY, description='offer名称', type=openapi.TYPE_STRING),
        ],
        tags=['后台项目offer']
    )
    @action(methods=['get'], detail=False, url_path='detail', serializer_class=ProjectOfferDetailSerializer)
    def project_offer_detail(self, request, *args, **kwargs):
        try:
            project_offer_id = request.query_params.get('id')
            project_offer = ProjectOffer.objects.get(id=project_offer_id)
        except ProjectOffer.DoesNotExist:
            return parameter_error_response()
        serializer = self.get_serializer(project_offer)
        return success_response(serializer.data)

    @swagger_auto_schema(
        operation_id='后台项目offer详情教练列表',
        operation_summary='后台项目offer详情教练列表',
        manual_parameters=[
            openapi.Parameter('offer_id', openapi.IN_QUERY, description='offer_id', type=openapi.TYPE_STRING),
            openapi.Parameter('coach_type', openapi.IN_QUERY, description='教练类型', type=openapi.TYPE_STRING),
            openapi.Parameter('name', openapi.IN_QUERY, description='教练姓名', type=openapi.TYPE_STRING),
            openapi.Parameter('order_receiving_status', openapi.IN_QUERY, description='接单状态',
                              type=openapi.TYPE_BOOLEAN),
        ],
        tags=['后台项目offer']
    )
    @action(methods=['get'], detail=False, url_path='coach_list', serializer_class=ProjectOfferCoachListSerializer)
    def project_offer_coach_list(self, request, *args, **kwargs):
        try:
            offer_id = request.query_params.get('offer_id')
            project_id = request.query_params.get('project_id')
            name = request.query_params.get('name')
            coach_type = request.query_params.get('coach_type')
            order_receiving_status = request.query_params.get('order_receiving_status')
        except KeyError:
            return parameter_error_response()
        coach = Coach.objects.filter(deleted=False).exclude(coach_type=CoachUserTypeEnum.student).order_by('-created_at')
        if name:
            coach = coach.filter(Q(user__true_name__icontains=name) | Q(user__name__icontains=name))
        if coach_type:
            coach = coach.filter(coach_type__in=coach_type.split(','))
        if order_receiving_status:
            order_receiving_status = True if str(order_receiving_status).lower() == 'true' else False
            coach = coach.filter(order_receiving_status=order_receiving_status)

        # 分页
        paginator = StandardResultsSetPagination()
        page_list = paginator.paginate_queryset(coach, self.request)
        serializer = self.get_serializer(page_list, many=True, context={"project_offer_id": offer_id,
                                                                        "project_id": project_id})
        response = paginator.get_paginated_response(serializer.data)
        return success_response(response)


    @swagger_auto_schema(
        operation_id='新建项目offer',
        operation_summary='新建项目offer',
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'offer_name': openapi.Schema(type=openapi.TYPE_NUMBER, description='offer名称'),
                'project_id': openapi.Schema(type=openapi.TYPE_NUMBER, description='项目id'),
                'price': openapi.Schema(type=openapi.TYPE_NUMBER, description='教练费用描述'),
                'project_msg': openapi.Schema(type=openapi.TYPE_STRING, description='项目描述'),
                'confirm_time': openapi.Schema(type=openapi.TYPE_STRING, description='确认截止时间'),
                'customer_count': openapi.Schema(type=openapi.TYPE_NUMBER, description='最多服务客户数量'),
                'coach_ids': openapi.Schema(type=openapi.TYPE_ARRAY, description='选中的教练id列表',
                                            items=openapi.Schema(type=openapi.TYPE_NUMBER)),

            }
        ),
        tags=['后台项目offer']
    )
    @action(methods=['post'], detail=False, url_path='add', serializer_class=ProjectOfferCreateSerializer)
    def add(self, request, *args, **kwargs):
        try:
            project_id = request.data.get('project_id')
            Project.objects.get(pk=project_id, deleted=False)
        except Project.DoesNotExist:
            return parameter_error_response()
        data = request.data
        serializer = self.get_serializer(data=data)
        serializer.is_valid(raise_exception=True)
        validated_data = serializer.validated_data
        return success_response(serializer.add_offer(validated_data))

    @swagger_auto_schema(
        operation_id='编辑项目offer',
        operation_summary='编辑项目offer',
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'id': openapi.Schema(type=openapi.TYPE_STRING, description='offer_id'),
                'offer_name': openapi.Schema(type=openapi.TYPE_NUMBER, description='offer名称'),
                'project_id': openapi.Schema(type=openapi.TYPE_NUMBER, description='项目id'),
                'project_msg': openapi.Schema(type=openapi.TYPE_STRING, description='项目描述'),
                'confirm_time': openapi.Schema(type=openapi.TYPE_STRING, description='确认截止时间'),
                'customer_count': openapi.Schema(type=openapi.TYPE_NUMBER, description='最多服务客户数量'),
                'coach_ids': openapi.Schema(type=openapi.TYPE_ARRAY, description='选中的教练id列表',
                                            items=openapi.Schema(type=openapi.TYPE_NUMBER)),
                'deleted': openapi.Schema(type=openapi.TYPE_BOOLEAN, description='是否删除')

            }
        ),
        tags=['后台项目offer']
    )
    @action(methods=['post'], detail=False, url_path='update', serializer_class=ProjectOfferUpdateSerializer)
    def edit_project_offer(self, request, *args, **kwargs):
        try:
            offer_id = request.data['id']
            ProjectOffer.objects.get(id=offer_id, deleted=False)
            deleted = request.data.get('deleted', False)
        except ProjectOffer.DoesNotExist:
            return parameter_error_response()
        data = request.data
        data['deleted'] = True if deleted else False
        serializer = self.get_serializer(data=data)
        serializer.is_valid(raise_exception=True)
        validated_data = serializer.validated_data
        return success_response(serializer.update_offer(validated_data))

