from rest_framework import serializers

from wisdom_v2.models import CompanyMember, ProjectInterested, MultipleAssociationRelation, ChangeObservationAnswer
from wisdom_v2.enum.service_content_enum import MultipleAssociationRelationTypeEnum


class AdminProjectInterestedSerializer(serializers.ModelSerializer):
    id = serializers.IntegerField(help_text='id')
    interested = serializers.CharField(source='interested.cover_name', help_text='利益相关者姓名')
    relation = serializers.IntegerField(help_text='上下级关系 1:上级 2：平级 3:下级')
    position = serializers.SerializerMethodField(help_text='职位')
    email = serializers.CharField(source='interested.email', help_text='邮箱')
    phone = serializers.CharField(source='interested.phone', help_text='手机号')
    user_id = serializers.IntegerField(source='interested_id', help_text='利益相关者用户id')
    is_set = serializers.SerializerMethodField(help_text='是否关联当前改变观察反馈')
    is_write = serializers.SerializerMethodField(help_text='当前利益相关者是否已填写')
    is_complete = serializers.SerializerMethodField(help_text='当前利益相关者是否已提交')

    class Meta:
        model = ProjectInterested
        fields = ('id', 'interested', 'relation', 'position', 'email', 'phone', 'is_set', 'is_write', 'user_id', 'is_complete')

    def get_position(self, obj):
        member = CompanyMember.objects.filter(company_id=obj.project.company_id, user_id=obj.interested_id).first()
        if member:
            return member.position

    def get_is_set(self, obj):
        change_observation_id = self.context.get('change_observation_id')
        if change_observation_id:
            if MultipleAssociationRelation.objects.filter(
                    main_id=change_observation_id, secondary_id=obj.pk, deleted=False,
                    type=MultipleAssociationRelationTypeEnum.stakeholder_change_observation).exists():
                return True
        return False

    def get_is_write(self, obj):
        change_observation_id = self.context.get('change_observation_id')
        if change_observation_id:
            if MultipleAssociationRelation.objects.filter(
                    main_id=change_observation_id, secondary_id=obj.pk, deleted=False,
                    type=MultipleAssociationRelationTypeEnum.stakeholder_change_observation).exists():
                if ChangeObservationAnswer.objects.filter(change_observation_id=change_observation_id,
                                                          public_attr__user_id=obj.interested_id).exists():
                    return True
        return False

    def get_is_complete(self, obj):
        change_observation_id = self.context.get('change_observation_id')
        if change_observation_id:
            if MultipleAssociationRelation.objects.filter(
                    main_id=change_observation_id, secondary_id=obj.pk, deleted=False,
                    type=MultipleAssociationRelationTypeEnum.stakeholder_change_observation).exists():
                if ChangeObservationAnswer.objects.filter(change_observation_id=change_observation_id,
                                                          public_attr__user_id=obj.interested_id).exists():
                    return True
                return False
        return
