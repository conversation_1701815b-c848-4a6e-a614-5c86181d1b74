from rest_framework import serializers

from wisdom_v2.models import SlideShow
from wisdom_v2.views.article_actions import ArticleSerializers


class SlideShowSerializers(serializers.ModelSerializer):
    article = serializers.SerializerMethodField(help_text='文章', read_only=True)
    created_at = serializers.SerializerMethodField(help_text='创建时间', read_only=True)
    theme = serializers.SerializerMethodField(required=False, help_text='主题', read_only=True)

    class Meta:
        model = SlideShow
        exclude = ('updated_at',)

    def get_article(self, obj):
        if obj.url_type == 1 and obj.article_id:  # 文章
            article_data = ArticleSerializers(obj.article).data
            return article_data

    def get_theme(self, obj):
        if obj.url_type == 2 and obj.theme:  # 主题
            return {"id": obj.theme.id, "name": obj.theme.name}

    def get_created_at(self, obj):
        return obj.created_at.strftime('%Y-%m-%d %H:%M:%S')

