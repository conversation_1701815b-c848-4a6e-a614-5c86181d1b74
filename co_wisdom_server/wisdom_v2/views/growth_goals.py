from drf_yasg import openapi
from rest_framework import viewsets
from drf_yasg.utils import swagger_auto_schema

from utils.api_response import parameter_error_response, success_response
from utils.pagination import StandardResultsSetPagination
from wisdom_v2.app_views.app_coachee_growth_actions import AppCoacheeGrowthGoalsViewSerializers
from wisdom_v2.common import project_coach_public
from wisdom_v2.models import GrowthGoals, ProjectMember, ProjectCoach
from wisdom_v2.views.constant import ATTR_TYPE_GROWTH_GOALS


class AdminGrowthGoalsViewSet(viewsets.ModelViewSet):
    queryset = GrowthGoals.objects.filter(deleted=False)
    serializer_class = AppCoacheeGrowthGoalsViewSerializers

    @swagger_auto_schema(
        operation_id='后台成长目标列表',
        operation_summary='后台成长目标列表',
        manual_parameters=[
            openapi.Parameter(
                'project_member_id', openapi.IN_QUERY, description='被教练者id', type=openapi.TYPE_NUMBER, required=True),
            openapi.Parameter('page', openapi.IN_QUERY, description='第几页', type=openapi.TYPE_NUMBER),
            openapi.Parameter('page_size', openapi.IN_QUERY, description='每页显示条数默认15条', type=openapi.TYPE_NUMBER)
        ],
        tags=['后台改变观察反馈相关']
    )
    def list(self, request, *args, **kwargs):
        try:
            project_member_id = request.query_params.get('project_member_id')
            project_member = ProjectMember.objects.get(pk=project_member_id)
        except:
            return parameter_error_response('未获取到被教练者信息')
        growth_goals = self.get_queryset()
        growth_goals = growth_goals.filter(
            public_attr__user_id=project_member.user_id, public_attr__type=ATTR_TYPE_GROWTH_GOALS)
        growth_goals = growth_goals.order_by('is_finish', '-created_at')
        coachee_name = project_member.user.cover_name
        coach_name = ''
        project_coaches = project_coach_public.get_project_user_coach(project_member)
        if project_coaches and project_coaches.exists():
            coach_name = project_coaches.first().coach.user.cover_name

        # 分页
        paginator = StandardResultsSetPagination()
        page_list = paginator.paginate_queryset(growth_goals, self.request)
        serializer = self.get_serializer(page_list, many=True)
        response = paginator.get_paginated_response(serializer.data)
        data = dict(response.data)
        growth_list = data['results']
        data['results'] = {}
        data['results']['coach_name'] = coach_name
        data['results']['coachee_name'] = coachee_name
        data['results']['results'] = growth_list
        response.data = data
        return success_response(response)
