from drf_yasg import openapi
from rest_framework.decorators import action
from drf_yasg.utils import swagger_auto_schema
from rest_framework import viewsets

from wisdom_v2.views.quick_link_action import QuickLinkSerializer
from wisdom_v2.models import QuickLink
from utils.pagination import StandardResultsSetPagination
from utils.api_response import success_response, parameter_error_response
from wisdom_v2.enum.service_content_enum import QuickLinkStatusEnum, AssociationTypeEnum


class QuickLinkViewSet(viewsets.ModelViewSet):
    queryset = QuickLink.objects.filter(deleted=False).order_by('display_position', '-created_at')
    serializer_class = QuickLinkSerializer

    @swagger_auto_schema(
        operation_id='后台导航列表',
        operation_summary='后台导航列表',
        manual_parameters=[
            openapi.Parameter('name	', openapi.IN_QUERY, description='名称', type=openapi.TYPE_STRING),
            openapi.Parameter('status	', openapi.IN_QUERY, description='状态 1-上架 2-下架', type=openapi.TYPE_STRING),
            openapi.Parameter('page', openapi.IN_QUERY, description='第几页', type=openapi.TYPE_NUMBER),
            openapi.Parameter('page_size', openapi.IN_QUERY, description='每页显示条数默认15条', type=openapi.TYPE_NUMBER),
        ],
        tags=['首页金刚区']
    )
    def list(self, request, *args, **kwargs):
        quick_link = self.get_queryset()
        if request.query_params.get('name', None):
            quick_link = quick_link.filter(name__icontains=request.query_params.get('name'))
        if request.query_params.get('status'):
            quick_link = quick_link.filter(status__in=request.query_params.get('status').split(','))
        if request.query_params.get('suitable_object', None):
            quick_link = quick_link.filter(suitable_object__in=request.query_params.get('suitable_object').split(','))
        # 分页
        paginator = StandardResultsSetPagination()
        page_list = paginator.paginate_queryset(quick_link, self.request)
        serializer = self.get_serializer(page_list, many=True)
        response = paginator.get_paginated_response(serializer.data)
        return success_response(response)

    @swagger_auto_schema(
        operation_id='导航详情',
        operation_summary='导航详情',
        manual_parameters=[
            openapi.Parameter('id', openapi.IN_QUERY, description='导航id', type=openapi.TYPE_NUMBER),
        ],
        tags=['首页金刚区']
    )
    @action(methods=['get'], detail=False, url_path='detail')
    def quick_link_detail(self, request, *args, **kwargs):
        try:
            instance = QuickLink.objects.get(pk=request.query_params.get('id', 0))
        except QuickLink.DoesNotExist as e:
            return parameter_error_response()

        serializer = self.get_serializer(instance)
        return success_response(serializer.data)

    @swagger_auto_schema(
        operation_id='修改导航信息',
        operation_summary='修改导航信息',
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'article_id': openapi.Schema(type=openapi.TYPE_NUMBER, description='文章ID'),
                'id': openapi.Schema(type=openapi.TYPE_NUMBER, description='导航ID'),
                'page_type': openapi.Schema(type=openapi.TYPE_NUMBER, description='链接类型 | 1:视野拓展 2:关于教练 3:预约辅导'),
                'name': openapi.Schema(type=openapi.TYPE_STRING, description='名称'),
                'image_url': openapi.Schema(type=openapi.TYPE_STRING, description='图片url'),
                'page_url': openapi.Schema(type=openapi.TYPE_STRING, description='跳转url'),
                'display_position': openapi.Schema(type=openapi.TYPE_STRING, description='排序'),
                'status': openapi.Schema(type=openapi.TYPE_BOOLEAN, description='是否启用'),
                'page_text': openapi.Schema(type=openapi.TYPE_STRING, description='轮播图上文字'),
            }
        ),
        tags=['首页金刚区']
    )
    @action(methods=['post'], detail=False, url_path='update')
    def quick_link_update(self, request, *args, **kwargs):
        try:
            instance = QuickLink.objects.get(pk=request.data.get('id', 0))
        except QuickLink.DoesNotExist as e:
            return parameter_error_response()
        if len(request.data.get('name', '')) > 8:
            return parameter_error_response('名称不可超过8个字')
        association_type = request.data.get('association_type')
        if association_type == AssociationTypeEnum.article:
            instance.article_id = request.data.get('article_id', 0)
            instance.page_url = None
            instance.theme = None
        elif association_type == AssociationTypeEnum.theme:
            instance.theme_id = request.data.get('theme_id')
            instance.page_url = None
            instance.article = None
        elif association_type == AssociationTypeEnum.external_link:
            instance.external_link = request.data.get('page_url')
            instance.article = None
            instance.theme = None
        elif association_type == AssociationTypeEnum.interview:
            instance.external_link = None
            instance.article = None
            instance.theme = None
            instance.page_url = None
        elif association_type == AssociationTypeEnum.not_jump:
            instance.external_link = None
            instance.article = None
            instance.theme = None
            instance.page_url = None
        instance.save()
        data = request.data.copy()
        serializer = self.get_serializer(instance, data=data, partial=True)
        serializer.is_valid(raise_exception=True)
        serializer.save()
        return success_response(serializer.data)

    @swagger_auto_schema(
        operation_id='创建导航',
        operation_summary='创建导航',
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'article_id': openapi.Schema(type=openapi.TYPE_NUMBER, description='文章ID'),
                'name': openapi.Schema(type=openapi.TYPE_NUMBER, description='名称'),
                'image_url': openapi.Schema(type=openapi.TYPE_NUMBER, description='图片url'),
                'page_url': openapi.Schema(type=openapi.TYPE_STRING, description='跳转url'),
                'page_type': openapi.Schema(type=openapi.TYPE_NUMBER, description='链接类型 | 1:视野拓展 2:关于教练 3:预约辅导'),
                'display_position': openapi.Schema(type=openapi.TYPE_STRING, description='排序'),
                'status': openapi.Schema(type=openapi.TYPE_BOOLEAN, description='是否启用'),
                'page_text': openapi.Schema(type=openapi.TYPE_STRING, description='轮播图上文字'),

            }
        ),
        tags=['首页金刚区']
    )
    def create(self, request, *args, **kwargs):
        if len(request.data.get('name', '')) > 8:
            return parameter_error_response('名称不可超过8个字')
        creator_id = request.user.pk
        data = {"name": request.data.get('name'), "creator_id": creator_id,
                "image_url": request.data.get('image_url'), "display_position": request.data.get('display_position'),
                "status": request.data.get('status', 2), "suitable_object":  request.data.get('suitable_object')}

        if request.data.get('article_id'):
            data['article_id'] = request.data.get('article_id')
            data['association_type'] = AssociationTypeEnum.article.value
            quick_link = QuickLink.objects.create(**data)
        elif request.data.get('theme_id'):
            data['theme_id'] = request.data.get('theme_id')
            data['association_type'] = AssociationTypeEnum.theme.value
            quick_link = QuickLink.objects.create(**data)
        elif request.data.get('page_url'):
            data['page_url'] = request.data.get('page_url')
            data['association_type'] = AssociationTypeEnum.external_link.value
            quick_link = QuickLink.objects.create(**data)
        else:
            data['association_type'] = request.data.get('association_type')
            quick_link = QuickLink.objects.create(**data)
        serializer = self.get_serializer(quick_link)
        return success_response(serializer.data)
