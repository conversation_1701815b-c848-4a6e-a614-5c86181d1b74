import pendulum

from django.db.models import Sum
from drf_yasg import openapi
from drf_yasg.utils import swagger_auto_schema
from rest_framework import viewsets

from utils.api_response import success_response
from utils.pagination import StandardResultsSetPagination
from utils.utc_date_time import datetime_change_utc
from .content_operation_tag_actions import ContentOperationTagSerializers
from ..models import ContentOperationPowerTag


class ContentOperationTagViewSet(viewsets.ModelViewSet):
    queryset = ContentOperationPowerTag.objects
    serializer_class = ContentOperationTagSerializers

    @swagger_auto_schema(
        operation_id='能力标签数据列表',
        operation_summary='能力标签数据列表',
        manual_parameters=[
            openapi.Parameter('start_date', openapi.IN_QUERY, description='起止时间(大于等于)| yyyy-mm-dd', type=openapi.FORMAT_DATE),
            openapi.Parameter('end_date', openapi.IN_QUERY, description='结束时间(小于)| yyyy-mm-dd', type=openapi.FORMAT_DATE),
            openapi.Parameter('channel', openapi.IN_QUERY, description='数据来源(逗号分隔)| 1:搜索,2:辅导记录', type=openapi.TYPE_STRING),
            openapi.Parameter('project_id', openapi.IN_QUERY, description='项目id(逗号分隔)｜ 1,2,3', type=openapi.TYPE_STRING),
            openapi.Parameter('page', openapi.TYPE_ARRAY, description='第几页', type=openapi.TYPE_NUMBER),
            openapi.Parameter('page_size', openapi.IN_QUERY, description='每页显示条数默认15条', type=openapi.TYPE_NUMBER),
        ],
        tags=['内容管理数据相关']
    )
    def list(self, request, *args, **kwargs):

        utc_date = pendulum.today()
        start_date = request.query_params.get('start_date')
        end_date = request.query_params.get('end_date')
        if start_date:
            start_date = datetime_change_utc(start_date)
        else:
            start_date = utc_date.subtract(days=30)

        if end_date:
            end_date = datetime_change_utc(end_date)
        else:
            end_date = utc_date

        # 获取参数并格式化
        channel = request.query_params.get('channel', '1,2').split(',')
        project = request.query_params.get('project_id', '')

        content_tag = self.get_queryset()
        if project:
            content_tag = content_tag.filter(project__pk__in=project.split(','))

        # 关键字分组查询
        content_tag = content_tag.filter(
            channel__in=channel,
            date__gte=start_date,
            date__lt=end_date,
        ).values('tag').annotate(raw_count=Sum('count')).values('tag', 'raw_count').order_by('-raw_count', 'tag')

        # 反序列化为分页作准备
        tags = []
        for item in content_tag:
            tags.append(ContentOperationPowerTag(
                count=item['raw_count'],
                tag=item['tag'],
            ))
        # 分页
        paginator = StandardResultsSetPagination()
        page_list = paginator.paginate_queryset(tags, self.request)
        serializer = self.get_serializer(page_list, many=True)
        response = paginator.get_paginated_response(serializer.data)
        return success_response(response)
