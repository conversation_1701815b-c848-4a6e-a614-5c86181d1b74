import datetime
import io
import re
import time

import xlrd
import openpyxl

from django.db.models import Sum, Q
from django.db import transaction

from rest_framework import serializers
from wisdom_v2.models import ProjectMember, User, Project, CompanyMember, WorkWechatUser, \
    ProjectInterview, GrowthGoals, CoachTask, EvaluationReport, \
    ProjectEvaluationReport, EvaluationModule
from utils.api_response import WisdomValidationError
from wisdom_v2.enum.user_enum import area_code_dic
from wisdom_v2.enum.company_member_enum import ManageRoleEnum
from wisdom_v2.views.company_member_actions import id_number_check
from wisdom_v2.enum.project_member_enum import ProjectMemberRoleEnum
from wisdom_v2.enum.project_enum import ProjectEvaluationReportTypeEnum
from utils import aesencrypt, randomPassword
from wisdom_v2.enum.service_content_enum import CoachTypeEnum, PersonalReportTypeEnum
from wisdom_v2.models import OneToOneCoach, PersonalReport
from .constant import ATTR_TYPE_INTERVIEW, ATTR_STATUS_INTERVIEW_CANCEL, ATTR_TYPE_GROWTH_GOALS, MANAGE_EVALUATION, \
    INTERVIEW_TYPE_COACHING
from wisdom_v2.enum.project_interview_enum import ProjectInterviewPlaceCategoryEnum
from utils import task, validate
from ..common import interview_public
from ..models_file import ProjectMemberServiceContent, ProjectServiceStage


class ProjectMemberSerializer(serializers.ModelSerializer):
    class Meta:
        model = ProjectMember


class RelevanceCompanyMemberListSerializer(serializers.ModelSerializer):
    id = serializers.IntegerField(help_text='企业用户id', read_only=True)
    name = serializers.CharField(source='user.name', help_text='用户名', read_only=True)
    email = serializers.CharField(source='user.email', help_text='邮箱', read_only=True)
    true_name = serializers.CharField(source='user.cover_name', help_text='姓名', read_only=True)
    phone = serializers.CharField(source='user.phone', help_text='手机号', read_only=True)
    position = serializers.CharField(help_text='职位', read_only=True)
    department = serializers.CharField(help_text='事业部', read_only=True)
    is_exists = serializers.SerializerMethodField(help_text='是否在当前项目中存在， 1-是，0-否')

    class Meta:
        model = CompanyMember
        fields = ['id', 'name', 'email', 'true_name', 'phone', 'position', 'department', 'is_exists']

    def get_is_exists(self, obj):
        return 0


class ProjectMemberListSerializer(serializers.ModelSerializer):
    id = serializers.IntegerField(help_text='id', read_only=True)
    name = serializers.CharField(source='user.name', help_text='用户名', read_only=True)
    user_id = serializers.IntegerField(source='user.id', help_text='用户id', read_only=True)
    true_name = serializers.CharField(source='user.cover_name', help_text='姓名', read_only=True)
    phone = serializers.CharField(source='user.phone', help_text='手机号', read_only=True)
    email = serializers.CharField(source='user.email', help_text='邮箱', read_only=True)
    position = serializers.CharField(help_text='职位', read_only=True)
    coach_match_type = serializers.IntegerField(help_text='一对一辅导类型', read_only=True)
    online_progress = serializers.CharField(help_text='线上1对1辅导', read_only=True)
    offline_progress = serializers.CharField(help_text='线下1对1辅导', read_only=True)
    coach_id = serializers.IntegerField(help_text='教练', read_only=True)
    coach_target = serializers.CharField(help_text='教练目标', read_only=True)
    service_content = serializers.CharField(help_text='服务内容', read_only=True)
    is_forbidden = serializers.IntegerField(help_text='状态', required=False)
    stakeholder = serializers.ListField(help_text='利益相关者', read_only=True)
    coach_name = serializers.CharField(help_text='教练姓名', read_only=True)
    company_member_id = serializers.IntegerField(help_text='企业员工id', read_only=True)
    company_id = serializers.IntegerField(help_text='企业id', read_only=True)
    external_user_id = serializers.SerializerMethodField(help_text='企业微信用户id', required=False)
    growth_count = serializers.SerializerMethodField(help_text='成长目标数', required=False)
    is_service_content = serializers.SerializerMethodField(help_text='是否配置服务内容', required=False)

    def get_is_service_content(self, obj):
        if ProjectMemberServiceContent.objects.filter(project_member_id=obj.id, deleted=False).exists():
            return 1
        if ProjectServiceStage.objects.filter(project_member_id=obj.id, deleted=False).exists():
            return 1
        return 0

    def get_external_user_id(self, obj):
        wx_user = WorkWechatUser.objects.filter(user_id=obj.user.id, deleted=False).first()
        if wx_user:
            return wx_user.external_user_id
        return

    def get_growth_count(self, obj):
        growth_goals = GrowthGoals.objects.filter(
            public_attr__user_id=obj.user.id, public_attr__type=ATTR_TYPE_GROWTH_GOALS, deleted=False)
        return growth_goals.count()

    class Meta:
        fields = ['id', 'name', 'true_name', 'phone', 'position', 'online_progress', 'offline_progress',
                  'coach_id', 'coach_target', 'service_content', 'is_forbidden', 'email', 'department',
                  'is_service_content', 'stakeholder', 'coach_name', 'coach_match_type',
                  'company_member_id', 'company_id', 'external_user_id', 'is_send_email', 'growth_count',
                  'user_id']
        model = ProjectMember

    def update(self, instance, validated_data):
        if 'is_forbidden' not in validated_data.keys():
            raise WisdomValidationError('未传入是否归档数据')
        is_forbidden = validated_data.get('is_forbidden')
        instance.is_forbidden = is_forbidden
        instance.save()
        # 查询是否需要重新生成教练型管理者项目报告
        report = ProjectEvaluationReport.objects.filter(
            project=instance.project, type=ProjectEvaluationReportTypeEnum.MANAGE_EVALUATION, deleted=False)
        if report.exists():
            if EvaluationModule.objects.filter(deleted=False, evaluation__code=MANAGE_EVALUATION,
                                               project_bundle__project_member=instance).exists():
                # 项目存在已生成的教练型管理者项目报告， 且当前用户配置了教练型管理者测评
                task.generate_manage_project_report.apply_async(
                    kwargs=dict(project_id=instance.project_id), countdown=5, expires=120)
        return instance


class ProjectMemberCreateSerializer(serializers.ModelSerializer):
    project_id = serializers.IntegerField(help_text='项目id(必传)', write_only=True)
    name = serializers.CharField(help_text='用户名（必填）', write_only=True, required=False)
    true_name = serializers.CharField(help_text='姓名', required=False, write_only=True)
    position = serializers.CharField(help_text='职务', required=False, write_only=True)
    area_code = serializers.IntegerField(help_text='区号', write_only=True, required=False)
    phone = serializers.CharField(help_text='手机号', required=False, write_only=True)
    email = serializers.CharField(help_text='邮箱', required=False, write_only=True)
    id_number = serializers.CharField(help_text='身份证号', required=False, write_only=True)
    department = serializers.CharField(help_text='事业部', required=False, write_only=True)
    job_number = serializers.CharField(help_text='工号', required=False, write_only=True)
    employee = serializers.IntegerField(help_text='直系下属人数', required=False, write_only=True)
    manage_role = serializers.IntegerField(help_text='管理角色', required=False, write_only=True)
    work_year = serializers.IntegerField(help_text='工作年限', required=False, write_only=True)
    company_year = serializers.IntegerField(help_text='公司年限', required=False, write_only=True)
    job_year = serializers.IntegerField(help_text='本岗年限', required=False, write_only=True)
    company_member_id = serializers.ListField(help_text='企业用户id(关联被教练者时传入)', required=False, write_only=True)

    class Meta:
        fields = ['id', 'name', 'true_name', 'position', 'area_code', 'phone', 'email', 'id_number', 'department',
                  'job_number', 'employee', 'manage_role', 'work_year', 'company_year', 'job_year', 'company_member_id',
                  'project_id']
        model = ProjectMember

    def create(self, validated_data):
        validated_data = remove_none_data(validated_data)

        project_id = validated_data.get('project_id')
        # validated_data.pop('project_id')
        project = Project.objects.get(id=project_id)

        if 'company_member_id' in validated_data.keys():
            company_member_id = validated_data.get('company_member_id')
            project_member_list = []
            if company_member_id:
                for id in company_member_id:
                    company_member = CompanyMember.objects.filter(
                        pk=id, user__deleted=False, deleted=False).first()
                    if not company_member:
                        raise WisdomValidationError('当前企业用户不存在')
                    if project.company_id != company_member.company_id:
                        raise WisdomValidationError('当前企业用户与当前项目不在同一企业下')
                    if ProjectMember.objects.filter(project_id=project_id, user=company_member.user,
                                                    deleted=False).exists():
                        raise WisdomValidationError('当前企业用户已存在于当前项目')
                    project_member_list.append(ProjectMember(project_id=project_id, user_id=company_member.user.id))
            if not project_member_list:
                raise WisdomValidationError('当前企业用户不存在')
            with transaction.atomic():
                ProjectMember.objects.bulk_create(project_member_list)
            return 'success'

        else:
            err = valid_member_data(validated_data)
            if err:
                raise WisdomValidationError(err)
            validated_data.pop('project_id')

            user_dict = {
                'name': validated_data.get('name') if validated_data.get('name') else validated_data.get('email'),
                'area_code': validated_data.get('area_code')}
            if 'true_name' in validated_data.keys():
                user_dict['true_name'] = validated_data.get('true_name')
            else:
                user_dict['true_name'] = "用户{}".format(randomPassword(length=4))
            if 'phone' in validated_data.keys():
                user_dict['phone'] = validated_data.get('phone')
            if 'email' in validated_data.keys():
                user_dict['email'] = validated_data.get('email')
            password = randomPassword()
            pwd = aesencrypt(password) if 'email' in user_dict.keys() else aesencrypt('123456')
            user_dict['password'] = pwd
            with transaction.atomic():
                user_instance = User.objects.create(**user_dict)
                validated_data = remove_user_data(validated_data)
                validated_data['user_id'] = user_instance.id
                validated_data['company_id'] = project.company_id
                CompanyMember.objects.create(**validated_data)
                instance = ProjectMember.objects.create(project_id=project_id, user_id=user_instance.id,
                                                        role=ProjectMemberRoleEnum.coachee.value)
            return instance


def remove_none_data(data: dict):
    for i in list(data.keys()):
        if not data[i] and data[i] != 0:
            del data[i]

    return data


def remove_user_data(data: dict):
    if 'true_name' in data.keys():
        data.pop('true_name')
    if 'phone' in data.keys():
        data.pop('phone')
    if 'email' in data.keys():
        data.pop('email')
    if 'area_code' in data.keys():
        data.pop('area_code')
    if 'name' in data.keys():
        data.pop('name')
    return data


def valid_member_data(data: dict, is_import=False, is_company_member=False):
    if not is_company_member:
        if 'project_id' not in data.keys():
            return '请传入项目id'
        project_id = data.get('project_id')
        project = Project.objects.filter(id=project_id, deleted=False).first()
        if not project:
            return '当前项目未关联企业，无法添加被教练者'

    # if 'company_member_id' in data.keys():
    #     if not CompanyMember.objects.filter(pk=data.get('company_member_id'), user__deleted=False).exists():
    #         return '企业用户不存在'

    # if 'name' not in data.keys():
    #     return '请输入用户名'

    if 'true_name' in data.keys():
        data['true_name'] = str(data['true_name'])
        if len(data.get('true_name')) > 50:
            return '姓名最多50个字'
    else:
        return '请输入姓名'

    if 'position' in data.keys():
        data['position'] = str(data['position'])
        if len(data.get('position')) > 50:
            return '职务最多50个字'
    else:
        return '请输入职务'

    if 'area_code' not in data.keys():
        data['area_code'] = 1
    else:
        if is_import:
            data['area_code'] = str(data['area_code'])
            if len(data['area_code']) > 4:
                return "区号最多4位"
            if not data['area_code'].isdigit():
                return "区号必须为数字,最多4位"
            if not area_code_dic.get(data['area_code'], None):
                return '当前区号不存在'
            data['area_code'] = area_code_dic.get(data['area_code'])

        else:
            if data.get('area_code') not in area_code_dic['code_scope']:
                return '区号错误'

    if 'phone' in data.keys():
        # 查重
        data['phone'] = str(data['phone'])
        phone = data.get('phone')
        if not phone.isdigit():
            return '手机号应为数字'
        if not re.match(r"^1[3456789][0-9]{9}$", phone):
            return '手机号格式错误'
       # 修改后手机号全系统查重
        if User.objects.filter(Q(phone=phone) | Q(name=phone), deleted=False).exists():
            return '当前手机号已存在'

    if 'email' not in data.keys():
        return '请输入邮箱'
    else:
        # 当前项目内的被教练者邮箱查重， 如果创建company_member时有当前邮箱的company_member则不创建
        data['email'] = str(data['email'])
        email = data.get('email')
        if ' ' in email:
            return '邮箱中存在空格，请修改后再添加。'
        if not validate.validate_email(email):
            return '邮箱错误'
        if len(email) > 50:
            return '邮箱最多50个字'
        if User.objects.filter(Q(email=email) | Q(name=email), deleted=False).exists():
            return '当前邮箱已存在'

    if 'name' in data.keys():
        if len(str(data['name'])) > 50:
            return '用户名最多50个字'

    data['name'] = data['name'] if 'name' in data.keys() else data['email']
    name = data.get('name')
    # 查重
    if User.objects.filter(Q(name=name) | Q(phone=name), deleted=False).exists():
        return '该用户名已存在'

    if 'id_number' in data.keys():
        # 身份证号字段在企业用户company_member中存储，一个company_member可以有多个project_member。
        # 所以查重在方法外查是否存在company_member，如果有就只创建当前用户的project_member不创建company_member
        data['id_number'] = str(data['id_number'])
        if not id_number_check(data.get('id_number')):
            return '身份证号错误'
        if CompanyMember.objects.filter(id_number=data['id_number'], user__deleted=False):
            return '该身份证号已存在'

    if 'department' in data.keys():
        data['department'] = str(data['department'])
        if len(data.get('department')) > 50:
            return '事业部最多50个字'

    if 'job_number' in data.keys():
        data['job_number'] = str(data['job_number'])
        if len(data.get('job_number')) > 50:
            return '工号最多50个字'

    if 'employee' in data.keys():
        # if is_import:
        #     if type(data.get('employee')) != int:
        #         return '直系下属应为整数'
        #     if data['employee'] < 1:
        #         return '直系下属应为正整数且不能为0'
        #     if data['employee'] > 99999:
        #         return '直系下属最多5位数'
        if '.' in str(data['employee']):
            return '直系下属应为整数'
        try:
            data['employee'] = int(data['employee'])
        except:
            return '直系下属应为整数'
        if type(data.get('employee')) != int:
            return '直系下属应为整数'
        if data.get('employee') < 1:
            return '直系下属应为正整数且不能为0'
        if data.get('employee') > 99999:
            return '直系下属最多5位数'

    if 'manage_role' in data.keys():
        if is_import:
            if data['manage_role'] not in ('一线经理', '部门经理', '事业部经理', '事业部总经理', '集团高管', '首席执行官'):
                return '管理角色错误'
            tmp = {'一线经理': 1, '部门经理': 2, '事业部经理': 3, '事业部总经理': 4, '集团高管': 5, '首席执行官': 6, }
            data['manage_role'] = tmp[data['manage_role']]
        data['manage_role'] = int(data['manage_role'])
        if data.get('manage_role') not in [ManageRoleEnum.line_manager.value, ManageRoleEnum.business_manager.value,
                                           ManageRoleEnum.department_manager.value, ManageRoleEnum.executives.value,
                                           ManageRoleEnum.general_business_manager.value, ManageRoleEnum.ceo.value]:
            return '管理角色参数错误'

    if 'company_year' in data.keys():
        # if is_import:
        #     if type(data.get('company_year')) != int:
        #         return '公司年限只能为整数'
        #     if data.get('company_year') < 1:
        #         return '公司年限只能为正整数且不能为0'
        #     if data.get('company_year') > 99:
        #         return '公司年限最多2位数'
        if '.' in str(data['company_year']):
            return '公司年限只能为整数'
        try:
            data['company_year'] = int(data['company_year'])
        except:
            return '公司年限只能为整数'
        if type(data.get('company_year')) != int:
            return '公司年限只能为整数'
        if data.get('company_year') < 1:
            return '公司年限只能为正整数且不能为0'
        if data.get('company_year') > 99:
            return '公司年限最多2位数'

    if 'job_year' in data.keys():
        # if is_import:
        #     if type(data.get('job_year')) == int:
        #         pass
        #     elif type(data.get('job_year')) == float:
        #         data['job_year'] = int(data['job_year'])
        #     else:
        #         return '本岗年限只能为整数'
        if '.' in str(data['job_year']):
            return '本岗年限只能为整数'
        try:
            data['job_year'] = int(data['job_year'])
        except:
            return '本岗年限只能为整数'
        if type(data.get('job_year')) != int:
            return '本岗年限只能为整数'
        if data.get('job_year') < 1:
            return '本岗年限只能为正整数且不能为0'
        if data.get('job_year') > 99:
            return '本岗年限最多2位数'

    if 'work_year' in data.keys():
        # if is_import:
        #     if type(data.get('work_year')) == int:
        #         pass
        #     elif type(data.get('work_year')) == float:
        #         data['work_year'] = int(data['work_year'])
        #     else:
        #         return '工作年限只能为整数'
        if '.' in str(data['work_year']):
            return '工作年限只能为整数'
        try:
            data['work_year'] = int(data['work_year'])
        except:
            return '工作年限只能为整数'
        if type(data.get('work_year')) != int:
            return '工作年限只能为整数'
        if data.get('work_year') < 1:
            return '工作年限只能为正整数且不能为0'
        if data.get('work_year') > 99:
            return '工作年限最多2位数'

    return None


def member_data_clean_reduce(file):
    file_xlsx = io.BytesIO(file.read())
    name_list = []
    email_list = []
    error = []
    try:
        raw_trainee_coaches = openpyxl.load_workbook(file_xlsx)['学员']
    except Exception as e:
        return False, [], '模版文件格式错误,请使用正确的模板文件'

    data_list = []

    for pos, item in enumerate(raw_trainee_coaches.rows):
        if all(cell.value is None for cell in item):
            continue
        row = pos + 1
        true_name, position, email = item[0].value, item[1].value, item[2].value
        if pos == 1:
            if [true_name, position, email] != ["*姓名", "*职务", "*邮箱"]:
                return False, [], '模版文件格式错误,请使用正确的模板文件'

        if pos == raw_trainee_coaches.max_row:
            break

        if pos > 1:
            if true_name:
                name = str(true_name)
                if len(name) > 20:
                    error.append({'error': '姓名最多20个字', 'count': row})
                if name in name_list:
                    error.append({'error': '姓名重复', 'count': row})
            else:
                error.append({'error': '请输入姓名', 'count': row})
            if not position:
                error.append({'error': '请输入职务', 'count': row})

            if email:
                email = str(email)
                if ' ' in email:
                    error.append({'error': '邮箱中存在空格，请修改后再添加。', 'count': row})
                elif not validate.validate_email(email):
                    error.append({'error': '邮箱格式错误', 'count': row})
                elif len(email) > 50:
                    error.append({'error': '邮箱最多50字', 'count': row})
                elif User.objects.filter(email=email, deleted=False).exists():
                    error.append({'error': '该邮箱已存在', 'count': row})
                else:
                    if email not in email_list:
                        email_list.append(email)
                    else:
                        error.append({'error': '文件中邮箱出现重复', 'count': row})
            else:
                error.append({'error': '请输入邮箱', 'count': row})
            data = {
                "true_name": true_name,
                "email": email,
                "position": position
            }
            data_list.append(data)

    return True, data_list, error


def member_data_clean(file_type, path, company_id=None, project_id=None):
    if file_type == 'xls':
        data = xlrd.open_workbook(path)
        table = data.sheet_by_name('被教练者')
        count = table.nrows
        lst = []
        lst_header = ['姓名', '职务', '手机区号', '手机号', '邮箱', '证件类型', '事业部', '工号', '直系下属（人）',
                      '管理角色', '工作年限（年）', '公司年限（年）', '本岗年限（年）']
        lst.append(table.cell_value(1, 1))
        lst.append(table.cell_value(1, 2))
        lst.append(table.cell_value(1, 3))
        lst.append(table.cell_value(1, 4))
        lst.append(table.cell_value(1, 5))
        lst.append(table.cell_value(1, 6))
        lst.append(table.cell_value(1, 7))
        lst.append(table.cell_value(1, 8))
        lst.append(table.cell_value(1, 9))
        lst.append(table.cell_value(1, 10))
        lst.append(table.cell_value(1, 11))
        lst.append(table.cell_value(1, 12))
        lst.append(table.cell_value(1, 13))
        lst.append(table.cell_value(1, 14))
        if lst != lst_header:
            return None, None, False
    else:
        data = openpyxl.load_workbook(path)
        sheet1 = data['被教练者']
        count = sheet1.max_row
        lst = []
        lst_header = ['用户名', '*姓名', '*职务', '手机区号', '手机号', '*邮箱', '证件类型', '事业部', '工号',
                      '直系下属（人）', '管理角色', '工作年限（年）', '公司年限（年）', '本岗年限（年）']
        lst.append(sheet1.cell(2, 1).value)
        lst.append(sheet1.cell(2, 2).value)
        lst.append(sheet1.cell(2, 3).value)
        lst.append(sheet1.cell(2, 4).value)
        lst.append(sheet1.cell(2, 5).value)
        lst.append(sheet1.cell(2, 6).value)
        lst.append(sheet1.cell(2, 7).value)
        lst.append(sheet1.cell(2, 8).value)
        lst.append(sheet1.cell(2, 9).value)
        lst.append(sheet1.cell(2, 10).value)
        lst.append(sheet1.cell(2, 11).value)
        lst.append(sheet1.cell(2, 12).value)
        lst.append(sheet1.cell(2, 13).value)
        lst.append(sheet1.cell(2, 14).value)
        if lst != lst_header:
            return None, None, False

    err_lst = []
    data_lst = []
    name_lst = []
    phone_lst = []
    email_lst = []
    for i in range(count + 1):
        if i < 3:
            continue
        if file_type == 'xls':
            if all(table.cell_value(i - 1, col) in [None, ''] for col in range(table.ncols)):
                continue  # 如果这一行所有单元格都为空，则跳过
            data = {
                'name': table.cell_value(i - 1, 0),
                'true_name': table.cell_value(i - 1, 1),
                'position': table.cell_value(i - 1, 2),
                'phone': table.cell_value(i - 1, 4),
                'email': table.cell_value(i - 1, 5),
                'department': table.cell_value(i - 1, 7),
                'job_number': table.cell_value(i - 1, 8),
                'employee': table.cell_value(i - 1, 9),
                'manage_role': table.cell_value(i - 1, 10),
                'work_year': table.cell_value(i - 1, 11),
                'company_year': table.cell_value(i - 1, 12),
                'job_year': table.cell_value(i - 1, 13),
                'area_code': table.cell_value(i - 1, 3),
            }
        else:
            if all(sheet1.cell(i, col + 1).value in [None, ''] for col in range(sheet1.max_column)):
                continue  # 如果这一行所有单元格都为空，则跳过

            data = {
                'name': sheet1.cell(i, 1).value,
                'true_name': sheet1.cell(i, 2).value,
                'position': sheet1.cell(i, 3).value,
                'phone': sheet1.cell(i, 5).value,
                'email': sheet1.cell(i, 6).value,
                'department': sheet1.cell(i, 8).value,
                'job_number': sheet1.cell(i, 9).value,
                'employee': sheet1.cell(i, 10).value,
                'manage_role': sheet1.cell(i, 11).value,
                'work_year': sheet1.cell(i, 12).value,
                'company_year': sheet1.cell(i, 13).value,
                'job_year': sheet1.cell(i, 14).value,
                'area_code': sheet1.cell(i, 4).value
            }
        if data['name']:
            if data['name'] in name_lst:
                err_lst.append({'count': i, 'error': '当前用户名重复'})
            name_lst.append(data['name'])
        if data['phone']:
            if data['phone'] in phone_lst:
                err_lst.append({'count': i, 'error': '当前手机号重复'})
            phone_lst.append(data['phone'])

        if data['email']:
            if data['email'] in email_lst:
                err_lst.append({'count': i, 'error': '当前邮箱重复'})
            if ' ' in data['email']:
                err_lst.append({'count': i, 'error': '邮箱中存在空格，请修改后再添加。'})
            email_lst.append(data['email'])

        if project_id:
            data['project_id'] = project_id
            data = remove_none_data(data)
            err = valid_member_data(data, is_import=True)
            if err:
                err_lst.append({"count": i, 'error': err})
                continue
        else:
            data = remove_none_data(data)
            err = valid_member_data(data, is_import=True, is_company_member=True)
            if err:
                err_lst.append({"count": i, 'error': err})
                continue

        data_lst.append(data)

    return data_lst, err_lst, True


def check_time_intersection(time_list):
    # 检查时间是否重合
    for limit_time in time_list:
        limit_start = int(time.mktime(time.strptime(limit_time[0], "%Y-%m-%d")))
        limit_end = int(time.mktime(time.strptime(limit_time[1], "%Y-%m-%d")))
        for n_time in time_list:
            if n_time == limit_time:
                continue
            n_start = int(time.mktime(time.strptime(n_time[0], "%Y-%m-%d")))
            n_end = int(time.mktime(time.strptime(n_time[1], "%Y-%m-%d")))
            if max(n_start, limit_start) <= min(n_end, limit_end):
                return False
    return True


def get_minute(start_time, end_time, is_date_time=False):
    if is_date_time:
        start_time = datetime.datetime.strptime(start_time, '%Y-%m-%d %H:%M:%S')
        end_time = datetime.datetime.strptime(end_time, '%Y-%m-%d %H:%M:%S')
    else:
        start_time = datetime.datetime.strptime(start_time, '%H:%M:%S')
        end_time = datetime.datetime.strptime(end_time, '%H:%M:%S')

    seconds = (end_time - start_time).total_seconds()

    mins = seconds / 60
    return int(mins)


class InterviewScheduleSerializer(serializers.ModelSerializer):
    id = serializers.IntegerField(help_text='id', read_only=True)
    true_name = serializers.CharField(source='user.cover_name', help_text='姓名', read_only=True)
    project_name = serializers.CharField(source='project.name', help_text='所属项目', read_only=True)
    interview_hour = serializers.SerializerMethodField(help_text='已辅导时长', read_only=True)
    all_hour = serializers.SerializerMethodField(help_text='总时长', read_only=True)
    all_score = serializers.SerializerMethodField(help_text='有效度、投入度、满意度分数', read_only=True)
    created_at = serializers.DateTimeField(help_text='加入日期', format='%Y-%m-%d')
    growth_count = serializers.SerializerMethodField(help_text='成长目标数量', read_only=True)
    report_count = serializers.SerializerMethodField(help_text='报告数量', read_only=True)
    project_id = serializers.IntegerField(help_text='项目id', read_only=True)

    class Meta:
        fields = ['id', 'true_name', 'project_name', 'interview_hour', 'all_hour', 'all_score',
                  'created_at', 'growth_count', 'report_count', 'project_id']
        model = ProjectMember

    def get_interview_hour(self, obj):
        # 线下集体辅导、线上/线下一对一辅导 已结束未取消的总时长
        interview_query = ProjectInterview.objects.filter(deleted=False,
                                                          type=INTERVIEW_TYPE_COACHING,
                                                          public_attr__type=ATTR_TYPE_INTERVIEW,
                                                          public_attr__project=obj.project,
                                                          public_attr__target_user=obj.user,
                                                          public_attr__end_time__lt=datetime.datetime.now()
                                                          ).exclude(public_attr__status=ATTR_STATUS_INTERVIEW_CANCEL)
        times = interview_query.aggregate(times_minute=Sum('times'))
        if times['times_minute']:
            interview_hour = round(times['times_minute'] / 60, 1)
        else:
            interview_hour = 0
        return interview_hour

    def get_all_hour(self, obj):
        # 总时长 = 配置的线上线下一对一辅导时长之和 + 未取消的集体辅导时长  (未配置线上/线下一对一取项目时长)
        all_hours = 0
        # online_one_to_one = OneToOneCoach.objects.filter(project_bundle__project_member=obj,
        #                                                  type=CoachTypeEnum.online.value, deleted=False).first()
        # if online_one_to_one:
        #     if online_one_to_one.online_available_time != 0 and online_one_to_one.online_available_time:
        #         all_hours = all_hours + online_one_to_one.online_available_time
        #     else:  # 配置时间为无限制 取项目剩余时长
        #         all_hours += obj.project.all_times if obj.project.all_times else 0
        
        
        all_times = OneToOneCoach.objects.filter(project_bundle__project_member=obj,
                                                         type=CoachTypeEnum.online.value, deleted=False).aggregate(used_times=Sum('online_time'))
        if all_times['used_times']:
            all_hours += all_times['used_times']
        # offline_one_to_one = OneToOneCoach.objects.filter(project_bundle__project_member=obj,
        #                                                   type=CoachTypeEnum.offline.value, deleted=False).first()
        # if offline_one_to_one:
        #     if offline_one_to_one.offline_available_time != 0 and offline_one_to_one.offline_available_time:
        #         all_hours += offline_one_to_one.offline_available_time
        #     else:
        #         all_hours += obj.project.offline_time if obj.project.offline_time else 0

        # group_coach_times = ProjectInterview.objects.filter(
        #     deleted=False, public_attr__project=obj.project, public_attr__target_user=obj.user,
        #     place_category=ProjectInterviewPlaceCategoryEnum.offline_group_coach.value).\
        #     exclude(public_attr__status=ATTR_STATUS_INTERVIEW_CANCEL).aggregate(times=Sum('times'))
        # if group_coach_times['times']:
        #     all_hours += round(group_coach_times['times'] / 60, 1)

        return all_hours

    def get_all_score(self, obj):
        score_data = interview_public.get_total_interview_scores(project_id=obj.project_id, coachee_user_id=obj.user_id)
        avg_target_progress = score_data.get('target_progress_avg_score')
        avg_harvest_score = score_data.get('harvest_avg_score')
        avg_satisfaction_score = score_data.get('satisfaction_avg_score')
        return {'target_progress': avg_target_progress, 'harvest_score': avg_harvest_score,
                'satisfaction_score': avg_satisfaction_score}

    def get_growth_count(self, obj):
        growth_goals = GrowthGoals.objects.filter(public_attr__user=obj.user, deleted=False).count()
        return growth_goals

    def get_report_count(self, obj):
        # 教练任务报告
        coach_task_count = CoachTask.objects.filter(
            Q(coach_submit_time__isnull=False) | Q(coachee_submit_time__isnull=False),
            project_bundle__project_member=obj, deleted=False).count()
        # 测评报告
        evaluation_report_count = EvaluationReport.objects.filter(deleted=False, public_attr__project=obj.project,
                                                                  public_attr__user=obj.user).count()
        # 改变观察
        personal_report = PersonalReport.objects.filter(
            user=obj.user, project=obj.project,
            type=PersonalReportTypeEnum.change_observation_report.value, deleted=False).count()
        return coach_task_count + evaluation_report_count + personal_report



