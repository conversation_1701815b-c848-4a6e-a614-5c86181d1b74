import re
from datetime import datetime

from django.db.models import Case, When, Q
from drf_yasg import openapi
from django.db import transaction
from rest_framework import viewsets
from drf_yasg.utils import swagger_auto_schema
from rest_framework.decorators import action

from utils.messagecenter import center
from utils import randomPassword, aesencrypt, validate
from utils.trainee_coaches_data import get_all_coaches
from wisdom_v2.models import TraineeCoach, User, PublicAttr, ProjectInterview, \
    TraineeCoachInviteUser, CoachAppraise, Coach, ProjectInterviewRecord, ProjectMember, PersonalUser
from utils.pagination import StandardResultsSetPagination
from utils.api_response import success_response, parameter_error_response
from wisdom_v2.views.project_interview_action import ProjectInterviewListSerializer
from wisdom_v2.views.trainee_coach_actions import TraineeCoachSerializers, TraineeCoachBaseSerializers, \
    TraineeCoachClientCommentSerializers, TraineeCoachUserSerializers, ProjectInterviewRecordAppraiseSerializer


class TraineeCoachBaseViewSet(viewsets.ModelViewSet):
    queryset = TraineeCoach.objects.filter(deleted=False).order_by('-created_at')
    serializer_class = TraineeCoachBaseSerializers

    @swagger_auto_schema(
        operation_id='个人教练详情',
        operation_summary='个人教练详情',
        manual_parameters=[
            openapi.Parameter('user_id', openapi.IN_QUERY, description='教练用户id', type=openapi.TYPE_NUMBER),
        ],
        tags=['个人教练相关']
    )
    @action(methods=['get'], detail=False, url_path='detail')
    def trainee_coach_detail(self, request, *args, **kwargs):
        try:
            user_id = request.query_params['user_id']
            trainee_coach = TraineeCoach.objects.get(user_id=user_id)
        except:
            return parameter_error_response()
        serializer = self.get_serializer(trainee_coach)
        return success_response(serializer.data)

    @swagger_auto_schema(
        operation_id='个人教练验证',
        operation_summary='个人教练验证',
        manual_parameters=[
            openapi.Parameter('email', openapi.IN_QUERY, description='邮箱', type=openapi.TYPE_STRING),
            openapi.Parameter('phone', openapi.IN_QUERY, description='手机号', type=openapi.TYPE_NUMBER),
        ],
        tags=['个人教练相关']
    )
    @action(methods=['get'], detail=False, url_path='check')
    def trainee_coach_check(self, request, *args, **kwargs):

        phone = request.query_params.get('phone')
        email = request.query_params.get('email')

        if phone:
            user = User.objects.filter(phone=phone, deleted=False).first()
            if user:
                if Coach.objects.filter(user=user, deleted=False).exists():
                    return success_response({'status': 0})
                if ProjectMember.objects.filter(user=user, deleted=False).exists():
                    return success_response({'status': 0})
                if not PersonalUser.objects.filter(user=user, deleted=False).exists():
                    return success_response({'status': 0})
        if email and User.objects.filter(email=email, deleted=False).exists():
            return success_response({'status': 0})
        return success_response({'status': 1})

    @swagger_auto_schema(
        operation_id='修改个人教练信息',
        operation_summary='修改个人教练信息',
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'user_id': openapi.Schema(type=openapi.TYPE_STRING, description='用户名'),
                'name': openapi.Schema(type=openapi.TYPE_STRING, description='用户名'),
                'email': openapi.Schema(type=openapi.TYPE_STRING, description='邮箱'),
                'phone': openapi.Schema(type=openapi.TYPE_STRING, description='手机号'),
                'head_image_url': openapi.Schema(type=openapi.TYPE_STRING, description='头像链接'),
                'user_class': openapi.Schema(
                    type=openapi.TYPE_STRING, description='班次'),
                'extra_time': openapi.Schema(
                    type=openapi.TYPE_NUMBER, description='平台外一对一辅导时常'),
                'working_years': openapi.Schema(
                    type=openapi.TYPE_NUMBER, description='工作年限 | 1：一年以上，2：三年以上，3：五年以上'),
                'language': openapi.Schema(
                    type=openapi.TYPE_NUMBER, description='语言｜ 1：中文，2：英语，3：法语，4：日语'),
                'work_experience': openapi.Schema(type=openapi.TYPE_STRING, description='工作经历'),
                'brief': openapi.Schema(type=openapi.TYPE_STRING, description='教练简介'),
                'customer_evaluate': openapi.Schema(type=openapi.TYPE_STRING, description='案例及客户评价'),
                'style': openapi.Schema(type=openapi.TYPE_STRING, description='教练风格'),
                'qualification': openapi.Schema(type=openapi.TYPE_ARRAY, description='资质证书', items=openapi.Schema(
                    type=openapi.TYPE_OBJECT, properties={
                        "describe": openapi.Schema(type=openapi.TYPE_STRING, description='描述'),
                        "image_url": openapi.Schema(type=openapi.TYPE_STRING, description='图片链接'),
                    })),
                'industry': openapi.Schema(type=openapi.TYPE_STRING, description='教练过的行业'),
                'domain': openapi.Schema(type=openapi.TYPE_STRING, description='教练领域'),
                'coach_auth': openapi.Schema(
                    type=openapi.TYPE_STRING, description='教练认证 ｜ 1：ICF-ACC, 2：ICF-PCC, 3：ICF-MCC, 4：准ICF-PCC, 5：个人教练'),
            }
        ),
        tags=['个人教练相关']
    )
    @action(methods=['post'], detail=False, url_path='update_trainee_coach')
    def update_trainee_coach(self, request, *args, **kwargs):
        try:
            data = request.data.copy()
            user_id = data['user_id']
            instance = TraineeCoach.objects.get(user_id=user_id)
        except:
            return parameter_error_response()
        name = request.data.get('name', '')
        phone = request.data.get('phone', 0)
        email = request.data.get('email', '')
        user_class = request.data.get('user_class', '')
        extra_time = request.data.get('extra_time', 0)
        if name:
            if len(name) > 20:
                return parameter_error_response('用户名最多20个字')
        if phone:
            phone = str(phone)
            if len(phone) > 11:
                return parameter_error_response('手机号最多11位')
            elif not phone.isdigit():
                return parameter_error_response('手机号应为数字')
            elif User.objects.filter(
                    phone=phone, deleted=False).exclude(id=instance.user.id).exists():
                return parameter_error_response('该手机号已注册')
        if email:
            if ' ' in email:
                return parameter_error_response('邮箱中存在空格，请修改后再添加。')
            if not validate.validate_email(email):
                return parameter_error_response('邮箱错误')
            elif len(email) > 50:
                return parameter_error_response('邮箱最多50个字')
            elif User.objects.filter(
                    email=email, deleted=False).exclude(id=instance.user.id).exists():
                return parameter_error_response('该邮箱已注册')
        if user_class:
            if len(str(user_class)) > 10:
                return parameter_error_response('班次最多10个字')
        if extra_time:
            if len(str(extra_time)) > 10:
                return parameter_error_response('平台外辅导时常最多五个字符')

        with transaction.atomic():
            user = instance.user
            user.true_name = request.data.get('name', user.true_name)
            user.email = request.data.get('email', user.email)
            user.phone = request.data.get('phone', user.phone)
            user.head_image_url = request.data.get('head_image_url', user.head_image_url)
            User.save(user)
            serializer = self.get_serializer(instance, data=data, partial=True)
            serializer.is_valid(raise_exception=True)
            serializer.save()
        return success_response(serializer.data)

    @swagger_auto_schema(
        operation_id='创建个人教练',
        operation_summary='创建个人教练',
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'name': openapi.Schema(type=openapi.TYPE_STRING, description='用户名'),
                'email': openapi.Schema(type=openapi.TYPE_STRING, description='邮箱'),
                'phone': openapi.Schema(type=openapi.TYPE_STRING, description='手机号'),
                'head_image_url': openapi.Schema(type=openapi.TYPE_STRING, description='头像链接'),

                'user_class': openapi.Schema(
                    type=openapi.TYPE_STRING, description='班次'),
                'extra_time': openapi.Schema(
                    type=openapi.TYPE_NUMBER, description='平台外一对一辅导时常'),
                'working_years': openapi.Schema(
                    type=openapi.TYPE_NUMBER, description='工作年限 | 1：一年以上，2：三年以上，3：五年以上'),
                'language': openapi.Schema(
                    type=openapi.TYPE_NUMBER, description='语言｜ 1：中文，2：英语，3：法语，4：日语'),
                'work_experience': openapi.Schema(type=openapi.TYPE_STRING, description='工作经历'),
                'brief': openapi.Schema(type=openapi.TYPE_STRING, description='教练简介'),
                'customer_evaluate': openapi.Schema(type=openapi.TYPE_STRING, description='案例及客户评价'),
                'style': openapi.Schema(type=openapi.TYPE_STRING, description='教练风格'),
                'qualification': openapi.Schema(type=openapi.TYPE_ARRAY, description='资质证书', items=openapi.Schema(
                    type=openapi.TYPE_OBJECT, properties={
                        "describe": openapi.Schema(type=openapi.TYPE_STRING, description='描述'),
                        "image_url": openapi.Schema(type=openapi.TYPE_BOOLEAN, description='图片链接'),
                    })),
                'industry': openapi.Schema(type=openapi.TYPE_STRING, description='教练过的行业'),
                'domain': openapi.Schema(type=openapi.TYPE_STRING, description='教练领域'),
                'coach_auth': openapi.Schema(
                    type=openapi.TYPE_STRING, description='教练认证 ｜ 1：ICF-ACC, 2：ICF-PCC, 3：ICF-MCC, 4：准ICF-PCC, 5：个人教练'),
            }
        ),
        tags=['个人教练相关']
    )
    def create(self, request, *args, **kwargs):
        try:
            name = request.data.get('name')
            email = request.data.get('email')
            phone = str(request.data.get('phone'))
            user_class = request.data.get('user_class')
            head_image_url = request.data.get('head_image_url')
            language = request.data.get('language')

            extra_time = request.data.get('extra_time')
            working_years = request.data.get('working_years')
            brief = request.data.get('brief')
            style = request.data.get('style')
            customer_evaluate = request.data.get('customer_evaluate')
            domain = request.data.get('domain')
            coach_auth = request.data.get('coach_auth')
            qualification = request.data.get('qualification')
            industry = request.data.get('industry')
        except (KeyError, TypeError) as e:
            return parameter_error_response(str(e))

        if name:
            if len(name) > 20:
                return parameter_error_response('用户名最多20个字')
        else:
            return parameter_error_response('请输入用户名')

        if phone:
            if len(phone) > 11:
                return parameter_error_response('手机号最多11位')
            elif not phone.isdigit():
                return parameter_error_response('手机号应为数字')
            elif User.objects.filter(phone=phone, deleted=False).exists():
                return parameter_error_response('该手机号已注册')
        else:
            return parameter_error_response('请输入手机号')

        if email:
            if ' ' in email:
                return parameter_error_response('邮箱中存在空格，请修改后再添加。')
            if not validate.validate_email(email):
                return parameter_error_response('邮箱格式错误')
            elif len(email) > 50:

                return parameter_error_response('邮箱最多50个字')
            elif User.objects.filter(email=email, deleted=False).exists():
                return parameter_error_response('该邮箱已注册')
        else:
            return parameter_error_response('请输入邮箱')
        if user_class:
            if len(user_class) > 10:
                return parameter_error_response('班次最多10个字符')
        else:
            return parameter_error_response('请输入班次')
        if extra_time:
            if len(str(extra_time)) > 10:
                return parameter_error_response('平台外辅导时常最多五个字符')

        try:
            with transaction.atomic():
                password = randomPassword()
                pwd = aesencrypt(password)
                user = User.objects.create(
                    name=phone, true_name=name, password=pwd, email=email, phone=phone, head_image_url=head_image_url)
                trainee_coach = TraineeCoach.objects.create(
                    user=user, user_class=user_class, language=language,
                    extra_time=extra_time, working_years=working_years,
                    brief=brief, customer_evaluate=customer_evaluate,
                    style=style, qualification=qualification,
                    industry=industry, domain=domain,
                    coach_auth=coach_auth)
                # center.send_email_to_member.delay(
                #     email=user.email, name=user.name, password=password,
                #     message_type='trainee_coach_password_message')
        except Exception as e:
            return parameter_error_response(str(e))
        serializer = self.get_serializer(trainee_coach)
        return success_response(serializer.data)


class TraineeCoachViewSet(viewsets.ModelViewSet):
    queryset = TraineeCoach.objects.filter(deleted=False).order_by('-created_at')
    serializer_class = TraineeCoachSerializers

    @swagger_auto_schema(
        operation_id='个人教练列表',
        operation_summary='个人教练列表',
        manual_parameters=[
            openapi.Parameter('keyword', openapi.IN_QUERY, description='教练姓名', type=openapi.TYPE_STRING),
            openapi.Parameter('page', openapi.IN_QUERY, description='第几页', type=openapi.TYPE_NUMBER),
            openapi.Parameter('page_size', openapi.IN_QUERY, description='每页显示条数默认15条', type=openapi.TYPE_NUMBER)
        ],
        tags=['个人教练相关']
    )
    def list(self, request, *args, **kwargs):

        trainee_coach = self.get_queryset()
        if request.query_params.get('keyword'):
            trainee_coach = trainee_coach.filter(
                user__true_name__icontains=request.query_params.get('keyword'))

        # 分页
        paginator = StandardResultsSetPagination()
        page_list = paginator.paginate_queryset(trainee_coach, self.request)
        serializer = self.get_serializer(page_list, many=True)
        response = paginator.get_paginated_response(serializer.data)
        return success_response(response)

    @swagger_auto_schema(
        operation_id='个人教练客户列表',
        operation_summary='个人教练客户列表',
        manual_parameters=[
            openapi.Parameter('page', openapi.IN_QUERY, description='第几页', type=openapi.TYPE_NUMBER),
            openapi.Parameter('page_size', openapi.IN_QUERY, description='每页显示条数默认15条', type=openapi.TYPE_NUMBER)
        ],
        tags=['个人教练相关']
    )
    @action(methods=['get'], detail=True, url_path='client')
    def client_list(self, request, *args, **kwargs):

        self.serializer_class = TraineeCoachUserSerializers

        trainee_coach = self.get_object()
        coachee_id_list = TraineeCoachInviteUser.objects.filter(
            trainee_coach=trainee_coach.user).order_by('created_at').values_list(
            'user_id',
            flat=True)
        order = Case(*[When(pk=order, then=pos) for pos, order in enumerate(coachee_id_list)])
        users = User.objects.filter(pk__in=coachee_id_list).order_by(order)
        # 分页
        paginator = StandardResultsSetPagination()
        page_list = paginator.paginate_queryset(users, self.request)
        serializer = self.get_serializer(page_list, many=True, context=trainee_coach.user)
        response = paginator.get_paginated_response(serializer.data)
        return success_response(response)


class TraineeCoachClientViewSet(viewsets.ModelViewSet):
    queryset = TraineeCoach.objects.filter(deleted=False).order_by('-created_at')
    serializer_class = TraineeCoachClientCommentSerializers

    @swagger_auto_schema(
        operation_id='个人教练客户评论',
        operation_summary='个人教练客户评论',
        manual_parameters=[
            openapi.Parameter('client_id', openapi.IN_QUERY, description='客户id', type=openapi.TYPE_NUMBER),
            openapi.Parameter('page', openapi.IN_QUERY, description='第几页', type=openapi.TYPE_NUMBER),
            openapi.Parameter('page_size', openapi.IN_QUERY, description='每页显示条数默认15条', type=openapi.TYPE_NUMBER),
            openapi.Parameter('user_id', openapi.IN_QUERY, description='教练用户id', type=openapi.TYPE_NUMBER),
        ],
        tags=['个人教练相关']
    )
    @action(methods=['get'], detail=False, url_path='client_comment')
    def client_comment_list(self, request, *args, **kwargs):
        try:
            user_id = request.query_params['user_id']
        except:
            return parameter_error_response()

        # 获取评论列表
        if request.query_params.get('client_id'):
            public_attr_uuid_list = PublicAttr.objects.filter(
                user_id=user_id,
                target_user_id=request.query_params.get('client_id'),
            ).order_by('-start_time').values_list(
                'uuid',
                flat=True)
        else:
            public_attr_uuid_list = PublicAttr.objects.filter(
                user_id=user_id).order_by('-start_time').values_list(
                'uuid',
                flat=True)

        # 根据列表id顺序获取interview_id列表
        uuid = Case(*[When(public_attr__uuid=order, then=pos) for pos, order in enumerate(public_attr_uuid_list)])
        project_interview_list = ProjectInterview.objects.filter(
            deleted=False, public_attr__uuid__in=public_attr_uuid_list).exclude(type=4).order_by(uuid).values_list(
            'id',
            flat=True)

        # 根据interview_id顺序获取评论列表
        project_interview = Case(*[When(interview__id=order, then=pos) for pos, order in enumerate(
            project_interview_list)])
        record = CoachAppraise.objects.filter(
            interview__id__in=project_interview_list, deleted=False).order_by(project_interview)
        coach_appraise_record_data = TraineeCoachClientCommentSerializers(record, many=True)

        project_interview_record = ProjectInterviewRecord.objects.filter(
            deleted=False, interview__public_attr__user_id=user_id,
            coach_score__isnull=False,
            interview__public_attr__target_user__isnull=False)
        project_interview_record_data = ProjectInterviewRecordAppraiseSerializer(project_interview_record, many=True)

        data = [*coach_appraise_record_data.data, *project_interview_record_data.data]
        data = sorted(data, key=lambda k: datetime.strptime(k['created_at'], '%Y-%m-%d %H:%M:%S'), reverse=True)

        # 分页
        paginator = StandardResultsSetPagination()
        page_list = paginator.paginate_queryset(data, self.request)
        response = paginator.get_paginated_response(page_list)
        return success_response(response)


class TraineeCoachInterviewViewSet(viewsets.ModelViewSet):
    queryset = TraineeCoach.objects.filter(deleted=False).order_by('-created_at')
    serializer_class = ProjectInterviewListSerializer

    @swagger_auto_schema(
        operation_id='个人教练辅导管理列表',
        operation_summary='个人教练辅导管理列表',
        manual_parameters=[
            openapi.Parameter('page', openapi.IN_QUERY, description='第几页', type=openapi.TYPE_NUMBER),
            openapi.Parameter('page_size', openapi.IN_QUERY, description='每页显示条数默认15条', type=openapi.TYPE_NUMBER),
            openapi.Parameter('status', openapi.IN_QUERY, description='状态 1未完成 2已完成 3异常',
                              type=openapi.TYPE_NUMBER),
            openapi.Parameter('keyword', openapi.IN_QUERY, description='被教练者姓名',
                              type=openapi.TYPE_STRING),
            openapi.Parameter('coachee_id', openapi.IN_QUERY, description='被教练者id',
                              type=openapi.TYPE_STRING)
        ],
        tags=['个人教练相关']
    )
    def retrieve(self, request, *args, **kwargs):
        trainee_coach = self.get_object()
        status = int(request.query_params.get('status', 1))
        keyword = request.query_params.get('keyword')
        coachee_id = request.query_params.get('coachee_id')

        # 根据辅导开始时间排序
        public_attr_uuid_list = PublicAttr.objects.filter(
            user=trainee_coach.user).order_by('-start_time').values_list(
            'uuid',
            flat=True)
        uuid = Case(*[When(pk=order, then=pos) for pos, order in enumerate(public_attr_uuid_list)])
        queryset = ProjectInterview.objects.filter(
            deleted=False, public_attr__user=trainee_coach.user).exclude(type=4).order_by(uuid).order_by(
            '-times',
            '-created_at'
        )
        if keyword:
            queryset = queryset.filter(public_attr__target_user__true_name__icontains=keyword)
        if coachee_id:
            queryset = queryset.filter(public_attr__target_user__id=coachee_id)

        if status == 1:  # 未开始、进行中
            queryset = queryset.filter(
                Q(public_attr__start_time__gt=datetime.now()) |
                Q(public_attr__start_time__lt=datetime.now(),
                  public_attr__end_time__gt=datetime.now())).\
                exclude(public_attr__status=6)
        elif status == 2:  # 已完成
            queryset = queryset.filter(
                public_attr__end_time__lt=datetime.now()).exclude(public_attr__status=6)
        elif status == 3:  # 已取消
            queryset = queryset.filter(public_attr__status=6)
        else:
            return parameter_error_response('辅导状态参数错误')
        # 分页
        paginator = StandardResultsSetPagination()
        page_list = paginator.paginate_queryset(queryset.order_by('-public_attr__start_time'), self.request)
        serializer = self.get_serializer(page_list, many=True)
        response = paginator.get_paginated_response(serializer.data)
        return success_response(response)