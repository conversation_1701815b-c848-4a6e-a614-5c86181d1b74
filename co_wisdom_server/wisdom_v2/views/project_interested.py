from django.db.models import F
from drf_yasg import openapi
from rest_framework import viewsets
from drf_yasg.utils import swagger_auto_schema

from utils.api_response import parameter_error_response, success_response
from utils.pagination import StandardResultsSetPagination
from utils.queryset import multiple_field_distinct
from wisdom_v2.views.project_interested_action import AdminProjectInterestedSerializer
from wisdom_v2.models import ProjectMember, ProjectInterested, Project


class AdminProjectInterestedViewSet(viewsets.ModelViewSet):
    queryset = ProjectInterested.objects.filter(deleted=False).order_by('-created_at')
    serializer_class = AdminProjectInterestedSerializer

    @swagger_auto_schema(
        operation_id='后台利益相关者列表',
        operation_summary='后台利益相关者列表',
        manual_parameters=[
            openapi.Parameter(
                'project_member_id', openapi.IN_QUERY, description='被教练者id', type=openapi.TYPE_NUMBER, required=True),
            openapi.Parameter('project_id', openapi.IN_QUERY, description='项目id', type=openapi.TYPE_NUMBER),
            openapi.Parameter('coach_user_ids', openapi.IN_QUERY, description='教练标识', type=openapi.TYPE_STRING),
            openapi.Parameter('name', openapi.IN_QUERY, description='用户名', type=openapi.TYPE_NUMBER),
            openapi.Parameter('page', openapi.IN_QUERY, description='第几页', type=openapi.TYPE_NUMBER),
            openapi.Parameter('page_size', openapi.IN_QUERY, description='每页显示条数默认15条', type=openapi.TYPE_NUMBER)
        ],
        tags=['后台改变观察反馈相关']
    )
    def list(self, request, *args, **kwargs):
        try:
            project_member_id = request.query_params.get('project_member_id')
            coach_user_ids = request.query_params.get('coach_user_ids')
            project_id = request.query_params.get('project_id')
            if project_id:
                Project.objects.get(pk=project_id, deleted=False)
            name = request.query_params.get('name')
        except Project.DoesNotExist:
            return parameter_error_response('未获取到项目信息')
        project_interested = self.get_queryset()
        if name:
            project_interested = project_interested.filter(interested__true_name__icontains=name)
        if project_member_id:
            project_member_ids = project_member_id.split(',')
            project_member_user = ProjectMember.objects.filter(pk__in=project_member_ids, deleted=False).all()
            if not project_member_user:
                return parameter_error_response('未获取到被教练者信息')
            project_interested = project_interested.filter(
                master_id__in=[item.user_id for item in project_member_user], project_id=project_member_user[0].project_id)
        if coach_user_ids:
            coach_ids = str(coach_user_ids).split(',')
            project_interested = project_interested.filter(
                project__project_user__member=F('master'),
                project__project_user__deleted=False,
                project__project_user__project_group_coach__isnull=True,
                project__project_user__coach__deleted=False,
                project__project_user__coach__user_id__in=coach_ids
            )
        if project_id:
            project_interested = project_interested.filter(project_id=project_id)
        project_interested = multiple_field_distinct(project_interested, ['interested_id'])

        # 分页
        paginator = StandardResultsSetPagination()
        page_list = paginator.paginate_queryset(project_interested, self.request)
        serializer = self.get_serializer(page_list, many=True)
        response = paginator.get_paginated_response(serializer.data)
        return success_response(response)