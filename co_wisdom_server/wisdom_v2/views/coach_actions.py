import datetime
import math
from decimal import Decimal

import redis
import json

from django.conf import settings
from django.db.models import Sum, Avg
from django.db import transaction
from rest_framework import serializers

from utils import task
from utils.messagecenter import getui
from wisdom_v2 import utils
from wisdom_v2.common import interview_public
from wisdom_v2.models import Coach, ProjectCoach, ProjectInterview, ProjectInterviewRecord, Resume, WorkWechatUser, \
    InterviewRecordTemplateAnswer, Project, ProjectMember, CompanyMember, CoachAppraise, ProjectInterviewRecord, User, \
    UserTmp
from utils.queryset import multiple_field_distinct
from wisdom_v2.enum.service_content_enum import InterviewRecordTemplateQuestionTypeEnum, \
    InterviewRecordTemplateQuestionRatingTypeEnum, ProjectCoachStatusEnum
from wisdom_v2.enum.project_interview_enum import ProjectInterviewRecordCompleteEnum
from utils.api_response import WisdomValidationError, WorkWechatUserError
from utils.multiple_selection_map import get_multiple_selection_detail, check_multiple_selection_data
from wisdom_v2.views.constant import ATTR_STATUS_INTERVIEW_CANCEL, INTERVIEW_TYPE_COACHING
from wisdom_v2.enum.project_interview_enum import ProjectInterviewPlaceCategoryEnum
from wisdom_v2.enum.user_enum import CoachUserTypeEnum, UserTmpEnum

third_party_redis = redis.Redis.from_url(settings.THIRD_PARTY_DATA_REDIS)


def get_coach_score(user_id, score_type):
    record = ProjectInterviewRecord.objects.filter(
        deleted=False, interview__public_attr__user_id=user_id,
        interview__coachee_record_status=True
    )
    all_score = record.aggregate(all_score=Sum(score_type))['all_score']
    count = record.count()
    return count, all_score if all_score else 0


def get_coach_score_questionnaire(user_id, score_type):
    answers = InterviewRecordTemplateAnswer.objects.filter(
        question__type=InterviewRecordTemplateQuestionTypeEnum.rating.value,
        question__rating_type=score_type,
        interview__public_attr__user_id=user_id
    )
    count = answers.count()
    score = answers.aggregate(all_score=Sum('score'))['all_score']
    return count, score if score else 0


class CoachSerializers(serializers.ModelSerializer):
    id = serializers.IntegerField(read_only=True)
    user_id = serializers.IntegerField(read_only=True)
    true_name = serializers.CharField(source='user.cover_name', read_only=True)
    email = serializers.CharField(source='user.email', read_only=True)
    is_share_resume = serializers.BooleanField(help_text='是否分享简历', read_only=True)
    project_count = serializers.SerializerMethodField(help_text='参与项目数量')
    interview_record_count = serializers.SerializerMethodField(help_text='辅导记录数量')
    project_member_count = serializers.SerializerMethodField(help_text='被教练客户数量')
    target_progress_score = serializers.SerializerMethodField(help_text='有效度')
    harvest_score = serializers.SerializerMethodField(help_text='投入度')
    satisfaction_score = serializers.SerializerMethodField(help_text='满意度')
    recommend_score = serializers.SerializerMethodField(help_text='推荐度')
    coach_type = serializers.IntegerField(read_only=True, help_text='教练类型')
    customization_resume = serializers.SerializerMethodField(help_text='定制化简历信息')
    created_at = serializers.DateTimeField(format='%Y-%m-%d %H:%M:%S', help_text='创建时间')
    phone = serializers.CharField(source='user.phone', read_only=True)
    birthday = serializers.DateField(format='%Y-%m-%d', source='user.birthday', read_only=True)
    order_receiving_status = serializers.BooleanField(read_only=True, help_text='接单状态')
    school = serializers.CharField(read_only=True, help_text='毕业学校')
    address = serializers.CharField(read_only=True, help_text='收件地址')
    head_image_url = serializers.SerializerMethodField(read_only=True, help_text='证件照')
    share_image_url = serializers.SerializerMethodField(read_only=True, help_text='分享图')
    gender = serializers.IntegerField(source='user.gender', help_text='性别', read_only=True)
    user_class = serializers.CharField(read_only=True, help_text='班次')
    posters_text = serializers.CharField(read_only=True, help_text='海报文本信息')
    city = serializers.CharField(read_only=True, help_text='城市')
    english_name = serializers.CharField(read_only=True, source='user.english_name')
    expected_hourly_salary = serializers.IntegerField(read_only=True, help_text='期望最低时薪')
    work_wechat_user_id = serializers.SerializerMethodField(read_only=True)
    work_wechat_user_name = serializers.SerializerMethodField(read_only=True)
    work_wechat_qr_code_url = serializers.SerializerMethodField(read_only=True)
    domain = serializers.SerializerMethodField(read_only=True)
    highest_degree = serializers.IntegerField(read_only=True)
    resume = serializers.SerializerMethodField(read_only=True, help_text='简历信息')
    one_to_one_interview_time = serializers.SerializerMethodField(read_only=True)
    is_set = serializers.SerializerMethodField(read_only=True, help_text='是否绑定项目')
    is_show = serializers.SerializerMethodField(read_only=True, help_text='是否对用户展示')
    price = serializers.SerializerMethodField(read_only=True, help_text='价格')
    platform_order_receiving_status = serializers.BooleanField(read_only=True, help_text='平台是否允许接单状态')
    personal_name = serializers.CharField(read_only=True, help_text='个人客户可见姓名')
    is_identity_check = serializers.BooleanField(read_only=True, source='user.is_identity_check', help_text='是否实名')


    class Meta:
        model = Coach
        fields = ('id', 'user_id', 'true_name', 'email', 'project_count', 'interview_record_count', 'recommend_score',
                  'project_member_count', 'target_progress_score', 'harvest_score', 'satisfaction_score', 'coach_type',
                  'customization_resume', 'created_at', 'phone', 'birthday', 'order_receiving_status', 'school',
                  'address', 'gender', 'user_class', 'english_name', 'expected_hourly_salary', 'work_wechat_user_id',
                  'head_image_url', 'work_wechat_user_name', 'highest_degree', 'resume', 'domain', 'is_share_resume',
                  'one_to_one_interview_time', 'is_set', 'price', 'city', 'posters_text', 'work_wechat_qr_code_url',
                  'platform_order_receiving_status', 'personal_name', 'is_identity_check', 'is_show', 'share_image_url')

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self._score_cache = {}

    def calculate_record_rating(self, obj):
        # 使用对象的ID作为缓存键
        obj_id = obj.id
        if obj_id not in self._score_cache:
            self._score_cache[obj_id] = interview_public.get_total_interview_scores(coach_user_id=obj.user_id)
        return self._score_cache[obj_id]

    def get_share_image_url(self, obj):
        resume = obj.resumes.filter(is_customization=False, deleted=False).first()
        if resume.share_image_url:
            return resume.share_image_url
        return


    def get_head_image_url(self, obj):
        resume = obj.resumes.filter(is_customization=False, deleted=False).first()
        if resume.head_image_url:
            return resume.head_image_url
        return obj.user.head_image_url

    def get_price(self, obj):
        return obj.display_price

    def get_work_wechat_user_name(self, obj):
        wx_user = WorkWechatUser.objects.filter(user_id=obj.user.id, deleted=False).first()
        if wx_user:
            state, data = utils.get_work_wechat_department_users(include=[settings.WORK_WECHAT_COACH_DEPARTMENT_ID])
            if not state:
                return
            for v in data:
                if v['work_wechat_user_id'] == wx_user.wx_user_id:
                    return v['name']

    def get_work_wechat_user_id(self, obj):
        wx_user = WorkWechatUser.objects.filter(user_id=obj.user.id, deleted=False).first()
        if wx_user:
            return wx_user.wx_user_id
        return

    def get_work_wechat_qr_code_url(self, obj):
        wx_user = WorkWechatUser.objects.filter(user_id=obj.user.id, deleted=False).first()
        if wx_user:
            return wx_user.qr_code
        return

    def get_resume(self, obj):
        resume = Resume.objects.filter(coach_id=obj.pk, deleted=False, is_customization=False).first()
        data = {}
        data['language'] = get_multiple_selection_detail('language', resume.coach_language)
        data['coach_auth'] = resume.coach_auth if resume.coach_auth else None
        data['working_years'] = resume.working_years if resume.working_years else None
        coach_customer_level = resume.coach_customer_level
        data['coach_customer_level'] = coach_customer_level[0] if coach_customer_level else None
        data['resume_id'] = resume.pk
        return data

    def get_customization_resume(self, obj):
        resumes = Resume.objects.filter(coach_id=obj.pk, deleted=False, is_customization=True).order_by('created_at')

        project_id = self.context.get('project_id')  # 筛选数据
        bind_project_id = self.context.get('bind_project_id')  # 项目匹配
        user_id = self.context.get('user_id')  # 是否允许修改判断
        show_resume_id = 0

        if project_id:
            project_coach = ProjectCoach.objects.filter(
                deleted=False,
                coach_id=obj.pk,
                project_id=project_id,
                member__isnull=True,
                project_group_coach__isnull=True
            ).first()

            if project_coach and project_coach.resume:
                resume_ids = project_coach.resume
                show_resume_id = project_coach.show_resume_id
            else:
                resume_ids = []
                show_resume_id = 0

            resumes = resumes.filter(id__in=resume_ids)
        if resumes.count() > 0:
            data = []
            for resume in resumes:
                is_set = False
                if bind_project_id:
                    project_coach = ProjectCoach.objects.filter(
                        coach=obj, deleted=False, project_id=bind_project_id,
                        member__isnull=True, project_group_coach__isnull=True).first()
                    if project_coach and resume.id in project_coach.resume:
                        is_set = True
                data.append({
                    "id": resume.pk,
                    "name": resume.name,
                    "created_at": resume.created_at.strftime("%Y-%m-%d %H:%M:%S"),
                    "creator": resume.creator.true_name,
                    "is_set": is_set,
                    "is_can_edit": True if user_id == resume.creator.id else False,
                    "is_show": True if resume.id == show_resume_id else False,
                })
            return data

    def get_recommend_score(self, obj):
        sum_score = CoachAppraise.objects.filter(
            interview__public_attr__target_user__isnull=False,
            interview__public_attr__user=obj.user, deleted=False
        ).aggregate(score_sum=Sum('score'))['score_sum']

        sum_score = round(sum_score, 1) if sum_score else 0

        q_count = CoachAppraise.objects.filter(
            # interview__public_attr__target_user__unionid__isnull=False,
            interview__public_attr__user=obj.user, deleted=False
        ).count()
        a_count = ProjectInterviewRecord.objects.filter(interview__public_attr__user=obj.user,
                                                        deleted=False, coach_score__isnull=False,
                                                        interview__public_attr__target_user__isnull=False).count()
        a_sum_score = ProjectInterviewRecord.objects.filter(interview__public_attr__user=obj.user,
                                                            deleted=False, coach_score__isnull=False,
                                                            interview__public_attr__target_user__isnull=False).aggregate(
            sum_score=Sum('coach_score'))['sum_score']

        a_sum_score = round(a_sum_score, 1) if a_sum_score else 0

        sum = sum_score + a_sum_score
        count = a_count + q_count
        if sum:
            return round(sum / count, 1)
        else:
            return 0

    def get_project_count(self, obj):
        count = ProjectCoach.objects.filter(coach_id=obj.id, member__isnull=True, deleted=False).values('project_id').distinct().count()
        return count

    def get_interview_record_count(self, obj):
        return obj.user.get_all_interview_count(coach_user_id=obj.user.id, place_category=ProjectInterviewPlaceCategoryEnum.online_one_to_one)

    def get_project_member_count(self, obj):
        project_interview = ProjectInterview.objects.filter(
            type=INTERVIEW_TYPE_COACHING,
            public_attr__user_id=obj.user_id, place_category=1,
            public_attr__project__isnull=False, deleted=False)
        queryset = multiple_field_distinct(project_interview, ['public_attr.project_id', 'public_attr.target_user_id'])
        return queryset.count()

    def get_target_progress_score(self, obj):
        score_data = self.calculate_record_rating(obj)
        return score_data.get('target_progress_avg_score')

    def get_harvest_score(self, obj):
        score_data = self.calculate_record_rating(obj)
        return score_data.get('harvest_avg_score')

    def get_satisfaction_score(self, obj):
        score_data = self.calculate_record_rating(obj)
        return score_data.get('satisfaction_avg_score')

    def get_domain(self, obj):
        resume = Resume.objects.filter(coach=obj, deleted=False, is_customization=False).first()
        if resume:
            if resume.coach_domain:
                domain = resume.coach_domain
                return domain[:3]
        return []

    def get_is_set(self, obj):
        resumes = Resume.objects.filter(coach_id=obj.pk, deleted=False, is_customization=False).first()
        bind_project_id = self.context.get('bind_project_id')
        if bind_project_id:
            project_coach = ProjectCoach.objects.filter(
                coach=obj,
                deleted=False,
                status=ProjectCoachStatusEnum.adopt,
                project_id=bind_project_id,
                member__isnull=True,
                project_group_coach__isnull=True
            ).first()
            if project_coach and resumes.id in project_coach.resume:
                return True
        return False

    def get_is_show(self, obj):
        resume = obj.resumes.filter(is_customization=False, deleted=False).first()

        project_id = self.context.get('project_id')  # 筛选数据
        project_coach = ProjectCoach.objects.filter(
            deleted=False,
            coach_id=obj.pk,
            project_id=project_id,
            member__isnull=True,
            project_group_coach__isnull=True
        ).first()
        return True if project_coach and resume.id == project_coach.show_resume_id else False


    def get_one_to_one_interview_time(self, obj):
        times = ProjectInterview.objects.filter(
            place_category=ProjectInterviewPlaceCategoryEnum.online_one_to_one.value,
            type=INTERVIEW_TYPE_COACHING,
            public_attr__type=1, deleted=False,
            public_attr__end_time__lt=datetime.datetime.now(),
            public_attr__user_id=obj.user_id).exclude(
            public_attr__status=ATTR_STATUS_INTERVIEW_CANCEL).aggregate(times_minute=Sum('times'))
        if times['times_minute']:
            interview_hour = round(times['times_minute'] / 60, 1)
        else:
            interview_hour = 0
        resume = Resume.objects.filter(coach=obj, deleted=False, is_customization=False).first()

        one_to_one_interview_time = resume.one_to_one_interview_time if resume.one_to_one_interview_time else 0
        return interview_hour + one_to_one_interview_time


class CoachProjectSerializer(serializers.ModelSerializer):
    id = serializers.IntegerField(read_only=True)
    name = serializers.CharField(source='full_name', read_only=True, help_text='项目名称')
    type = serializers.IntegerField(read_only=True, help_text='项目类型')
    status = serializers.IntegerField(read_only=True, help_text='状态')
    project_progress = serializers.CharField(read_only=True, help_text='项目进展')
    project_member_count = serializers.SerializerMethodField(read_only=True, help_text='被教练者数量')
    project_cycle = serializers.SerializerMethodField(read_only=True, help_text='项目周期')
    remain_time = serializers.SerializerMethodField(read_only=True, help_text='项目剩余时长')
    interview_count = serializers.SerializerMethodField(read_only=True, help_text='辅导记录数量')

    class Meta:
        model = Project
        fields = ('id', 'name', 'type', 'status', 'project_progress', 'project_member_count', 'project_cycle',
                  'remain_time', 'interview_count')

    def get_project_cycle(self, obj):

        if obj.start_time and obj.end_time:
            return datetime.datetime.strftime(obj.start_time, '%Y-%m-%d') + ' - ' + datetime.datetime.strftime(obj.end_time, '%Y-%m-%d')
        return '-'


    def get_remain_time(self, obj):
        if obj.end_time:

            if datetime.datetime.now().date() < obj.end_time:
                remain_time = obj.end_time - datetime.datetime.now().date()
                return remain_time.days
            return 0
        else:
            return 0

    def get_interview_count(self, obj):
        user_id = self.context.get('user_id')
        interview = ProjectInterview.objects.filter(
            public_attr__project=obj, deleted=False,
            type=INTERVIEW_TYPE_COACHING,
            place_category=ProjectInterviewPlaceCategoryEnum.online_one_to_one,
            public_attr__user_id=user_id).exclude(public_attr__status=ATTR_STATUS_INTERVIEW_CANCEL)
        return interview.count()

    def get_project_member_count(self, obj):
        user_id = self.context.get('user_id')
        project_interview = ProjectInterview.objects.filter(
            type=INTERVIEW_TYPE_COACHING,
            public_attr__user_id=user_id, place_category=ProjectInterviewPlaceCategoryEnum.online_one_to_one,
            public_attr__project=obj, deleted=False).exclude(public_attr__status=ATTR_STATUS_INTERVIEW_CANCEL)
        queryset = multiple_field_distinct(project_interview, ['public_attr.project_id', 'public_attr.target_user_id'])
        return queryset.count()


class InterviewRecordSerializer(serializers.ModelSerializer):
    id = serializers.IntegerField(read_only=True)
    project_member_name = serializers.CharField(source='public_attr.target_user.name', help_text='被教练者姓名',
                                                read_only=True)
    project_name = serializers.CharField(source='public_attr.project.name', help_text='项目名称', read_only=True)
    project_id = serializers.IntegerField(source='public_attr.project_id', help_text='项目id', read_only=True)
    time = serializers.SerializerMethodField(help_text='辅导时长', read_only=True)
    place_category = serializers.IntegerField(help_text='辅导类型', read_only=True)
    interview_date = serializers.SerializerMethodField(help_text='辅导时间', read_only=True)
    interview_number = serializers.SerializerMethodField(help_text='辅导次数', read_only=True)
    interview_record_status = serializers.SerializerMethodField(help_text='辅导记录状态', read_only=True)
    record_type = serializers.IntegerField(help_text='辅导记录类型', read_only=True)
    close_reason = serializers.CharField(help_text='取消原因', read_only=True)
    company_member_id = serializers.SerializerMethodField(help_text='企业用户id', read_only=True)
    topic = serializers.CharField(help_text='辅导议题', read_only=True)


    class Meta:
        model = ProjectInterview
        fields = ('id', 'project_member_name', 'project_name', 'project_id', 'time', 'place_category', 'interview_date',
                  'interview_number', 'interview_record_status', 'record_type', 'close_reason', 'company_member_id',
                  'topic')

    def get_time(self, obj):
        return round(int(obj.times) / 60, 1)

    def get_interview_date(self, obj):
        start_time = datetime.datetime.strftime(obj.public_attr.start_time, '%Y-%m-%d %H:%M')
        end_time = datetime.datetime.strftime(obj.public_attr.end_time, '%Y-%m-%d %H:%M')
        return start_time + '-' + end_time[-5:]

    def get_interview_number(self, obj):
        # 按辅导时间排序
        project_interviews = ProjectInterview.objects.filter(type=obj.type, place_category=obj.place_category,
                                                             public_attr__target_user=obj.public_attr.target_user,
                                                             public_attr__type=obj.public_attr.type,
                                                             public_attr__project=obj.public_attr.project,
                                                             deleted=False
                                                             ).exclude(public_attr__status=6). \
            order_by('public_attr__start_time')
        count = 0
        id_lst = []
        for project_interview in project_interviews:
            id_lst.append(project_interview.id)
            count += 1
            if project_interview.id == obj.id:
                break
        if obj.id not in id_lst:
            return None
        return str(count)

    def get_interview_record_status(self, obj):
        if not obj.coach_record_status and not obj.coachee_record_status:
            return ProjectInterviewRecordCompleteEnum.all_unfinished.value
        elif not obj.coach_record_status and obj.coachee_record_status:
            return ProjectInterviewRecordCompleteEnum.coach_unfinished.value
        elif obj.coach_record_status and not obj.coachee_record_status:
            return ProjectInterviewRecordCompleteEnum.member_unfinished.value
        elif obj.coach_record_status and obj.coachee_record_status:
            return ProjectInterviewRecordCompleteEnum.finished.value
        return 0

    def get_company_member_id(self, obj):
        member = CompanyMember.objects.filter(company=obj.public_attr.project.company,
                                              user=obj.public_attr.target_user).first()
        return member.pk


class CoachProjectMemberSerializer(serializers.ModelSerializer):
    id = serializers.IntegerField(read_only=True)
    name = serializers.CharField(source='user.name', read_only=True)
    true_name = serializers.CharField(source='user.cover_name', read_only=True)
    phone = serializers.CharField(source='user.phone', read_only=True)
    email = serializers.CharField(source='user.email', read_only=True)
    project_name = serializers.CharField(source='project.name', read_only=True)
    project_id = serializers.IntegerField(read_only=True)
    online_progress = serializers.CharField(read_only=True, help_text='线上进展')
    offline_progress = serializers.CharField(read_only=True, help_text='线下进展')
    company_member_id = serializers.IntegerField(read_only=True, help_text='企业用户id')
    position = serializers.SerializerMethodField(read_only=True, help_text='职位')
    recommend_score = serializers.SerializerMethodField(read_only=True, help_text='推荐度')
    customer_evaluation = serializers.SerializerMethodField(read_only=True, help_text='客户评价')
    customer_evaluation_time = serializers.SerializerMethodField(read_only=True, help_text='客户评价时间')

    class Meta:
        model = ProjectMember
        fields = ('id', 'true_name', 'phone', 'email', 'project_name', 'project_id', 'online_progress',
                  'offline_progress', 'company_member_id', 'name', 'position', 'recommend_score',
                  'customer_evaluation', 'customer_evaluation_time')

    def get_position(self, obj):
        company_member = CompanyMember.objects.filter(company=obj.project.company, user=obj.user).first()
        return company_member.position

    def get_recommend_score(self, obj):
        user_id = self.context.get('user_id')
        coach_appraise = CoachAppraise.objects.filter(
            interview__public_attr__target_user=obj.user,
            interview__public_attr__user_id=user_id, deleted=False
        )
        sum_score = coach_appraise.aggregate(score_sum=Sum('score'))['score_sum']
        sum_score = round(sum_score, 1) if sum_score else 0

        q_count = coach_appraise.count()

        interview_record = ProjectInterviewRecord.objects.filter(interview__public_attr__user_id=user_id,
                                                                 deleted=False, coach_score__isnull=False,
                                                                 interview__public_attr__target_user=obj.user)
        a_count = interview_record.count()
        a_sum_score = interview_record.aggregate(sum_score=Sum('coach_score'))['sum_score']
        a_sum_score = round(a_sum_score, 1) if a_sum_score else 0
        sum = sum_score + a_sum_score
        count = a_count + q_count
        if sum:
            return round(sum / count, 1)
        else:
            return 0

    def get_customer_evaluation(self, obj):
        user_id = self.context.get('user_id')
        coach_appraise = CoachAppraise.objects.filter(
            interview__public_attr__target_user=obj.user,
            interview__public_attr__user_id=user_id, deleted=False
        ).order_by('-created_at').first()
        if coach_appraise:
            return coach_appraise.appraise

        interview_record = ProjectInterviewRecord.objects.filter(
            interview__public_attr__user_id=user_id,
            deleted=False, coach_score__isnull=False,
            interview__public_attr__target_user=obj.user).order_by('-created_at').first()
        if interview_record:
            return interview_record.coach_comment

    def get_customer_evaluation_time(self, obj):
        user_id = self.context.get('user_id')
        coach_appraise = CoachAppraise.objects.filter(
            interview__public_attr__target_user=obj.user,
            interview__public_attr__user_id=user_id, deleted=False
        ).order_by('-created_at').first()
        if coach_appraise:
            return coach_appraise.created_at.strftime('%Y-%m-%d')

        interview_record = ProjectInterviewRecord.objects.filter(
            interview__public_attr__user_id=user_id,
            deleted=False, coach_score__isnull=False,
            interview__public_attr__target_user=obj.user).order_by('-created_at').first()
        if interview_record:
            return interview_record.created_at.strftime('%Y-%m-%d')


class CoachEditSerializer(serializers.Serializer):
    id = serializers.IntegerField(required=True, write_only=True)
    true_name = serializers.CharField(required=False, write_only=True, allow_null=True, allow_blank=True)
    personal_name = serializers.CharField(required=False, write_only=True, allow_null=True, allow_blank=True)
    user_id = serializers.IntegerField(required=False, write_only=True, allow_null=True)
    english_name = serializers.CharField(required=False, write_only=True, allow_null=True, allow_blank=True)
    birthday = serializers.CharField(required=False, write_only=True, allow_null=True, allow_blank=True)
    email = serializers.CharField(required=False, write_only=True)
    phone = serializers.CharField(required=False, write_only=True)
    posters_text = serializers.CharField(required=False, write_only=True, allow_null=True, allow_blank=True)
    price = serializers.FloatField(required=False, write_only=True, allow_null=True)
    city = serializers.CharField(required=False, write_only=True, allow_null=True, allow_blank=True)
    is_share_resume = serializers.BooleanField(required=False, write_only=True, allow_null=True)
    gender = serializers.IntegerField(required=False, write_only=True, allow_null=True)
    highest_degree = serializers.IntegerField(required=False, write_only=True, help_text='最高学历 1-专科 2-本科 3-硕士 '
                                                                                         '4-博士', allow_null=True)
    school = serializers.CharField(required=False, write_only=True, help_text='毕业学校', allow_null=True, allow_blank=True)
    order_receiving_status = serializers.BooleanField(required=False, write_only=True, help_text='接单状态', allow_null=True)
    coach_type = serializers.IntegerField(required=False, write_only=True, help_text='教练类型')
    expected_hourly_salary = serializers.IntegerField(required=False, write_only=True, help_text='期望最低时薪',
                                                      allow_null=True)
    language = serializers.ListField(required=False, write_only=True, help_text='语言', allow_null=True)
    user_class = serializers.CharField(required=False, write_only=True, help_text='班次', allow_null=True, allow_blank=True)
    address = serializers.CharField(required=False, write_only=True, help_text='收件地址', allow_null=True, allow_blank=True)
    head_image_url = serializers.CharField(required=False, write_only=True, help_text='证件照', allow_null=True, allow_blank=True)
    is_add_work_wechat_user = serializers.BooleanField(required=False, write_only=True, help_text='是否是新建企业微信账号')
    work_wechat_user_id = serializers.CharField(required=False, write_only=True, help_text='企业微信账号', allow_null=True, allow_blank=True)
    platform_order_receiving_status = serializers.BooleanField(required=False, write_only=True,
                                                               help_text='平台是否允许接单状态', allow_null=True)


    def update_coach(self, validated_data):
        coach = Coach.objects.get(pk=validated_data.pop('id'))
        user = coach.user
        resume = Resume.objects.filter(is_customization=False, coach=coach).first()
        if validated_data.get('email'):
            if ' ' in validated_data.get('email'):
                raise WisdomValidationError('邮箱中存在空格，请修改后再添加。')
            if validated_data['email'] != user.email and User.objects.filter(email=validated_data['email'],
                                                                             deleted=False).exists():
                raise WisdomValidationError('当前邮箱已存在')
        if validated_data.get('phone'):
            if validated_data['phone'] != user.phone and User.objects.filter(phone=validated_data['phone'],
                                                                             deleted=False).exists():
                raise WisdomValidationError('当前手机号已存在')
        # validated_data.get('price')是数字时，转换为分
        if validated_data.get('price'):
            validated_data['price'] = int(Decimal(str(validated_data['price'])) * Decimal('100').quantize(Decimal('0')))

        # 查询后台账户对应企业微信信息
        coach_type = validated_data.get('coach_type')
        is_add_work_wechat_user = validated_data.get('is_add_work_wechat_user')
        if 'is_add_work_wechat_user' in validated_data.keys():
            validated_data.pop('is_add_work_wechat_user')
        work_wechat_user_id = validated_data.get('work_wechat_user_id')
        if 'work_wechat_user_id' in validated_data.keys():
            validated_data.pop('work_wechat_user_id')

        if work_wechat_user_id:
            wx_user = WorkWechatUser.objects.filter(
                user__isnull=False,
                wx_user_id=work_wechat_user_id, deleted=False).first()
            if wx_user and wx_user.user.id != user.id:
                raise WorkWechatUserError({'is_bind': True, 'user_name': wx_user.user.cover_name})
        if is_add_work_wechat_user:
            wx_user = WorkWechatUser.objects.filter(
                user__isnull=False,
                wx_user_id=user.phone, deleted=False).first()
            if wx_user:
                raise WisdomValidationError('手机号对应的企业微信账号已存在')

        is_platform_order_receiving_notice = False
        if validated_data.get('platform_order_receiving_status') and not coach.platform_order_receiving_status:
            is_platform_order_receiving_notice = True

        with transaction.atomic():

            tmp_validated_data = update_resume(validated_data, resume)
            validated_data = update_resume_user(tmp_validated_data, user)
            Coach.objects.filter(pk=coach.pk).update(**validated_data)
            if is_add_work_wechat_user:
                state = utils.add_work_wechat_user(user, settings.WORK_WECHAT_COACH_DEPARTMENT_ID)
                if state:
                    raise WorkWechatUserError(state)
            else:
                utils.update_work_wechat_user(work_wechat_user_id, user)
            if is_platform_order_receiving_notice:
                wx_user = WorkWechatUser.objects.filter(user=user, deleted=False).first()
                if wx_user:
                    getui.send_work_wechat_coach_notice.delay(
                        wx_user.wx_user_id, 'platform_order_receiving', coach_id=coach.id, coach_name=coach.user.name)
            UserTmp.objects.filter(type__in=[UserTmpEnum.resume.value, UserTmpEnum.entrant.value], data_id=resume.pk).delete()

        # 更新教练标签
        task.update_coach_tag.delay(resume.coach_id)
        data = CoachSerializers(coach).data
        return data

    def validate(self, attrs):
        if 'language' in attrs.keys():
            result = check_multiple_selection_data('language', attrs.get('language'))
            if result == 'error':
                raise WisdomValidationError('语言参数错误')
        return attrs


def update_resume_user(validated_data, user):
    if 'true_name' in validated_data.keys():
        user.true_name = validated_data['true_name']
        validated_data.pop('true_name')
    if 'birthday' in validated_data.keys():
        user.birthday = validated_data['birthday']
        validated_data.pop('birthday')
    if 'email' in validated_data.keys():
        user.email = validated_data['email']
        validated_data.pop('email')
    if 'phone' in validated_data.keys():
        user.phone = validated_data['phone']
        validated_data.pop('phone')
    if 'gender' in validated_data.keys():
        user.gender = validated_data['gender']
        validated_data.pop('gender')
    if 'english_name' in validated_data.keys():
        user.english_name = validated_data['english_name']
        validated_data.pop('english_name')
    if 'user_id' in validated_data.keys():
        validated_data.pop('user_id')
    user.save()

    return validated_data


def update_resume(validated_data, resume):
    if 'language' in validated_data.keys():
        resume.coach_language = validated_data['language']
        validated_data.pop('language')
    if 'english_name' in validated_data.keys():
        resume.english_name = validated_data['english_name']
    if 'head_image_url' in validated_data.keys():
        resume.head_image_url = validated_data['head_image_url']
        task.update_coach_resume_share_url.delay(resume.pk, validated_data['head_image_url'])  # 更新小程序教练简历分享的头像
        validated_data.pop('head_image_url')
    resume.save()
    return validated_data
