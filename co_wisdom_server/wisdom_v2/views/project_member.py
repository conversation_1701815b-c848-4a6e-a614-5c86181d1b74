import logging
import tempfile

from rest_framework import viewsets
from drf_yasg import openapi
from rest_framework.decorators import action
from django.db import transaction
from django.db.models import F

from drf_yasg.utils import swagger_auto_schema

from wisdom_v2.common import project_member_public, project_service_public, interview_public
from wisdom_v2.common.project_member_public import add_project_member
from wisdom_v2.models import ProjectMember, Project, CompanyMember, User, \
    CoachTask, EvaluationModule, ProjectInterested, MultipleAssociationRelation, CustomerPortrait, ProjectCoach
from wisdom_v2.models_file.project_service import ProjectServiceMember
from wisdom_v2.views.customer_portrait_actions import CustomerPortraitBaseSerializers
from wisdom_v2.views.project_member_action import ProjectMemberListSerializer, \
    ProjectMemberCreateSerializer, member_data_clean, remove_user_data, \
    RelevanceCompanyMemberListSerializer, remove_none_data, member_data_clean_reduce
from wisdom_v2.enum.service_content_enum import Coach<PERSON><PERSON><PERSON><PERSON>, EvaluationWriteRoleEnum, \
    MultipleAssociationRelationTypeEnum, NewCoachTaskTypeEnum
from utils.pagination import StandardResultsSetPagination
from utils.api_response import success_response, parameter_error_response, WisdomValidationError
from wisdom_v2.enum.project_member_enum import ProjectMemberRoleEnum, StakeHolderRelationEnum
from utils import aesencrypt, randomPassword
from utils.messagecenter.center import send_evaluation_message
from wisdom_v2.models_file import StakeholderInterview, ProjectServiceStage, ProjectMemberServiceContent, ServiceStage, \
    ProjectTagGroup

api_action_logging = logging.getLogger('api_action')


class ProjectMemberViewSet(viewsets.ModelViewSet):
    queryset = ProjectMember.objects.filter(deleted=False).order_by('-created_at')
    serializer_class = ProjectMemberListSerializer
    authentication_classes = []

    @swagger_auto_schema(
        operation_id='项目成员列表',
        operation_summary='项目成员列表',
        manual_parameters=[
            openapi.Parameter('page', openapi.IN_QUERY, description='第几页', type=openapi.TYPE_NUMBER),
            openapi.Parameter('page_size', openapi.IN_QUERY, description='每页显示条数默认15条', type=openapi.TYPE_NUMBER),
            openapi.Parameter('true_name', openapi.IN_QUERY, description='被教练者姓名', type=openapi.TYPE_STRING),
            openapi.Parameter('coach_user_ids', openapi.IN_QUERY, description='教练id', type=openapi.TYPE_STRING),
            openapi.Parameter('project_id', openapi.IN_QUERY, description='项目id', type=openapi.TYPE_NUMBER),
            openapi.Parameter('is_service', openapi.IN_QUERY, description='是否配置服务内容, 0-否，1-是', type=openapi.TYPE_NUMBER)
        ],
        tags=['项目成员相关']
    )
    def list(self, request, *args, **kwargs):
        try:
            project_id = request.query_params.get('project_id', 0)
            true_name = request.query_params.get('true_name', 0)
            is_service = request.query_params.get('is_service', 1)
            coach_user_ids = request.query_params.get('coach_user_ids')
        except Exception as e:
            return parameter_error_response()
        # self.serializer_class = ProjectMemberListSerializer
        project_member = self.get_queryset()
        project_member = project_member.exclude(role=ProjectMemberRoleEnum.coach.value)
        if project_id:
            if type(project_id) != int:
                if not project_id.isdigit():
                    return parameter_error_response('项目id参数类型错误')
                project_id = int(project_id)
            project_member = project_member.filter(project_id=project_id)
        if true_name:
            project_member = project_member.filter(user__true_name__icontains=true_name)
        if is_service == 0 or is_service == '0':
            project_member = project_member.filter(project_bundle__isnull=True)

        if coach_user_ids:
            coach_user_ids = str(coach_user_ids).split(',')
            project_member = project_member.filter(
                project__project_user__member=F('user'),
                project__project_user__coach__deleted=False,
                project__project_user__coach__user_id__in=coach_user_ids,
                project__project_user__deleted=False,
                project__project_user__project_group_coach__isnull=True,
            )

        # 分页
        paginator = StandardResultsSetPagination()
        page_list = paginator.paginate_queryset(project_member.order_by('-created_at'), self.request)
        serializer = self.get_serializer(page_list, many=True)
        response = paginator.get_paginated_response(serializer.data)
        return success_response(response)

    @swagger_auto_schema(
        operation_id='创建项目成员',
        operation_summary='创建项目成员',
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'project_id': openapi.Schema(type=openapi.TYPE_NUMBER, description='项目id(必传)'),
                'name': openapi.Schema(type=openapi.TYPE_STRING, description='用户名(必传)'),
                'true_name': openapi.Schema(type=openapi.TYPE_STRING, description='用户姓名'),
                'manage_role': openapi.Schema(type=openapi.TYPE_NUMBER, description='管理角色'),
                'employee': openapi.Schema(type=openapi.TYPE_NUMBER, description='直系下属人数'),
                'position': openapi.Schema(type=openapi.TYPE_STRING, description='职位'),
                'department': openapi.Schema(type=openapi.TYPE_STRING, description='部门'),
                'remark': openapi.Schema(type=openapi.TYPE_STRING, description='管理员备注'),
                'phone': openapi.Schema(type=openapi.TYPE_STRING, description='用户手机号'),
                'email': openapi.Schema(type=openapi.TYPE_STRING, description='用户邮箱'),
                'id_number': openapi.Schema(type=openapi.TYPE_STRING, description='用户身份证号'),
                'job_number': openapi.Schema(type=openapi.TYPE_STRING, description='用户工号'),
                'work_year': openapi.Schema(type=openapi.TYPE_NUMBER, description='工作年限'),
                'job_year': openapi.Schema(type=openapi.TYPE_NUMBER, description='本岗年限'),
                'company_year': openapi.Schema(type=openapi.TYPE_NUMBER, description='公司年限'),
                'area_code': openapi.Schema(type=openapi.TYPE_STRING, description='区号'),
                'company_member_id': openapi.Schema(type=openapi.TYPE_NUMBER, description='企业用户id(关联被教练者时传入)'),
            }
        ),
        tags=['项目成员相关']
    )
    def create(self, request, *args, **kwargs):
        self.serializer_class = ProjectMemberCreateSerializer
        data = request.data.copy()
        data = remove_none_data(data)
        request._full_data = data
        return success_response(super().create(request, *args, **kwargs).data)

    @swagger_auto_schema(
        operation_id='编辑项目成员（归档/恢复）',
        operation_summary='编辑项目成员（归档/恢复）',
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'is_forbidden': openapi.Schema(type=openapi.TYPE_NUMBER, description='归档/恢复，归档1，恢复0'),
            }
        ),
        tags=['项目成员相关']
    )
    def update(self, request, *args, **kwargs):
        data = request.data.copy()
        data = remove_none_data(data)
        request._full_data = data
        return success_response(super().update(request, *args, **kwargs).data)

    @swagger_auto_schema(
        operation_id='批量添加项目成员',
        operation_summary='批量添加项目成员',
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'project_id': openapi.Schema(type=openapi.TYPE_NUMBER, description='项目id'),
                'file': openapi.Schema(type=openapi.TYPE_FILE, description='批量添加项目成员excel'),
            }
        ),
        tags=['项目成员相关']
    )
    @action(methods=['post'], detail=False, url_path='bulk_import_project_member')
    def bulk_import_project_member(self, request, *args, **kwargs):
        file = request.FILES.get('file')
        project_id = request.data.get('project_id')
        if not project_id:
            return parameter_error_response('缺少项目id')
        if not file:
            return parameter_error_response('请上传文件')

        project = Project.objects.filter(id=project_id, deleted=False).first()
        if not project:
            return parameter_error_response(err='当前项目不存在')
        file_name = file.name
        file_type = file_name.split('.')[-1]
        if file_type not in ('xls', 'xlsx'):
            return parameter_error_response(err='模版格式错误,请上传xls，xlsx格式文件')
        max_size = 1048576
        if file.size > max_size:
            return parameter_error_response(err='模版文件大于1MB请重新上传')
        temp_dir = tempfile.mkdtemp()
        path = temp_dir + '/' + file_name
        with open(path, 'ab') as f:
            for chunk in file.chunks():
                f.write(chunk)
        data_lst, err_lst, header_validation = member_data_clean(file_type, path, project_id=project_id)
        if not header_validation:
            return parameter_error_response('模版错误', {"is_template": 1})
        if err_lst:
            return parameter_error_response(err=err_lst)
        if data_lst:
            with transaction.atomic():
                for validated_data in data_lst:
                    validated_data.pop('project_id')
                    user_dict = {
                        'name': validated_data.get('name') if validated_data.get('name') else validated_data.get('email'),
                        'area_code': validated_data.get('area_code')}
                    if 'true_name' in validated_data.keys():
                        user_dict['true_name'] = validated_data.get('true_name')
                    if 'phone' in validated_data.keys():
                        user_dict['phone'] = validated_data.get('phone')
                    if 'email' in validated_data.keys():
                        user_dict['email'] = validated_data.get('email')
                    password = randomPassword()
                    pwd = aesencrypt(password) if 'email' in user_dict.keys() else aesencrypt('123456')
                    user_dict['password'] = pwd
                    user_dict['true_name'] = \
                        validated_data.get('true_name') or "用户{}".format(randomPassword(length=4))

                    user_instance = User.objects.create(**user_dict)
                    validated_data = remove_user_data(validated_data)
                    validated_data['user_id'] = user_instance.id
                    validated_data['company_id'] = project.company_id
                    CompanyMember.objects.create(**validated_data)
                    ProjectMember.objects.create(project_id=project_id, user_id=user_instance.id,
                                                 role=ProjectMemberRoleEnum.coachee.value)
        return success_response()


    @swagger_auto_schema(
        operation_id='企业管理员批量添加项目成员',
        operation_summary='企业管理员批量添加项目成员',
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'project_id': openapi.Schema(type=openapi.TYPE_NUMBER, description='项目id'),
                'file': openapi.Schema(type=openapi.TYPE_FILE, description='批量添加项目成员excel'),
            }
        ),
        tags=['项目成员相关']
    )
    @action(methods=['post'], detail=False, url_path='bulk_import_project_member/reduce')
    def bulk_import_project_member_reduce(self, request, *args, **kwargs):
        file = request.FILES.get('file')
        project_id = request.data.get('project_id')
        if not project_id:
            return parameter_error_response('缺少项目id')
        if not file:
            return parameter_error_response('请上传文件')

        project = Project.objects.filter(id=project_id, deleted=False).first()
        if not project:
            return parameter_error_response(err='当前项目不存在')
        file_name = file.name
        file_type = file_name.split('.')[-1]
        if file_type != 'xlsx':
            return parameter_error_response(err='模版格式错误,请上传xlsx格式文件')
        max_size = 1048576
        if file.size > max_size:
            return parameter_error_response(err='模版文件大于1MB请重新上传')

        state, data, error = member_data_clean_reduce(file)
        if not state:
            return parameter_error_response(f'模版错误:{error}', {"is_template": 1})
        elif error:
            return parameter_error_response(err=error)

        with transaction.atomic():
            for item in data:
                add_project_member(item, project)
        return success_response()


    @swagger_auto_schema(
        operation_id='关联添加服务内容',
        operation_summary='关联添加服务内容',
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'project_member_id': openapi.Schema(type=openapi.TYPE_NUMBER, description='项目成员id'),
                'target_project_member_id': openapi.Schema(type=openapi.TYPE_ARRAY,
                                                           items=openapi.Schema(type=openapi.TYPE_OBJECT),
                                                           description='目标关联用户id列表'),
            }
        ),
        tags=['项目成员相关']
    )
    @action(methods=['post'], detail=False, url_path='relevance_service_content')
    def relevance_service_content(self, request, *args, **kwargs):
        project_member_id = request.data.get('project_member_id', None)
        target_project_member_id = request.data.get('target_project_member_id', None)
        if not project_member_id:
            return parameter_error_response('请传入被复制服务内容用户id')
        if not target_project_member_id:
            return parameter_error_response('请传入目标用户id')
        if len(target_project_member_id) >= 50:
            return parameter_error_response('单次复制服务内容用户数应小于50人')
        project_member = ProjectMember.objects.filter(id=project_member_id, deleted=False).first()
        if not project_member:
            return parameter_error_response('当前被复制服务内容用户id不存在')
        project_bundle = project_member.project_bundle.filter(deleted=False).first()
        if not project_bundle:
            return parameter_error_response('当前被复制服务内容未配置服务内容')

        service_stage = ProjectServiceStage.objects.filter(project_member_id=project_member_id, deleted=False).order_by('order').all()
        service_content = ProjectMemberServiceContent.objects.filter(project_member_id=project_member_id, deleted=False).all()
        if not service_stage and not service_content:
            return parameter_error_response('当前被复制服务内容未配置服务内容')
        with transaction.atomic():
            # 遍历服务阶段
            for stage in service_stage if service_stage else [None]:

                # 基于服务阶段获取服务内容
                if stage:
                    service_content = stage.project_member_service_content.filter(deleted=False).all()
                else:
                    service_content = service_content

                # 遍历每个服务内容项
                for item in service_content:

                    # 遍历每个 target_project_member_id
                    # 基于服务阶段创建目标内容

                    for target_id in target_project_member_id:

                        target_project_member = ProjectMember.objects.filter(id=target_id, deleted=False).first()
                        if target_project_member.project_id != project_member.project_id:
                            raise WisdomValidationError('当前被复制服务内容用户与目标用户不属于同一项目')
                        if stage:
                            # 如果复制的用户是自定义阶段，则创建阶段
                            target_stage = ProjectServiceStage.objects.filter(
                                stage_name=stage.stage_name, project_member_id=target_id, deleted=False).first()
                            if not target_stage:
                                target_stage = ProjectServiceStage.objects.create(
                                    stage_name=stage.stage_name, project_member_id=target_id, order=stage.order)
                            target_content = ProjectMemberServiceContent.objects.create(
                                project_member_id=target_id, content_type=item.content_type, service_stage_id=target_stage.id)
                        else:
                            target_content = ProjectMemberServiceContent.objects.create(
                                project_member_id=target_id, content_type=item.content_type, service_stage=item.service_stage)

                        # 复制服务内容
                        if item.object_ids:
                            objects_ids = project_service_public.cp_project_member_service_content(item, target_project_member)
                        else:
                            objects_ids = []
                        target_content.object_ids = [str(i) for i in objects_ids]
                        target_content.save()

                        # 获取项目服务成员
                        project_service_member = item.project_service_members.filter(deleted=False).first()
                        if project_service_member:
                            # 创建新的ProjectServiceMember
                            ProjectServiceMember.objects.create(project_service=project_service_member.project_service, member_service=target_content)

            member_stage = ServiceStage.objects.filter(project_member_id=project_member.id, deleted=False).first()
            for item in target_project_member_id:
                ServiceStage.objects.create(project_member_id=item, is_stage=member_stage.is_stage, deleted=False)
        return success_response()

    @swagger_auto_schema(
        operation_id='关联利益相关者',
        operation_summary='关联利益相关者',
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'stakeholders': openapi.Schema(type=openapi.TYPE_ARRAY, items=openapi.Schema(type=openapi.TYPE_OBJECT),
                                               description='利益相关者信息{"stake_holder_id":1, "relation":2, '
                                                           '"concert_years":4}')
            }
        ),
        tags=['项目成员相关']
    )
    @action(methods=['post'], detail=True, url_path='relevance_stake_holder')
    def relevance_stake_holder(self, request, *args, **kwargs):
        data = request.data
        project_member = self.get_object()
        if not project_member:
            return parameter_error_response('当前被教练者不存在')
        stakeholders = data.get('stakeholders', None)
        if not stakeholders:
            return parameter_error_response('请填写利益相关者信息')
        evaluation_modules = EvaluationModule.objects.filter(
            project_bundle__project_member=project_member,
            evaluation__role=EvaluationWriteRoleEnum.coachee_stakeholder.value,
            deleted=False)
        for info in stakeholders:
            stake_holder_id, relation, concert_years = info.get('stake_holder_id', None), \
                                                       info.get('relation', None), info.get('concert_years', 0)
            if not project_member:
                return parameter_error_response('当前被教练者不存在')
            if not stake_holder_id:
                return parameter_error_response('请选择利益相关者')
            company_member = CompanyMember.objects.filter(id=stake_holder_id, user__deleted=False,
                                                          company_id=project_member.project.company_id).first()
            if not company_member:
                return parameter_error_response('当前利益相关者不存在')
            if not relation:
                return parameter_error_response('请选择与被教练者的关系')
            elif relation not in [StakeHolderRelationEnum.superior.value, StakeHolderRelationEnum.peers.value,
                                  StakeHolderRelationEnum.junior.value]:
                return parameter_error_response('关系错误')
            if ProjectInterested.objects.filter(interested_id=company_member.user_id, master_id=project_member.user_id,
                                                project_id=project_member.project_id, deleted=False).exists():
                return parameter_error_response('当前利益相关者已关联')
            create_data = {"interested_id": company_member.user_id, "master_id": project_member.user_id,
                           "relation": relation, "project_id": project_member.project_id}

            if concert_years:
                if type(concert_years) != int:
                    return parameter_error_response('合作年限只能为整数')
                if concert_years < 0:
                    return parameter_error_response('合作年限只能为正整数')
                if concert_years > 99:
                    return parameter_error_response('合作年限最多2位数')
                create_data['concert_years'] = concert_years

            with transaction.atomic():
                project_interested = ProjectInterested.objects.create(**create_data)

            if evaluation_modules.exists():
                for evaluation_module in evaluation_modules:
                    send_evaluation_message.apply_async(kwargs=dict(
                        project_member_id=project_member.pk, evaluation_module_id=evaluation_module.id,
                        project_interested_id=project_interested.id),
                        countdown=3, expires=120)

            # 绑定未完成的利益相关者的教练任务
            coach_tasks = CoachTask.objects.filter(
                deleted=False,
                project_bundle__project_member=project_member,
                type=NewCoachTaskTypeEnum.stakeholder_research,
                stakeholder_submit_time__isnull=True,
            )
            for item in coach_tasks:
                MultipleAssociationRelation.objects.create(
                    main_id=item.id,
                    deleted=False,
                    secondary_id=project_interested.id,
                    type=MultipleAssociationRelationTypeEnum.stakeholder_coach_task
                )
        return success_response()

    @swagger_auto_schema(
        operation_id='移除利益相关者',
        operation_summary='移除利益相关者',
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'project_interested_id': openapi.Schema(type=openapi.TYPE_NUMBER, description='利益相关者id'),
            }
        ),
        tags=['项目成员相关']
    )
    @action(methods=['post'], detail=True, url_path='del_stake_holder')
    def del_stake_holder(self, request, *args, **kwargs):
        data = request.data
        project_member = self.get_object()
        project_interested_id = data.get('project_interested_id', None)
        if not project_member:
            return parameter_error_response('当前被教练者不存在')
        if not project_interested_id:
            return parameter_error_response('请选择利益相关者')

        project_interested = ProjectInterested.objects.filter(id=project_interested_id,
                                                              deleted=False).first()
        if not project_interested:
            return parameter_error_response('当前利益相关者已被移除，请勿重复操作')
        if StakeholderInterview.objects.filter(deleted=False, project_interested=project_interested).exists():
            return parameter_error_response('当前利益相关者已配置利益相关者访谈禁止移除')

        with transaction.atomic():
            project_interested.deleted = True
            project_interested.save()

            MultipleAssociationRelation.objects.filter(
                secondary_id=project_interested.id,
                deleted=False,
                type__in=[MultipleAssociationRelationTypeEnum.stakeholder_coach_task,
                          MultipleAssociationRelationTypeEnum.stakeholder_change_observation]
            ).update(deleted=True)

        return success_response()

    @swagger_auto_schema(
        operation_id='关联被教练者企业用户列表',
        operation_summary='关联被教练者企业用户列表',
        manual_parameters=[
            openapi.Parameter('page', openapi.IN_QUERY, description='第几页', type=openapi.TYPE_NUMBER),
            openapi.Parameter('page_size', openapi.IN_QUERY, description='每页显示条数默认15条', type=openapi.TYPE_NUMBER),
            openapi.Parameter('project_id', openapi.IN_QUERY, description='项目id', type=openapi.TYPE_NUMBER),
            openapi.Parameter('true_name', openapi.IN_QUERY, description='员工姓名', type=openapi.TYPE_STRING),
        ],
        tags=['项目成员相关']
    )
    @action(methods=['get'], detail=False, url_path='relevance_company_member_list')
    def relevance_project_member_company_member_list(self, request, *args, **kwargs):
        self.serializer_class = RelevanceCompanyMemberListSerializer
        project_id = request.query_params.get('project_id', None)
        true_name = request.query_params.get('true_name', None)
        if not project_id:
            return parameter_error_response('请传入项目id')
        project = Project.objects.filter(pk=project_id, deleted=False).first()
        if not project:
            return parameter_error_response('当前项目不存在')
        company_member = project.company.company_user.all().order_by('-id')
        if true_name:
            company_member = company_member.filter(user__true_name__icontains=true_name)
        # 分页
        paginator = StandardResultsSetPagination()
        page_list = paginator.paginate_queryset(company_member, self.request)
        serializer = self.get_serializer(page_list, many=True)
        response = paginator.get_paginated_response(serializer.data)
        for info in response.data['results']:
            company_member = CompanyMember.objects.get(pk=info['id'])
            if ProjectMember.objects.filter(user_id=company_member.user_id, project_id=project_id,
                                            deleted=False).exists():
                info['is_exists'] = 1
            else:
                info['is_exists'] = 0
        return success_response(response)


    @swagger_auto_schema(
        operation_id='删除项目成员',
        operation_summary='删除项目成员',
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'project_member_id': openapi.Schema(type=openapi.TYPE_NUMBER, description='项目成员id')
            }
        ),
        tags=['项目成员相关']
    )
    @action(methods=['post'], detail=False, url_path='deleted')
    def project_member(self, request, *args, **kwargs):
        try:
            project_member_id = request.data.get('project_member_id')
            project_member = ProjectMember.objects.get(id=project_member_id, deleted=False)
        except ProjectMember.DoesNotExist:
            return parameter_error_response()

        # 检查项目成员是否有相关数据。如果有则返回错误描述。
        # error = project_member_public.is_project_member_have_data(project_member)
        # if error:
        #     return parameter_error_response(error)
        project_member.deleted = True
        project_member.save()
        return success_response()

    @swagger_auto_schema(
        operation_id='项目成员的辅导信息',
        operation_summary='项目成员的辅导信息',
        manual_parameters=[
            openapi.Parameter('project_member_id', openapi.IN_QUERY, description='项目用户唯一标识id', type=openapi.TYPE_NUMBER)
        ],
        tags=['项目成员相关']
    )
    @action(methods=['get'], detail=False, url_path='interview')
    def get_project_member_interview(self, request, *args, **kwargs):
        try:
            project_member_id = request.query_params.get('project_member_id', None)
            project_member = ProjectMember.objects.get(id=project_member_id, deleted=False)
        except ProjectMember.DoesNotExist:
            return parameter_error_response()
        # 可用总时长, 辅导已用时长,
        all_times, used_times = project_member_public.get_interview_all_times_and_user_time(
            project_member)
        total_interview_hours = round(all_times / 60, 1)
        used_interview_hours = round(used_times / 60, 1)

        # 获取基础评分信息
        score_data = interview_public.get_total_interview_scores(
            project_id=project_member.project_id, coachee_user_id=project_member.user_id)
        # 从 score_data 中获取各项评分
        total_target_progress = score_data['target_progress_sum_score']
        total_harvest_score = score_data['harvest_sum_score']
        total_satisfaction_score = score_data['satisfaction_sum_score']
        target_progress_count = score_data['target_progress_count']
        harvest_count = score_data['harvest_count']
        satisfaction_count = score_data['satisfaction_count']
        # 计算平均分
        avg_data = {
            'target_progress_avg': round(total_target_progress / target_progress_count, 1) if target_progress_count > 0 else '--',
            'harvest_score_avg': round(total_harvest_score / harvest_count, 1) if harvest_count > 0 else '--',
            'satisfaction_score_avg': round(total_satisfaction_score / satisfaction_count, 1) if satisfaction_count > 0 else '--'
        }

        # 获取用户的教练信息
        coach_data, selected_status = project_member_public.get_project_member_coach(project_member)
        coach_info = []
        if coach_data:
            for coach in coach_data.all():

                # 查询教练和项目绑定的数据，获取显示的简历id
                # 如果没有获取到，查询教练主简历。
                project_coach = ProjectCoach.objects.filter(
                    deleted=False, coach_id=coach.pk, project_id=project_member.project_id,
                    resume__isnull=False, show_resume__isnull=False
                ).first()
                if project_coach:
                    resume = project_coach.show_resume
                else:
                    resume = coach.resumes.filter(is_customization=False, deleted=False).first()

                coach_info.append({
                    'head_image_url': resume.head_image_url if resume and resume.head_image_url else coach.user.head_image_url,
                    'coach_id': coach.id,
                    'resume_id': resume.id if resume else None,
                    'user_id': coach.user.id,
                    'true_name': coach.user.cover_name,
                    'coach_type': coach.coach_type,
                    'coach_auth_str': CoachAuthEnum(resume.coach_auth).describe() if resume and resume.coach_auth else None
                })

        data = {
            'total_interview_hours': total_interview_hours,  # 辅导总时长
            'used_interview_hours': used_interview_hours,  # 辅导已用时长
            'score_avg': avg_data,
            'coach_info': coach_info,
            'is_selected_coach': selected_status
        }
        return success_response(data)


    @swagger_auto_schema(
        operation_id='项目成员的详细信息',
        operation_summary='项目成员的详细信息',
        manual_parameters=[
            openapi.Parameter('project_member_id', openapi.IN_QUERY, description='项目用户唯一标识id', type=openapi.TYPE_NUMBER)
        ],
        tags=['项目成员相关']
    )
    @action(methods=['get'], detail=False, url_path='customer_info')
    def get_project_member_customer_info(self, request, *args, **kwargs):
        try:
            project_member_id = request.query_params.get('project_member_id')
            if project_member_id:
                project_member = ProjectMember.objects.get(id=project_member_id, deleted=False)
                coachee_user_id = project_member.user_id
                project_id = project_member.project_id
            else:
                coachee_user_id = request.query_params.get('user_id')
                User.objects.get(id=coachee_user_id, deleted=False)
                project_id = request.query_params.get('project_id')
                Project.objects.get(id=project_id, deleted=False)
        except ProjectMember.DoesNotExist:
            return parameter_error_response()
        except User.DoesNotExist:
            return parameter_error_response()
        except Project.DoesNotExist:
            return parameter_error_response()

        data = project_member_public.get_project_member_customer_info(project_id, coachee_user_id)

        return success_response(data)

    @swagger_auto_schema(
        operation_id='修改项目成员的项目运营信息',
        operation_summary='修改项目成员的项目运营信息',
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'project_member_id': openapi.Schema(type=openapi.TYPE_NUMBER, description='项目成员id')
            }
        ),
        tags=['项目成员相关']
    )
    @action(methods=['post'], detail=False, url_path='customer_info/update')
    def update_project_member_info(self, request, *args, **kwargs):
        try:
            project_member_id = request.data.get('project_member_id')
            if project_member_id:
                project_member = ProjectMember.objects.get(id=project_member_id, deleted=False)
                coachee_user_id = project_member.user_id
                project_id = project_member.project_id
            else:
                coachee_user_id = request.data.get('user_id')
                User.objects.get(id=coachee_user_id, deleted=False)
                project_id = request.data.get('project_id')
                Project.objects.get(id=project_id, deleted=False)
        except ProjectMember.DoesNotExist:
            return parameter_error_response()

        # 更新项目运营的客户画像信息
        customer_portrait, is_create = CustomerPortrait.objects.get_or_create(
            user_id=coachee_user_id, coach_id__isnull=True, project_id=project_id, deleted=False)
        data = request.data.copy()
        serializer = CustomerPortraitBaseSerializers(customer_portrait, data=request.data)
        serializer.is_valid(raise_exception=True)
        serializer.update(customer_portrait, data)

        # 利益相关者对客户的反馈和期待需要同步到客户需求标签
        # 需要项目客户信息中关联客户才能找到和同步
        if 'stakeholder_feedback_and_expectations' in data:
            stakeholder_feedback_and_expectations = data.get('stakeholder_feedback_and_expectations')
            # 获取客户的基础信息
            project_tag_group = ProjectTagGroup.objects.filter(
                project_id=project_id, project_member__user_id=coachee_user_id, deleted=False).first()
            if project_tag_group:
                project_tag_group.stakeholder_feedback_and_expectations = stakeholder_feedback_and_expectations
                project_tag_group.save()

        if 'coach_extra' in data:
            other_info = data.get('coach_extra')
            # Get customer's basic info
            project_tag_group = ProjectTagGroup.objects.filter(
                project_id=project_id, project_member__user_id=coachee_user_id, deleted=False).first()
            if project_tag_group:
                project_tag_group.other_info = other_info
                project_tag_group.save()

        return success_response()