from ast import literal_eval

from rest_framework import serializers

from wisdom_v2.models import TotalTemplate, TotalTemplateReport


class TotalTemplateListSerializer(serializers.ModelSerializer):
    title = serializers.CharField(help_text='标题', read_only=True)
    created_at = serializers.DateTimeField(help_text='创建时间', format='%Y-%m-%d', read_only=True)
    write_role = serializers.IntegerField(help_text='填写人', read_only=True)
    status = serializers.IntegerField(help_text='状态 0-未启用 1-启用中', read_only=True)
    type = serializers.IntegerField(help_text='总模版类型 1-一对一 2-教练任务', read_only=True)
    image_url = serializers.CharField(help_text='配图链接', read_only=True)

    class Meta:
        model = TotalTemplate
        fields = ('id', 'created_at', 'write_role', 'status', 'type', 'title', 'coach_template_id',
                  'coachee_template_id', 'image_url', 'stakeholder_template_id')


class TotalTemplateReportListSerializer(serializers.ModelSerializer):
    name = serializers.CharField(source='total_template.title', help_text='总模版标题', read_only=True)
    image_url = serializers.CharField(source='total_template.image_url', help_text='配图链接', read_only=True)
    type = serializers.IntegerField(source='total_template.type', help_text='总辅导模版类型 | 1：一对一辅导；2：教练任务',
                                    read_only=True)
    role = serializers.IntegerField(source='total_template.write_role',
                                    help_text='填写记录的角色 | 1：教练；2：被教练者; 3: 教练及被教练者 4：利益相关者', read_only=True)
    title = serializers.CharField(help_text='标题', read_only=True)
    title_type = serializers.IntegerField(help_text='标题类型 | 1：一级标题；2：二级标题', read_only=True)
    description = serializers.CharField(help_text='说明', read_only=True)
    total_template_id = serializers.IntegerField(help_text='总模版id', read_only=True)
    questions_order = serializers.SerializerMethodField()
    coach_related_question = serializers.SerializerMethodField()
    coachee_related_question = serializers.SerializerMethodField()

    class Meta:
        model = TotalTemplateReport
        fields = ('id', 'name', 'type', 'role', 'title', 'title_type', 'description', 'total_template_id',
                  'coach_related_question', 'coachee_related_question', 'questions_order', 'image_url')

    def get_coach_related_question(self, obj):
        if obj.related_question_id:
            related_question = obj.related_question
            if related_question.coach_question_id:
                return {"id": related_question.coach_question.pk, "title": related_question.coach_question.title}
        return None

    def get_coachee_related_question(self, obj):
        if obj.related_question_id:
            related_question = obj.related_question
            if related_question.coachee_question_id:
                return {"id": related_question.coachee_question.pk, "title": related_question.coachee_question.title}
        return None

    def get_questions_order(self, obj):
        return literal_eval(obj.total_template.questions_order)

