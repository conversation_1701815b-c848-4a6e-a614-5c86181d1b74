from rest_framework import serializers

from wisdom_v2.enum.service_content_enum import NoticeTemplateTypeEnum
from wisdom_v2.models import UserNoticeRecord, Resume


# 数据创建/更新序列化
class UserNoticeRecordSerializers(serializers.ModelSerializer):
    id = serializers.CharField(source='uuid', help_text='标识')
    required_coach_count = serializers.IntegerField(source='project.required_coach_count', help_text='教练数量')
    project_name = serializers.CharField(source='project.name', help_text='项目名称')
    send_user_name = serializers.CharField(source='user.cover_name', help_text='创建人名称')
    content = serializers.SerializerMethodField(help_text='具体通知信息')
    created_at = serializers.SerializerMethodField(help_text='创建时间', read_only=True)
    selected_count = serializers.SerializerMethodField(help_text='已选简历数量', read_only=True)
    status = serializers.SerializerMethodField(help_text='选择状态', read_only=True)

    def get_created_at(self, obj):
        return obj.created_at.strftime('%Y-%m-%d %H:%M:%S')

    def get_selected_count(self, obj):
        if obj.type == NoticeTemplateTypeEnum.coach_resume:
            content = obj.content
            selected_resume_ids = content.get('selected_resume_id', [])
            return len(selected_resume_ids)

    def get_status(self, obj):
        if obj.type == NoticeTemplateTypeEnum.coach_resume:
            content = obj.content
            selected_resume_ids = content.get('selected_resume_id')
            if selected_resume_ids:
                return 2  # 已选择
            else:
                return 1  # 未选择

    def get_content(self, obj):
        if obj.type == NoticeTemplateTypeEnum.coach_resume:

            content = obj.content
            resume_ids = content.get('resume_id')
            selected_resume_ids = content.get('selected_resume_id', [])

            data = []
            coach_resume = Resume.objects.filter(id__in=resume_ids, deleted=False).all()

            for resume in coach_resume:
                raw_resume_data = {
                    "id": resume.id,
                    "true_name": resume.coach.user.cover_name,
                    "gender": resume.coach.user.gender,
                    "working_years": resume.working_years if resume.working_years else None,
                    "coach_auth": resume.coach_auth,
                    "head_image_url": resume.head_image_url,
                    "domain": resume.coach_domain[:3] if resume.coach_domain else None,
                    "order_receiving_status": resume.coach.order_receiving_status,
                    "is_customization": resume.is_customization,
                    "is_selected": True if resume.id in selected_resume_ids else False,
                    "customization_resume": None
                }
                if resume.is_customization:
                    raw_resume_data['customization_resume'] = {
                        "name": resume.name,
                        "created_at": resume.created_at.strftime("%Y-%m-%d %H:%M:%S"),
                        "creator": resume.creator.true_name
                    }

                data.append(raw_resume_data)
            return data
        return

    class Meta:
        model = UserNoticeRecord
        fields = ['id', 'project_name', 'send_user_name', 'content', 'type', 'channel', 'extra', 'created_at',
                  'selected_count', 'status', 'required_coach_count', 'feedback']
