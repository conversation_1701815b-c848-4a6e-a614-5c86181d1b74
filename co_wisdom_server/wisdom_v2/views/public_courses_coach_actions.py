from rest_framework import serializers

from wisdom_v2.common import public_courses_public
from wisdom_v2.models_file import PublicCoursesCoach, BusinessOrder2Object


class AdminPublicCoursesCoachListSerializer(serializers.ModelSerializer):
    """
    公开课教练列表序列化
    """
    status = serializers.SerializerMethodField(help_text='教练课程状态')
    class_time = serializers.SerializerMethodField(help_text='工作时间')
    coach_name = serializers.CharField(source='coach.user.cover_name', help_text='教练名称')
    business_order_id = serializers.SerializerMethodField(help_text='结算订单id')

    def get_status(self, obj):
        return public_courses_public.get_status_display(obj)

    def get_class_time(self, obj):
        return f'{obj.start_time.strftime("%Y-%m-%d %H:%M")} ~ {obj.end_time.strftime("%Y-%m-%d %H:%M")}'

    def get_business_order_id(self, obj):
        business_order2_object = BusinessOrder2Object.objects.filter(
            object_id=str(obj.pk), deleted=False, business_order__deleted=False).first()
        if business_order2_object:
            return str(business_order2_object.business_order.pk)
        return

    class Meta:
        model = PublicCoursesCoach
        fields = ('id', 'class_name', 'status', 'type', 'class_time', 'coach_name', 'student_name', 'business_order_id')
