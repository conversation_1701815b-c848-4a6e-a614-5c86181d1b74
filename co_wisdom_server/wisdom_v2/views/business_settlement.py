from collections import defaultdict
from decimal import Decimal

from django.db.models import Q
from django.db import transaction
from rest_framework.decorators import action
from drf_yasg.utils import swagger_auto_schema
from drf_yasg import openapi
from rest_framework import viewsets
from datetime import datetime

from utils.api_response import success_response, parameter_error_response
from utils.messagecenter import getui
from utils.pagination import StandardResultsSetPagination
from wisdom_v2.common.business_settlement_public import generate_business_settlements, mark_settlement_status
from wisdom_v2.common import business_order_public
from wisdom_v2.enum.business_order_enum import BusinessOrderPayStatusEnum, BusinessOrderSettlementStatusEnum, \
    BusinessOrderWithdrawalStatusEnum
from wisdom_v2.models_file import BusinessOrder, BusinessSettlement
from wisdom_v2.models import Coach, UserBackend, WorkWechatUser
from wisdom_v2.views import constant
from wisdom_v2.views.business_settlement_actions import BusinessSettlementSerializer, BusinessSettlementOrdersSerializer


class BusinessSettlementViewSet(viewsets.ModelViewSet):
    queryset = BusinessSettlement.objects.filter(deleted=False).order_by('-apply_time')
    serializer_class = BusinessSettlementSerializer

    @swagger_auto_schema(
        operation_id='结算单列表',
        operation_summary='结算单列表',
        manual_parameters=[
            openapi.Parameter('coach_name', openapi.IN_QUERY, description='教练名称', type=openapi.TYPE_STRING),
            openapi.Parameter('settlement_status', openapi.IN_QUERY, description='结算状态', type=openapi.TYPE_STRING),
            openapi.Parameter('user_type', openapi.IN_QUERY, description='用户类型', type=openapi.TYPE_STRING),
            openapi.Parameter('apply_start_date', openapi.IN_QUERY, description='发起提现开始时间', type=openapi.TYPE_STRING),
            openapi.Parameter('apply_end_date	', openapi.IN_QUERY, description='发起提现结束时间', type=openapi.TYPE_STRING),
            openapi.Parameter('settlement_start_date', openapi.IN_QUERY, description='结算开始时间', type=openapi.TYPE_STRING),
            openapi.Parameter('settlement_end_date	', openapi.IN_QUERY, description='结算结束时间', type=openapi.TYPE_STRING),
            openapi.Parameter('page', openapi.IN_QUERY, description='第几页', type=openapi.TYPE_NUMBER),
            openapi.Parameter('page_size', openapi.IN_QUERY, description='每页显示条数默认15条', type=openapi.TYPE_NUMBER),
        ],
        tags=['后台结算管理相关']
    )
    def list(self, request, *args, **kwargs):
        business_settlements = self.get_queryset()
        if request.query_params.get('coach_name'):
            business_settlements = business_settlements.filter(
                Q(coach__user__true_name__icontains=request.query_params.get('coach_name')) |
                Q(coach__personal_name__icontains=request.query_params.get('coach_name')))
        if request.query_params.get('settlement_status'):
            business_settlements = business_settlements.filter(
                settlement_status__in=request.query_params.get('settlement_status').split(','))
        if request.query_params.get('user_type'):
            business_settlements = business_settlements.filter(
                user_type__in=request.query_params.get('user_type').split(','))
        if request.query_params.get('apply_start_date') and request.query_params.get('apply_end_date'):
            apply_start_date = datetime.strptime(request.query_params.get('apply_start_date'), '%Y-%m-%d').date()
            apply_end_date = datetime.strptime(request.query_params.get('apply_end_date'), '%Y-%m-%d').date()
            business_settlements = business_settlements.filter(
                apply_time__date__range=[apply_start_date, apply_end_date])
        if request.query_params.get('settlement_start_date') and request.query_params.get('settlement_end_date'):
            settlement_start_date = datetime.strptime(request.query_params.get('settlement_start_date'),
                                                      '%Y-%m-%d').date()
            settlement_end_date = datetime.strptime(request.query_params.get('settlement_end_date'), '%Y-%m-%d').date()
            business_settlements = business_settlements.filter(
                settlement_time__date__range=[settlement_start_date, settlement_end_date])

        # 分页
        paginator = StandardResultsSetPagination()
        page_list = paginator.paginate_queryset(business_settlements, self.request)
        serializer = self.get_serializer(page_list, many=True)
        response = paginator.get_paginated_response(serializer.data)
        return success_response(response)

    @swagger_auto_schema(
        operation_id='订单列表',
        operation_summary='订单列表',
        manual_parameters=[
            openapi.Parameter('id', openapi.IN_QUERY, description='订单编号', type=openapi.TYPE_STRING),
            openapi.Parameter('type', openapi.IN_QUERY, description='工作类型', type=openapi.TYPE_STRING),
            openapi.Parameter('work_type', openapi.IN_QUERY, description='工作形式',type=openapi.TYPE_STRING),
            openapi.Parameter('pay_status', openapi.IN_QUERY, description='支付状态', type=openapi.TYPE_STRING),
            openapi.Parameter('coach_type', openapi.IN_QUERY, description='教练类型', type=openapi.TYPE_STRING),
            openapi.Parameter('class_name', openapi.IN_QUERY, description='班级名称', type=openapi.TYPE_STRING),
            openapi.Parameter('project_id', openapi.IN_QUERY, description='项目标识', type=openapi.TYPE_NUMBER),
            openapi.Parameter('company_id', openapi.IN_QUERY, description='公司标识', type=openapi.TYPE_NUMBER),
            openapi.Parameter('user_name', openapi.IN_QUERY, description='用户名', type=openapi.TYPE_STRING),
            openapi.Parameter('coach_name', openapi.IN_QUERY, description='教练名称', type=openapi.TYPE_STRING),
            openapi.Parameter('settlement_status', openapi.IN_QUERY, description='结算状态', type=openapi.TYPE_STRING),
            openapi.Parameter('withdrawal_status', openapi.IN_QUERY, description='提现状态', type=openapi.TYPE_STRING),
            openapi.Parameter('pay_start_date', openapi.IN_QUERY, description='支付开始时间', type=openapi.TYPE_STRING),
            openapi.Parameter('pay_end_date	', openapi.IN_QUERY, description='支付结束时间', type=openapi.TYPE_STRING),
            openapi.Parameter('settlement_start_date', openapi.IN_QUERY, description='结算开始时间', type=openapi.TYPE_STRING),
            openapi.Parameter('settlement_end_date	', openapi.IN_QUERY, description='结算结束时间', type=openapi.TYPE_STRING),
            openapi.Parameter('page', openapi.IN_QUERY, description='第几页', type=openapi.TYPE_NUMBER),
            openapi.Parameter('page_size', openapi.IN_QUERY, description='每页显示条数默认15条', type=openapi.TYPE_NUMBER),
            openapi.Parameter('apply_start_date', openapi.IN_QUERY, description='发起提现开始时间',
                              type=openapi.TYPE_STRING),
            openapi.Parameter('apply_end_date	', openapi.IN_QUERY, description='发起提现结束时间',
                              type=openapi.TYPE_STRING),
            openapi.Parameter('work_start_date', openapi.IN_QUERY, description='工作开始时间',
                              type=openapi.TYPE_STRING),
            openapi.Parameter('work_end_date	', openapi.IN_QUERY, description='工作结束时间',
                              type=openapi.TYPE_STRING),
        ],
        tags=['后台结算管理相关']
    )
    @action(methods=['get'], detail=False, url_path='orders')
    def get_settlement_order_list(self, request, *args, **kwargs):
        user = request.user
        user_backend = UserBackend.objects.filter(user=user, deleted=False).first()
        if not user_backend:
            return parameter_error_response('当前用户无权限')

        if user_backend.role_id not in [constant.ADMIN, constant.FINANCE]:
            return parameter_error_response('当前用户无权限')

        # 获取查询好的订单对象
        sorted_orders = business_order_public.get_business_settlement_to_order(request.query_params)

        # 分页
        paginator = StandardResultsSetPagination()
        page_list = paginator.paginate_queryset(sorted_orders, self.request)
        serializer = BusinessSettlementOrdersSerializer(page_list, many=True)
        response = paginator.get_paginated_response(serializer.data)
        return success_response(response)

    @swagger_auto_schema(
        operation_id='结算单下订单明细',
        operation_summary='结算单下订单明细',
        manual_parameters=[
            openapi.Parameter('settlement_id', openapi.IN_QUERY, description='结算单id', type=openapi.TYPE_STRING),
        ],
        tags=['后台结算管理相关']
    )
    @action(methods=['get'], detail=False, url_path='detail')
    def settlement_detail(self, request, *args, **kwargs):
        try:
            settlement_id = request.query_params['settlement_id']
            business_settlement = BusinessSettlement.objects.get(id=settlement_id, deleted=False)
        except (KeyError, BusinessSettlement.DoesNotExist):
            return parameter_error_response()
        data = self.serializer_class(business_settlement, many=False).data
        return success_response(data)

    @swagger_auto_schema(
        operation_id='后台发起结算',
        operation_summary='后台发起结算',
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'ids': openapi.Schema(type=openapi.TYPE_NUMBER, description='订单id列表'),
            }
        ),
        tags=['后台结算管理相关']
    )
    def create(self, request, *args, **kwargs):
        try:
            ids = request.data['ids']
            business_order = BusinessOrder.objects.filter(id__in=ids, deleted=False)
            user = request.user
        except (KeyError, BusinessOrder.DoesNotExist):
            return parameter_error_response()
        if not business_order.exists():
            return parameter_error_response('订单不存在')
        cant_settle_order = business_order.filter(
            Q(pay_status=BusinessOrderPayStatusEnum.non) |
            Q(withdrawal_status=BusinessOrderWithdrawalStatusEnum.non_withdrawable.value) |
            Q(settlement_status=BusinessOrderSettlementStatusEnum.settled))
        if cant_settle_order.exists():
            text = f"订单编号{'、 '.join([str(order.id) for order in cant_settle_order])}不符合提现条件"
            return parameter_error_response(text)
        exists_order = business_order.values_list('id', 'coach_id')
        exists_data = defaultdict(list)
        for id, coach_id in exists_order:
            exists_data[coach_id].append(id)
        with transaction.atomic():
            for coach_id, order_ids in exists_data.items():
                coach = Coach.objects.filter(id=coach_id, deleted=False).first()
                business_order = BusinessOrder.objects.filter(id__in=order_ids)
                business_settlements = generate_business_settlements(coach, business_order, user)
                # 发送消息
                work_wechat_user = WorkWechatUser.objects.filter(
                    wx_user_id__isnull=False, user_id=coach.user.id, deleted=False).first()
                if work_wechat_user:
                    getui.send_work_wechat_coach_notice.delay(
                        work_wechat_user.wx_user_id,
                        'coach_request_settlement_info',
                        coach_name=coach.user.cover_name,
                        coach_id=coach.user.id,
                        project_id=None,
                        receiver_id=coach.user.id,
                        sender_id=None,
                        money=str(Decimal(str(business_settlements.apply_withdrawal_amount)) / Decimal('100'))
                    )

        return success_response()

    @swagger_auto_schema(
        operation_id='后台标记结算',
        operation_summary='后台标记结算',
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'ids': openapi.Schema(type=openapi.TYPE_NUMBER, description='订单id列表'),
                'is_sure_save': openapi.Schema(type=openapi.TYPE_NUMBER, description='是否直接保存'),
            }
        ),
        tags=['后台结算管理相关']
    )
    @action(methods=['post'], detail=False, url_path='settled')
    def settled_settlement(self, request, *args, **kwargs):
        try:
            ids = request.data['ids']
            is_sure_save = request.data.get('is_sure_save')
            business_order = BusinessOrder.objects.filter(id__in=ids, deleted=False)
            user = request.user
        except KeyError:
            return parameter_error_response()
        if not business_order.exists():
            return parameter_error_response('未找到相关订单')
        if business_order.filter(business_settlement__settlement_status=BusinessOrderSettlementStatusEnum.settled).exists():
            return parameter_error_response('存在已结算订单，请勿重复操作')
        if business_order.filter(withdrawal_status=BusinessOrderWithdrawalStatusEnum.non_withdrawable.value).exists():
            return parameter_error_response('存在不可提现订单，请勿操作')
        if business_order.filter(business_settlement__isnull=True).exists():
            return parameter_error_response('存在未发起提现订单，请勿操作')

        business_settlement_ids = list(business_order.values_list('business_settlement', flat=True).distinct())

        if not is_sure_save:
            not_selected_business_order, count = business_order_public.get_not_selected_business_order(ids, business_settlement_ids)
            if not_selected_business_order:
                return success_response({'not_selected_business_order': not_selected_business_order, 'count': count})
        business_settlements = BusinessSettlement.objects.filter(id__in=business_settlement_ids)
        with transaction.atomic():
            mark_settlement_status(business_settlements, user)
        return success_response()


    @swagger_auto_schema(
        operation_id='后台标可提现',
        operation_summary='后台标可提现',
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'ids': openapi.Schema(type=openapi.TYPE_NUMBER, description='结算单id列表'),
            }
        ),
        tags=['后台结算管理相关']
    )
    @action(methods=['post'], detail=False, url_path='draw_money')
    def draw_money_settlement_order(self, request, *args, **kwargs):
        try:
            ids = request.data['ids']
            business_order = BusinessOrder.objects.filter(id__in=ids, deleted=False)
            user = request.user
        except KeyError:
            return parameter_error_response()
        if not business_order.exists():
            return parameter_error_response('未找到相关订单')
        if business_order.filter(apply_withdrawal_time__isnull=False).exists():
            return parameter_error_response('已存在可提现订单，请勿重复操作')
        with transaction.atomic():
            business_order.update(
                withdrawal_status=BusinessOrderWithdrawalStatusEnum.can_withdraw.value,
                apply_withdrawal_time=datetime.now(),
                apply_withdrawal_processor=user
            )
        return success_response()


    @swagger_auto_schema(
        operation_id='删除结算单',
        operation_summary='删除结算单',
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'settlement_id': openapi.Schema(type=openapi.TYPE_NUMBER, description='结算单id'),
            }
        ),
        tags=['后台结算管理相关']
    )
    @action(methods=['post'], detail=False, url_path='delete_order')
    def delete_order(self, request, *args, **kwargs):
        try:
            order_id = request.data['order_id']
            business_order = BusinessOrder.objects.get(id=order_id, deleted=False)
        except (KeyError, BusinessSettlement.DoesNotExist, BusinessOrder.DoesNotExist):
            return parameter_error_response('未找到相关结算单')

        # Check if the settlement is already settled
        if business_order.business_settlement and business_order.business_settlement.settlement_status == BusinessOrderSettlementStatusEnum.settled:
            return parameter_error_response('已结算的订单不允许删除')

        with transaction.atomic():
            business_order.deleted = True
            business_order.save()

            # Check if there are any remaining orders for this settlement
            if business_order.business_settlement:
                remaining_orders = BusinessOrder.objects.filter(business_settlement=business_order.business_settlement, deleted=False).exists()

                if not remaining_orders:
                    # If no orders left, delete the settlement
                    business_order.business_settlement.deleted = True
                    business_order.business_settlement.save()


            # Send delete message alert in Feishu robot
            from utils.feishu_robot import send_lark_message
            send_lark_message(order_id, 'delete_settlement_orders')

        return success_response('结算单删除成功')

