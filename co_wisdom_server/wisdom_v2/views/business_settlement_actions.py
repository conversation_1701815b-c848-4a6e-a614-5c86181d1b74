from decimal import Decimal

from rest_framework import serializers

from wisdom_v2.common import business_order_public
from wisdom_v2.enum.business_order_enum import SettlementUserTypeEnum, \
    BusinessOrderTypeEnum, BusinessOrderDurationTypeEnum, WorkTypeEnum, BusinessOrderDataTypeEnum
from wisdom_v2.enum.project_interview_enum import ProjectInterviewPlaceTypeEnum
from wisdom_v2.enum.service_content_enum import CoachOfferStatusEnum
from wisdom_v2.models import UserContract, CoachOffer, ProjectInterview
from wisdom_v2.models_file import BusinessOrder, BusinessSettlement, BusinessOrder2Object
from wisdom_v2.models_file.business_order import BusinessOrderExtraCost
from wisdom_v2.views.constant import ATTR_STATUS_INTERVIEW_CANCEL


class BusinessSettlementSerializer(serializers.ModelSerializer):
    id = serializers.CharField(help_text='id')
    coach_name = serializers.CharField(source='coach.user.true_name', help_text='教练名称')
    personal_name = serializers.Char<PERSON><PERSON>(source='coach.personal_name', help_text='对个人用户可见姓名')
    apply_withdrawal_amount = serializers.SerializerMethodField(help_text='申请提现金额')
    apply_time = serializers.DateTimeField(help_text='申请提现时间', format='%Y-%m-%d %H:%M:%S')
    user_type = serializers.IntegerField(help_text='用户类型')
    member_paid = serializers.SerializerMethodField(help_text='客户实付')
    platform_service_amount = serializers.SerializerMethodField(help_text='平台服务费')
    tax_amount = serializers.SerializerMethodField(help_text='税费')
    remain_withdrawable_orders = serializers.SerializerMethodField(help_text='剩余可提现订单数')
    remain_withdrawable_amount = serializers.SerializerMethodField(help_text='剩余可提现金额')
    create_user = serializers.CharField(source='create_user.cover_name')
    settlement_status = serializers.IntegerField(help_text='结算状态')
    processor = serializers.SerializerMethodField(help_text='处理人')
    settlement_time = serializers.DateTimeField(help_text='结算时间', format='%Y-%m-%d %H:%M:%S')
    coach_id = serializers.IntegerField(help_text='教练id')

    class Meta:
        model = BusinessSettlement
        fields = ('id', 'coach_name', 'apply_withdrawal_amount', 'apply_time', 'user_type',
                  'member_paid', 'platform_service_amount', 'tax_amount', 'remain_withdrawable_orders',
                  'remain_withdrawable_amount', 'create_user', 'settlement_status', 'processor', 'settlement_time',
                  'coach_id', 'personal_name')

    def get_processor(self, obj):
        if obj.processor:
            return obj.processor.true_name

    def get_coach_name(self, obj):
        if obj.user_type == SettlementUserTypeEnum.personal:
            return obj.coach.personal_name if obj.coach.personal_name else obj.coach.user.cover_name
        else:
            return obj.coach.user.cover_name

    def get_apply_withdrawal_amount(self, obj):
        return str(Decimal(str(obj.apply_withdrawal_amount)) / Decimal('100'))

    def get_member_paid(self, obj):
        if obj.user_type == SettlementUserTypeEnum.personal:
            return str(Decimal(str(obj.member_paid)) / Decimal('100'))

    def get_platform_service_amount(self, obj):
        if obj.user_type == SettlementUserTypeEnum.personal:
            return str(Decimal(str(obj.platform_service_amount)) / Decimal('100'))

    def get_tax_amount(self, obj):
        if obj.user_type == SettlementUserTypeEnum.enterprise:
            return str(Decimal(str(obj.tax_amount)) / Decimal('100'))

    def get_remain_withdrawable_orders(self, obj):
        return business_order_public.get_coach_remain_withdrawable_orders(obj.coach)

    def get_remain_withdrawable_amount(self, obj):
        return business_order_public.get_coach_remain_withdrawable_amount(obj.coach)


#  新增结算页面的订单列表详情（超大综合）
class BusinessSettlementOrdersSerializer(serializers.ModelSerializer):
    id = serializers.CharField(help_text='id')
    type = serializers.IntegerField(help_text='工作类型')
    work_type = serializers.IntegerField(help_text='工作形式')
    project_name = serializers.SerializerMethodField(help_text='项目名称')
    member_name = serializers.SerializerMethodField(help_text='客户名称')
    class_name = serializers.SerializerMethodField(help_text='班级名称')
    coach_name = serializers.CharField(source='coach.user.true_name', help_text='教练名称')
    coach_type = serializers.CharField(source='coach.coach_type', help_text='教练类型')
    personal_name = serializers.CharField(source='coach.personal_name', help_text='对个人用户可见姓名')
    coach_id = serializers.IntegerField(help_text='教练id')
    coach_price = serializers.SerializerMethodField(help_text='教练单价')
    work_time = serializers.SerializerMethodField(help_text='工作时间')
    duration = serializers.SerializerMethodField(help_text='结算时长')
    member_paid = serializers.SerializerMethodField(help_text='客户实付')
    pay_status = serializers.IntegerField(help_text='支付状态')
    withdrawal_status = serializers.IntegerField(help_text='提现状态')
    settlement_status = serializers.IntegerField(help_text='结算状态')
    pay_time = serializers.DateTimeField(help_text='支付时间', format='%Y-%m-%d %H:%M:%S')
    settlement_time = serializers.DateTimeField(help_text='结算时间', format='%Y-%m-%d %H:%M:%S')
    platform_scale = serializers.SerializerMethodField(help_text='平台扣除比例')
    platform_service_amount = serializers.SerializerMethodField(help_text='平台服务费')
    tax_point = serializers.SerializerMethodField(help_text='税点')
    tax_amount = serializers.SerializerMethodField(help_text='税费')
    coach_actual_income = serializers.SerializerMethodField(help_text='教练实际收益')
    history_record = serializers.SerializerMethodField(help_text='变更记录')
    apply_settlement_time = serializers.SerializerMethodField(help_text='申请提现时间')
    project_offer_id = serializers.SerializerMethodField(help_text='教练offer')
    contract_url = serializers.SerializerMethodField(help_text='教练合同链接')
    order_count = serializers.SerializerMethodField(help_text='购买小时数')
    used_order_count = serializers.SerializerMethodField(help_text='已核销小时数')
    company_name = serializers.SerializerMethodField(help_text='公司简称')
    processor = serializers.SerializerMethodField(help_text='处理人')
    remain_withdrawable_orders = serializers.SerializerMethodField(help_text='剩余可提现订单数')
    remain_withdrawable_amount = serializers.SerializerMethodField(help_text='剩余可提现金额')
    apply_time = serializers.SerializerMethodField(help_text='申请提现时间')
    create_user = serializers.SerializerMethodField(help_text='申请发起人')
    apply_withdrawal_amount = serializers.SerializerMethodField(help_text='申请提现金额')
    payable_amount = serializers.SerializerMethodField(help_text='应付金额')
    extra_cost_count = serializers.SerializerMethodField(help_text='额外费用数量')
    total_extra_cost = serializers.SerializerMethodField(help_text='费用总金额')
    activity_theme = serializers.SerializerMethodField(help_text='活动主题')
    to_c_order_actual_income = serializers.SerializerMethodField(help_text='C端订单支付金额')
    to_c_settlement_actual_income = serializers.SerializerMethodField(help_text='C端订单结算金额')
    place_type = serializers.SerializerMethodField(help_text='辅导地址类型')

    class Meta:
        model = BusinessOrder
        fields = ('id', 'type', 'work_type', 'project_name', 'member_name', 'class_name', 'coach_id', 'coach_name', 'coach_price',
                  'work_time', 'duration', 'member_paid', 'pay_status', 'withdrawal_status', 'settlement_status',
                  'pay_time', 'settlement_time', 'platform_scale', 'platform_service_amount', 'tax_point', 'tax_amount',
                  'coach_actual_income', 'history_record', 'apply_settlement_time', 'project_offer_id', 'contract_url',
                  'order_count', 'used_order_count', 'personal_name', 'company_name','remain_withdrawable_orders',
                  'remain_withdrawable_amount', 'processor', 'apply_time', 'create_user', 'apply_withdrawal_amount',
                  'payable_amount', 'extra_cost_count', 'total_extra_cost', 'coach_type', 'activity_theme',
                  'to_c_order_actual_income', 'to_c_settlement_actual_income', 'place_type'
                  )

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self._cache = {}

    # 计算或检索订单详情缓存
    def calculate_order_details(self, obj):
        if str(obj.coach.user_id) not in self._cache.keys():
            self._cache[str(obj.coach.user_id)] = {
                'remain_withdrawable_orders': business_order_public.get_coach_remain_withdrawable_orders(obj.coach),
                'remain_withdrawable_amount': business_order_public.get_coach_remain_withdrawable_amount(obj.coach),
                'to_c_order_actual_income': business_order_public.get_coach_to_c_order_actual_income(obj.coach),
                'to_c_settlement_actual_income': business_order_public.get_coach_to_c_settlement_actual_income(obj.coach),
            }

        return self._cache[str(obj.coach.user_id)]

    def get_place_type(self, obj):
        if obj.work_type in WorkTypeEnum.interview_data():
            bo2o = BusinessOrder2Object.objects.filter(
                business_order=obj, deleted=False, data_type=BusinessOrderDataTypeEnum.interview.value).first()
            if bo2o:
                # 根据 BusinessOrder2Object 实例中的 object_id 查找对应的未被删除的 ProjectInterview 实例
                project_interview = ProjectInterview.objects.filter(
                    id=bo2o.object_id, deleted=False, place_type__isnull=False).first()
                if project_interview:
                    return ProjectInterviewPlaceTypeEnum.get_display(project_interview.place_type)
        return '--'

    def get_activity_theme(self, obj):
        activity = business_order_public.get_business_order_activity(obj)
        if activity:
            return activity.theme
        return '--'

    def get_extra_cost_count(self, obj):
        return BusinessOrderExtraCost.objects.filter(business_order=obj, deleted=False).count()

    def get_total_extra_cost(self, obj):
        return f'{business_order_public.get_all_business_order_extra_cost(obj)}元'

    def get_payable_amount(self, obj):
        payable_amount = Decimal(obj.coach_price) * Decimal(obj.duration) / Decimal('100')
        total_extra_cost = business_order_public.get_all_business_order_extra_cost(obj)
        return f"{payable_amount + total_extra_cost}元"

    def get_apply_withdrawal_amount(self, obj):
        if obj.business_settlement and obj.business_settlement.apply_withdrawal_amount:
            return str(Decimal(str(obj.business_settlement.apply_withdrawal_amount)) / Decimal('100'))
        return

    def get_create_user(self, obj):
        if obj.business_settlement and obj.business_settlement.create_user:
            return obj.business_settlement.create_user.cover_name
        return

    def get_apply_time(self, obj):
        if obj.business_settlement and obj.business_settlement.apply_time:
            return obj.business_settlement.apply_time.strftime('%Y-%m-%d %H:%M:%S')
        return

    def get_processor(self, obj):
        if obj.business_settlement and obj.business_settlement.processor:
            return obj.business_settlement.processor.true_name

    def get_company_name(self, obj):
        if obj.project:
            return obj.project.company.real_name

    def get_order_count(self, obj):
        if obj.type == BusinessOrderTypeEnum.personal:
            relation = BusinessOrder2Object.objects.filter(business_order_id=obj.id, type=WorkTypeEnum.one_to_one).first()
            if relation:
                interview = ProjectInterview.objects.filter(id=relation.object_id).first()
                if interview:
                    order = interview.order
                    return order.count

    def get_used_order_count(self, obj):
        if obj.type == BusinessOrderTypeEnum.personal:
            relation = BusinessOrder2Object.objects.filter(business_order_id=obj.id, type=WorkTypeEnum.one_to_one).first()
            if relation:
                interview = ProjectInterview.objects.filter(id=relation.object_id).first()
                if interview:
                    order = interview.order
                    interviews = order.interview.filter(
                        deleted=False).exclude(
                        public_attr__status=ATTR_STATUS_INTERVIEW_CANCEL).order_by(
                        'public_attr__start_time').values_list('id', flat=True)
                    count = 0
                    for index, interview_item in enumerate(interviews):
                        if interview_item == interview.id:
                            count += index + 1
                            break
                    return count

    def get_member_name(self, obj):
        return business_order_public.get_business_order_member_name(obj)

    def get_apply_settlement_time(self, obj):
        if obj.business_settlement:
            return obj.business_settlement.created_at.strftime('%Y-%m-%d %H:%M:%S')

    def get_project_offer_id(self, obj):
        if obj.type == BusinessOrderTypeEnum.enterprise:
            coach_offer = CoachOffer.objects.filter(
                status=CoachOfferStatusEnum.joined, coach=obj.coach, project_offer__project=obj.project,
                deleted=False, project_offer__deleted=False).first()
            if coach_offer:
                return coach_offer.project_offer_id

    def get_contract_url(self, obj):
        if obj.type == BusinessOrderTypeEnum.personal:
            contract = UserContract.objects.filter(user=obj.coach.user, deleted=False).first()
            if contract:
                return contract.contract_url

    def get_history_record(self, obj):
        return obj.history_record

    def get_platform_scale(self, obj):
        if obj.type == BusinessOrderTypeEnum.personal:
            return f"{obj.platform_scale}%"

    def get_platform_service_amount(self, obj):
        if obj.type == BusinessOrderTypeEnum.personal:
            return str(Decimal(str(obj.platform_service_amount)) / Decimal('100'))

    def get_tax_point(self, obj):
        if obj.tax_point is not None:
            return f"{obj.tax_point}%"

    def get_tax_amount(self, obj):
        if obj.tax_amount is not None:
            return str(Decimal(str(obj.tax_amount)) / Decimal('100'))

    def get_coach_actual_income(self, obj):
        return str(Decimal(str(obj.coach_actual_income)) / Decimal('100'))

    def get_project_name(self, obj):
        if obj.type == BusinessOrderTypeEnum.enterprise:
            if obj.project:
                return obj.project.full_name
            elif obj.extra_info and 'project_name' in obj.extra_info:
                return obj.extra_info['project_name']
    def get_class_name(self, obj):
        if obj.type == BusinessOrderTypeEnum.public_course:
            public_course_coach = business_order_public.get_business_order_to_public_course(obj)
            if public_course_coach:
                return public_course_coach.class_name
            elif obj.extra_info and 'project_name' in obj.extra_info:
                return obj.extra_info['project_name']

    def get_coach_name(self, obj):
        if obj.type == BusinessOrderTypeEnum.personal:
            return obj.coach.personal_name if obj.coach.personal_name else obj.coach.user.cover_name
        else:
            return obj.coach.user.cover_name

    def get_coach_price(self, obj):
        amount = str(Decimal(obj.coach_price) / Decimal('100'))
        if obj.duration_type == BusinessOrderDurationTypeEnum.hour:
            return f"{amount}元/小时"
        return f"{amount}元/天"

    def get_work_time(self, obj):
        if obj.extra_info and 'work_time' in obj.extra_info:
            return obj.extra_info['work_time']
        return f"{obj.work_start_time.strftime('%Y-%m-%d %H:%M')}-{obj.work_end_time.strftime('%Y-%m-%d %H:%M')}"

    def get_duration(self, obj):
        duration = int(obj.duration) if isinstance(obj.duration, float) and obj.duration.is_integer() else obj.duration
        if obj.duration_type == BusinessOrderDurationTypeEnum.hour:
            return f"{duration}小时"
        return f"{duration}天"

    def get_member_paid(self, obj):
        if obj.type == BusinessOrderTypeEnum.personal and obj.work_type == WorkTypeEnum.one_to_one:
            return str(Decimal(str(obj.member_paid)) / Decimal('100'))

    def get_remain_withdrawable_orders(self, obj):
        calculate_order_details = self.calculate_order_details(obj)
        return calculate_order_details.get('remain_withdrawable_orders')

    def get_remain_withdrawable_amount(self, obj):
        calculate_order_details = self.calculate_order_details(obj)
        return calculate_order_details.get('remain_withdrawable_amount')

    def get_to_c_order_actual_income(self, obj):
        calculate_order_details = self.calculate_order_details(obj)
        return calculate_order_details.get('to_c_order_actual_income')

    def get_to_c_settlement_actual_income(self, obj):
        calculate_order_details = self.calculate_order_details(obj)
        return calculate_order_details.get('to_c_settlement_actual_income')
