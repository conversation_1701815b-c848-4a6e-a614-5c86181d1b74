from rest_framework import serializers

from utils import multiple_selection_map
from wisdom_v2.enum.service_content_enum import CoachOfferStatusEnum
from wisdom_v2.models import CoachOffer, Resume


class CoachOfferSerializer(serializers.ModelSerializer):
    id = serializers.CharField(help_text='id')
    order_receiving_status = serializers.BooleanField(source='coach.order_receiving_status', help_text='接单状态')
    status = serializers.IntegerField(help_text='加入状态')
    refuse_reason = serializers.CharField(help_text='拒绝原因')
    confirm_time = serializers.DateTimeField(format='%Y-%m-%d %H:%M:%S', help_text='确认时间')
    project_offer_id = serializers.CharField(help_text='项目offer_id')
    coach_info = serializers.SerializerMethodField(help_text='教练信息')
    button_type = serializers.SerializerMethodField(help_text='操作按钮选项')
    max_customer_count = serializers.IntegerField(help_text='最多服务客户数量')

    class Meta:
        model = CoachOffer
        fields = ('id', 'order_receiving_status', 'status', 'max_customer_count', 'refuse_reason', 'confirm_time',
                  'project_offer_id', 'coach_info', 'button_type')

    def get_coach_info(self, obj):
        coach = obj.coach
        resume = Resume.objects.filter(coach_id=coach.pk, deleted=False, is_customization=False).first()
        data = {'coach_auth': resume.coach_auth if resume.coach_auth else None,
                'working_years': resume.working_years if resume.working_years else None,
                'coach_domain': multiple_selection_map.get_multiple_selection_detail('coach_domain',
                                                                                     resume.coach_domain)['show_text'],
                'id': coach.pk, 'coach_name': coach.user.cover_name}
        return data

    def get_button_type(self, obj):
        if not obj.send_time and obj.status == CoachOfferStatusEnum.not_confirm:
            return 1  # 发送 移除 按钮         未发送过消息的教练可以移除
        elif obj.status == CoachOfferStatusEnum.not_confirm:
            return 2  # 再次发送按钮       发送给消息的教练可以再次发送
        else:
            return 3  # 置灰再次发送按钮      其他类型的置灰操作


