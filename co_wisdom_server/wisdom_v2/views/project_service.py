from django.db import transaction
from django.db.models import F
from drf_yasg import openapi
from drf_yasg.utils import swagger_auto_schema
from rest_framework import viewsets
from rest_framework.decorators import action

from utils.api_response import parameter_error_response, success_response, WisdomValidationError
from wisdom_v2.common import project_service_public
from wisdom_v2.models import Project, ProjectMember
from wisdom_v2.models_file import ProjectServiceStage, ProjectServiceContent, ProjectMemberServiceContent, \
    ProjectServiceMember, ServiceStage
from wisdom_v2.views.project_service_action import ProjectServiceStageSerializer


class ProjectServiceViewSet(viewsets.ModelViewSet):
    queryset = ProjectServiceStage.objects.filter(deleted=False).order_by('-created_at')
    serializer_class = ProjectServiceStageSerializer

    @swagger_auto_schema(
        operation_id='修改项目服务',
        operation_summary='修改项目服务',
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'project_id': openapi.Schema(type=openapi.TYPE_NUMBER, description='项目id'),
                'is_stage': openapi.Schema(type=openapi.TYPE_BOOLEAN, description='服务配置类型 T-分阶段 F-不分阶段')
            }
        ),
        tags=['服务配置']
    )
    @action(methods=['post'], detail=False, url_path='update')
    def update_project_service(self, request, *args, **kwargs):
        try:
            project_id = request.data.get('project_id')
            is_stage = request.data.get('is_stage')
            project = Project.objects.get(id=project_id, deleted=False)
        except Project.DoesNotExist:
            return parameter_error_response('项目不存在')
        except Exception as e:
            return parameter_error_response()

        service_stage = ServiceStage.objects.filter(project_id=project_id, deleted=False).first()
        if service_stage and is_stage == service_stage.is_stage:
            return parameter_error_response('服务配置类型一致，无需修改')

        with transaction.atomic():
            if service_stage:
                if is_stage:
                    stage = ProjectServiceStage.objects.create(project_id=project_id, stage_name='阶段名称')
                    ProjectServiceContent.objects.filter(project_id=project_id, deleted=False).update(
                        service_stage=stage)
                    ProjectMemberServiceContent.objects.filter(
                        project_service_members__project_service__project_id=project_id, deleted=False).update(
                        service_stage=stage)
                else:
                    project.project_service_stage.filter(deleted=False).update(deleted=True)
                    
                    ProjectServiceContent.objects.filter(project_id=project_id, deleted=False).update(
                        service_stage=None)
                    ProjectMemberServiceContent.objects.filter(service_stage__project_id=project_id,
                                                               deleted=False).update(service_stage=None)
                    
                service_stage.is_stage = is_stage
                service_stage.save()
                # 同步更新和项目服务绑定的客户的服务阶段数据
                related_project_members = ProjectServiceMember.objects.filter(member_service__project_member__project_id=project_id,
                                                    deleted=False).values_list('member_service__project_member', flat=True)
                ServiceStage.objects.filter(project_member_id__in=related_project_members, deleted=False).update(
                    is_stage=is_stage)

            else:
                ServiceStage.objects.create(project_id=project_id, is_stage=is_stage)

        return success_response()

    @swagger_auto_schema(
        operation_id='服务路径图',
        operation_summary='服务路径图',
        manual_parameters=[
            openapi.Parameter('project_id', openapi.IN_QUERY, description='项目id', type=openapi.TYPE_STRING),
        ],
        tags=['服务配置']
    )
    @action(methods=['get'], detail=False, url_path='path_detail')
    def get_project_service_path(self, request, *args, **kwargs):
        try:

            project_member_id = request.query_params.get('project_member_id')
            project_member = ProjectMember.objects.get(id=project_member_id, deleted=False)
        except ProjectMember.DoesNotExist:
            return parameter_error_response('项目用户不存在')
        except Exception as e:
            return parameter_error_response()

        data = project_service_public.get_path_diagram(project_member)
        return success_response(data)

    @swagger_auto_schema(
        operation_id='添加项目服务阶段',
        operation_summary='添加项目服务阶段',
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'stage_name': openapi.Schema(type=openapi.TYPE_NUMBER, description='阶段名'),
                'project_member_id': openapi.Schema(type=openapi.TYPE_NUMBER, description='项目标识'),
                'project_id': openapi.Schema(type=openapi.TYPE_NUMBER, description='项目用户标识'),
            }
        ),
        tags=['服务配置']
    )
    @action(methods=['post'], detail=False, url_path='stage/add')
    def add_project_service_stage(self, request, *args, **kwargs):
        try:
            stage_name = request.data.get('stage_name')
            project_id = request.data.get('project_id')
            project_member_id = request.data.get('project_member_id')
            if project_id:
                Project.objects.get(id=project_id, deleted=False)
            if project_member_id:
                ProjectMember.objects.get(id=project_member_id, deleted=False)
        except Project.DoesNotExist:
            return parameter_error_response('项目不存在')
        except ProjectMember.DoesNotExist:
            return parameter_error_response('项目用户不存在')
        except Exception as e:
            return parameter_error_response()
        if not stage_name:
            return parameter_error_response('阶段名称不能为空')
        if not project_id and not project_member_id:
            return parameter_error_response('项目标识、服务配置id不能同时为空')

        with transaction.atomic():
            if project_member_id:
                new_stage_order = 0
                old_stage = ProjectServiceStage.objects.filter(project_member_id=project_member_id, deleted=False).order_by('-order').first()
                if old_stage:
                    new_stage_order = old_stage.order + 1
                else:
                    member_service_content = ProjectMemberServiceContent.objects.filter(
                        project_member_id=project_member_id, deleted=False, service_stage__isnull=False).order_by('service_stage__order')
                    if member_service_content.exists():
                        for item in member_service_content.all():
                            project_service_stage = ProjectServiceStage.objects.filter(
                                stage_name=item.service_stage.stage_name, project_member_id=project_member_id, deleted=False).first()
                            if not project_service_stage:
                                project_service_stage = ProjectServiceStage.objects.create(
                                    stage_name=item.service_stage.stage_name, project_member_id=project_member_id, order=new_stage_order)
                                new_stage_order += 1
                            item.service_stage = project_service_stage
                            item.save()
                    ProjectServiceMember.objects.filter(
                        member_service__project_member_id=project_member_id, deleted=False).update(deleted=True)
                now_stage = ProjectServiceStage.objects.create(
                    stage_name=stage_name, order=new_stage_order, project_member_id=project_member_id)

            else:
                old_stage = ProjectServiceStage.objects.filter(project_id=project_id, deleted=False).order_by('-order').first()
                now_stage = ProjectServiceStage.objects.create(
                    stage_name=stage_name, order=old_stage.order + 1 if old_stage else 0, project_id=project_id)

        return success_response(ProjectServiceStageSerializer(now_stage).data)

    @swagger_auto_schema(
        operation_id='修改项目服务阶段',
        operation_summary='修改项目服务阶段',
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'stage_id': openapi.Schema(type=openapi.TYPE_NUMBER, description='阶段id'),
                'stage_name': openapi.Schema(type=openapi.TYPE_NUMBER, description='阶段名'),
                'move_direction': openapi.Schema(type=openapi.TYPE_NUMBER, description='移动方向 1-上移 2-下移'),
                'is_deleted': openapi.Schema(type=openapi.TYPE_NUMBER, description='是否删除'),
                'project_member_id': openapi.Schema(type=openapi.TYPE_NUMBER, description='项目用户标识'),
            }
        ),
        tags=['服务配置']
    )
    @action(methods=['post'], detail=False, url_path='stage/update')
    def update_project_service_stage(self, request, *args, **kwargs):
        try:
            stage_name = request.data.get('stage_name')
            move_direction = request.data.get('move_direction')
            stage_id = request.data.get('stage_id')
            is_deleted = request.data.get('is_deleted')
            project_member_id = request.data.get('project_member_id')
            stage = ProjectServiceStage.objects.get(id=stage_id, deleted=False)
        except ProjectServiceStage.DoesNotExist:
            return parameter_error_response('阶段不存在')
        except Exception as e:
            return parameter_error_response()

        if ProjectServiceStage.objects.filter(
            project_member_id=stage.project_member_id,
                project__id=stage.project_id, deleted=False).count() == 1 and is_deleted:
            return parameter_error_response('只有一个阶段不能删除')

        with transaction.atomic():
            if is_deleted:
                # 用户删除自己的阶段
                if stage.project_member:

                    # 删除所有阶段内容
                    content = ProjectMemberServiceContent.objects.filter(service_stage=stage, deleted=False)
                    for item in content.all():
                        if item.object_ids:
                            for object_id in item.object_ids:
                                project_service_public.del_project_member_service(item, object_id)
                    ProjectServiceMember.objects.filter(member_service__in=content.all()).update(deleted=True)
                    content.update(deleted=True)
                    # 删除关联
                    # 删除阶段
                    stage.deleted = True
                    stage.save()

                # 项目删除自己的阶段
                elif stage.project and not project_member_id:
                    # 项目的阶段直接删除
                    ProjectServiceContent.objects.filter(service_stage=stage).update(deleted=True)
                    # 用户的阶段循环删除
                    service_content = ProjectMemberServiceContent.objects.filter(service_stage=stage)
                    service_content_ids = []
                    for i in service_content.all():
                        if i.object_ids:
                            for item in i.object_ids:
                                project_service_public.del_project_member_service(i, item)
                        service_content_ids.append(i.id)
                    service_content.update(deleted=True)
                    # 删除关联
                    ProjectServiceMember.objects.filter(project_service_id__in=service_content_ids).update(deleted=True)
                    # 删除阶段
                    stage.deleted = True
                    stage.save()
                # 项目用户删除项目的阶段
                elif project_member_id:
                    # 用户阶段下的数据删除
                    content = ProjectMemberServiceContent.objects.filter(
                        service_stage=stage, project_member_id=project_member_id, deleted=False)
                    for item in content.all():
                        if item.object_ids:
                            for object_id in item.object_ids:
                                project_service_public.del_project_member_service(item, object_id)
                    ProjectServiceMember.objects.filter(
                        member_service__in=content.all(), deleted=False).update(deleted=True)
                    content.update(deleted=True)
                return success_response()
            if stage_name:
                stage.stage_name = stage_name
                stage.save()
            if move_direction and move_direction in [1, 2]:
                project_service_public.move_service_stage(stage, move_direction)

        return success_response(ProjectServiceStageSerializer(stage).data)

    # 增加项目服务配置模块
    @swagger_auto_schema(
        operation_id='添加项目服务配置模块',
        operation_summary='添加项目服务配置模块',
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'stage_id': openapi.Schema(type=openapi.TYPE_NUMBER, description='阶段id'),
                'project_service_id': openapi.Schema(type=openapi.TYPE_NUMBER, description='服务配置id'),
                'project_member_ids': openapi.Schema(type=openapi.TYPE_NUMBER, description='适配用户id'),
                'model_type_ids': openapi.Schema(type=openapi.TYPE_ARRAY, description='模块类型id',
                                                 items=openapi.Schema(type=openapi.TYPE_NUMBER))
            }
        ),
        tags=['服务配置']
    )
    @action(methods=['post'], detail=False, url_path='model/add')
    def add_project_service_model(self, request, *args, **kwargs):
        try:
            stage_id = request.data.get('stage_id')
            project_id = request.data.get('project_id')
            project_member_id = request.data.get('project_member_id')
            model_type_ids = request.data.get('model_type_ids')
            project_member_ids = request.data.get('project_member_ids', [])
        except Exception as e:
            return parameter_error_response()
        if not model_type_ids:
            return parameter_error_response('模块类型不能为空')
        if not isinstance(model_type_ids, list):
            return parameter_error_response('模块类型必须为列表')
        if not isinstance(project_member_ids, list):
            return parameter_error_response('适配用户必须为列表')

        if not stage_id and not project_id and not project_member_id:
            return parameter_error_response('参数错误')

        if project_member_id:
            if not ServiceStage.objects.filter(project_member_id=project_member_id, deleted=False).exists():
                return parameter_error_response('请先选择是否分阶段展现服务')
        else:
            if not ServiceStage.objects.filter(project_id=project_id, deleted=False).exists():
                return parameter_error_response('请先选择是否分阶段展现服务')

        with transaction.atomic():
            if project_member_id:
                create_list = []
                for item in model_type_ids:
                    create_list.append(ProjectMemberServiceContent(
                        project_member_id=project_member_id,
                        content_type=item,
                        service_stage_id=stage_id
                    ))
                ProjectMemberServiceContent.objects.bulk_create(create_list)
            else:
                # 更新现有项目服务内容的order值，为新实例留出空间
                ProjectServiceContent.objects.filter(project_id=project_id).update(
                    order=F('order') + len(model_type_ids))

                create_list = []
                for index, item in enumerate(model_type_ids):
                    create_list.append(ProjectServiceContent(
                        project_id=project_id,
                        content_type=item,
                        service_stage_id=stage_id,
                        order=index + 1  # 从1开始，按照列表顺序设置order
                    ))
                project_service_content = ProjectServiceContent.objects.bulk_create(create_list)
                project_service_ids = [item.id for item in project_service_content]

                project_member_service_ids = project_service_public.get_project_member_service_ids(
                    stage_id, project_member_ids, model_type_ids)
                if project_member_service_ids:
                    for p, item in enumerate(project_service_ids):
                        for m in project_member_service_ids:
                            ProjectServiceMember.objects.create(project_service_id=item, member_service_id=m[p])
        return success_response()

    @swagger_auto_schema(
        operation_id='移除项目服务配置模块',
        operation_summary='移除项目服务配置模块',
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'model_id': openapi.Schema(type=openapi.TYPE_NUMBER, description='服务模块id')
            }
        ),
        tags=['服务配置']
    )
    @action(methods=['post'], detail=False, url_path='model/del')
    def del_project_service_model(self, request, *args, **kwargs):
        try:
            model_id = request.data.get('model_id')
            service_content = ProjectServiceContent.objects.get(id=model_id, deleted=False)
        except ProjectServiceContent.DoesNotExist:
            return parameter_error_response('服务模块不存在')
        except Exception as e:
            return parameter_error_response()
        member_content = ProjectMemberServiceContent.objects.filter(
            project_service_members__project_service=service_content, deleted=False)
        for item in member_content.all():
            if item.object_ids:
                for obj_id in item.object_ids:
                    project_service_public.del_project_member_service(item, obj_id)
        member_content.update(deleted=True)
        ProjectServiceMember.objects.filter(project_service=service_content, deleted=False).update(deleted=True)
        service_content.deleted = True
        service_content.save()
        return success_response()

    @swagger_auto_schema(
        operation_id='查看项目服务配置模块可适配的用户',
        operation_summary='查看项目服务配置模块可适配的用户',
        manual_parameters=[
            openapi.Parameter('model_id', openapi.IN_QUERY, description='服务模块id', type=openapi.TYPE_STRING),
        ],
        tags=['服务配置']
    )
    @action(methods=['get'], detail=False, url_path='model/apply_user')
    def get_project_service_model_apply_user(self, request, *args, **kwargs):

        try:
            model_id = request.query_params.get('model_id')
            service_content = ProjectServiceContent.objects.get(id=model_id)
        except ProjectServiceContent.DoesNotExist:
            return parameter_error_response('服务模块不存在')
        except Exception as e:
            return parameter_error_response()

        project_member = ProjectMember.objects.filter(
            project_id=service_content.project_id, deleted=False).order_by('-created_at')

        data = []
        for item in project_member.all():
            is_set = False
            if ProjectServiceMember.objects.filter(
                    project_service=service_content, member_service__project_member=item, deleted=False).exists():
                is_set = True
            data.append({'id': item.id, 'name': item.user.cover_name, 'is_set': is_set})
        return success_response(data)

    @swagger_auto_schema(
        operation_id='编辑项目服务配置模块可适配的用户',
        operation_summary='编辑项目服务配置模块可适配的用户',
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'model_id': openapi.Schema(type=openapi.TYPE_NUMBER, description='服务模块id'),
                'project_member_ids': openapi.Schema(type=openapi.TYPE_NUMBER, description='用户标识')
            }
        ),
        tags=['服务配置']
    )
    @action(methods=['post'], detail=False, url_path='model/apply_user/update')
    def post_project_service_model_apply_user(self, request, *args, **kwargs):
        try:
            model_id = request.data.get('model_id')
            service_content = ProjectServiceContent.objects.get(id=model_id, deleted=False)
            project_member_ids = request.data.get('project_member_ids', [])
        except ProjectServiceContent.DoesNotExist:
            return parameter_error_response('服务模块不存在')
        except Exception as e:
            return parameter_error_response()

        project_service_member = ProjectServiceMember.objects.filter(project_service=service_content, deleted=False)

        old_project_member_ids = list(project_service_member.values_list('member_service__project_member_id', flat=True))

        # 删除用户列表
        del_member_list = list(set(old_project_member_ids).difference(set(project_member_ids)))
        # 新增用户列表
        add_member_list = list(set(project_member_ids).difference(set(old_project_member_ids)))

        with transaction.atomic():
            if del_member_list:
                for item in del_member_list:
                    member_service_content = ProjectMemberServiceContent.objects.filter(
                        project_service_members__project_service=service_content, project_member_id=item, deleted=False).first()
                    if member_service_content.object_ids:
                        for object_id in member_service_content.object_ids:
                            project_service_public.del_project_member_service(member_service_content, object_id)
                    member_service_content.deleted = True
                    member_service_content.save()
                project_service_member.filter(member_service__project_member_id__in=del_member_list).update(deleted=True)

            if add_member_list:
                for add_member_id in add_member_list:
                    project_member = ProjectMember.objects.get(id=add_member_id, deleted=False)
                    # 用户是否自定义服务阶段
                    if project_member.project_service_stage.filter(deleted=False).exists():
                        raise WisdomValidationError(
                            f'{project_member.user.cover_name}用户已经自定义服务，无法添加服务内容')
                    else:
                        # 如果用户没有配置服务阶段，查询有没有自定义服务模块。
                        if (project_member.service_stage.filter(deleted=False, is_stage=False).exists() and
                                project_member.project_member_service_content.filter(
                                    deleted=False, project_service_members__isnull=True).exists()):
                            raise WisdomValidationError(
                                f'{project_member.user.cover_name}用户已经自定义服务，无法添加服务内容')
                    # 同阶段下删除数据 无阶段下不删除数据
                    if service_content.service_stage_id:
                        member_content = ProjectMemberServiceContent.objects.filter(
                            service_stage_id=service_content.service_stage_id, content_type=service_content.content_type,
                            project_member=project_member, deleted=False)
                        for item in member_content.all():
                            if item.object_ids:
                                for i in item.object_ids:
                                    project_service_public.del_project_member_service(item, i)
                        member_content.update(deleted=True)
                        # 删除关联
                        ProjectServiceMember.objects.filter(
                            project_service=service_content, member_service__project_member=project_member).update(
                            deleted=True)

                    member_service_content = ProjectMemberServiceContent.objects.create(
                        project_member_id=add_member_id,
                        service_stage=service_content.service_stage,
                        content_type=service_content.content_type, deleted=False)

                    if service_content.content:
                        state = project_service_public.project_service_content_validate(
                            service_content, service_content.content, project_member)
                        if state:
                            raise WisdomValidationError(state)
                        object_ids = project_service_public.project_member_service_content_create(
                            service_content.content, member_service_content)
                    else:
                        object_ids = []
                    object_ids = [str(i) for i in object_ids]
                    member_service_content.object_ids = object_ids
                    member_service_content.save()
                    ProjectServiceMember.objects.create(
                        project_service=service_content, member_service=member_service_content, deleted=False)
                    user_stage = ServiceStage.objects.filter(project_member=project_member, deleted=False).first()
                    if not user_stage:
                        is_stage = True if service_content.service_stage else False
                        ServiceStage.objects.create(
                            project_member=project_member, is_stage=is_stage, deleted=False)
                    else:
                        user_stage.is_stage = True if service_content.service_stage else False
                        user_stage.save()
        return success_response()

    @swagger_auto_schema(
        operation_id='添加项目服务配置模块数据',
        operation_summary='添加项目服务配置模块数据',
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'model_id': openapi.Schema(type=openapi.TYPE_NUMBER, description='服务模块id'),
                'model_data': openapi.Schema(type=openapi.TYPE_OBJECT, description='服务模块数据'),
            }
        ),
        tags=['服务配置']
    )
    @action(methods=['post'], detail=False, url_path='content/update')
    def update_project_service_content(self, request, *args, **kwargs):
        try:
            model_id = request.data.get('model_id')
            model_content = request.data.get('model_content')
            service_model = ProjectServiceContent.objects.get(id=model_id, deleted=False)
        except ProjectServiceContent.DoesNotExist:
            return parameter_error_response('服务模块不存在')
        except Exception as e:
            return parameter_error_response()
        if not isinstance(model_content, list):
            return parameter_error_response('模块数据必须为列表')

        state = project_service_public.project_service_content_validate(service_model, model_content, None, False)
        if state:
            return parameter_error_response(state)

        with transaction.atomic():
            model_content = project_service_public.update_project_member_service_content(model_content, service_model)
            project_service_public.create_project_member_service_content(model_content, service_model)
            service_model.content = model_content
            service_model.save()
        return success_response()

    @swagger_auto_schema(
        operation_id='获取项目服务配置详情',
        operation_summary='获取项目服务配置详情',
        manual_parameters=[
            openapi.Parameter('project_id', openapi.IN_QUERY, description='项目标识', type=openapi.TYPE_STRING),
        ],
        tags=['服务配置']
    )
    @action(methods=['get'], detail=False, url_path='details')
    def get_project_service_details(self, request, *args, **kwargs):
        try:
            project_id = request.query_params.get('project_id')
            project_member_id = request.query_params.get('project_member_id')
        except Exception as e:
            return parameter_error_response()
        if not project_id and not project_member_id:
            return parameter_error_response('参数错误')
        if project_member_id:
            data = project_service_public.get_project_member_service_content_all(project_member_id=project_member_id)
        else:
            data = project_service_public.get_project_service_content_all(project_id=project_id)
        return success_response(data)


    @swagger_auto_schema(
        operation_id='移动项目服务配置模块数据',
        operation_summary='移动项目服务配置模块数据',
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'model_id': openapi.Schema(type=openapi.TYPE_NUMBER, description='服务模块id'),
                'move_direction': openapi.Schema(type=openapi.TYPE_OBJECT, description='移动方向 1-上移 2-下移'),
                'move_distance': openapi.Schema(type=openapi.TYPE_OBJECT, description='移动步长'),
            }
        ),
        tags=['服务配置']
    )
    @action(methods=['post'], detail=False, url_path='model/order/update')
    def update_project_service_order(self, request, *args, **kwargs):
        try:
            move_direction = request.data.get('move_direction')
            move_distance = int(request.data.get('move_distance', 1))
            model_id = request.data.get('model_id')
            service_model = ProjectServiceContent.objects.get(id=model_id, deleted=False)
        except ProjectServiceContent.DoesNotExist:
            return parameter_error_response('服务模块不存在')
        except Exception as e:
            return parameter_error_response()

        if move_direction not in [1, 2]:
            return parameter_error_response('移动方向错误')

        project_service_public.move_service_model(service_model, move_direction, move_distance)

        return success_response()
