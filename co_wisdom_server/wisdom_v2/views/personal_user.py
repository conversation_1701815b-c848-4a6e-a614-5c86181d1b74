from decimal import Decimal

from django.db.models import Q
from drf_yasg import openapi
from rest_framework import viewsets
from drf_yasg.utils import swagger_auto_schema
from rest_framework.decorators import action

from utils import task
from utils.messagecenter import getui
from utils.pagination import StandardResultsSetPagination
from wisdom_v2.app_views import app_order_action
from wisdom_v2.common import order_public
from wisdom_v2.enum.pay_enum import OrderStatusEnum
from wisdom_v2.models import PersonalUser, Order, ProjectInterview, WorkWechatUser
from utils.api_response import success_response, parameter_error_response
from wisdom_v2.views import constant
from wisdom_v2.views.constant import ATTR_STATUS_INTERVIEW_CANCEL
from wisdom_v2.views.personal_user_action import PersonalUserViewSetSerializers, PersonalUserOrderViewSetSerializers


class PersonalUserViewSet(viewsets.ModelViewSet):
    queryset = PersonalUser.objects.filter(deleted=False).order_by('-created_at')
    serializer_class = PersonalUserViewSetSerializers

    @swagger_auto_schema(
        operation_id='个人用户列表查询',
        operation_summary='个人用户列表查询',
        manual_parameters=[
            openapi.Parameter('user_name', openapi.IN_QUERY, description='用户名', type=openapi.TYPE_STRING),
            openapi.Parameter('phone', openapi.IN_QUERY, description='手机号', type=openapi.TYPE_STRING),
            openapi.Parameter('referrer_name', openapi.IN_QUERY, description='推荐人姓名', type=openapi.TYPE_STRING),
            openapi.Parameter('nickname', openapi.IN_QUERY, description='昵称', type=openapi.TYPE_STRING),
            openapi.Parameter('email', openapi.IN_QUERY, description='邮箱', type=openapi.TYPE_STRING),
            openapi.Parameter('start_time', openapi.IN_QUERY, description='开始时间', type=openapi.TYPE_STRING),
            openapi.Parameter('end_time', openapi.IN_QUERY, description='结束时间', type=openapi.TYPE_STRING),
            openapi.Parameter('source', openapi.IN_QUERY, description='来源 1-自然流量 2-客户推荐 3-教练推荐 4-教练邀请 ', type=openapi.TYPE_STRING),
            openapi.Parameter('page', openapi.IN_QUERY, description='第几页', type=openapi.TYPE_NUMBER),
            openapi.Parameter('page_size', openapi.IN_QUERY, description='每页显示条数默认15条', type=openapi.TYPE_NUMBER)
        ],
        tags=['个人用户相关']
    )
    def list(self, request, *args, **kwargs):
        try:
            user_name = request.query_params.get('user_name')
            phone = request.query_params.get('phone')
            referrer_name = request.query_params.get('referrer_name')
            source = request.query_params.get('source')
            start_time = request.query_params.get('start_time')
            end_time = request.query_params.get('end_time')
            nickname = request.query_params.get('nickname')
            email = request.query_params.get('email')

            obj = self.get_queryset()
            if user_name:
                obj = obj.filter(user__true_name__icontains=user_name)
            if phone:
                obj = obj.filter(user__phone__icontains=phone)
            if referrer_name:
                obj = obj.filter(invite__referrer__true_name__icontains=referrer_name)
            if nickname:
                obj = obj.filter(nickname__icontains=nickname)
            if email:
                obj = obj.filter(email__icontains=email)
            if source:
                obj = obj.filter(source__in=source.split(','))
            if start_time:
                obj = obj.filter(created_at__gte=start_time)
            if end_time:
                obj = obj.filter(created_at__lte=end_time)

        except Exception as e:
            return parameter_error_response(str(e))

        # 分页
        paginator = StandardResultsSetPagination()
        page_list = paginator.paginate_queryset(obj, self.request)
        serializer = self.get_serializer(page_list, many=True)
        response = paginator.get_paginated_response(serializer.data)
        return success_response(response)

    @swagger_auto_schema(
        operation_id='个人用户订单列表列表查询',
        operation_summary='个人用户订单列表查询',
        manual_parameters=[
            openapi.Parameter('user_name', openapi.IN_QUERY, description='用户名', type=openapi.TYPE_STRING),
            openapi.Parameter('phone', openapi.IN_QUERY, description='手机号', type=openapi.TYPE_STRING),
            openapi.Parameter('coach_name', openapi.IN_QUERY, description='教练姓名', type=openapi.TYPE_STRING),
            openapi.Parameter('order_id', openapi.IN_QUERY, description='订单id', type=openapi.TYPE_STRING),
            openapi.Parameter('activity_name', openapi.IN_QUERY, description='活动名称', type=openapi.TYPE_STRING),
            openapi.Parameter('status', openapi.IN_QUERY, description='订单状态', type=openapi.TYPE_NUMBER),
            openapi.Parameter('start_time', openapi.IN_QUERY, description='开始时间', type=openapi.TYPE_STRING),
            openapi.Parameter('end_time', openapi.IN_QUERY, description='结束时间', type=openapi.TYPE_STRING),
            openapi.Parameter('page', openapi.IN_QUERY, description='第几页', type=openapi.TYPE_NUMBER),
            openapi.Parameter('page_size', openapi.IN_QUERY, description='每页显示条数默认15条', type=openapi.TYPE_NUMBER)
        ],
        tags=['个人用户相关']
    )
    @action(methods=['get'], detail=False, url_path='orders')
    def get_orders_list(self, request, *args, **kwargs):
        try:
            user_name = request.query_params.get('user_name')
            coach_name = request.query_params.get('coach_name')
            phone = request.query_params.get('phone')
            start_time = request.query_params.get('start_time')
            end_time = request.query_params.get('end_time')
            order_id = request.query_params.get('order_id')
            status = request.query_params.get('status')
            activity_name = request.query_params.get('activity_name')

        except Exception as e:
            return parameter_error_response(str(e))

        obj = Order.objects

        if order_id:
            obj = obj.filter(order_no__icontains=order_id)
        if user_name:
            obj = obj.filter(public_attr__user__true_name__icontains=user_name)
        if coach_name:
            obj = obj.filter(public_attr__target_user__true_name__icontains=coach_name)
        if phone:
            obj = obj.filter(public_attr__user__phone__icontains=phone)
        if start_time:
            obj = obj.filter(created_at__gte=start_time)
        if end_time:
            obj = obj.filter(created_at__lte=end_time)
        if activity_name:
            obj = obj.filter(activity__theme__icontains=activity_name)
        if status:
            tmp_q = Q()
            for item in status.split(','):
                # 待支付的查询未付款，订单未删除的
                if int(item) == constant.ADMIN_ORDER_STATE_PENDING_PAY:
                    tmp_q.add(Q(status=OrderStatusEnum.pending_pay.value, deleted=False), Q.OR)

                # 已关闭的查询未付款，订单已删除的
                elif int(item) == constant.ADMIN_ORDER_STATE_CLOSURE:
                    tmp_q.add(Q(status=OrderStatusEnum.pending_pay.value, deleted=True), Q.OR)

                # 已完成的查询已付款，订单未删除的
                elif int(item) == constant.ADMIN_ORDER_STATE_COMPLETE:
                    tmp_q.add(Q(status=OrderStatusEnum.paid.value, deleted=False), Q.OR)

                # 已退款的查询待退款的和已退款的
                elif int(item) == constant.ADMIN_ORDER_STATE_REFUND:
                    tmp_q.add(Q(status__in=[OrderStatusEnum.under_refund.value, OrderStatusEnum.refunded.value], deleted=False), Q.OR)
            obj = obj.filter(tmp_q)

        self.serializer_class = PersonalUserOrderViewSetSerializers

        # 分页
        paginator = StandardResultsSetPagination()
        page_list = paginator.paginate_queryset(obj.order_by('-created_at'), self.request)
        serializer = self.get_serializer(page_list, many=True)
        response = paginator.get_paginated_response(serializer.data)
        return success_response(response)

    @swagger_auto_schema(
        operation_id='个人用户订单退款',
        operation_summary='个人用户订单退款',
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'order_id': openapi.Schema(type=openapi.TYPE_STRING, description='订单标识'),
            }),
        tags=['个人用户相关']
    )
    @action(methods=['post'], detail=False, url_path='orders/refund')
    def refund_personal_user_order(self, request, *args, **kwargs):
        try:
            order_id = request.data.get('order_id')
            order = Order.objects.get(order_no=order_id, deleted=False)
        except Order.DoesNotExist:
            return parameter_error_response('订单不存在')
        except Exception as e:
            return parameter_error_response(str(e))

        err_msg = order_public.refund_order(order, '后台退款')
        if err_msg:
            return parameter_error_response(err_msg)

        params = {
            'url': request.get_full_path(),
            'token': request.META.get('HTTP_AUTHORIZATION'),
            'user_id': request.user.pk,
            'true_name': request.user.cover_name,
            'body': request.body.decode('utf8') if request.body.decode('utf8') else {},
            'params': request.GET if request.GET else {},
            'message': '订单退款'
        }
        task.send_business_api_sls_log.delay(params)

        return success_response('退款成功')

