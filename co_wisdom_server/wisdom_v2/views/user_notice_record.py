from django.db import transaction
from drf_yasg import openapi
from rest_framework import viewsets
from drf_yasg.utils import swagger_auto_schema
from rest_framework.decorators import action

from utils.pagination import StandardResultsSetPagination
from wisdom_v2.common import user_notice_record_public
from wisdom_v2.models import UserNoticeRecord, Project
from utils.api_response import success_response, parameter_error_response
from wisdom_v2.views.user_notice_record_action import UserNoticeRecordSerializers


class UserNoticeRecordBaseViewSet(viewsets.ModelViewSet):
    queryset = UserNoticeRecord.objects.filter(deleted=False).order_by('-created_at')
    serializer_class = UserNoticeRecordSerializers

    @swagger_auto_schema(
        operation_id='查看用户通知记录列表',
        operation_summary='查看用户通知记录列表',
        manual_parameters=[
            openapi.Parameter(
                'project_id', openapi.IN_QUERY, description='项目id', type=openapi.TYPE_NUMBER, required=True),
            openapi.Parameter('type', openapi.IN_QUERY, description='通知类型 1-账号开通通知 2-改变观察问卷 3-利益相关者调研问卷 4-教练简历匹配通知', type=openapi.TYPE_NUMBER),
            openapi.Parameter('channel', openapi.IN_QUERY, description='发送渠道类型 | 1-企业微信 2-短信 3-邮件', type=openapi.TYPE_NUMBER),
            openapi.Parameter('user_id', openapi.IN_QUERY, description='用户id', type=openapi.TYPE_NUMBER),
            openapi.Parameter('page', openapi.IN_QUERY, description='第几页', type=openapi.TYPE_NUMBER),
            openapi.Parameter('page_size', openapi.IN_QUERY, description='每页显示条数默认15条', type=openapi.TYPE_NUMBER)
        ],
        tags=['用户通知相关']
    )
    def list(self, request, *args, **kwargs):
        try:
            project_id = request.query_params.get('project_id')
            user_id = request.query_params.get('user_id')
            notice_type = request.query_params.get('type')
            channel = request.query_params.get('channel')

            obj = self.get_queryset()
            if project_id:
                obj = obj.filter(project_id=project_id)
            if user_id:
                obj = obj.filter(user_id=user_id)
            if notice_type:
                obj = obj.filter(type=notice_type)
            if channel:
                obj = obj.filter(channel=channel)

        except Exception as e:
            return parameter_error_response(str(e))

        # 更新未读状态
        obj.update(is_now_modify=False)

        # 分页
        paginator = StandardResultsSetPagination()
        page_list = paginator.paginate_queryset(obj, self.request)
        serializer = self.get_serializer(page_list, many=True)
        response = paginator.get_paginated_response(serializer.data)
        return success_response(response)

    @swagger_auto_schema(
        operation_id='查看用户通知记录详情',
        operation_summary='查看用户通知记录详情',
        manual_parameters=[
            openapi.Parameter(
                'id', openapi.IN_QUERY, description='通知id', type=openapi.TYPE_NUMBER, required=True),
            openapi.Parameter('page', openapi.IN_QUERY, description='第几页', type=openapi.TYPE_NUMBER),
            openapi.Parameter('page_size', openapi.IN_QUERY, description='每页显示条数默认15条', type=openapi.TYPE_NUMBER)
        ],
        tags=['用户通知相关']
    )
    @action(methods=['get'], detail=False, url_path='detail', authentication_classes=[])
    def get_details(self, request, *args, **kwargs):
        try:
            notice_id = request.query_params.get('id')
            user_notice = UserNoticeRecord.objects.get(pk=notice_id, deleted=False)

        except Exception as e:
            return parameter_error_response(str(e))
        serializer = self.get_serializer(user_notice)
        return success_response(serializer.data)


    @swagger_auto_schema(
        operation_id='更新教练通知信息',
        operation_summary='更新教练通知信息',
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'id': openapi.Schema(type=openapi.TYPE_NUMBER, description='通知id'),
                'resume_ids': openapi.Schema(type=openapi.TYPE_NUMBER, description='教练简历ids'),
                'selected_resume_id': openapi.Schema(type=openapi.TYPE_NUMBER, description='选中教练简历ids'),
                'feedback': openapi.Schema(type=openapi.TYPE_NUMBER, description='反馈'),
            }
        ),
        tags=['用户通知相关']
    )
    @action(methods=['post'], detail=False, url_path='coach/update', authentication_classes=[])
    def user_notice_record_update_coach(self, request, *args, **kwargs):
        try:
            notice_id = request.data.get('id')
            feedback = request.data.get('feedback')
            user_notice = UserNoticeRecord.objects.get(pk=notice_id, deleted=False)
        except UserNoticeRecord.DoesNotExist:
            return parameter_error_response('通知不存在')
        except Exception as e:
            return parameter_error_response()
        with transaction.atomic():
            if feedback:
                user_notice.feedback = feedback
                user_notice.save()
            user_notice_record_public.update_coach_notice(user_notice, request.data)
        serializer = self.get_serializer(user_notice)
        return success_response(serializer.data)

    @swagger_auto_schema(
        operation_id='项目下是否有未查看的最新通知',
        operation_summary='项目下是否有未查看的最新通知',
        manual_parameters=[
            openapi.Parameter(
                'project_id', openapi.IN_QUERY, description='项目id', type=openapi.TYPE_NUMBER, required=True),
            openapi.Parameter('notice_type', openapi.IN_QUERY, description='通知类型 1-账号开通通知 2-改变观察问卷 3-利益相关者调研问卷 4-教练简历匹配通知', type=openapi.TYPE_NUMBER),
            openapi.Parameter('channel', openapi.IN_QUERY, description='发送渠道类型 | 1-企业微信 2-短信 3-邮件', type=openapi.TYPE_NUMBER),
        ],
        tags=['用户通知相关']
    )
    @action(methods=['get'], detail=False, url_path='remind')
    def user_notice_record_remind(self, request, *args, **kwargs):
        try:
            project_id = request.query_params.get('project_id')
            notice_type = request.query_params.get('notice_type')
            channel = request.query_params.get('channel')
            Project.objects.get(pk=project_id, deleted=False)
        except Project.DoesNotExist:
            return parameter_error_response('项目不存在')
        except Exception as e:
            return parameter_error_response()

        queryset = self.get_queryset()
        queryset = queryset.filter(
            project_id=project_id, type=notice_type, channel=channel, is_now_modify=True).exists()
        return success_response({'is_latest_record': queryset})
