from rest_framework import serializers
from wisdom_v2.models import Role, UserBackend


class RoleListSerializer(serializers.ModelSerializer):
    id = serializers.IntegerField(help_text='id')
    name = serializers.CharField(help_text='报告名称')
    user_count = serializers.SerializerMethodField(help_text='人数')

    class Meta:
        model = Role
        fields = ['id', 'name', 'user_count']

    def get_user_count(self, obj):
        return len(set(obj.role_2_user.filter(deleted=False).values_list('user_id', flat=True)))
