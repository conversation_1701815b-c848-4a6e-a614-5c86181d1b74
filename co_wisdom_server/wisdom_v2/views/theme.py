from drf_yasg import openapi
from rest_framework.decorators import action
from drf_yasg.utils import swagger_auto_schema
from rest_framework import viewsets

from wisdom_v2.views.theme_actions import ThemeSerializer, ArticleThemeSerializer, ArticleListSerializer, \
    CoachThemeSerializer, ThemeCoachListSerializer
from wisdom_v2.models import Theme, Article, ArticleThemeRelation, Coach
from utils.pagination import StandardResultsSetPagination
from utils.api_response import success_response, parameter_error_response


class ThemeViewSet(viewsets.ModelViewSet):
    queryset = Theme.objects.filter(deleted=False).order_by('-created_at')
    serializer_class = ThemeSerializer

    @swagger_auto_schema(
        operation_id='后台主题列表',
        operation_summary='后台主题列表',
        manual_parameters=[
            openapi.Parameter('name	', openapi.IN_QUERY, description='名称', type=openapi.TYPE_STRING),
            openapi.Parameter('page', openapi.IN_QUERY, description='第几页', type=openapi.TYPE_NUMBER),
            openapi.Parameter('page_size', openapi.IN_QUERY, description='每页显示条数默认15条', type=openapi.TYPE_NUMBER),
        ],
        tags=['后台主题管理']
    )
    def list(self, request, *args, **kwargs):
        theme = self.get_queryset()
        if request.query_params.get('name', None):
            theme = theme.filter(name__icontains=request.query_params.get('name'))
        # 分页
        paginator = StandardResultsSetPagination()
        page_list = paginator.paginate_queryset(theme, self.request)
        serializer = self.get_serializer(page_list, many=True)
        response = paginator.get_paginated_response(serializer.data)
        return success_response(response)

    @swagger_auto_schema(
        operation_id='后台主题详情',
        operation_summary='后台主题详情',
        manual_parameters=[
            openapi.Parameter('theme_id	', openapi.IN_QUERY, description='名称', type=openapi.TYPE_STRING),
        ],
        tags=['后台主题管理']
    )
    @action(methods=['get'], detail=False, url_path='detail')
    def theme_detail(self, request, *args, **kwargs):
        try:
            theme_id = request.query_params.get('theme_id')
            theme = Theme.objects.get(id=theme_id)
        except (KeyError, Theme.DoesNotExist):
            return parameter_error_response()
        serializer = self.get_serializer(theme)
        return success_response(serializer.data)

    @swagger_auto_schema(
        operation_id='主题关联文章',
        operation_summary='主题关联文章',
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'theme_id': openapi.Schema(type=openapi.TYPE_NUMBER, description='主题id'),
                'article_ids': openapi.Schema(type=openapi.TYPE_STRING, description='文章id列表'),
            }
        ),
        tags=['后台主题管理']
    )
    @action(methods=['post'], detail=False, url_path='article/relation')
    def article_relation(self, request, *args, **kwargs):
        try:
            article_ids = request.data['article_ids']
            theme_id = request.data['theme_id']
            theme = Theme.objects.get(id=theme_id)
        except (KeyError,  Theme.DoesNotExist):
            return parameter_error_response()
        articles = Article.objects.filter(id__in=article_ids, deleted=False)
        if articles.exists():
            for article in articles:
                if not ArticleThemeRelation.objects.filter(article=article, theme=theme, deleted=False).exists():
                    ArticleThemeRelation.objects.create(article=article, theme=theme)
        return success_response()

    @swagger_auto_schema(
        operation_id='主题关联文章-文章列表（点击关联文章请求的文章列表）',
        operation_summary='主题关联文章-文章列表（点击关联文章请求的文章列表）',
        manual_parameters=[
            openapi.Parameter('theme_id', openapi.IN_QUERY, description='主题id', type=openapi.TYPE_NUMBER),
        ],
        tags=['后台主题管理']
    )
    @action(methods=['get'], detail=False, url_path='article_list', serializer_class=ArticleListSerializer)
    def article_list(self, request, *args, **kwargs):
        try:
            theme_id = request.query_params['theme_id']
            name = request.query_params.get('name')
            theme = Theme.objects.get(id=theme_id)
        except (KeyError, Theme.DoesNotExist):
            return parameter_error_response()
        exclude_article_id = list(ArticleThemeRelation.objects.filter(
            theme=theme, article__isnull=False, deleted=False).values_list('article_id', flat=True))
        article = Article.objects.filter(deleted=False).exclude(id__in=exclude_article_id)
        if name:
            article = article.filter(title__icontains=name)
        # 分页
        paginator = StandardResultsSetPagination()
        page_list = paginator.paginate_queryset(article, self.request)
        serializer = self.get_serializer(page_list, many=True)
        response = paginator.get_paginated_response(serializer.data)
        return success_response(response)

    @swagger_auto_schema(
        operation_id='编辑关联文章权重/移除关联文章',
        operation_summary='编辑关联文章权重/移除关联文章',
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'id': openapi.Schema(type=openapi.TYPE_NUMBER, description='导航ID'),
                'weight': openapi.Schema(type=openapi.TYPE_STRING, description='名称'),
                'deleted': openapi.Schema(type=openapi.TYPE_BOOLEAN, description='是否删除'),
            }
        ),
        tags=['后台主题管理']
    )
    @action(methods=['post'], detail=False, url_path='article/update')
    def update_article(self, request, *args, **kwargs):
        try:
            id = request.data['id']
            weight = request.data.get('weight')
            deleted = request.data.get('deleted')
            relation_theme = ArticleThemeRelation.objects.get(id=id, article__isnull=False)
        except (KeyError, ArticleThemeRelation.DoesNotExist):
            return parameter_error_response()
        if weight:
            relation_theme.weight = weight
        if deleted:
            relation_theme.deleted = True
        relation_theme.save()
        return success_response()

    @swagger_auto_schema(
        operation_id='主题已关联文章列表',
        operation_summary='主题已关联文章列表',
        manual_parameters=[
            openapi.Parameter('theme_id', openapi.IN_QUERY, description='主题id', type=openapi.TYPE_NUMBER),
        ],
        tags=['后台主题管理']
    )
    @action(methods=['get'], detail=False, url_path='article', serializer_class=ArticleThemeSerializer)
    def relation_article_list(self, request, *args, **kwargs):
        try:
            theme_id = request.query_params['theme_id']
            instance = Theme.objects.get(id=theme_id)
        except (KeyError, Theme.DoesNotExist):
            return parameter_error_response()
        relation_article_list = ArticleThemeRelation.objects.filter(
            theme=instance, deleted=False,
            article__isnull=False).distinct().order_by('-weight')
        # 分页
        paginator = StandardResultsSetPagination()
        page_list = paginator.paginate_queryset(relation_article_list, self.request)
        serializer = self.get_serializer(page_list, many=True)
        response = paginator.get_paginated_response(serializer.data)
        return success_response(response)

    @swagger_auto_schema(
        operation_id='修改主题信息',
        operation_summary='修改主题信息',
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'id': openapi.Schema(type=openapi.TYPE_NUMBER, description='导航ID'),
                'name': openapi.Schema(type=openapi.TYPE_STRING, description='名称'),
                'image_url': openapi.Schema(type=openapi.TYPE_STRING, description='图片url'),
                'brief': openapi.Schema(type=openapi.TYPE_STRING, description='跳转url'),
                'deleted': openapi.Schema(type=openapi.TYPE_BOOLEAN, description='是否删除'),
            }
        ),
        tags=['后台主题管理']
    )
    @action(methods=['post'], detail=False, url_path='update')
    def theme_update(self, request, *args, **kwargs):
        try:
            instance = Theme.objects.get(pk=request.data.get('id', 0))
        except Theme.DoesNotExist:
            return parameter_error_response()
        if len(request.data.get('name', '')) > 50:
            return parameter_error_response('名称不可超过50个字')
        brief = request.data.get('brief', None)
        if brief and len(brief) > 100:
            return parameter_error_response('简介不可超过100个字')
        Theme.objects.filter(pk=request.data.get('id', 0)).update(**request.data)
        return success_response()

    @swagger_auto_schema(
        operation_id='创建主题',
        operation_summary='创建主题',
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'name': openapi.Schema(type=openapi.TYPE_NUMBER, description='名称'),
                'image_url': openapi.Schema(type=openapi.TYPE_NUMBER, description='图片url'),
                'brief': openapi.Schema(type=openapi.TYPE_STRING, description='简介'),
            }
        ),
        tags=['后台主题管理']
    )
    def create(self, request, *args, **kwargs):
        name = request.data.get('name')
        brief = request.data.get('brief', None)
        image_url = request.data.get('image_url')
        if not name:
            return parameter_error_response('主题名称和简介不能为空')
        if len(name) > 50:
            return parameter_error_response('主题名称不能超过50个字')
        if len(brief) > 100:
            return parameter_error_response('主题简介不能超过100个字')
        data = {"name": name}
        if image_url:
            data['image_url'] = image_url
        if brief:
            data['brief'] = brief
        theme = Theme.objects.create(**data)
        serializer = self.get_serializer(theme)
        return success_response(serializer.data)

    @swagger_auto_schema(
        operation_id='主题已关联教练列表',
        operation_summary='主题已关联教练列表',
        manual_parameters=[
            openapi.Parameter('theme_id', openapi.IN_QUERY, description='主题id', type=openapi.TYPE_NUMBER),
        ],
        tags=['后台主题管理']
    )
    @action(methods=['get'], detail=False, url_path='coach', serializer_class=CoachThemeSerializer)
    def relation_coach_list(self, request, *args, **kwargs):
        try:
            theme_id = request.query_params['theme_id']
            instance = Theme.objects.get(id=theme_id)
        except (KeyError, Theme.DoesNotExist):
            return parameter_error_response()
        relation_coach_list = ArticleThemeRelation.objects.filter(
            theme=instance, deleted=False,
            coach__isnull=False).distinct().order_by('-weight')
        # 分页
        paginator = StandardResultsSetPagination()
        page_list = paginator.paginate_queryset(relation_coach_list, self.request)
        serializer = self.get_serializer(page_list, many=True)
        response = paginator.get_paginated_response(serializer.data)
        return success_response(response)

    @swagger_auto_schema(
        operation_id='编辑关联教练权重/移除关联教练',
        operation_summary='编辑关联教练权重/移除关联教练',
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'id': openapi.Schema(type=openapi.TYPE_NUMBER, description='导航ID'),
                'weight': openapi.Schema(type=openapi.TYPE_STRING, description='权重'),
                'deleted': openapi.Schema(type=openapi.TYPE_STRING, description='图片url'),
            }
        ),
        tags=['后台主题管理']
    )
    @action(methods=['post'], detail=False, url_path='coach/update')
    def update_coach(self, request, *args, **kwargs):
        try:
            id = request.data['id']
            weight = request.data.get('weight')
            deleted = request.data.get('deleted')
            relation_theme = ArticleThemeRelation.objects.get(id=id, coach__isnull=False)
        except (KeyError, ArticleThemeRelation.DoesNotExist):
            return parameter_error_response()
        if weight:
            relation_theme.weight = weight
        if deleted:
            relation_theme.deleted = True
        relation_theme.save()
        return success_response()

    @swagger_auto_schema(
        operation_id='主题关联教练-教练列表（点击关联教练请求的教练列表）',
        operation_summary='主题关联教练-教练列表（点击关联教练请求的教练列表）',
        manual_parameters=[
            openapi.Parameter('theme_id', openapi.IN_QUERY, description='主题id', type=openapi.TYPE_NUMBER),
        ],
        tags=['后台主题管理']
    )
    @action(methods=['get'], detail=False, url_path='coach_list', serializer_class=ThemeCoachListSerializer)
    def coach_list(self, request, *args, **kwargs):
        try:
            theme_id = request.query_params['theme_id']
            name = request.query_params.get('name')
            theme = Theme.objects.get(id=theme_id)
        except (KeyError, Theme.DoesNotExist):
            return parameter_error_response()
        exclude_coach_id = list(ArticleThemeRelation.objects.filter(
            theme=theme, coach__isnull=False, deleted=False).values_list('coach_id', flat=True))
        coach = Coach.objects.filter(deleted=False).exclude(id__in=exclude_coach_id)
        if name:
            coach = coach.filter(user__true_name__icontains=name)
        # 分页
        paginator = StandardResultsSetPagination()
        page_list = paginator.paginate_queryset(coach, self.request)
        serializer = self.get_serializer(page_list, many=True)
        response = paginator.get_paginated_response(serializer.data)
        return success_response(response)

    @swagger_auto_schema(
        operation_id='主题关联教练',
        operation_summary='主题关联教练',
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'theme_id': openapi.Schema(type=openapi.TYPE_NUMBER, description='主题id'),
                'coach_ids': openapi.Schema(type=openapi.TYPE_STRING, description='教练id列表'),
            }
        ),
        tags=['后台主题管理']
    )
    @action(methods=['post'], detail=False, url_path='coach/relation')
    def coach_relation(self, request, *args, **kwargs):
        try:
            coach_ids = request.data['coach_ids']
            theme_id = request.data['theme_id']
            theme = Theme.objects.get(id=theme_id)
        except (KeyError, Theme.DoesNotExist):
            return parameter_error_response()
        coaches = Coach.objects.filter(id__in=coach_ids, deleted=False)
        if coaches.exists():
            for coach in coaches:
                if not ArticleThemeRelation.objects.filter(coach=coach, theme=theme, deleted=False).exists():
                    ArticleThemeRelation.objects.create(coach=coach, theme=theme)
        return success_response()