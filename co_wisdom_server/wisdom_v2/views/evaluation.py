import redis

from drf_yasg import openapi
from drf_yasg.utils import swagger_auto_schema
from rest_framework.decorators import action

from utils.message.email import message_send_email_base
from utils import redis_public
from wisdom_v2.views.evaluation_action import EvaluationListSerializers, ProjectMemberEvaluationListSerializer, get_status_submit_time
from wisdom_v2.models import Evaluation, EvaluationModule, ProjectMember, User
from wisdom_v2.enum.service_content_enum import EvaluationWriteRoleEnum
from rest_framework import viewsets

from utils.pagination import StandardResultsSetPagination
from utils.api_response import success_response, parameter_error_response

stakeholder_email_redis = redis.Redis.from_url('redis://127.0.0.1:6379/6')


class EvaluationViewSet(viewsets.ModelViewSet):
    queryset = Evaluation.objects.filter(deleted=False).order_by('-id')
    serializer_class = EvaluationListSerializers

    @swagger_auto_schema(
        operation_id='测评列表',
        operation_summary='测评列表',
        manual_parameters=[
            openapi.Parameter('keyword', openapi.IN_QUERY, description='关键字（测评标题）', type=openapi.TYPE_STRING),
            openapi.Parameter('page', openapi.IN_QUERY, description='第几页', type=openapi.TYPE_NUMBER),
            openapi.Parameter('page_size', openapi.IN_QUERY, description='每页显示条数默认15条', type=openapi.TYPE_NUMBER),
            openapi.Parameter('project_member_id', openapi.IN_QUERY, description='被教练者id', type=openapi.TYPE_NUMBER)
        ],
        tags=['测评相关']
    )
    def list(self, request, *args, **kwargs):
        evaluation = self.get_queryset()
        if request.query_params.get('keyword', None):
            evaluation = evaluation.filter(name__icontains=request.query_params.get('keyword'))
        project_member_id = request.query_params.get('project_member_id', None)

        # 分页
        paginator = StandardResultsSetPagination()
        page_list = paginator.paginate_queryset(evaluation, self.request)
        serializer = self.get_serializer(page_list, many=True)
        response = paginator.get_paginated_response(serializer.data)
        if project_member_id:
            project_member = ProjectMember.objects.get(id=project_member_id)
            evaluation_ids = EvaluationModule.objects.filter(
                project_bundle__project_member=project_member, deleted=False,
                project_bundle__deleted=False).values_list('evaluation_id', flat=True)
            evaluation_ids = list(evaluation_ids)
            for eva in response.data['results']:
                if eva['id'] in evaluation_ids:
                    eva['is_relevance'] = 1
                else:
                    eva['is_relevance'] = 0
        return success_response(response)


class ProjectMemberEvaluationViewSet(viewsets.ModelViewSet):
    queryset = EvaluationModule.objects.filter(deleted=False).order_by('end_time')
    serializer_class = ProjectMemberEvaluationListSerializer

    @swagger_auto_schema(
        operation_id='测评管理-测评列表',
        operation_summary='测评管理-测评列表',
        manual_parameters=[
            openapi.Parameter('name', openapi.IN_QUERY, description='测评名称', type=openapi.TYPE_STRING),
            openapi.Parameter('project_id', openapi.IN_QUERY, description='项目id(必传)', type=openapi.TYPE_NUMBER),
            openapi.Parameter('status', openapi.IN_QUERY, description='测评状态 1-未完成 2-已过期 3-已完成(必传)',
                              type=openapi.TYPE_NUMBER),
            openapi.Parameter('tab', openapi.IN_QUERY, description='1-被教练者 2-利益相关者', type=openapi.TYPE_NUMBER),
            openapi.Parameter('page', openapi.IN_QUERY, description='第几页', type=openapi.TYPE_NUMBER),
            openapi.Parameter('page_size', openapi.IN_QUERY, description='每页显示条数默认15条', type=openapi.TYPE_NUMBER),
        ],
        tags=['测评相关']
    )
    def list(self, request, *args, **kwargs):
        evaluation_module = self.get_queryset()
        project_id = request.query_params.get('project_id', None)
        status = request.query_params.get('status', None)
        tab = int(request.query_params.get('tab', 1))
        if not project_id:
            return parameter_error_response('请选择项目')
        if not status or status not in [1, 2, 3, '1', '2', '3']:
            return parameter_error_response('测评状态错误')
        evaluation_module = evaluation_module.filter(project_bundle__project_id=project_id)
        if request.query_params.get('name', None):
            evaluation_module = evaluation_module.filter(evaluation__name__icontains=request.query_params.get('name'))


        # 分页
        if tab == 1:
            paginator = StandardResultsSetPagination()
            data_list = ProjectMemberEvaluationListSerializer(evaluation_module, many=True).data
            data_list = [i for i in data_list if i['status'] == status or i['status'] == int(status)]
            page_list = paginator.paginate_queryset(data_list, self.request)
            response = paginator.get_paginated_response(page_list)
        else:
            data_lst = []
            for e_module in evaluation_module.filter(evaluation__role=EvaluationWriteRoleEnum.coachee_stakeholder.value):
                stakeholders = e_module.project_bundle.project_member.stakeholder
                if stakeholders:
                    for stakeholder in stakeholders:
                        e_status, submit_time = get_status_submit_time(e_module, stakeholder['user_id'])

                        data_lst.append({'id': e_module.pk,
                                         'evaluation_name': e_module.evaluation.name,
                                         'user_id': stakeholder['user_id'],
                                         'end_time': e_module.end_time.strftime('%Y-%m-%d'),
                                         'member_name': stakeholder['true_name'],
                                         'send_email': 1 if stakeholder_email_redis.get(f"email_{stakeholder['user_id']}_{project_id}_{e_module.pk}") else 0,
                                         'start_time': e_module.start_time.strftime('%Y-%m-%d'),
                                         'status': e_status,
                                         'submit_time': submit_time})
            data_lst = [i for i in data_lst if i['status'] == status or i['status'] == int(status)]

            paginator = StandardResultsSetPagination()
            page_list = paginator.paginate_queryset(
                data_lst, self.request)
            response = paginator.get_paginated_response(page_list)
        return success_response(response)

    @swagger_auto_schema(
        operation_id='测评管理-提醒发送邮件',
        operation_summary='测评管理-提醒发送邮件',
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'evaluation_module_id': openapi.Schema(type=openapi.TYPE_NUMBER, description='当前测评id(必传)'),
            }
        ),
        tags=['测评相关']
    )
    @action(methods=['post'], detail=False, url_path='send_remind_email')
    def send_remind_email(self, request, *args, **kwargs):
        evaluation_module_id = request.data.get('evaluation_module_id', None)
        user_id = request.data.get('user_id')
        sender = request.user.pk

        if not evaluation_module_id:
            return parameter_error_response('请选择需要发送邮件的测评')
        evaluation_module = EvaluationModule.objects.filter(pk=evaluation_module_id, deleted=False).first()
        if not evaluation_module:
            return parameter_error_response('当前测评不存在')
        if evaluation_module.send_email:
            return parameter_error_response('今日已发送邮件，请勿重复发送')
        
        if user_id:
            user = User.objects.get(pk=user_id)
        else:
            user = evaluation_module.project_bundle.project_member.user
        if user.email:
            message_send_email_base.delay('evaluation_remind_msg', {'evaluation_name': evaluation_module.evaluation.name},
                                  [user.email], project_id=evaluation_module.project_bundle.project_id,
                                  sender_id=sender,
                                  receiver_ids=user.id)

        if not user_id:
            evaluation_module.send_email = True
            evaluation_module.save()
        else:
            redis_public.save_today_end_redis(stakeholder_email_redis, f'email_{user_id}_{evaluation_module.project_bundle.project_id}_{evaluation_module_id}', 1)
        return success_response()