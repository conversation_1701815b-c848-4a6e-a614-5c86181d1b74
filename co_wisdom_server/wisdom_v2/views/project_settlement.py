from datetime import datetime

import pendulum
from django.db.models import Q
from drf_yasg import openapi
from drf_yasg.utils import swagger_auto_schema
from rest_framework.decorators import action
from rest_framework import viewsets

from utils.api_response import success_response, parameter_error_response
from utils.pagination import StandardResultsSetPagination
from wisdom_v2.common import project_settlement_public, business_order_public
from wisdom_v2.enum.business_order_enum import BusinessOrderTypeEnum
from wisdom_v2.models import Project, Coach
from wisdom_v2.models_file import PublicCourses
from wisdom_v2.models_file.project import ProjectSettlement
from wisdom_v2.views.project_settlement_actions import AdminProjectSettlementListSerializer


class AdminProjectSettlementViewSet(viewsets.ModelViewSet):
    queryset = ProjectSettlement.objects.filter(deleted=False).order_by('-work_start_time')
    serializer_class = AdminProjectSettlementListSerializer
    @swagger_auto_schema(
        operation_id='获取项目结算信息列表',
        operation_summary='获取项目结算信息列表',
        manual_parameters=[
            openapi.Parameter('status', openapi.IN_QUERY, description='活动状态 1-未开始 2-进行中 3-已结束', type=openapi.TYPE_NUMBER,
                              required=True),
            openapi.Parameter('project_id', openapi.IN_QUERY, description='项目id',
                              type=openapi.TYPE_NUMBER, required=True),
            openapi.Parameter('work_type', openapi.IN_QUERY, description='工作形式 1-一对一辅导 2-利益相关者访谈 3-工作坊 4-小组辅导 5-授课 6-考评 7-案例定制 8-论文评审',
                              type=openapi.TYPE_STRING, required=True),
            openapi.Parameter('work_start_time', openapi.IN_QUERY, description='开始时间',
                              type=openapi.TYPE_STRING, required=True),
            openapi.Parameter('work_end_time', openapi.IN_QUERY, description='结束时间',
                              type=openapi.TYPE_STRING, required=True),
            openapi.Parameter('coach_name', openapi.IN_QUERY, description='教练名称', type=openapi.TYPE_STRING,
                              required=True),
        ],
        tags=['项目结算']
    )
    def list(self, request, *args, **kwargs):

        try:
            project_id = request.query_params.get('project_id')
            status = request.query_params.get('status')
            work_type = request.query_params.get('work_type')
            coach_name = request.query_params.get('coach_name')
            work_start_time = request.query_params.get('work_start_time')
            work_end_time = request.query_params.get('work_end_time')
            project = Project.objects.get(pk=project_id, deleted=False)
        except Project.DoesNotExist:
            return parameter_error_response('项目不存在')
        except Exception as e:
            return parameter_error_response()
        queryset = self.get_queryset().filter(project=project)
        try:
            if work_start_time:
                work_start_time = pendulum.parse(work_start_time)
            if work_end_time:
                work_end_time = pendulum.parse(work_end_time)
        except ValueError:
            return parameter_error_response("无效的时间格式")

        if status:
            current_date = datetime.now().date()
            current_hour = datetime.now().hour

            # 未开始 开始时间>当前时间
            if status == '1':
                queryset = queryset.filter(
                    # 开始日期大于当前日期 或 (开始日期等于当前日期且开始时间的小时大于当前小时)
                    Q(work_start_time__date__gt=current_date) | (
                                Q(work_start_time__date=current_date) & Q(work_start_time__hour__gt=current_hour))
                )
            # 进行中 开始时间<=当前时间，结束时间>=当前时间
            elif status == '2':
                queryset = queryset.filter(
                    # 开始日期小于当前日期 或 (开始日期等于当前日期且开始小时小于当前小时)
                    Q(work_start_time__date__lt=current_date) | (
                            Q(work_start_time__date=current_date) & Q(work_start_time__hour__lte=current_hour)),
                    # 结束日期大于当前日期 或 (结束日期等于当前日期且结束小时大于等于当前小时)
                    Q(work_end_time__date__gt=current_date) | (
                            Q(work_end_time__date=current_date) & Q(work_end_time__hour__gt=current_hour))
                )
            # 已结束 结束时间<当前时间
            elif status == '3':
                queryset = queryset.filter(
                    # 结束日期小于当前日期 或 (结束日期等于当前日期且结束时间的小时小于等于当前小时)
                    Q(work_end_time__date__lt=current_date) | (
                                Q(work_end_time__date=current_date) & Q(work_end_time__hour__lte=current_hour))
                )
        if coach_name:
            queryset = queryset.filter(
                coach__user__true_name__icontains=coach_name, coach__deleted=False).distinct()
        if work_start_time:
            queryset = queryset.filter(
                Q(work_start_time__date__gt=work_start_time.date()) | (
                                Q(work_start_time__date=work_start_time.date()) & Q(work_start_time__hour__gt=work_start_time.hour)))
        if work_end_time:
            queryset = queryset.filter(Q(work_end_time__date__lt=work_end_time.date()) | (
                                Q(work_end_time__date=work_end_time.date()) & Q(work_end_time__hour__lt=work_end_time.hour)))
        if work_type:
            queryset = queryset.filter(work_type__in=str(work_type).split(','))

        # 分页
        paginator = StandardResultsSetPagination()
        page_list = paginator.paginate_queryset(queryset, self.request)
        serializer = self.serializer_class(page_list, many=True)
        response = paginator.get_paginated_response(serializer.data)
        return success_response(response)


    @swagger_auto_schema(
        operation_id='获取项目结算信息详情',
        operation_summary='获取项目结算信息详情',
        manual_parameters=[
            openapi.Parameter('project_settlement_id', openapi.IN_QUERY, description='项目结算信息标识', type=openapi.TYPE_NUMBER,
                              required=True),
        ],
        tags=['项目结算']
    )
    @action(methods=['get'], detail=False, url_path='detail')
    def get_project_settlement_detail(self, request, *args, **kwargs):
        try:
            project_settlement_id = request.query_params.get('project_settlement_id')
            project_settlement = ProjectSettlement.objects.get(id=project_settlement_id, deleted=False)
        except ProjectSettlement.DoesNotExist:
            return parameter_error_response('项目结算订单不存在')
        except Exception as e:
            return parameter_error_response()
        params = {
            'settlement_content': [{
                "coach_name": project_settlement.coach.user.cover_name,
                "coach_id": project_settlement.coach_id,
                "content": [{
                        "id": str(project_settlement.pk),
                        "work_type": project_settlement.work_type,
                        "coachee_name": project_settlement.coachee_name,
                        "work_end_time": project_settlement.work_end_time.strftime('%Y-%m-%d %H:%M:%S'),
                        "work_start_time": project_settlement.work_start_time.strftime('%Y-%m-%d %H:%M:%S'),
                        "time_type": project_settlement.time_type,
                        "place_type": project_settlement.place_type,
                }]
        }]}
        return success_response(params)

    @swagger_auto_schema(
        operation_id='创建项目结算信息',
        operation_summary='创建项目结算信息',
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'project_id': openapi.Schema(type=openapi.TYPE_STRING, description='活动主题'),
                'settlement_content': openapi.Schema(type=openapi.TYPE_STRING, description='结算信息'),
            }),
        tags=['项目结算']
    )
    @action(methods=['post'], detail=False, url_path='add')
    def add_project_settlement(self, request, *args, **kwargs):
        try:
            project_id = request.data.get('project_id')
            settlement_content = request.data.get('settlement_content')
            Project.objects.get(pk=project_id, deleted=False)
        except Project.DoesNotExist:
            return parameter_error_response('项目不存在')
        except Exception as e:
            return parameter_error_response()

        # 检查结算内容是否为列表
        if not isinstance(settlement_content, list):
            return parameter_error_response('结算信息错误')

        add_settlement_all = []
        for settlement_item in settlement_content:
            # 获取教练ID
            coach_id = settlement_item.get('coach_id')
            # 检查教练是否存在且未被删除
            if not Coach.objects.filter(pk=coach_id, deleted=False).exists():
                return parameter_error_response('教练信息错误')

            content = settlement_item.get('content')
            # 检查教练结算信息是否为列表
            if not isinstance(content, list):
                return parameter_error_response('教练结算信息错误')

            for coach_item in content:
                work_type = coach_item.get('work_type')
                coachee_name = coach_item.get('coachee_name')
                work_end_time = coach_item.get('work_end_time')
                work_start_time = coach_item.get('work_start_time')
                time_type = coach_item.get('time_type')
                place_type = coach_item.get('place_type')
                # 检查必要参数是否存在
                if not all([work_type, work_end_time, work_start_time]):
                    return parameter_error_response('缺少必填参数')

                # 检查开始时间是否大于结束时间
                if work_start_time > work_end_time:
                    return parameter_error_response('开始时间不能大于结束时间')

                # 创建结算实例并加入列表
                add_settlement_all.append(
                    ProjectSettlement(
                        project_id=project_id,
                        work_type=work_type,
                        coachee_name=coachee_name,
                        work_start_time=work_start_time,
                        work_end_time=work_end_time,
                        coach_id=coach_id,
                        time_type=time_type,
                        place_type=place_type,
                    )
                )
        # 如果有结算数据，则进行批量创建
        if add_settlement_all:
            ProjectSettlement.objects.bulk_create(add_settlement_all)
        return success_response()

    @swagger_auto_schema(
        operation_id='修改项目结算信息',
        operation_summary='修改项目结算信息',
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'settlement_content': openapi.Schema(type=openapi.TYPE_STRING, description='结算信息'),
            }),
        tags=['项目结算']
    )
    @action(methods=['post'], detail=False, url_path='update')
    def update_project_settlement(self, request, *args, **kwargs):
        try:
            settlement_content = request.data.get('settlement_content')
        except Exception as e:
            return parameter_error_response()

        # 检查结算内容是否为列表
        if not isinstance(settlement_content, list):
            return parameter_error_response('项目结算信息错误')

        for settlement_item in settlement_content:
            # 获取项目结算信息
            content = settlement_item.get('content')
            if not content:
                return parameter_error_response('未获取到结算信息')
            if not isinstance(content, list):
                return parameter_error_response('项目结算信息数据格式错误')

            for item in content:
                # 获取课程相关信息
                project_settlement_id = item.get('id')
                if not project_settlement_id:
                    return parameter_error_response('未获取到项目结算信息标识')

                # 查询对应ID的项目结算对象
                project_settlement = ProjectSettlement.objects.filter(
                    pk=project_settlement_id, deleted=False).first()
                if not project_settlement:
                    return parameter_error_response('未获取到项目结算信息')

                # 检查是否已发起提现
                if business_order_public.get_object_settlement_status(
                        [str(project_settlement.pk)], BusinessOrderTypeEnum.enterprise.value,
                        project_settlement.work_type):
                    return parameter_error_response('已发起提现不可修改')

                # 获取课程的其他信息
                work_start_time = item.get('work_start_time')
                work_end_time = item.get('work_end_time')
                work_type = item.get('work_type')
                coachee_name = item.get('coachee_name')
                time_type = item.get('time_type')
                place_type = item.get('place_type')

                # 检查必填信息是否完整
                if not all([work_type, work_start_time, work_end_time]):
                    return parameter_error_response('请提供完整的工作类型、开始时间和结束时间')

                # 检查开始时间是否大于结束时间
                if work_start_time > work_end_time:
                    return parameter_error_response('开始时间不能大于结束时间')

                # 判断是否需要更新时间
                update_data = {}
                if work_start_time != project_settlement.work_start_time.strftime('%Y-%m-%d %H:%M:%S'):
                    start_time = datetime.strptime(work_start_time, "%Y-%m-%d %H:%M:%S")
                    project_settlement.work_start_time = start_time
                    update_data['start_time'] = start_time
                    update_data['end_time'] = project_settlement.work_end_time

                if work_end_time != project_settlement.work_end_time.strftime('%Y-%m-%d %H:%M:%S'):
                    end_time = datetime.strptime(work_end_time, "%Y-%m-%d %H:%M:%S")
                    project_settlement.work_end_time = end_time
                    update_data['end_time'] = end_time
                    if not update_data.get('start_time'):
                        update_data['start_time'] = project_settlement.work_start_time

                if time_type != project_settlement.time_type:
                    project_settlement.time_type = time_type
                    update_data['time_type'] = time_type

                if place_type != project_settlement.place_type:
                    project_settlement.place_type = place_type

                old_work_type = None
                if work_type != project_settlement.work_type:
                    old_work_type = project_settlement.work_type
                    project_settlement.work_type = work_type
                    update_data['type'] = work_type

                # 更新公共课程教练信息
                project_settlement.coachee_name = coachee_name
                project_settlement.save()

                # 结算信息更新后如果对价格有影响，就需要更新订单
                if update_data:
                    # 如果更新了数据，更新相应的业务订单信息
                    object_ids = [str(project_settlement.pk)]
                    # 如果更新了工作类型，需要使用之前的工作类型查询
                    update_data_type = old_work_type or project_settlement.work_type

                    # 如果修改了时间，并且状态变成了未开始或进行中，删除原来订单数据
                    if ((update_data.get('start_time') or update_data.get('end_time')) and
                            project_settlement_public.get_status_display(project_settlement)) in [1, 2]:
                        business_order_public.del_business_order(
                            object_ids, BusinessOrderTypeEnum.enterprise.value, update_data_type)
                    # 如果状态没变化，更新原有数据
                    else:
                        # 更新对应数据
                        business_order_public.update_business_order(
                            object_ids, BusinessOrderTypeEnum.enterprise.value, update_data_type, update_data)

        return success_response()
    @swagger_auto_schema(
        operation_id='删除教练结算信息',
        operation_summary='删除教练结算信息',
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'project_settlement_id': openapi.Schema(type=openapi.TYPE_NUMBER, description='标识'),
            }),
        tags=['项目结算']
    )
    @action(methods=['post'], detail=False, url_path='del')
    def del_project_settlement(self, request, *args, **kwargs):
        try:
            project_settlement_id = request.data.get('project_settlement_id')
            project_settlement = ProjectSettlement.objects.get(id=project_settlement_id, deleted=False)
        except ProjectSettlement.DoesNotExist:
            return parameter_error_response('教练教练结算订单不存在')
        except Exception as e:
            return parameter_error_response()
        business_order_public.del_business_order(
            [str(project_settlement.pk)], BusinessOrderTypeEnum.enterprise.value, project_settlement.work_type)
        project_settlement.deleted = True
        project_settlement.save()
        return success_response()