from ast import literal_eval

from drf_yasg import openapi
from rest_framework.decorators import action
from drf_yasg.utils import swagger_auto_schema

from wisdom_v2 import utils
from wisdom_v2.enum.project_interview_enum import TemplateTypeEnum
from wisdom_v2.enum.service_content_enum import InterviewRecordTemplateQuestionTypeEnum as QuestionTypeEnum, \
    InterviewRecordTemplateRoleEnum
from wisdom_v2.views.interview_question_actions import InterviewRecordTemplateQuestionSerializers
from wisdom_v2.models import InterviewRecordTemplateQuestion, InterviewRecordTemplateOption, \
    InterviewRecordTemplateAnswer, RelatedQuestion
from rest_framework import viewsets

from utils.pagination import StandardResultsSetPagination
from utils.api_response import success_response, parameter_error_response


class InterViewRecordTemplateQuestionViewSet(viewsets.ModelViewSet):
    queryset = InterviewRecordTemplateQuestion.objects.filter(deleted=False).order_by('-created_at')
    serializer_class = InterviewRecordTemplateQuestionSerializers
    authentication_classes = []

    @swagger_auto_schema(
        operation_id='辅导模版问题列表',
        operation_summary='辅导模版问题列表',
        manual_parameters=[
            openapi.Parameter('interview_record_id', openapi.IN_QUERY, description='辅导记录模版id', type=openapi.TYPE_STRING),
            openapi.Parameter('page', openapi.IN_QUERY, description='第几页', type=openapi.TYPE_NUMBER),
            openapi.Parameter('page_size', openapi.IN_QUERY, description='每页显示条数默认15条', type=openapi.TYPE_NUMBER),
        ],
        tags=['辅导模版问题相关']
    )
    def list(self, request, *args, **kwargs):
        interview_question = self.get_queryset()
        if request.query_params.get('interview_record_id', None):
            interview_question = interview_question.filter(template_id=request.query_params.get('interview_record_id'))

        # 分页
        paginator = StandardResultsSetPagination()
        page_list = paginator.paginate_queryset(interview_question, self.request)
        serializer = self.get_serializer(page_list, many=True)
        response = paginator.get_paginated_response(serializer.data)
        return success_response(response)

    @swagger_auto_schema(
        operation_id='修改辅导模版问题',
        operation_summary='修改辅导模版问题',
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'interview_question_id': openapi.Schema(type=openapi.TYPE_NUMBER, description='模版问题ID'),
                'answer_type': openapi.Schema(
                    type=openapi.TYPE_NUMBER,
                    description='回答格式类型 ｜ 1:普通文本框, 2:行动计划, 3:习惯养成, 4:成长笔记，5：能力标签，6:多条文本框'),
                'rating_type': openapi.Schema(
                    type=openapi.TYPE_NUMBER,
                    description='评分格式类型 ｜ 1:有效度，2:投入度，3:满意度'),
                'type': openapi.Schema(type=openapi.TYPE_NUMBER, description='问题类型 | 1:填空，2:单选，3:多选，4:评分'),
                'title': openapi.Schema(type=openapi.TYPE_STRING, description='问题'),
                'title_describe': openapi.Schema(type=openapi.TYPE_STRING, description='问题描述'),
                'order': openapi.Schema(type=openapi.TYPE_NUMBER, description='问题排序'),
                'answer_tips': openapi.Schema(type=openapi.TYPE_STRING, description='回答提示(仅文本框需要)'),
                'required': openapi.Schema(type=openapi.TYPE_BOOLEAN, description='是否必须填写'),
                'option': openapi.Schema(type=openapi.TYPE_ARRAY, description='选项内容列表', items=openapi.Schema(
                    type=openapi.TYPE_OBJECT, properties={
                        "title": openapi.Schema(type=openapi.TYPE_STRING, description='选项内容'),
                        "user_defined": openapi.Schema(type=openapi.TYPE_BOOLEAN, description='是否自定义填写'),
                    })),
                'opposite_question_id': openapi.Schema(type=openapi.TYPE_NUMBER, description='关联问题id'),
                'description': openapi.Schema(type=openapi.TYPE_STRING, description='关联问题说明'),
                'sub_tips': openapi.Schema(type=openapi.TYPE_STRING, description='分条提示'),
                'deleted': openapi.Schema(type=openapi.TYPE_BOOLEAN, description='设置删除'),
            }
        ),
        tags=['辅导模版问题相关']
    )
    @action(methods=['post'], detail=False, url_path='update')
    def question_update(self, request, *args, **kwargs):
        try:
            question = InterviewRecordTemplateQuestion.objects.get(pk=request.data.get('interview_question_id', 0))
        except InterviewRecordTemplateQuestion.DoesNotExist as e:
            return parameter_error_response()

        # 用户已填写的模版不能修改   查询当前question的answer，如过answer存在则说明有人填过
        if question.interview_record_template_answer_question.exists():
            return parameter_error_response('当前模板已使用无法修改')

        # 是否进行类型修改
        new_interview_type = request.data.get('type', 0)
        if new_interview_type:
            # 清理选择题数据
            InterviewRecordTemplateOption.objects.filter(question=question.pk).delete()
        if new_interview_type in [QuestionTypeEnum.single, QuestionTypeEnum.multiple]:
            option = request.data.get('option', [])
            if len(option) <= 1:
                return parameter_error_response('选择题选项需要大于一个')
            # 批量创建选择题选项数据
            options = []
            for order, item in enumerate(option):
                options.append(InterviewRecordTemplateOption(
                    question_id=question.pk,
                    title=item.get('title'),
                    user_defined=item.get('user_defined'),
                    order=order
                ))
            InterviewRecordTemplateOption.objects.bulk_create(options)

        # 清理原填空类型
        if new_interview_type in [QuestionTypeEnum.blank, QuestionTypeEnum.rating]:
            request.data['answer_type'] = request.data.get('answer_type', None)

        # 修改在模板中的位置顺序
        if request.data.get('order') or request.data.get('order') == 0:
            questions_order = literal_eval(question.template.questions_order)
            questions_order.remove(question.pk)
            questions_order.insert(request.data.get('order'), question.pk)
            question.template.questions_order = str(questions_order)
            question.template.save()
        if request.data.get('deleted'):
            questions_order = literal_eval(question.template.questions_order)
            questions_order.remove(question.pk)
            question.template.questions_order = str(questions_order)
            question.template.save()

        data = request.data.copy()
        opposite_question_id, description = None, None
        if 'opposite_question_id' in data.keys():
            opposite_question_id = request.data.get('opposite_question_id')
            data.pop('opposite_question_id')
        if 'description' in data.keys():
            description = request.data.get('description')
            data.pop('description')
        data = request.data.copy()
        for k, v in data.items():
            if v == '':
                data[k] = None
        serializer = self.get_serializer(question, data=data, partial=True)
        serializer.is_valid(raise_exception=True)
        serializer.update(question, data)

        deleted = data.get('deleted', 0)
        if deleted:
            template = question.template
            if template.role == InterviewRecordTemplateRoleEnum.coach.value:
                if RelatedQuestion.objects.filter(
                        coach_question_id=question.pk, deleted=False, template_type=TemplateTypeEnum.report.value).exists():
                    return parameter_error_response('题目在报告中被使用，请先在报告中移除')

                r_question = RelatedQuestion.objects.filter(coach_question_id=question.pk, deleted=False,
                                                            template_type=TemplateTypeEnum.coach.value).first()
                if r_question:
                    r_question.deleted = True
                    r_question.save()
            else:
                if RelatedQuestion.objects.filter(
                        coachee_question_id=question.pk, deleted=False, template_type=TemplateTypeEnum.report.value).exists():
                    return parameter_error_response('题目在报告中被使用，请先在报告中移除')

                r_question = RelatedQuestion.objects.filter(coachee_question_id=question.pk, deleted=False,
                                                            template_type=TemplateTypeEnum.coachee.value).first()
                if r_question:
                    r_question.deleted = True
                    r_question.save()
        else:
            if opposite_question_id:
                template = question.template
                if template.role == 1:  # 教练问卷模版
                    r_question = RelatedQuestion.objects.filter(coach_question_id=question.pk, deleted=False,
                                                                template_type=1).first()
                    if r_question:
                        r_question.coachee_question_id = opposite_question_id
                        if description:
                            r_question.description = description
                        r_question.save()
                    else:
                        data = {"coach_question_id": question.pk, "coachee_question_id": opposite_question_id,
                                "template_type": 1}
                        if description:
                            data['description'] = description
                        RelatedQuestion.objects.create(**data)

                else:
                    r_question = RelatedQuestion.objects.filter(coachee_question_id=question.pk, deleted=False,
                                                                template_type=2).first()
                    if r_question:
                        r_question.coach_question_id = opposite_question_id
                        if description:
                            r_question.description = description
                        r_question.save()
                    else:
                        data = {"coach_question_id": opposite_question_id, "coachee_question_id": question.pk,
                                "template_type": 2}
                        if description:
                            data['description'] = description
                        RelatedQuestion.objects.create(**data)
        utils.del_template_user_tmp(question.template_id, question.template.type)

        return success_response(serializer.data)

    @swagger_auto_schema(
        operation_id='创建辅导模版问题',
        operation_summary='创建辅导模版问题',
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'template_id': openapi.Schema(type=openapi.TYPE_NUMBER, description='元模版ID'),
                'answer_type': openapi.Schema(
                    type=openapi.TYPE_NUMBER,
                    description='回答格式类型 ｜ 1:普通文本框, 2:行动计划, 3:习惯养成, 4:成长笔记，5:能力标签，6:多条文本框'),
                'rating_type': openapi.Schema(
                    type=openapi.TYPE_NUMBER,
                    description='评分格式类型 ｜ 1:有效度，2:投入度，3:满意度'),
                'type': openapi.Schema(type=openapi.TYPE_NUMBER, description='问题类型 | 1:填空，2:单选，3:多选，4:评分'),
                'title': openapi.Schema(type=openapi.TYPE_STRING, description='问题'),
                'title_describe': openapi.Schema(type=openapi.TYPE_STRING, description='问题描述'),
                'answer_tips': openapi.Schema(type=openapi.TYPE_STRING, description='回答提示(仅文本框需要)'),
                'order': openapi.Schema(type=openapi.TYPE_NUMBER, description='问题排序'),
                'required': openapi.Schema(type=openapi.TYPE_BOOLEAN, description='是否必须填写'),
                'option': openapi.Schema(type=openapi.TYPE_ARRAY, description='选项内容列表', items=openapi.Schema(
                    type=openapi.TYPE_OBJECT, properties={
                        "title": openapi.Schema(type=openapi.TYPE_STRING, description='选项内容'),
                        "user_defined": openapi.Schema(type=openapi.TYPE_BOOLEAN, description='是否自定义填写'),
                    })),
                'deleted': openapi.Schema(type=openapi.TYPE_BOOLEAN, description='设置删除'),
                'opposite_question_id': openapi.Schema(type=openapi.TYPE_NUMBER, description='关联问题id'),
                'sub_tips': openapi.Schema(type=openapi.TYPE_STRING, description='分条提示'),
                'description': openapi.Schema(type=openapi.TYPE_STRING, description='关联问题说明')

            }
        ),
        tags=['辅导模版问题相关']
    )
    def create(self, request, *args, **kwargs):
        if InterviewRecordTemplateAnswer.objects.filter(question__template_id=request.data.get('template_id')).exists():
            return parameter_error_response('当前模版已使用，无法新增问题')

        new_question = InterviewRecordTemplateQuestion(
            template_id=request.data.get('template_id'),
            answer_type=request.data.get('answer_type'),
            rating_type=request.data.get('rating_type'),
            answer_tips=request.data.get('answer_tips'),
            type=request.data.get('type'),
            title=request.data.get('title'),
            title_describe=request.data.get('title_describe'),
            sub_tips=request.data.get('sub_tips'),
            required=request.data.get('required')
        )
        new_question.save()

        # 判断是否是选择题
        if request.data.get('type') in [QuestionTypeEnum.single, QuestionTypeEnum.multiple]:
            option = request.data.get('option', [])
            if len(option) <= 1:
                return parameter_error_response('选择题选项需要大于一个')
            # 批量添加选项
            options = []
            for order, item in enumerate(option):
                options.append(InterviewRecordTemplateOption(
                    question_id=new_question.pk,
                    title=item.get('title'),
                    user_defined=item.get('user_defined'),
                    order=order
                ))
            InterviewRecordTemplateOption.objects.bulk_create(options)

        questions_order = literal_eval(new_question.template.questions_order)

        # 模板中位置顺序录入
        if request.data.get('order') is not None:
            questions_order.insert(request.data.get('order'), new_question.pk)
        else:
            questions_order.append(new_question.pk)

        new_question.template.questions_order = str(questions_order)
        new_question.template.save()
        question = self.get_serializer(new_question)
        opposite_question_id = request.data.get('opposite_question_id')
        description = request.data.get('description')
        if opposite_question_id:
            template = new_question.template
            if template.role == 1:  # 教练问卷模版
                data = {"coach_question_id": new_question.pk, "coachee_question_id": opposite_question_id,
                        "template_type": 1}
            else:
                data = {"coach_question_id": opposite_question_id, "coachee_question_id": new_question.pk,
                        "template_type": 2}
            if description:
                data['description'] = description
            RelatedQuestion.objects.create(**data)

        utils.del_template_user_tmp(request.data.get('template_id'), new_question.template.type)

        return success_response(question.data)


