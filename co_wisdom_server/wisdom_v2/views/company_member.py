import tempfile

from drf_yasg import openapi
from rest_framework.decorators import action
from drf_yasg.utils import swagger_auto_schema
from ..models import CompanyMember, User, Company, ProjectMember, \
    ProjectInterested, UserBackend, Project
from .company_member_actions import CompanyMemberSerializers
from rest_framework import viewsets
from django.db import transaction
from django.db.models import Q

from utils import aesencrypt, randomPassword

from utils.pagination import StandardResultsSetPagination
from utils.api_response import success_response, parameter_error_response
from wisdom_v2.views.project_member_action import remove_user_data, remove_none_data, valid_member_data, \
    member_data_clean


class CompanyMemberViewSet(viewsets.ModelViewSet):
    queryset = CompanyMember.objects.filter().order_by('is_forbidden', 'created_at')
    serializer_class = CompanyMemberSerializers

    @swagger_auto_schema(
        operation_id='企业成员列表',
        operation_summary='企业成员列表',
        manual_parameters=[
            openapi.Parameter('company_id', openapi.IN_QUERY, description='企业id', type=openapi.TYPE_NUMBER,
                              required=True),
            openapi.Parameter('true_name', openapi.IN_QUERY, description='用户名', type=openapi.TYPE_NUMBER),
            openapi.Parameter('page', openapi.IN_QUERY, description='第几页', type=openapi.TYPE_NUMBER),
            openapi.Parameter('page_size', openapi.IN_QUERY, description='每页显示条数默认15条', type=openapi.TYPE_NUMBER),
            openapi.Parameter('project_member_id', openapi.IN_QUERY, description='项目成员id(添加利益相关者时的利益相关者列表)'
                , type=openapi.TYPE_NUMBER)
        ],
        tags=['企业成员相关']
    )
    def list(self, request, *args, **kwargs):
        true_name = request.query_params.get('true_name')
        project_id = request.query_params.get('project_id', None)
        # 网站管理员和项目管理员才能调用接口
        company = Company.objects.filter(pk=request.query_params.get('company_id', 0)).first()
        # manager_ids = Project.objects.filter(company=company).values_list('manager_id', flat=True)
        # if request.user.id not in manager_ids or user.user_type == 1:
        #     return parameter_error_response('用户无权查看企业信息')
        company_member = self.get_queryset()
        if request.query_params.get('company_id'):
            company_member = company_member.filter(company_id=request.query_params.get('company_id', 0)).order_by('-created_at')
        if true_name:
            company_member = company_member.filter(user__true_name__icontains=true_name).order_by('-created_at')
        project_member_id = request.query_params.get('project_member_id', None)
        if project_member_id:
            project_member = ProjectMember.objects.filter(id=project_member_id, deleted=False).first()
            if project_member:
                interested_ids = ProjectInterested.objects.filter(project=project_member.project,
                                                                  master=project_member.user, deleted=False).\
                    values_list('interested_id', flat=True)
                interested_ids = list(interested_ids)
                interested_ids.append(project_member.user_id)
                company_member = company_member.filter(company_id=project_member.project.company_id).\
                    exclude(user_id__in=interested_ids).order_by('-created_at')
        # 增加project_id参数，从项目获取公司id
        if project_id:
            project = Project.objects.filter(pk=project_id).first()
            if project:
                company_member = company_member.filter(company_id=project.company_id).order_by('-created_at')
        # 分页
        paginator = StandardResultsSetPagination()
        page_list = paginator.paginate_queryset(company_member, self.request)
        serializer = self.get_serializer(page_list, many=True)
        response = paginator.get_paginated_response(serializer.data)
        return success_response(response)

    @swagger_auto_schema(
        operation_id='企业成员详情',
        operation_summary='企业成员详情',
        manual_parameters=[
            openapi.Parameter('member_id', openapi.IN_QUERY, description='成员id不是用户id', type=openapi.TYPE_NUMBER),

        ],
        tags=['企业成员相关']
    )
    @action(methods=['get'], detail=False, url_path='detail')
    def company_member_detail(self, request, *args, **kwargs):
        try:
            instance = CompanyMember.objects.get(pk=request.query_params.get('member_id', 0))
        except CompanyMember.DoesNotExist as e:
            return parameter_error_response()
        serializer = self.get_serializer(instance)
        return success_response(serializer.data)

    @swagger_auto_schema(
        operation_id='修改企业成员信息',
        operation_summary='修改企业成员信息',
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'member_id': openapi.Schema(type=openapi.TYPE_NUMBER, description='成员ID'),
                'manage_role': openapi.Schema(type=openapi.TYPE_NUMBER, description='管理角色'),
                'employee': openapi.Schema(type=openapi.TYPE_NUMBER, description='直系下属人数'),
                'position': openapi.Schema(type=openapi.TYPE_STRING, description='职位'),
                'department': openapi.Schema(type=openapi.TYPE_STRING, description='部门'),
                'remark': openapi.Schema(type=openapi.TYPE_STRING, description='管理员备注'),
                'true_name': openapi.Schema(type=openapi.TYPE_STRING, description='用户姓名'),
                'phone': openapi.Schema(type=openapi.TYPE_STRING, description='用户手机号'),
                'email': openapi.Schema(type=openapi.TYPE_STRING, description='用户邮箱'),
                'id_number': openapi.Schema(type=openapi.TYPE_STRING, description='用户身份证号'),
                'job_number': openapi.Schema(type=openapi.TYPE_STRING, description='用户工号'),
                'is_forbidden': openapi.Schema(type=openapi.TYPE_BOOLEAN, description='归档'),
                'work_year': openapi.Schema(type=openapi.TYPE_NUMBER, description='工作年限'),
                'job_year': openapi.Schema(type=openapi.TYPE_NUMBER, description='本岗年限'),
                'company_year': openapi.Schema(type=openapi.TYPE_NUMBER, description='公司年限')
            }
        ),
        tags=['企业成员相关']
    )
    @action(methods=['post'], detail=False, url_path='update')
    def company_member_update(self, request, *args, **kwargs):
        try:
            instance = CompanyMember.objects.get(pk=request.data.get('member_id', 0))
        except CompanyMember.DoesNotExist as e:
            return parameter_error_response()
        true_name = request.data.get('true_name')
        phone = request.data.get('phone')
        email = request.data.get('email')
        id_number = request.data.get('id_number')
        name = request.data.get('name')
        position = request.data.get('position')
        age = request.data.get('age')
        gender = request.data.get('gender')

        if id_number:
            if CompanyMember.objects.filter(id_number=id_number).exclude(pk=instance.pk).exists():
                return parameter_error_response('用户身份证号已存在')

        if true_name:
            instance.user.true_name = true_name
        else:
            return parameter_error_response('请输入姓名')

        if age:
            instance.user.age = age
        if gender:
            instance.user.gender = gender

        if phone:
            if User.objects.filter(Q(phone=phone) | Q(name=phone), deleted=False).exclude(pk=instance.user.pk).exists():
                return parameter_error_response('用户手机号已存在')
            instance.user.phone = phone
        if email:
            if ' ' in email:
                return parameter_error_response('邮箱中存在空格，请修改后再添加。')
            if User.objects.filter(Q(email=email) | Q(name=email), deleted=False).exclude(pk=instance.user.pk).exists():
                return parameter_error_response('用户邮箱已存在')
            instance.user.email = email

        if name:
            if User.objects.filter(Q(email=name) | Q(phone=name) | Q(name=name), deleted=False).exclude(pk=instance.user.pk).exists():
                return parameter_error_response('用户名已存在')
        if not position:
            return parameter_error_response('请输入职务')

        data = request.data.copy()
        serializer = self.get_serializer(instance, data=data, partial=True)
        serializer.is_valid(raise_exception=True)
        serializer.save()
        instance.user.save()
        return success_response(serializer.data)

    @swagger_auto_schema(
        operation_id='创建企业成员信息',
        operation_summary='创建企业成员信息',
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'user_id': openapi.Schema(type=openapi.TYPE_NUMBER, description='用户id（传用户id就不用传用户名）'),
                'name': openapi.Schema(type=openapi.TYPE_STRING, description='用户名)'),
                'true_name': openapi.Schema(type=openapi.TYPE_STRING, description='用户姓名'),
                'company_id': openapi.Schema(type=openapi.TYPE_NUMBER, description='企业id'),
                'manage_role': openapi.Schema(type=openapi.TYPE_NUMBER, description='管理角色'),
                'employee': openapi.Schema(type=openapi.TYPE_NUMBER, description='直系下属人数'),
                'position': openapi.Schema(type=openapi.TYPE_STRING, description='职位'),
                'department': openapi.Schema(type=openapi.TYPE_STRING, description='部门'),
                'remark': openapi.Schema(type=openapi.TYPE_STRING, description='管理员备注'),
                'phone': openapi.Schema(type=openapi.TYPE_STRING, description='用户手机号'),
                'email': openapi.Schema(type=openapi.TYPE_STRING, description='用户邮箱(必传)'),
                'id_number': openapi.Schema(type=openapi.TYPE_STRING, description='用户身份证号'),
                'job_number': openapi.Schema(type=openapi.TYPE_STRING, description='用户工号'),
                'work_year': openapi.Schema(type=openapi.TYPE_NUMBER, description='工作年限'),
                'job_year': openapi.Schema(type=openapi.TYPE_NUMBER, description='本岗年限'),
                'company_year': openapi.Schema(type=openapi.TYPE_NUMBER, description='公司年限'),
                'area_code': openapi.Schema(type=openapi.TYPE_STRING, description='区号')
            }
        ),
        tags=['企业成员相关']
    )
    def create(self, request, *args, **kwargs):
        validated_data = request.data.copy()
        if request.data.get('user_id', 0):
            if not User.objects.filter(pk=request.data.get('user_id'), deleted=False).exists() \
                    or not Company.objects.filter(pk=request.data.get('company_id')).exists():
                return parameter_error_response('用户或企业参数错误')
            if CompanyMember.objects.filter(user_id=request.data.get('user_id', 0),
                                            company_id=request.data.get('company_id'), user__deleted=False).exists():
                return parameter_error_response('当前用户已存在与该企业中，请勿重复添加')

        else:
            validated_data = remove_none_data(validated_data)
            err = valid_member_data(validated_data, is_company_member=True)
            if err:
                return parameter_error_response(err=err)
            company_id = request.data.get('company_id', None)
            if not company_id:
                return parameter_error_response('未知公司')
            user_data = {}
            user_data['name'] = validated_data['name'] if validated_data.get('name') else validated_data['email']
            user_data['true_name'] = \
                validated_data.get('true_name') or "用户{}".format(randomPassword(length=4))
            if 'true_name' in validated_data.keys():
                user_data['true_name'] = validated_data['true_name']
            if 'phone' in validated_data.keys():
                user_data['phone'] = validated_data['phone']
            if 'email' in validated_data.keys():
                user_data['email'] = validated_data['email']
            if 'area_code' in validated_data.keys():
                user_data['area_code'] = validated_data['area_code']
            if 'gender' in validated_data.keys():
                user_data['gender'] = validated_data['gender']
            if 'age' in validated_data.keys():
                user_data['age'] = validated_data['age']
            password = randomPassword()
            pwd = aesencrypt(password) if 'email' in validated_data.keys() else aesencrypt('123456')
            user_data['password'] = pwd

        with transaction.atomic():
            if request.data.get('user_id', 0):
                user = User.objects.get(pk=request.data.get('user_id', 0))
            else:
                user = User.objects.create(**user_data)
            validated_data['user_id'] = user.pk
            serializer = self.get_serializer(data=validated_data)
            serializer.is_valid(raise_exception=True)
            serializer.save()
        return success_response(serializer.data)

    @swagger_auto_schema(
        operation_id='批量添加企业成员',
        operation_summary='批量添加企业成员',
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'company_id': openapi.Schema(type=openapi.TYPE_NUMBER, description='企业id'),
                'file': openapi.Schema(type=openapi.TYPE_FILE, description='批量添加企业成员excel'),
            }
        ),
        tags=['企业成员相关']
    )
    @action(methods=['post'], detail=False, url_path='bulk_import_company_member')
    def bulk_import_company_member(self, request, *args, **kwargs):
        try:
            file = request.FILES['file']
            company_id = request.data.get('company_id')
        except:
            return parameter_error_response()
        if not Company.objects.filter(id=company_id).exists():
            return parameter_error_response(err='当前公司不存在')
        company = Company.objects.get(id=company_id)
        file_name = file.name
        file_type = file_name.split('.')[-1]
        if file_type not in ('xls', 'xlsx'):
            return parameter_error_response(err='模版格式错误,请上传xls，xlsx格式文件')
        max_size = 1048576
        if file.size > max_size:
            return parameter_error_response(err='模版文件大于1MB请重新上传')
        temp_dir = tempfile.mkdtemp()
        path = temp_dir + '/' + file_name
        with open(path, 'ab') as f:
            for chunk in file.chunks():
                f.write(chunk)
        data_lst, err_lst, header_validation = member_data_clean(file_type, path, company_id)
        if not header_validation:
            return parameter_error_response('模版错误', {"is_template": 1})
        if err_lst:
            return parameter_error_response(err=err_lst)
        if data_lst:
            with transaction.atomic():
                for data in data_lst:
                    user_data = {}
                    user_data['name'] = data['name'] if data.get('name') else data.get('email')
                    user_data['true_name'] = \
                        data.get('true_name') or "用户{}".format(randomPassword(length=4))
                    if 'phone' in data.keys():
                        user_data['phone'] = data['phone']
                    if 'email' in data.keys():
                        user_data['email'] = data['email']
                    if 'area_code' in data.keys():
                        user_data['area_code'] = data['area_code']
                    password = randomPassword()
                    pwd = aesencrypt(password) if 'email' in data.keys() else aesencrypt('123456')
                    user_data['password'] = pwd
                    user = User.objects.create(**user_data)
                    data = remove_user_data(data)
                    data['user_id'] = user.id
                    data['company_id'] = company.id
                    CompanyMember.objects.create(**data)
        return success_response()

    @swagger_auto_schema(
        operation_id='企业成员数据校验',
        operation_summary='企业成员数据校验',
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={

                'phone': openapi.Schema(type=openapi.TYPE_STRING, description='用户手机号'),
                'email': openapi.Schema(type=openapi.TYPE_STRING, description='用户邮箱'),
                'id_number': openapi.Schema(type=openapi.TYPE_STRING, description='用户身份证号'),
                'name': openapi.Schema(type=openapi.TYPE_STRING, description='用户名'),
                'company_manage_id': openapi.Schema(type=openapi.TYPE_NUMBER, description='企业管理员id')
            }
        ),
        tags=['企业成员相关']
    )
    @action(methods=['post'], detail=False, url_path='data_validation')
    def data_validation(self, request, *args, **kwargs):
        phone = request.data.get('phone', None)
        email = request.data.get('email', None)
        id_number = request.data.get('id_number', None)
        name = request.data.get('name', None)
        company_manage_id = request.data.get('company_manage_id', None)
        if company_manage_id:
            user_backends = UserBackend.objects.get(id=company_manage_id)
            user = user_backends.user
            if name and User.objects.filter(name=name, deleted=False).exclude(id=user.id).exists():
                return success_response({'status': 0})
            if email and User.objects.filter(email=email, deleted=False).exclude(id=user.id).exists():
                return success_response({'status': 0})
        else:
            if name and User.objects.filter(name=name, deleted=False).exists():
                return success_response({'status': 0})
            if phone and User.objects.filter(phone=phone, deleted=False).exists():
                return success_response({'status': 0})
            if email and User.objects.filter(email=email, deleted=False).exists():
                return success_response({'status': 0})
            if id_number and CompanyMember.objects.filter(id_number=id_number, user__deleted=False).exists():
                return success_response({'status': 0})

        return success_response({'status': 1})






