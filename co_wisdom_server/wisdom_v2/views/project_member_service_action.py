
from rest_framework import serializers

from wisdom_v2.models_file import ProjectMemberServiceContent


class ProjectMemberServiceSerializer(serializers.ModelSerializer):
    """
    项目服务内容
    """
    id = serializers.CharField(help_text='id')
    content_type = serializers.IntegerField(help_text='内容类型')
    content = serializers.CharField(help_text='内容')
    project_member_id = serializers.CharField(help_text='项目用户id')

    class Meta:
        model = ProjectMemberServiceContent
        fields = ('id', 'content_type', 'content', 'project_member_id')

