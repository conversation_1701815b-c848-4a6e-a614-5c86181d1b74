from ast import literal_eval

from django.db import transaction
from rest_framework.decorators import action
from rest_framework.viewsets import ModelViewSet

from co_wisdom_server.base import NOT_SELECT_IMAGE_URL
from utils.pagination import StandardResultsSetPagination
from utils.api_response import success_response, parameter_error_response
from drf_yasg.utils import swagger_auto_schema
from drf_yasg import openapi
from django.db.models import Case, When

from wisdom_v2.common import total_template_public
from wisdom_v2.models import TotalTemplate, InterviewRecordTemplate, InterviewRecordTemplateQuestion, \
    TotalTemplateReport, RelatedQuestion
from wisdom_v2.views.total_template_action import TotalTemplateListSerializer, TotalTemplateReportListSerializer
from wisdom_v2.views.project_member_action import remove_none_data
from wisdom_v2.enum.project_interview_enum import TemplateTypeEnum, TotalTemplateTypeEnum
from wisdom_v2.enum.service_content_enum import InterviewRecordTemplateTypeEnum, InterviewRecordTemplateRoleEnum

class TotalTemplateViewSet(ModelViewSet):
    queryset = TotalTemplate.objects.all().order_by('-created_at')
    serializer_class = TotalTemplateListSerializer
    authentication_classes = []

    @swagger_auto_schema(
        operation_id='模版列表',
        operation_summary='模版列表',
        manual_parameters=[
            openapi.Parameter('type', openapi.IN_QUERY, description='模版类型 1-一对一辅导模版（见习）2-教练任务模版',
                              type=openapi.TYPE_NUMBER, required=True),
            openapi.Parameter('title', openapi.IN_QUERY, description='模版标题', type=openapi.TYPE_STRING,
                              required=False),
            openapi.Parameter('is_service', openapi.IN_QUERY, description='是否在服务内容中调用 1-是 0-否',
                              type=openapi.TYPE_NUMBER, required=False)
        ],
        tags=['总模版相关']
    )
    def list(self, request, *args, **kwargs):
        try:
            type = request.query_params['type']
            title = request.query_params.get('title')
            is_service = request.query_params.get('is_service')
        except:
            return parameter_error_response()

        queryset = self.get_queryset().filter(type=type)
        if title:
            queryset = queryset.filter(title__icontains=title)
        if is_service:
            queryset = queryset.filter(status=1)

        # 分页
        paginator = StandardResultsSetPagination()
        page_list = paginator.paginate_queryset(queryset, self.request)
        serializer = self.get_serializer(page_list, many=True)
        response = paginator.get_paginated_response(serializer.data)
        return success_response(response)

    @swagger_auto_schema(
        operation_id='教练任务上下架/见习模版启用禁用',
        operation_summary='教练任务上下架/见习模版启用禁用',
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'total_template_id': openapi.Schema(type=openapi.TYPE_NUMBER, description='模版id'),
                'type': openapi.Schema(type=openapi.TYPE_NUMBER, description='模版类型 1-见习模版 2-教练任务模版'),
                'status': openapi.Schema(type=openapi.TYPE_NUMBER, description='0-禁用 1-启用')
            }
        ),
        tags=['总模版相关']
    )
    @action(methods=['post'], detail=False, url_path='enable_conf')
    def enable_conf(self, request, *args, **kwargs):
        try:
            total_template_id = request.data.get('total_template_id')
            template = TotalTemplate.objects.get(pk=total_template_id)
            type = int(request.data.get('type'))
            status = int(request.data.get('status'))
        except:
            return parameter_error_response()
        if status == 1:
            if not template.coachee_template_id and not template.coach_template_id and not template.stakeholder_template_id:
                return parameter_error_response('当前模版教练问卷/被教练者问卷/利益相关者问卷未完成')
        if type in [TotalTemplateTypeEnum.project_one_to_one.value, TotalTemplateTypeEnum.one_to_one_tutor.value] :  # 见习一对一模版
            if status == 1:
                exist_total_template = TotalTemplate.objects.filter(type=type, status=status).\
                    exclude(pk=total_template_id).first()
                if exist_total_template:
                    exist_total_template.status = 0
                    exist_total_template.save()
            template.status = status
        else:  # 教练任务模版
            template.status = status
        template.save()
        return success_response()

    @swagger_auto_schema(
        operation_id='创建总模版',
        operation_summary='创建总模版',
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'title': openapi.Schema(type=openapi.TYPE_NUMBER, description='模版标题'),
                'type': openapi.Schema(type=openapi.TYPE_NUMBER, description='模版类型 1-见习模版 2-教练任务模版'),
                'write_role': openapi.Schema(type=openapi.TYPE_NUMBER, description='填写人 1-教练 2-被教练 3-教练及被教练者 4-利益相关者'),
                'image_url': openapi.Schema(type=openapi.TYPE_NUMBER, description='展示图片链接')
            }
        ),
        tags=['总模版相关']
    )
    def create(self, request, *args, **kwargs):
        try:
            total_template_type = int(request.data['type'])
            title = request.data['title']
            write_role = request.data.get('write_role')
            image_url = request.data.get('image_url')
            image_url = image_url if image_url else NOT_SELECT_IMAGE_URL
        except:
            return parameter_error_response()

        if write_role:
            write_role = int(write_role)
        else:
            return parameter_error_response('请选择填写人')

        data = {
            'title': title,
            'write_role': write_role,
            'questions_order': '[]',
            'image_url': image_url,
            'type': total_template_type,
        }
        if total_template_type == TotalTemplateTypeEnum.one_to_one_tutor.value:
            template_type = InterviewRecordTemplateTypeEnum.one_to_one.value
        elif total_template_type == TotalTemplateTypeEnum.coach_task.value:
            template_type = InterviewRecordTemplateTypeEnum.coach_task.value
        elif total_template_type == TotalTemplateTypeEnum.project_one_to_one.value:
            template_type = InterviewRecordTemplateTypeEnum.project_one_to_one.value
        else:
            return parameter_error_response('模版类型错误')

        if write_role == 1:  # 利益相关者访谈模版
            coach_template = InterviewRecordTemplate.objects.create(
                name='%s利益相关者访谈报告' % title,
                role=InterviewRecordTemplateRoleEnum.coach.value,
                type=template_type)
            data['coach_template_id'] = coach_template.pk
        elif write_role == 4:
            stakeholder_template = InterviewRecordTemplate.objects.create(
                name='%s利益相关者问卷' % title,
                role=InterviewRecordTemplateRoleEnum.stakeholder.value,
                type=template_type)
            data['stakeholder_template_id'] = stakeholder_template.pk
        else:
            coach_template = InterviewRecordTemplate.objects.create(
                name='%s教练问卷' % title,
                role=InterviewRecordTemplateRoleEnum.coach.value,
                type=template_type)

            data['coach_template_id'] = coach_template.pk
            coachee_template = InterviewRecordTemplate.objects.create(
                name='%s被教练者教练问卷' % title,
                role=InterviewRecordTemplateRoleEnum.student.value,
                type=template_type)
            data['coachee_template_id'] = coachee_template.pk
        template = TotalTemplate.objects.create(**data)
        serializer = self.get_serializer(template)
        return success_response(serializer.data)

    @swagger_auto_schema(
        operation_id='模版详情中关联问题列表',
        operation_summary='模版详情中关联问题列表',
        manual_parameters=[
            openapi.Parameter('total_template_id', openapi.IN_QUERY, description='总模版id',
                              type=openapi.TYPE_NUMBER, required=True),
            openapi.Parameter('template_type', openapi.IN_QUERY, type=openapi.TYPE_NUMBER,
                              description='模版类型 1-教练问卷 2-被教练问卷')
        ],
        tags=['总模版相关']
    )
    @action(methods=['get'], detail=False, url_path='related_question_list')
    def related_question_list(self, request, *args, **kwargs):
        try:
            total_template_id = request.query_params.get('total_template_id')
            total_template = TotalTemplate.objects.get(pk=total_template_id)
            template_type = int(request.query_params.get('template_type'))
        except:
            return parameter_error_response()
        if template_type == 1:  # 展示被教练者问卷题目id及title
            if total_template.coachee_template_id:
                coachee_template = total_template.coachee_template
                data = list(InterviewRecordTemplateQuestion.objects.filter(
                    template_id=coachee_template.pk, deleted=False).values('id', 'title'))
            else:
                data = []
        else:  # 展示教练问卷题目id及title
            if total_template.coach_template_id:
                coach_template = total_template.coach_template
                data = list(InterviewRecordTemplateQuestion.objects.filter(
                    template_id=coach_template.pk, deleted=False).values('id', 'title'))
            else:
                data = []
        return success_response(data)

    @swagger_auto_schema(
        operation_id='复制总模版',
        operation_summary='复制总模版',
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'total_template_id': openapi.Schema(type=openapi.TYPE_NUMBER, description='模版id'),
            }
        ),
        tags=['总模版相关']
    )
    @action(methods=['post'], detail=False, url_path='copy')
    def copy_total_template(self, request, *args, **kwargs):
        try:
            total_template_id = request.data.get('total_template_id')
            title = request.data.get('title')
            total_template = TotalTemplate.objects.get(pk=total_template_id)
        except TotalTemplate.DoesNotExist:
            return parameter_error_response('模板标识错误')
        except Exception:
            return parameter_error_response()

        with transaction.atomic():
            now_total_template = TotalTemplate.objects.create(
                title=title,
                write_role=total_template.write_role,
                image_url=total_template.image_url,
                type=total_template.type,
            )

            if total_template.coach_template_id:
                now_coach_template = total_template_public.cp_interview_record_template(total_template.coach_template)
                now_total_template.coach_template_id = now_coach_template.id

            if total_template.coachee_template_id:
                now_coachee_template = total_template_public.cp_interview_record_template(total_template.coachee_template)
                now_total_template.coachee_template_id = now_coachee_template.id

            if total_template.stakeholder_template_id:
                now_stakeholder_template = total_template_public.cp_interview_record_template(total_template.stakeholder_template)
                now_total_template.stakeholder_template_id = now_stakeholder_template.id

            total_template_public.cp_template_related_question(total_template, now_total_template)

            now_total_template.save()
        return success_response()


class TotalTemplateReportViewSet(ModelViewSet):
    queryset = TotalTemplateReport.objects.all()
    serializer_class = TotalTemplateReportListSerializer

    @swagger_auto_schema(
        operation_id='模版报告列表',
        operation_summary='模版报告列表',
        manual_parameters=[
            openapi.Parameter('total_template_id', openapi.IN_QUERY, description='总模版id',
                              type=openapi.TYPE_NUMBER, required=True),
        ],
        tags=['总模版报告相关']
    )
    def list(self, request, *args, **kwargs):
        try:
            total_template_id = request.query_params.get('total_template_id')
            total_template = TotalTemplate.objects.get(pk=total_template_id)
        except:
            return parameter_error_response()
        queryset = self.get_queryset().filter(total_template_id=total_template.pk)
        order = Case(*[When(pk=order, then=pos) for pos, order in enumerate(literal_eval(total_template.questions_order))])
        queryset = queryset.order_by(order)
        # 分页
        paginator = StandardResultsSetPagination()
        page_list = paginator.paginate_queryset(queryset, self.request)
        serializer = self.get_serializer(page_list, many=True)
        response = paginator.get_paginated_response(serializer.data)
        return success_response(response)

    @swagger_auto_schema(
        operation_id='创建报告标题',
        operation_summary='创建报告标题',
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'title': openapi.Schema(type=openapi.TYPE_STRING, description='报告标题'),
                'title_type': openapi.Schema(type=openapi.TYPE_NUMBER, description='标题类型 | 1：一级标题；2：二级标题'),
                'description': openapi.Schema(type=openapi.TYPE_STRING, description='说明'),
                'total_template_id': openapi.Schema(type=openapi.TYPE_NUMBER, description='总模版id'),
                'coach_question_id': openapi.Schema(type=openapi.TYPE_NUMBER, description='教练问卷题目id'),
                'coachee_question_id': openapi.Schema(type=openapi.TYPE_NUMBER, description='被教练者教练问卷题目id'),
                'order': openapi.Schema(type=openapi.TYPE_NUMBER, description='问题排序')
            }
        ),
        tags=['总模版报告相关']
    )
    def create(self, request, *args, **kwargs):
        try:
            total_template_id = request.data.get('total_template_id')
            total_template = TotalTemplate.objects.get(pk=total_template_id)
            title = request.data['title']
            title_type = int(request.data['title_type'])
        except:
            return parameter_error_response()
        data = request.data.copy()
        data = remove_none_data(data)
        related_data = {}
        if 'coach_question_id' in data.keys():
            related_data['coach_question_id'] = data['coach_question_id']
            data.pop('coach_question_id')
        if 'coachee_question_id' in data.keys():
            related_data['coachee_question_id'] = data['coachee_question_id']
            data.pop('coachee_question_id')
        if related_data:
            related_data['template_type'] = TemplateTypeEnum.report.value
            related_question = RelatedQuestion.objects.create(**related_data)
            data['related_question_id'] = related_question.pk

        order = data['order'] if 'order' in data else None
        data.pop('order')

        report = TotalTemplateReport.objects.create(**data)
        questions_order = literal_eval(report.total_template.questions_order)
        # 模板中位置顺序录入
        if order is not None:
            questions_order.insert(order, report.pk)
        else:
            questions_order.append(report.pk)
        report.total_template.questions_order = str(questions_order)
        report.total_template.save()
        serializer = self.get_serializer(report)
        return success_response(serializer.data)

    @swagger_auto_schema(
        operation_id='编辑报告标题',
        operation_summary='编辑报告标题',
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'id': openapi.Schema(type=openapi.TYPE_NUMBER, description='报告id'),
                'title': openapi.Schema(type=openapi.TYPE_STRING, description='报告标题'),
                'title_type': openapi.Schema(type=openapi.TYPE_NUMBER, description='标题类型 | 1：一级标题；2：二级标题'),
                'description': openapi.Schema(type=openapi.TYPE_STRING, description='说明'),
                'coach_question_id': openapi.Schema(type=openapi.TYPE_NUMBER, description='教练问卷题目id'),
                'coachee_question_id': openapi.Schema(type=openapi.TYPE_NUMBER, description='被教练者教练问卷题目id'),
                "deleted": openapi.Schema(type=openapi.TYPE_NUMBER, description='是否删除 0-否 1-是'),
                'order': openapi.Schema(type=openapi.TYPE_NUMBER,description='问题排序')
            }
        ),
        tags=['总模版报告相关']
    )
    @action(methods=['post'], detail=False, url_path='update')
    def update_report(self, request, *args, **kwargs):
        try:
            id = request.data.get('id')
            report = TotalTemplateReport.objects.get(pk=id)
            title = request.data.get('title')
            title_type = int(request.data.get('title_type', 0))
        except:
            return parameter_error_response()
        data = request.data.copy()
        if request.data.get('order') is not None:
            questions_order = literal_eval(report.total_template.questions_order)
            questions_order.remove(report.pk)
            questions_order.insert(request.data.get('order'), report.pk)
            report.total_template.questions_order = str(questions_order)
            report.total_template.save()
        if 'deleted' in data.keys() and data['deleted']:
            questions_order = literal_eval(report.total_template.questions_order)
            if questions_order:
                questions_order.remove(report.pk)
            report.total_template.questions_order = str(questions_order)
            report.total_template.save()

            # TotalTemplateReport表设计的时候没有加deleted字段，所以使用物理删除
            # 为了防止related_question反查询报错，related_question也使用物理删除
            if report.related_question_id:
                related_question = report.related_question
                related_question.delete()
            report.delete()
            return success_response()
        if 'order' in data.keys():
            data.pop('order')
        data.pop('id')
        data = remove_none_data(data)
        if report.related_question_id:
            related_question = report.related_question
            if 'coach_question_id' in data.keys():
                related_question.coach_question_id = data['coach_question_id']
                data.pop('coach_question_id')
            if 'coachee_question_id' in data.keys():
                related_question.coachee_question_id = data['coachee_question_id']
                data.pop('coachee_question_id')
            related_question.save()
        else:
            r_data = {}
            if 'coach_question_id' in data.keys():
                r_data['coach_question_id'] = data['coach_question_id']
                data.pop('coach_question_id')
            if 'coachee_question_id' in data.keys():
                r_data['coachee_question_id'] = data['coachee_question_id']
                data.pop('coachee_question_id')
            if r_data:
                r_data['template_type'] = 3
                r_question = RelatedQuestion.objects.create(**r_data)
                data['related_question_id'] = r_question.pk
        TotalTemplateReport.objects.filter(pk=report.pk).update(**data)
        serializer = self.get_serializer(report)
        return success_response(serializer.data)
