from ast import literal_eval

from rest_framework import serializers
from django.db.models import Case, When

from wisdom_v2.common import coach_public
from wisdom_v2.common.coach_task_public import check_coach_task_is_stakeholder_interview_summary
from wisdom_v2.enum.user_enum import UserRoleEnum
from wisdom_v2.models import Coach<PERSON><PERSON>, InterviewR<PERSON>ordTemplateAnswer, InterviewR<PERSON>ordT<PERSON>plateOption, User, \
    InterviewRecordTemplateQuestion, TotalTemplateReport, TotalTemplate
from wisdom_v2.enum.service_content_enum import InterviewRecordTemplateQuestionTypeEnum, \
    InterviewRecordTemplateQuestionAnswerTypeEnum, NewCoachTaskTypeEnum
from wisdom_v2.views.interview_question_actions import InterviewRecordTemplateQuestionSerializers


class CoachTaskSerlizer(serializers.ModelSerializer):
    name = serializers.CharField(source='template.title', read_only=True)
    coach_name = serializers.SerializerMethodField(read_only=True)
    coachee_name = serializers.SerializerMethodField(read_only=True)
    template_id = serializers.IntegerField(read_only=True)
    coach_submit_time = serializers.DateTimeField(read_only=True, format='%Y.%m.%d')
    coachee_submit_time = serializers.DateTimeField(read_only=True, format='%Y.%m.%d')
    write_role = serializers.IntegerField(source='template.write_role', read_only=True)

    def get_coach_name(self, obj):
        if obj.public_attr_id:
            return obj.public_attr.user.cover_name if obj.public_attr.user_id else '--'
        return '--'

    def get_coachee_name(self, obj):
        return obj.project_bundle.project_member.user.name

    class Meta:
        model = CoachTask
        fields = ['id', 'coach_name', 'name', 'coachee_name', 'template_id', 'coach_submit_time',
                  'coachee_submit_time', 'write_role', 'type']


def check_answer_data(question_data, coach_task_id, is_single=False):
    if not question_data:
        return '当前数据不存在'

    coach_task = CoachTask.objects.get(pk=coach_task_id)
    if coach_task.type == NewCoachTaskTypeEnum.default_task and not coach_task.public_attr.user_id:
        return '当前用户未匹配教练'

    order_lst = []
    for index, question in enumerate(question_data):
        # 填空题判断
        if question['type'] == InterviewRecordTemplateQuestionTypeEnum.blank.value:
            # 普通文本框
            if question['answer_type'] in [
                    InterviewRecordTemplateQuestionAnswerTypeEnum.normal_text.value,
                    InterviewRecordTemplateQuestionAnswerTypeEnum.multiple_text.value]:
                if question['answer']:
                    if 'content' not in question['answer'].keys():
                        return '问题%s缺少回答' % question['title']
                    if not question['answer']['content']:
                        return '请填写问题%s的答案' % question['title']

                    if 'id' in question['answer'].keys():
                        answer = InterviewRecordTemplateAnswer.objects.filter(pk=question['answer']['id']).first()
                        if not answer:
                            return '问题%s回答数据错误' % question['title']
                        if answer.question_id != question['id']:
                            return '问题%s回答数据与问题冲突' % question['title']
                    else:
                        answer = InterviewRecordTemplateAnswer.objects.filter(
                            public_attr=coach_task.public_attr,
                            question_id=question['id'],
                            question__type=InterviewRecordTemplateQuestionTypeEnum.blank.value,
                            question__answer_type=question['answer_type'])
                        if answer.exists():
                            return '问题%s回答数据错误，缺少问题标识' % question['title']
                else:
                    if not is_single:
                        if question['required']:
                            order_lst.append(index + 1)

            # 能力标签
            if question['answer_type'] == InterviewRecordTemplateQuestionAnswerTypeEnum.power_tag.value:
                if question['answer']:
                    if 'content' not in question['answer'].keys():
                        return '问题%s缺少回答' % question['title']
                    if not question['answer']['content']:
                        return '请填写问题%s的答案' % question['title']

                    if 'id' in question['answer'].keys():
                        answer = InterviewRecordTemplateAnswer.objects.filter(
                            pk=question['answer']['id']).first()
                        if not answer:
                            return '问题%s回答数据错误' % question['title']
                        if answer.question_id != question['id']:
                            return '问题%s回答数据与问题冲突' % question['title']
                    else:
                        answer = InterviewRecordTemplateAnswer.objects.filter(
                            public_attr=coach_task.public_attr,
                            question_id=question['id'],
                            question__type=InterviewRecordTemplateQuestionTypeEnum.blank.value,
                            question__answer_type=InterviewRecordTemplateQuestionAnswerTypeEnum.power_tag.value)
                        if answer.exists():
                            return '问题%s回答数据错误，缺少问题标识' % question['title']
                else:
                    if not is_single:
                        if question['required']:
                            order_lst.append(index + 1)

            # 行动计划
            if question['answer_type'] == InterviewRecordTemplateQuestionAnswerTypeEnum.action_plan.value:
                # if 'content' in question.keys() and question['content'] and 'end_date' in question.keys() \
                #         and question['end_date']:
                if question['answer']:
                    for answer_f in question['answer']:
                        if 'content' not in answer_f.keys():
                            return '问题%s缺少回答' % question['title']
                        if not answer_f['content']:
                            return '请填写问题%s的答案' % question['title']
                        if 'end_date' not in answer_f.keys():
                            return '问题%s缺少结束日期' % question['title']

                        if 'id' in answer_f.keys():
                            answer = InterviewRecordTemplateAnswer.objects.filter(pk=answer_f['id']).first()
                            if not answer:
                                return '问题%s回答数据错误' % question['title']
                            if answer.question_id != question['id']:
                                return '问题%s回答数据与问题冲突' % question['title']

                else:
                    if not is_single:
                        if question['required']:
                            order_lst.append(index + 1)

            # 习惯养成
            if question['answer_type'] == InterviewRecordTemplateQuestionAnswerTypeEnum.habit_formation.value:
                if question['answer']:
                    for answer_f in question['answer']:
                        if 'when' not in answer_f.keys() or not answer_f['when']:
                            return '问题%s缺少何时答案' % question['title']
                        if 'stop' not in answer_f.keys() or not answer_f['stop']:
                            return '问题%s缺少停止答案' % question['title']
                        if 'change' not in answer_f.keys() or not answer_f['change']:
                            return '问题%s缺少改变答案' % question['title']
                        if 'end_date' not in answer_f.keys() or not answer_f['end_date']:
                            return '问题%s缺少结束日期' % question['title']
                        if 'start_date' not in answer_f.keys() or not answer_f['start_date']:
                            return '问题%s缺少开始日期' % question['title']

                        if 'id' in answer_f.keys():
                            answer = InterviewRecordTemplateAnswer.objects.filter(pk=answer_f['id']).first()
                            if not answer:
                                return '问题%s回答数据错误' % question['title']
                            if answer.question_id != question['id']:
                                return '问题%s回答数据与问题冲突' % question['title']

                        # else:
                        #     answer = InterviewRecordTemplateAnswer.objects.filter(
                        #         interview_id=project_interview.id, public_attr=project_interview.public_attr,
                        #         question_id=question['id'], question__type=InterviewRecordTemplateQuestionTypeEnum.blank.value,
                        #         question__answer_type=InterviewRecordTemplateQuestionAnswerTypeEnum.action_plan.value)
                        #     if answer.exists():
                        #         return '问题%s回答数据错误，缺少问题标识' % question['title']

                else:
                    if not is_single:
                        if question['required']:
                            order_lst.append(index + 1)

            # 成长笔记
            if question['answer_type'] == InterviewRecordTemplateQuestionAnswerTypeEnum.growth_notes.value:
                if question['answer']:
                    if 'content' not in question['answer'].keys() or not question['answer']['content']:
                        return '问题%s缺少答案' % question['title']

                    if 'id' in question['answer'].keys():
                        answer = InterviewRecordTemplateAnswer.objects.filter(pk=question['answer']['id']).first()
                        if not answer:
                            return '问题%s回答数据错误' % question['title']
                        if answer.question_id != question['id']:
                            return '问题%s回答数据与问题冲突' % question['title']

                    else:
                        answer = InterviewRecordTemplateAnswer.objects.filter(
                            public_attr=coach_task.public_attr,
                            question_id=question['id'],
                            question__type=InterviewRecordTemplateQuestionTypeEnum.blank.value,
                            question__answer_type=InterviewRecordTemplateQuestionAnswerTypeEnum.growth_notes.value)
                        if answer.exists():
                            return '问题%s回答数据错误，缺少问题标识' % question['title']

                else:
                    if not is_single:
                        if question['required']:
                            order_lst.append(index + 1)

        # 单选
        if question['type'] == InterviewRecordTemplateQuestionTypeEnum.single.value:
            if question['answer']:
                if 'option' not in question['answer'][0].keys():
                    return '问题%s缺少选项' % question['title']
                option_lst = InterviewRecordTemplateOption.objects.filter(question_id=question['id'], deleted=False). \
                    values_list('id', flat=True)
                option_lst = list(option_lst)
                if question['answer'][0]['option'] not in option_lst:
                    return '问题%s选项错误' % question['title']
                for i in question['option']:
                    if i['id'] == question['answer'][0]['option']:
                        if i['user_defined']:
                            if 'option_custom' not in question['answer'][0].keys():
                                return '请在第%s题的文本框中输入回答' % str(index + 1)
                            if not question['answer'][0]['option_custom']:
                                return '请在第%s题的文本框中输入回答' % str(index + 1)

            else:
                if not is_single:
                    if question['required']:
                        order_lst.append(index + 1)

        # 多选
        if question['type'] == InterviewRecordTemplateQuestionTypeEnum.multiple.value:
            if question['answer']:
                option_lst = InterviewRecordTemplateOption.objects.filter(question_id=question['id'], deleted=False). \
                    values_list('id', flat=True)
                option_lst = list(option_lst)
                for option in question['answer']:
                    if 'option' not in option.keys():
                        return '问题%s缺少选项' % question['title']

                    if option['option'] not in option_lst:
                        return '问题%s选项错误' % question['title']
                    for i in question['option']:
                        if i['id'] == option['option']:
                            if i['user_defined'] and 'option_custom' not in option.keys():
                                return '请在第%s题的文本框中输入回答' % str(index + 1)
                            if i['user_defined'] and not option['option_custom']:
                                return '请在第%s题的文本框中输入回答' % str(index + 1)

            else:
                if not is_single:
                    if question['required']:
                        order_lst.append(index + 1)

        # 评分
        if question['type'] == InterviewRecordTemplateQuestionTypeEnum.rating.value:
            if question['answer']:
                if 'score' not in question['answer'].keys():
                    return '问题%s缺少分数' % question['title']
                if type(question['answer']['score']) != int:
                    return '问题%s分数类型错误' % question['title']

                if 'id' in question['answer'].keys():
                    answer = InterviewRecordTemplateAnswer.objects.filter(pk=question['answer']['id']).first()
                    if not answer:
                        return '问题%s回答数据错误' % question['title']
                    if answer.question_id != question['id']:
                        return '问题%s回答数据与问题冲突' % question['title']
                else:
                    answer = InterviewRecordTemplateAnswer.objects.filter(
                        public_attr=coach_task.public_attr,
                        question_id=question['id'], question__type=InterviewRecordTemplateQuestionTypeEnum.rating.value)
                    if answer.exists():
                        return '问题%s回答数据错误，缺少问题标识' % question['title']
            else:
                if not is_single:
                    if question['required']:
                        order_lst.append(index + 1)

    if order_lst:
        order = '、'.join(map(str, order_lst))
        return '请填写第' + order + '题'


class CoachTaskQuestionnaireSerializer(serializers.ModelSerializer):
    name = serializers.CharField(source='template.title', read_only=True)
    write_role = serializers.IntegerField(source='template.write_role', read_only=True)
    coach_info = serializers.SerializerMethodField()
    coachee_info = serializers.SerializerMethodField()
    question_detail = serializers.SerializerMethodField()
    remind_text = serializers.SerializerMethodField()
    preface = serializers.SerializerMethodField()
    is_summary_report = serializers.SerializerMethodField()

    def get_is_summary_report(self, obj):
        return check_coach_task_is_stakeholder_interview_summary(obj.id)


    def get_preface(self, obj):
        if obj.type == NewCoachTaskTypeEnum.default_task:
            role = self.context.get('role', None)
            preface = None
            if role in [UserRoleEnum.coach, UserRoleEnum.trainee_coach]:
                if obj.template.coach_template:
                    preface = obj.template.coach_template.preface
            else:
                if obj.template.coachee_template:
                    preface = obj.template.coachee_template.preface
            return preface
        elif obj.type == NewCoachTaskTypeEnum.stakeholder_research:
            return obj.template.stakeholder_template.preface
        return

    def get_remind_text(self, obj):

        if obj.type == NewCoachTaskTypeEnum.stakeholder_research:
            user = obj.public_attr.target_user
            return f'正在给{user.cover_name}填写反馈...'
        if self.context.get('role') in [
                UserRoleEnum.coach, UserRoleEnum.trainee_coach]:

            user = obj.public_attr.target_user
            return f'正在给{user.cover_name}填写{obj.template.title}...'
        return

    def get_coach_info(self, obj):
        coach_user_id = obj.public_attr.user_id
        if coach_user_id:
            user = User.objects.get(pk=coach_user_id)
            status, resume = coach_public.get_project_coach_resume(coach_user_id, obj.project_bundle.project_id)
            if status:
                head_image_url = resume.head_image_url
            else:
                head_image_url = user.head_image_url
            return {'id': user.pk, 'name': user.cover_name, 'head_image_url': head_image_url, 'level': '高级教练'}
        else:
            return {'id': None, 'name': None, 'head_image_url': None, 'level': '高级教练'}

    def get_coachee_info(self, obj):
        coachee_user_id = obj.public_attr.target_user_id
        user = User.objects.get(pk=coachee_user_id)
        return {'id': user.pk, 'name': user.cover_name, 'head_image_url': user.head_image_url}

    def get_question_detail(self, obj):
        role = self.context.get('role', None)
        total_template = obj.template
        if obj.type == NewCoachTaskTypeEnum.default_task:
            template = total_template.coach_template if role in [
                UserRoleEnum.coach, UserRoleEnum.trainee_coach] else total_template.coachee_template
        else:
            template = total_template.stakeholder_template
        order = Case(*[When(pk=order, then=pos) for pos, order in enumerate(literal_eval(template.questions_order))])
        question = InterviewRecordTemplateQuestion.objects.filter(
            template_id=template.pk, deleted=False).order_by(order).all()
        question = InterviewRecordTemplateQuestionSerializers(question, many=True)
        data = add_edit_data(question.data, obj.id, role=role)
        return data

    class Meta:
        model = CoachTask
        fields = ['id', 'name', 'write_role', 'coach_info',
                  'coachee_info', 'question_detail', 'type', 'preface', 'remind_text', 'is_summary_report']


def add_edit_data(questions, coach_task_id, role=None):
    from wisdom_v2.app_views.app_interview_actions import get_question_answer, get_opposite_question
    for question in questions:
        data = get_question_answer(coach_task_id, question['id'], is_coach_task=True)
        question['answer'] = data
        question['description'] = None
        question['opposite_answer'] = None
        if role:
            opposite_question_id, description = get_opposite_question(question['id'], role)
            if opposite_question_id:
                data = get_question_answer(coach_task_id, opposite_question_id, is_echo=True, is_coach_task=True)
                question['opposite_answer'] = data
                question['description'] = description
    return questions


class CoachTaskReportDetailSerializer(serializers.ModelSerializer):
    name = serializers.CharField(source='template.title', read_only=True)
    write_role = serializers.IntegerField(source='template.write_role', read_only=True)
    coach_info = serializers.SerializerMethodField()
    coachee_info = serializers.SerializerMethodField()
    question_detail = serializers.SerializerMethodField()
    created_time = serializers.SerializerMethodField()
    is_summary_report = serializers.SerializerMethodField()
    report_url = serializers.CharField(read_only=True)

    def get_is_summary_report(self, obj):
        return check_coach_task_is_stakeholder_interview_summary(obj.id)

    def get_created_time(self, obj):
        if obj.coach_submit_time:
            return obj.coach_submit_time.strftime('%Y.%m.%d %H:%M:%S')
        elif obj.coachee_submit_time:
            return obj.coachee_submit_time.strftime('%Y.%m.%d %H:%M:%S')
        else:
            return None

    def get_coach_info(self, obj):
        coach_user_id = obj.public_attr.user_id
        user = User.objects.get(pk=coach_user_id)
        status, resume = coach_public.get_project_coach_resume(coach_user_id, obj.project_bundle.project_id)
        if status:
            head_image_url = resume.head_image_url
        else:
            head_image_url = user.head_image_url
        return {'id': user.pk, 'name': user.cover_name, 'head_image_url': head_image_url, 'level': '高级教练'}

    def get_coachee_info(self, obj):
        coachee_user_id = obj.public_attr.target_user_id
        user = User.objects.get(pk=coachee_user_id)
        return {'id': user.pk, 'name': user.cover_name, 'head_image_url': user.head_image_url}

    def get_question_detail(self, obj):
        total_template = obj.template
        data = get_report_question(total_template.pk, obj.pk)
        return data

    class Meta:
        model = CoachTask
        fields = ['id', 'name', 'write_role', 'coach_info', 'coachee_info', 'question_detail', 'created_time', 'type',
                  'report_url', 'is_summary_report']


def get_report_question(total_template_id, coach_task_id):
    total_template = TotalTemplate.objects.filter(id=total_template_id).first()
    order = Case(*[When(pk=order, then=pos) for pos, order in enumerate(literal_eval(total_template.questions_order))])
    total_template_report = TotalTemplateReport.objects.filter(
        total_template_id=total_template_id).order_by(order)
    data = []
    for report in total_template_report:
        report_data = {'title': report.title, 'title_type': report.title_type, 'description': report.description,
                       'coach_data': [], 'coachee_data': []}
        if report.related_question_id:
            related_question = report.related_question
            if related_question.coach_question_id:
                question = InterviewRecordTemplateQuestionSerializers(related_question.coach_question)
                question = [question.data]
                question_data = add_edit_data(question, coach_task_id)
                report_data['coach_data'] = question_data

            if related_question.coachee_question_id:
                question = InterviewRecordTemplateQuestionSerializers(related_question.coachee_question)
                question = [question.data]
                question_data = add_edit_data(question, coach_task_id)
                report_data['coachee_data'] = question_data
        data.append(report_data)
    return data


class CoachTaskStakeholderReportDetailSerializer(serializers.ModelSerializer):

    write_role = serializers.IntegerField(source='template.write_role', read_only=True)
    user_info = serializers.SerializerMethodField(help_text='用户信息')
    detail = serializers.SerializerMethodField(help_text='详情')
    name = serializers.SerializerMethodField(help_text='标题')
    created_time = serializers.SerializerMethodField()

    def get_name(self, obj):
        return f'{obj.template.title}报告'

    def get_created_time(self, obj):
        if obj.stakeholder_submit_time:
            return obj.stakeholder_submit_time.strftime('%Y.%m.%d')
        else:
            return '--'

    def get_user_info(self, obj):

        relation = self.context.get('relation')

        coach_name = obj.public_attr.user.cover_name if obj.public_attr.user else '--'
        data = {
            'coach_name': coach_name,
            'coachee_name': obj.public_attr.target_user.cover_name,
            'stakeholder_name': [item.public_attr.user.cover_name for item in relation],
        }
        return data

    def get_detail(self, obj):
        data = []
        relation = self.context.get('relation')
        questions = obj.template.stakeholder_template.questions_order
        order = Case(*[When(pk=order, then=pos) for pos, order in enumerate(literal_eval(questions))])
        questions = InterviewRecordTemplateQuestion.objects.filter(
            template_id=obj.template.stakeholder_template.id, deleted=False).order_by(order).all()

        # 循环问题
        for question in questions:
            if question.type == InterviewRecordTemplateQuestionTypeEnum.blank and \
                    question.answer_type == InterviewRecordTemplateQuestionAnswerTypeEnum.multiple_text:
                raw_data = {
                    'title': question.title,
                    'answer': [],
                }
                # 循环利益相关者
                for item in relation:
                    answers = InterviewRecordTemplateAnswer.objects.filter(
                        public_attr=item.public_attr,
                        question_id=question.id
                    ).values_list('answer', flat=True)
                    for answer in answers:
                        raw_data['answer'] += [item for item in literal_eval(answer)]
                data.append(raw_data)
        return data

    class Meta:
        model = CoachTask
        fields = ['id', 'name', 'write_role', 'user_info', 'detail', 'created_time', 'type', 'report_url']
