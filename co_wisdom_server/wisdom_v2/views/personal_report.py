from drf_yasg import openapi
from rest_framework import viewsets
from drf_yasg.utils import swagger_auto_schema
from rest_framework.decorators import action

from utils.api_response import parameter_error_response, success_response
from utils.pagination import StandardResultsSetPagination
from wisdom_v2.enum.service_content_enum import PersonalReportTypeEnum
from wisdom_v2.models import PersonalReport, Project
from wisdom_v2.views.personal_report_action import PersonalReportSerializer


class PersonalReportViewSet(viewsets.ModelViewSet):
    queryset = PersonalReport.objects.all()
    serializer_class = PersonalReportSerializer

    @swagger_auto_schema(
        operation_id='后台个人报告列表',
        operation_summary='后台个人报告列表',
        manual_parameters=[
            openapi.Parameter(
                'project_id', openapi.IN_QUERY, description='项目id', type=openapi.TYPE_NUMBER, required=True),
            openapi.Parameter('page', openapi.IN_QUERY, description='第几页', type=openapi.TYPE_NUMBER),
            openapi.Parameter('page_size', openapi.IN_QUERY, description='每页显示条数默认15条', type=openapi.TYPE_NUMBER)
        ],
        tags=['后台个人报告相关']
    )
    def list(self, request, *args, **kwargs):
        try:
            project_id = request.query_params.get('project_id')
            project = Project.objects.get(pk=project_id)
        except:
            return parameter_error_response('未获取到项目信息')
        personal_report = self.get_queryset()
        change_observation = personal_report.filter(
            project=project,
            type=PersonalReportTypeEnum.change_observation_report)

        # 分页
        paginator = StandardResultsSetPagination()
        page_list = paginator.paginate_queryset(change_observation, self.request)
        serializer = self.get_serializer(page_list, many=True)
        response = paginator.get_paginated_response(serializer.data)
        return success_response(response)

    @swagger_auto_schema(
        operation_id='后台个人报告详情',
        operation_summary='后台个人报告详情',
        manual_parameters=[
            openapi.Parameter(
                'report_id	', openapi.IN_QUERY, description='个人报告id', type=openapi.TYPE_NUMBER,
                required=True),
        ],
        tags=['后台个人报告相关']
    )
    @action(methods=['get'], detail=False, url_path='detail')
    def personal_report_detail(self, request, *args, **kwargs):
        try:
            report_id = request.query_params.get('report_id')
            personal_report = PersonalReport.objects.get(pk=report_id)
        except:
            return parameter_error_response('未获取到报告信息')
        serializer = self.get_serializer(personal_report)
        return success_response(serializer.data)
