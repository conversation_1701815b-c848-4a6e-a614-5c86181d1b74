import datetime
from rest_framework import serializers
from wisdom_v2.models import Project<PERSON>ember, ProjectCoach, ProjectGroupCoach, Coach


class FiveALCProjectCoachSerializer(serializers.ModelSerializer):
    id = serializers.IntegerField(help_text='被教练者id', read_only=True)
    name = serializers.CharField(source='user.name', help_text='被教练者姓名', read_only=True)
    coach_name = serializers.SerializerMethodField(help_text='被教练者姓名', read_only=True)
    status = serializers.SerializerMethodField(help_text='状态，1-以匹配，0-未匹配', read_only=True)
    coach_id = serializers.SerializerMethodField(help_text='教练id', read_only=True)

    class Meta:
        model = ProjectMember
        fields = ['id', 'name', 'coach_name', 'status', 'coach_id']

    def get_coach_name(self, obj):
        project_coach = ProjectCoach.objects.filter(member_id=obj.user_id, project_id=obj.project_id,
                                                    deleted=False).first()
        if project_coach:
            return project_coach.coach.user.cover_name
        else:
            return ''

    def get_status(self, obj):
        project_coach = ProjectCoach.objects.filter(member_id=obj.user_id, project_id=obj.project_id,
                                                    deleted=False).first()
        if project_coach:
            return 1
        else:
            return 0

    def get_coach_id(self, obj):
        project_coach = ProjectCoach.objects.filter(member_id=obj.user_id, project_id=obj.project_id,
                                                    deleted=False).first()
        if project_coach:
            return project_coach.coach_id
        else:
            return ''


class ProjectGroupCoachListSerializer(serializers.ModelSerializer):
    id = serializers.IntegerField(help_text='集体辅导id', read_only=True)
    start_time = serializers.DateTimeField(help_text='开始辅导时间', read_only=True, format='%Y-%m-%d %H:%M')
    end_time = serializers.DateTimeField(help_text='结束辅导时间', read_only=True, format='%Y-%m-%d %H:%M')
    theme = serializers.CharField(help_text='辅导主题', read_only=True)
    place = serializers.CharField(help_text='辅导地点', read_only=True)
    coach_name = serializers.SerializerMethodField(help_text='教练姓名', read_only=True)
    coach_id = serializers.SerializerMethodField(help_text='教练id', read_only=True)
    status = serializers.SerializerMethodField(help_text='教练匹配状态', read_only=True)
    start_stop_time = serializers.SerializerMethodField(help_text='起止时间', read_only=True)

    class Meta:
        model = ProjectGroupCoach
        fields = ['id', 'start_time', 'end_time', 'theme', 'place', 'coach_name', 'status', 'coach_id',
                  'start_stop_time']

    def get_start_stop_time(self, obj):
        start_time = datetime.datetime.strftime(obj.start_time, '%Y-%m-%d %H:%M')
        end_time = datetime.datetime.strftime(obj.end_time, '%Y-%m-%d %H:%M')
        end_time = end_time.split(' ')[1]
        return start_time + '-' + end_time

    def get_coach_name(self, obj):
        project_coach = ProjectCoach.objects.filter(project_id=obj.project_id, project_group_coach_id=obj.id,
                                                    deleted=False).first()
        if project_coach:
            return project_coach.coach.user.cover_name
        else:
            return ''

    def get_status(self, obj):
        project_coach = ProjectCoach.objects.filter(project_id=obj.project_id, project_group_coach_id=obj.id,
                                                    deleted=False)
        if project_coach.exists():
            return 1
        else:
            return 0

    def get_coach_id(self, obj):
        project_coach = ProjectCoach.objects.filter(project_id=obj.project_id, project_group_coach_id=obj.id,
                                                    deleted=False).first()
        if project_coach:
            return project_coach.coach.id
        else:
            return 0


class CoachListSerializer(serializers.ModelSerializer):
    id = serializers.IntegerField(help_text='教练id', read_only=True)
    true_name = serializers.CharField(source='user.cover_name', read_only=True)

    class Meta:
        model = Coach
        fields = ['id', 'true_name']
