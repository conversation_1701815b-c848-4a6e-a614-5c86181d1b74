from drf_yasg import openapi
from rest_framework import viewsets
from drf_yasg.utils import swagger_auto_schema
from rest_framework.decorators import action

from wisdom_v2.models import Coach, TraineeCoach, Resume, User
from wisdom_v2.views.resume_action import ResumeEditSerializer, TraineeCoachResumeBaseSerializers, \
    CoachResumeBaseSerializers, ResumeBaseSerializer, ResumeDetailSerializer, ResumeUpdateSerializer, \
    ResumeCreateSerializer
from utils.api_response import success_response, parameter_error_response


class ResumeViewSet(viewsets.ModelViewSet):
    queryset = Resume.objects.filter(deleted=False).order_by('-created_at')
    serializer_class = ResumeBaseSerializer

    @swagger_auto_schema(
        operation_id='简历详情',
        operation_summary='简历详情',
        manual_parameters=[
            openapi.Parameter('resume_id', openapi.IN_QUERY, description='简历id', type=openapi.TYPE_NUMBER),
            openapi.Parameter('user_id', openapi.IN_QUERY, description='教练用户id', type=openapi.TYPE_NUMBER)
        ],
        tags=['教练简历相关']
    )
    @action(methods=['get'], detail=False, url_path='detail',
            serializer_class=ResumeDetailSerializer, authentication_classes=[])
    def resume_detail(self, request, *args, **kwargs):
        try:
            user_id = request.query_params.get('user_id')
            resume_id = request.query_params.get('resume_id')
            if resume_id:
                resume = Resume.objects.get(pk=resume_id)
            else:
                resume = Resume.objects.filter(coach__user_id=user_id, deleted=False,
                                               coach__deleted=False).order_by('created_at').first()
                name = resume.name
        except:
            return parameter_error_response()
        serializer = self.get_serializer(resume)
        return success_response(serializer.data)

    @swagger_auto_schema(
        operation_id='修改简历信息',
        operation_summary='修改简历信息',
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'type': openapi.Schema(type=openapi.TYPE_NUMBER, description='教练类型 1-签约教练 2-个人教练'),
                'user_id': openapi.Schema(type=openapi.TYPE_NUMBER, description='教练用户id'),
                'name': openapi.Schema(type=openapi.TYPE_STRING, description='用户名'),
                'email': openapi.Schema(type=openapi.TYPE_STRING, description='邮箱'),
                'phone': openapi.Schema(type=openapi.TYPE_STRING, description='手机号'),
                'gender': openapi.Schema(type=openapi.TYPE_NUMBER, description='性别'),
                'english_name': openapi.Schema(type=openapi.TYPE_STRING, description='英文名'),
                'head_image_url': openapi.Schema(type=openapi.TYPE_STRING, description='头像链接'),
                'birthday': openapi.Schema(type=openapi.TYPE_STRING, description='生日'),
                'user_class': openapi.Schema(
                    type=openapi.TYPE_STRING, description='班次'),
                'extra_time': openapi.Schema(
                    type=openapi.TYPE_NUMBER, description='平台外一对一辅导时常'),
                'working_years': openapi.Schema(
                    type=openapi.TYPE_NUMBER, description='工作年限 | 1：一年以上，2：三年以上，3：五年以上'),
                'language': openapi.Schema(
                    type=openapi.TYPE_NUMBER, description='语言｜ 1：中文，2：英语，3：法语，4：日语'),
                'work_experience': openapi.Schema(type=openapi.TYPE_STRING, description='工作经历'),
                'job_profile': openapi.Schema(type=openapi.TYPE_STRING, description='一句话介绍工作经历'),
                'brief': openapi.Schema(type=openapi.TYPE_STRING, description='教练简介'),
                'customer_evaluate': openapi.Schema(type=openapi.TYPE_STRING, description='案例及客户评价'),
                'style': openapi.Schema(type=openapi.TYPE_STRING, description='教练风格'),
                'qualification': openapi.Schema(type=openapi.TYPE_ARRAY, description='资质证书', items=openapi.Schema(
                    type=openapi.TYPE_OBJECT, properties={
                        "describe": openapi.Schema(type=openapi.TYPE_STRING, description='描述'),
                        "image_url": openapi.Schema(type=openapi.TYPE_STRING, description='图片链接'),
                    })),
                'industry': openapi.Schema(type=openapi.TYPE_STRING, description='教练过的行业'),
                'domain': openapi.Schema(type=openapi.TYPE_STRING, description='教练领域'),
                'coach_auth': openapi.Schema(
                    type=openapi.TYPE_STRING, description='教练认证 ｜ 1：ICF-ACC, 2：ICF-PCC, 3：ICF-MCC, 4：准ICF-PCC, 5：个人教练'),
            }
        ),
        tags=['教练简历相关']
    )
    @action(methods=['post'], detail=False, url_path='update', serializer_class=ResumeUpdateSerializer)
    def update_resume(self, request, *args, **kwargs):
        try:
            resume_id = request.data.get('id')
            resume = Resume.objects.get(pk=resume_id)
        except:
            return parameter_error_response()
        data = request.data
        serializer = self.get_serializer(data=data)
        serializer.is_valid(raise_exception=True)
        validated_data = serializer.validated_data
        return success_response(serializer.edit_resume_msg(validated_data))

    @swagger_auto_schema(
        operation_id='创建简历信息',
        operation_summary='创建简历信息',
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'type': openapi.Schema(type=openapi.TYPE_NUMBER, description='教练类型 1-签约教练 2-个人教练'),
                'user_id': openapi.Schema(type=openapi.TYPE_NUMBER, description='教练用户id'),
                'name': openapi.Schema(type=openapi.TYPE_STRING, description='用户名'),
                'email': openapi.Schema(type=openapi.TYPE_STRING, description='邮箱'),
                'phone': openapi.Schema(type=openapi.TYPE_STRING, description='手机号'),
                'gender': openapi.Schema(type=openapi.TYPE_NUMBER, description='性别'),
                'english_name': openapi.Schema(type=openapi.TYPE_STRING, description='英文名'),
                'head_image_url': openapi.Schema(type=openapi.TYPE_STRING, description='头像链接'),
                'birthday': openapi.Schema(type=openapi.TYPE_STRING, description='生日'),
                'user_class': openapi.Schema(
                    type=openapi.TYPE_STRING, description='班次'),
                'extra_time': openapi.Schema(
                    type=openapi.TYPE_NUMBER, description='平台外一对一辅导时常'),
                'working_years': openapi.Schema(
                    type=openapi.TYPE_NUMBER, description='工作年限 | 1：一年以上，2：三年以上，3：五年以上'),
                'language': openapi.Schema(
                    type=openapi.TYPE_NUMBER, description='语言｜ 1：中文，2：英语，3：法语，4：日语'),
                'work_experience': openapi.Schema(type=openapi.TYPE_STRING, description='工作经历'),
                'brief': openapi.Schema(type=openapi.TYPE_STRING, description='教练简介'),
                'customer_evaluate': openapi.Schema(type=openapi.TYPE_STRING, description='案例及客户评价'),
                'style': openapi.Schema(type=openapi.TYPE_STRING, description='教练风格'),
                'qualification': openapi.Schema(type=openapi.TYPE_ARRAY, description='资质证书', items=openapi.Schema(
                    type=openapi.TYPE_OBJECT, properties={
                        "describe": openapi.Schema(type=openapi.TYPE_STRING, description='描述'),
                        "image_url": openapi.Schema(type=openapi.TYPE_STRING, description='图片链接'),
                    })),
                'industry': openapi.Schema(type=openapi.TYPE_STRING, description='教练过的行业'),
                'domain': openapi.Schema(type=openapi.TYPE_STRING, description='教练领域'),
                'coach_auth': openapi.Schema(
                    type=openapi.TYPE_STRING, description='教练认证 ｜ 1：ICF-ACC, 2：ICF-PCC, 3：ICF-MCC, 4：准ICF-PCC, 5：个人教练'),
            }
        ),
        tags=['教练简历相关']
    )
    @action(methods=['post'], detail=False, url_path='add', serializer_class=ResumeCreateSerializer)
    def add_resume(self, request, *args, **kwargs):
        try:
            coach_id = request.data.get('coach_id')
            coach = Coach.objects.get(pk=coach_id, deleted=False)
        except:
            return parameter_error_response()
        data = request.data
        creator_id = request.user.pk
        if creator_id:
            data['creator_id'] = creator_id
        serializer = self.get_serializer(data=data)
        serializer.is_valid(raise_exception=True)
        validated_data = serializer.validated_data
        validated_data['project_id'] = request.data.get('project_id')
        return success_response(serializer.add_resume_msg(validated_data))


    @swagger_auto_schema(
        operation_id='教练详情',
        operation_summary='教练详情',
        manual_parameters=[
            openapi.Parameter('coach_id', openapi.IN_QUERY, description='教练id', type=openapi.TYPE_NUMBER),
            openapi.Parameter('type', openapi.IN_QUERY, description='教练类型 1-签约教练 2-个人教练', type=openapi.TYPE_NUMBER),
            openapi.Parameter('user_id', openapi.IN_QUERY, description='教练用户id', type=openapi.TYPE_NUMBER)
        ],
        tags=['教练简历相关']
    )
    @action(methods=['get'], detail=False, url_path='coach_detail')
    def coach_detail(self, request, *args, **kwargs):
        try:
            type = int(request.query_params.get('type', 0))
            user_id = request.query_params.get('user_id', 0)
            coach = Coach.objects.filter(user_id=user_id).first() if type == 1 else \
                TraineeCoach.objects.filter(user_id=user_id).first()
        except:
            return parameter_error_response()
        if type == 1:  # 签约教练
            serializer = CoachResumeBaseSerializers(coach)
        else:  # 个人教练
            serializer = TraineeCoachResumeBaseSerializers(coach)

        return success_response(serializer.data)

    @swagger_auto_schema(
        operation_id='修改教练信息',
        operation_summary='修改教练信息',
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'type': openapi.Schema(type=openapi.TYPE_NUMBER, description='教练类型 1-签约教练 2-个人教练'),
                'user_id': openapi.Schema(type=openapi.TYPE_NUMBER, description='教练用户id'),
                'name': openapi.Schema(type=openapi.TYPE_STRING, description='用户名'),
                'email': openapi.Schema(type=openapi.TYPE_STRING, description='邮箱'),
                'phone': openapi.Schema(type=openapi.TYPE_STRING, description='手机号'),
                'gender': openapi.Schema(type=openapi.TYPE_NUMBER, description='性别'),
                'english_name': openapi.Schema(type=openapi.TYPE_STRING, description='英文名'),
                'head_image_url': openapi.Schema(type=openapi.TYPE_STRING, description='头像链接'),
                'birthday': openapi.Schema(type=openapi.TYPE_STRING, description='生日'),
                'user_class': openapi.Schema(
                    type=openapi.TYPE_STRING, description='班次'),
                'extra_time': openapi.Schema(
                    type=openapi.TYPE_NUMBER, description='平台外一对一辅导时常'),
                'working_years': openapi.Schema(
                    type=openapi.TYPE_NUMBER, description='工作年限 | 1：一年以上，2：三年以上，3：五年以上'),
                'language': openapi.Schema(
                    type=openapi.TYPE_NUMBER, description='语言｜ 1：中文，2：英语，3：法语，4：日语'),
                'work_experience': openapi.Schema(type=openapi.TYPE_STRING, description='工作经历'),
                'brief': openapi.Schema(type=openapi.TYPE_STRING, description='教练简介'),
                'customer_evaluate': openapi.Schema(type=openapi.TYPE_STRING, description='案例及客户评价'),
                'style': openapi.Schema(type=openapi.TYPE_STRING, description='教练风格'),
                'qualification': openapi.Schema(type=openapi.TYPE_ARRAY, description='资质证书', items=openapi.Schema(
                    type=openapi.TYPE_OBJECT, properties={
                        "describe": openapi.Schema(type=openapi.TYPE_STRING, description='描述'),
                        "image_url": openapi.Schema(type=openapi.TYPE_STRING, description='图片链接'),
                    })),
                'industry': openapi.Schema(type=openapi.TYPE_STRING, description='教练过的行业'),
                'domain': openapi.Schema(type=openapi.TYPE_STRING, description='教练领域'),
                'coach_auth': openapi.Schema(
                    type=openapi.TYPE_STRING, description='教练认证 ｜ 1：ICF-ACC, 2：ICF-PCC, 3：ICF-MCC, 4：准ICF-PCC, 5：个人教练'),
            }
        ),
        tags=['教练简历相关']
    )
    @action(methods=['post'], detail=False, url_path='edit_resume', serializer_class=ResumeEditSerializer)
    def edit_resume(self, request, *args, **kwargs):
        try:
            type = request.data['type']
            user_id = request.data['user_id']
            user = User.objects.get(pk=user_id)
        except:
            return parameter_error_response()
        if type not in [1, 2]:
            return parameter_error_response()
        data = request.data
        if data.get('email'):
            if ' ' in data.get('email'):
                return parameter_error_response('邮箱中存在空格，请修改后再添加。')

            if data['email'] != user.email and User.objects.filter(email=data['email'], deleted=False).exists():
                return parameter_error_response('当前邮箱已存在')
        if data.get('phone'):
            if data['phone'] != user.phone and User.objects.filter(phone=data['phone'], deleted=False).exists():
                return parameter_error_response('当前手机号已存在')
        serializer = self.get_serializer(data=data)
        serializer.is_valid(raise_exception=True)
        validated_data = serializer.validated_data
        return success_response(serializer.update_resume(validated_data))

    @swagger_auto_schema(
        operation_id='管理后台修改教练信息',
        operation_summary='管理后台修改教练信息',
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'type': openapi.Schema(type=openapi.TYPE_NUMBER, description='教练类型 1-签约教练 2-个人教练'),
                'user_id': openapi.Schema(type=openapi.TYPE_NUMBER, description='教练用户id'),
                'name': openapi.Schema(type=openapi.TYPE_STRING, description='用户名'),
                'email': openapi.Schema(type=openapi.TYPE_STRING, description='邮箱'),
                'phone': openapi.Schema(type=openapi.TYPE_STRING, description='手机号'),
                'gender': openapi.Schema(type=openapi.TYPE_NUMBER, description='性别'),
                'english_name': openapi.Schema(type=openapi.TYPE_STRING, description='英文名'),
                'head_image_url': openapi.Schema(type=openapi.TYPE_STRING, description='头像链接'),
                'birthday': openapi.Schema(type=openapi.TYPE_STRING, description='生日'),
                'user_class': openapi.Schema(
                    type=openapi.TYPE_STRING, description='班次'),
                'extra_time': openapi.Schema(
                    type=openapi.TYPE_NUMBER, description='平台外一对一辅导时常'),
                'working_years': openapi.Schema(
                    type=openapi.TYPE_NUMBER, description='工作年限 | 1：一年以上，2：三年以上，3：五年以上'),
                'language': openapi.Schema(
                    type=openapi.TYPE_NUMBER, description='语言｜ 1：中文，2：英语，3：法语，4：日语'),
                'work_experience': openapi.Schema(type=openapi.TYPE_STRING, description='工作经历'),
                'brief': openapi.Schema(type=openapi.TYPE_STRING, description='教练简介'),
                'customer_evaluate': openapi.Schema(type=openapi.TYPE_STRING, description='案例及客户评价'),
                'style': openapi.Schema(type=openapi.TYPE_STRING, description='教练风格'),
                'qualification': openapi.Schema(type=openapi.TYPE_ARRAY, description='资质证书', items=openapi.Schema(
                    type=openapi.TYPE_OBJECT, properties={
                        "describe": openapi.Schema(type=openapi.TYPE_STRING, description='描述'),
                        "image_url": openapi.Schema(type=openapi.TYPE_STRING, description='图片链接'),
                    })),
                'industry': openapi.Schema(type=openapi.TYPE_STRING, description='教练过的行业'),
                'domain': openapi.Schema(type=openapi.TYPE_STRING, description='教练领域'),
                'coach_auth': openapi.Schema(
                    type=openapi.TYPE_STRING, description='教练认证 ｜ 1：ICF-ACC, 2：ICF-PCC, 3：ICF-MCC, 4：准ICF-PCC, 5：个人教练'),
            }
        ),
        tags=['教练简历相关']
    )
    @action(methods=['post'], detail=False, url_path='admin_edit_resume', serializer_class=ResumeEditSerializer)
    def admin_edit_resume(self, request, *args, **kwargs):
        try:
            type = request.data['type']
            user_id = request.data['user_id']
            user = User.objects.get(pk=user_id)
        except:
            return parameter_error_response()
        if type not in [1, 2]:
            return parameter_error_response()
        data = request.data
        if data.get('email'):
            if ' ' in data.get('email'):
                return parameter_error_response('邮箱中存在空格，请修改后再添加。')
            if data['email'] != user.email and User.objects.filter(email=data['email'], deleted=False).exists():
                return parameter_error_response('当前邮箱已存在')
        if data.get('phone'):
            if data['phone'] != user.phone and User.objects.filter(phone=data['phone'], deleted=False).exists():
                return parameter_error_response('当前手机号已存在')
        serializer = self.get_serializer(data=data)
        serializer.is_valid(raise_exception=True)
        validated_data = serializer.validated_data
        return success_response(serializer.update_admin_resume(validated_data))


