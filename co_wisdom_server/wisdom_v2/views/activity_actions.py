from rest_framework import serializers

from utils.wechat_oauth import WeChatMiniProgram
from wisdom_v2.common import activity_public
from wisdom_v2.common import activity_interview_public
from wisdom_v2.enum.service_content_enum import UserInviteTypeEnum, ActivityCoachStatusEnum
from wisdom_v2.models import UserInvi<PERSON><PERSON><PERSON><PERSON>, Coach, Resume, User
from wisdom_v2.models_file import Activity


class AdminActivityListSerializer(serializers.ModelSerializer):
    """
    活动列表序列化
    """
    start_date = serializers.DateField(help_text='开始日期', required=False, format='%Y-%m-%d')
    end_date = serializers.DateField(help_text='结束日期', required=False, format='%Y-%m-%d')
    created_at = serializers.DateTimeField(help_text='创建时间', required=False, format='%Y-%m-%d')
    status = serializers.SerializerMethodField(help_text='活动状态')
    activity_date = serializers.SerializerMethodField(help_text='活动时间')


    def get_status(self, obj):
        return activity_public.get_status_display(obj)

    def get_activity_date(self, obj):
        return f'{obj.start_date.strftime("%Y.%m.%d")}-{obj.end_date.strftime("%Y.%m.%d")}'

    class Meta:
        model = Activity
        fields = ('id', 'theme', 'status', 'type', 'start_date', 'end_date', 'activity_date',
                  'poster_image_url', 'limit_count', 'created_at', 'rule', 'notes')


class AdminActivityDetailSerializer(serializers.ModelSerializer):
    """
    活动详情序列化
    """

    start_date = serializers.DateField(help_text='开始日期', required=False, format='%Y-%m-%d')
    end_date = serializers.DateField(help_text='结束日期', required=False, format='%Y-%m-%d')
    interview_start_date = serializers.DateField(required=False, allow_null=True, format='%Y-%m-%d')
    interview_end_date = serializers.DateField(required=False, allow_null=True, format='%Y-%m-%d')
    activity_coach = serializers.ListField(write_only=True)
    confirm_coach_count = serializers.SerializerMethodField(help_text='接受邀请的教练数量')
    coach_content = serializers.SerializerMethodField(help_text='教练数据')
    # qrcode = serializers.SerializerMethodField(help_text='二维码')
    price = serializers.SerializerMethodField(help_text='活动金额')
    post_info = serializers.SerializerMethodField(help_text='活动邀请素材，包括用户的邀请链接和教练的邀请链接')

    def get_post_info(self, obj):
        activity_public.create_activity_invites(obj.id)
        # 获取活动的所有邀请记录
        invite_records = UserInviteRecord.objects.filter(
            deleted=False, type=UserInviteTypeEnum.activity.value, object_id=obj.id
        ).select_related('referrer')
        
        # 批量获取所有邀请人的用户信息
        referrer_ids = [invite.referrer_id for invite in invite_records]
        users = User.objects.filter(id__in=referrer_ids, deleted=False)
        user_map = {user.id: user for user in users}
        
        # 批量获取教练简历信息
        coach_resumes = Resume.objects.filter(
            coach__user_id__in=referrer_ids, 
            deleted=False, 
            is_customization=False
        ).select_related('coach')
        resume_map = {resume.coach.user_id: resume for resume in coach_resumes}
        
        # 批量获取教练信息
        coach_ids = [resume.coach_id for resume in coach_resumes]
        coaches = Coach.objects.filter(id__in=coach_ids, deleted=False)
        coach_map = {coach.user_id: coach for coach in coaches}
        
        post_data = []
        for invite in invite_records:
            invite_user = user_map.get(invite.referrer_id)
            if not invite_user:
                continue
                
            short_link = ''
            resume_info = {}
            invite_coach_resume = resume_map.get(invite.referrer_id)
            
            if invite_coach_resume:
                invite_coach = coach_map.get(invite.referrer_id)
                if not invite_coach:
                    continue
                    
                invite_link = f"https://www.qzcoach.com/landing/?p=/pages/user/myResume&invite_code={invite.uuid}&resume_id={invite_coach_resume.id}"
                resume_info = {
                    'head_image_url': invite_coach_resume.head_image_url,
                    'working_years': invite_coach_resume.working_years,
                    'job_profile': invite_coach_resume.job_profile,
                    'highest_position': str(invite_coach_resume.highest_position or ''),
                    'one_line_words': invite_coach.posters_text,
                }
            else:
                invite_link = f"https://www.qzcoach.com/landing/?p=/pages_coach/activity_landing/activity_coach_list&activity_id={obj.id}&invite_code={invite.uuid}"

            if invite_user.true_name in ['公众号', '官网']:
                url_state, short_link = WeChatMiniProgram().get_url_link(
                    'pages_coach/activity_landing/activity_coach_list',
                    f'activity_id={obj.id}&invite_code={invite.uuid}')
                if url_state:
                    short_link = short_link
            
            post_data.append({
                'invite_code': invite.uuid,
                'invite_link': invite_link,
                'user_name': invite_user.true_name,
                'short_link': short_link if short_link else '',
                'resume_info': resume_info if resume_info else '',
            })
        # Sort post_data to put entries with resume_info first
        post_data.sort(key=lambda x: bool(x.get('resume_info')), reverse=True)
        return post_data
    

    def get_price(self, obj):
        return obj.display_price

    def get_confirm_coach_count(self, obj):
        return obj.activity_coach.filter(
            deleted=False, status__in=[
                ActivityCoachStatusEnum.not_invitation.value, ActivityCoachStatusEnum.joined.value]).count()

    def get_coach_content(self, obj):
        return activity_public.get_coach_content(obj)

    # def get_qrcode(self, obj):
    #     return activity_public.get_activity_qrcode(obj)

    class Meta:
        model = Activity
        fields = ('id', 'theme', 'type', 'start_date', 'end_date', 'poster_image_url', 'limit_count', 'coach_content',
                  'activity_coach', 'brief', 'model_image_url', 'guide_image_url', 'rule', 'bg_color',
                  'head_image_url', 'notes', 'confirm_coach_count', 'interview_start_date', 'interview_end_date',
                  'price', 'accum_limit_count', 'post_info')


class AdminActivityDataDetailsSerializer(serializers.ModelSerializer):
    """
    活动数据详情序列化
    """
    pay_user_count = serializers.SerializerMethodField(help_text='支付用户数')
    interview_user_count = serializers.SerializerMethodField(help_text='辅导用户数')
    coach_content = serializers.SerializerMethodField(help_text='教练数据')

    def get_pay_user_count(self, obj):
        pay_user_count = activity_interview_public.get_pay_user_count(obj, coach_user=None, is_set=True)
        return pay_user_count if pay_user_count else 0

    def get_interview_user_count(self, obj):
        interview_user_count = activity_interview_public.get_interview_user_count(obj, coach_user=None, is_set=True)
        return interview_user_count if interview_user_count else 0

    def get_coach_content(self, obj):
        activity_coaches = obj.activity_coach.filter(deleted=False).all()

        # 获取所有教练的user_id
        coach_user_ids = [item.coach.user_id for item in activity_coaches]

        pay_user_count_map = activity_interview_public.get_user_counts(
            obj, coach_user_ids, True, include_interview=False
        )
        interview_user_count_map = activity_interview_public.get_user_counts(
            obj, coach_user_ids, True, include_interview=True
        )

        data = []
        for item in activity_coaches:
            user_id = item.coach.user_id
            data.append({
                'id': item.coach.id,
                'true_name': item.coach.user.cover_name,
                'pay_user_count': pay_user_count_map.get(user_id, 0),
                'interview_user_count': interview_user_count_map.get(user_id, 0),
            })
        return data

    class Meta:
        model = Activity
        fields = ('id', 'theme', 'pay_user_count', 'interview_user_count', 'coach_content')
