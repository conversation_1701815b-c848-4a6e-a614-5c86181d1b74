import datetime
from rest_framework import serializers
from wisdom_v2.views.constant import MANAGE_EVALUATION

from wisdom_v2.models import Evaluation, EvaluationModule, EvaluationAnswer


class EvaluationListSerializers(serializers.ModelSerializer):
    id = serializers.IntegerField(read_only=True, help_text='id')
    name = serializers.CharField(read_only=True, help_text='名称')
    brief = serializers.CharField(read_only=True, help_text='简要介绍')
    role = serializers.IntegerField(read_only=True, help_text='测评角色 填写角色 1-被教练者 2-被教练者，利益相关者')

    class Meta:
        model = Evaluation
        fields = ['id', 'name', 'brief', 'role']


class ProjectMemberEvaluationListSerializer(serializers.ModelSerializer):
    id = serializers.IntegerField(help_text='id', read_only=True)
    evaluation_name = serializers.CharField(source='evaluation.name', help_text='测评名称', read_only=True)
    member_name = serializers.Cha<PERSON><PERSON><PERSON>(source='project_bundle.project_member.user.name', help_text='被教练者姓名',
                                        read_only=True)
    start_time = serializers.DateField(help_text='开始日期', format='%Y-%m-%d', read_only=True)
    end_time = serializers.DateField(help_text='截止日期', format='%Y-%m-%d', read_only=True)
    submit_time = serializers.SerializerMethodField(help_text='提交日期', read_only=True)
    status = serializers.SerializerMethodField(help_text='测评状态 1-未完成 2-已过期 3-已完成', read_only=True)
    send_email = serializers.IntegerField(help_text='今日是否手动发送提醒邮件 1-是 0-否')

    class Meta:
        model = EvaluationModule
        fields = ['id', 'evaluation_name', 'member_name', 'start_time', 'end_time', 'submit_time', 'status',
                  'send_email']

    def get_submit_time(self, obj, stake_holder_id=None):
        answer = EvaluationAnswer.objects.filter(
            option__question__evaluation_id=obj.evaluation_id,
            public_attr__project=obj.project_bundle.project_id,
            public_attr__user_id=obj.project_bundle.project_member.user_id).first() if not stake_holder_id \
            else EvaluationAnswer.objects.filter(
            option__question__evaluation_id=obj.evaluation_id,
            public_attr__project=obj.project_bundle.project_id,
            public_attr__user_id=stake_holder_id,
            public_attr__target_user_id=obj.project_bundle.project_member.user_id).first()
        if answer:
            submit_time = answer.created_at
            return submit_time.strftime('%Y-%m-%d')
        return ''

    def get_status(self, obj, stake_holder_id=None):
        now = datetime.datetime.now().date()
        if obj.evaluation.code == MANAGE_EVALUATION:
            if not obj.is_submit and now <= obj.end_time:
                return 1
            if not obj.is_submit and now > obj.end_time:
                return 2
            if obj.is_submit:
                return 3
            else:
                return 0
        else:
            answer = EvaluationAnswer.objects.filter(
                option__question__evaluation_id=obj.evaluation_id,
                public_attr__project=obj.project_bundle.project_id,
                public_attr__target_user_id=obj.project_bundle.project_member.user_id,
                public_attr__user_id=obj.project_bundle.project_member.user_id).exists() if not stake_holder_id \
                else EvaluationAnswer.objects.filter(
                option__question__evaluation_id=obj.evaluation_id,
                public_attr__project=obj.project_bundle.project_id,
                public_attr__user_id=stake_holder_id,
                public_attr__target_user_id=obj.project_bundle.project_member.user_id).exists()
            if now <= obj.end_time and not answer:
                return 1
            elif answer:
                return 3
            elif now > obj.end_time and not answer:
                return 2
            else:
                return 0


def filter_status(status, data):
    results = []
    for i in data:
        if status == 1 or status == '1':
            if i['status'] == 1:
                results.append(i)
        if status == 2 or status == '2':
            if i['status'] == 2:
                results.append(i)
        if status == 3 or status == '3':
            if i['status'] == 3:
                results.append(i)
    return results


def get_status_submit_time(evaluation_module, stake_holder_id):
    submit_time, status = '', 0
    now = datetime.datetime.now().date()
    answer = EvaluationAnswer.objects.filter(
        option__question__evaluation_id=evaluation_module.evaluation_id,
        public_attr__project=evaluation_module.project_bundle.project_id,
        public_attr__user_id=stake_holder_id,
        public_attr__target_user_id=evaluation_module.project_bundle.project_member.user_id)
    if answer.exists():
        submit_time = answer.first().created_at
        status = 3
    else:
        if now <= evaluation_module.end_time:
            status = 1
        elif now > evaluation_module.end_time:
            status = 2
    return status, submit_time