from rest_framework import serializers

from wisdom_v2.common import project_settlement_public
from wisdom_v2.enum.business_order_enum import BusinessOrderDataTypeEnum
from wisdom_v2.models_file import BusinessOrder2Object
from wisdom_v2.models_file.project import ProjectSettlement


class AdminProjectSettlementListSerializer(serializers.ModelSerializer):
    """
    项目结算列表列表序列化
    """
    coach_name = serializers.CharField(source='coach.user.cover_name', help_text='教练名称')
    status = serializers.SerializerMethodField(help_text='状态')
    work_date = serializers.SerializerMethodField(help_text='时间')
    business_order_id = serializers.SerializerMethodField(help_text='结算订单id')
    time_type = serializers.SerializerMethodField(help_text='时间类型')
    place_type = serializers.SerializerMethodField(help_text='地点类型')

    def get_status(self, obj):
        return project_settlement_public.get_status_display(obj)

    def get_work_date(self, obj):
        return f'{obj.work_start_time.strftime("%Y-%m-%d %H:%M")} ~ {obj.work_end_time.strftime("%Y-%m-%d %H:%M")}'

    def get_business_order_id(self, obj):
        business_order2_object = BusinessOrder2Object.objects.filter(
            data_type=BusinessOrderDataTypeEnum.project_settlement.value,
            object_id=str(obj.pk), deleted=False, business_order__deleted=False).first()
        if business_order2_object:
            return str(business_order2_object.business_order.pk)
        return None

    def get_time_type(self, obj):
        return obj.time_type

    def get_place_type(self, obj):
        return obj.place_type

    class Meta:
        model = ProjectSettlement
        fields = ('id', 'status', 'work_type', 'work_date', 'coachee_name', 'coach_name', 'business_order_id', 'time_type', 'place_type')
