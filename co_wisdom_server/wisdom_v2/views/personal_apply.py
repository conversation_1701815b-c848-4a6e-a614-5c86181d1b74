import re
import datetime

from django.conf import settings
from drf_yasg import openapi
from rest_framework.decorators import action
from drf_yasg.utils import swagger_auto_schema
from rest_framework import viewsets
from django.db import transaction
from django.db.models import Q

from utils.message.email import message_send_email_base
from utils.messagecenter import getui
from wisdom_v2 import utils
from wisdom_v2.enum.service_content_enum import CoachAuth<PERSON>num
from wisdom_v2.enum.user_enum import CoachUserTypeEnum, PersonalApplyStatusEnum, PersonalApplyTypeEnum, \
    CoachInternToPersonalStatus
from wisdom_v2.views.personal_apply_action import PersonalApplySerializer
from wisdom_v2.models import PersonalApply, Coach, User, WorkWechatUser
from utils.pagination import StandardResultsSetPagination
from utils.api_response import success_response, parameter_error_response, WisdomValidationError
from utils.validate import validate_email, validate_phone
from utils import task


class PersonalApplyViewSet(viewsets.ModelViewSet):
    queryset = PersonalApply.objects.filter(deleted=False).order_by('-created_at')
    serializer_class = PersonalApplySerializer

    @swagger_auto_schema(
        operation_id='后台学员申请列表',
        operation_summary='后台学员申请列表',
        manual_parameters=[
            openapi.Parameter('true_name', openapi.IN_QUERY, description='名称', type=openapi.TYPE_STRING),
            openapi.Parameter('status', openapi.IN_QUERY, description='状态 1-未处理 2-已通过 3-已拒绝', type=openapi.TYPE_STRING),
            openapi.Parameter('phone', openapi.IN_QUERY, description='手机号', type=openapi.TYPE_STRING),
            openapi.Parameter('email', openapi.IN_QUERY, description='邮箱', type=openapi.TYPE_STRING),
            openapi.Parameter('type', openapi.IN_QUERY, description='申请类型1-个人教练 2-见习教练', type=openapi.TYPE_NUMBER),
            openapi.Parameter('resume_status', openapi.IN_QUERY, description='简历审核状态 1-未审核 2-已审核', type=openapi.TYPE_STRING),
            openapi.Parameter('start_date', openapi.IN_QUERY, description='开始日期', type=openapi.TYPE_STRING),
            openapi.Parameter('end_date', openapi.IN_QUERY, description='结束日期', type=openapi.TYPE_STRING),
            openapi.Parameter('page', openapi.IN_QUERY, description='第几页', type=openapi.TYPE_NUMBER),
            openapi.Parameter('page_size', openapi.IN_QUERY, description='每页显示条数默认15条', type=openapi.TYPE_NUMBER),
        ],
        tags=['学员申请相关']
    )
    def list(self, request, *args, **kwargs):
        personal_apply = self.get_queryset()
        if request.query_params.get('true_name', None):
            personal_apply = personal_apply.filter(true_name__icontains=request.query_params.get('true_name'))
        if request.query_params.get('status'):
            personal_apply = personal_apply.filter(status__in=request.query_params.get('status').split(','))
        if request.query_params.get('phone', None):
            personal_apply = personal_apply.filter(phone__icontains=request.query_params.get('phone'))
        if request.query_params.get('email', None):
            personal_apply = personal_apply.filter(email__icontains=request.query_params.get('email'))
        if request.query_params.get('type'):
            personal_apply = personal_apply.filter(type=request.query_params.get('type'))
        if request.query_params.get('start_date') and request.query_params.get('end_date'):
            start_date = datetime.datetime.strptime(request.query_params.get('start_date'), '%Y-%m-%d')
            end_date = datetime.datetime.strptime(request.query_params.get('end_date'), '%Y-%m-%d')
            personal_apply = personal_apply.filter(created_at__date__range=[start_date.date(), end_date.date()])
        if request.query_params.get('resume_status'):
            resume_status = request.query_params.get('resume_status').split(',')
            if 1 <= len(resume_status) < 2:
                if resume_status[0] in [1, '1']:
                    personal_apply = personal_apply.filter(Q(coach__platform_order_receiving_status=False) |
                                                           Q(coach__isnull=True))
                else:
                    personal_apply = personal_apply.filter(coach__platform_order_receiving_status=True)
        if request.query_params.get('is_sign'):
            is_sign = False if request.query_params.get('is_sign').lower() == 'true' else True
            personal_apply = personal_apply.filter(coach__user__user_contract__isnull=is_sign)
        # 分页
        paginator = StandardResultsSetPagination()
        page_list = paginator.paginate_queryset(personal_apply, self.request)
        serializer = self.get_serializer(page_list, many=True)
        response = paginator.get_paginated_response(serializer.data)
        return success_response(response)

    @swagger_auto_schema(
        operation_id='后台学员申请处理详情',
        operation_summary='后台学员申请处理详情',
        manual_parameters=[
            openapi.Parameter('id', openapi.IN_QUERY, description='申请id', type=openapi.TYPE_NUMBER),
        ],
        tags=['学员申请相关']
    )
    @action(methods=['get'], detail=False, url_path='process_detail')
    def process_detail(self, request, *args, **kwargs):
        try:
            personal_apply_id = request.query_params.get('id')
            personal_apply = PersonalApply.objects.get(pk=personal_apply_id)
        except:
            return parameter_error_response()
        personal = {'source': '学员填写',
                    'true_name': personal_apply.true_name,
                    'phone': personal_apply.phone,
                    'email': personal_apply.email,
                    'user_class': personal_apply.user_class,
                    'coach_auth': personal_apply.coach_auth,
                    'qualification': personal_apply.qualification}
        platform = {}
        if personal_apply.coach:
            coach = personal_apply.coach
            personal['coach_type'] = personal_apply.coach.coach_type
            search_text = "见习学员申请<span style='color:red;'>账号已绑定</span>"
        else:
            search_coach = Coach.objects.filter(deleted=False)
            if search_coach.filter(user__phone=personal_apply.phone).exists():
                coach = search_coach.filter(user__phone=personal_apply.phone).first()
                search_text = "使用<span style='color:red;'>手机号</span>查找到学员"
            elif search_coach.filter(user__email=personal_apply.email).exists():
                coach = search_coach.filter(user__email=personal_apply.email).first()
                search_text = "使用<span style='color:red;'>邮箱</span>查找到学员"
            elif search_coach.filter(user__true_name=personal_apply.true_name).exists():
                coach = search_coach.filter(user__true_name=personal_apply.true_name).first()
                search_text = "使用<span style='color:red;'>姓名</span>查找到学员"
            else:
                coach = None
                search_text = "教练池中不存在<span style='color:red;'>手机号、邮箱、姓名</span>与该申请相同的数据，需要通过申请，请先在教练管理页面新增教练账号后再操作"
        if coach:
            platform['source'], platform['true_name'], platform['phone'], platform['email'], \
            platform['user_class'], platform['coach_type'] = '平台查找', coach.user.cover_name, coach.user.phone, \
                                                             coach.user.email, coach.user_class, coach.coach_type
        coach_list = [personal]
        if platform:
            coach_list.append(platform)
        results = {'results': coach_list, 'search_text': search_text, 'type': personal_apply.type}
        return success_response(results)

    @swagger_auto_schema(
        operation_id='提交申请',
        operation_summary='提交申请',
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'true_name': openapi.Schema(type=openapi.TYPE_NUMBER, description='姓名'),
                'user_class': openapi.Schema(type=openapi.TYPE_NUMBER, description='班次'),
                'email': openapi.Schema(type=openapi.TYPE_STRING, description='邮箱'),
                'phone': openapi.Schema(type=openapi.TYPE_NUMBER, description='手机号'),
                'coach_auth': openapi.Schema(type=openapi.TYPE_NUMBER, description='教练资质 1-ICF认证ACC教练 2-ICF认证PCC教练 3-ICF认证MCC教练 4-通过PCC口试 5-通过ACC口试'),
                'qualification': openapi.Schema(type=openapi.TYPE_STRING, description='教练证书'),
            }
        ),
        tags=['学员申请相关']
    )
    @action(methods=['post'], detail=False, url_path='add', authentication_classes=[])
    def add_personal_apply(self, request, *args, **kwargs):
        try:
            true_name = request.data.get('true_name')
            user_class = request.data.get('user_class')
            email = request.data.get('email')
            phone = request.data.get('phone')
            coach_auth = request.data.get('coach_auth')
            qualification = request.data.get('qualification')
        except:
            return parameter_error_response()
        if not validate_email(email):
            return parameter_error_response('邮箱错误')
        if not validate_phone(phone):
            return parameter_error_response('手机号格式错误')

        if coach_auth:
            if coach_auth not in CoachAuthEnum.get_describe_keys():
                return parameter_error_response('教练资质错误')
            if not qualification:
                return parameter_error_response('未上传教练证书')
            if not isinstance(qualification, list):
                return parameter_error_response('教练证书数据类型错误')

        personal_apply = PersonalApply.objects.create(
            true_name=true_name, user_class=user_class, phone=phone, type=PersonalApplyTypeEnum.user_to_coach.value,
            email=email, coach_auth=coach_auth, qualification=qualification)
        serializer = self.get_serializer(personal_apply)
        task.push_lark_message_celery.delay([personal_apply.pk], 'add_personal_apply')
        return success_response(serializer.data)

    @swagger_auto_schema(
        operation_id='学员处理申请处理（通过or拒绝）',
        operation_summary='学员处理申请处理（通过or拒绝）',
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'id': openapi.Schema(type=openapi.TYPE_NUMBER, description='id'),
                'status': openapi.Schema(type=openapi.TYPE_NUMBER, description='状态 1-通过见习教练入驻，2-通过个人教练转见习教练 3-拒绝个人教练转见习教练'),
                'rejection_reason': openapi.Schema(type=openapi.TYPE_NUMBER, description='拒绝原因'),
                'coach_type': openapi.Schema(type=openapi.TYPE_STRING, description='教练类型'),
            }
        ),
        tags=['学员申请相关']
    )
    @action(methods=['post'], detail=False, url_path='update')
    def update_personal_apply(self, request, *args, **kwargs):
        try:
            personal_apply_id = request.data.get('id')
            personal_apply = PersonalApply.objects.get(pk=personal_apply_id)
            status = int(request.data.get('status'))
            rejection_reason = request.data.get('rejection_reason')
            coach_type = int(request.data.get('coach_type', 0))
        except:
            return parameter_error_response()

        if status == 1:  # 1-通过见习教练入驻
            status_enum = PersonalApplyStatusEnum.passed.value
        elif status == 2:  # 通过个人教练转见习教练
            status_enum = PersonalApplyStatusEnum.passed.value
        elif status == 3:  # 拒绝个人教练转见习教练
            status_enum = PersonalApplyStatusEnum.rejected.value
        else:
            return parameter_error_response('错误的状态类型')

        if personal_apply.coach:  # 见习教练入驻申请或者见习教练转个人教练申请时，coach已经绑定上。
            with transaction.atomic():
                coach = personal_apply.coach
                personal_apply.status = status_enum
                personal_apply.auditor = request.user
                personal_apply.rejection_reason = rejection_reason
                personal_apply.save()

                work_wechat_user = WorkWechatUser.objects.filter(
                    wx_user_id__isnull=False, user_id=personal_apply.coach.user_id, deleted=False).first()
                if personal_apply.type == PersonalApplyTypeEnum.personal.value:
                    if status_enum == PersonalApplyStatusEnum.passed.value:
                        coach.coach_type = coach_type
                        # 通过见习教练转个人教练申请，状态改为已完成
                        coach.intern_to_personal_status = CoachInternToPersonalStatus.complete.value
                        coach.save()
                        # 通过申请需要保存当前通过的教练类型
                        personal_apply.coach_type = coach.coach_type
                        personal_apply.save()

                        if work_wechat_user:
                            getui.send_work_wechat_coach_notice.delay(
                                work_wechat_user.wx_user_id,
                                'internship_to_personal_agree_notice',
                                coach_name=work_wechat_user.user.cover_name,
                                coach_id=work_wechat_user.user_id
                            )
                    elif status_enum == PersonalApplyStatusEnum.rejected.value:
                        # 被拒绝后，小程序再次申请会进入简历编辑页面，编辑后需要进入价格修改节目，所以应将intern_to_personal_status改为编辑简历
                        coach.intern_to_personal_status = CoachInternToPersonalStatus.resume.value
                        coach.save()

                        if work_wechat_user:
                            getui.send_work_wechat_coach_notice.delay(
                                work_wechat_user.wx_user_id,
                                'internship_to_personal_refuse_notice',
                                rejection_reason=rejection_reason,
                                coach_name=work_wechat_user.user.cover_name,
                                coach_id=work_wechat_user.user_id
                            )
                    else:
                        raise WisdomValidationError('不支持此类操作')
                elif personal_apply.type == PersonalApplyTypeEnum.internship.value:
                    if status_enum == PersonalApplyStatusEnum.passed.value:
                        # 通过入驻申请，将简历改为可分享状态
                        coach.is_share_resume = True
                        coach.save()
                        # 通过申请需要保存当前通过的教练类型
                        personal_apply.coach_type = coach.coach_type
                        personal_apply.save()

                        if work_wechat_user:
                            getui.send_work_wechat_coach_notice.delay(
                                work_wechat_user.wx_user_id,
                                'internship_entrant_notice',
                                coach_name=work_wechat_user.user.cover_name,
                                coach_id=work_wechat_user.user_id
                            )
                    else:
                        raise WisdomValidationError('不支持此类操作')
                else:
                    raise WisdomValidationError('申请类型错误')
        else:
            if status_enum == PersonalApplyStatusEnum.passed.value:
                # C端用户申请可以转成任意类型教练
                if not coach_type or coach_type not in CoachUserTypeEnum.get_describe_keys():
                    return parameter_error_response()
                coach = Coach.objects.filter(coach_type=CoachUserTypeEnum.student, deleted=False,
                                             user__phone=personal_apply.phone).first()
                if not coach:
                    coach = Coach.objects.filter(coach_type=CoachUserTypeEnum.student, deleted=False,
                                                 user__email=personal_apply.email).first()
                    if not coach:
                        coach = Coach.objects.filter(coach_type=CoachUserTypeEnum.student, deleted=False,
                                                     user__true_name=personal_apply.true_name).first()
                        if not coach:
                            return parameter_error_response('未查询到相关学员信息')
                        else:  # 姓名查到的
                            user = coach.user
                            exist_email_user = User.objects.filter(email=personal_apply.email, deleted=False)
                            exist_phone_user = User.objects.filter(phone=personal_apply.phone, deleted=False)
                            if exist_email_user.exists() and exist_email_user.first().pk != user.pk:
                                return parameter_error_response('当前申请手机号邮箱查出多个用户')
                            if exist_phone_user.exists() and exist_phone_user.first().pk != user.pk:
                                return parameter_error_response('当前申请手机号邮箱查出多个用户')

                    else:  # 邮箱查到的
                        user = coach.user
                        exist_user = User.objects.filter(phone=personal_apply.phone, deleted=False)
                        if exist_user.exists() and exist_user.first().pk != user.pk:  # 用户输入手机号和邮箱查出了两个不同的用户，数据错误
                            return parameter_error_response('当前申请手机号邮箱查出多个用户')

                else:  # 手机号查到的
                    user = coach.user
                    exist_user = User.objects.filter(email=personal_apply.email, deleted=False)
                    if exist_user.exists() and exist_user.first().pk != user.pk:  # 用户输入手机号和邮箱查出了两个不同的用户，数据错误
                        return parameter_error_response('当前申请手机号邮箱查出多个用户')

                if WorkWechatUser.objects.filter(user__isnull=False, wx_user_id=user.phone, deleted=False).exclude(user=user).exists():
                    return parameter_error_response('手机号对应的企业微信账号已存在')

                # 根据类型确定邮件和企业微信的内容
                if coach_type == CoachUserTypeEnum.student.value:
                    coach_type_describe = '见习教练'
                    content_type = "add_personal_apply_student_user"
                elif coach_type in CoachUserTypeEnum.personal_coach_value():
                    coach_type_describe = '个人教练'
                    content_type = "add_personal_apply_user"
                elif coach_type in CoachUserTypeEnum.enterprise_coach_value():
                    coach_type_describe = '企业教练'
                    content_type = "add_user"
                else:
                    coach_type_describe = ''
                    content_type = "add_user"

                with transaction.atomic():
                    user.email = personal_apply.email
                    user.phone = personal_apply.phone
                    user.true_name = personal_apply.true_name
                    user.save()
                    # 更新教练类型
                    coach.coach_type = coach_type
                    coach.save()

                    # 存在教练资质信息则更新简历数据
                    if personal_apply.coach_auth:
                        resume = coach.resumes.filter(deleted=False, is_customization=False).first()
                        resume.coach_auth = personal_apply.coach_auth
                        resume.qualification = personal_apply.qualification
                        resume.save()

                    personal_apply.coach_id = coach.pk
                    personal_apply.status = status_enum
                    personal_apply.auditor = request.user
                    personal_apply.coach_type = coach_type  # 通过申请需要保存当前通过的教练类型
                    personal_apply.save()

                    # 教练是否绑定
                    if WorkWechatUser.objects.filter(wx_user_id__isnull=False, user=user, deleted=False).exists():
                        work_wechat_user = WorkWechatUser.objects.filter(wx_user_id__isnull=False, user=user, deleted=False).first()
                        getui.send_work_wechat_coach_notice.apply_async(kwargs=dict(
                            user=work_wechat_user.wx_user_id, content_type=content_type, coach_id=user.pk, coach_name=user.cover_name),
                            countdown=180, expires=360)
                    else:
                        state = utils.add_work_wechat_user(user, settings.WORK_WECHAT_COACH_DEPARTMENT_ID, content_type=content_type)
                        if state:
                            raise WisdomValidationError('添加企业微信用户失败')
                # 发送通过邮件
                params = {"true_name": personal_apply.true_name, "coach_type_describe": coach_type_describe}
                message_send_email_base.delay('personal_apply_passed', params, [personal_apply.email], receiver_ids=user.id, sender_id=request.user.id)

            elif status_enum == PersonalApplyStatusEnum.rejected.value:
                if not rejection_reason or len(rejection_reason) > 800:
                    return parameter_error_response('拒绝原因未填写或字数超过800字')
                personal_apply.rejection_reason = rejection_reason
                personal_apply.status = status_enum
                personal_apply.auditor = request.user
                personal_apply.save()
                # 发送拒绝邮件
                rejection_reason = rejection_reason.replace('\n', '<br/>')
                params = {"true_name": personal_apply.true_name, "rejection_reason": rejection_reason}
                message_send_email_base.delay('personal_apply_rejected', params, [personal_apply.email], receiver_ids=user.id, sender_id=request.user.id)
            else:
                return parameter_error_response()

        return success_response()

    @swagger_auto_schema(
        operation_id='删除学员申请记录',
        operation_summary='删除学员申请记录',
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'id': openapi.Schema(type=openapi.TYPE_NUMBER, description='学员申请记录id'),
            }
        ),
        tags=['学员申请相关']
    )
    @action(methods=['post'], detail=False, url_path='deleted')
    def deleted_personal_apply(self, request, *args, **kwargs):
        try:
            personal_apply_id = request.data.get('id')
            personal_apply = self.queryset.get(pk=personal_apply_id)
            personal_apply.deleted = True
            personal_apply.save()
            return success_response()
        except PersonalApply.DoesNotExist:
            return parameter_error_response()
        except Exception as e:
            return parameter_error_response(str(e))
