from rest_framework import serializers
from ..models import InterviewRecordTemplateAnswer


class InterviewRecordTemplateAnswerSerializers(serializers.ModelSerializer):

    class Meta:
        model = InterviewRecordTemplateAnswer
        exclude = ('created_at', 'updated_at')

    def create(self, validated_data):
        instance = InterviewRecordTemplateAnswer(**validated_data)
        instance.save()
        return instance
