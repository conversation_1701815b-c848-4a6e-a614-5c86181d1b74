from rest_framework import serializers
from django.db import transaction

from utils.api_response import WisdomValidationError
from wisdom_v2.common import stakeholder_interview_public, project_member_public
from wisdom_v2.common.stakeholder_interview_public import check_elements_exist
from wisdom_v2.enum.user_enum import UserTmpEnum
from wisdom_v2.models_file import StakeholderInterview, StakeholderInterviewModule
from wisdom_v2.models import ProjectMember, Coach, ProjectBundle, \
    ProjectInterested, CompanyMember, InterviewRecordTemplate, TotalTemplate, InterviewRecordTemplateAnswer, UserTmp


class StakeholderListSerializer(serializers.ModelSerializer):
    id = serializers.IntegerField(help_text='利益相关id')
    true_name = serializers.CharField(source='interested.cover_name', help_text='利益相关者姓名')
    relation = serializers.IntegerField(help_text='上下级关系 1:上级 2：平级 3:下级')
    position = serializers.SerializerMethodField(help_text='利益相关者职位')
    email = serializers.CharField(source='interested.email', help_text='利益相关者邮箱')
    phone = serializers.CharField(source='interested.phone', help_text='利益相关者手机号')
    is_set = serializers.SerializerMethodField(help_text='是否已配置利益相关者访谈')
    stakeholder_interview_id = serializers.SerializerMethodField(help_text='利益相关者访谈id')
    is_cancel = serializers.SerializerMethodField(help_text='是否可以取消勾选')
    is_complete = serializers.SerializerMethodField(help_text='是否填写')

    class Meta:
        model = ProjectInterested
        fields = ('id', 'true_name', 'relation', 'position', 'email', 'phone', 'is_set', 'stakeholder_interview_id',
                  'is_cancel', 'is_complete')

    def get_position(self, obj):
        company_member = CompanyMember.objects.filter(company=obj.project.company, user=obj.interested,
                                                      deleted=False).first()
        if company_member:
            return company_member.position

    def get_is_set(self, obj):
        project_member = self.context.get('project_member')
        if project_member:
            return stakeholder_interview_public.get_stakeholder_interview_is_set(project_member, obj)
        return False

    def get_stakeholder_interview_id(self, obj):
        project_member = self.context.get('project_member')
        if project_member:
            stakeholder_interview = StakeholderInterview.objects.filter(
                project_interested=obj, deleted=False,
                stakeholder_interview_module__project_member=project_member).first()
            if stakeholder_interview:
                return stakeholder_interview.id

    def get_is_cancel(self, obj):
        project_member = self.context.get('project_member')
        if project_member:
            return stakeholder_interview_public.get_stakeholder_interview_is_cancel(project_member, obj)
        return True

    def get_is_complete(self, obj):
        project_member = self.context.get('project_member')
        if project_member:
            return stakeholder_interview_public.get_stakeholder_interview_is_complete(project_member, obj)
        return



class StakeholderInterviewModuleSerializer(serializers.ModelSerializer):
    id = serializers.CharField(help_text='利益相关者访谈配置id')
    project_member_id = serializers.IntegerField(help_text='被教练者id')
    stakeholder_interview_number = serializers.IntegerField(help_text='访谈人数')
    duration = serializers.FloatField(help_text='访谈时长 默认0.5，支持设置为0.5、1、1.5、2，4个值')
    start_date = serializers.DateField(format='%Y-%m-%d')
    end_date = serializers.DateField(format='%Y-%m-%d')
    coach_template_id = serializers.IntegerField(help_text='访谈记录模版id')
    report_template_id = serializers.IntegerField(help_text='访谈报告模版id')
    stakeholder = serializers.SerializerMethodField(help_text='利益相关者列表')
    coach_template_name = serializers.SerializerMethodField(help_text='访谈记录模版名称')
    report_template_name = serializers.SerializerMethodField(help_text='访谈报告模版名称')
    rate = serializers.SerializerMethodField(help_text='反馈进度')
    coach_id = serializers.SerializerMethodField(help_text='教练id')
    coach_name = serializers.SerializerMethodField(help_text='教练姓名')

    class Meta:
        model = StakeholderInterviewModule
        fields = ('id', 'project_member_id', 'stakeholder_interview_number', 'duration', 'start_date', 'end_date',
                  'coach_template_id', 'report_template_id', 'stakeholder', 'report_template_name', 'coach_template_name',
                  'rate', 'coach_id', 'coach_name')

    def get_coach_id(self, obj):
        if obj.coach_task.public_attr.user_id:
            coach_user = Coach.objects.filter(user_id=obj.coach_task.public_attr.user_id, deleted=False).first()
            return coach_user.id
        return

    def get_coach_name(self, obj):
        if obj.coach_task.public_attr.user_id:
            return obj.coach_task.public_attr.user.cover_name
        return None

    def get_rate(self, obj):
        complete_count = stakeholder_interview_public.get_stakeholder_interview_complete_count(obj.project_member)
        interview_count = obj.stakeholder_interview.filter(deleted=False, interview__isnull=False).count()
        return f'{complete_count}/{interview_count}'

    def get_coach_template_name(self, obj):
        if obj.coach_template_id:
            return obj.coach_template.name

    def get_report_template_name(self, obj):
        if obj.report_template_id:
            return obj.report_template.title


    def get_stakeholder(self, obj):
        data = []
        project_member = obj.project_member
        if project_member:
            stakeholders = ProjectInterested.objects.filter(
                master_id=project_member.user_id, project_id=project_member.project_id,
                deleted=False).order_by('-created_at')
            if stakeholders.exists():
                data = StakeholderListSerializer(stakeholders, many=True,
                                                 context={'project_member': project_member}).data
        return data


class StakeholderInterviewModuleCreateSerializer(serializers.Serializer):
    project_member_id = serializers.IntegerField(help_text='被教练者id', write_only=True, required=True)
    duration = serializers.FloatField(help_text='访谈时长 默认0.5，支持设置为0.5、1、1.5、2，4个值', write_only=True,
                                      required=True)
    start_date = serializers.DateField(help_text='开始日期', write_only=True, required=True)
    end_date = serializers.DateField(help_text='结束日期', write_only=True, required=True)
    stakeholder_interview_number = serializers.IntegerField(help_text='访谈人数', write_only=True, required=True)
    coach_template_id = serializers.IntegerField(help_text='访谈记录模版id', write_only=True, required=True)
    report_template_id = serializers.IntegerField(help_text='访谈报告模版id', write_only=True, required=True)
    stakeholder = serializers.ListField(help_text='利益相关关系id列表', write_only=True, required=False)

    def create(self, validated_data):
        project_member = ProjectMember.objects.filter(id=validated_data['project_member_id'], deleted=False).first()
        if not project_member:
            raise WisdomValidationError('未找到被教练者')
        if project_member.stakeholder_interview_module.filter(deleted=False).exists():
            raise WisdomValidationError('当前被教练者已配置利益相关者访谈，请勿重复配置')
        if not project_member.coach_id:
            raise WisdomValidationError('当前被教练者未匹配教练，请先匹配教练')
        if validated_data['stakeholder_interview_number'] not in list(range(1, 11)):
            raise WisdomValidationError('访谈人数只支持1-10人')
        if validated_data['duration'] not in [0.5, 1, 1.5, 2]:
            raise WisdomValidationError('利益相关者访谈时长仅支持 0.5, 1, 1.5, 2小时')
        if not InterviewRecordTemplate.objects.filter(id=validated_data['coach_template_id'], deleted=False).exists():
            raise WisdomValidationError('未找到访谈记录模版')
        if not TotalTemplate.objects.filter(id=validated_data['report_template_id'], deleted=False).exists():
            raise WisdomValidationError('未找到访谈报告模版')
        stakeholder = []
        if 'stakeholder' in validated_data.keys():
            stakeholder = validated_data.pop('stakeholder')
            if stakeholder:
                stakeholder_ids = list(ProjectInterested.objects.filter(
                    master_id=project_member.user_id, project_id=project_member.project_id,
                    deleted=False).values_list('id', flat=True))
                if not check_elements_exist(stakeholder, stakeholder_ids):  # 检查传入的利益相关者关系id是否在被教练者的所有利益相关者关系id中
                    raise WisdomValidationError('参与访谈的人员错误')
        coach = Coach.objects.filter(id=project_member.coach_id, deleted=False).first()
        if not coach:
            raise WisdomValidationError('当前匹配教练信息错误')

        with transaction.atomic():
            project_bundle = project_member.project_bundle.filter(deleted=False).first()
            if not project_bundle:
                project_bundle = ProjectBundle.objects.create(project=project_member.project,
                                                              project_member=project_member)
            # 创建教练任务
            coach_task = project_member_public.add_coach_task(
                coach.user_id, validated_data['report_template_id'], 0, project_member, project_bundle)
            validated_data['coach_task_id'] = coach_task.id
            # 创建利益相关者访谈配置
            instance = StakeholderInterviewModule.objects.create(**validated_data)
            if stakeholder:
                stakeholder_interview_list = []
                for project_interested_id in stakeholder:
                    stakeholder_interview_list.append(
                        StakeholderInterview(stakeholder_interview_module=instance,
                                             project_interested_id=project_interested_id))
                # 创建利益相关者访谈
                StakeholderInterview.objects.bulk_create(stakeholder_interview_list)
        return StakeholderInterviewModuleSerializer(instance).data


class StakeholderInterviewModuleUpdateSerializer(serializers.ModelSerializer):
    id = serializers.CharField(help_text='利益相关者访谈配置id', write_only=True, required=True)
    duration = serializers.FloatField(help_text='访谈时长 默认0.5，支持设置为0.5、1、1.5、2，4个值', write_only=True,
                                      required=False)
    start_date = serializers.DateField(help_text='开始日期', write_only=True, required=False)
    end_date = serializers.DateField(help_text='结束日期', write_only=True, required=False)
    stakeholder_interview_number = serializers.IntegerField(help_text='访谈人数', write_only=True, required=False)
    coach_template_id = serializers.IntegerField(help_text='访谈记录模版id', write_only=True, required=False)
    report_template_id = serializers.IntegerField(help_text='访谈报告模版id', write_only=True, required=False)
    stakeholder = serializers.ListField(help_text='利益相关关系id列表', write_only=True, required=False)
    deleted = serializers.BooleanField(help_text='是否删除', write_only=True, required=False)

    class Meta:
        model = StakeholderInterviewModule
        fields = ('id', 'duration', 'start_date', 'end_date', 'stakeholder_interview_number', 'coach_template_id',
                  'report_template_id', 'stakeholder', 'deleted')

    def update(self, instance, validated_data):
        deleted = validated_data.get('deleted')
        validated_data.pop('id')
        if deleted:
            if instance.stakeholder_interview.filter(interview__isnull=False).exists():
                raise WisdomValidationError('当前参与人员已预约辅导，无法移除利益相关者访谈配置')
            if InterviewRecordTemplateAnswer.objects.filter(public_attr=instance.coach_task.public_attr,
                                                            deleted=False).exists():
                raise WisdomValidationError('当前教练已填写利益相关者访谈报告，无法移除利益相关者访谈配置')
        if 'duration' in validated_data.keys():
            if validated_data['duration'] not in [0.5, 1, 1.5, 2]:
                raise WisdomValidationError('利益相关者访谈时长仅支持 0.5, 1, 1.5, 2小时')
        if 'stakeholder_interview_number' in validated_data.keys():
            if validated_data['stakeholder_interview_number'] not in list(range(1, 11)):
                raise WisdomValidationError('访谈人数只支持1-10人')
        if 'coach_template_id' in validated_data.keys():
            if not InterviewRecordTemplate.objects.filter(id=validated_data['coach_template_id'],
                                                          deleted=False).exists():
                raise WisdomValidationError('未找到访谈记录模版')
            if validated_data['coach_template_id'] != instance.coach_template_id:
                # 修改了访谈记录模版，检查是否存在教练已填写的利益相关者访谈
                stakeholder_interviews = instance.stakeholder_interview.filter(interview__isnull=False, deleted=False)
                if stakeholder_interviews.exists():
                    for stakeholder_interview in stakeholder_interviews:
                        if InterviewRecordTemplateAnswer.objects.filter(
                                interview=stakeholder_interview.interview, deleted=False).exists():
                            raise WisdomValidationError('当前访谈记录模版教练已填写，禁止修改')

        if 'report_template_id' in validated_data.keys():
            if not TotalTemplate.objects.filter(id=validated_data['report_template_id'], deleted=False).exists():
                raise WisdomValidationError('未找到访谈报告模版')
            if validated_data['report_template_id'] != instance.report_template_id:
                # 修改利益相关者访谈报告模版（教练任务模版），检查是否存在教练已填写的利益相关者访谈报告
                if InterviewRecordTemplateAnswer.objects.filter(public_attr=instance.coach_task.public_attr,
                                                                deleted=False).exists():
                    raise WisdomValidationError('当前访谈报告模版教练已填写，禁止修改')

        deleted_list, add_list = [], []
        if 'stakeholder' in validated_data.keys():
            stakeholder = validated_data.pop('stakeholder')
            err, deleted_list, add_list = stakeholder_interview_public.check_update_stakeholder(
                stakeholder, instance, validated_data.get('stakeholder_interview_number'))
            if err:
                raise WisdomValidationError(err)
        with transaction.atomic():
            if deleted:
                # 移除利益相关者访谈配置
                instance.stakeholder_interview.filter(deleted=False).update(deleted=True)
                instance.deleted = True
                instance.save()
                coach_task = instance.coach_task
                coach_task.deleted = True
                coach_task.save()
            else:
                exists_coach_template_id = instance.coach_template_id
                exists_report_template_id = instance.report_template_id
                super().update(instance=instance, validated_data=validated_data)

                if 'coach_template_id' in validated_data.keys():
                    if validated_data['coach_template_id'] != exists_coach_template_id:
                        # 修改模板删除缓存id
                        for item in instance.stakeholder_interview.filter(
                                deleted=False, interview__isnull=False).all():
                            UserTmp.objects.filter(
                                data_id=item.interview_id, type=UserTmpEnum.interview,
                                extra_id=exists_coach_template_id
                                ).delete()

                if 'report_template_id' in validated_data.keys():
                    report_template_id = validated_data['report_template_id']
                    coach_task = instance.coach_task
                    if exists_report_template_id != report_template_id:
                        # 修改模板删除缓存id
                        UserTmp.objects.filter(data_id=coach_task.id, type=UserTmpEnum.coach_tasks).delete()
                        coach_task.template_id = report_template_id
                        coach_task.save()

                stakeholder_interview_public.update_stakeholder_interview(add_list, deleted_list, instance)
        return instance