import datetime
import numpy as np

from rest_framework import viewsets
from django.utils import timezone
from drf_yasg import openapi
from rest_framework.decorators import action
from django.db.models import Q, Avg, Count, Sum
from django.db import transaction
from utils import aesencrypt, task
from utils.messagecenter.center import push_v2_message

from drf_yasg.utils import swagger_auto_schema

from .constant import PROJECT_OPERATION, PROJECT_COMPANY_ADMIN, PROJECT_ACCOUNT_MANAGER, INTERVIEW_TYPE_COACHING
from .. import utils
from ..common.project_service_public import get_project_progress_data, get_project_progress_report_data
from ..enum.service_content_enum import MultipleAssociationRelationTypeEnum, NewCoachTaskTypeEnum, \
    NoticeChannelTypeEnum, NoticeTemplateTypeEnum, ProjectCoachStatusEnum, CoachSourceTypeEnum, CoachOfferStatusEnum, \
    ChangeObservationInviteTypeEnum, TagObjectTypeEnum, TagRequirementConfigTypeEnum, PersonalReportTypeEnum
from wisdom_v2.common import project_coach_public, project_member_public, user_public, interview_public, tag_public, chemical_interview_public
from ..models import Project, Company, User, Coach, ProjectMember, ProjectCoach, ProjectInterested, \
    CompanyMember, ProjectInterviewRecord, ProjectInterview, ActionPlan, Habit, Diary, \
    ProjectBundle, ProjectDocs, UserBackend, OneToOneCoach, GrowthGoals, CoachTask, EvaluationReport, \
    GuidanceSuggestion, GroupCoach, PowerTag, EvaluationModule, ArticleModule, ProjectGroupCoach, Schedule, \
    ChangeObservation, MultipleAssociationRelation, GrowthGoals2ChangeObservation, PersonalReport, ProjectNote, \
    ProjectEvaluationReport, CoachAppraise, CapacityTag, ProjectInterviewOffLineRecord, LearnArticle, \
    EvaluationReportScore, WorkWechatUser, ChangeObservationAnswer, Resume, CoachOffer, UserNoticeRecord, ProjectOffer

from .project_actions import ProjectSerializers, ProjectMemberSerializers, get_query_times_count, \
    get_project_member_count, get_project_score, get_member_growth, InterviewDetailSerializers, valid_project_data, \
    ProjectInfoListSerializer, ProjectDetailsSerializers, ProjectBaseSerializers, project_progress_time_hours, \
    project_progress_all_hours
from wisdom_v2.app_views.app_coachee_growth_actions import AppCoacheeGrowthGoalsViewSerializers
from wisdom_v2.views import constant
from wisdom_v2.views.project_member_action import remove_none_data, InterviewScheduleSerializer
from wisdom_v2.views.user_backend_action import UserBackendListSerializer
from wisdom_v2.views.project_interview_action import ProjectInterviewListSerializer
from utils.pagination import StandardResultsSetPagination
from utils.api_response import success_response, parameter_error_response, WisdomValidationError
from wisdom_v2.enum.project_enum import ProjectStatusEnum
from wisdom_v2.enum.user_backend_enum import UserBackendTypeEnum
from utils.queryset import distinct
from wisdom_v2.models_file import StakeholderInterviewModule, StakeholderInterview, ProjectTagGroup, TagObject, \
    ProjectTagConfig
from ..models_file.tag import TagRequirementConfig


class ProjectViewSet(viewsets.ModelViewSet):
    queryset = Project.objects.filter(deleted=False).order_by('-created_at')
    serializer_class = ProjectSerializers

    @swagger_auto_schema(
        operation_id='项目列表',
        operation_summary='项目列表',
        manual_parameters=[
            openapi.Parameter('page', openapi.IN_QUERY, description='第几页', type=openapi.TYPE_NUMBER),
            openapi.Parameter('page_size', openapi.IN_QUERY, description='每页显示条数默认15条', type=openapi.TYPE_NUMBER),
            openapi.Parameter('company_name', openapi.IN_QUERY, description='企业名称｜简称', type=openapi.TYPE_STRING),
            openapi.Parameter('status', openapi.IN_QUERY, description="项目状态：1:打单中 2:进行中 3:已停用 4:已完成 5:已输单", type=openapi.TYPE_NUMBER),
            openapi.Parameter('company_id', openapi.IN_QUERY, description='企业id（编辑企业页面内项目列表展示）',
                              type=openapi.TYPE_NUMBER),
            openapi.Parameter('project_id', openapi.IN_QUERY, description='项目id', type=openapi.TYPE_NUMBER)
        ],
        tags=['项目相关']
    )
    def list(self, request, *args, **kwargs):
        try:
            status = request.query_params.get('status')
            company_name = request.query_params.get('company_name')
            company_id_str = request.query_params.get('company_id')
            project_id_str = request.query_params.get('project_id')
            project = self.get_queryset()
            if company_id_str:
                company_id_list = str(company_id_str).split(',')
                company_id_list = [int(item) for item in company_id_list]
                project = project.filter(company_id__in=company_id_list)
            if project_id_str:
                project_id_list = str(project_id_str).split(',')
                project_id_list = [int(item) for item in project_id_list]
                project = project.filter(id__in=project_id_list)
        except Exception as e:
            return parameter_error_response()

        # 项目顾问数据权限
        user = request.user
        user_backends = UserBackend.objects.filter(user_id=user.pk, role__name__in=['项目运营', '客户顾问'], deleted=False)
        if user_backends.exists():
            project = project.filter(project_user_backend__user_id=user.id)

        company_ids = UserBackend.objects.filter(~Q(company=None), user__id=user.id, user__deleted=False, role__name='企业管理员', deleted=False).values_list('company_id', flat=True)
        if company_ids:
            project = project.filter(company_id__in=company_ids, status__in=ProjectStatusEnum.admin_data_viewing())

        if company_name:
            project = project.filter(Q(company__name__icontains=company_name) |
                                     Q(company__short__icontains=company_name))

        if status and int(status) in ProjectStatusEnum.get_describe_keys():
            project = project.filter(status=int(status))

        # 分页
        paginator = StandardResultsSetPagination()
        page_list = paginator.paginate_queryset(project.order_by('status', '-created_at'), self.request)
        serializer = self.get_serializer(page_list, many=True)
        response = paginator.get_paginated_response(serializer.data)
        return success_response(response)

    @swagger_auto_schema(
        operation_id='项目信息查询列表',
        operation_summary='项目信息查询列表',
        manual_parameters=[
            openapi.Parameter('project_name', openapi.IN_QUERY, description='项目名称', type=openapi.TYPE_STRING),
            openapi.Parameter('full_name', openapi.IN_QUERY, description='企业名+项目名', type=openapi.TYPE_STRING),
            openapi.Parameter('project_id', openapi.IN_QUERY, description='项目ID', type=openapi.TYPE_NUMBER)
        ],
        tags=['项目相关']
    )
    @action(methods=['get'], detail=False, url_path='info', serializer_class=ProjectInfoListSerializer)
    def get_project_list_by_name(self, request, *args, **kwargs):
        project_name = request.query_params.get('project_name', None)
        full_name = request.query_params.get('full_name')
        project_id = request.query_params.get('project_id', None)
        project = self.get_queryset()
        if project_name:
            project = project.filter(name__icontains=project_name)
        if project_id:
            project = project.filter(id=project_id)
        if full_name:
            project = project.filter(
                Q(name__icontains=full_name) | Q(company__name__icontains=full_name) | Q(company__short__icontains=full_name))
        serializer = self.get_serializer(project, many=True)
        return success_response(serializer.data)

    @swagger_auto_schema(
        operation_id='项目详情',
        operation_summary='项目详情',
        manual_parameters=[
            openapi.Parameter('project_id', openapi.IN_QUERY, description='项目id', type=openapi.TYPE_NUMBER),

        ],
        tags=['项目相关']
    )
    @action(methods=['get'], detail=False, url_path='detail')
    def project_detail(self, request, *args, **kwargs):
        try:
            instance = Project.objects.get(pk=request.query_params.get('project_id', 0))
        except Project.DoesNotExist as e:
            return parameter_error_response()

        serializer = ProjectDetailsSerializers(instance)
        return success_response(serializer.data)

    @swagger_auto_schema(
        operation_id='编辑项目信息',
        operation_summary='编辑项目信息',
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'name': openapi.Schema(type=openapi.TYPE_STRING, description='项目名'),
                'company_id': openapi.Schema(type=openapi.TYPE_NUMBER, description='企业id'),
                'manager_user_ids': openapi.Schema(type=openapi.TYPE_NUMBER, description='项目顾问的用户id列表'),
                'account_manager_user_id': openapi.Schema(type=openapi.TYPE_NUMBER, description='客户经理的用户id列表'),
                'status': openapi.Schema(type=openapi.TYPE_NUMBER, description='项目状态'),
            }
        ),
        tags=['项目相关']
    )
    def update(self, request, *args, **kwargs):
        instance = self.get_object()
        exists_providing_coach_list_time = instance.providing_coach_list_time.strftime('%Y-%m-%d') if \
            instance.providing_coach_list_time else None
        mark_user_id_list = []
        data = request.data.copy()
        now_providing_coach_list_time = data.get('providing_coach_list_time', None)
        if 'company_id' in data.keys():
            return parameter_error_response('不可修改企业信息')

        manager_user_ids = None
        if 'manager_user_ids' in data.keys():
            manager_user_ids = data.get('manager_user_ids', [])
            data.pop('manager_user_ids')

        account_manager_user_id = 0
        if 'account_manager_user_id' in data.keys():
            account_manager_user_id = data.get('account_manager_user_id')
            data.pop('account_manager_user_id')

        # 企业管理员可以为空
        admin_user_ids = None
        if 'admin_user_ids' in data.keys():
            admin_user_ids = data.get('admin_user_ids', [])
            data.pop('admin_user_ids')

        # 是否需要更新标签信息：
        if 'status' in data.keys() and data.get('status') == ProjectStatusEnum.completed.value and instance.status != ProjectStatusEnum.completed.value:
            is_update_resume_tag = True
        else:
            is_update_resume_tag = False

        # 项目改为已输单，刷新用户token
        if 'status' in data.keys() and data.get('status') == ProjectStatusEnum.lost:
            mark_user_id_list = list(ProjectMember.objects.filter(project_id=instance.id, deleted=False).values_list('user_id', flat=True))
        with transaction.atomic():

            serializer = ProjectBaseSerializers(instance, data=data, partial=True)
            serializer.is_valid(raise_exception=True)
            serializer.save()
            if isinstance(manager_user_ids, list):
                utils.update_project_user_data(manager_user_ids, PROJECT_OPERATION, instance.id)
            if account_manager_user_id:
                utils.update_project_user_data([account_manager_user_id], PROJECT_ACCOUNT_MANAGER, instance.id)
            if isinstance(admin_user_ids, list):
                utils.update_project_user_data(admin_user_ids, PROJECT_COMPANY_ADMIN, instance.id)

            if mark_user_id_list:
                user_public.mark_user_as_terminated(list(set(mark_user_id_list)))

            # 项目改为已结束，刷新教练经验标签
            if is_update_resume_tag:
                tag_public.update_project_coach_tag(instance.id)

        serializer = ProjectDetailsSerializers(instance)
        if now_providing_coach_list_time and exists_providing_coach_list_time != now_providing_coach_list_time:
            task.push_lark_message_celery.delay([instance.pk], 'project_match_coach')
        return success_response(serializer.data)

    @swagger_auto_schema(
        operation_id='创建项目信息',
        operation_summary='创建项目信息',
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            required=['name', 'company_id', 'manager_user_ids', 'account_manager_user_id'],
            properties={
                'name': openapi.Schema(type=openapi.TYPE_STRING, description='项目名'),
                'company_id': openapi.Schema(type=openapi.TYPE_NUMBER, description='企业id'),
                'manager_user_ids': openapi.Schema(type=openapi.TYPE_NUMBER, description='项目顾问的用户id列表'),
                'account_manager_user_id': openapi.Schema(type=openapi.TYPE_NUMBER, description='客户经理的用户id列表'),
                'status': openapi.Schema(type=openapi.TYPE_INTEGER, description='1:打单中 2:进行中 3:已停用 4:已完成 5:已输单'),
            }
        ),
        tags=['项目相关']
    )
    def create(self, request, *args, **kwargs):
        try:
            manager_user_ids = request.data['manager_user_ids']
            account_manager_user_id = request.data['account_manager_user_id']
            admin_user_ids = request.data.get('admin_user_ids')
            status = request.data.get('status')
            company_id = request.data['company_id']
            Company.objects.get(pk=int(company_id))
            if status not in ProjectStatusEnum.get_describe_keys():
                return parameter_error_response('项目状态错误')

        except Company.DoesNotExist:
            return parameter_error_response('企业不存在')
        except Exception:
            return parameter_error_response('缺少必填参数信息')
        if not isinstance(manager_user_ids, list):
            return parameter_error_response('项目运营参数错误')
        if not isinstance(account_manager_user_id, int):
            return parameter_error_response('客户经理参数错误')

        if UserBackend.objects.filter(
                user_id__in=manager_user_ids,
                deleted=False,
                role__name='项目运营').count() < len(manager_user_ids):
            return parameter_error_response('项目运营不存在')

        data = request.data.copy()
        # 自定义校验 因返回格式未使用serializer校验
        data = remove_none_data(data)
        err = valid_project_data(data)
        if err:
            return parameter_error_response(err=err)

        with transaction.atomic():
            serializer = ProjectBaseSerializers(data=data)
            serializer.is_valid(raise_exception=True)
            instance = serializer.save()
            # 关联项目顾问
            utils.add_project_user_data(manager_user_ids, PROJECT_OPERATION, instance.id)
            # 关联客户顾问
            utils.add_project_user_data([account_manager_user_id], PROJECT_ACCOUNT_MANAGER, instance.id)
            # 关联企业管理员
            if admin_user_ids:
                utils.add_project_user_data(admin_user_ids, PROJECT_COMPANY_ADMIN, instance.id)
        serializer = ProjectDetailsSerializers(instance)
        return success_response(serializer.data)

    @swagger_auto_schema(
        operation_id='添加教练到项目',
        operation_summary='添加教练到项目',
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'project_id': openapi.Schema(type=openapi.TYPE_NUMBER, description='项目id'),
                'coach': openapi.Schema(type=openapi.TYPE_NUMBER, description='教练的用户id'),

            }
        ),
        tags=['项目相关']
    )
    @action(methods=['post'], detail=False, url_path='coach/add')
    def add_coach(self, request, *args, **kwargs):
        try:
            project = Project.objects.get(pk=int(request.data.get('project_id')))
            coach = Coach.objects.get(pk=int(request.data.get('coach_user_id')))
        except Exception as e:
            print(e)
            return parameter_error_response('请检查传入参数')

        project_coach, is_create = ProjectMember.objects.get_or_create(coach_id=coach.pk, project_id=project.pk, role=4)
        if not is_create:
            return parameter_error_response('用户已经是项目教练')

        return success_response()

    @swagger_auto_schema(
        operation_id='匹配教练',
        operation_summary='匹配教练',
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'project_id': openapi.Schema(type=openapi.TYPE_NUMBER, description='项目id'),
                'coach_user_id': openapi.Schema(type=openapi.TYPE_NUMBER, description='教练的用户id'),
                'coachee_user_id': openapi.Schema(type=openapi.TYPE_NUMBER, description='被教练者的用户id'),

            }
        ),
        tags=['项目相关']
    )
    @action(methods=['post'], detail=False, url_path='coach/relation')
    def coach_relation(self, request, *args, **kwargs):
        try:
            project = Project.objects.get(pk=int(request.data.get('project_id')))
            coach = Coach.objects.get(pk=int(request.data.get('coach_user_id')))
            user = Coach.objects.get(pk=int(request.data.get('coachee_user_id')))
        except Exception as e:
            print(e)
            return parameter_error_response('请检查传入参数')

        project_coach, is_create = ProjectCoach.objects.get_or_create(coach_id=coach.pk, project_id=project.pk,
                                                                      member_id=user.pk)
        if not is_create:
            return parameter_error_response('用户已经是匹配教练')

        return success_response()

    @swagger_auto_schema(
        operation_id='添加客户到项目',
        operation_summary='添加客户到项目',
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'project_id': openapi.Schema(type=openapi.TYPE_NUMBER, description='项目id'),
                'user_id': openapi.Schema(type=openapi.TYPE_NUMBER, description='客户id'),
                'role': openapi.Schema(type=openapi.TYPE_NUMBER, description='角色 5:项目管理员 6:被教练者'),
                'week_interview_count': openapi.Schema(type=openapi.TYPE_NUMBER,
                                                       description='每周辅导次数上限 -1:不做限制, 剩余传数字'),
                'one_interview_time': openapi.Schema(type=openapi.TYPE_NUMBER,
                                                     description='单次辅导时长上限 -1:不做限制, 剩余传数字（单位 分钟）'),
                'all_interview_time': openapi.Schema(type=openapi.TYPE_NUMBER,
                                                     description='该员工总辅导时长上限（-1:不做限制）（单位 分钟）'),

            }
        ),
        tags=['项目相关']
    )
    @action(methods=['post'], detail=False, url_path='member/add')
    def add_member(self, request, *args, **kwargs):
        try:
            project = Project.objects.get(pk=int(request.data.get('project_id')))
            user = User.objects.get(pk=int(request.data.get('user_id')))
            role = int(request.data.get('role'))
            week_interview_count = int(request.data.get('week_interview_count'))
            one_interview_time = int(request.data.get('one_interview_time'))
            all_interview_time = int(request.data.get('all_interview_time'))
        except Exception as e:
            return parameter_error_response('请检查传入参数')
        if all_interview_time > 0:
            interview_time = ProjectInterview.objects.filter(
                type=INTERVIEW_TYPE_COACHING,
                public_attr__project=project,  deleted=False).exclude(
                public_attr__status=constant.ATTR_STATUS_INTERVIEW_CANCEL).aggregate(times_minute=Sum('times'))['times_minute']
            if interview_time and project.all_times > (project.all_times - interview_time):
                return parameter_error_response('该员工总辅导时长超出限制')
        try:

            ProjectMember.objects.get(user_id=user.pk, role=role, project_id=project.pk)
            return parameter_error_response('该用户已是项目成员')
        except ProjectMember.DoesNotExist:
            member = ProjectMember.objects.create(user_id=user.pk, role=role, project_id=project.pk,
                                                  week_interview_count=week_interview_count,
                                                  one_interview_time=one_interview_time,
                                                  all_interview_time=all_interview_time)
            push_v2_message.delay(user, 'welcome_msg', param={'project_name': project.name}, project_id=project.pk)
            if ProjectBundle.objects.filter(project=project,  deleted=False, one_to_one_coach__type=constant.BUNDLE_INTEWVIEW_TYPE_OFFLINE_1V1).exists():
                push_v2_message.delay(user, 'coachee_first_msg', param={}, project_id=project.pk)
                member.coachee_first_msg_status = True
                member.save()

        return success_response()

    @swagger_auto_schema(
        operation_id='添加利益相关者到项目',
        operation_summary='添加利益相关者到项目',
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'project_id': openapi.Schema(type=openapi.TYPE_NUMBER, description='项目id'),
                'interested_user_id': openapi.Schema(type=openapi.TYPE_NUMBER, description='利益相关者（注意是member_id)'),
                'master_user_id': openapi.Schema(type=openapi.TYPE_NUMBER, description='利益相关者对象(注意是member_id)'),
                'relation': openapi.Schema(type=openapi.TYPE_STRING, description='上下级关系'),
                'concert_years': openapi.Schema(type=openapi.TYPE_STRING, description='合作年限'),
            }
        ),
        tags=['项目相关']
    )
    @action(methods=['post'], detail=False, url_path='interested/add')
    def add_interested(self, request, *args, **kwargs):
        try:
            relation = request.data.get('relation', None)
            concert_years = request.data.get('concert_years', None)
            project = Project.objects.get(pk=int(request.data.get('project_id')))
            interested_id = User.objects.get(pk=int(request.data.get('interested_user_id')))
            master_id = User.objects.get(pk=int(request.data.get('master_user_id')))
        except Exception as e:
            return parameter_error_response('请检查传入参数')
        project_interested, is_create = ProjectInterested.objects.get_or_create(interested_id=interested_id.pk,
                                                                                master_id=master_id.pk,
                                                                                project_id=project.pk)
        # 如果添加的利益相关者没有利益相关者身份 需要添加
        ProjectMember.objects.get_or_create(user_id=interested_id, project_id=project.pk, role=7)

        if not is_create:
            return parameter_error_response('请勿重复添加')
        if relation:
            project_interested.relation = relation
        if concert_years:
            project_interested.concert_years = concert_years
        project_interested.save()
        return success_response()

    @swagger_auto_schema(
        operation_id='添加项目成员',
        operation_summary='添加项目成员',
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'project_id': openapi.Schema(type=openapi.TYPE_NUMBER, description='项目id'),
                'company_id': openapi.Schema(type=openapi.TYPE_NUMBER, description='企业id'),
                'department': openapi.Schema(type=openapi.TYPE_STRING, description='部门'),
                'position': openapi.Schema(type=openapi.TYPE_STRING, description='职位'),
                'manage_role': openapi.Schema(type=openapi.TYPE_NUMBER, description='管理角色'),
                'employee': openapi.Schema(type=openapi.TYPE_NUMBER, description='下属人数'),

                'name': openapi.Schema(type=openapi.TYPE_STRING, description='用户名'),
                'true_name': openapi.Schema(type=openapi.TYPE_STRING, description='用户真名'),
                'password': openapi.Schema(type=openapi.TYPE_STRING, description='用户密码'),
                'phone': openapi.Schema(type=openapi.TYPE_STRING, description='用户手机号'),
                'email': openapi.Schema(type=openapi.TYPE_STRING, description='用户邮箱'),
                'gender': openapi.Schema(type=openapi.TYPE_NUMBER, description='性别 1:男 2:女'),
                'role': openapi.Schema(type=openapi.TYPE_NUMBER, description='5:企业管理员 6:被教练者')
            }
        ),
        tags=['项目相关']
    )
    @action(methods=['post'], detail=False, url_path='project_member/add')
    def add_project_member(self, request, *args, **kwargs):
        try:
            project = Project.objects.get(pk=int(request.data.get('project_id')))
            company = Company.objects.get(pk=int(request.data.get('company_id')))

            department = request.data.get('department', None)
            position = request.data.get('position', None)
            manage_role = int(request.data.get('manage_role', None))
            employee = int(request.data.get('employee', None))

            name = request.data.get('name', None)
            true_name = request.data.get('true_name', None)
            password = request.data.get('password', None)
            phone = request.data.get('phone', None)
            email = request.data.get('email', None)
            gender = int(request.data.get('gender', 1))
            role = int(request.data.get('role', None))


        except Exception as e:
            return parameter_error_response()
        if not all([name, true_name, password, phone, email, gender, role]):
            return parameter_error_response('缺少用户必填信息')
        if ' ' in email:
            return parameter_error_response('邮箱中存在空格，请修改后再添加。')

        _user = User.objects.filter(Q(name=name) | Q(email=email) | Q(phone=phone), deleted=False)
        # 验证用户是否存在，密码是否正确
        if _user.exists():
            return parameter_error_response('用户信息已存在')
        try:
            with transaction.atomic():

                user = User.objects.create(name=name, true_name=true_name, phone=phone,
                                           gender=gender,
                                           password=aesencrypt(password))
                CompanyMember.objects.create(user=user, company=company, manage_role=manage_role,
                                             department=department, position=position, employee=employee)

                ProjectMember.objects.create(user=user, project=project, role=role)

        except Exception as e:
            print(e)
            return parameter_error_response()
        return success_response()

    @swagger_auto_schema(
        operation_id='修改项目成员',
        operation_summary='修改项目成员',
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'member_id': openapi.Schema(type=openapi.TYPE_NUMBER, description='成员id'),
                'project_id': openapi.Schema(type=openapi.TYPE_NUMBER, description='项目id'),
                'week_interview_count': openapi.Schema(type=openapi.TYPE_NUMBER,
                                                       description='每周辅导次数上限 -1:不做次数限制【1-5】次'),
                'one_interview_time': openapi.Schema(type=openapi.TYPE_NUMBER, description='单次辅导时长上限（分钟）'),
                'all_interview_time': openapi.Schema(type=openapi.TYPE_NUMBER, description='该员工总辅导时长上限（分钟 -1:不做限制）'),
            }
        ),
        tags=['项目相关']
    )
    @action(methods=['post'], detail=False, url_path='project_member/update')
    def update_project_member(self, request, *args, **kwargs):
        try:
            project = Project.objects.get(pk=int(request.data.get('project_id')))
            member = ProjectMember.objects.get(pk=int(request.data.get('member_id')))

            week_interview_count = request.data.get('week_interview_count', None)
            one_interview_time = int(request.data.get('one_interview_time', 0))
            all_interview_time = request.data.get('all_interview_time', None)

        except Exception as e:
            return parameter_error_response()
        if ProjectMember.objects.filter(project=project, user_id=member.user_id, deleted=False).exclude(pk=member.pk).exists():
            return parameter_error_response('用户已经是该项目成员')

        if week_interview_count:
            member.week_interview_count = week_interview_count
        if one_interview_time:
            member.one_interview_time = one_interview_time
        if all_interview_time:
            member.all_interview_time = all_interview_time
        member.save()
        return success_response()

    @swagger_auto_schema(
        operation_id='项目成员列表',
        operation_summary='项目成员列表',
        manual_parameters=[
            openapi.Parameter('project_id', openapi.IN_QUERY, description='项目id', type=openapi.TYPE_NUMBER),

        ],
        tags=['项目相关']
    )
    @action(methods=['get'], detail=False, url_path='member/list')
    def member_list(self, request, *args, **kwargs):
        try:
            project = Project.objects.get(pk=request.query_params.get('project_id', 0), deleted=False)
        except Project.DoesNotExist as e:
            return parameter_error_response()

        member = ProjectMember.objects.filter(project=project, deleted=False).exclude(role=6)
        # 分页
        paginator = StandardResultsSetPagination()
        page_list = paginator.paginate_queryset(member, self.request)
        serializer = ProjectMemberSerializers(page_list, many=True)
        response = paginator.get_paginated_response(serializer.data)
        return success_response(response)

    @swagger_auto_schema(
        operation_id='移除项目成员',
        operation_summary='移除项目成员',
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'project_id': openapi.Schema(description='项目id', type=openapi.TYPE_NUMBER),
                'project_member_id': openapi.Schema('project_member_id', type=openapi.TYPE_ARRAY, description='项目成员id',
                                                    items=openapi.Schema(type=openapi.TYPE_NUMBER))
            },
        ),
        tags=['项目相关']

    )
    @action(methods=['post'], detail=False, url_path='member/remove')
    def member_remove(self, request, *args, **kwargs):
        try:
            project = Project.objects.get(pk=request.data.get('project_id', 0), deleted=False)
        except Project.DoesNotExist as e:
            return parameter_error_response()
        member_ids = request.data.get('project_member_id')
        members = ProjectMember.objects.filter(project=project, id__in=member_ids)
        # 用户token将禁止登录
        user_public.mark_user_as_terminated(list(members.values_list('user_id', flat=True)))
        members.update(deleted=True)

        return success_response()

    @swagger_auto_schema(
        operation_id='项目教练列表',
        operation_summary='项目教练列表',
        manual_parameters=[
            openapi.Parameter('project_id', openapi.IN_QUERY, description='项目id', type=openapi.TYPE_NUMBER),

        ],
        tags=['项目相关']
    )
    @action(methods=['get'], detail=False, url_path='coach/list')
    def coach_list(self, request, *args, **kwargs):
        try:
            project = Project.objects.get(pk=request.query_params.get('project_id', 0), deleted=False)
        except Project.DoesNotExist as e:
            return parameter_error_response()

        member = ProjectMember.objects.filter(project=project, role=4, deleted=False)
        # 分页
        paginator = StandardResultsSetPagination()
        page_list = paginator.paginate_queryset(member, self.request)
        serializer = ProjectMemberSerializers(page_list, many=True)
        response = paginator.get_paginated_response(serializer.data)
        return success_response(response)

    @swagger_auto_schema(
        operation_id='项目教练查询',
        operation_summary='项目教练查询',
        manual_parameters=[
            openapi.Parameter('keyword', openapi.IN_QUERY, description='关键字', type=openapi.TYPE_STRING),

        ],
        tags=['项目相关']
    )
    @action(methods=['get'], detail=False, url_path='coach/search')
    def coach_search(self, request, *args, **kwargs):
        try:
            keyword = request.query_params.get('keyword', None)
        except (ValueError, TypeError) as e:
            return parameter_error_response()
        coach_list = Coach.objects.filter(Q(user__name__icontains=keyword), Q(user__true_name__icontains=keyword),
                                          deleted=False)
        data = []
        for coach in coach_list:
            data.append({'coach_user_id': coach.user.pk,
                         'coach_list': coach.user.cover_name}
                        )
        return success_response(data)

    @swagger_auto_schema(
        operation_id='项目列表（企业管理员）',
        operation_summary='项目列表（企业管理员）',
        manual_parameters=[
            openapi.Parameter('company_manager_user_id', openapi.IN_QUERY, description='企业管理员id',
                              type=openapi.TYPE_NUMBER),

        ],
        tags=['企业管理员-数据看板']
    )
    @action(methods=['get'], detail=False, url_path='project_list')
    def project_list(self, request, *args, **kwargs):
        try:
            project_ids = UserBackend.objects.filter(
                ~Q(company=None),
                user__id=int(request.query_params.get('company_manager_user_id', 0)),
                user__deleted=False,
                role=5).values_list('project__id', flat=True)
        except User.DoesNotExist as e:
            return parameter_error_response()
        projects = Project.objects.filter(
            pk__in=project_ids, deleted=False,
            status__in=ProjectStatusEnum.admin_data_viewing()).order_by('-end_time')
        data = []
        for item in projects:
            times_minute = ProjectInterview.objects.filter(
                ~Q(public_attr__target_user=None),
                ~Q(public_attr__user=None),
                type=INTERVIEW_TYPE_COACHING,
                public_attr__project=item,
                deleted=False
            ).aggregate(times_minute=Sum('times'))['times_minute']
            if times_minute and item.all_times:
                remain_hours = round((item.all_times - times_minute) / 60, 1)
            else:
                remain_hours = 0
            data.append({'id': item.pk,
                         'name': item.name,
                         'remain_hours': remain_hours})
        return success_response(data)

    @swagger_auto_schema(
        operation_id='项目总览',
        operation_summary='项目总览',
        manual_parameters=[
            openapi.Parameter('company_manager_user_id', openapi.IN_QUERY, description='企业管理员id',
                              type=openapi.TYPE_NUMBER),
            openapi.Parameter('project_id', openapi.IN_QUERY, description='项目id', type=openapi.TYPE_NUMBER),

        ],
        tags=['企业管理员-数据看板']
    )
    @action(methods=['get'], detail=False, url_path='statistics')
    def statistics(self, request, *args, **kwargs):
        try:
            user = User.objects.get(pk=int(request.query_params.get('company_manager_user_id', 0)), deleted=False)
            project = Project.objects.get(pk=int(request.query_params.get('project_id', 0)), deleted=False)
        except (Project.DoesNotExist, User.DoesNotExist) as e:
            return parameter_error_response()

        today = datetime.datetime.date(datetime.datetime.today())
        # 项目周期
        project_cycle = project.start_time.strftime('%Y-%m-%d') + '~' + project.end_time.strftime('%Y-%m-%d') if \
            project.start_time and project.end_time else '--'
        # 总天数
        all_days = (project.end_time - project.start_time).days + 1 if project.start_time and project.end_time else 0
        # 项目剩余天数
        remain_days = (project.end_time-today).days if project.end_time and project.end_time > today else 0
        # 项目已使用天数
        used_days = all_days - remain_days if all_days else 0

        # 辅导已用时长
        used_interview_hours = project_progress_time_hours(project)
        # 项目总时长
        total_interview_hours = project_progress_all_hours(project)
        # 辅导时长剩余时长
        remain_interview_hours = round(total_interview_hours - used_interview_hours, 1) if total_interview_hours else 0

        # 参与人数
        member_count = ProjectMember.objects.filter(project=project, deleted=False).count()

        score_data = interview_public.get_total_interview_scores(project_id=project.id)
        # 从 score_data 中获取各项评分
        total_target_progress = score_data['target_progress_sum_score']
        total_harvest_score = score_data['harvest_sum_score']
        total_satisfaction_score = score_data['satisfaction_sum_score']
        target_progress_count = score_data['target_progress_count']
        harvest_count = score_data['harvest_count']
        satisfaction_count = score_data['satisfaction_count']

        # 计算平均分
        avg_data = {
            'target_progress_avg': round(total_target_progress / target_progress_count, 1) if target_progress_count > 0 else '--',
            'harvest_score_avg': round(total_harvest_score / harvest_count, 1) if harvest_count > 0 else '--',
            'satisfaction_score_avg': round(total_satisfaction_score / satisfaction_count, 1) if satisfaction_count > 0 else '--'
        }

        data = {
            'all_days': all_days,  # 项目总天数
            'remain_days': remain_days,  # 项目剩余天数
            'used_days': used_days,  # 项目已使用天数
            'project_cycle': project_cycle,  # 项目周期
            'total_interview_hours': total_interview_hours,  # 辅导总时长
            'used_interview_hours': used_interview_hours,  # 辅导已用时长
            'remain_interview_hours': remain_interview_hours,  # 辅导剩余时长
            'member_count': member_count,  # 参与人数
            'score_avg': avg_data,
            'is_action_plan_to_report': project.is_action_plan_to_report,  # 行动计划是否写入教练报告
            'start_data': project.start_time.strftime('%Y-%m-%d') if project.start_time else None,  # 开始日期
            'end_data': project.end_time.strftime('%Y-%m-%d') if project.start_time else None ,  # 结束日期
        }
        return success_response(data)

    @swagger_auto_schema(
        operation_id='辅导进度',
        operation_summary='辅导进度',
        manual_parameters=[
            openapi.Parameter('name', openapi.IN_QUERY, description='员工姓名',
                              type=openapi.TYPE_STRING),
            openapi.Parameter('project_id', openapi.IN_QUERY, description='项目id', type=openapi.TYPE_NUMBER),

        ],
        tags=['企业管理员-数据看板']
    )
    @action(methods=['get'], detail=False, url_path='interview_schedule', serializer_class=InterviewScheduleSerializer)
    def interview_schedule(self, request, *args, **kwargs):
        try:
            project_id = request.query_params.get('project_id', None)
            user_id = request.user.pk
            user = UserBackend.objects.filter(
                ~Q(company=None),
                user__id=user_id,
                user__deleted=False,
                role=5).first()
            name = request.query_params.get('name', None)
        except:
            return parameter_error_response()
        queryset = ProjectMember.objects.filter(project__company_id=user.company.id, deleted=False).order_by('-created_at')
        if project_id:
            queryset = queryset.filter(project_id=project_id)
        else:
            queryset = queryset.filter(project__status__in=ProjectStatusEnum.admin_data_viewing())
        if name:
            queryset = queryset.filter(user__true_name__icontains=name)

        # 分页
        paginator = StandardResultsSetPagination()
        page_list = paginator.paginate_queryset(queryset, self.request)
        serializer = self.get_serializer(page_list, many=True)
        response = paginator.get_paginated_response(serializer.data)
        return success_response(response)


    @swagger_auto_schema(
        operation_id='成长目标',
        operation_summary='成长目标',
        manual_parameters=[
            openapi.Parameter('project_id', openapi.IN_QUERY, description='项目id', type=openapi.TYPE_NUMBER),
            openapi.Parameter('project_member_id', openapi.IN_QUERY, description='被教练者id', type=openapi.TYPE_NUMBER),

        ],
        tags=['企业管理员-数据看板']
    )
    @action(methods=['get'], detail=False, url_path='growth_goals', serializer_class=AppCoacheeGrowthGoalsViewSerializers)
    def growth_goals(self, request, *args, **kwargs):
        try:
            project_member_id = request.query_params.get('project_member_id')
            project_id = request.query_params.get('project_id')
        except:
            return parameter_error_response()
        # 成长目标
        if project_member_id:
            project_member = ProjectMember.objects.filter(pk=project_member_id, deleted=False).first()
            if not project_member:
                return parameter_error_response()
            gwoth_goals = GrowthGoals.objects.filter(public_attr__user=project_member.user,
                                                     deleted=False)
        elif project_id:
            project = Project.objects.filter(pk=project_id, deleted=False).first()
            if not project:
                return parameter_error_response()
            user_ids = list(ProjectMember.objects.filter(project_id=project_id,
                                                         deleted=False).values_list('user_id', flat=True))

            gwoth_goals = GrowthGoals.objects.filter(public_attr__user__in=user_ids, deleted=False)
        else:
            user_id = request.user.pk
            user = UserBackend.objects.filter(
                ~Q(company=None),
                user__id=user_id,
                user__deleted=False,
                role=5).first()
            if not user or not user.company_id:
                return parameter_error_response()
            user_ids = ProjectMember.objects.filter(project__company_id=user.company.id,
                                                    deleted=False).values_list('user_id', flat=True)
            gwoth_goals = GrowthGoals.objects.filter(public_attr__user__in=user_ids, deleted=False)

        # 分页
        paginator = StandardResultsSetPagination()
        page_list = paginator.paginate_queryset(gwoth_goals.order_by('-created_at'), self.request)
        serializer = self.get_serializer(page_list, many=True)
        response = paginator.get_paginated_response(serializer.data)
        return success_response(response)

    @swagger_auto_schema(
        operation_id='员工详情页员工报告',
        operation_summary='员工详情页员工报告',
        manual_parameters=[
            openapi.Parameter('project_id', openapi.IN_QUERY, description='项目id', type=openapi.TYPE_NUMBER),
            openapi.Parameter('project_member_id', openapi.IN_QUERY, description='被教练者id', type=openapi.TYPE_NUMBER),

        ],
        tags=['企业管理员-数据看板']
    )
    @action(methods=['get'], detail=False, url_path='member_report')
    def member_report(self, request, *args, **kwargs):
        try:
            project_member_id = request.query_params.get('project_member_id')
            project_id = request.query_params.get('project_id')
            admin_user = request.user
        except:
            return parameter_error_response()
        if project_member_id:
            project_member = ProjectMember.objects.filter(pk=project_member_id, deleted=False).first()
            if not project_member:
                return parameter_error_response()
            # 教练任务报告
            coach_tasks = CoachTask.objects.filter(Q(coach_submit_time__isnull=False) |
                                                   Q(coachee_submit_time__isnull=False),
                                                   project_bundle__project_member=project_member, deleted=False)
            # 测评报告
            evaluation_reports = EvaluationReport.objects.filter(public_attr__project=project_member.project, deleted=False,
                                                                 public_attr__user=project_member.user).exclude(
                evaluation__code=constant.MANAGE_EVALUATION)
            # 第三方报告
            project_docs = ProjectDocs.objects.filter(project_member=project_member, deleted=False)
            # 个人报告
            personal_reports = PersonalReport.objects.filter(project=project_member.project, user=project_member.user,
                                                             deleted=False)

        elif project_id:
            project = Project.objects.filter(pk=project_id).first()
            if not project:
                return parameter_error_response()
            coach_tasks = CoachTask.objects.filter(Q(coach_submit_time__isnull=False) |
                                                   Q(coachee_submit_time__isnull=False),
                                                   project_bundle__project=project, deleted=False)
            evaluation_reports = EvaluationReport.objects.filter(public_attr__project=project, deleted=False).exclude(
                evaluation__code=constant.MANAGE_EVALUATION)
            project_docs = ProjectDocs.objects.filter(project_member__project=project, deleted=False)
            personal_reports = PersonalReport.objects.filter(project_id=project_id, deleted=False)
        else:
            user_id = request.user.pk
            user = UserBackend.objects.filter(
                ~Q(company=None),
                user__id=user_id,
                user__deleted=False,
                role=5).first()
            if not user or not user.company_id:
                return parameter_error_response()
            coach_tasks = CoachTask.objects.filter(Q(coach_submit_time__isnull=False) |
                                                   Q(coachee_submit_time__isnull=False),
                                                   project_bundle__project__company_id=user.company_id, deleted=False)
            evaluation_reports = EvaluationReport.objects.filter(public_attr__project__company_id=user.company_id, deleted=False).exclude(
                evaluation__code=constant.MANAGE_EVALUATION)
            project_docs = ProjectDocs.objects.filter(project_member__project__company_id=user.company_id, deleted=False)
            personal_reports = PersonalReport.objects.filter(project__company_id=user.company_id, deleted=False)

        # 个人报告不返回未完成的“利益相关者访谈纪要”
        # 个人报告不返回未完成的“利益相关者访谈总结报告”
        personal_reports = personal_reports.exclude(
            type=PersonalReportTypeEnum.notes_report.value, pdf_url__isnull=True).exclude(
            type=PersonalReportTypeEnum.summary_report.value, pdf_url__isnull=True)

        if UserBackend.objects.filter(user__id=admin_user.id, user__deleted=False, role__name='企业管理员').exists():
            # 企业管理员的教练任务不返回“利益相关者访谈总结报告“的任务
            coach_tasks = coach_tasks.exclude(template__title__icontains=constant.EXCLUDE_COACH_TASK_TEMPLATE_NAME)
            # 企业管理员不返回“利益相关者访谈纪要”
            personal_reports = personal_reports.exclude(
                type=PersonalReportTypeEnum.notes_report.value)

        evaluation_reports_data = []
        coach_tasks_data = []
        project_docs_data = []
        personal_report_data = []
        if coach_tasks.exists():
            coach_tasks_data = [{'id': coach_task.pk,
                                 'name': coach_task.template.title,
                                 'type': 1,
                                 "file_path": '',  # 页面展示的图片路径，并非pdf文件路径，该类类型暂时不需要
                                 'coachee_name': coach_task.project_bundle.project_member.user.cover_name} for coach_task in coach_tasks]

        if evaluation_reports.exists():
            evaluation_reports_data = []
            for evaluation_report in evaluation_reports:
                evaluation_report_config = evaluation_report.evaluation.evaluation_report_config.filter(deleted=False).first()
                evaluation_reports_data.append(
                    {
                        'id': evaluation_report.pk,
                        'name': evaluation_report.evaluation.name,
                        'type': 2 if evaluation_report.evaluation.code == constant.MANAGE_EVALUATION else 3,
                        "file_path": '',  # 页面展示的图片路径，并非pdf文件路径，该类类型暂时不需要
                        "evaluation_report_type": evaluation_report_config.type if evaluation_report_config else None,
                        'coachee_name': evaluation_report.public_attr.user.cover_name
                    })
        if project_docs.exists():
            project_docs_data = [{'id': project_doc.pk,
                                  'name': project_doc.file.file_name,
                                  'type': 4,
                                  'file_path': project_doc.file.file_path,
                                  'coachee_name': project_doc.project_member.user.cover_name if
                                  project_doc.project_member_id else ''} for project_doc in project_docs]
        if personal_reports.exists():
            personal_report_data = [{"id": personal_report.pk,
                                     "name": personal_report.name,
                                     "type": 6,
                                     "pdf_url": personal_report.pdf_url,
                                     "personal_report_type": personal_report.type,
                                     "file_path": '',  # 页面展示的图片路径，并非pdf文件路径，该类类型暂时不需要
                                     "coachee_name": personal_report.user.cover_name,
                                     }
                                    for personal_report in personal_reports]
        # type 1-教练任务 2-教练型管理者测评 3-LBI测评 4-第三方报告 6-个人报告
        data = evaluation_reports_data + coach_tasks_data + project_docs_data + personal_report_data

        # 分页
        paginator = StandardResultsSetPagination()
        page_list = paginator.paginate_queryset(data, self.request)
        response = paginator.get_paginated_response(page_list)
        return success_response(response)

    @swagger_auto_schema(
        operation_id='员工详情辅导明细列表',
        operation_summary='员工详情辅导明细列表',
        manual_parameters=[
            openapi.Parameter('project_id', openapi.IN_QUERY, description='项目id', type=openapi.TYPE_NUMBER),
            openapi.Parameter('project_member_id', openapi.IN_QUERY, description='被教练者id', type=openapi.TYPE_NUMBER),

        ],
        tags=['企业管理员-数据看板']
    )
    @action(methods=['get'], detail=False, url_path='member_interview_detail', serializer_class=ProjectInterviewListSerializer)
    def member_interview_detail(self, request, *args, **kwargs):
        try:
            project_member_id = request.query_params.get('project_member_id')
            project_id = request.query_params.get('project_id')
            start_time = request.query_params.get('start_time')
            end_time = request.query_params.get('end_time')
        except:
            return parameter_error_response()
        if project_member_id:
            project_member = ProjectMember.objects.filter(pk__in=project_member_id.split(','), deleted=False).first()
            if not project_member:
                return parameter_error_response()
            interview = ProjectInterview.objects.filter(public_attr__project=project_member.project, deleted=False,
                                                        public_attr__target_user=project_member.user).exclude(
                public_attr__status=constant.ATTR_STATUS_INTERVIEW_CANCEL)
        elif project_id:
            project = Project.objects.filter(pk=project_id, deleted=False).first()
            if not project:
                return parameter_error_response()
            interview = ProjectInterview.objects.filter(public_attr__project=project, deleted=False).exclude(
                public_attr__status=constant.ATTR_STATUS_INTERVIEW_CANCEL)
        else:
            user_id = request.user.pk
            user = UserBackend.objects.filter(
                ~Q(company=None),
                user__id=user_id,
                user__deleted=False,
                role=5).first()
            if not user or not user.company_id:
                return parameter_error_response()
            interview = ProjectInterview.objects.filter(public_attr__project__company_id=user.company_id, deleted=False).exclude(
                public_attr__status=constant.ATTR_STATUS_INTERVIEW_CANCEL)
        if start_time and end_time:
            interview = interview.filter(public_attr__start_time__range=[start_time, end_time])

        # 分页
        paginator = StandardResultsSetPagination()
        page_list = paginator.paginate_queryset(interview.order_by('-public_attr__start_time'), self.request)
        serializer = self.get_serializer(page_list, many=True)
        response = paginator.get_paginated_response(serializer.data)
        return success_response(response)

    @swagger_auto_schema(
        operation_id='项目月报',
        operation_summary='项目月报',
        manual_parameters=[
            openapi.Parameter('company_manager_user_id', openapi.IN_QUERY, description='企业管理员id',
                              type=openapi.TYPE_NUMBER),
            openapi.Parameter('project_id', openapi.IN_QUERY, description='项目id', type=openapi.TYPE_NUMBER),
            openapi.Parameter('start_time', openapi.IN_QUERY, description='开始时间', type=openapi.TYPE_STRING),
            openapi.Parameter('end_time', openapi.IN_QUERY, description='结束时间', type=openapi.TYPE_STRING),
            openapi.Parameter('time_type', openapi.IN_QUERY, description='1:天 2:周 3:月', type=openapi.TYPE_NUMBER),
            openapi.Parameter('member_growth', openapi.IN_QUERY, description='员工成长 1:行动力 2:学习力 3:改变力 4:思辨力',
                              type=openapi.TYPE_NUMBER),

        ],
        tags=['企业管理员-数据看板']
    )
    @action(methods=['get'], detail=False, url_path='month/report')
    def month_report(self, request, *args, **kwargs):
        try:
            user = User.objects.get(pk=int(request.query_params.get('company_manager_user_id', 0)), deleted=False)
            project = Project.objects.get(pk=int(request.query_params.get('project_id', 0)), deleted=False)
            time_type = int(request.query_params.get('time_type', 1))
            member_growth = int(request.query_params.get('member_growth', 1))
            start_time = datetime.datetime.strptime(request.query_params.get('start_time', None), '%Y-%m-%d')
            end_time = datetime.datetime.strptime(request.query_params.get('end_time', None), '%Y-%m-%d')
        except (Project.DoesNotExist, User.DoesNotExist, TypeError) as e:
            return parameter_error_response()

        if time_type not in [1, 2, 3]:
            return parameter_error_response('time_type参数错误')
        if time_type == 1 and (end_time - start_time).days > 32:
            return parameter_error_response('所选时间范围过长')
        if time_type == 2 and (end_time - start_time).days > 92:
            return parameter_error_response('所选时间范围过长')
        if time_type == 3 and (end_time - start_time).days > 366:
            return parameter_error_response('所选时间范围过长')

        time_type_map = {1: {'date_format': 'D', 'step': 1, 'str_format': '%Y-%m-%d', 'filed_format': 'date'},
                         2: {'date_format': 'D', 'step': 7, 'str_format': '%W', 'filed_format': 'week'},
                         3: {'date_format': 'M', 'step': 1, 'str_format': '%m', 'filed_format': 'month'}
                         }

        if member_growth not in [1, 2, 3, 4]:
            return parameter_error_response('member_growth参数错误')

        if time_type == 1:
            time_obj = datetime.timedelta(days=1)
        elif time_type == 2:
            time_obj = datetime.timedelta(weeks=1)
        else:
            time_obj = datetime.timedelta(days=1)
        interview_query = ProjectInterview.objects.filter(public_attr__project=project,
                                                          public_attr__type=constant.ATTR_TYPE_INTERVIEW,
                                                          public_attr__start_time__range=[start_time, end_time],
                                                          # coachee_record_status=True,
                                                          # coach_record_status=True
                                                          deleted=False,
                                                          ).exclude(public_attr__status=constant.ATTR_STATUS_INTERVIEW_CANCEL)
        # 辅导总时长 & 辅导总次数
        interview_times, interview_count = get_query_times_count(interview_query)
        date_list = np.arange(start_time,
                              end_time + time_obj,
                              dtype='datetime64[%s]' % time_type_map[time_type]['date_format'],
                              step=time_type_map[time_type]['step'])
        interview_data = {'data': [],
                          'interview_times': interview_times,
                          'interview_count': interview_count,
                          }
        interview_data_all = interview_query.values(
            'public_attr__start_time__%s' % time_type_map[time_type]['filed_format']). \
            annotate(interview_count=Count('pk'), interview_times=Sum('times') / 60)
        interview_res = {str(_['public_attr__start_time__%s' % time_type_map[time_type]['filed_format']]):
                             {'interview_times': _['interview_times'],
                              'interview_count': _['interview_count']} for _ in interview_data_all}

        project_member_all, activate_count_all, active_count_all = get_project_member_count(project, start_time,
                                                                                            end_time, all_data=True)
        member_data = {'data': [],
                       'project_member': project_member_all,
                       'activate_count': activate_count_all,
                       'active_count': active_count_all
                       }

        score_avg_all = get_project_score(project, start_time, end_time, all_data=True)
        score_data = {'data': [],
                      'score_avg_all': score_avg_all
                      }

        record = ProjectInterviewRecord.objects.filter(deleted=False, interview__public_attr__project=project,
                                                       created_at__date__range=[start_time, end_time])

        score_avg = record.values('created_at__%s' % time_type_map[time_type]['filed_format']).annotate(
            target_progress_avg=Avg('target_progress'), harvest_score_avg=Avg('harvest_score'),
            satisfaction_score_avg=Avg('satisfaction_score'))
        record_res = {str(_['created_at__%s' % time_type_map[time_type]['filed_format']]):
                          {'target_progress_avg': _['target_progress_avg'],
                           'harvest_score_avg': _['harvest_score_avg'],
                           'satisfaction_score_avg': _['satisfaction_score_avg'],
                           } for _ in score_avg}
        # 1:行动力 2:学习力 3:改变力 4:思辨力
        all_member_growth_data = {'action': [], 'learn': [], 'change': [], 'analyse': []}
        new_count, end_count = get_member_growth(project, start_time, end_time, member_growth, all_data=True)
        member_growth_data = {'data': [],
                              'new_count': new_count,
                              'end_count': end_count
                              }
        for index, value in enumerate(date_list):
            try:
                _date = value.astype(datetime.datetime).strftime('%s' % time_type_map[time_type]['str_format'])
                if _date in interview_res.keys():
                    interview_data['data'].append({'date': value,
                                                   'values': {
                                                       'interview_times': interview_res[_date]['interview_times'],
                                                       'interview_count': interview_res[_date]['interview_count']}})
                else:
                    interview_data['data'].append({'date': value,
                                                   'values': {'interview_times': 0,
                                                              'interview_count': 0}})
                if _date in record_res.keys():
                    score_data['data'].append({'date': value,
                                               'values': {'score_avg': {
                                                   'target_progress_avg': record_res[_date]['target_progress_avg'],
                                                   'harvest_score_avg': record_res[_date]['harvest_score_avg'],
                                                   'satisfaction_score_avg': record_res[_date][
                                                       'satisfaction_score_avg'],
                                               },
                                                   'target_progress_count': record.values('target_progress').annotate(
                                                       target_progress_count=Count('target_progress')),
                                                   'harvest_score_count': record.values('harvest_score').annotate(
                                                       harvest_score_count=Count('harvest_score')),
                                                   'satisfaction_score_count': record.values(
                                                       'satisfaction_score').annotate(
                                                       satisfaction_score_count=Count('satisfaction_score')),
                                               }})
                else:
                    score_data['data'].append({'date': value,
                                               'values': {'score_avg': {
                                                   'target_progress_avg': None,
                                                   'harvest_score_avg': None,
                                                   'satisfaction_score_avg': None,
                                               },
                                                   'target_progress_count': [],
                                                   'harvest_score_count': [],
                                                   'satisfaction_score_count': [],
                                               }})


                project_member, activate_count, active_count = get_project_member_count(project,
                                                                                        value.astype(datetime.datetime),
                                                                                        value.astype(
                                                                                            datetime.datetime) + time_obj,
                                                                                        all_time=start_time)
                member_data['data'].append({'date': (value.astype(datetime.datetime)).strftime('%Y-%m-%d'),
                                            'values': {'project_member': project_member,
                                                       'activate_count': activate_count,
                                                       'active_count': active_count}})

                for growth in [1, 2, 3, 4]:
                    new_count, end_count = get_member_growth(project, value.astype(datetime.datetime),
                                                             value.astype(datetime.datetime) + time_obj,
                                                             growth)
                    keyname = {1: 'action', 2: 'learn', 3: 'change', 4: 'analyse'}
                    all_member_growth_data[keyname[growth]].append(
                        {'date': (value.astype(datetime.datetime)).strftime('%Y-%m-%d'),
                         'values': {'new_count': new_count, 'end_count': end_count}})

            except Exception as e:
                print('error ', e)
                break
        member_growth_data['data'] = all_member_growth_data
        data = {'interview_data': interview_data,  # 辅导动态
                'member_data': member_data,  # 活跃动态
                'score_data': score_data,  # 项目评分
                'member_growth_data': member_growth_data,  # 员工成长
                'date_list': date_list
                }
        return success_response(data)

    @swagger_auto_schema(
        operation_id='辅导明细',
        operation_summary='辅导明细',
        manual_parameters=[
            openapi.Parameter('project_id', openapi.IN_QUERY, description='项目id', type=openapi.TYPE_NUMBER),
            openapi.Parameter('company_manager_user_id', openapi.IN_QUERY, description='用户id',
                              type=openapi.TYPE_NUMBER, required=True),
            openapi.Parameter('user_name', openapi.IN_QUERY, description='员工姓名', type=openapi.TYPE_STRING),
            openapi.Parameter('start_time', openapi.IN_QUERY, description='开始时间', type=openapi.TYPE_STRING),
            openapi.Parameter('end_time', openapi.IN_QUERY, description='结束时间', type=openapi.TYPE_STRING),
            openapi.Parameter('interview_status', openapi.IN_QUERY, description='辅导状态 1:未开始 2:进行中 3:完成 4:取消 5:默认',
                              type=openapi.TYPE_NUMBER),

            openapi.Parameter('page', openapi.IN_QUERY, description='第几页', type=openapi.TYPE_NUMBER),
            openapi.Parameter('page_size', openapi.IN_QUERY, description='每页显示条数默认15条', type=openapi.TYPE_NUMBER)

        ],
        tags=['企业管理员-数据看板']
    )
    @action(methods=['get'], detail=False, url_path='interview/detail', serializer_class=InterviewDetailSerializers)
    def interview_detail(self, request, *args, **kwargs):
        try:
            user = UserBackend.objects.filter(
                user__id=int(request.query_params.get('company_manager_user_id', 0)),
                user__deleted=False,
                role=5).first()
            project_id = int(request.query_params.get('project_id', 0))
            interview_status = int(request.query_params.get('interview_status', 5))
            user_name = request.query_params.get('user_name', None)
            start_time = request.query_params.get('start_time', None)
            end_time = request.query_params.get('end_time', None)
        except (TypeError, ValueError, Project.DoesNotExist):
            return parameter_error_response()
        project_ids = Project.objects.filter(company_id=user.company.id, deleted=False).values_list('pk', flat=True)
        queryset = ProjectInterview.objects.filter(
            ~Q(public_attr__target_user=None),
            ~Q(public_attr__user=None),
            deleted=False,
            public_attr__project_id__in=project_ids
        ).order_by('-created_at')
        if project_id:
            queryset = queryset.filter(public_attr__project_id=project_id)
        if user_name:
            queryset = queryset.filter(public_attr__target_user__true_name=user_name)
        if start_time and end_time:
            queryset = queryset.filter(public_attr__start_time__range=[start_time, end_time])
        now_time = timezone.now()
        if interview_status == 1:
            queryset = queryset.filter(public_attr__start_time__gt=now_time
                                       ).exclude(public_attr__status=constant.ATTR_STATUS_INTERVIEW_CANCEL)
        elif interview_status == 2:
            queryset = queryset.filter(public_attr__start_time__lt=now_time,
                                       public_attr__end_time__gt=now_time,
                                       ).exclude(public_attr__status=constant.ATTR_STATUS_INTERVIEW_CANCEL)

        elif interview_status == 3:
            queryset = queryset.filter(public_attr__end_time__lt=now_time
                                       ).exclude(public_attr__status=constant.ATTR_STATUS_INTERVIEW_CANCEL)
        elif interview_status == 4:
            queryset = queryset.filter(public_attr__status=constant.ATTR_STATUS_INTERVIEW_CANCEL)
        else:
            queryset = queryset.filter()
        # 分页
        paginator = StandardResultsSetPagination()
        page_list = paginator.paginate_queryset(queryset, self.request)
        serializer = InterviewDetailSerializers(page_list, many=True)
        response = paginator.get_paginated_response(serializer.data)
        return success_response(response)


    @swagger_auto_schema(
        operation_id='停用/启用项目',
        operation_summary='停用/启用项目',
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'project_id': openapi.Schema(description='项目id', type=openapi.TYPE_NUMBER),
                'is_enable': openapi.Schema(type=openapi.TYPE_NUMBER, description='停用/启用 1-启用 0停用')
            },
        ),
        tags=['项目相关']

    )
    @action(methods=['post'], detail=False, url_path='project_is_enable')
    def project_is_enable(self, request, *args, **kwargs):
        try:
            project_id = request.data.get('project_id')
            is_enable = request.data.get('is_enable')
            project = Project.objects.get(pk=project_id)
        except:
            return parameter_error_response()
        if is_enable not in [0, 1]:
            return parameter_error_response('启用禁用参数错误')
        project.is_enable = is_enable
        project.save()
        return success_response()

    @swagger_auto_schema(
        operation_id='编辑项目-关联项目用户列表',
        operation_summary='编辑项目-关联项目用户列表',
        manual_parameters=[
            openapi.Parameter('page', openapi.IN_QUERY, description='第几页', type=openapi.TYPE_NUMBER),
            openapi.Parameter('page_size', openapi.IN_QUERY, description='每页显示条数默认15条', type=openapi.TYPE_NUMBER),
            openapi.Parameter('keyword', openapi.IN_QUERY, description='关键字搜索（姓名）', type=openapi.TYPE_STRING),
            openapi.Parameter('role', openapi.IN_QUERY, description='角色类型(编辑项目时传入) 3-项目运营 5-企业管理员 6-客户经理', type=openapi.TYPE_NUMBER),
            openapi.Parameter('project_id', openapi.IN_QUERY, description='项目id(编辑项目时传入)', type=openapi.TYPE_NUMBER),
            openapi.Parameter('query_project_id', openapi.IN_QUERY, description='项目id(只查询该项目下后台用户信息)', type=openapi.TYPE_NUMBER)
        ],
        tags=['项目相关']
    )
    @action(methods=['get'], detail=False, url_path='project_consultant_list')
    def project_consultant_list(self, request, *args, **kwargs):
        self.serializer_class = UserBackendListSerializer
        project_id = request.query_params.get('project_id')
        query_project_id = request.query_params.get('query_project_id')
        role = int(request.query_params.get('role', 3))

        if role == PROJECT_OPERATION:
            role_name = '项目运营'
        elif role == PROJECT_COMPANY_ADMIN:
            role_name = '企业管理员'
        elif role == PROJECT_ACCOUNT_MANAGER:
            role_name = '客户顾问'
        else:
            return parameter_error_response("暂不支持该角色查询")
        user_backends = UserBackend.objects.filter(
            deleted=False,
            role__name=role_name).order_by('-created_at')

        if query_project_id:
            user_backends = user_backends.filter(project_id=query_project_id)

        # 如果是企业管理员，则只匹配企业内数据
        if role == PROJECT_COMPANY_ADMIN:
            project = Project.objects.filter(id=project_id, deleted=False).first()
            if not project:
                return parameter_error_response('查询企业管理员需要正确的项目id')
            user_backends = user_backends.filter(
                Q(company=project.company) |
                Q(project=project)
            )
        keyword = request.query_params.get('keyword', None)
        if keyword:
            user_backends = user_backends.filter(
                Q(user__name__icontains=keyword) |
                Q(user__true_name__icontains=keyword))
        user_backends = distinct(user_backends, ['user_id'])
        paginator = StandardResultsSetPagination()
        page_list = paginator.paginate_queryset(user_backends, self.request)
        serializer = self.get_serializer(page_list, many=True)
        response = paginator.get_paginated_response(serializer.data)
        results = response.data['results']
        for info in results:
            if project_id and info['project_id'] == int(project_id):
                info['is_set'] = 1
            else:
                info['is_set'] = 0
        response.data['results'] = results
        return success_response(response)


    @swagger_auto_schema(
        operation_id='删除项目',
        operation_summary='删除项目',
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'project_id': openapi.Schema(description='项目id', type=openapi.TYPE_NUMBER),
            },
        ),
        tags=['项目相关']

    )
    @action(methods=['post'], detail=False, url_path='delete_project')
    def delete_project(self, request, *args, **kwargs):
        try:
            project_id = request.data.get('project_id')
            project = Project.objects.get(pk=project_id)
        except:
            return parameter_error_response()
        data = {'delete_data': []}

        mark_user_id_list = []
        with transaction.atomic():

            project.deleted = True
            project.save()
            data['delete_data'].append({'model': 'Project', 'id': [project.pk]})

            # 个人报告
            personal_report = PersonalReport.objects.filter(project_id=project_id, deleted=False)
            if personal_report.exists():
                personal_report_ids = list(personal_report.values_list('id', flat=True))
                personal_report.update(deleted=True)
                data['delete_data'].append({'model': 'PersonalReport', 'id': personal_report_ids})

            # 被教练者
            project_member = ProjectMember.objects.filter(project_id=project_id, deleted=False)
            if project_member.exists():
                project_member_ids = list(project_member.values_list('id', flat=True))
                coachee_user_ids = list(project_member.values_list('user_id', flat=True))
                mark_user_id_list += coachee_user_ids
                project_member.update(deleted=True)
                data['delete_data'].append({'model': 'ProjectMember', 'id': project_member_ids})

                # 改变观察
                change_observations = ChangeObservation.objects.filter(project_member_id__in=project_member_ids,
                                                                       deleted=False)
                if change_observations.exists():
                    change_observation_ids = list(change_observations.values_list('id', flat=True))
                    change_observations.update(deleted=True)
                    data['delete_data'].append({'model': 'ChangeObservation', 'id': change_observation_ids})

                    # 改变观察反馈成长目标关系表
                    growth_goals2change_observation = GrowthGoals2ChangeObservation.objects.filter(
                        change_observation_id__in=change_observation_ids, deleted=False)
                    if growth_goals2change_observation.exists():
                        growth_goals2change_observation_ids = list(growth_goals2change_observation.values_list(
                            'id', flat=True))
                        growth_goals2change_observation.update(deleted=True)
                        data['delete_data'].append({'model': 'GrowthGoals2ChangeObservation',
                                                    'id': growth_goals2change_observation_ids})

                    # 多类型对象关系表
                    multiple_association_relation = MultipleAssociationRelation.objects.filter(
                        main_id__in=change_observation_ids, deleted=False,
                        type=MultipleAssociationRelationTypeEnum.stakeholder_change_observation.value)
                    if multiple_association_relation.exists():
                        multiple_association_relation_ids = list(multiple_association_relation.values_list(
                            'id', flat=True))
                        multiple_association_relation.update(deleted=True)
                        data['delete_data'].append({'model': 'MultipleAssociationRelation',
                                                    'id': multiple_association_relation_ids})

            # 利益相关者
            project_interested = ProjectInterested.objects.filter(project_id=project_id, deleted=False)
            if project_interested.exists():
                project_interested_ids = list(project_interested.values_list('id', flat=True))
                project_interested.update(deleted=True)
                data['delete_data'].append({'model': 'ProjectInterested', 'id': project_interested_ids})

            # 项目匹配教练
            project_coach = ProjectCoach.objects.filter(project_id=project_id, deleted=False)
            if project_coach.exists():
                project_coach_ids = list(project_coach.values_list('id', flat=True))
                coach_user_ids = list(project_coach.values_list('coach__user_id', flat=True))
                mark_user_id_list += coach_user_ids
                project_coach.update(deleted=True)
                data['delete_data'].append({'model': 'ProjectCoach', 'id': project_coach_ids})

            # 客户服务内容
            project_bundle = ProjectBundle.objects.filter(project_id=project_id, deleted=False)
            if project_bundle.exists():
                project_bundle_ids = list(project_bundle.values_list('id', flat=True))
                project_bundle.update(deleted=True)
                data['delete_data'].append({'model': 'ProjectBundle', 'id': project_bundle_ids})

            # 一对一辅导
            one_to_one = OneToOneCoach.objects.filter(project_bundle__project_id=project_id, deleted=False)
            if one_to_one.exists():
                one_to_one_ids = list(one_to_one.values_list('id', flat=True))
                one_to_one.update(deleted=True)
                data['delete_data'].append({'model': 'OneToOneCoach', 'id': one_to_one_ids})

            # 一对一辅导建议
            guidance_suggestion = GuidanceSuggestion.objects.filter(
                one_to_one_coach__project_bundle__project_id=project_id, deleted=False)
            if guidance_suggestion.exists():
                guidance_suggestion_ids = list(guidance_suggestion.values_list('id', flat=True))
                guidance_suggestion.update(deleted=True)
                data['delete_data'].append({'model': 'GuidanceSuggestion', 'id': guidance_suggestion_ids})

            # 集体辅导
            group_coach = GroupCoach.objects.filter(project_bundle__project_id=project_id, deleted=False)
            if group_coach.exists():
                group_coach_ids = list(group_coach.values_list('id', flat=True))
                group_coach.update(deleted=True)
                data['delete_data'].append({'model': 'GroupCoach', 'id': group_coach_ids})

            # 集体辅导能力标签
            power_tag = PowerTag.objects.filter(group_coach__project_bundle__project_id=project_id, deleted=False)
            if power_tag.exists():
                power_tag_ids = list(power_tag.values_list('id', flat=True))
                power_tag.update(deleted=True)
                data['delete_data'].append({'model': 'PowerTag', 'id': power_tag_ids})

            # 教练任务
            coach_task = CoachTask.objects.filter(project_bundle__project_id=project_id, deleted=False)
            if coach_task.exists():
                coach_task_ids = list(coach_task.values_list('id', flat=True))
                coach_task.update(deleted=True)
                data['delete_data'].append({'model': 'CoachTask', 'id': coach_task_ids})

            # 测评模块
            evaluation_module = EvaluationModule.objects.filter(project_bundle__project_id=project_id, deleted=False)
            if evaluation_module.exists():
                evaluation_module_ids = list(evaluation_module.values_list('id', flat=True))
                evaluation_module.update(deleted=True)
                data['delete_data'].append({'model': 'EvaluationModule', 'id': evaluation_module_ids})

            # 文章模块
            article_module = ArticleModule.objects.filter(project_bundle__project_id=project_id, deleted=False)
            if article_module.exists():
                article_module_ids = list(article_module.values_list('id', flat=True))
                article_module.update(deleted=True)
                data['delete_data'].append({'model': 'ArticleModule', 'id': article_module_ids})

            # 项目线下集体辅导
            project_group_coach = ProjectGroupCoach.objects.filter(project_id=project_id, deleted=False)
            if project_group_coach.exists():
                project_group_coach_ids = list(project_group_coach.values_list('id', flat=True))
                project_group_coach.update(deleted=True)
                data['delete_data'].append({'model': 'ProjectGroupCoach', 'id': project_group_coach_ids})

            # 项目资料
            project_docs = ProjectDocs.objects.filter(project_id=project_id, deleted=False)
            if project_docs.exists():
                project_docs_ids = list(project_docs.values_list('id', flat=True))
                project_docs.update(deleted=True)
                data['delete_data'].append({'model': 'ProjectDocs', 'id': project_docs_ids})

            # 辅导 - 日程
            project_interview = ProjectInterview.objects.filter(deleted=False, public_attr__project_id=project_id)
            if project_interview.exists():
                project_interview_ids = list(project_interview.values_list('id', flat=True))
                project_interview.update(deleted=True)
                data['delete_data'].append({'model': 'ProjectInterview', 'id': project_interview_ids})

                schedule_obj = {"model": "Schedule", "id": []}
                for interview in ProjectInterview.objects.filter(id__in=project_interview_ids,
                                                                 public_attr__start_time__lt=datetime.datetime.now()):
                    schedule = Schedule.objects.filter(public_attr=interview.public_attr).first()
                    if schedule:
                        schedule.deleted = True
                        schedule.save()
                        schedule_obj['id'].append(schedule.pk)

                data['delete_data'].append(schedule_obj)

            # 辅导记录
            project_interview_record = ProjectInterviewRecord.objects.filter(
                deleted=False, interview__public_attr__project_id=project_id)
            if project_interview_record.exists():
                project_interview_record_ids = list(project_interview_record.values_list('id', flat=True))
                project_interview_record.update(deleted=True)
                data['delete_data'].append({'model': 'ProjectInterviewRecord', 'id': project_interview_record_ids})

            # 行动计划
            action_plan = ActionPlan.objects.filter(public_attr__project_id=project_id, deleted=False)
            if action_plan.exists():
                action_plan_ids = list(action_plan.values_list('id', flat=True))
                action_plan.update(deleted=True)
                data['delete_data'].append({'model': 'ActionPlan', 'id': action_plan_ids})

            # 习惯养成
            habit = Habit.objects.filter(public_attr__project_id=project_id, deleted=False)
            if habit.exists():
                habit_ids = list(habit.values_list('id', flat=True))
                habit.update(deleted=True)
                data['delete_data'].append({'model': 'Habit', 'id': habit_ids})

            # 成长笔记
            diary = Diary.objects.filter(public_attr__project_id=project_id, deleted=False)
            if diary.exists():
                diary_ids = list(diary.values_list('id', flat=True))
                diary.update(deleted=True)
                data['delete_data'].append({'model': 'Diary', 'id': diary_ids})

            # 项目笔记
            project_note = ProjectNote.objects.filter(public_attr__project_id=project_id, deleted=False)
            if project_note.exists():
                project_note_ids = list(project_note.values_list('id', flat=True))
                project_note.update(deleted=True)
                data['delete_data'].append({'model': 'ProjectNote', 'id': project_note_ids})

            # 项目报告
            project_evaluation_report = ProjectEvaluationReport.objects.filter(project_id=project_id, deleted=False)
            if project_evaluation_report.exists():
                project_evaluation_report_ids = list(project_evaluation_report.values_list('id', flat=True))
                project_evaluation_report.update(deleted=True)
                data['delete_data'].append({'model': 'ProjectEvaluationReport', 'id': project_evaluation_report_ids})

            # 教练评价
            coach_appraise = CoachAppraise.objects.filter(interview__public_attr__project_id=project_id, deleted=False)
            if coach_appraise.exists():
                coach_appraise_ids = list(coach_appraise.values_list('id', flat=True))
                coach_appraise.update(deleted=True)
                data['delete_data'].append({'model': 'CoachAppraise', 'id': coach_appraise_ids})

            # 能力标签
            capacity_tag = CapacityTag.objects.filter(interview__public_attr__project_id=project_id, deleted=False)
            if capacity_tag.exists():
                capacity_tag_ids = list(capacity_tag.values_list('id', flat=True))
                capacity_tag.update(deleted=True)
                data['delete_data'].append({'model': 'CapacityTag', 'id': capacity_tag_ids})

            # 辅导记录线下
            interview_offline_record = ProjectInterviewOffLineRecord.objects.filter(
                interview__public_attr__project_id=project_id, deleted=False)
            if interview_offline_record.exists():
                interview_offline_record_ids = list(interview_offline_record.values_list('id', flat=True))
                interview_offline_record.update(deleted=True)
                data['delete_data'].append({'model': 'ProjectInterviewOffLineRecord', 'id': interview_offline_record_ids})

            # 成长目标
            growth_goal = GrowthGoals.objects.filter(public_attr__project_id=project_id, deleted=False)
            if growth_goal.exists():
                growth_goal_ids = list(growth_goal.values_list('id', flat=True))
                growth_goal.update(deleted=True)
                data['delete_data'].append({'model': 'GrowthGoals', 'id': growth_goal_ids})

            # 学习文章
            learn_article = LearnArticle.objects.filter(public_attr__project_id=project_id, deleted=False)
            if learn_article.exists():
                learn_article_ids = list(learn_article.values_list('id', flat=True))
                learn_article.update(deleted=True)
                data['delete_data'].append({'model': 'LearnArticle', 'id': learn_article_ids})

            # 测评报告
            evaluation_report = EvaluationReport.objects.filter(deleted=False, public_attr__project_id=project_id)
            if evaluation_report.exists():
                evaluation_report_ids = list(evaluation_report.values_list('id', flat=True))
                evaluation_report.update(deleted=True)
                data['delete_data'].append({'model': 'EvaluationReport', 'id': evaluation_report_ids})

            # 测评回答得分
            evalution_report_score = EvaluationReportScore.objects.filter(deleted=False,
                                                                          report__public_attr__project_id=project_id)
            if evalution_report_score.exists():
                evalution_report_score_ids = list(evalution_report_score.values_list('id', flat=True))
                evalution_report_score.update(deleted=True)
                data['delete_data'].append({'model': 'EvaluationReportScore', 'id': evalution_report_score_ids})

            # 移除项目时更新项目用户token将禁止登录
            user_public.mark_user_as_terminated(list(set(mark_user_id_list)))

            project.history_data = data
            project.save()
        return success_response()

    @swagger_auto_schema(
        operation_id='获取项目可发送通知的用户信息',
        operation_summary='获取项目可发送通知的用户信息',
        manual_parameters=[
            openapi.Parameter(
                'project_id', openapi.IN_QUERY, description='', type=openapi.TYPE_NUMBER, required=True),
            openapi.Parameter('template_type', openapi.IN_QUERY, description='发送模板类型 1-账号开通通知 2-改变观察问卷 3-利益相关者调研问卷 '
                                                                             '4-教练简历发送通知 5-企业管理员账号开通通知, 6-化学面谈预约提醒-客户，'
                                                                             '7-化学面谈反馈提醒-客户 8-化学面谈可预约日程设置提醒-教练',
                              type=openapi.TYPE_NUMBER),
            openapi.Parameter('channel_type', openapi.IN_QUERY, description='发送渠道 1-企业微信 2-短信 3-邮件',
                              type=openapi.TYPE_NUMBER),
            openapi.Parameter('name', openapi.IN_QUERY, description='真实姓名', type=openapi.TYPE_NUMBER),
            openapi.Parameter('page', openapi.IN_QUERY, description='第几页', type=openapi.TYPE_NUMBER),
            openapi.Parameter('page_size', openapi.IN_QUERY, description='每页显示条数默认15条', type=openapi.TYPE_NUMBER)
        ],
        tags=['项目相关']
    )
    @action(methods=['get'], detail=False, url_path='user_info/notice')
    def get_project_notice_interested_data(self, request, *args, **kwargs):

        try:
            project_id = request.query_params['project_id']
            name = request.query_params.get('name')
            template_type = int(request.query_params['template_type'])
            channel_type = int(request.query_params['channel_type'])
            project = Project.objects.get(id=project_id)
        except KeyError:
            return parameter_error_response()
        except Project.DoesNotExist:
            return parameter_error_response('项目数据错误')
        if channel_type == constant.ADMIN_SEND_EMAIL and template_type in [
            NoticeTemplateTypeEnum.change_observation, NoticeTemplateTypeEnum.stakeholder,
            NoticeTemplateTypeEnum.chemical_interview_appointment, NoticeTemplateTypeEnum.chemical_interview_feedback,
            NoticeTemplateTypeEnum.stakeholder_interview_reservation,
            NoticeTemplateTypeEnum.coachee_invite_stakeholder_interview_reservation,
            NoticeTemplateTypeEnum.change_observation_customer.value,
            NoticeTemplateTypeEnum.customer_infomation_collection.value
        ]:
            if not project.manager_list:
                return parameter_error_response('因邮件中需要项目运营信息，请配置项目运营再发送邮件')
        data = []
        # 化学面谈预约提醒, 化学面谈反馈提醒
        if template_type in [NoticeTemplateTypeEnum.chemical_interview_appointment,
                             NoticeTemplateTypeEnum.chemical_interview_feedback]:
            queryset = ProjectMember.objects.filter(
                project=project, project__deleted=False, deleted=False,
                chemical_interview__deleted=False).order_by('-created_at').distinct()
            if name:
                queryset = queryset.filter(user__true_name__icontains=name)

            for item in queryset.all():
                coachee_data = project_member_public.get_project_member_coachee_data(item.user)
                coachee_data['id'] = item.id
                coachee_data['is_send_notice'] = UserNoticeRecord.objects.filter(
                        project=project, user=item.user, type=template_type,
                        channel=channel_type, deleted=False).exists()
                data.append(coachee_data)
        # 化学面谈日程设置提醒
        if template_type == NoticeTemplateTypeEnum.chemical_interview_schedule:
            queryset = ProjectCoach.objects.filter(
                deleted=False, project=project,
                coach__deleted=False, resume__isnull=False).order_by('-coach__created_at')
            if name:
                queryset = queryset.filter(coach__user__true_name__icontains=name)
            for item in queryset.all():
                # 获取教练信息
                # 查询该教练的化学面谈配置
                
                coach_data = project_member_public.get_project_member_coach_data(item.coach.user, project_id)
                coach_data['id'] = item.pk
                coach_data['is_send_notice'] = UserNoticeRecord.objects.filter(
                        project=project, user=item.coach.user, type=template_type,
                        channel=channel_type, deleted=False).exists()
                coach_data['pending_interview_appointment'] = chemical_interview_public.pending_interview_appointment(item.coach, project_id)
                data.append(coach_data)

        # 企业管理员账号开通通知
        if template_type in [NoticeTemplateTypeEnum.company_manage_create_account.value]:
            queryset = UserBackend.objects.filter(user_type=UserBackendTypeEnum.company_manager,
                                                  deleted=False).order_by('-created_at')
            if name:
                queryset = queryset.filter(user__true_name__icontains=name)
            for item in queryset.all():
                data.append({
                    "id": item.id,
                    "true_name": item.user.cover_name,
                    "company_name": item.company.real_name,
                    "email": item.user.email,
                    "phone": item.user.phone,
                    "is_send_notice": item.is_send_email
                })

        if template_type == NoticeTemplateTypeEnum.customer_infomation_collection.value:
            queryset = UserBackend.objects.filter(role_id=constant.PROJECT_COMPANY_ADMIN,
                                                  project_id=project_id,
                                                  deleted=False).order_by('-created_at')
            if name:
                queryset = queryset.filter(user__true_name__icontains=name)
            for item in queryset.all():
                data.append({
                    "id": item.id,
                    "true_name": item.user.cover_name,
                    "email": item.user.email
                })

        # 被教练者账号开通通知
        if template_type == NoticeTemplateTypeEnum.create_account:
            queryset = ProjectMember.objects.filter(
                project_id=project_id,
                deleted=False)

            if name:
                queryset = queryset.filter(user__true_name__icontains=name)

            for item in queryset.all():

                if channel_type == NoticeChannelTypeEnum.email:
                    is_send_notice = item.is_send_email
                else:
                    is_send_notice = False

                wx_user = WorkWechatUser.objects.filter(
                    user_id=item.user.id, deleted=False).first()
                external_user_id = wx_user.external_user_id if wx_user else None
                data.append({
                    'id': item.id,
                    'interested_name': None,
                    'master_name': item.user.cover_name,
                    'relation': None,
                    'email': item.user.email,
                    'external_user_id': external_user_id,
                    'phone': item.user.phone,
                    'is_send_notice': is_send_notice,

                })
        # 改变观察问卷
        if template_type == NoticeTemplateTypeEnum.change_observation:
            queryset = ProjectInterested.objects.filter(project_id=project_id, deleted=False)

            if name:
                queryset = queryset.filter(
                    Q(interested__true_name__icontains=name) | Q(master__true_name__icontains=name)
                )
            change_observation = ChangeObservation.objects.filter(
                project_member__project_id=project_id,
                deleted=False,
                is_complete=False
            )

            relation = MultipleAssociationRelation.objects.filter(
                deleted=False,
                main_id__in=list(change_observation.values_list('id', flat=True)),
                type=MultipleAssociationRelationTypeEnum.stakeholder_change_observation
            )

            for item in relation:
                interested = queryset.filter(
                    id=item.secondary_id,
                    deleted=False,
                ).first()
                # 是否已经回答关联的改变观察反馈问题
                if interested:
                    answer = ChangeObservationAnswer.objects.filter(
                        change_observation=item.main_id,
                        public_attr__user_id=interested.interested.id
                    )

                    # 没有回答则可发送
                    if not answer.exists():
                        interested_data = project_member_public.get_project_member_stakeholder_data(interested)

                        if channel_type == NoticeChannelTypeEnum.sms:
                            is_send_notice = item.is_send_sms_notice
                        elif channel_type == NoticeChannelTypeEnum.email:
                            is_send_notice = item.is_send_email_notice
                        else:
                            is_send_notice = False

                        interested_data['is_send_notice'] = is_send_notice
                        interested_data['id'] = f'{interested.id}-{item.id}'
                        data.append(interested_data)

        # 利益相关者调研问卷
        if template_type == NoticeTemplateTypeEnum.stakeholder:
            queryset = ProjectInterested.objects.filter(project_id=project_id, deleted=False)

            if name:
                queryset = queryset.filter(
                    Q(interested__true_name__icontains=name) | Q(master__true_name__icontains=name)
                )
            coach_task = CoachTask.objects.filter(
                project_bundle__project_id=project_id,
                public_attr__user_id__isnull=False,
                deleted=False,
                stakeholder_submit_time__isnull=True,
                type=NewCoachTaskTypeEnum.stakeholder_research,
            )

            # 是否关联过改变观察反馈
            relations = MultipleAssociationRelation.objects.filter(
                deleted=False,
                public_attr__isnull=True,
                main_id__in=list(coach_task.values_list('id', flat=True)),
                type=MultipleAssociationRelationTypeEnum.stakeholder_coach_task
            )

            for item in relations:
                interested = queryset.filter(
                    id=item.secondary_id,
                    deleted=False,
                ).first()
                if interested:
                    interested_data = project_member_public.get_project_member_stakeholder_data(interested)

                    if channel_type == NoticeChannelTypeEnum.sms:
                        is_send_notice = item.is_send_sms_notice
                    elif channel_type == NoticeChannelTypeEnum.email:
                        is_send_notice = item.is_send_email_notice
                    else:
                        is_send_notice = False

                    interested_data['is_send_notice'] = is_send_notice
                    interested_data['id'] = f'{interested.id}-{item.id}'
                    data.append(interested_data)

        # 利益相关者访谈预约提醒
        if template_type == NoticeTemplateTypeEnum.stakeholder_interview_reservation:
            stakeholder_interview = StakeholderInterview.objects.filter(
                deleted=False, project_interested__isnull=False,
                stakeholder_interview_module__coach_task__public_attr__user_id__isnull=False,
                stakeholder_interview_module__project_member__project_id=project_id,
            )
            if name:
                stakeholder_interview = stakeholder_interview.filter(
                    Q(project_interested__interested__true_name__icontains=name) |
                    Q(project_interested__master__true_name__icontains=name)
                )
            data = []
            for item in stakeholder_interview:
                interested_data = project_member_public.get_project_member_stakeholder_data(item.project_interested)
                is_send_notice = UserNoticeRecord.objects.filter(
                        project_id=project_id, user=item.project_interested.interested, type=template_type,
                        channel=channel_type, deleted=False).exists()
                interested_data['id'] = item.id
                interested_data['is_send_notice'] = is_send_notice
                data.append(interested_data)

        # 客户邀请利益相关者预约访谈提醒
        if template_type == NoticeTemplateTypeEnum.coachee_invite_stakeholder_interview_reservation:
            stakeholder_interview = StakeholderInterviewModule.objects.filter(
                deleted=False, project_member__isnull=False, coach_task__public_attr__user_id__isnull=False,
                project_member__project_id=project_id,
            )
            if name:
                stakeholder_interview = stakeholder_interview.filter(
                    project_member__user__true_name__icontains=name
                )
            data = []
            for item in stakeholder_interview.all():
                coachee_data = project_member_public.get_project_member_coachee_data(item.project_member.user)
                coachee_data['id'] = item.id
                coachee_data['is_send_notice'] = UserNoticeRecord.objects.filter(
                    project=project, user=item.project_member.user, type=template_type,
                    channel=channel_type, deleted=False).exists()
                data.append(coachee_data)

        # 利益相关者访谈报告填写提醒
        if template_type == NoticeTemplateTypeEnum.stakeholder_interview_fill_in_report:
            # 查询利益相关者配置
            stakeholder_interview = StakeholderInterviewModule.objects.filter(
                deleted=False, coach_task__public_attr__user_id__isnull=False,
                project_member__project_id=project_id,
            )
            if name:
                stakeholder_interview = stakeholder_interview.filter(
                    coach_task__public_attr__user__true_name__icontains=name)
            data = []
            for item in stakeholder_interview:
                # 获取教练的用户对象
                coach_user = item.coach_task.public_attr.user
                # 获取教练信息
                coach_data = project_member_public.get_project_member_coach_data(coach_user, project_id)
                coach_data['id'] = item.pk
                coach_data['master_name'] = item.coach_task.public_attr.target_user.cover_name
                coach_data['is_send_notice'] = UserNoticeRecord.objects.filter(
                        project_id=project_id, user=coach_user, type=template_type,
                        channel=channel_type, deleted=False).exists()
                data.append(coach_data)

        # 利益相关者访谈可预约日程设置提醒
        if template_type == NoticeTemplateTypeEnum.stakeholder_interview_schedule:
            # 查询利益相关者配置
            stakeholder_interview = StakeholderInterviewModule.objects.filter(
                deleted=False, coach_task__public_attr__user_id__isnull=False,
                project_member__project_id=project_id,
            )
            if name:
                stakeholder_interview = stakeholder_interview.filter(
                    coach_task__public_attr__user__true_name__icontains=name)
            data = []
            for item in stakeholder_interview:
                # 获取教练的用户对象
                coach_user = item.coach_task.public_attr.user
                # 获取教练信息
                coach_data = project_member_public.get_project_member_coach_data(coach_user, project_id)
                coach_data['id'] = item.pk
                coach_data['is_send_notice'] = UserNoticeRecord.objects.filter(
                        project_id=project_id, user=coach_user, type=template_type,
                        channel=channel_type, deleted=False).exists()
                data.append(coach_data)

        if template_type == NoticeTemplateTypeEnum.change_observation_customer.value:
            # 查询利益相关者配置
            change_observation = ChangeObservation.objects.filter(
                project_member__project_id=project_id, deleted=False, is_complete=False,
                invite_type=ChangeObservationInviteTypeEnum.customer.value,
            )
            if name:
                change_observation = change_observation.filter(
                    project_member__user__true_name__icontains=name)
            data = []
            for item in change_observation:
                coachee_data = project_member_public.get_project_member_coachee_data(item.project_member.user)
                coachee_data['id'] = item.id
                coachee_data['is_send_notice'] = UserNoticeRecord.objects.filter(
                    project=project, user=item.project_member.user, type=template_type,
                    channel=channel_type, deleted=False).exists()
                data.append(coachee_data)
        paginator = StandardResultsSetPagination()
        page_list = paginator.paginate_queryset(data, self.request)
        response = paginator.get_paginated_response(page_list)
        return success_response(response)


    @swagger_auto_schema(
        operation_id='项目匹配教练',
        operation_summary='项目匹配教练',
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'coach': openapi.Schema(description='教练简历i信息', type=openapi.TYPE_ARRAY, items=openapi.TYPE_NUMBER),
                'project_id': openapi.Schema(description='项目id', type=openapi.TYPE_ARRAY, items=openapi.TYPE_NUMBER),
            },
        ),
        tags=['项目相关']

    )
    @action(methods=['post'], detail=False, url_path='bind_coach')
    def post_bind_coach(self, request, *args, **kwargs):
        try:
            coach = request.data.get('coach')
            project_id = request.data.get('project_id')
            if not isinstance(coach, list):
                return parameter_error_response('教练信息参数格式错误')
            Project.objects.get(pk=project_id, deleted=False)
        except Project.DoesNotExist:
            return parameter_error_response('')

        except Exception:
            return parameter_error_response('参数错误')

        with transaction.atomic():
            for item in coach:
                user_id = item.get('user_id')
                resume_ids = item.get('resume_ids')
                if not resume_ids:
                    raise WisdomValidationError("未获取到需要修改的简历列表")

                for resume_id in resume_ids:
                    if not isinstance(resume_id, int):
                        raise WisdomValidationError("简历列表的id需要数字类型")

                coach = Coach.objects.filter(deleted=False, user_id=user_id).first()
                if not coach:
                    raise WisdomValidationError("教练信息错误")

                # 校验简历信息
                resume = Resume.objects.filter(id__in=resume_ids, deleted=False)
                if len(resume_ids) != resume.count():
                    raise WisdomValidationError("简历数量错误，请核查")
                if len(resume_ids) != resume.filter(coach__user_id=user_id).count():
                    raise WisdomValidationError("简历与教练不匹配，请核查")

                projcet_coach = ProjectCoach.objects.filter(
                    project_id=project_id,
                    status=ProjectCoachStatusEnum.adopt,
                    coach_id=coach.id,
                    member__isnull=True,
                    project_group_coach__isnull=True,
                    deleted=False
                ).first()
                # 如果项目和教练已绑定，更新项目教练的简历信息。
                if projcet_coach:
                    projcet_coach.resume = resume_ids
                    projcet_coach.save()
                else:
                    # 项目关联教练
                    state, content = project_coach_public.project_add_coach(project_id, coach.id, resume_ids)
                    if not state:
                        raise WisdomValidationError(content)
                    # 用户token将禁止登录
                    user_public.mark_user_as_terminated([coach.user_id])
        return success_response()

    @swagger_auto_schema(
        operation_id='移除项目匹配的教练',
        operation_summary='移除项目匹配的教练',
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'resume_ids': openapi.Schema(description='简历id', type=openapi.TYPE_ARRAY, items=openapi.TYPE_NUMBER),
                'project_id': openapi.Schema(description='项目id', type=openapi.TYPE_ARRAY, items=openapi.TYPE_NUMBER),
            },
        ),
        tags=['项目相关']

    )
    @action(methods=['post'], detail=False, url_path='remove_coach')
    def post_remove_coach(self, request, *args, **kwargs):

        try:
            resume_ids = request.data.get('resume_ids')
            project_id = request.data.get('project_id')
            if not isinstance(resume_ids, list):
                return parameter_error_response('教练信息参数格式错误')
        except Exception as e:
            return parameter_error_response()

        with transaction.atomic():

            coach_ids = list(Resume.objects.filter(id__in=resume_ids, deleted=False).values_list('coach_id', flat=True))
            if not coach_ids:
                raise WisdomValidationError("未获取到简历对应教练信息")
            if len(set(coach_ids)) > 1:
                raise WisdomValidationError("简历不是来同一个教练")
            coach_id = coach_ids[0]

            project_coach = ProjectCoach.objects.filter(
                project_id=project_id,
                status=ProjectCoachStatusEnum.adopt,
                coach_id=coach_id,
                member__isnull=True,
                deleted=False,
                project_group_coach__isnull=True,
            ).first()
            if not project_coach:
                raise WisdomValidationError("该教练暂未绑定项目")

            new_resume = project_coach.resume
            for item in resume_ids:
                new_resume.remove(int(item))
            # 如果还有值就更新，没有则删除。
            if new_resume:
                project_coach.resume = new_resume
                # 如果展示的定制化简历删除了，就展示主简历，主简历默认第一个
                if project_coach.show_resume_id not in new_resume:
                    project_coach.show_resume_id = new_resume[0]
            else:
                # 删除前看一下用户有没有匹配过该教练
                if ProjectCoach.objects.filter(
                    project_id=project_id, status=ProjectCoachStatusEnum.adopt,
                    coach_id=coach_id, member__isnull=False, deleted=False,
                ).exists():
                    raise WisdomValidationError("教练在项目中已服务客户不能移除")
                project_coach.deleted = True
                # 用户token将禁止登录
                user_public.mark_user_as_terminated([project_coach.coach.user_id])

            project_coach.save()
            if project_coach.source_type == CoachSourceTypeEnum.offer:  # 删除offer进来的教练将教练offer状态修改为未通过企业面试
                CoachOffer.objects.filter(coach=project_coach.coach,
                                          project_offer__project=project_coach.project,
                                          deleted=False).update(status=CoachOfferStatusEnum.company_rejected)
        return success_response()

    @swagger_auto_schema(
        operation_id='企业管理-项目进度',
        operation_summary='企业管理-项目进度',
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'project_id': openapi.Schema(description='项目id', type=openapi.TYPE_ARRAY, items=openapi.TYPE_NUMBER),
            },
        ),
        tags=['企业管理员-数据看板']

    )
    @action(methods=['get'], detail=False, url_path='project_progress')
    def project_progress(self, request, *args, **kwargs):
        try:
            project_id = request.query_params['project_id']
            project = Project.objects.get(id=project_id)
        except (KeyError, ValueError, Project.DoesNotExist):
            return parameter_error_response()
        progress_data = get_project_progress_data(project)
        return success_response(progress_data)

    @swagger_auto_schema(
        operation_id='企业管理-项目进度报告',
        operation_summary='企业管理-项目进度报告',
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'project_id': openapi.Schema(description='项目id', type=openapi.TYPE_ARRAY, items=openapi.TYPE_NUMBER),
            },
        ),
        tags=['企业管理员-数据看板']

    )
    @action(methods=['get'], detail=False, url_path='project_progress_report')
    def project_progress_report(self, request, *args, **kwargs):
        try:
            project_id = request.query_params['project_id']
            project = Project.objects.get(id=project_id)
            start_date = request.query_params['start_date']
            end_date = request.query_params['end_date']
        except (KeyError, ValueError, Project.DoesNotExist):
            return parameter_error_response()
        report_data = get_project_progress_report_data(project, start_date, end_date)
        return success_response(report_data)

    @swagger_auto_schema(
        operation_id='项目需求标签数据',
        operation_summary='项目需求标签数据',
        manual_parameters=[
            openapi.Parameter(
                'project_tag_group_id', openapi.IN_QUERY, description='项目需求标签标识', type=openapi.TYPE_NUMBER, required=True)
        ],
        tags=['项目标签']

    )
    @action(methods=['get'], detail=False, url_path='require_tag', authentication_classes=[])
    def project_require_tag(self, request, *args, **kwargs):
        try:
            project_tag_group_id = request.query_params.get('project_tag_group_id')
            if project_tag_group_id:
                project_tag_group = ProjectTagGroup.objects.get(id=project_tag_group_id, deleted=False)
            else:
                project_tag_group = None
        except ProjectTagGroup.DoesNotExist:
            return parameter_error_response('项目标签信息不存在')
        except (KeyError, ValueError):
            return parameter_error_response()

        # 获取项目标签集和全部基础tag信息
        if project_tag_group_id:
            project_tag_group_data = tag_public.get_project_tag_group_info(project_tag_group_id, is_matching_tag=False)
        else:
            project_tag_group_data = {}
        selected_tag_ids = project_tag_group_data.get('selected_tag_ids', [])
        mandatory_tag_ids = project_tag_group_data.get('mandatory_tag_ids', [])

        # 获取配置的标签
        all_tag = TagRequirementConfig.objects.filter(
            deleted=False, config_type=TagRequirementConfigTypeEnum.project_requirements.value).order_by('order').all()
        # 检查标签是否存在
        if not all_tag:
            return parameter_error_response('未获取到项目需求配置标签')

        # 获取所有当前标签+子标签
        all_sub_tags = tag_public.get_all_sub_tags(all_tag, selected_tag_ids, mandatory_tag_ids)

        data = {
                "id": str(project_tag_group.id) if project_tag_group else None,
                "title": project_tag_group.title if project_tag_group else None,
                "stakeholder_feedback_and_expectations": project_tag_group.stakeholder_feedback_and_expectations if project_tag_group else None,
                "project_member_name": project_tag_group.project_member.user.cover_name if project_tag_group and project_tag_group.project_member else None,
                "project_member_id": project_tag_group.project_member_id if project_tag_group and project_tag_group.project_member else None,
                "require_tag": all_sub_tags,
                "other_info": project_tag_group.other_info if project_tag_group else None,
                "other_info_from_company_background": project_tag_group.other_info_from_company_background if project_tag_group else None,
                "other_info_from_company_customer_info": project_tag_group.other_info_from_company_customer_info if project_tag_group else None,
                "other_info_from_company_coach_requirements": project_tag_group.other_info_from_company_coach_requirements if project_tag_group else None,
                "stakeholder_feedback_from_company_background": project_tag_group.stakeholder_feedback_from_company_background if project_tag_group else None,
                "stakeholder_feedback_from_company_customer_info": project_tag_group.stakeholder_feedback_from_company_customer_info if project_tag_group else None,
                "stakeholder_feedback_from_company_coach_requirements": project_tag_group.stakeholder_feedback_from_company_coach_requirements if project_tag_group else None,
            }
        return success_response(data)

    @swagger_auto_schema(
        operation_id='修改/创建项目需求标签数据',
        operation_summary='修改/创建项目需求标签数据',
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'project_tag_group_id': openapi.Schema(description='项目需求标签组id', type=openapi.TYPE_STRING),
                'project_id': openapi.Schema(description='项目id', type=openapi.TYPE_STRING),
                'title': openapi.Schema(description='标题', type=openapi.TYPE_STRING),
                'tag_content': openapi.Schema(description='标签内容', type=openapi.TYPE_STRING),
            },
        ),
        tags=['项目标签']
    )
    @action(methods=['post'], detail=False, url_path='require_tag/update', authentication_classes=[])
    def update_project_require_tag(self, request, *args, **kwargs):
        """
        更新或创建项目需求标签组及其配置。
        """
        try:
            # 获取请求数据
            project_id = request.data.get('project_id')
            title = request.data.get('title')
            project_member_id = request.data.get('project_member_id')
            tag_content = request.data.get('tag_content')
            project_tag_group_id = request.data.get('project_tag_group_id')
            stakeholder_feedback_and_expectations = request.data.get('stakeholder_feedback_and_expectations')
            other_info = request.data.get('other_info')
            # Validate request data
            if not all([project_id, title, tag_content]):
                return parameter_error_response('Missing required fields')

            # Additional company info fields
            other_info_from_company_background = request.data.get('other_info_from_company_background')
            other_info_from_company_customer_info = request.data.get('other_info_from_company_customer_info')
            other_info_from_company_coach_requirements = request.data.get('other_info_from_company_coach_requirements')
            stakeholder_feedback_from_company_background = request.data.get('stakeholder_feedback_from_company_background')
            stakeholder_feedback_from_company_customer_info = request.data.get('stakeholder_feedback_from_company_customer_info')
            stakeholder_feedback_from_company_coach_requirements = request.data.get('stakeholder_feedback_from_company_coach_requirements')

            # 验证项目和项目成员是否存在
            project = Project.objects.get(id=project_id, deleted=False)
            if project_member_id:
                ProjectMember.objects.get(id=project_member_id, deleted=False)
            if project_tag_group_id:
                project_tag_group = ProjectTagGroup.objects.get(id=project_tag_group_id, deleted=False)
            else:
                project_tag_group = None

        except ProjectTagGroup.DoesNotExist:
            return parameter_error_response('项目需求配置不存在')
        except Project.DoesNotExist:
            return parameter_error_response('项目信息不存在')
        except ProjectMember.DoesNotExist:
            return parameter_error_response('项目用户不存在')
        except (KeyError, ValueError):
            return parameter_error_response()

        # 使用数据库事务确保数据一致性
        with transaction.atomic():
            # 如果提供了 project_tag_group_id，表示更新现有的标签组
            if project_tag_group:
                project_tag_group.title = title
                project_tag_group.stakeholder_feedback_and_expectations = stakeholder_feedback_and_expectations
                project_tag_group.project_member_id = project_member_id
                project_tag_group.other_info = other_info
                # Update additional company info fields
                project_tag_group.other_info_from_company_background = other_info_from_company_background
                project_tag_group.other_info_from_company_customer_info = other_info_from_company_customer_info
                project_tag_group.other_info_from_company_coach_requirements = other_info_from_company_coach_requirements
                project_tag_group.stakeholder_feedback_from_company_background = stakeholder_feedback_from_company_background
                project_tag_group.stakeholder_feedback_from_company_customer_info = stakeholder_feedback_from_company_customer_info
                project_tag_group.stakeholder_feedback_from_company_coach_requirements = stakeholder_feedback_from_company_coach_requirements
                project_tag_group.save()

                project_tag_data = tag_public.get_project_tag_group_info(project_tag_group_id, is_matching_tag=False)
                selected_tag_ids = project_tag_data.get('selected_tag_ids')
                mandatory_tag_ids = project_tag_data.get('mandatory_tag_ids')
            else:
                # 否则，创建一个新的标签组
                project_tag_group = ProjectTagGroup.objects.create(
                    title=title, project_id=project_id, deleted=False, project_member_id=project_member_id,
                    stakeholder_feedback_and_expectations=stakeholder_feedback_and_expectations,
                    other_info=other_info,
                    other_info_from_company_background=other_info_from_company_background,
                    other_info_from_company_customer_info=other_info_from_company_customer_info,
                    other_info_from_company_coach_requirements=other_info_from_company_coach_requirements,
                    stakeholder_feedback_from_company_background=stakeholder_feedback_from_company_background,
                    stakeholder_feedback_from_company_customer_info=stakeholder_feedback_from_company_customer_info,
                    stakeholder_feedback_from_company_coach_requirements=stakeholder_feedback_from_company_coach_requirements
                )
                selected_tag_ids = []
                mandatory_tag_ids = []

            create_project_tag_config_list = []
            del_project_tag_config_id_list = selected_tag_ids.copy()

            # 解析 tag_content，更新或创建标签配置
            for tag in tag_content:
                is_required = tag.get('is_required', False)
                selected_ids = tag.get('selected_ids', [])
                for item_tag_id in selected_ids:
                    if item_tag_id in selected_tag_ids:
                        # 如果标签已存在，更新其必选状态
                        if (is_required and item_tag_id not in mandatory_tag_ids) or (
                                not is_required and item_tag_id in mandatory_tag_ids):
                            ProjectTagConfig.objects.filter(
                                project_tag_group_id=project_tag_group.id, deleted=False, tag_object__tag_id=item_tag_id
                            ).update(is_mandatory=is_required)
                        del_project_tag_config_id_list.remove(item_tag_id)
                    else:
                        # 创建新的标签对象和配置
                        tag_object = TagObject.objects.create(
                            object_type=TagObjectTypeEnum.project.value, object_id=project_id, tag_id=item_tag_id)
                        create_project_tag_config_list.append(
                            ProjectTagConfig(
                                tag_object_id=tag_object.id,
                                project_tag_group_id=project_tag_group.id,
                                is_mandatory=is_required
                            )
                        )

            # 批量创建新的标签配置
            if create_project_tag_config_list:
                ProjectTagConfig.objects.bulk_create(create_project_tag_config_list)

            # 删除不再需要的标签配置
            if del_project_tag_config_id_list:
                project_tag_config = ProjectTagConfig.objects.filter(
                    project_tag_group_id=project_tag_group.id, deleted=False,
                    tag_object__tag_id__in=del_project_tag_config_id_list
                ).values_list('id', flat=True)
                tag_public.delete_specific_tag_configs_and_objects(list(project_tag_config))

        if project.status == ProjectStatusEnum.completed.value:
            # 更新附加的教练关联标签（教练的项目标签）
            tag_public.update_project_coach_tag(project_id)

        # 更新教练标签匹配数据
        coach_content = tag_public.get_coach_content(project_tag_group.id)
        project_tag_group.coach_content = coach_content
        project_tag_group.save()

        # 返回更新或创建后的数据
        data = {
            "id": str(project_tag_group.id),
            "title": project_tag_group.title,
            "other_info": project_tag_group.other_info,
            "stakeholder_feedback_and_expectations": project_tag_group.stakeholder_feedback_and_expectations,
            "coach_content": project_tag_group.coach_content,
            "project_member_name": project_tag_group.project_member.user.cover_name if project_tag_group.project_member else None,
            "project_member_id": project_tag_group.project_member_id if project_tag_group.project_member else None,
            "require_tag": tag_public.get_project_tag_list(project_tag_group.id),
            "other_info_from_company_background": project_tag_group.other_info_from_company_background,
            "other_info_from_company_customer_info": project_tag_group.other_info_from_company_customer_info,
            "other_info_from_company_coach_requirements": project_tag_group.other_info_from_company_coach_requirements,
            "stakeholder_feedback_from_company_background": project_tag_group.stakeholder_feedback_from_company_background,
            "stakeholder_feedback_from_company_customer_info": project_tag_group.stakeholder_feedback_from_company_customer_info,
            "stakeholder_feedback_from_company_coach_requirements": project_tag_group.stakeholder_feedback_from_company_coach_requirements
        }
        return success_response(data)

    @swagger_auto_schema(
        operation_id='删除项目需求标签数据',
        operation_summary='删除项目需求标签数据',
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'project_tag_group_id': openapi.Schema(description='项目需求标签组id', type=openapi.TYPE_STRING)
            },
        ),
        tags=['项目标签']
    )
    @action(methods=['post'], detail=False, url_path='require_tag/del')
    def del_project_require_tag(self, request, *args, **kwargs):
        """
        删除项目需求标签数据。
        """
        try:
            # 获取请求参数中的项目需求标签组ID
            project_tag_group_id = request.data.get('project_tag_group_id')
            # 查找对应的项目需求标签组
            project_tag_group = ProjectTagGroup.objects.get(id=project_tag_group_id, deleted=False)
        except ProjectTagGroup.DoesNotExist:
            return parameter_error_response('项目标签信息不存在')
        except (KeyError, ValueError):
            return parameter_error_response()

        with transaction.atomic():
            # 查找并逻辑删除项目标签配置
            project_tag_configs = ProjectTagConfig.objects.filter(
                project_tag_group_id=project_tag_group_id, deleted=False
            ).values_list('id', flat=True)
            # 删除附属标签
            tag_public.delete_specific_tag_configs_and_objects(list(project_tag_configs))
            # 删除项目标签组
            project_tag_group.deleted = True
            project_tag_group.save()

        if project_tag_group.project.status == ProjectStatusEnum.completed.value:
            # 更新附加的教练关联标签（教练的项目标签）
            tag_public.update_project_coach_tag(project_tag_group.project_id)
        return success_response()

    @swagger_auto_schema(
        operation_id='新建项目需求标签offer',
        operation_summary='新建项目需求标签offer',
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'project_tag_group_id': openapi.Schema(description='项目需求标签组id', type=openapi.TYPE_STRING),
                'coach_index_list': openapi.Schema(description='选中的教练下标列表', type=openapi.TYPE_STRING)
            }
        ),
        tags=['后台项目offer']
    )
    @action(methods=['post'], detail=False, url_path='require_tag_offer/add')
    def add_require_tag_offer(self, request, *args, **kwargs):
        try:
            # 获取请求参数中的项目需求标签组ID
            project_tag_group_id = request.data.get('project_tag_group_id')
            coach_index_list = request.data.get('coach_index_list')
            # 查找对应的项目需求标签组
            project_tag_group = ProjectTagGroup.objects.get(id=project_tag_group_id, deleted=False)
        except ProjectTagGroup.DoesNotExist:
            return parameter_error_response('项目标签信息不存在')
        except (KeyError, ValueError):
            return parameter_error_response()

        if not coach_index_list:
            return parameter_error_response('未选中教练')

        coach_content = project_tag_group.coach_content

        coach_ids = coach_content.get('coach_ids', [])
        # 确保coach_index_list中的索引适用于coach_ids列表
        valid_coach_indices = [index for index in coach_index_list if index < len(coach_ids)]
        # 使用列表推导式根据索引列表从coach_ids中提取元素
        selected_coaches = [coach_ids[index] for index in valid_coach_indices]

        project_offer = ProjectOffer.objects.create(
            offer_name=f'需求配置-{project_tag_group.title}', project_id=project_tag_group.project_id)
        if selected_coaches:
            coach_list = [CoachOffer(project_offer=project_offer, coach_id=coach_id) for coach_id in selected_coaches]
            CoachOffer.objects.bulk_create(coach_list)

        content = coach_content['content']
        titles = coach_content['titles']
        # 是否新加“选择状态”到标题中
        is_add_status_title = False
        if titles[1] != '选择状态':
            titles.insert(1, '选择状态')
            is_add_status_title = True

        # 循环教练数据，根据教练数据下标和选中的valid_coach_indices教练数据下标进行匹配，控制是否选中
        for index, content_item in enumerate(content):
            if index in valid_coach_indices:
                choice_describe = '已选择'
            else:
                choice_describe = '未选择'

            # 如果标题新加“选择状态”，教练content新增选择状态描述，否则做更新操作
            if is_add_status_title:
                content_item.insert(1, choice_describe)
            else:
                content_item[1] = choice_describe
        # 按照choice_describe排序
        sorted_data = sorted(content, key=lambda x: x[1])
        coach_content['status'] = 2
        coach_content['content'] = sorted_data
        coach_content['offer_created_at'] = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        coach_content['titles'] = titles
        project_tag_group.coach_content = coach_content
        project_tag_group.save()
        return success_response()


    # 从用户通知数据获取对应的项目详情
    @swagger_auto_schema(
        operation_id='Get project details from user notification',
        operation_summary='Get project details from user notification',
        manual_parameters=[
            openapi.Parameter('notice_id', openapi.IN_QUERY, description='User notification ID', type=openapi.TYPE_NUMBER),
        ],
        tags=['Project Related']
    )
    @action(methods=['get'], detail=False, url_path='notice_project_detail', authentication_classes=[])
    def get_notice_project_detail(self, request, *args, **kwargs):
        """Get project details from user notification record"""
        try:
            notice_id = request.query_params.get('notice_id')
            if not notice_id:
                return parameter_error_response('Missing notice_id parameter')

            # Get project ID from user notification record
            notice = UserNoticeRecord.objects.filter(uuid=notice_id, deleted=False).first()
            if not notice or not notice.project_id:
                return parameter_error_response('Invalid notice or no associated project')

            # Get project details
            project = Project.objects.filter(id=notice.project_id, deleted=False).first()
            if not project:
                return parameter_error_response('Project not found')

            serializer = ProjectDetailsSerializers(project)
            return success_response(serializer.data)

        except Exception as e:
            print(f"Error getting project details from notice: {str(e)}")
            return parameter_error_response('Failed to get project details')
