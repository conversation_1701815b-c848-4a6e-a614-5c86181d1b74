from datetime import datetime

from drf_yasg import openapi
from rest_framework import viewsets, serializers
from drf_yasg.utils import swagger_auto_schema
from rest_framework.decorators import action

from static.contract_text import contract_text
from utils import task
from utils.pagination import StandardResultsSetPagination
from wisdom_v2.enum.service_content_enum import PdfReportTypeEnum
from wisdom_v2.enum.user_enum import PersonalApplyStatusEnum
from wisdom_v2.models import UserContract, Coach, PersonalApply, Resume
from utils.api_response import success_response, parameter_error_response, WisdomValidationError


class ContractViewSetSerializers(serializers.ModelSerializer):
    user_id = serializers.IntegerField(help_text='用户id')
    sign_url = serializers.CharField(help_text='签名合同链接')
    name = serializers.CharField(source='user.cover_name', help_text='用户名', read_only=True)
    phone = serializers.CharField(source='user.phone', help_text='手机号', read_only=True)
    email = serializers.Char<PERSON><PERSON>(source='user.email', help_text='邮箱', read_only=True)
    created_at = serializers.DateTimeField(help_text='创建时间', format='%Y-%m-%d %H:%M:%S')
    internship_empower_at = serializers.DateTimeField(help_text='见习教练授权时间',  format='%Y-%m-%d %H:%M:%S')
    personal_empower_at = serializers.DateTimeField(help_text='个人教练授权时间', format='%Y-%m-%d %H:%M:%S')
    user_class = serializers.SerializerMethodField(help_text='班次')
    coach_type = serializers.SerializerMethodField(help_text='教练类型')
    resume_id = serializers.SerializerMethodField(help_text='简历id')

    class Meta:
        model = UserContract
        fields = ['id', 'user_id', 'name', 'phone', 'email', 'user_class','internship_empower_at', 'personal_empower_at',
                  'coach_type', 'sign_url', 'contract_url','created_at', 'resume_id']

    def get_user_class(self, obj):
        coach = Coach.objects.filter(user_id=obj.user_id, deleted=False).first()
        if coach:
            return coach.user_class
    def get_coach_type(self, obj):
        coach = Coach.objects.filter(user_id=obj.user_id, deleted=False).first()
        if coach:
            return coach.coach_type

    def get_resume_id(self, obj):
        resume = Resume.objects.filter(coach__user_id=obj.user_id, is_customization=False, deleted=False).first()
        if resume:
            return resume.id

    def create(self, validated_data):
        if not validated_data.get('user_id'):
            raise WisdomValidationError('未获取到用户信息')
        if not validated_data.get('sign_url'):
            raise WisdomValidationError('未获取到合同信息')

        personal_apply = PersonalApply.objects.filter(
            coach__user_id=validated_data.get('user_id'),
            status=PersonalApplyStatusEnum.passed,
            deleted=False
        ).first()
        if not personal_apply:
            raise WisdomValidationError('未获取到用户已通过的教练申请记录')
        # 如果已有就更新，没有则新建。
        user_contract = UserContract.objects.filter(
            user_id=validated_data.get('user_id'), deleted=False).first()
        if user_contract:
            user_contract.personal_empower_at = datetime.now()
            user_contract.sign_url = validated_data.get('sign_url')
        else:
            validated_data['personal_empower_at'] = datetime.now()
            user_contract = UserContract(**validated_data)
        user_contract.save()
        task.get_user_pdf_url.delay(
            user_contract.id,
            f'{user_contract.user.cover_name}_签约合同_{user_contract.id.hex}.pdf',
            PdfReportTypeEnum.coach_contract.value, data_type='agreement')
        return user_contract


class ContractViewSet(viewsets.ModelViewSet):
    queryset = UserContract.objects.filter(deleted=False)
    serializer_class = ContractViewSetSerializers

    @swagger_auto_schema(
        operation_id='用户合同列表查询',
        operation_summary='用户合同列表查询',
        manual_parameters=[
            openapi.Parameter('name', openapi.IN_QUERY, description='用户名', type=openapi.TYPE_STRING),
            openapi.Parameter('phone', openapi.IN_QUERY, description='手机号', type=openapi.TYPE_STRING),
            openapi.Parameter('email', openapi.IN_QUERY, description='推荐人姓名', type=openapi.TYPE_STRING),
            openapi.Parameter('start_time', openapi.IN_QUERY, description='开始时间', type=openapi.TYPE_STRING),
            openapi.Parameter('end_time', openapi.IN_QUERY, description='结束时间', type=openapi.TYPE_STRING),
            openapi.Parameter('coach_type', openapi.IN_QUERY,
                              description='教练类型 1-见习教练 2-初级个人教练 3-初级教练 4-中级教练 5-高级教练 6-资深教练7-中级个人教练 8-高级个人教练',
                              type=openapi.TYPE_STRING)
        ],
        tags=['合同相关']
    )
    def list(self, request, *args, **kwargs):
        try:
            name = request.query_params.get('name')
            phone = request.query_params.get('phone')
            email = request.query_params.get('email')
            start_date = request.query_params.get('start_date')
            end_date = request.query_params.get('end_date')
            coach_type = request.query_params.get('coach_type')

            queryset = self.get_queryset()
            if name:
                queryset = queryset.filter(user__true_name__icontains=name)
            if phone:
                queryset = queryset.filter(user__phone__icontains=phone)
            if email:
                queryset = queryset.filter(user__email__icontains=email)
            if start_date:
                queryset = queryset.filter(created_at__date__gte=start_date)
            if end_date:
                queryset = queryset.filter(created_at__date__lte=end_date)
            if coach_type:
                coach_type = str(coach_type).split(',')
                queryset = queryset.filter(user__coach_user__coach_type__in=coach_type)

        except Exception as e:
            return parameter_error_response(str(e))
        # 分页
        paginator = StandardResultsSetPagination()
        page_list = paginator.paginate_queryset(queryset.order_by('-created_at'), self.request)
        serializer = self.get_serializer(page_list, many=True)
        response = paginator.get_paginated_response(serializer.data)
        return success_response(response)


    @swagger_auto_schema(
        operation_id='查看用户合同详情',
        operation_summary='查看用户合同详情',
        manual_parameters=[
            openapi.Parameter('user_contract_id', openapi.IN_QUERY, description='用户签名合同编号', type=openapi.TYPE_STRING),
        ],
        tags=['合同相关']
    )
    @action(methods=['get'], detail=False, url_path='detail', authentication_classes=[])
    def get_user_contract_detail(self, request, *args, **kwargs):
        try:
            user_contract_id = request.query_params.get('user_contract_id')
            user_contract = self.queryset.get(id=user_contract_id)
        except UserContract.DoesNotExist:
            return parameter_error_response('未查询到用户合同信息')
        except Exception as e:
            return parameter_error_response(str(e))
        serializer = self.get_serializer(user_contract)
        return success_response(serializer.data, business={'contract_text': contract_text})
