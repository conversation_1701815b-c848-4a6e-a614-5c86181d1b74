from decimal import Decimal
from itertools import chain

import pendulum
from django.conf import settings
from django.db.models import Sum, Q, F

from wisdom_v2.enum.service_content_enum import PersonalActivityTypeEnum, UserInviteTypeEnum
from wisdom_v2.enum.user_enum import UserTmpEnum, BrowseRecordObjectEnum
from wisdom_v2.models import Resume, UserInviteRecord, PersonalUser, UserTmp
from wisdom_v2.models_file import PersonalActivityInterview, PersonalActivity
from wisdom_v2.models_file.coach import BrowseRecord
from wisdom_v2.views.constant import ATTR_STATUS_INTERVIEW_CANCEL


def get_coach_share_pay_count(coach_user, is_set=None):
    """
    获取个人活动支付用户数
    :param coach_user: 教练用户，即教练的User模型实例
    :param is_set: 是否去重 (根据用户去重复)，如果为True，则统计的是不同用户的数量
    :return: int 返回与教练相关的个人活动中已支付的用户数量，如果is_set为True，则返回去重后的数量
    """
    # 筛选与教练相关的、未删除的个人活动访谈记录
    activity_interview = PersonalActivityInterview.objects.filter(
        interview__public_attr__user_id=coach_user,  # 访谈记录关联的是这位教练
        deleted=False,                               # 个人活动访谈记录未被删除
        interview__deleted=False,                    # 关联的访谈记录未被删除
    ).exclude(
        interview__public_attr__status=ATTR_STATUS_INTERVIEW_CANCEL  # 排除掉状态为“访谈取消”的记录
    )
    # 如果指定了去重
    if is_set:
        # 根据目标用户（即支付用户）进行去重
        activity_interview = activity_interview.values("interview__public_attr__target_user").distinct()
    # 返回计算的用户数
    return activity_interview.count()


def get_coach_share_user_look_list(coach_user):
    """
    获取教练用户分享的用户浏览列表
    :param coach_user: 教练用户 教练user对象
    :return: int 活动用户浏览列表
    """
    # 获取该教练用户所有未删除活动的user_look字段，每个字段是一个ID列表
    user_look_lists = PersonalActivity.objects.filter(
        coach__user=coach_user, deleted=False).values_list('look_all_user', flat=True)

    # 额外记录通过教练邀请进来的新的个人客户的user_id
    personal_user_ids = PersonalUser.objects.filter(
        invite__referrer_id=coach_user.id,
        invite__type=UserInviteTypeEnum.coach_share.value,
        deleted=False
    ).filter(
        Q(
            # 在Coach中，Coach创建时间 > PersonalUser创建时间，不在ProjectMember中
            user__coach_user__created_at__gt=F('created_at'),
            user__user_project__isnull=True
        ) |
        Q(
            # 在ProjectMember中，ProjectMember创建时间 > PersonalUser创建时间，不在Coach中
            user__user_project__created_at__gt=F('created_at'),
            user__coach_user__isnull=True
        ) |
        Q(
            # 不在Coach中，也不在ProjectMember中
            user__coach_user__isnull=True,
            user__user_project__isnull=True
        ) |
        Q(
            # 同时在ProjectMember和Coach中，且它们的创建时间都 > PersonalUser创建时间
            user__user_project__created_at__gt=F('created_at'),
            user__coach_user__created_at__gt=F('created_at')
        )).distinct().values_list('user__id', flat=True)

    # user_look_lists需要清理None元素，确保所有元素都是有效的
    cleaned_user_look_lists = filter(None, user_look_lists) if user_look_lists else []
    # 使用chain.from_iterable合并清理后的user_look_lists
    combined_ids = chain.from_iterable(cleaned_user_look_lists) if cleaned_user_look_lists else []

    # 将combined_ids和cleaned_personal_user_ids合并，并转换为集合去重
    unique_user_look_ids = set(chain(combined_ids, personal_user_ids))
    return unique_user_look_ids


def get_coach_share_all_look_count(coach_user):
    """
    获取教练用户分享的用户浏览列表
    :param coach_user: 教练用户 教练user对象
    :return: int 活动用户浏览列表
    """
    # 获取所有未删除的 PersonalActivity 相关的 look_all_count 的总和
    total_look_count = PersonalActivity.objects.filter(
        coach__user=coach_user,
        deleted=False
    ).aggregate(total_look_all_count=Sum('look_all_count'))

    # total_look_count 现在是一个字典，包含了 look_all_count 的总和
    return total_look_count.get('total_look_all_count') or 0


def add_personal_activity(coach, data):
    """
    根据提供的数据为用户添加个人活动
    :param coach: 教练Coach对象
    :param data: 创建活动所需数据
    :return: obj，error_msg  活动对象，错误描述
    """

    # 从数据中获取活动类型，如果没有提供，默认为教练分享类型
    activity_type = data.get('type', PersonalActivityTypeEnum.coach_share.value)
    # 如果活动类型是教练分享
    if activity_type in [PersonalActivityTypeEnum.coach_share.value, PersonalActivityTypeEnum.wechat_share.value]:
        # 获取相关的教练和简历信息，如果有多个教练或简历，默认取第一个
        resume = Resume.objects.filter(coach__user_id=coach.user_id, deleted=False, is_customization=False).first()

        # 获取价格，如果数据中没有提供，则使用教练的价格,没有教练金额，默认0
        if data.get('price'):
            price = Decimal(str(data.get('price'))) * Decimal('100')
        elif coach.price:
            price = coach.price
        else:
            price = 0
        # 获取分享次数上限，默认为1
        limit_count = data.get('limit_count') or 1

        old_activity = PersonalActivity.objects.filter(
            deleted=False, type=activity_type, coach__user_id=coach.user_id).order_by('-created_at').first()
        # 有旧海报数据默认读取旧海报
        if old_activity:
            # 创建新的个人活动实例，活动有效期为1年
            personal_activity = PersonalActivity.objects.create(
                type=activity_type, price=price, expire_time=pendulum.now().add(years=1).to_datetime_string(),
                coach=coach, resume=resume, limit_count=limit_count, personal_name=old_activity.personal_name,
                city=old_activity.city, coach_domain=old_activity.coach_domain,
                highest_position=old_activity.highest_position, job_profile=old_activity.job_profile,
                posters_text=old_activity.posters_text, coach_auth=old_activity.coach_auth,
                qualification=old_activity.qualification, working_years=old_activity.working_years,
            )
        else:
            # 创建新的个人活动实例，活动有效期为1年
            personal_activity = PersonalActivity.objects.create(
                type=activity_type, price=price, expire_time=pendulum.now().add(years=1).to_datetime_string(),
                coach=coach, resume=resume, limit_count=limit_count,
            )
        UserInviteRecord.objects.create(
            object_id=str(personal_activity.pk), referrer=coach.user, type=UserInviteTypeEnum.coach_share.value,
        )
        if data.get('is_sync_to_resume'):
            personal_activity_update_coach_info(personal_activity, data)
        return personal_activity, ''

    else:
        # 如果活动类型不是教练分享，返回错误信息
        return None, '错误的活动类型'


def update_personal_activity(personal_activity, data):
    """
    根据提供的数据为修改个人活动信息
    """
    fields = [
        'price', 'limit_count', 'personal_name', 'city', 'coach_domain', 'qualification',
        'highest_position', 'job_profile', 'posters_text', 'coach_auth', 'working_years'
    ]
    for field in fields:
        if field in data:
            setattr(personal_activity, field, data[field])
    personal_activity.save()

    if data.get('is_sync_to_resume'):
        personal_activity_update_coach_info(personal_activity, data)
    return personal_activity


def personal_activity_update_coach_info(personal_activity, data):

    coach = personal_activity.coach
    resume = personal_activity.resume
    if 'personal_name' in data.keys():
        coach.personal_name = data['personal_name']
    if 'city' in data.keys():
        coach.city = data['city']
    if 'posters_text' in data.keys():
        coach.posters_text = data['posters_text']
    coach.save()

    if 'coach_domain' in data.keys():
        resume.coach_domain = data['coach_domain']
    if 'highest_position' in data.keys():
        resume.highest_position = data['highest_position']
    if 'job_profile' in data.keys():
        resume.job_profile = data['job_profile']
    if 'qualification' in data.keys():
        resume.qualification = data['qualification']
    if 'coach_auth' in data.keys():
        resume.coach_auth = data['coach_auth']
    if 'working_years' in data.keys():
        resume.working_years = data['working_years']
    resume.save()

    # 清理简历缓存数据
    UserTmp.objects.filter(type=UserTmpEnum.resume, data_id=resume.pk).delete()
    return


def get_coach_share_invite_url(personal_activity):
    # 根据个人活动的ID、关联的教练用户以及邀请类型为教练分享，查询用户邀请记录
    invite_record = UserInviteRecord.objects.filter(
        object_id=str(personal_activity.pk),  # 将个人活动的主键作为对象ID
        referrer=personal_activity.coach.user,  # 教练的用户作为推荐人
        type=UserInviteTypeEnum.coach_share.value,  # 邀请类型为教练分享
    ).first()  # 获取查询结果中的第一条记录

    # 如果查询到了邀请记录
    if invite_record:
        # 返回生成的邀请链接，包含了网站的URL、落地页面的路径、邀请码以及个人活动关联的简历ID
        return f'{settings.SITE_URL}landing/?p=/pages/user/myResume&invite_code={invite_record.uuid}&resume_id={personal_activity.resume_id}'
    # 如果没有找到邀请记录，函数返回None
    return

