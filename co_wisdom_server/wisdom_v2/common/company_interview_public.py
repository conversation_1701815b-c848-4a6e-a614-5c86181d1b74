import datetime
from utils.messagecenter.getui import send_work_wechat_coach_notice
from wisdom_v2.models import WorkWechatUser
from wisdom_v2.models_file.company_interview import CompanyInterview


def send_remind(company_interview_id):
    """
    Send reminder notification to coach via WeCom API

    Args:
        company_interview_id: ID of the company interview
    """
    company_interview = CompanyInterview.objects.filter(id=company_interview_id).first()
    if company_interview:
        work_wechat_user = WorkWechatUser.objects.filter(
            wx_user_id__isnull=False,
            user_id=company_interview.coach.user.id,
            deleted=False
        ).first()
        if work_wechat_user:
            start = company_interview.schedule.public_attr.start_time.strftime('%Y-%m-%d %H:%M') if company_interview.schedule.public_attr.start_time else None
            end = company_interview.schedule.public_attr.end_time.strftime('%H:%M') if company_interview.schedule.public_attr.end_time else None
            interview_datetime = f"{start} - {end}"
            content_item = [
                        {"key": "所属企业", "value": company_interview.project.company.real_name},
                        {"key": "所属项目", "value": company_interview.project.name},
                        {"key": "面试时间", "value": interview_datetime},
                    ]
            user = work_wechat_user.user
            content_ret, app_ret = send_work_wechat_coach_notice(
                work_wechat_user.wx_user_id,
                'company_interview_remind',
                interview_id=company_interview.pk,
                project_name=company_interview.project.full_name,
                user_id=user.pk,
                coach_name=user.cover_name,
                content_item=content_item,
                project_id=company_interview.project.id
            )

            return content_ret and app_ret


def send_30_remind():
    now = datetime.datetime.now()
    target_time = now + datetime.timedelta(minutes=30)

    # Filter interviews starting in 30 minutes by matching minutes
    company_interviews = CompanyInterview.objects.filter(
        deleted=False,
        schedule__public_attr__start_time__year=target_time.year,
        schedule__public_attr__start_time__month=target_time.month,
        schedule__public_attr__start_time__day=target_time.day,
        schedule__public_attr__start_time__hour=target_time.hour,
        schedule__public_attr__start_time__minute=target_time.minute
    )

    for company_interview in company_interviews:
        send_remind(company_interview.id)
