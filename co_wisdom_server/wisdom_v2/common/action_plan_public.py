import pendulum

from utils import utc_date_time


def get_base_default_start_date(end_date_str, created_data_str):
    """
    根据提供的结束日期字符串和创建日期字符串计算默认的开始日期。

    :param end_date_str: 结束日期的字符串表示（假设为 YYYY-MM-DD 格式）
    :param created_data_str: 创建日期的字符串表示（假设为 YYYY-MM-DD 格式）
    :return: 默认的开始日期字符串或错误信息
    """

    # 检查是否提供了结束日期和创建日期
    if not created_data_str or not end_date_str:
        return None, "未获取到必传的时间信息"

    # 将结束日期字符串转换为 UTC 时间
    utc_end_date = utc_date_time.datetime_change_utc(end_date_str)

    # 将创建日期字符串转换为 UTC 时间
    utc_created_data = utc_date_time.datetime_change_utc(created_data_str)

    # 如果 UTC 结束日期大于 UTC 创建日期，则返回创建日期的字符串表示
    if utc_end_date.date() > utc_created_data.date():
        return utc_created_data.to_date_string(), None
    else:
        # 否则，将 UTC 结束日期向前推移 7 天，并返回其字符串表示
        return utc_end_date.subtract(days=7).to_date_string(), None


def get_default_start_date(data):
    """
    根据提供的数据字典中的结束时间和开始时间来确定默认的开始时间。

    :param data: 包含 'end_date' 和 'start_date' 键的字典
    :return: 计算得到的开始时间字符串或错误信息
    """

    # 检查数据字典中是否包含 'end_date' 键
    if not data.get('end_date'):
        return None, "未获取到必传的结束时间信息"

    # 检查数据字典中是否包含 'start_date' 键
    if not data.get('start_date'):
        # 如果没有 'start_date'，则调用 get_base_default_start_date 函数计算默认开始时间
        start_date, error = get_base_default_start_date(
            data.get('end_date'), pendulum.now().to_date_string())

        # 如果在计算过程中发生错误，则返回错误信息
        if error:
            return None, error

        # 返回计算得到的开始时间和 None（表示没有错误）
        return start_date, None

    # 检查提供的结束时间是否在开始时间之后
    elif data.get('end_date') < data.get('start_date'):
        return None, '结束时间必须大于开始时间'

    # 如果以上条件都不满足，则返回提供的开始时间
    return data.get('start_date'), None

