import datetime

from django.db.models import Count, F, Q, Sum

from wisdom_v2.app_views import app_order_action
from wisdom_v2.enum.pay_enum import OrderStatusEnum
from wisdom_v2.enum.project_interview_enum import InterviewRecordTypeEnum
from wisdom_v2.enum.service_content_enum import ActivityTypeEnum, ScheduleTypeEnum
from wisdom_v2.models import Order, ProjectInterview, PersonalUser, User, PublicAttr, Schedule
from wisdom_v2.views.constant import ATTR_STATUS_INTERVIEW_CANCEL, ATTR_TYPE_INTERVIEW, INTERVIEW_TYPE_COACHING


def activity_anticipation_allowed(coachee_user_id, activity):
    """
    判断用户是否有资格预期参与指定的活动
    :param coachee_user_id: 被教练者用户id
    :param activity: 活动对象，是Activity的实例
    :return: bool 返回用户是否还有资格预期参与活动（True表示有资格，False表示无资格）
    """
    limit_count = 0
    # 获取活动允许的参与人数上限
    user_count = Order.objects.filter(
        public_attr__user_id=coachee_user_id, deleted=False, status=OrderStatusEnum.paid.value)

    # 如果是当前活动次数限制，查询匹配当前活动的订单数。
    if activity.limit_count:
        limit_count = activity.limit_count
        user_count = user_count.filter(activity=activity)

    # 如果是累计活动次数限制，查询匹配当前类型活动的订单数。
    elif activity.accum_limit_count:
        limit_count = activity.accum_limit_count
        user_count = user_count.filter(activity__type=ActivityTypeEnum.public_welfare_coach.value)

    # 如果用户已支付的订单数量大于或等于活动限制人数，则用户不再有资格参与
    return user_count.count() < limit_count


def personal_activity_anticipation_allowed(coachee_user_id, personal_activity, new_add_count):
    """
    判断用户是否有资格预期参与指定的个人活动
    :param coachee_user_id: 被教练者用户id
    :param personal_activity: 个人活动对象，是PersonalActivity的实例
    :param new_add_count: 新增订单的次数
    :return: bool 返回用户是否还有资格预期参与活动（True表示有资格，False表示无资格）
    """
    # 获取活动允许的参与人数上限
    user_count = Order.objects.filter(
        public_attr__user_id=coachee_user_id, deleted=False, status=OrderStatusEnum.paid.value)
    # 已预约订单次数
    query_user_count = user_count.filter(personal_activity=personal_activity).aggregate(query_user_count=Sum('count')).get('query_user_count')
    # query_user_count的值可能为空
    query_user_count = query_user_count if query_user_count else 0

    # 活动限制次数
    limit_count = personal_activity.limit_count

    # 已预约次数 + 本次预约次数
    user_count = query_user_count + new_add_count
    # 如果用户已支付的订单数量 + 新建的订单数小于或等于活动限制人数，则用户有资格参与
    return user_count <= limit_count


def get_user_available_order(coachee_user_id=None, coach_user_id=None):
    """
    获取用户未完成的订单。

    该函数返回一个查询集，包括那些已支付且与之相关联的ProjectInterview数量小于订单中记录的数量的订单，并且包括每个订单的ProjectInterview数量作为interviews_hour字段。

    :param coachee_user_id: 被教练者的用户唯一标识符。
    :param coach_user_id: 教练的用户的唯一标识符（指定教练）。
    :return: 符合条件的订单查询集。
    """
    # 获取已支付且未删除的订单，并计算每个订单关联的有效ProjectInterview数量
    # 还要过滤掉超过活动时间的订单
    # 活动时间从order.activity读取
    orders = Order.objects.filter(
        Q(activity__isnull=True) |  # Include orders without activity
        Q(activity__interview_end_date__gte=datetime.datetime.now().date())  # And valid activity orders
    ).filter(
        status=OrderStatusEnum.paid.value,
        deleted=False
    ).annotate(
        interviews_count=Count(
            'interview',
            filter=Q(interview__deleted=False) & ~Q(interview__public_attr__status=ATTR_STATUS_INTERVIEW_CANCEL)
        ),
        interviews_hour=F('interviews_count')  # 为了明确，在这里直接使用interviews_count作为interviews_hour
    ).filter(
        interviews_count__lt=F('count')  # 筛选出ProjectInterview数量小于订单count的订单
    ).order_by('-created_at')  # 按创建时间降序排列
    
    
    if coachee_user_id:
        orders = orders.filter(public_attr__user_id=coachee_user_id)
    if coach_user_id:
        orders = orders.filter(public_attr__target_user_id=coach_user_id)

    return orders


def get_user_refund_order(user_id):
    """
    获取用户退款的订单。

    返回一个查询集，包括那些已退款或正在退款的订单。

    :param user_id: 用户的唯一标识符。
    :return: 符合条件的订单查询集。
    """
    orders = Order.objects.filter(
        public_attr__user_id=user_id,
        status__in=[OrderStatusEnum.under_refund.value, OrderStatusEnum.refunded.value],  # 包括正在退款和已退款状态
        deleted=False
    ).order_by('-created_at')  # 按创建时间降序排列

    return orders


def refund_order(order, refund_reason):
    if order.status != OrderStatusEnum.paid.value:
        return '只有已支付订单可以退款'

    if ProjectInterview.objects.filter(
            order=order, deleted=False).exclude(public_attr__status=ATTR_STATUS_INTERVIEW_CANCEL).exists():
        return '已经预约过教练辅导，暂不支持退款'

    is_success, msg = app_order_action.create_refund_order(order, refund_reason, refund_type='wechat')
    if not is_success:
        return '退款失败'

    return


def get_order_coachee_nickname(order):
    """
    从订单对象中获取个人用户的昵称。

    :param order: 订单对象，包含公共属性
    :return: 如果找到，返回个人用户的昵称；否则用户姓名

    1. 在PersonalUser表中查找与辅导对象中的目标用户ID匹配的用户记录。
    2. 如果找到了匹配的用户，并且该用户的昵称存在，则返回该昵称。
    3. 如果没有找到匹配的用户或用户没有昵称，则返回用户姓名。
    """

    # 查找匹配的PersonalUser记录
    personal_user = PersonalUser.objects.filter(
        user_id=order.public_attr.user_id, deleted=False).first()

    # 如果找到了且有昵称，则返回昵称
    if personal_user and personal_user.nickname:
        return personal_user.nickname

    # 否则返回None
    return order.public_attr.user.cover_name


def create_interview(start_time, end_time, coach_user_id, user_id, topic):
    """
    创建一个辅导会话。

    参数:
    start_time (str): 辅导开始时间，格式为 '%Y-%m-%d %H:%M:%S'。
    end_time (str): 辅导结束时间，格式为 '%Y-%m-%d %H:%M:%S'。
    coach_user_id (int): 教练的用户ID。
    user_id (int): 接受辅导的用户的用户ID。
    topic (str): 辅导的主题。

    返回:
    ProjectInterview: 创建的辅导会话对象。
    """

    # 获取教练和学员的User对象
    coach_user = User.objects.get(pk=coach_user_id)
    user = User.objects.get(pk=user_id)

    # 创建公共属性PublicAttr对象
    public_attr = PublicAttr.objects.create(
        start_time=start_time, end_time=end_time, user_id=coach_user.pk, target_user_id=user.pk,
        type=ATTR_TYPE_INTERVIEW, status=3)

    # 创建日程Schedule对象
    Schedule.objects.create(public_attr_id=public_attr.pk, type=ScheduleTypeEnum.interview, title=topic)

    # 将字符串时间转换为datetime对象
    start_time_obj = datetime.datetime.strptime(start_time, '%Y-%m-%d %H:%M:%S')
    end_time_obj = datetime.datetime.strptime(end_time, '%Y-%m-%d %H:%M:%S')

    # 计算辅导时长（分钟）
    times = round((end_time_obj - start_time_obj).seconds / 60)

    # 创建ProjectInterview对象
    project_interview = ProjectInterview.objects.create(public_attr_id=public_attr.pk,
                                                        type=INTERVIEW_TYPE_COACHING,
                                                        is_coach_agree=False,
                                                        record_type=InterviewRecordTypeEnum.questionnaire,
                                                        place_category=1,
                                                        coachee_topic=topic,
                                                        topic=topic,
                                                        times=times)

    return project_interview
