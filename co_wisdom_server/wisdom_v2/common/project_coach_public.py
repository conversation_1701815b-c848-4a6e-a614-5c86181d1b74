from wisdom_v2.common import interview_public
from wisdom_v2.enum.service_content_enum import ProjectCoachStatusEnum, CoachSourceTypeEnum, OneToOneMatchTypeEnum
from wisdom_v2.models import ProjectCoach, PublicAttr, Coach
from wisdom_v2.views.constant import ATTR_TYPE_COACH_TASK


def project_add_coach(
        project_id, coach_id, resume_ids,
        source_type=CoachSourceTypeEnum.default(), status=ProjectCoachStatusEnum.adopt):
    """
    项目关联教练，目前有两个来源
    1-管理后台-项目列表关联
    2-教练同意项目offer关联

    :param project_id:  项目id  type: int
    :param coach_id:  教练id（不是用户id） type: int
    :param resume_ids:  简历id列表  type: list
    :param source_type:  加入类型（默认项目加入） type: int
    :param status:  通过状态（默认通过）  type: int
    :return: status, object / status. error
    """

    if not project_id or not coach_id or not resume_ids:
        return False, "缺少必填参数"

    project_coach = ProjectCoach.objects.filter(
        project_id=project_id,
        status=status,
        coach_id=coach_id,
        member__isnull=True,
        project_group_coach__isnull=True,
        deleted=False
    ).first()
    if not project_coach:
        project_coach = ProjectCoach.objects.create(
            project_id=project_id,
            status=status,
            coach_id=coach_id,
            resume=resume_ids,
            show_resume_id=resume_ids[0],
            deleted=False,
            source_type=source_type
        )
    return True, project_coach


def user_add_coach(member_id, project_id, coach_id, coach_user_id):
    """
    用户关联教练
    1.小程序化学面谈反馈选择通过
    :param project_id:  项目id  type: int
    :param coach_id:  教练id type: obj
    :param coach_user_id:  教练用户id type: obj
    :param member_id:  用户id type: int
    :param status:  通过状态（默认通过）  type: int
    """
    if not project_id or not member_id or not coach_id or not coach_user_id:
        return False, "缺少必填参数"
    project_coach = ProjectCoach.objects.filter(
        deleted=False, member_id=member_id, project_id=project_id, coach_id=coach_id,
        status=ProjectCoachStatusEnum.adopt).first()
    if not project_coach:
        project_coach = ProjectCoach.objects.create(
            member_id=member_id, project_id=project_id, coach_id=coach_id, status=ProjectCoachStatusEnum.adopt)
        PublicAttr.objects.filter(
            user_id__isnull=True,
            target_user_id=member_id,
            project_id=project_id,
            type=ATTR_TYPE_COACH_TASK,
        ).update(user_id=coach_user_id)
    return True, project_coach


def get_project_coach_exists(project_member, coach_user_id):
    """
    获取项目下用户是否已关联教练
    :param project_member: 项目成员
    :param coach_user_id: 教练用户id
    :return: bool
    """
    if not project_member or not coach_user_id:
        return False

    return ProjectCoach.objects.filter(
            member=project_member.user, coach__user_id=coach_user_id,
            project=project_member.project, deleted=False, project_group_coach__isnull=True).exists()


def get_mop_project_coach_exists(project, coach_user_id):
    """
    获取项目下是否已关联教练
    :param project: 项目信息
    :param coach_user_id: 教练用户id
    :return: bool
    """
    if not project or not coach_user_id:
        return False

    return ProjectCoach.objects.filter(
        coach__user_id=coach_user_id, project=project, deleted=False,
        project_group_coach__isnull=True).exists()


def member_user_unbind_coach(member_user_id, project_id, coach_id, coach_user_id):
    """
    用户解绑关联的教练

    :param member_user_id:  用户id type: int
    :param project_id:  项目id  type: int
    :param coach_id:  教练id type: obj
    :param coach_user_id:  教练用户id type: obj

    1-解除项目中教练和用户的绑定关系
    2-将教练和用户绑定的教练任务解绑
    """

    # 删除已确定的，教练和用户的一对一绑定
    project_coach = ProjectCoach.objects.filter(
        deleted=False, member_id=member_user_id,
        project_id=project_id,
        coach_id=coach_id, status=ProjectCoachStatusEnum.adopt.value
    )
    if project_coach.exists():
        project_coach.update(deleted=True)

    # 解除用户教练任务和教练的绑定
    PublicAttr.objects.filter(
        user_id=coach_user_id,
        target_user_id=member_user_id,
        project_id=project_id,
        type=ATTR_TYPE_COACH_TASK
    ).update(user_id=None)  # 新绑定教练，新教练会匹配用户user_id=None的教练任务
    return


def get_project_user_coach(project_member):
    """
    根据项目成员获取对应的项目教练信息。

    如果项目成员存在一对一指定教练，则优先返回该成员的指定教练列表；
    否则，返回该项目下所有符合条件的教练列表。

    :param project_member: 项目成员对象
    :return: QuerySet or None, 返回教练对象的查询集或者None
    """
    if not project_member:
        return None  # 如果项目成员不存在，则直接返回None

    # 构建基础查询条件：项目ID、未删除、状态为采用、非小组教练
    base_query = ProjectCoach.objects.filter(
        project_id=project_member.project_id,
        deleted=False,
        status=ProjectCoachStatusEnum.adopt.value,
        project_group_coach__isnull=True
    )

    # 如果项目成员的一对一类型为指定教练
    if project_member.coach_match_type == OneToOneMatchTypeEnum.appoint_coach.value:
        # 筛选特定成员的教练
        member_specific_coaches = base_query.filter(member_id=project_member.user_id)
        return member_specific_coaches
    # 如果项目成员的一对一类型是教练池，返回项目匹配的教练列表
    if project_member.coach_match_type == OneToOneMatchTypeEnum.all_project_coach.value:
        all_project_coach = base_query.filter(member__isnull=True)
        return all_project_coach

    # 没有一对一辅导类型，不返回教练信息
    return None


def get_project_coach_and_coachee_info(project_id, coach_ids=None):
    """
    根据项目ID及可选的教练ID列表，获取项目中相关辅导对象及其辅导对象（coachee）的信息，
    并整合项目内正在进行的正式面试的相关用户信息。

    此函数执行以下步骤：
    1. 筛选出项目中所有独立的、状态为已接纳的、未被删除的辅导关系。
    2. 如提供教练ID列表，则进一步筛选出与这些教练相关的辅导关系。
    3. 提取这些辅导关系中教练和辅导对象的用户ID。
    4. 通过外部接口获取项目中正在进行的正式面试，提取其中的教练和目标用户的ID。
    5. 整合并去重上述所有用户ID，形成最终的项目辅导相关用户ID集合。

    参数:
    - project_id: int, 必需，项目的唯一标识符。
    - coach_ids: list of int, 可选，具体教练的ID列表，用于进一步限定筛选范围。

    返回:
    - dict: 包含多个键值对的数据字典，其中键包括：
        - 'project_coach_user_ids': list, 项目辅导关系中教练的用户ID列表。
        - 'project_coachee_user_ids': list, 项目辅导关系中辅导对象的用户ID列表。
        - 'interview_coach_user_ids': list, 正式面试中教练的用户ID列表。
        - 'interview_coachee_user_ids': list, 正式面试中目标用户的用户ID列表。
        - 'all_coach_ids': list, 项目中所有辅导相关教练的去重用户ID列表。
        - 'all_coachee_ids': list, 项目中所有辅导相关用户的去重ID列表。
    """

    # 构建基础的项目辅导关系查询条件：项目ID、未分配至教练组、状态为已接纳、未删除、成员ID存在
    base_project_coach_relations = ProjectCoach.objects.filter(
        project_id=project_id,
        project_group_coach_id__isnull=True,
        status=ProjectCoachStatusEnum.adopt.value,
        deleted=False,
        member_id__isnull=False,
    )

    # 如果指定了教练ID列表，进一步限制查询结果为这些教练的辅导关系
    if coach_ids:
        base_project_coach_relations = base_project_coach_relations.filter(coach_id__in=coach_ids)
        coach_user_ids = Coach.objects.filter(id__in=coach_ids, deleted=False).values_list('user_id', flat=True)
    else:
        coach_user_ids = None

    # 从辅导关系中提取教练和辅导对象的用户ID
    project_coach_user_ids = list(base_project_coach_relations.values_list('coach__user_id', flat=True))
    project_coachee_user_ids = list(base_project_coach_relations.values_list('member_id', flat=True))

    # 通过外部接口获取正在进行的正式面试，以及其中教练和目标用户的ID
    interviews = interview_public.get_formal_interview(coach_user_ids, None, project_id, None, None)
    interview_coach_user_ids = list(interviews.values_list('public_attr__user_id', flat=True))
    interview_coachee_user_ids = list(interviews.values_list('public_attr__target_user_id', flat=True))

    # 合并所有用户ID并去重，形成项目中所有辅导相关用户ID的集合
    all_coach_ids = list(set(project_coach_user_ids + interview_coach_user_ids))
    all_coachee_ids = list(set(project_coachee_user_ids + interview_coachee_user_ids))

    # 将数据整理成字典形式返回
    data = {
        'project_coach_user_ids': project_coach_user_ids,
        'project_coachee_user_ids': project_coachee_user_ids,
        'interview_coach_user_ids': interview_coach_user_ids,
        'interview_coachee_user_ids': interview_coachee_user_ids,
        'all_coach_ids': all_coach_ids,
        'all_coachee_ids': all_coachee_ids,
    }

    return data