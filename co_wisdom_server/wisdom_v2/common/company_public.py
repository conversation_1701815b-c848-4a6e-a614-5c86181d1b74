from wisdom_v2.enum.company_enum import CompanyScaleEnum, CompanyDevelopmentStageEnum, CompanyBusinessScaleEnum, \
    CompanyAttrEnum, CompanyIndustryEnum
from wisdom_v2.enum.service_content_enum import TagObjectTypeEnum
from wisdom_v2.models import Company
from wisdom_v2.common import tag_public
from wisdom_v2.models_file import TagObject


def update_company_tag(company_id):
    """
    更新公司标签。

    参数:
    company_id (UUID): 公司ID。

    功能描述:
    该函数根据公司ID获取公司信息，并根据公司人员规模、发展阶段、业务规模和企业性质更新公司的标签。
    对于每个标签，首先确定对应的标签名称，然后调用 `tag_name_update_company_tag` 函数进行标签更新。
    最后，更新与已完成状态的项目关联的教练项目标签。

    """

    # 获取公司信息
    company = Company.objects.filter(id=company_id, deleted=False).first()
    if not company:
        return
    scale = company.scale  # 企业人员规模
    development_stage = company.development_stage  # 发展阶段
    business_scale = company.business_scale  # 企业业务规模
    company_attr = company.company_attr  # 企业性质
    industry = company.industry  # 所属行业

    # 更新企业人员规模标签
    scale_parent_tag_name = '企业人员规模'
    if scale in CompanyScaleEnum.under_100():
        scale_tag_name = '100人以内'
    elif scale in CompanyScaleEnum.under_1000():
        scale_tag_name = '1000人以内'
    elif scale in CompanyScaleEnum.under_10000():
        scale_tag_name = '10000人以内'
    elif scale == CompanyScaleEnum.above_10000_people.value:
        scale_tag_name = '10000人以上'
    else:
        scale_tag_name = None

    # 清理企业历史标签，重新创建企业标签信息
    TagObject.objects.filter(
        deleted=False, object_type=TagObjectTypeEnum.company.value, object_id=company_id
    ).update(deleted=True)

    tag_public.tag_name_update_object_tag(company_id, TagObjectTypeEnum.company.value, scale_tag_name, scale_parent_tag_name)

    # 更新发展阶段标签
    development_stage_parent_tag_name = '发展阶段'
    if development_stage:
        development_stage_tag_name = CompanyDevelopmentStageEnum.get_display(development_stage)
        tag_public.tag_name_update_object_tag(company_id, TagObjectTypeEnum.company.value, development_stage_tag_name, development_stage_parent_tag_name)

    # 更新企业业务规模标签
    business_scale_parent_tag_name = '企业业务规模'
    if business_scale:
        business_scale_tag_name = CompanyBusinessScaleEnum.get_display(business_scale)
        tag_public.tag_name_update_object_tag(company_id, TagObjectTypeEnum.company.value, business_scale_tag_name, business_scale_parent_tag_name)

    # 更新企业性质标签
    company_attr_parent_tag_name = '企业性质'
    if company_attr in CompanyAttrEnum.is_create_tag():
        company_attr_tag_name = CompanyAttrEnum.get_display(company_attr)
        tag_public.tag_name_update_object_tag(company_id, TagObjectTypeEnum.company.value, company_attr_tag_name, company_attr_parent_tag_name)

    # 更新企业行业标签
    company_industry_parent_tag_name = '所属行业'
    if industry:
        company_industry_tag_name = CompanyIndustryEnum.get_display(industry)
        tag_public.tag_name_update_object_tag(company_id, TagObjectTypeEnum.company.value, company_industry_tag_name, company_industry_parent_tag_name)
