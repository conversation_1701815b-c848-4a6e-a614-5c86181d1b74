import datetime
from decimal import Decimal, ROUND_UP

import pendulum
from django.conf import settings
from django.db.models import Sum, Q, When, Case, Avg
from django.db import transaction

from utils.aliyun_sls_log import AliyunSlsLogLayout
from utils.miniapp_version_judge import compare_version
from utils.queryset import multiple_field_distinct
from utils.utc_date_time import datetime_change_utc
from utils.work_wechat import WorkWechat
from wisdom_v2.app_views.app_interview_actions import is_interview_update_date_button
from wisdom_v2.common import stakeholder_interview_public, chemical_interview_public, coach_public, project_member_public
from wisdom_v2.enum.business_order_enum import WorkTypeEnum
from wisdom_v2.enum.pay_enum import OrderStatusEnum
from wisdom_v2.enum.project_interview_enum import ProjectInterviewPlaceCategoryEnum, ProjectInterviewTypeEnum, \
    GroupCoachTypeEnum, ProjectInterviewRecordCompleteEnum, InterviewRecordTypeEnum, TotalTemplateTypeEnum, \
    InterviewMeetingChannelTypeE<PERSON>, InterviewDetailHeadEnum, InterviewD<PERSON>ilContentEnum, InterviewDetailButtonEnum, \
    ProjectInterviewPlaceTypeEnum, InterviewSubjectEnum
from wisdom_v2.enum.service_content_enum import CoachTypeEnum, InterviewRecordTemplateRoleEnum, \
    InterviewRecordTemplateQuestionRatingTypeEnum, InterviewRecordTemplateQuestionTypeEnum, \
    InterviewRecordTemplateQuestionAnswerTypeEnum, InterviewRecordTemplateTypeEnum
from wisdom_v2.enum.user_enum import UserRoleEnum
from wisdom_v2.models import ProjectInterview, PublicAttr, Schedule, ProjectInterviewRecord, \
    InterviewRecordTemplateAnswer, TotalTemplate, PersonalUser, ProjectNote, Diary, CoachAppraise, \
    QuestionObjectRelationship, WorkWechatUser, GroupCoach
from wisdom_v2.models_file import BusinessOrder2Object
from wisdom_v2.models_file.interview import InterviewMeeting
from wisdom_v2.views import constant
from utils import task
from wisdom_v2.views.constant import ATTR_STATUS_INTERVIEW_CANCEL, INTERVIEW_TYPE_COACHING, ATTR_TYPE_PROJECTNOTE, \
    ROLE_COACH, DIARY_FROM_RECORD, ATTR_STATUS_INTERVIEW_COACHING_FINISHED, TITLE_TO_SCORE, UPDATE_INTERVIEW_MEETING, \
    CANCEL_INTERVIEW_MEETING
from utils.messagecenter.center import push_v2_message
from utils.messagecenter.getui import push_to_single
from utils.wechat_oauth import WeChatMiniProgram
from utils.qr_code import get_qr_code


def get_project_coach_interview_time(user_id):
    """
    查询项目教练已完成的辅导时长
    :param user_id: 教练的用户id  type: int
    :return: 辅导小时数
    """
    # 项目教练同时查B&C辅导
    interview = ProjectInterview.objects.filter(
        deleted=False,
        is_coach_agree=True,
        place_category=ProjectInterviewPlaceCategoryEnum.online_one_to_one.value,
        public_attr__user_id=user_id,
        public_attr__end_time__lte=datetime.datetime.now(),  # 只查询已完成的
        type=constant.INTERVIEW_TYPE_COACHING,
    ).exclude(
        # 排除已取消的
        public_attr__status=constant.ATTR_STATUS_INTERVIEW_CANCEL).exclude(
        # 排除未支付，退款的
        order__status__in=[OrderStatusEnum.pending_pay, OrderStatusEnum.under_refund, OrderStatusEnum.refunded])
    interview = interview.aggregate(used_times=Sum('times'))
    interview_time = interview.get('used_times', 0) if interview.get('used_times', 0) else 0
    interview_time = round(interview_time / 60, 1) if interview_time > 0 else 0
    return interview_time if interview_time else None


def get_personal_coach_interview_time(user_id):
    """
    查询个人教练已完成的辅导时长
    :param user_id: 教练的用户id  type: int
    :return: 辅导小时数  type: int
    """
    # 个人教练只查询C端辅导
    interview = ProjectInterview.objects.filter(
        deleted=False,
        public_attr__project__isnull=True,
        place_category=ProjectInterviewPlaceCategoryEnum.online_one_to_one.value,
        public_attr__user_id=user_id,
        public_attr__end_time__lte=datetime.datetime.now(),  # 只查询已完成的
        type=constant.INTERVIEW_TYPE_COACHING,
    ).exclude(
        # 排除已取消的
        public_attr__status=constant.ATTR_STATUS_INTERVIEW_CANCEL).exclude(
        # 排除未支付，退款的
        order__status__in=[OrderStatusEnum.pending_pay, OrderStatusEnum.under_refund, OrderStatusEnum.refunded])
    interview = interview.aggregate(used_times=Sum('times'))
    interview_time = interview.get('used_times', 0) if interview.get('used_times', 0) else 0
    interview_time = round(interview_time / 60, 1) if interview_time > 0 else 0
    return interview_time if interview_time else None


def user_update_interview_time(interview, start_time, end_time, role=None, mp=None):
    """
    修改辅导时间
    interview： 辅导对象
    start_time（str）： 新的开始时间
    end_time（str）：新的结束时间
    role：修改者的角色
    """
    
    # 将时间字符串转换成datetime对象
    start_time_obj = datetime.datetime.strptime(start_time, '%Y-%m-%d %H:%M')
    end_time_obj = datetime.datetime.strptime(end_time, '%Y-%m-%d %H:%M')
    time_delta = end_time_obj - start_time_obj

    # 时间没有变化，不用改动
    if start_time_obj == interview.public_attr.start_time and end_time_obj == interview.public_attr.end_time:
        return

    before_time = '{}~{}'.format(
        interview.public_attr.start_time.strftime('%Y-%m-%d %H:%M'),
        interview.public_attr.end_time.strftime('%H:%M'))
    PublicAttr.objects.filter(uuid=interview.public_attr.uuid).update(start_time=start_time_obj, end_time=end_time_obj)

    # 更新辅导时长
    interview.times = round(time_delta.seconds / 60)
    interview.save()

    now_time = f"{start_time_obj.strftime('%Y-%m-%d %H:%M')}~{end_time_obj.strftime('%H:%M')}"
    task.user_update_interview_time_message.apply_async(
        kwargs=dict(interview_id=interview.pk, before_time=before_time,
                    now_time=now_time, role=role), countdown=3, expires=120)

    # 线上辅导才需要修改会议信息。
    if interview.place_type == ProjectInterviewPlaceTypeEnum.online.value:
        # 2.33版本更新辅导时间时同步更新企业微信会议。
        if mp and compare_version(mp.get('version'), '2.33') >= 0:
            work_wechat_user = WorkWechatUser.objects.filter(
                wx_user_id__isnull=False, user_id=interview.public_attr.user_id, deleted=False
            ).first()
            if work_wechat_user:

                # 如果有会议进行更新
                meeting = interview.interview_meeting.filter(deleted=False).first()
                if meeting:
                    task.update_project_interview_meeting.delay(
                        UPDATE_INTERVIEW_MEETING, meeting.meeting_id,
                        start_time=int(start_time_obj.timestamp()), duration=time_delta.seconds)
                # 没有会议
                else:
                    # 教练同意辅导则创建会议
                    if interview.is_coach_agree:
                        if interview.type == ProjectInterviewTypeEnum.formal_interview.value:
                            title = '教练辅导'
                        elif interview.type == ProjectInterviewTypeEnum.chemical_interview.value:
                            title = '化学面谈'
                        elif interview.type == ProjectInterviewTypeEnum.stakeholder_interview.value:
                            title = '利益相关者访谈'
                        else:
                            title = '教练辅导'
                        task.create_project_interview_meeting.delay(
                            interview.id, work_wechat_user.wx_user_id, title,
                            int(start_time_obj.timestamp()), time_delta.seconds)


def cancel_interview(interview, reason):
    """
    取消辅导
    interview： 辅导对象
    reason： 取消原因
    """
    with transaction.atomic():
        interview.close_reason = reason
        interview.save()
        interview.public_attr.status = ATTR_STATUS_INTERVIEW_CANCEL
        interview.public_attr.save()

        # 个人活动需要移除关联
        interview.personal_activity_interview.filter(deleted=False).update(deleted=True)
        # 公益教练需要移除关联
        interview.activity_interview.filter(deleted=False).update(deleted=True)
        schedule = Schedule.objects.filter(public_attr=interview.public_attr).first()
        if schedule:
            schedule.deleted = True
            schedule.save()

        meeting = interview.interview_meeting.filter(deleted=False).first()
        if meeting:
            meeting.deleted = True
            meeting.save()
            task.update_project_interview_meeting.delay(CANCEL_INTERVIEW_MEETING, meeting.meeting_id)
        
        # 化学面谈要更新配置
        chemical_interview = interview.chemical_interview.filter(deleted=False).first()
        if chemical_interview:
            chemical_interview.interview = None
            chemical_interview.save()


def get_user_interview_time(project_member):
    """
    获取用户已完成的辅导时长
    :param project_member: 项目成员对象
    :return: 辅导小时数
    """

    interview_time = ProjectInterview.objects.filter(
        deleted=False,
        public_attr__target_user_id=project_member.user_id,
        public_attr__project_id=project_member.project_id,
        public_attr__end_time__lt=datetime.datetime.now(),
        place_category=ProjectInterviewPlaceCategoryEnum.online_one_to_one.value,
        type=constant.INTERVIEW_TYPE_COACHING,
    ).exclude(public_attr__status=constant.ATTR_STATUS_INTERVIEW_CANCEL).aggregate(used_times=Sum('times'))
    interview_time = interview_time.get('used_times', 0) if interview_time.get('used_times', 0) else 0
    interview_time = round(interview_time / 60, 1) if interview_time > 0 else 0

    return interview_time


def get_user_reservation_interview_time(project_member):
    """
    获取用户预约的一对一辅导时长
    :param project_member: 项目成员对象
    :return: 辅导小时数
    """

    interview_time = ProjectInterview.objects.filter(
        deleted=False,
        public_attr__target_user_id=project_member.user_id,
        public_attr__project_id=project_member.project_id,
        place_category=ProjectInterviewPlaceCategoryEnum.online_one_to_one.value,
        type=constant.INTERVIEW_TYPE_COACHING,
    ).exclude(public_attr__status=constant.ATTR_STATUS_INTERVIEW_CANCEL).aggregate(used_times=Sum('times'))
    interview_time = interview_time.get('used_times', 0) if interview_time.get('used_times', 0) else 0
    interview_time = round(interview_time / 60, 1) if interview_time > 0 else 0
    return interview_time


def get_formal_interview(coach_users, coachee_users, project_id, start_date, end_date):
    """
    获取指定项目下的正式访谈列表
    可选参数：教练用户列表，被教练者用户列表，开始时间，结束时间
    :param coach_users: 教练用户列表
    :param coachee_users: 被教练者用户列表
    :param project_id: 项目id
    :param start_date: 开始时间
    :param end_date: 结束时间
    :return: 正式访谈列表
    """

    queryset = ProjectInterview.objects.filter(
        type=ProjectInterviewTypeEnum.formal_interview.value,
        place_category=ProjectInterviewPlaceCategoryEnum.online_one_to_one.value,
        public_attr__project_id=project_id,
        deleted=False,
    ).exclude(public_attr__status=ATTR_STATUS_INTERVIEW_CANCEL).order_by('public_attr__start_time')
    if start_date:
        queryset = queryset.filter(public_attr__start_time__date__gte=start_date)
    if end_date:
        queryset = queryset.filter(public_attr__end_time__date__lte=end_date)
    if coach_users:
        queryset = queryset.filter(public_attr__user__in=coach_users)
    if coachee_users:
        queryset = queryset.filter(public_attr__target_user__in=coachee_users)

    return queryset


def get_offline_group_coach(coach_users, coachee_users, project_id, start_date, end_date, group_coach_type=None):
    """
    获取指定项目下线下集体辅导
    可选参数：教练用户列表，被教练者用户列表，开始时间，结束时间
    :param coach_users: 教练用户列表
    :param coachee_users: 被教练者用户列表
    :param project_id: 项目id
    :param start_date: 开始时间 type date
    :param end_date: 结束时间 type date
    :param group_coach_type: 集体辅导类型 type int  1-工作坊 2-小组辅导
    :return: 线下集体辅导列表
    """
    queryset = ProjectInterview.objects.filter(
        type=ProjectInterviewTypeEnum.formal_interview.value,
        place_category=ProjectInterviewPlaceCategoryEnum.offline_group_coach.value,
        public_attr__project_id=project_id,
        deleted=False,
    ).exclude(public_attr__status=ATTR_STATUS_INTERVIEW_CANCEL).order_by('public_attr__start_time')

    if start_date:
        queryset = queryset.filter(public_attr__start_time__date__gte=start_date)
    if end_date:
        queryset = queryset.filter(public_attr__end_time__date__lte=end_date)

    if coach_users:
        queryset = queryset.filter(public_attr__user__in=coach_users)

    if coachee_users:
        queryset = queryset.filter(public_attr__target_user__in=coachee_users)

    if group_coach_type:
        queryset = queryset.filter(
            coach_group_module__project_group_coach__type=group_coach_type, coach_group_module__deleted=False)

    return queryset


def get_admin_interview_schedules(coach_users, coachee_users, stakeholder_users, project_id, start_date, end_date):
    """
    获取管理后台辅导数据
    可选参数：教练用户列表，被教练者用户列表，利益相关者用户列表，开始时间，结束时间
    :param coach_users: 教练用户列表
    :param coachee_users: 被教练者用户列表
    :param stakeholder_users: 利益相关者用户列表
    :param project_id: 项目id
    :param start_date: 开始时间 type date
    :param end_date: 结束时间 type date
    :return: 管理后台辅导日程列表

    type: 1-一对一辅导 2-利益相关者访谈 3-化学面谈 4-集体辅导 5-可预约日程
    """
    data = []

    # 如果有利益相关者，则只获取利益相关者访谈
    if stakeholder_users:
        stakeholder_interview = stakeholder_interview_public.get_stakeholder_interview(
            coach_users, coachee_users, stakeholder_users, project_id, start_date, end_date)
        if stakeholder_interview.exists():
            for item in stakeholder_interview.all():
                data.append({
                    'start': item.interview.public_attr.start_time,
                    'end': item.interview.public_attr.end_time,
                    'type': 2,
                    'is_day': False,
                    'details': {
                        'id': item.interview.id,
                        'title': '利益相关者访谈',
                        'coach_name': item.interview.public_attr.user.cover_name,
                        'coachee_name': item.project_interested.master.cover_name,
                        'stakeholder_name': item.project_interested.interested.cover_name,
                    }
                })
    else:
        formal_interview = get_formal_interview(coach_users, coachee_users, project_id, start_date, end_date)
        if formal_interview.exists():
            for item in formal_interview.all():
                data.append({
                    'start': item.public_attr.start_time,
                    'end': item.public_attr.end_time,
                    'type': 1,
                    'is_day': False,
                    'details': {
                        'id': item.id,
                        'title': '一对一辅导',
                        'coach_name': item.public_attr.user.cover_name,
                        'coachee_name': item.public_attr.target_user.cover_name,
                    }
                })

        stakeholder_interview = stakeholder_interview_public.get_stakeholder_interview(coach_users, coachee_users, stakeholder_users, project_id, start_date, end_date)
        if stakeholder_interview.exists():
            for item in stakeholder_interview.all():
                data.append({
                    'start': item.interview.public_attr.start_time,
                    'end': item.interview.public_attr.end_time,
                    'type': 2,
                    'is_day': False,
                    'details': {
                        'id': item.interview.id,
                        'title': '利益相关者访谈',
                        'coach_name': item.interview.public_attr.user.cover_name,
                        'coachee_name': item.project_interested.master.cover_name,
                        'stakeholder_name': item.project_interested.interested.cover_name,
                    }
                })

        chemical_interview = chemical_interview_public.get_chemical_interview(coach_users, coachee_users, project_id, start_date, end_date)
        if chemical_interview.exists():
            for item in chemical_interview.all():
                data.append({
                    'start': item.interview.public_attr.start_time,
                    'end': item.interview.public_attr.end_time,
                    'type': 3,
                    'is_day': False,
                    'details': {
                        'id': item.interview.id,
                        'title': '化学面谈',
                        'coach_name': item.interview.public_attr.user.cover_name,
                        'coachee_name': item.interview.public_attr.target_user.cover_name,
                    }
                })

        raw_offline_group_coach = get_offline_group_coach(coach_users, coachee_users, project_id, start_date, end_date, GroupCoachTypeEnum.collective_tutoring.value)
        if raw_offline_group_coach.exists():
            offline_group_coach = multiple_field_distinct(
                raw_offline_group_coach, ['topic', 'times', 'place', 'place_category', 'public_attr.start_time', 'public_attr.end_time'])

            for item in offline_group_coach.all():
                data.append({
                    'start': item.public_attr.start_time,
                    'end': item.public_attr.end_time,
                    'type': 4,
                    'is_day': False,
                    'details': {
                        'id': item.id,
                        'title': '线下工作坊',
                        'coach_name': item.public_attr.user.cover_name
                    }
                })

        raw_group_tutoring = get_offline_group_coach(coach_users, coachee_users, project_id, start_date, end_date, GroupCoachTypeEnum.group_tutoring.value)
        if raw_group_tutoring.exists():
            group_tutoring = multiple_field_distinct(
                raw_group_tutoring, ['topic', 'times', 'place', 'place_category', 'public_attr.start_time', 'public_attr.end_time'])

            for item in group_tutoring.all():
                data.append({
                    'start': item.public_attr.start_time,
                    'end': item.public_attr.end_time,
                    'type': 6,
                    'is_day': False,
                    'details': {
                        'id': item.id,
                        'title': '小组辅导',
                        'coach_name': item.public_attr.user.cover_name
                    }
                })

    for item in data:
        start = item['start']
        end = item['end']

        # 检查结束时间是否跨越到第二天
        if end.date() > start.date():
            # 如果结束时间跨越到第二天，将它减去一天，并设置时间为24:00
            end = end - datetime.timedelta(days=1)
            end_datetime_str = f'{end.date()} 24:00:00'  # 设置结束日期的字符串表示
            end_time_str = f'24:00'  # 设置结束时间为24:00

        else:
            # 如果结束时间没有跨越到第二天，直接格式化它
            end_datetime_str = end.strftime("%Y-%m-%d %H:%M:%S")
            end_time_str = end.strftime("%H:%M")

        # 更新item中的开始和结束时间，以及details中的时间
        item['start'] = start.strftime("%Y-%m-%d %H:%M:%S")
        item['end'] = end_datetime_str
        item['details']['time'] = f'{start.strftime("%H:%M")}-{end_time_str}'
    return data


def get_interview_prompt(interview, role):
    """
    获取辅导提示信息
    :param interview: 用户辅导
    :param role: 角色
    """
    if not interview or not role:
        return
    role = int(role)
    if role == UserRoleEnum.trainee_coach.value:  # C端个人教练
        # 没同意或者未开始
        if not interview.is_coach_agree or interview.public_attr.start_time > datetime.datetime.now():
            return '如果客户临时需要增加约谈时间，建议告知客户付费预约后再提供服务。平台最终会按照客户实际付费进行结算。'
    return


def get_activity_data(interview):
    """
    获取活动数据
    :param interview: 用户辅导
    :return: 活动数据
    """
    activity_interview = interview.activity_interview.filter(deleted=False).first()
    if activity_interview:
        return {
            'id': activity_interview.id,
            'start_date': activity_interview.activity.start_date,
            'end_date': activity_interview.activity.end_date,
            'interview_start_date': activity_interview.activity.interview_start_date,
            'interview_end_date': activity_interview.activity.interview_end_date,
            'theme': activity_interview.activity.theme,
            'guide_image_url': activity_interview.activity.guide_image_url,
        }
    return


def get_personal_activity_data(interview):
    """
    获取教练个人活动数据
    :param interview: 用户辅导
    :return: 个人活动数据
    """
    activity_interview = interview.personal_activity_interview.filter(deleted=False).first()
    if activity_interview:
        return {
            'id': activity_interview.personal_activity.id,
            'type': activity_interview.personal_activity.type
        }
    return


def check_member_interview_times(project_member, times, one_to_one_coach_type=CoachTypeEnum.online.value):
    """
    检查项目成员在特定类型的辅导（如在线一对一）下的剩余可用辅导时间是否满足预约需求。

    :param project_member: 项目成员对象，代表被教练者。
    :param times: int, 预约所需的辅导时间（单位：分钟）。
    :param one_to_one_coach_type: int, 默认为在线一对一类型，用于筛选特定类型的教练资源。

    :return: tuple, 包含两个元素的元组：
             - 第一个元素为布尔值，表示是否有足够的剩余时间。
             - 第二个元素为浮点数，表示剩余的可用辅导时间（单位：小时）。
    """

    all_times, used_times = project_member_public.get_interview_all_times_and_user_time(
        project_member, one_to_one_coach_type)
    # 判断剩余时间是否大于当次预约时间
    if all_times - used_times < times:
        return False, (all_times - used_times) / 60
    return True, (all_times - used_times) / 60


def get_group_coach_type(interview):
    """
    获取集体辅导的教练类型
    """
    if interview.place_category == ProjectInterviewPlaceCategoryEnum.offline_group_coach:
        group_coach = interview.coach_group_module.filter(deleted=False).first()
        return group_coach.project_group_coach.type


def get_interview_to_business_order(interview):
    """
    获取辅导对应的结算单信息
    :param interview: 辅导对象
    :return: 订单对象
    """
    # 线上一对一辅导
    if interview.place_category == ProjectInterviewPlaceCategoryEnum.online_one_to_one.value:
        if interview.type == ProjectInterviewTypeEnum.formal_interview.value:
            work_type = WorkTypeEnum.one_to_one.value
        elif interview.type == ProjectInterviewTypeEnum.stakeholder_interview.value:
            work_type = WorkTypeEnum.stakeholder_interview.value
        else:
            return

    # 线下集体辅导
    elif interview.place_category == ProjectInterviewPlaceCategoryEnum.offline_group_coach.value:
        if interview.coach_group_module.filter(deleted=False).exists():
            if interview.coach_group_module.filter(deleted=False).first().project_group_coach.type == GroupCoachTypeEnum.group_tutoring.value:
                work_type = WorkTypeEnum.stakeholder_interview.value
            elif interview.coach_group_module.filter(deleted=False).first().project_group_coach.type == GroupCoachTypeEnum.collective_tutoring.value:
                work_type = WorkTypeEnum.stakeholder_interview.value
            else:
                return
        else:
            return
    else:
        return
    business_order_2_object = BusinessOrder2Object.objects.filter(object_id=interview.id, type=work_type, deleted=False).first()
    if business_order_2_object:
        return business_order_2_object.business_order
    return


def get_project_interview_details(params):
    """
        获取项目辅导详情
    """
    str_project_ids = params.get('project_ids')  # 项目id
    place_category = params.get(
        'place_category', ProjectInterviewPlaceCategoryEnum.online_one_to_one.value)  # 辅导类型 1-个人 3-集体
    str_coachee_ids = params.get('coachee_ids')  # 被教者id
    str_coach_ids = params.get('coach_ids')  # 教练id
    start_time = params.get('start_time')  # 辅导开始时间
    end_time = params.get('end_time')  # 辅导结束时间
    created_start_time = params.get('created_start_time')  # 用户预约辅导的开始时间
    created_end_time = params.get('created_end_time')  # 用户预约辅导的结束时间
    interview_status = params.get('interview_status')  # 访谈状态
    interview_record_status = params.get('interview_record_status')  # 记录填写状态
    is_settlement = params.get('is_settlement')  # 是否结算
    name = params.get('name')  # 项目名称
    coachee_type = params.get('coachee_type')  # 被辅导人类型 1-项目 2-个人
    order_state = params.get('order_state')  # 订单状态
    activity_ids = params.get('activity_ids')  # 活动id
    queryset = ProjectInterview.objects.filter(type=constant.INTERVIEW_TYPE_COACHING).exclude(deleted=True)

    try:
        if str_project_ids:
            project_id_list = str_project_ids.split(',')
            queryset = queryset.filter(public_attr__project_id__in=project_id_list)
        if str_coachee_ids:
            coachee_id_list = str_coachee_ids.split(',')
            queryset = queryset.filter(public_attr__target_user_id__in=coachee_id_list)
        if str_coach_ids:
            coach_id_list = str_coach_ids.split(',')
            queryset = queryset.filter(public_attr__user_id__in=coach_id_list)
        if place_category:
            queryset = queryset.filter(place_category=place_category)
        if start_time:
            queryset = queryset.filter(public_attr__start_time__gte=start_time)
        if end_time:
            queryset = queryset.filter(public_attr__start_time__lte=end_time)
        if created_start_time:
            queryset = queryset.filter(created_at__gte=created_start_time)
        if created_end_time:
            queryset = queryset.filter(created_at__lte=created_end_time)
        if is_settlement:
            is_settlement = True if is_settlement.lower() == 'true' else False
            queryset = queryset.filter(is_settlement=is_settlement)
        if name:
            queryset = queryset.filter(public_attr__target_user__true_name__icontains=name)
        if coachee_type:
            if int(coachee_type) == constant.ADMIN_PROJECT_USER:
                queryset = queryset.filter(public_attr__project__isnull=False)
            elif int(coachee_type) == constant.ADMIN_PERSONAL_USER:
                queryset = queryset.filter(public_attr__project__isnull=True)
        if activity_ids:
            activity_id_list = activity_ids.split(',')
            queryset = queryset.filter(
                activity_interview__activity_id__in=activity_id_list, activity_interview__deleted=False)

        # 辅导状态
        if interview_status:
            queryset = queryset.exclude(public_attr__status=ATTR_STATUS_INTERVIEW_CANCEL)

            if int(interview_status) == constant.ADMIN_COACH_AGREE:
                queryset = queryset.filter(is_coach_agree=False)
            else:
                queryset = queryset.filter(is_coach_agree=True)
                if int(interview_status) == constant.ADMIN_INTERVIEW_NOT_START:
                    queryset = queryset.filter(public_attr__start_time__gt=datetime.datetime.now())
                elif int(interview_status) == constant.ADMIN_INTERVIEW_ONGOING:
                    queryset = queryset.filter(
                        public_attr__start_time__lte=datetime.datetime.now(),
                        public_attr__end_time__gte=datetime.datetime.now()
                    )
                elif int(interview_status) == constant.ADMIN_INTERVIEW_END:
                    queryset = queryset.filter(public_attr__end_time__lt=datetime.datetime.now())

        # 填写状态
        if interview_record_status:
            if int(interview_record_status) == ProjectInterviewRecordCompleteEnum.all_unfinished.value:
                queryset = queryset.filter(coach_record_status=False, coachee_record_status=False)
            elif int(interview_record_status) == ProjectInterviewRecordCompleteEnum.coach_unfinished.value:
                queryset = queryset.filter(coach_record_status=False, coachee_record_status=True)
            elif int(interview_record_status) == ProjectInterviewRecordCompleteEnum.member_unfinished.value:
                queryset = queryset.filter(coach_record_status=True, coachee_record_status=False)
            elif int(interview_record_status) == ProjectInterviewRecordCompleteEnum.finished.value:
                queryset = queryset.filter(coach_record_status=True, coachee_record_status=True)

        if order_state:
            tmp_q = Q()
            for item in order_state.split(','):
                # 待支付的查询未付款，订单未删除的
                if int(item) == constant.ADMIN_ORDER_STATE_PENDING_PAY:
                    tmp_q.add(Q(order__status=OrderStatusEnum.pending_pay, order__deleted=False), Q.OR)

                # 已关闭的查询未付款，订单已删除的
                elif int(item) == constant.ADMIN_ORDER_STATE_CLOSURE:
                    tmp_q.add(Q(order__status=OrderStatusEnum.pending_pay, order__deleted=True), Q.OR)

                # 已完成的查询已付款，订单未删除的
                elif int(item) == constant.ADMIN_ORDER_STATE_COMPLETE:
                    tmp_q.add(Q(order__status=OrderStatusEnum.paid, order__deleted=False), Q.OR)

                # 已完成的查询已退款，订单未删除的
                elif int(item) == constant.ADMIN_ORDER_STATE_REFUND:
                    tmp_q.add(Q(
                        order__status__in=[OrderStatusEnum.under_refund, OrderStatusEnum.refunded]), Q.OR)
            queryset = queryset.filter(tmp_q)
        return True, queryset.order_by('-public_attr__start_time')

    except Exception as e:
        return False, str(e)


# 默认从obj中获取topic，如果obj中没有topic，则返回'教练辅导'
def get_interview_topic(role, obj):
    return obj.message_topic


def get_interview_thinking_and_reflection(interview):
    """
    获取辅导记录的思考与反思。
    此函数处理辅导记录，提取和整理相关信息，包括参与者的回答和辅导时间等。

    :param interview: 辅导记录列表，每个记录包含辅导的详细信息。
    :return: 处理后的辅导记录数据列表。
    """
    if isinstance(interview, list):
        return []

    data = []
    for item in interview:

        coach_answer = None
        coachee_answer = None
        public_attr = item.public_attr

        # 格式化辅导开始和结束时间
        interview_time = public_attr.start_time.strftime(
            '%Y-%m-%d %H:%M') + '-' + public_attr.end_time.strftime('%H:%M')

        if item.record_type == InterviewRecordTypeEnum.questionnaire.value:

            if item.public_attr.project_id:
                if item.coach_record_status:
                    # 教练使用问卷模板的第二题作为回答（你对这个客户有哪些观察）
                    coach_answer = get_interview_to_question_answer(item, 2)
                if item.coachee_record_status:
                    # 受辅导者使用问卷模板的第二题作为回答（本次教练辅导给你带来哪些思考和收获）
                    coachee_answer = get_interview_to_question_answer(item, 2, is_coach=False)
            else:
                if item.coach_record_status:
                    # 教练使用问卷模板的第一题作为回答（你对这个客户有哪些观察）
                    coach_answer = get_interview_to_question_answer(item, 1)
                if item.coachee_record_status:
                    # 受辅导者使用问卷模板的第四题作为回答（教练辅导给你带来哪些新的洞见）
                    coachee_answer = get_interview_to_question_answer(item, 4, is_coach=False)

        elif item.record_type == InterviewRecordTypeEnum.question_and_answer.value:
            if item.coachee_record_status or item.coach_record_status:
                record = ProjectInterviewRecord.objects.filter(deleted=False, interview=item).first()
                if record:
                    # 提取受辅导者的发现和教练的观察
                    coachee_answer = record.discover.content if record.discover else None
                    coach_answer = record.observation
        else:
            continue

        # 如果都题目未填写，不展示
        if not coach_answer and not coachee_answer:
            continue

        # 将提取的信息添加到数据列表
        data.append({
            "id": item.id,
            "title": item.topic if item.topic else item.coachee_topic,
            "interview_time": interview_time,
            "coachee_name": public_attr.target_user.cover_name,
            "coach_answer": coach_answer,
            "coachee_answer": coachee_answer
        })

    return data


def get_interview_to_question_answer(interview, order, is_coach=True):
    """
    根据题目顺序获取辅导记录中特定问卷题目的答案。
    此函数在问卷类型的辅导记录中查找特定顺序的问题，并提取相关答案。
    可以指定是获取教练的回答还是受辅导者的回答。

    :param interview: 辅导实例，包含辅导记录的详细信息。
    :param order: 题目的顺序号。序号从1开始。
    :param is_coach: 是否是教练的答案。默认为True，表示获取教练的答案；若为False，则获取受辅导者的答案。
    :return: 对应题目的答案，如果找不到则返回None。
    """

    # 获取该辅导记录中用户回答的Answer对象
    tmp_answer = InterviewRecordTemplateAnswer.objects.filter(interview=interview, deleted=False).first()

    if interview.public_attr.project_id:
        total_template_type = TotalTemplateTypeEnum.project_one_to_one.value
    else:
        total_template_type = TotalTemplateTypeEnum.one_to_one_tutor.value

    if tmp_answer:
        # 根据回答获取对应的辅导模板
        template = tmp_answer.question.template

        # 根据模板，找到适用于教练或受辅导者的总模板
        total_template = TotalTemplate.objects.filter(
            (Q(coach_template=template) | Q(coachee_template=template)),
            type=total_template_type,
            write_role=InterviewRecordTemplateRoleEnum.coach_student.value).first()

        if total_template:
            # 根据is_coach参数决定使用教练模板还是受辅导者模板
            interview_template = total_template.coach_template if is_coach else total_template.coachee_template
            if interview_template and interview_template.questions_order:
                # 从辅导模板中获取对应顺序的问题ID
                question_order = eval(interview_template.questions_order)
                if len(question_order) > order - 1:  # 减1因为问题序号从1开始
                    # 获取并返回该问题的答案
                    question_id = question_order[order - 1]
                    answer_obj = InterviewRecordTemplateAnswer.objects.filter(
                        interview=interview, question_id=question_id, deleted=False).first()

                    if answer_obj:
                        answer_type = answer_obj.question.answer_type
                        if answer_type == InterviewRecordTemplateQuestionAnswerTypeEnum.growth_notes.value:
                            relationship = QuestionObjectRelationship.objects.filter(answer=answer_obj, deleted=False).first()
                            diary = Diary.objects.filter(pk=relationship.obj_id, deleted=False).first()
                            if diary:
                                return diary.content
                        return answer_obj.answer
    return


def get_miniapp_interview_list(user_id, status, role):
    """
    获取小程序中的辅导记录列表。
    根据提供的用户ID、状态、用户角色和被辅导者用户ID来筛选并排序辅导记录。

    :param user_id: 当前登录的用户ID。
    :param status: 查询辅导列表的状态 0-全部 1-进行中 2-已完成。
    :param role: 用户角色
    :return: 经过筛选和排序的辅导记录查询集。
    """

    # 排除C端未支付的辅导记录
    queryset = ProjectInterview.objects.filter(deleted=False).exclude(order__status=OrderStatusEnum.pending_pay)

    # 排除已经取消的集体辅导
    queryset = queryset.exclude(place_category=ProjectInterviewPlaceCategoryEnum.offline_group_coach,
                                public_attr__status=ATTR_STATUS_INTERVIEW_CANCEL)

    # 根据用户身份信息进行基础查询
    if role == UserRoleEnum.coach:
        queryset = queryset.filter(public_attr__user_id=user_id)
    elif role == UserRoleEnum.trainee_coach:
        queryset = queryset.filter(public_attr__project__isnull=True, public_attr__user_id=user_id)
    elif role == UserRoleEnum.coachee:
        queryset = queryset.filter(public_attr__project__isnull=False, public_attr__target_user_id=user_id)
    elif role == UserRoleEnum.trainee_coachee:
        queryset = queryset.filter(public_attr__project__isnull=True, public_attr__target_user_id=user_id)
    else:
        return queryset

    to_coach_agree = []
    not_completed = []
    completed_or_cancel = []

    # 查询全部辅导/进行中的辅导时返回未确定的和进行中/未开始的辅导记录
    if not status or status == 1:
        to_coach_agree = list(queryset.filter(is_coach_agree=False).exclude(
            public_attr__status=ATTR_STATUS_INTERVIEW_CANCEL).values_list('id', flat=True))

        not_completed = queryset.filter(
            is_coach_agree=True, public_attr__end_time__gte=datetime.datetime.now()
        ).exclude(public_attr__status=ATTR_STATUS_INTERVIEW_CANCEL).order_by(
            'public_attr__start_time')

        if role == UserRoleEnum.coach:
            # 使用多字段去重（教练的集体辅导只返回一张卡片）
            not_completed = multiple_field_distinct(not_completed, ['topic', 'times', 'place', 'place_category',
                                                                    'public_attr.start_time', 'public_attr.end_time'])
        not_completed = list(not_completed.values_list('id', flat=True))

    # 查询全部辅导/已完成的辅导时返回已结束/已取消的辅导记录
    if not status or status == 2:
        completed_or_cancel = list(queryset.filter(
            Q(public_attr__status=ATTR_STATUS_INTERVIEW_CANCEL) | Q(
                ~Q(public_attr__status=ATTR_STATUS_INTERVIEW_CANCEL),
                is_coach_agree=True,
                public_attr__end_time__lt=datetime.datetime.now()
            )).order_by('-public_attr__start_time').values_list('id', flat=True))

    # 合并所有查询结果并排序
    queryset_sort_data = [*to_coach_agree, *not_completed, *completed_or_cancel]

    # 构建排序条件
    order = Case(*[When(pk=order, then=pos) for pos, order in enumerate(queryset_sort_data)])
    queryset = queryset.filter(id__in=queryset_sort_data).order_by(order)

    return queryset


def get_coachee_interview_count(coachee_user_id, coach_user_id=None):
    """
    获取学员的访谈次数
    :param coachee_user_id: 学员用户ID
    :param coach_user_id: （可选）教练用户ID
    :return: 指定学员与教练之间的访谈次数
    """
    # 查询与指定学员相关的访谈记录，排除尚未支付的访谈
    interview = ProjectInterview.objects.filter(
        deleted=False,
        public_attr__target_user_id=coachee_user_id,
        type=INTERVIEW_TYPE_COACHING,
    ).exclude(order__status=OrderStatusEnum.pending_pay)

    # 如果提供了教练ID，则进一步筛选与该教练相关的访谈
    if coach_user_id:
        interview = interview.filter(public_attr__user_id=coach_user_id)

    # 返回访谈次数
    return interview.count()


def get_interview_to_order_discount_amount(interview):
    """
    计算订单中第一个辅导的优惠金额。
    : param interview: 辅导对象
    : return: 优惠金额
    """
    discount_amount = 0

    order = interview.order
    if not order:
        return discount_amount

    # 当前订单只购买了单次辅导，才能触发优惠卷，所以订单只要有优惠金额，就计算并返回。
    if order.discount_amount:
        discount_amount = Decimal(
            str(interview.order.discount_amount)) / Decimal('100').quantize(Decimal('0.00'), rounding=ROUND_UP)

    return discount_amount


def get_interview_to_order_original_price(interview):
    """
    计算订单中单个辅导的金额。
    : param interview: 辅导对象
    : return: 辅导金额
    """
    order = interview.order
    if not order:
        return 0

    total_amount = Decimal(str(order.total_amount))
    order_count = Decimal(str(order.count))
    original_price = (total_amount / order_count / Decimal('100')).quantize(
        Decimal('0.00'), rounding=ROUND_UP)
    return original_price if original_price > 0 else 0


def get_interview_to_order_real_income(payer_amount, coach_user_id, coachee_user_id):
    """
    - 首先检查参数有效性，如果任何参数为空，则返回0。
    - 检查个人客户是否是通过教练邀请加入的。
    - 如果是通过教练邀请的，则实际收入为支付金额的95%。
    - 如果不是通过教练邀请的，则实际收入为支付金额的75%。
    - 计算并返回实际收入和服务费用，保留两位小数。

    计算个人辅导的实际收入。
    :param payer_amount: 单个辅导的收入金额
    :param coach_user_id: 教练的用户ID（整数类型）
    :param coachee_user_id: 个人客户的用户ID（整数类型）
    :return: 实际收入（Decimal类型），以及服务费用
    """

    if not all([payer_amount, coach_user_id, coachee_user_id]):
        return 0, 0

    # 检查个人客户是否是通过教练邀请进来的
    is_coach_invite = coach_public.is_coachee_invitation_from_a_coach(coach_user_id, coachee_user_id)

    # 根据是否是通过教练邀请来计算实际收入
    if is_coach_invite:
        # 如果是通过教练邀请进来的，实际收入为订单支付金额的百分之九十五
        raw_real_income = Decimal(str(payer_amount)) * Decimal('0.95')
    else:
        # 如果不是通过教练邀请进来的，实际收入为订单支付金额的百分之七十五
        raw_real_income = Decimal(str(payer_amount)) * Decimal('0.75')

    real_income = raw_real_income.quantize(Decimal('0.00'), rounding=ROUND_UP)
    service_charge = payer_amount - real_income

    # 保留两位小数并返回实际收入和服务费用
    return real_income, service_charge


def get_interview_coachee_nickname(interview):
    """
    从辅导对象中获取个人用户的昵称。

    :param interview: 辅导对象，包含公共属性
    :return: 如果找到，返回个人用户的昵称；否则用户姓名

    1. 检查辅导对象的公共属性中是否有项目ID。如果有，直接返回用户姓名。
    2. 在PersonalUser表中查找与辅导对象中的目标用户ID匹配的用户记录。
    3. 如果找到了匹配的用户，并且该用户的昵称存在，则返回该昵称。
    4. 如果没有找到匹配的用户或用户没有昵称，则返回用户姓名。
    """

    # 检查是否有项目ID
    if interview.public_attr.project_id:
        return interview.public_attr.target_user.cover_name

    # 查找匹配的PersonalUser记录
    personal_user = PersonalUser.objects.filter(
        user_id=interview.public_attr.target_user_id, deleted=False).first()

    # 如果找到了且有昵称，则返回昵称
    if personal_user and personal_user.nickname:
        return personal_user.nickname

    # 否则返回None
    return interview.public_attr.target_user.cover_name


def get_interview_number_by_obj(obj, describe=False):
    """
    根据给定的对象获取辅导次数。
    参数:
    - obj: 一个对象，代表需要查询辅导次数的具体实体，需要有type, place_category, public_attr等属性。
    - describe: 布尔值，当为True时，返回带有描述性的辅导次数（如“第n次”），否则只返回辅导次数的数字。

    返回值:
    - 辅导次数的字符串表示。如果该对象没有对应的辅导记录，则返回'--'；如果有，则返回辅导次数。
    """

    # 查询并按照辅导时间排序符合条件的辅导记录
    project_interviews = ProjectInterview.objects.filter(
        deleted=False, type=obj.type, place_category=obj.place_category,
        public_attr__target_user=obj.public_attr.target_user,
        public_attr__type=obj.public_attr.type,
        public_attr__project=obj.public_attr.project,
    ).exclude(public_attr__status=ATTR_STATUS_INTERVIEW_CANCEL).order_by('public_attr__start_time')

    count = 0  # 初始化辅导次数
    id_lst = []  # 用于存储查询到的辅导记录ID列表
    for project_interview in project_interviews:
        id_lst.append(project_interview.id)
        count += 1
        # 如果当前遍历到的辅导记录ID与传入的对象ID相同，则停止遍历
        if project_interview.id == obj.id:
            break

    # 如果传入的对象ID不在查询到的ID列表中，说明没有对应的辅导记录，返回'--'
    if obj.id not in id_lst:
        return '--'

    # 根据describe参数决定返回辅导次数的数字还是带有描述性的字符串
    return str(count) if not describe else f'第{count}次'


def create_or_update_interview_diary(interview, diary):
    """
    创建或更新面谈日记。

    参数:
    - interview: 面谈对象，包含公共属性等信息。
    - diary: 日记内容。

    功能:
    该函数首先检查是否存在与给定面谈和用户相关的日记。如果存在，则更新该日记的内容。
    如果不存在，则创建新的公共属性，并创建新的日记。

    返回:
    返回创建或更新的日记对象。
    """
    # 查找是否存在对应的面谈日记，条件是面谈、用户、教练角色且未删除
    exist_diary = Diary.objects.filter(interview=interview, public_attr__user=interview.public_attr.user,
                                       creator_role=ROLE_COACH, deleted=False).first()
    if exist_diary:
        # 如果存在，更新日记内容
        exist_diary.content = diary
        exist_diary.save()
    else:
        # 如果不存在，创建新的公共属性
        public_attr = PublicAttr.objects.create(
            project=interview.public_attr.project, user=interview.public_attr.user, type=ATTR_TYPE_PROJECTNOTE)
        # 创建新的日记
        Diary.objects.create(content=diary, type=DIARY_FROM_RECORD, public_attr=public_attr,
                             interview=interview, creator_role=ROLE_COACH)
    return exist_diary


def create_or_update_interview_project_note(interview, project_note):
    """
    创建或更新面谈项目笔记。

    参数:
    - interview: 面谈对象，包含公共属性等信息。
    - project_note: 项目笔记内容。

    功能:
    该函数首先检查是否存在与给定面谈和用户相关的项目笔记。如果存在，则更新该项目笔记的内容。
    如果不存在，则创建新的公共属性，并创建新的项目笔记。此外，更新客户画像中的教练额外信息。

    返回:
    返回创建或更新的项目笔记对象。
    """
    # 查找是否存在对应的项目笔记，条件是面谈、用户且未删除
    exist_note = ProjectNote.objects.filter(interview=interview, deleted=False,
                                            public_attr__user=interview.public_attr.user).first()
    if exist_note:
        # 如果存在，更新笔记内容
        exist_note.content = project_note
        exist_note.save()
    else:
        # 如果不存在，创建新的公共属性
        public_attr = PublicAttr.objects.create(
            project=interview.public_attr.project, user=interview.public_attr.user, type=ATTR_TYPE_PROJECTNOTE)
        # 创建新的项目笔记
        exist_note = ProjectNote.objects.create(interview=interview, public_attr=public_attr, content=project_note)

    return exist_note


def get_questionnaire_interview_record_rating(
        interview=None, project_id=None, coach_user_id=None, coachee_user_id=None):
    """
    获取问卷面谈记录的评分。

    参数:
    - interview: 面谈对象，包含面谈记录的相关信息（可选）。
    - project_id: 项目ID（可选）。
    - coach_user_id: 教练用户ID（可选）。
    - coachee_user_id: 被辅导用户ID（可选）。

    功能:
    该函数根据提供的参数过滤问卷面谈记录，计算和返回效果度、投入度和满意度的平均评分、总分和次数。

    返回:
    返回一个包含效果度、投入度和满意度评分的字典。
    """
    # 初始化基础查询，过滤未删除的评分问题类型的回答
    base_query = InterviewRecordTemplateAnswer.objects.filter(
        deleted=False, question__type=InterviewRecordTemplateQuestionTypeEnum.rating.value,
    )

    # 根据提供的参数过滤查询
    if interview:
        base_query = base_query.filter(interview=interview)
    if project_id:
        base_query = base_query.filter(interview__public_attr__project_id=project_id)
    if coach_user_id:
        base_query = base_query.filter(interview__public_attr__user_id=coach_user_id)
    if coachee_user_id:
        base_query = base_query.filter(interview__public_attr__target_user_id=coachee_user_id)

    # 定义一个辅助函数计算评分数据
    def calculate_scores(queryset, rating_type):
        filtered_query = queryset.filter(question__rating_type=rating_type)
        avg_score = filtered_query.aggregate(score_avg=Avg('score'))['score_avg']
        sum_score = filtered_query.aggregate(score_sum=Sum('score'))['score_sum']
        count = filtered_query.count()
        return {
            'avg_score': round(avg_score, 1) if avg_score else 0,
            'sum_score': sum_score if sum_score else 0,
            'count': count
        }

    # 计算各类评分
    target_progress_scores = calculate_scores(base_query, InterviewRecordTemplateQuestionRatingTypeEnum.validity.value)
    harvest_scores = calculate_scores(base_query, InterviewRecordTemplateQuestionRatingTypeEnum.Immersion.value)
    satisfaction_scores = calculate_scores(base_query, InterviewRecordTemplateQuestionRatingTypeEnum.satisfaction.value)

    # 将评分数据存储在字典中
    data = {
        'target_progress_avg_score': target_progress_scores['avg_score'],
        'target_progress_sum_score': target_progress_scores['sum_score'],
        'target_progress_count': target_progress_scores['count'],
        'harvest_avg_score': harvest_scores['avg_score'],
        'harvest_sum_score': harvest_scores['sum_score'],
        'harvest_count': harvest_scores['count'],
        'satisfaction_avg_score': satisfaction_scores['avg_score'],
        'satisfaction_sum_score': satisfaction_scores['sum_score'],
        'satisfaction_count': satisfaction_scores['count'],
    }

    return data


def get_interview_record_rating(interview):
    """
    获取面谈记录的评分。

    参数:
    - interview: 面谈对象，包含面谈记录的相关信息。

    功能:
    该函数根据面谈记录的类型，计算和返回效果度、投入度、满意度和教练评分。
    如果面谈记录类型是问卷，则通过模板答案计算平均评分。
    如果面谈记录类型是问答，则直接从面谈记录中获取评分。

    返回:
    返回一个包含效果度、投入度、满意度和教练评分的元组。
    """
    # 初始化评分变量
    target_progress_score = 0
    harvest_score = 0
    satisfaction_score = 0
    coach_score = 0

    if interview.record_type == InterviewRecordTypeEnum.questionnaire.value:

        score_data = get_combined_interview_scores(interview=interview)
        target_progress_score = score_data.get('target_progress_avg_score')
        harvest_score = score_data.get('harvest_avg_score')
        satisfaction_score = score_data.get('satisfaction_avg_score')

        # 获取教练评分
        coach_appraise = CoachAppraise.objects.filter(interview=interview, deleted=False).first()
        if coach_appraise:
            coach_score = coach_appraise.score if coach_appraise.score else 0

    elif interview.record_type == InterviewRecordTypeEnum.question_and_answer.value:
        # 如果是问答类型，直接获取评分
        record = ProjectInterviewRecord.objects.filter(deleted=False, interview=interview).first()
        if record:
            target_progress_score = record.target_progress if record.target_progress else 0
            harvest_score = record.harvest_score if record.harvest_score else 0
            satisfaction_score = record.satisfaction_score if record.satisfaction_score else 0
            coach_score = record.coach_score if record.coach_score else 0

    # 返回所有评分
    return target_progress_score, harvest_score, satisfaction_score, coach_score


def fill_record(instance, is_coach=True):
    # 邮件参数
    page = f"pages_interview/interview/detail"
    scene = f'id={instance.id}&refer=2'
    # 生成短链接
    url_state, short_link = WeChatMiniProgram().get_url_link(page, scene)
    url = get_qr_code(f'{settings.SITE_URL}landing/?p=/{page}&{scene}')
    
    if is_coach:
        instance.coach_record_status = 1
        push_v2_message.delay(instance.public_attr.target_user, 'record_end_msg',
                              param={'target_user': instance.public_attr.user.cover_name,
                                     'start_time': instance.public_attr.start_time.strftime('%Y-%m-%d'),
                                     'qr_code_url': url,
                                     'short_link': short_link}, project_id=instance.public_attr.project_id)
        # push_to_single.delay(instance.public_attr.target_user.user_cid, title='%s完成了%s的教练辅导记录，邀请你查看' % (
        #     instance.public_attr.user.cover_name, instance.public_attr.start_time.strftime('%Y-%m-%d')),
        #                      body='温故而知新，快去获得新发现吧！')

        # 微信服务通知最高20个字
        topic = instance.topic if instance.topic else instance.coachee_topic
        task.send_wechat_msg.delay(settings.END_RECORD_TEMPLATE, instance.public_attr.target_user.openid,
                              value_list={
                                          'thing1': {'value': '%s完成了%s日的辅导记录，请查看' % (
                                              instance.public_attr.user.cover_name,
                                              instance.public_attr.start_time.strftime('%d'))},
                                          'thing2': {'value': topic[:20]},
                                          'time3': {
                                              'value': instance.public_attr.start_time.strftime('%Y年%m月%d日 %H:%M') +
                                               '~' + instance.public_attr.end_time.strftime('%H:%M')}
                              }, url=None)
    else:
        instance.coachee_record_status = 1
        push_v2_message.delay(instance.public_attr.user, 'record_end_msg',
                              param={'target_user': instance.public_attr.target_user.cover_name,
                                     'start_time': instance.public_attr.start_time.strftime('%Y-%m-%d'),
                                     'qr_code_url': url,
                                     'short_link': short_link}, project_id=instance.public_attr.project_id)
        # push_to_single.delay(instance.public_attr.user.user_cid, title='%s完成了%s的教练辅导记录，邀请你查看' % (
        #     instance.public_attr.target_user.cover_name, instance.public_attr.start_time.strftime('%Y-%m-%d')),
        #                      body='温故而知新，快去获得新发现吧！')

    if instance.coach_record_status == 1 and instance.coachee_record_status == 1:
        instance.public_attr.status = ATTR_STATUS_INTERVIEW_COACHING_FINISHED
        instance.public_attr.save()
    instance.save()


def calculate_score(queryset, question_id):
    """
    根据问题ID计算分数。

    参数:
        queryset (QuerySet): 筛选后的查询集
        question_id (int): 问题ID

    返回:
        tuple: 包含总分和选项数量的元组
    """
    # 根据问题ID和选项是否为空过滤出选项标题
    score_query = queryset.filter(deleted=False, question_id=question_id, option__isnull=False).values_list(
        'option__title', flat=True)
    total_score = 0
    # 遍历选项标题并计算总分
    for title in score_query:
        # 移除选项标题中的无效空格
        cleaned_title = str(title).replace(' ', '')
        # 根据评分字典获取对应的分数并累加到总分
        total_score += TITLE_TO_SCORE.get(cleaned_title, 0)
    # 返回总分和选项数量
    return total_score, len(score_query)


# 获取访谈记录评分的主函数
def get_evaluation_option_scores(interview=None, project_id=None, coach_user_id=None, coachee_user_id=None):
    """
    获取访谈记录评分的函数。

    参数:
        interview (int): 访谈ID
        project_id (int): 项目ID
        coach_user_id (int): 教练用户ID
        coachee_user_id (int): 被教练用户ID

    返回:
        dict: 包含各评分项的总分、平均分和计数的字典
    """

    # 初始化查询集，过滤掉已删除的记录
    base_query = InterviewRecordTemplateAnswer.objects.filter(
        deleted=False, option__isnull=False, option__question__template__type__in=[
            InterviewRecordTemplateTypeEnum.one_to_one, InterviewRecordTemplateTypeEnum.project_one_to_one.value
        ])

    # 根据提供的参数进一步过滤查询集
    if interview:
        base_query = base_query.filter(interview=interview)
    if project_id:
        base_query = base_query.filter(interview__public_attr__project_id=project_id)
    if coach_user_id:
        base_query = base_query.filter(interview__public_attr__user_id=coach_user_id)
    if coachee_user_id:
        base_query = base_query.filter(interview__public_attr__target_user_id=coachee_user_id)

    # 获取唯一的模板ID列表
    template_ids = base_query.values_list('question__template_id', flat=True).distinct()

    # 初始化累加变量
    score_data = {
        'satisfaction': {'score': 0, 'count': 0},
        'harvest': {'score': 0, 'count': 0},
        'target_progress': {'score': 0, 'count': 0},
    }

    # 定义模板类型和问题顺序的对应关系
    template_question_mapping = {
        InterviewRecordTemplateTypeEnum.one_to_one.value: [0, 1, 2],
        InterviewRecordTemplateTypeEnum.project_one_to_one.value: [4, 5, 6]
    }

    # 遍历每个模板ID
    for item in template_ids:
        # 获取与模板ID匹配的总模板
        total_template = TotalTemplate.objects.filter(
            (Q(coach_template_id=item) | Q(coachee_template_id=item)),
            write_role=InterviewRecordTemplateRoleEnum.coach_student.value).first()

        # 获取访谈模板中的问题顺序
        interview_template_questions_order = eval(total_template.coachee_template.questions_order)

        question_indices = template_question_mapping.get(total_template.coachee_template.type, [])

        if question_indices:
            # 计算并累加各项评分
            for idx, key in zip(question_indices, score_data.keys()):
                if idx >= len(interview_template_questions_order):
                    continue
                score, count = calculate_score(base_query, interview_template_questions_order[idx])
                score_data[key]['score'] += score
                score_data[key]['count'] += count

    # 计算平均分并保留一位小数
    result = {}
    for key, values in score_data.items():
        avg_score = round(values['score'] / values['count'], 1) if values['count'] > 0 else 0
        result[f'{key}_avg_score'] = avg_score
        result[f'{key}_sum_score'] = values['score']
        result[f'{key}_count'] = values['count']

    return result


def get_combined_interview_scores(interview=None, project_id=None, coach_user_id=None, coachee_user_id=None):
    """
    获取总的访谈记录评分，包括问卷和选项评分。

    参数:
        interview (int): 面谈ID（可选）
        project_id (int): 项目ID（可选）
        coach_user_id (int): 教练用户ID（可选）
        coachee_user_id (int): 被辅导用户ID（可选）

    返回:
        dict: 包含总分、总数和总平均分的字典
    """
    # 获取问卷评分数据
    rating_score_data = get_questionnaire_interview_record_rating(
        interview=interview, project_id=project_id, coach_user_id=coach_user_id, coachee_user_id=coachee_user_id)

    # 获取选项评分数据
    option_score_data = get_evaluation_option_scores(
        interview=interview, project_id=project_id, coach_user_id=coach_user_id, coachee_user_id=coachee_user_id)

    # 初始化总数据字典，确保所有需要的键值都有初始值
    total_data = {
        'target_progress_sum_score': 0,
        'target_progress_count': 0,
        'target_progress_avg_score': 0,
        'harvest_sum_score': 0,
        'harvest_count': 0,
        'harvest_avg_score': 0,
        'satisfaction_sum_score': 0,
        'satisfaction_count': 0,
        'satisfaction_avg_score': 0,
    }

    # 初始化total_data字典
    total_data = {}
    score_keys = ['target_progress', 'harvest', 'satisfaction']

    # 合并总分和计数
    for key in score_keys:
        sum_key = f"{key}_sum_score"
        count_key = f"{key}_count"

        total_data[sum_key] = rating_score_data.get(sum_key, 0) + option_score_data.get(sum_key, 0)
        total_data[count_key] = rating_score_data.get(count_key, 0) + option_score_data.get(count_key, 0)

    # 计算平均分
    for key in score_keys:
        avg_key = f"{key}_avg_score"
        sum_key = f"{key}_sum_score"
        count_key = f"{key}_count"

        total_sum = total_data.get(sum_key, 0)
        total_count = total_data.get(count_key, 0)

        total_data[avg_key] = round(total_sum / total_count, 1) if total_count > 0 else 0

    return total_data


def get_filled_interview_record_scores(interview=None, project_id=None, coach_user_id=None, coachee_user_id=None):
    """
    获取客户和教练已填写的面谈记录数据。

    参数:
        interview (int): 面谈ID（可选）
        project_id (int): 项目ID（可选）
        coach_user_id (int): 教练用户ID（可选）
        coachee_user_id (int): 被辅导用户ID（可选）

    返回:
        dict: 包含总分和计数的字典
    """
    # 获取客户和教练已填写的面谈记录数据
    base_query = ProjectInterviewRecord.objects.filter(
        deleted=False, interview__coachee_record_status=True)

    # 根据提供的参数进一步过滤查询集
    if interview:
        base_query = base_query.filter(interview=interview)
    if project_id:
        base_query = base_query.filter(interview__public_attr__project_id=project_id)
    if coach_user_id:
        base_query = base_query.filter(interview__public_attr__user_id=coach_user_id)
    if coachee_user_id:
        base_query = base_query.filter(interview__public_attr__target_user_id=coachee_user_id)

    data = {
        'target_progress_sum_score': base_query.aggregate(total=Sum('target_progress'))['total'] or 0,
        'harvest_sum_score': base_query.aggregate(total=Sum('harvest_score'))['total'] or 0,
        'satisfaction_sum_score': base_query.aggregate(total=Sum('satisfaction_score'))['total'] or 0,
        'count': base_query.count(),
    }
    return data


def get_total_interview_scores(interview=None, project_id=None, coach_user_id=None, coachee_user_id=None):
    """
    获取总的访谈记录评分，包括问卷和选项评分。

    参数:
        interview (int): 面谈ID（可选）
        project_id (int): 项目ID（可选）
        coach_user_id (int): 教练用户ID（可选）
        coachee_user_id (int): 被辅导用户ID（可选）

    返回:
        dict: 包含总分、总数和总平均分的字典
    """
    # 获取问卷面谈记录评分数据
    questionnaire_score_data = get_combined_interview_scores(
        interview=interview, project_id=project_id, coach_user_id=coach_user_id, coachee_user_id=coachee_user_id)

    # 获取客户和教练已填写的面谈记录数据
    record_score_data = get_filled_interview_record_scores(
        interview=interview, project_id=project_id, coach_user_id=coach_user_id, coachee_user_id=coachee_user_id)

    # 合并数据
    data = {
        'target_progress_sum_score': questionnaire_score_data['target_progress_sum_score'] + record_score_data[
            'target_progress_sum_score'],
        'harvest_sum_score': questionnaire_score_data['harvest_sum_score'] + record_score_data['harvest_sum_score'],
        'satisfaction_sum_score': questionnaire_score_data['satisfaction_sum_score'] + record_score_data[
            'satisfaction_sum_score'],
        'target_progress_count': questionnaire_score_data['target_progress_count'] + record_score_data['count'],
        'harvest_count': questionnaire_score_data['harvest_count'] + record_score_data['count'],
        'satisfaction_count': questionnaire_score_data['satisfaction_count'] + record_score_data['count'],
    }

    # 计算总平均分
    data['target_progress_avg_score'] = round(
        data['target_progress_sum_score'] / data['target_progress_count'], 1) if data['target_progress_count'] > 0 else 0
    data['harvest_avg_score'] = round(
        data['harvest_sum_score'] / data['harvest_count'], 1) if data['harvest_count'] > 0 else 0
    data['satisfaction_avg_score'] = round(
        data['satisfaction_sum_score'] / data['satisfaction_count'], 1) if data['satisfaction_count'] > 0 else 0

    return data


def get_to_c_not_filled_in_interview_record():
    """
    获取C端，结束时间是昨天，客户未填写的辅导数据
    """
    # 获取昨天的日期
    start_date = datetime.datetime.now() - datetime.timedelta(days=1)
    # 过滤符合条件的ProjectInterview对象
    interviews = ProjectInterview.objects.filter(
        order__isnull=False,  # 订单不为空
        order__status=OrderStatusEnum.paid,  # 订单状态为已支付
        deleted=False,  # 没有被删除
        coachee_record_status=False,  # 客户未填写辅导记录
        place_category=ProjectInterviewPlaceCategoryEnum.online_one_to_one.value,  # 辅导地点为线上一对一
        is_coach_agree=True,  # 教练同意
        public_attr__end_time__date=start_date.date(),  # 辅导结束时间为昨天
        public_attr__project__isnull=True  # 辅导项目为空
    ).exclude(public_attr__status=ATTR_STATUS_INTERVIEW_CANCEL).all()
    return interviews


def create_interview_meeting(
        interview_id, admin_userid, title, start_time, duration,
        channel_type=InterviewMeetingChannelTypeEnum.tencent_meeting.value):
    """
    创建辅导会议
    :param interview_id: 面试ID
    :param admin_userid: 管理员UserID
    :param title: 会议标题
    :param start_time: 会议开始时间
    :param duration: 会议时长
    :param channel_type: 会议渠道类型，默认为腾讯会议
    :return: 成功返回面试会议对象，失败返回None
    """
    if start_time < int(pendulum.now().timestamp()):
        return

    # 调用WorkWechat类中的create_meeting方法创建会议
    create_meeting_status, data = WorkWechat().create_meeting(admin_userid, title, start_time, duration)
    if create_meeting_status:
        # 获取会议ID
        meeting_id = data.get('meetingid')

        # 创建InterviewMeeting对象
        interview_meeting = InterviewMeeting.objects.create(
            interview_id=interview_id, meeting_id=meeting_id, channel_type=channel_type)

        # 获取会议详情
        meeting_details_status, data = WorkWechat().get_meeting_details(meeting_id)
        if meeting_details_status:
            # 获取会议代码
            meeting_code = data.get('meeting_code')
            interview_meeting.meeting_code = meeting_code
            interview_meeting.save()

        AliyunSlsLogLayout().send_third_api_log(
            message='企业微信-创建腾讯会议',
            content={"interview_id": interview_id, "wx_user_id": admin_userid,
                     "title": title, 'channel_type': channel_type, 'start_time': start_time}
        )
        return interview_meeting
    return


def update_interview_meeting(status, meeting_id, title=None, start_time=None, duration=None):
    """
    修改辅导会议
    """
    # 获取会议详情
    meeting_details_status, data = WorkWechat().get_meeting_details(meeting_id)
    # 会议的状态，1：待开始，2：会议中，3：已结束，4：已取消，5：已过期
    meeting_status = data.get('status')

    # 除了待开始的，其他状态的会议不可操作。
    if meeting_status == 1:
        if status == UPDATE_INTERVIEW_MEETING:  # 修改辅导会议信息

            # 辅导时间大于当前时间
            if start_time > int(pendulum.now().timestamp()):
                # 修改会议时间。
                WorkWechat().update_meeting(
                    meeting_id, title, start_time, duration)
            # 辅导时间小于当前时间，取消会议，不创建新的会议。
            else:
                WorkWechat().cancel_meeting(meeting_id)
        elif status == CANCEL_INTERVIEW_MEETING:  # 取消辅导会议信息
            WorkWechat().cancel_meeting(meeting_id)
    # 会议不可操作，修改=新建，取消=无需操作
    else:
        # 修改辅导会议信息 = 移除之前的会议，创建新的会议
        if status == UPDATE_INTERVIEW_MEETING:
            interview_meeting = InterviewMeeting.objects.filter(
                channel_type=InterviewMeetingChannelTypeEnum.tencent_meeting.value,
                deleted=False, meeting_id=meeting_id).first()
            if interview_meeting:
                # 删除之前的辅导会议
                interview_meeting.deleted = True
                interview_meeting.save()

                # 创建新的辅导会议
                interview = interview_meeting.interview
                coach_user_id = interview.public_attr.user_id
                # 获取教练企业微信
                work_wechat_user = WorkWechatUser.objects.filter(
                    wx_user_id__isnull=False, user_id=coach_user_id, deleted=False).first()
                if work_wechat_user:
                    # 创建会议
                    create_interview_meeting(
                        interview.id, work_wechat_user.wx_user_id, "教练辅导", start_time, duration,
                        channel_type=InterviewMeetingChannelTypeEnum.tencent_meeting.value)
    return


def get_interview_meeting_info(interview):
    """
    获取辅导会议信息
    :param interview: 面试对象
    :return: 返回面试会议信息的字典
    """

    # 获取未被删除的第一个面试会议
    meeting = interview.interview_meeting.filter(deleted=False).first()
    if meeting:
        # 返回会议信息
        data = {
            'id': str(meeting.id),
            'type': str(meeting.channel_type),
            'meeting_id': meeting.meeting_id,
            'meeting_code': meeting.meeting_code,
        }
        return data
    return


def get_one_to_one_interview_detail_status(interview, is_coach):
    result = {
        "head": InterviewDetailHeadEnum.one_to_one_not_start_or_ongoing.value,
        "content": InterviewDetailContentEnum.contact_detail.value,
        "button": [InterviewDetailButtonEnum.interview_record.value],  # 默认展示“辅导记录”按钮
    }

    # 辅导已取消
    if interview.public_attr.status == ATTR_STATUS_INTERVIEW_CANCEL:
        result["content"] = InterviewDetailContentEnum.not_show.value
        # 已取消不展示辅导记录按钮
        result["button"].remove(InterviewDetailButtonEnum.interview_record.value)

    # 待教练确认
    elif not interview.is_coach_agree:
        result["button"].append(InterviewDetailButtonEnum.update_interview_time.value)
        # 待确认不展示辅导记录按钮
        result["button"].remove(InterviewDetailButtonEnum.interview_record.value)

        # 如果是教练增加“同意辅导邀请”按钮
        if is_coach:
            result["button"].append(InterviewDetailButtonEnum.agree_interview.value)
        # 如果是客户增加“取消辅导”按钮
        else:
            result["button"].append(InterviewDetailButtonEnum.cancel_interview.value)

    # 未开始
    elif interview.public_attr.start_time > datetime.datetime.now() and interview.public_attr.status != ATTR_STATUS_INTERVIEW_CANCEL:
        # 展示“修改辅导时间”按钮，“辅导记录”按钮
        result["button"].append(InterviewDetailButtonEnum.update_interview_time.value)

        # 线上辅导，展示“进入辅导”按钮
        if interview.place_type == ProjectInterviewPlaceTypeEnum.online.value:
            if interview.place is None or interview.place == '' or interview.place == '腾讯会议':
                result["button"].append(InterviewDetailButtonEnum.access_interview.value)

        # 被教练者增加“取消辅导”按钮
        if not is_coach:
            result["button"].append(InterviewDetailButtonEnum.cancel_interview.value)
            # 教练形式等于影子观察，客户不展示“辅导记录”按钮
            if interview.interview_subject == InterviewSubjectEnum.shadow_observation.value:
                result['button'].remove(InterviewDetailButtonEnum.interview_record.value)

    # 进行中
    elif interview.public_attr.start_time < datetime.datetime.now() < interview.public_attr.end_time \
            and interview.public_attr.status != ATTR_STATUS_INTERVIEW_CANCEL:

        # 如果是教练
        if is_coach:
            # 如果辅导记录未填写
            if not interview.coach_record_status:
                # 未填写，并且是线上辅导，展示“进入辅导”按钮
                if interview.place_type == ProjectInterviewPlaceTypeEnum.online.value:
                    if interview.place is None or interview.place == '' or interview.place == '腾讯会议':
                        result["button"].append(InterviewDetailButtonEnum.access_interview.value)
                # 客户也未填写，展示“修改辅导时间”按钮
                if not interview.coachee_record_status:
                    result['button'].append(InterviewDetailButtonEnum.update_interview_time.value)
        else:
            # 教练形式等于影子观察，客户不展示“辅导记录”按钮
            if interview.interview_subject == InterviewSubjectEnum.shadow_observation.value:
                result['button'].remove(InterviewDetailButtonEnum.interview_record.value)

            # 如果辅导记录未填写
            if not interview.coachee_record_status:
                # 未填写，并且是线上辅导，展示“进入辅导”按钮
                if interview.place_type == ProjectInterviewPlaceTypeEnum.online.value:
                    if interview.place is None or interview.place == '' or interview.place == '腾讯会议':
                        result["button"].append(InterviewDetailButtonEnum.access_interview.value)
                # 教练也未填写，展示“修改辅导时间”按钮
                if not interview.coach_record_status:
                    result["button"].append(InterviewDetailButtonEnum.update_interview_time.value)

    # 完成
    elif interview.public_attr.status != ATTR_STATUS_INTERVIEW_CANCEL and interview.public_attr.end_time < datetime.datetime.now():
        # 教练形式等于影子观察，客户不展示“辅导记录”按钮
        if not is_coach:
            if interview.interview_subject == InterviewSubjectEnum.shadow_observation.value:
                result['button'].remove(InterviewDetailButtonEnum.interview_record.value)

        if not interview.coachee_record_status and not interview.coach_record_status:  # 双方未完成 可以修改时间
            result["button"].append(InterviewDetailButtonEnum.update_interview_time.value)

    return result


def get_chemical_interview_detail_status(interview, is_coach):
    """
    获取化学面谈详情状态
    :param interview: 辅导对象
    :param is_coach: 是否是教练
    :return: 返回面试详情状态的字典

    注意点：
    化学面谈客户修改辅导时间时按钮，需要看当前时间是否在配置的化学面谈开始结束时间内
    """

    # 定义最基础的展示
    result = {
        "head": InterviewDetailHeadEnum.one_to_one_not_start_or_ongoing.value,
        "content": InterviewDetailContentEnum.contact_detail.value,
        "button": [],  # 默认不展示按钮
    }
    # 教练默认展示“辅导记录”按钮
    if is_coach:
        result["button"] = [InterviewDetailButtonEnum.interview_record.value]

    # 辅导已取消
    if interview.public_attr.status == ATTR_STATUS_INTERVIEW_CANCEL:
        result["content"] = InterviewDetailContentEnum.not_show.value
        # 已取消不展示辅导记录按钮
        result["button"] = []

    # 未开始
    elif interview.public_attr.start_time > datetime.datetime.now() and interview.public_attr.status != ATTR_STATUS_INTERVIEW_CANCEL:
        result["button"].append(InterviewDetailButtonEnum.update_interview_time.value)

        # 线上辅导，展示“进入辅导”按钮
        if interview.place_type == ProjectInterviewPlaceTypeEnum.online.value:
            result["button"].append(InterviewDetailButtonEnum.access_interview.value)

    # 进行中
    elif interview.public_attr.start_time < datetime.datetime.now() < interview.public_attr.end_time \
            and interview.public_attr.status != ATTR_STATUS_INTERVIEW_CANCEL:
        # 如果是教练
        if is_coach:
            # 如果辅导记录未填写
            if not interview.coach_record_status:
                # 未填写，并且是线上辅导，展示“进入辅导”按钮
                if interview.place_type == ProjectInterviewPlaceTypeEnum.online.value:
                    result["button"].append(InterviewDetailButtonEnum.access_interview.value)

                # 客户也未填写，展示“修改辅导时间”按钮
                if not interview.coachee_record_status:
                    result['button'].append(InterviewDetailButtonEnum.update_interview_time.value)
        else:
            # 如果辅导记录未填写
            if not interview.coachee_record_status:
                # 未填写，并且是线上辅导，展示“进入辅导”按钮
                if interview.place_type == ProjectInterviewPlaceTypeEnum.online.value:
                    result["button"].append(InterviewDetailButtonEnum.access_interview.value)

                # 教练也未填写，并且在可选时间范围内，展示“修改辅导时间”按钮
                if not interview.coach_record_status and  is_interview_update_date_button(interview):
                    result["button"].append(InterviewDetailButtonEnum.update_interview_time.value)

    # 完成
    elif interview.public_attr.status != ATTR_STATUS_INTERVIEW_CANCEL and interview.public_attr.end_time < datetime.datetime.now():
        if not interview.coachee_record_status and not interview.coach_record_status:
            # 双方未完成 教练可以修改时间,用户根据当前化学面谈访谈的结束时间控制是否能修改时间
            if is_coach or is_interview_update_date_button(interview):
                result["button"].append(InterviewDetailButtonEnum.update_interview_time.value)

    return result


def get_stakeholder_interview_detail_status(interview, is_coach):
    """
    获取改变观察反馈详情状态
    :param interview: 辅导对象
    :param is_coach: 是否是教练
    :return: 返回面试详情状态的字典

    注意点：
    利益相关者修改辅导时间按钮，需要看当前时间是否在配置的利益相关者开始结束时间内
    利益相关者不用填写辅导记录， 已完成的利益相关者不展示辅导记录填写按钮

    """
    result = {
        "head": InterviewDetailHeadEnum.one_to_one_not_start_or_ongoing.value,
        "content": InterviewDetailContentEnum.contact_detail.value,
        "button": [InterviewDetailButtonEnum.interview_record.value],  # 默认展示“辅导记录”按钮,
    }

    # 辅导已取消
    if interview.public_attr.status == ATTR_STATUS_INTERVIEW_CANCEL:
        result["content"] = InterviewDetailContentEnum.not_show.value

    # 未开始
    elif interview.public_attr.start_time > datetime.datetime.now() and interview.public_attr.status != ATTR_STATUS_INTERVIEW_CANCEL:
        result["button"].append(InterviewDetailButtonEnum.update_interview_time.value)

        # 线上辅导，展示“进入辅导”按钮
        if interview.place_type == ProjectInterviewPlaceTypeEnum.online.value:
            result["button"].append(InterviewDetailButtonEnum.access_interview.value)

    # 进行中
    elif interview.public_attr.start_time < datetime.datetime.now() < interview.public_attr.end_time \
            and interview.public_attr.status != ATTR_STATUS_INTERVIEW_CANCEL:
        # 如果是教练
        if is_coach:
            # 如果辅导记录位填写
            if not interview.coach_record_status:
                # 未填写，并且是线上辅导，展示“进入辅导”按钮
                if interview.place_type == ProjectInterviewPlaceTypeEnum.online.value:
                    result["button"].append(InterviewDetailButtonEnum.access_interview.value)
                # 客户也未填写，展示“修改辅导时间”按钮
                if not interview.coachee_record_status:
                    result['button'].append(InterviewDetailButtonEnum.update_interview_time.value)
        else:
            # 利益相关者访谈，客户不展示辅导记录按钮
            result["button"].remove(InterviewDetailButtonEnum.interview_record.value)
            if not interview.coachee_record_status:
                # 未填写，并且是线上辅导，展示“进入辅导”按钮
                if interview.place_type == ProjectInterviewPlaceTypeEnum.online.value:
                    result["button"].append(InterviewDetailButtonEnum.access_interview.value)

                # 教练也未填写，并且在可选时间范围内，展示“修改辅导时间”按钮
                if not interview.coach_record_status and is_interview_update_date_button(interview):
                    result["button"].append(InterviewDetailButtonEnum.update_interview_time.value)

    # 完成
    elif interview.public_attr.status != ATTR_STATUS_INTERVIEW_CANCEL and interview.public_attr.end_time < datetime.datetime.now():
        # 教练默认展示“辅导记录”按钮，客户不展示
        if not is_coach:
            result["button"].remove(InterviewDetailButtonEnum.interview_record.value)
        # 双方未完成 教练可以修改时间,用户根据当前利益相关者访谈的结束时间控制是否能修改时间
        if not interview.coachee_record_status and not interview.coach_record_status:
            if is_coach or is_interview_update_date_button(interview):
                result["button"].append(InterviewDetailButtonEnum.update_interview_time.value)
    return result


def coach_interview_time_validate(coach_user_id, start_time, end_time, interview_id=None):
    """
    检查指定教练在给定的时间段内是否已有预约的辅导课程。

    :param coach_user_id: 教练的用户ID
    :param start_time: 辅导课程开始时间的字符串表示
    :param end_time: 辅导课程结束时间的字符串表示
    :param interview_id: 需要排除的辅导id
    :return: 如果在指定时间段内存在冲突的辅导，则返回第一个冲突的辅导信息；否则返回None
    """

    # 将输入的时间字符串转换为本地时区的datetime对象
    # 并返回一个带有时区信息的datetime对象。为了进行数据库查询，我们使用`.naive()`方法移除时区信息，
    # 因为Django默认不存储时区信息的datetime。
    start_time_obj = datetime_change_utc(start_time).naive()
    end_time_obj = datetime_change_utc(end_time).naive()

    project_interview = ProjectInterview.objects.filter(
        # 使用Q对象构建查询条件，以查找与给定时间范围有交集的任何预约
        # 这里我们查找所有开始时间在给定结束时间之前，且结束时间在给定开始时间之后的预约
        Q(public_attr__start_time__lt=end_time_obj, public_attr__end_time__gt=start_time_obj) |
        Q(public_attr__start_time__gte=start_time_obj, public_attr__end_time__lte=end_time_obj),
        deleted=False, public_attr__user_id=coach_user_id).exclude(
        public_attr__status=ATTR_STATUS_INTERVIEW_CANCEL).exclude(id=interview_id).order_by('public_attr__start_time')

    # 如果存在任何冲突的预约，返回第一个预约的信息
    if project_interview.exists():
        return project_interview.first()
    return


def question_id_to_answer_type(question_id):
    """
    2.36版本新增三个题目类型 项目信息 企业信息 客户信息
    旧版小程序无此类型的处理逻辑，特作兼容处理。
    1 - 后端接口返回时，将指定题目id转成普通问答文本类型。
    2 - 小程序传参，进行数据处理时，指定题目id转成对应类型处理。

    question_id： 需要转换的题目id
    """
    # TODO 版本新增三个题目类型 项目信息 企业信息 客户信息的特殊兼容处理
    if question_id == 315:
        answer_type = InterviewRecordTemplateQuestionAnswerTypeEnum.company_info.value
    elif question_id == 316:
        answer_type = InterviewRecordTemplateQuestionAnswerTypeEnum.project_info.value
    elif question_id == 317:
        answer_type = InterviewRecordTemplateQuestionAnswerTypeEnum.customer_info.value
    else:
        answer_type = None

    return answer_type


def get_interview_type_describe(interview):
    """
    获取辅导面试的类型描述。

    :param interview: 辅导面试对象
    :return: 辅导面试的类型描述，如果没有找到合适的描述则返回None
    """
    type_describe = None

    # 检查面试地点类别是否为“线上一对一”
    if interview.place_category == ProjectInterviewPlaceCategoryEnum.online_one_to_one.value:
        # 根据面试类型设置描述
        if interview.type == ProjectInterviewTypeEnum.formal_interview.value:
            type_describe = InterviewSubjectEnum.get_display(interview.interview_subject)
        elif interview.type == ProjectInterviewTypeEnum.chemical_interview.value:
            type_describe = ProjectInterviewTypeEnum.get_display(interview.type)
        elif interview.type == ProjectInterviewTypeEnum.stakeholder_interview.value:
            type_describe = ProjectInterviewTypeEnum.get_display(interview.type)

    # 检查面试地点类别是否为“线下小组辅导”
    elif interview.place_category == ProjectInterviewPlaceCategoryEnum.offline_group_coach.value:
        # 查找相关的小组辅导对象
        group_coach = GroupCoach.objects.filter(interview=interview, deleted=False).first()
        if group_coach:
            # 获取项目小组辅导的类型并设置描述
            project_group_coach = group_coach.project_group_coach
            if project_group_coach and project_group_coach.type:
                type_describe = GroupCoachTypeEnum.get_display(project_group_coach.type)

    return type_describe
