import redis
import random

from datetime import datetime
from django.conf import settings

from wisdom_v2.common import activity_public, order_public
from wisdom_v2.enum.service_content_enum import ActivityCoachStatusEnum
from wisdom_v2.models import Coach
from wisdom_v2.models_file import ActivityCoach, Activity, ActivityInterview
from wisdom_v2.views.constant import ATTR_STATUS_INTERVIEW_CANCEL

data_redis = redis.Redis.from_url(settings.DATA_REDIS)


def add_activity_coach(activity, activity_coach_list):
    """
    添加活动教练
    :param activity: 活动对象
    :param activity_coach_list: 需要添加的教练列表
    :return: None
    """
    for item in activity_coach_list:
        coach_id = item.get('coach_id')
        status = item.get('status')
        coach = Coach.objects.filter(id=coach_id, deleted=False).first()
        resume = coach.resumes.filter(deleted=False, is_customization=False).first()
        ActivityCoach.objects.create(
            activity_id=activity.id,
            coach_id=coach.id,
            resume_id=resume.id if resume else None,
            status=status,
            additional_count=random.randint(5, 50),
        )


def update_activity_coach_query_data(activity_coach, user_id):
    """
    更新教练关联活动的查询数据。

    :param activity_coach: 教练关联活动的对象
    :param user_id: 当前浏览的用户ID
    """

    # 获取当前所有浏览的用户ID列表，并转化为集合进行去重操作
    look_all_user_set = activity_coach.look_all_user if activity_coach.look_all_user else []
    # 如果用户未在浏览列表中，则添加
    if user_id and user_id not in look_all_user_set:
        look_all_user_set.append(user_id)
        activity_coach.look_all_user = list(look_all_user_set)

    # 更新浏览次数
    look_all_count = activity_coach.look_all_count
    activity_coach.look_all_count = look_all_count + 1

    # 保存更新到数据库
    activity_coach.save()


# 基于给定的邀请和简历来获取活动教练的数据。
def invite_to_activity_coach_data(invite, resume):

    # 使用ActivityCoach模型查询相关数据。查询的条件包括：
    # 2. 简历与提供的简历相匹配。
    # 3. 活动教练没有被删除。
    # 4. 活动的开始日期应小于或等于今天的日期。
    # 5. 活动的结束日期应大于或等于今天的日期。
    # 6. 活动没有被删除。
    # 7. 活动教练的状态应为“已加入”或“免邀请”。
    activity_coach = ActivityCoach.objects.filter(
        activity__id=invite.object_id,
        resume=resume,
        activity__deleted=False,
        activity__start_date__lte=datetime.now().date(),
        activity__end_date__gte=datetime.now().date(),
        deleted=False,
        status__in=[ActivityCoachStatusEnum.joined.value,
                    ActivityCoachStatusEnum.not_invitation.value]
    )
    # 如果查询结果存在，返回第一个结果。
    if activity_coach.exists():
        return activity_coach.first()

    # 如果没有符合条件的结果，返回None。
    return


def get_coach_activity_data(activity_id, coach, coachee_user_id):
    """
    获取指定教练关联的活动详细信息。

    :param activity_id: 活动的 ID。
    :param coach: 教练对象。
    :param coachee_user_id: 被教练者用户标识。（简历页面可以不登录查询，coachee_user_id可能为空）
    :return: 包含活动详细信息的字典，如果活动不存在则返回 {}。
    """
    # 根据 ID 查找未删除的活动
    activity = Activity.objects.filter(id=str(activity_id), deleted=False).first()
    # 教练是否参加活动
    is_participate = ActivityCoach.objects.filter(activity_id=activity.id, coach_id=coach.id, deleted=False).exists()

    if activity:
        # 如果活动存在，构造包含活动信息的字典
        data = {
            'id': str(activity.id),  # 活动标识
            'theme': activity.theme,  # 活动主题
            'start_date': activity.start_date,  # 活动开始日期
            'price': activity.display_price,  # 活动价格
            'end_date': activity.end_date,  # 活动结束日期
            'interview_start_date': activity.interview_start_date,  # 辅导开始日期
            'interview_end_date': activity.interview_end_date,  # 辅导结束日期
            'status': activity_public.get_status_display(activity),  # 活动状态
            'is_participate': is_participate,
            'is_residue_count': True,  # 默认活动可预约
            'not_filled_interview_time': None,  # 默认辅导记录都填写完毕, 无时间
            'not_filled_interview_coach_name': None  # 默认辅导记录都填写完毕, 无名称
        }
        if coachee_user_id:
            # 用户在当前活动中是否有剩余可用的的辅导次数
            is_residue_count = order_public.activity_anticipation_allowed(coachee_user_id, activity)
            data['is_residue_count'] = is_residue_count

            # 用户是否存在未填写的辅导记录
            not_filled_interview = ActivityInterview.objects.filter(
                deleted=False, interview__deleted=False,  # 没有删除
                interview__public_attr__target_user_id=coachee_user_id,  # 辅导用户是当前用户
                interview__coachee_record_status=False,  # 用户没填写辅导记录
            ).exclude(
                interview__public_attr__status=ATTR_STATUS_INTERVIEW_CANCEL,  # 排除已取消的
            ).first()
            if not_filled_interview:
                public_attr = not_filled_interview.interview.public_attr
                data['not_filled_interview_time'] = (f'{public_attr.start_time.strftime("%Y-%m-%d %H:%M")}-'
                                                     f'{public_attr.end_time.strftime("%H:%M")}')
                data['not_filled_interview_coach_name'] = public_attr.user.cover_name

        return data
    # 如果未找到活动，返回 None
    return



