from wisdom_v2.models import Coach
from wisdom_v2.models_file import PublicCoursesCoach


def add_public_courses_coach(public_courses, add_coach_list):
    """
    添加公开课教练
    :param public_courses: 公开课对象
    :param add_coach_list: 教练id列表
    :return: None
    """

    public_courses_coach = []
    for item in add_coach_list:
        coach = Coach.objects.filter(id=item, deleted=False).first()
        resume = coach.resumes.filter(deleted=False, is_customization=False).first()
        public_courses_coach.append(PublicCoursesCoach(
            public_courses_id=public_courses.id,
            coach_id=coach.id,
            resume_id=resume.id if resume else None,
        ))
    PublicCoursesCoach.objects.bulk_create(public_courses_coach)
