from django.db.models import Count, Subquery, OuterRef

from wisdom_v2.models_file import ActivityInterview
from wisdom_v2.views.constant import ATTR_STATUS_INTERVIEW_CANCEL


def get_pay_user_count(activity, coach_user, is_set=None):
    """
    获取活动支付用户数
    :param activity: 活动对象
    :param coach_user: 教练用户列表 教练user对象
    :param is_set: 是否去重 (根据用户去重复)
    :return: int 活动支付用户数
    """
    activity_interview = ActivityInterview.objects.filter(
        activity=activity, deleted=False, interview__deleted=False,
    ).exclude(interview__public_attr__status=ATTR_STATUS_INTERVIEW_CANCEL)
    if coach_user:
        activity_interview = activity_interview.filter(interview__public_attr__user__in=coach_user)

    if is_set:
        activity_interview = activity_interview.values("interview__public_attr__target_user").distinct()

    return activity_interview.count()


def get_interview_user_count(activity, coach_user, is_set=None):
    """
    获取活动辅导用户数
    :param activity: 活动对象
    :param coach_user: 教练用户列表 教练user对象
    :param is_set: 是否去重 (根据用户去重复)
    :return: int 活动辅导用户数
    """
    activity_interview = ActivityInterview.objects.filter(
        activity=activity, deleted=False, interview__deleted=False,
        interview__coach_record_status=True,
    ).exclude(interview__public_attr__status=ATTR_STATUS_INTERVIEW_CANCEL)

    if coach_user:
        activity_interview = activity_interview.filter(interview__public_attr__user__in=coach_user)
    if is_set:
        activity_interview = activity_interview.values("interview__public_attr__target_user").distinct()

    return activity_interview.count()


def get_user_counts(activity, user_ids, is_set, include_interview):
    """
    获取活动用户数
    :param activity: 活动对象
    :param user_ids: 用户id列表
    :param include_interview: 是否包含辅导
    :return: 用户计数的字典
    """
    base_query = ActivityInterview.objects.filter(
        activity=activity,
        deleted=False,
        interview__deleted=False,
        interview__public_attr__user__in=user_ids
    )

    if include_interview:
        base_query = base_query.filter(interview__coach_record_status=True)

    if is_set:
        base_query = base_query.values('interview__public_attr__user').annotate(
            count=Count('interview__public_attr__target_user', distinct=True)
        )
    else:
        base_query = base_query.values('interview__public_attr__user').annotate(
            count=Count('interview__public_attr__user')
        )

    return {item['interview__public_attr__user']: item['count'] for item in base_query}
