from datetime import datetime


def get_status_display(project_settlement):
    """
    获取项目结算订单状态
    :param project_settlement: 对象
    :return: 结算订单状态 1-未开始 2-进行中 3-已结束
    """
    # 获取当前日期和时间
    current_date = datetime.now().date()
    current_hour = datetime.now().hour

    status = None

    # 判断结算订单是否未开始
    if ((project_settlement.work_start_time.date() > current_date) or
          (project_settlement.work_start_time.date() == current_date and project_settlement.work_start_time.hour > current_hour)):
        status = 1

    # 判断结算订单是否进行中
    elif ((project_settlement.work_start_time.date() < current_date or
          (project_settlement.work_start_time.date() == current_date and project_settlement.work_start_time.hour <= current_hour)) and
          (project_settlement.work_end_time.date() > current_date or
           (project_settlement.work_end_time.date() == current_date and project_settlement.work_end_time.hour > current_hour))):
        status = 2

    # 判断结算订单是否已结束
    elif ((project_settlement.work_end_time.date() < current_date) or
          (project_settlement.work_end_time.date() == current_date and project_settlement.work_end_time.hour <= current_hour)):
        status = 3

    return status
