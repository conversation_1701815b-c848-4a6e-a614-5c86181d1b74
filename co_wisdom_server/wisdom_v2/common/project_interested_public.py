from wisdom_v2.models import ProjectInterested


def add_project_interested(project_member, company_member, relation, position, exists_company_member=False):
    """
    被教练者添加利益相关者
    project_member: 被教练者
    company_member: 利益相关者
    relation: 关系
    add_position: 被教练者添加利益相关者时填入的岗位
    exists_company_member: 是否为已存在的company_member，客户添加利益相关者如果查出已存在的company_member将客户输入的岗位
    添加到查出的company_member的岗位后面
    """
    err = None
    project_interested = None
    if ProjectInterested.objects.filter(interested_id=company_member.user_id, master_id=project_member.user_id,
                                        project_id=project_member.project_id, deleted=False).exists():
        err = '当前利益相关者已关联'
    else:
        if exists_company_member:
            if company_member.position != position:
                company_member.position = company_member.position + '/' + position
                company_member.save()
        project_interested = ProjectInterested.objects.create(interested_id=company_member.user_id,
                                         master_id=project_member.user_id,
                                         relation=relation, project_id=project_member.project_id)
    return err, project_interested
