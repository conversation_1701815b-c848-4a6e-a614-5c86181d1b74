from django.db import transaction

from utils import randomPassword, aesencrypt
from wisdom_v2.models import User, CompanyMember


def create_company_member(true_name, phone, email, position, project_member):
    """
    创建被教练者
    """
    password = randomPassword()
    pwd = aesencrypt(password)
    with transaction.atomic():
        user = User.objects.create(true_name=true_name, email=email, phone=phone, name=phone, password=pwd)
        company_member = CompanyMember.objects.create(user=user, company=project_member.project.company,
                                                      position=position)
    return company_member