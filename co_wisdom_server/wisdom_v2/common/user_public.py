import time

import redis
from django.conf import settings

from wisdom_v2.models import ProjectMember

data_redis = redis.Redis.from_url(settings.DATA_REDIS)

def del_specified_user_permissions(all_role, del_role):
    """
    从 `all_role` 列表中删除 `del_role` 列表中指定的元素。
    如果 `all_role` 或 `del_role` 为空，或者它们不是列表类型，那么函数将返回 []。
    如果 `del_role` 中的元素在 `all_role` 中存在，那么它将被移除。
    最后，函数返回更新后的 `all_role` 列表。
    :param all_role: list 包含所有角色的列表
    :param del_role: list 需要被删除的角色列表
    :return: all_role : list 更新后的角色列表
    """

    if not all_role or not del_role:
        return []

    if not isinstance(all_role, list):
        return []
    if not isinstance(del_role, list):
        return []

    for item in del_role:
        if item in all_role:
            all_role.remove(item)

    return all_role


def mark_user_as_terminated(user_ids):
    """
    标记用户状态为终止，该用户之前登录生成的token都将禁止登录，但不影响用户后续登录使用
    :param user_ids: 用户id
    """
    if not isinstance(user_ids, list):
        raise ValueError("user_ids must be a list of user IDs")

    for user_id in user_ids:
        if not isinstance(user_id, (int, str)):
            raise ValueError(f"Invalid user ID {user_id}")
        key = f'user:{user_id}:status'
        # 先移除已有的数据
        data_redis.delete(key)
        value = 'terminated'
        # 6个月以秒为单位
        ttl = 6 * 30 * 24 * 60 * 60
        data_redis.set(key, value, ex=ttl)


def is_user_token_terminated(user_id, exp):
    """
    判断用户token是否被终止
    :param user_id: 用户id
    :param exp: token有效期
    :return: 是否能使用token
    """
    if not user_id or not isinstance(user_id, (int, str)):
        raise ValueError("user_id must be a non-empty string")
    if not exp or not isinstance(exp, int):
        raise ValueError("exp must be a datetime timestamp")

    status = data_redis.get(f'user:{user_id}:status')
    if status:
        status = status.decode()
        if status == 'terminated':
            token_ttl = exp
            user_ttl = int(round(time.time())) + data_redis.ttl(f'user:{user_id}:status')
            if token_ttl < user_ttl:
                return True
    return False


def get_user_to_project_member_all(user_id):
    """
    获取用户参加的项目，多个地方需要使用，整合到一起，后续有改动就改这里
    登录时默认project_member的第一个
    """
    project_member = ProjectMember.objects.filter(user_id=user_id, deleted=False, is_forbidden=False).all()
    return project_member
