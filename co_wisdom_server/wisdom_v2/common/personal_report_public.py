from wisdom_v2.common import project_coach_public
from wisdom_v2.enum.service_content_enum import PersonalReportTypeEnum
from wisdom_v2.models import ProjectMember, CoachTask


def get_user_to_coach_user(personal_report):
    """
    获取与用户相关联的教练用户

    这个函数根据个人报告（personal_report）获取与用户相关联的教练用户。

    步骤：
    1. 检查personal_report是否为空。如果为空，直接返回。
    2. 获取用户ID和项目ID。
    3. 初始化教练变量为None。
    4. 根据不同的报告类型，采用不同的方式获取教练：
       a. 如果是改变观察报告，通过项目成员和项目教练关系获取教练用户。
       b. 如果是利益相关者访谈纪要报告或利益相关者访谈总结报告，通过教练任务获取教练用户。
    5. 返回教练用户。

    参数:
    personal_report (object): 个人报告对象。

    返回:
    object: 教练用户对象，如果没有找到，返回None。
    """
    if not personal_report:
        return

    user_id = personal_report.user_id
    project_id = personal_report.project_id

    coach = None

    # 不同类型，教练取值不一样。
    if personal_report.type == PersonalReportTypeEnum.change_observation_report.value:
        # 对于变更观察报告，通过项目成员获取项目教练
        project_member = ProjectMember.objects.filter(user_id=user_id, project_id=project_id, deleted=False).first()
        if project_member:
            project_coaches = project_coach_public.get_project_user_coach(project_member)
            if project_coaches and project_coaches.exists():
                coach = project_coaches.first().coach.user

    elif personal_report.type in [
            PersonalReportTypeEnum.notes_report.value,
            PersonalReportTypeEnum.summary_report.value]:
        # 对于笔记报告或总结报告，通过教练任务获取教练
        coach_task = CoachTask.objects.filter(id=personal_report.object_id, deleted=False).first()
        coach = coach_task.public_attr.user if coach_task else None

    return coach

