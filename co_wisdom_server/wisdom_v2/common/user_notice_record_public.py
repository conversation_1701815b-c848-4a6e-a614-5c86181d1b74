from utils.messagecenter import getui
from wisdom_v2.models import Project, WorkWechatUser


def update_coach_notice(user_notice, data):
    """
    更新教练通知信息
    :param user_notice: 通知对象
    :param data: 通知数据
    :return:
    """
    content = user_notice.content
    resume_ids = data.get('resume_ids')
    if resume_ids:
        content['selected_resume_id'] = resume_ids
    user_notice.content = content
    user_notice.is_now_modify = True
    user_notice.save()

    project = Project.objects.filter(id=user_notice.project_id, deleted=False).first()

    # 给项目运营发送企业微信消息通知
    manager_list = project.manager_list
    if manager_list:
        manager_user_ids = [item.get('user_id') for item in manager_list]

        wx_user_ids = list(WorkWechatUser.objects.filter(
            user_id__in=manager_user_ids, wx_user_id__isnull=False, deleted=False
        ).values_list('wx_user_id', flat=True).distinct())

        str_wx_user_ids = '|'.join(wx_user_ids)
        getui.send_work_wechat_coach_notice.delay(
            str_wx_user_ids,
            'hr_selected_coach_resume',
            project_name=project.full_name,
        )

