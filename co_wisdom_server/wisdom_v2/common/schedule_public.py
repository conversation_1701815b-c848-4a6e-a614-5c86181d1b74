import math
import pendulum

from copy import deepcopy
from datetime import datetime, time, timedelta

from django.db.models import Q
from django.utils.dateparse import parse_date

from utils.api_response import WisdomValidationError
from wisdom_v2.common import recurring_schedule_public
from wisdom_v2.enum.project_interview_enum import ProjectInterviewPlaceCategoryEnum
from wisdom_v2.enum.service_content_enum import ScheduleTypeEnum, ScheduleApplyTypeEnum
from wisdom_v2.models import Schedule, PublicAttr, ProjectInterview
from wisdom_v2.models_file import RecurringSchedule
from wisdom_v2.views.constant import SCHEDULE_INTERVAL_MINUTES


def excerpt_schedule(schedule, start_time=None, end_time=None):
    """
    对跨天的日程，根据给定的开始时间和结束时间，进行修正

    :param schedule: Schedule表的日程对象  type: object
    :param start_time: 需要限制的日程开始时间  type: datetime
    :param end_time: 需要限制的日程结束时间  type: datetime
    :return: 修改过后的Schedule日程对象  type: object
    """
    if not schedule:
        raise WisdomValidationError('未获取到日程对象')

    if not start_time and not end_time:
        raise WisdomValidationError('start_time与end_time不可都为空')

    # 如果特点查询某个时间之前/之后的日程，需要修正开始/结束时间
    if start_time and schedule.public_attr.start_time.date() < start_time.date():
        schedule.public_attr.start_time = datetime.combine(start_time, time.min)
    if end_time and schedule.public_attr.end_time.date() > end_time.date():
        schedule.public_attr.end_time = datetime.combine(end_time, time.max)
    return schedule


def split_schedule(schedule):
    """
    将跨天的日程拆分成每天的子日程
    :param schedule: Schedule表的日程对象  type: object
    :return: 日程对象列表的  type: list
    """
    # 如果是同一天则直接返回
    if schedule.public_attr.start_time.date() == schedule.public_attr.end_time.date():
        return [schedule]

    # 如果结束时间是第二天00:00:00,则改为前一天23:59:59,防止第二天多出一份00:00-00:00的日程数据
    if schedule.public_attr.end_time.time() == time.min:
        schedule.public_attr.end_time = datetime.combine(schedule.public_attr.end_time - timedelta(days=1), time.max)

    # 将时间转化成日期格式
    start_date = schedule.public_attr.start_time.date()
    end_date = schedule.public_attr.end_time.date()

    schedules = []
    schedule_count = 1
    while start_date <= end_date:
        next_date = start_date + timedelta(days=1)

        # 创建一个attr对象
        attr = PublicAttr(
            project_id=schedule.public_attr.project_id,
            user=schedule.public_attr.user,
            # 开始时间为当天0点0分0秒
            start_time=datetime(start_date.year, start_date.month, start_date.day),
            # 结束时间为当天极限时间23点59分59秒
            end_time=datetime(start_date.year, start_date.month, start_date.day, 23, 59, 59, 999999),
            created_at=schedule.public_attr.created_at,
        )
        # 创建一个schedule对象
        s = Schedule(
            # 需要唯一id标识
            id=int(f'{schedule.id}000{schedule_count}'),
            title=schedule.title,
            type=schedule.type,
            apply_type=schedule.apply_type,
            platform=schedule.platform,
            remark=schedule.remark,
            is_open_manage=schedule.is_open_manage,
            public_attr=attr,
            created_at=schedule.created_at,
            is_all_day=schedule.is_all_day,
        )
        # 新建的日程需要带上主日程id
        s.main_id = schedule.id
        schedules.append(s)
        start_date = next_date
        schedule_count += 1
    # 跨天日程在切片中开始时间/结束时间使用的当天最小/最大时间
    if schedules:
        # 更新第一天的开始时间
        schedules[0].public_attr.start_time = schedule.public_attr.start_time
        # 更新最后一天的结束时间
        schedules[-1].public_attr.end_time = schedule.public_attr.end_time
    return schedules


def get_schedule_day_list(user_id, target_date_time, interval_minutes=SCHEDULE_INTERVAL_MINUTES, time_status=False, interview_id=None, apply_type=None):
    """
    将指定日期的所有日程拆分，并返回该日期的可用时间段和不可用时间段的布尔列表。
    :param user_id: 用户id type: int
    :param target_date_time: 需要查询的时间 type: datetime
    :param time_status: 默认教练可预约时间状态 type: bool
    :param interval_minutes: 时间切分间隔 单位分钟，默认30  type: int
    :param interview_id: 需要排除的辅导 type: int
    :param apply_type: 应用类型一致的日程 type: int
    :return: 当天拆分好的日程 type: list
    """
    # 查询进行中的日程

    apply_type = int(apply_type) if apply_type else None
    query = Schedule.objects.filter(
        Q(public_attr__start_time__date__lte=target_date_time, public_attr__end_time__date__gte=target_date_time),
        public_attr__user_id=user_id,
        type=ScheduleTypeEnum.available,
        deleted=False,
        recurring_schedule__isnull=True
    )

    # 特殊处理 apply_type = 3 的情况
    if apply_type == ScheduleApplyTypeEnum.activity.value:
        query = query.filter(
            Q(apply_type__isnull=True) |
            Q(apply_type__contains=[ScheduleApplyTypeEnum.personal.value]) |  # 匹配包含 1 的记录
            Q(apply_type__contains=[apply_type])  # 匹配包含 3 的记录
        )
    elif apply_type is not None:
        # 处理其他 apply_type_param 值的通用情况
        query = query.filter(
            Q(apply_type__isnull=True) |
            Q(apply_type__contains=[apply_type])
        )

    schedules = list(query.all())

    # 查询当天可能存在的重复日程
    recurring_query = RecurringSchedule.objects.filter(
        Q(end_repeat_date__gte=target_date_time.date()) | Q(end_repeat_date__isnull=True),
        schedule__public_attr__start_time__date__lte=target_date_time.date(),
        schedule__public_attr__user_id=user_id,
        schedule__type=ScheduleTypeEnum.available,
        schedule__deleted=False,
        deleted=False
    )

    # 应用同样的 apply_type 逻辑到重复日程
    if apply_type == ScheduleApplyTypeEnum.activity.value:
        recurring_query = recurring_query.filter(
            Q(schedule__apply_type__isnull=True) |
            Q(schedule__apply_type__contains=[ScheduleApplyTypeEnum.personal.value]) |
            Q(schedule__apply_type__contains=[apply_type])
        )
    elif apply_type is not None:
        recurring_query = recurring_query.filter(
            Q(schedule__apply_type__isnull=True) |
            Q(schedule__apply_type__contains=[apply_type])
        )

    recurring_schedules = list(recurring_query.all())

    # 对于每个找到的重复日程，检查目标日期是否在其中
    for recurring in recurring_schedules:
        if recurring_schedule_public.date_in_recurring_schedule(recurring, target_date_time.date()):
            instance = deepcopy(recurring.schedule)  # 创建日程实例
            instance.public_attr.start_time = datetime.combine(target_date_time.date(), instance.public_attr.start_time.time())
            instance.public_attr.end_time = datetime.combine(target_date_time.date(), instance.public_attr.end_time.time())
            schedules.append(instance)

    # 对 schedules 列表进行排序
    schedules.sort(key=lambda x: x.public_attr.created_at)

    # 获取辅导的日程
    interview_schedule = Schedule.objects.filter(
        Q(public_attr__start_time__date__lte=target_date_time, public_attr__end_time__date__gte=target_date_time),
        public_attr__user_id=user_id,
        type=ScheduleTypeEnum.interview,
        deleted=False
    ).order_by('public_attr__created_at').all()

    # 如果有辅导id，计算可用时间时，需要排除此辅导
    if interview_id:
        interview = ProjectInterview.objects.filter(pk=interview_id, deleted=False).first()
        if interview:
            interview_schedule = interview_schedule.exclude(public_attr_id=interview.public_attr_id)

    # 日程列表末尾追加辅导日程
    schedules = [*schedules, *interview_schedule]

    # 当天最小/最大时间
    time_min = datetime.combine(target_date_time, time.min)
    time_max = datetime.combine(target_date_time, time.max)

    # 计算一天中有多少个时间段（以传入时间为单位）
    slots_per_day = math.ceil(24 * 60 / interval_minutes)

    # 根据教练身份区分默认可预约时间段
    time_slots = [time_status] * slots_per_day

    for schedule in schedules:

        # 跨天的日程只取当天最小/最大时间
        if schedule.public_attr.start_time.date() < target_date_time.date():
            start_time = time_min
        else:
            start_time = schedule.public_attr.start_time

        if schedule.public_attr.end_time.date() > target_date_time.date():
            end_time = time_max
        else:
            end_time = schedule.public_attr.end_time

        # 如果开始时间=结束时间=00:00，该日程不进入计算
        if start_time == schedule.public_attr.end_time == time_min:
            continue

        # 计算当前日程在列表中的位置
        if schedule.is_all_day:
            start_slot = math.ceil((8 * 60) / interval_minutes)
            end_slot = math.ceil((22 * 60) / interval_minutes)
        else:
            start_slot = math.ceil((start_time.hour * 60 + start_time.minute) / interval_minutes)
            end_slot = math.ceil((end_time.hour * 60 + end_time.minute) / interval_minutes)

        # 如果是可预约日程，将相应的时间段设置为可预约
        if schedule.type == ScheduleTypeEnum.available:
            for slot in range(start_slot, end_slot):
                time_slots[slot] = True
        # 如果是不可预约日程，将相应的时间段设置为不可预约
        elif schedule.type in [ScheduleTypeEnum.unavailable, ScheduleTypeEnum.interview]:
            for slot in range(start_slot, end_slot):
                time_slots[slot] = False

    return time_slots


def split_datetime_schedule(start_time, end_time):
    """
    将跨天的时间拆分成每天的子时间
    :param start_time: 开始时间  type: datetime
    :param end_time: 结束时间  type: datetime
    :return: 拆分好的日程数据  type: dict
    """

    data = {}
    # 如果是同一天则直接返回
    if start_time.date() == end_time.date():
        data[start_time.strftime("%Y-%m-%d")] = [start_time, end_time]
        return data

    # 如果结束时间是第二天00:00:00,则改为前一天23:59:59,防止第二天多出一份00:00-00:00的日程数据
    if end_time.time() == time.min:
        end_time = datetime.combine(end_time - timedelta(days=1), time.max)

    # 将时间转化成日期格式
    start_date = start_time.date()
    end_date = end_time.date()

    while start_date <= end_date:
        next_date = start_date + timedelta(days=1)
        data[start_date.strftime("%Y-%m-%d")] = [
            datetime(start_date.year, start_date.month, start_date.day),
            datetime(start_date.year, start_date.month, start_date.day, 23, 59, 59, 999999)]
        start_date = next_date

    # 跨天日程在切片中开始时间/结束时间使用的当天最小/最大时间
    if data:
        # 更新第一天的开始时间
        first_key = next(iter(data))
        data[first_key][0] = start_time

        # 更新最后一天的结束时间
        last_key = list(data.keys())[-1]
        data[last_key][1] = end_time
    return data


def get_schedule_time_list(
        user_id, start_date, end_date, interval_minutes=SCHEDULE_INTERVAL_MINUTES, time_status=False, apply_type=None,
        interval_time_minutes=60, delay_hour=0, interview_id=None):
    """
    获取指定用户在给定日期范围内的日程列表和总可用小时数。

    参数:
    user_id: 用户标识
    start_date: 查询开始日期
    end_date: 查询结束日期
    interval_minutes: 每个时间段的间隔分钟数，默认为SCHEDULE_INTERVAL_MINUTES
    time_status: 时间段的默认可用状态，True 表示默认可用，False 表示默认不可用
    interval_time_minutes: 需要的辅导时长（分钟）
    delay_hour: 当天往后延迟时长
    interview_id: 需要排除的辅导id

    返回:
    all_date: 每日的可用小时数和星期信息列表
    all_hour: 所有查询日内的总可用小时数
    """

    sorted_schedules, interview_schedule_data, all_schedules = get_base_schedules_dict_data(user_id, start_date, end_date, apply_type=apply_type, interview_id=interview_id)

    all_date = []  # 存储每天的日程信息
    all_hour = 0  # 记录总可用小时数
    week_days = ["周一", "周二", "周三", "周四", "周五", "周六", "周日"]

    # 遍历查询日期范围的每一天
    while start_date <= end_date:
        next_date = start_date

        data_str = next_date.strftime('%Y-%m-%d')
        schedules = all_schedules.get(data_str)

        # 计算一天中有多少个时间段（以传入时间为单位）
        slots_per_day = math.ceil(24 * 60 / interval_minutes)

        # 根据教练身份区分默认可预约时间段
        time_slots = [time_status] * slots_per_day

        if schedules:
            # 计算日程的时间
            for schedule in schedules:
                start_time, end_time, schedule_type, is_all_day, created_at = schedule

                # 如果开始时间=结束时间=00:00，该日程不进入计算
                if start_time == end_time == datetime.combine(next_date, time.min):
                    continue

                # 计算当前日程在列表中的位置
                if is_all_day:
                    start_slot = math.ceil((8 * 60) / interval_minutes)
                    end_slot = math.ceil((22 * 60) / interval_minutes)
                else:
                    start_slot = math.ceil((start_time.hour * 60 + start_time.minute) / interval_minutes)
                    end_slot = math.ceil((end_time.hour * 60 + end_time.minute) / interval_minutes)

                # 如果是可预约日程，将相应的时间段设置为可预约
                if schedule_type == ScheduleTypeEnum.available.value:
                    for slot in range(start_slot, end_slot):
                        time_slots[slot] = True
                # 如果是不可预约日程，将相应的时间段设置为不可预约
                elif schedule_type in [ScheduleTypeEnum.unavailable.value, ScheduleTypeEnum.interview.value]:
                    for slot in range(start_slot, end_slot):
                        time_slots[slot] = False

        # 每天0点-8点不可以预约，所以默认推后8小时
        # 去掉不可以预约的逻辑
        current_hour = 0
        # 如果时间是今天，需要特别处理,当前时间推移+6
        if next_date == datetime.now().date():
            additional_hours = 0.5 if datetime.now().minute < 30 else 1  # 如果分钟数<30，加0.5小时；否则加1小时
            current_hour = datetime.now().hour + additional_hours + delay_hour

        time_slots = adjust_days_time_slots(time_slots, current_hour)

        # 获取当天可用时长
        hour = calculate_continuous_available_hours(time_slots, interval_time_minutes=interval_time_minutes)
        all_date.append({
            'date': next_date.strftime('%m/%d'),
            'complete_date': next_date.strftime("%Y-%m-%d"),
            'hour': hour,
            'week_day': week_days[next_date.weekday()],
        })

        all_hour += hour
        # 计算并累加每天的可用小时数，准备下一天的处理
        start_date = next_date + timedelta(days=1)
    return all_date, all_hour


def get_time_interval_schedule_to_list(user_id, start_date, end_date):
    """
     将指定用户在指定时间范围内的日程安排转换为列表格式。

     参数:
     user_id: 用户ID，用于查询该用户的日程安排。
     start_date: 开始日期，格式为datetime.date，表示查询范围的起始日期。
     end_date: 结束日期，格式为datetime.date，表示查询范围的结束日期。

     返回值:
     返回一个列表，列表中包含每一天的日程信息，每一天的信息是一个字典，包括：
     - date: 日期，格式为字符串'YYYY-MM-DD'。
     - is_interview_schedule: 布尔值，表示当天是否有辅导安排。
     - is_coach_schedule: 布尔值，表示当天是否有教练安排。
     """
    sorted_schedules, interview_schedule_data, all_schedules = get_base_schedules_dict_data(user_id, start_date, end_date)

    all_date = []  # 存储每天的日程信息

    # 遍历查询日期范围的每一天
    while start_date <= end_date:
        next_date = start_date

        data_str = next_date.strftime('%Y-%m-%d')
        schedules = sorted_schedules.get(data_str)
        interview_schedules = interview_schedule_data.get(data_str)
        all_date.append({
            'date': next_date.strftime('%Y-%m-%d'),
            'is_interview_schedule': bool(interview_schedules),
            'is_coach_schedule': bool(schedules),
        })
        start_date = next_date + timedelta(days=1)
    return all_date


def get_base_schedules_dict_data(user_id, start_date, end_date, apply_type=None, interview_id=None):
    """
    查询指定时间内的日程数据，并切分成每一天。

    :param user_id: 用户ID，用于查询该用户的日程。
    :param start_date: 开始日期，查询此日期及以后的日程。
    :param end_date: 结束日期，查询此日期及以前的日程。
    :param apply_type: 日程应用类型。
    :param interview_id: 需要排除的日程id。
    :return: 返回一个包含三个元素的元组。第一个元素是排序后的日程数据字典，以日期为键，每天的日程列表为值；
    第二个元素是辅导日程数据字典，结构同上；第三个元素是所有日程数据字典，包括重复日程和非重复日程。
    """

    all_schedules = {}

    # 查询用户在指定日期范围内的非重复日程
    # 根据日程的开始时间和结束时间筛选，只查询可预约类型的日程
    schedules_query = Schedule.objects.filter(
        Q(public_attr__start_time__date__gte=start_date, public_attr__start_time__date__lte=end_date) |
        Q(public_attr__end_time__date__gte=start_date, public_attr__end_time__date__lte=end_date) |
        Q(public_attr__start_time__date__lte=start_date, public_attr__end_time__date__gte=end_date),
        public_attr__user_id=user_id,
        type=ScheduleTypeEnum.available.value,
        deleted=False
    ).filter(recurring_schedule__isnull=True)
    # 特殊处理 apply_type = 3 的情况
    if apply_type == ScheduleApplyTypeEnum.activity.value:
        schedules_query = schedules_query.filter(
            Q(apply_type__isnull=True) |
            Q(apply_type__contains=[ScheduleApplyTypeEnum.personal.value]) |  # 匹配包含 1 的记录
            Q(apply_type__contains=[apply_type])  # 匹配包含 3 的记录
        )
    elif apply_type is not None:
        # 处理其他 apply_type_param 值的通用情况
        schedules_query = schedules_query.filter(
            Q(apply_type__isnull=True) |
            Q(apply_type__contains=[apply_type])
        )
    schedules_time = schedules_query.values_list(
        'public_attr__start_time', 'public_attr__end_time', 'public_attr__created_at', 'type', 'is_all_day')

    # 对每个日程进行处理，如果日程跨越多天，则进行切分，每天作为一个单独的条目处理
    for schedule_time in schedules_time:
        start_time, end_time, created_at, schedule_type, is_all_day = schedule_time

        # 跨天日程切片，获取每天的数据
        schedule_data = split_datetime_schedule(start_time, end_time)
        for k, v in schedule_data.items():
            # 补充创建时间和日程类型，后续数据处理使用。
            v += [schedule_type, is_all_day, created_at]
            if k in all_schedules:
                all_schedules[k].append(v)
            else:
                all_schedules[k] = [v]

    # 查询并处理用户在指定日期范围内的重复日程，只查询可预约类型的日程
    recurring_query = RecurringSchedule.objects.filter(
        Q(end_repeat_date__gte=start_date) | Q(end_repeat_date__isnull=True),
        schedule__public_attr__start_time__date__lte=end_date,
        schedule__public_attr__user_id=user_id,
        schedule__type=ScheduleTypeEnum.available.value,
        schedule__deleted=False,
        deleted=False
    )
    # 应用同样的 apply_type 逻辑到重复日程
    if apply_type == ScheduleApplyTypeEnum.activity.value:
        recurring_query = recurring_query.filter(
            Q(schedule__apply_type__isnull=True) |
            Q(schedule__apply_type__contains=[ScheduleApplyTypeEnum.personal.value]) |
            Q(schedule__apply_type__contains=[apply_type])
        )
    elif apply_type is not None:
        recurring_query = recurring_query.filter(
            Q(schedule__apply_type__isnull=True) |
            Q(schedule__apply_type__contains=[apply_type])
        )
    recurring_schedules = recurring_query.values_list(
        'excluded_dates', 'repeat_type', 'end_repeat_date', 'schedule__public_attr__created_at', 'schedule__type',
        'schedule__is_all_day',
        'schedule__public_attr__start_time', 'schedule__public_attr__end_time',
    )
    # 对于每个找到的重复日程，生成指定日期内的所有日程数据
    for recurring_schedules_item in recurring_schedules:
        excluded_dates, repeat_type, end_repeat_date, created_at, schedule_type, is_all_day, schedule_start_time, schedule_end_time = recurring_schedules_item
        # 重复日程切片
        recurring_schedule_data = recurring_schedule_public.time_within_recurring_schedule(
            excluded_dates, repeat_type, end_repeat_date, schedule_start_time, schedule_end_time, start_date, end_date)

        for k, v in recurring_schedule_data.items():
            # 补充创建时间和日程类型，后续数据处理使用。
            v += [schedule_type, is_all_day, created_at]
            if k in all_schedules:
                all_schedules[k].append(v)
            else:
                all_schedules[k] = [v]

    # 对 schedules 列表进行排序
    sorted_schedules = {k: sorted(v, key=lambda x: x[-1]) for k, v in all_schedules.items()}

    all_schedules = sorted_schedules.copy()
    interview_schedule_data = {}
    # 获取辅导的日程-辅导日程的优先级最高，不参与日程创建时间排序。
    interview_schedule = Schedule.objects.filter(
        Q(public_attr__start_time__date__gte=start_date, public_attr__start_time__date__lte=end_date) |
        Q(public_attr__end_time__date__gte=start_date, public_attr__end_time__date__lte=end_date),
        public_attr__user_id=user_id,
        type=ScheduleTypeEnum.interview.value,
        deleted=False
    ).filter(recurring_schedule__isnull=True)
    # 如果有辅导id，计算可用时间时，需要排除此辅导
    if interview_id:
        interview = ProjectInterview.objects.filter(pk=interview_id, deleted=False).first()
        if interview:
            interview_schedule = interview_schedule.exclude(public_attr_id=interview.public_attr_id)
    interview_schedule = interview_schedule.values_list(
        'public_attr__start_time', 'public_attr__end_time', 'public_attr__created_at', 'type', 'is_all_day')
    for interview_schedule_time in interview_schedule:
        start_time, end_time, created_at, schedule_type, is_all_day = interview_schedule_time

        # 跨天日程切片，获取每天的数据
        schedule_data = split_datetime_schedule(start_time, end_time)
        for k, v in schedule_data.items():
            # 补充创建时间和日程类型，后续数据处理使用。
            v += [schedule_type, is_all_day, created_at]
            if k in interview_schedule_data:
                interview_schedule_data[k].append(v)
            else:
                interview_schedule_data[k] = [v]

            if k in all_schedules:
                all_schedules[k].append(v)
            else:
                all_schedules[k] = [v]

    return sorted_schedules, interview_schedule_data, all_schedules


def calculate_continuous_available_hours(time_slots, interval_time_minutes=SCHEDULE_INTERVAL_MINUTES, interval_minutes=SCHEDULE_INTERVAL_MINUTES):
    """
    计算教练的连续可用时长，只有连续地可用时间段能组成完整小时时才计入总时长。

    :param time_slots: 一个布尔列表，表示每个时间段是否可用（True为可用）。
    :param interval_time_minutes: 需要的时间间隔长度。
    :param interval_minutes: 每个时间段的长度，以分钟为单位。
    :return: 教练的连续可用时长，以小时为单位。
    """
    slots_per_hour = interval_time_minutes // interval_minutes
    continuous_true_count = 0
    total_hours = 0

    for slot in time_slots:
        if slot:
            continuous_true_count += 1
            if continuous_true_count == slots_per_hour:
                total_hours += 1
                continuous_true_count = 0  # 重置连续计数器
        else:
            continuous_true_count = 0  # 重置连续计数器，因为遇到了不可用的时间段

    return total_hours


def adjust_days_time_slots(time_slots, offset_hours, interval_minutes=SCHEDULE_INTERVAL_MINUTES):
    """
    调整天数的time_slots列表，去除每天开始的offset_hours小时对应的时间段。

    :param time_slots: 当天的时间段列表（布尔值）。
    :param offset_hours: 每天开始时需要去除的小时数。
    :return: 调整后的时间段列表。
    """
    # 计算需要去除的时间段数量，每小时对应的段数为 60 / SCHEDULE_INTERVAL_MINUTES
    slots_to_remove = int((offset_hours * 60) // interval_minutes)
    return time_slots[slots_to_remove:]


def is_time_slot_available(time_slots, start_time, end_time, interval_minutes=SCHEDULE_INTERVAL_MINUTES):
    """
    根据提供的时间段和用户的可用日程列表，判断该时间段是否可用。
    :param time_slots:  get_schedule功能函数返回当天可用日程列表  type: list
    :param start_time:  开始时间  type: datetime
    :param end_time:  结束时间  type: datetime
    :param interval_minutes:  时间切分间隔 单位分钟，默认30。  type: int
    :return:  日程是否被占用  type: bool
    """

    # 查询时要对数据进行转换,正确寻找对应下标
    if start_time.minute > 45:
        start_time = start_time.replace(minute=0) + datetime.timedelta(hours=1)
    elif start_time.minute > 30:
        start_time = start_time.replace(minute=45)
    elif start_time.minute > 15:
        start_time = start_time.replace(minute=30)
    elif start_time.minute > 0:
        start_time = start_time.replace(minute=15)

    if end_time.minute > 45:
        end_time = end_time.replace(minute=0) + datetime.timedelta(hours=1)
    elif end_time.minute > 30:
        end_time = end_time.replace(minute=45)
    elif end_time.minute > 15:
        end_time = end_time.replace(minute=30)
    elif end_time.minute > 0:
        end_time = end_time.replace(minute=15)

    # 计算时间段在列表中的位置
    start_slot = (start_time.hour * 60 + start_time.minute) // interval_minutes
    end_slot = (end_time.hour * 60 + end_time.minute) // interval_minutes
    end_slot = end_slot if end_slot else len(time_slots)

    # 检查所有相关的时间段是否可预约
    for slot in range(start_slot, end_slot):
        if not time_slots[slot]:
            # 如果有任何时间段不可预约，返回False
            return False

    # 所有相关的时间段都是可预约的，返回True
    return True


def get_available_periods(time_slots, target_date, min_duration, interval_minutes=SCHEDULE_INTERVAL_MINUTES):
    """
    根据给定的时间段布尔列表，返回当天的所有可用时间段。
    :param time_slots: 布尔列表，表示时间段的可用性 type: list
    :param target_date: 需要查询的日期 type: date
    :param min_duration: 最小时间段长度，单位分钟 type: int
    :param interval_minutes: 时间切分间隔 单位分钟，默认30  type: int
    :return: 当天的所有可用时间段，每个时间段是一个字符串，格式为 "开始时间-结束时间" type: list
    """

    periods = []
    start_time = None
    total_seconds = 0

    for i, slot in enumerate(time_slots):
        current_time = datetime.combine(target_date, time()) + timedelta(minutes=i * interval_minutes)

        # 如果当前时间段是可用的，并且还没有开始时间，那么设置开始时间为当前时间
        if slot and start_time is None:
            start_time = current_time

        # 如果当前时间段是不可用的，或者已经是最后一个时间段，并且有开始时间，那么设置结束时间，并将可用时间段添加到列表中
        if (not slot or i == len(time_slots) - 1) and start_time is not None:
            # 如果是最后一个时间段并且是可用的，设置结束时间为次日的00:00
            if i == len(time_slots) - 1 and slot:
                end_time = datetime.combine(target_date + timedelta(days=1), time())
                end_time_str = "24:00"
            else:
                end_time = current_time
                end_time_str = end_time.strftime('%H:%M')

            # 检查时间段的长度是否大于或等于最小长度
            if (end_time - start_time).total_seconds() >= min_duration * 60:
                periods.append(f"{start_time.strftime('%H:%M')} - {end_time_str}")
                total_seconds += (end_time - start_time).total_seconds()

            start_time = None
    # 将总时间从秒转换为小时
    return round((total_seconds / 3600) * 2) / 2, periods


def get_schedule_intervals_detailed(time_slots, target_date, interval_minutes=SCHEDULE_INTERVAL_MINUTES):
    """
    根据给定的时间段布尔列表，返回当天的所有可预约时间段作为时间间隔对象列表。
    这个版本的函数还包括一个details字段，其中包含时间间隔的格式化字符串。

    :param time_slots: 布尔列表，表示时间段的可用性 type: list
    :param target_date: 需要查询的日期 type: date
    :param interval_minutes: 时间切分间隔 单位分钟，默认30  type: int
    :return: 时间间隔对象列表，每个对象包含start, end, is_day和details字段 type: list
    """

    intervals = []
    start_time = None

    for i, slot in enumerate(time_slots):
        current_time = datetime.combine(target_date, time()) + timedelta(minutes=i * interval_minutes)

        # 如果当前时间段是可用的，并且还没有开始时间，那么设置开始时间为当前时间
        if slot and start_time is None:
            start_time = current_time

        # 如果当前时间段是不可用的或者已经是最后一个时间段，并且有开始时间，那么设置结束时间，并将时间间隔对象添加到列表中
        if (not slot or i == len(time_slots) - 1) and start_time:
            # 如果是最后一个时间段并且是可用的，设置结束时间为24:00
            end_time = current_time if not slot else datetime.combine(target_date, time(23, 59, 59))

            # 判断时间间隔是否为整天
            is_day = start_time.time() == time() and end_time.time() == time(23, 59, 59)

            end_time_str = "24:00" if end_time.time() == time(23, 59, 59) else end_time.strftime("%H:%M")

            end_datetime_str = f"{end_time.strftime('%Y-%m-%d')} 24:00:00" if end_time_str == "24:00" else end_time.strftime("%Y-%m-%d %H:%M:%S")

            intervals.append({
                "start": start_time.strftime("%Y-%m-%d %H:%M:%S"),
                "end": end_datetime_str,
                "is_day": is_day,
                "details": {
                    'time': f'{start_time.strftime("%H:%M")}-{end_time_str}'
                }
            })

            start_time = None

    return intervals


def get_coach_all_schedule(start_date, end_date, coach):
    """
    获取所有教练的日程信息
    : param start_time: 开始时间
    : param end_time: 结束时间
    : param coach: 教练对象
    :return: 日程数据 dict
    """

    # 查询自定义日程
    schedules = list(Schedule.objects.filter(
        Q(public_attr__start_time__date__lte=end_date, public_attr__end_time__date__gte=start_date),
        public_attr__user_id=coach.user_id,
        type__in=[ScheduleTypeEnum.available, ScheduleTypeEnum.interview],
        deleted=False, recurring_schedule__isnull=True
    ).all())

    schedules_data = [{
        'start_time': item.public_attr.start_time,
        'end_time': item.public_attr.end_time,
        'title': item.title,
        'type': item.type,
    } for item in schedules]

    # 查询当天可能存在的重复日程
    recurring_schedules = RecurringSchedule.objects.filter(
        Q(end_repeat_date__gte=start_date) | Q(end_repeat_date__isnull=True),
        schedule__public_attr__start_time__date__lte=end_date,
        schedule__public_attr__user_id=coach.user_id,
        schedule__type__in=[ScheduleTypeEnum.available, ScheduleTypeEnum.interview],
        schedule__deleted=False,
        deleted=False).all()

    pendulum_start_time = pendulum.datetime(start_date.year, start_date.month, start_date.day)
    pendulum_end_time = pendulum.datetime(end_date.year, end_date.month, end_date.day)
    period = pendulum.period(pendulum_start_time, pendulum_end_time)

    # 对于每个找到的重复日程，检查目标日期是否在其中
    for recurring in recurring_schedules:
        # 循环日期，获取每一天的时间
        for dt in period.range('days'):
            if recurring_schedule_public.date_in_recurring_schedule(recurring, dt.date()):
                schedules_data.append({
                    'start_time': datetime.combine(
                    dt.date(), recurring.schedule.public_attr.start_time.time()),
                    'end_time': datetime.combine(
                    dt.date(), recurring.schedule.public_attr.end_time.time()),
                    'title': recurring.schedule.title,
                    'type': recurring.schedule.type,
                })

    # 对 schedules 列表进行排序
    schedules_data = sorted(schedules_data, key=lambda x: x['start_time'])
    return schedules_data


def create_new_schedule_from_existing(new_schedule_data, old_schedule, is_add_recurring_schedule=True):
    """
    复制旧日程安排并添加新日程。

    :param new_schedule_data: 包含新日程信息的字典，例如开始时间、结束时间等。
    :param old_schedule: 旧的日程对象，用于复制一些公共属性。
    :param is_add_recurring_schedule: 是否添加重复日程 bool
    :return: 新日程对象。
    """

    public_attr = old_schedule.public_attr

    public_attr = PublicAttr.objects.create(
        project_id=public_attr.project_id,
        user=public_attr.user,
        type=public_attr.type,
        start_time=new_schedule_data.get('start_time'),
        end_time=new_schedule_data.get('end_time'))
    schedule = Schedule.objects.create(
        is_all_day=new_schedule_data.get('is_all_day'),
        title=new_schedule_data.get('title'),
        remark=new_schedule_data.get('remark'),
        type=new_schedule_data.get('type', ScheduleTypeEnum.available.value),
        is_open_manage=new_schedule_data.get('is_open_manage'),
        public_attr=public_attr,
        apply_type=new_schedule_data.get('apply_type')
    )

    if is_add_recurring_schedule and new_schedule_data.get('repeat_type'):
        RecurringSchedule.objects.create(
            schedule=schedule, end_repeat_date=new_schedule_data.get('end_repeat_date'),
            repeat_type=new_schedule_data.get('repeat_type'),
        )
    return schedule


def split_schedules(mixed_schedules):
    """
    将混合的日程列表分割成普通日程和面试日程两个列表。

    参数:
    mixed_schedules - 混合的日程列表，每个日程是一个字典，包含至少一个'type'键，用于区分日程类型。

    返回值:
    schedules - 普通日程列表，包含所有非面试类型的日程。
    interview_schedules - 面试日程列表，包含所有类型为面试的日程。
    """
    if not isinstance(mixed_schedules, list):
        return [], []

    # 初始化两个空列表
    schedules = []
    interview_schedules = []

    # 遍历混合日程列表
    for item in mixed_schedules:
        if item.get('type') == ScheduleTypeEnum.interview.value:
            interview_schedules.append(item)
        else:
            schedules.append(item)

    # 对日程列表进行排序
    schedules.sort(key=lambda x: datetime.strptime(x['start_time'], '%Y-%m-%d %H:%M'))
    interview_schedules.sort(key=lambda x: datetime.strptime(x['start_time'], '%Y-%m-%d %H:%M'))
    return schedules, interview_schedules


def get_merge_schedule_to_dict(schedules, interview_schedules, status=1):
    """
    将辅导安排合并到原始日程中，并按照开始时间排序。这个函数主要处理两个列表：一个是常规的日程安排列表，另一个是特定的辅导日程安排列表。
    合并这两种类型的日程后，将它们按照开始时间进行排序。

    :param schedules: 原始日程安排列表，每个日程是一个字典，至少包含“开始时间”和“结束时间”键。
    :param interview_schedules: 辅导安排列表，每个辅导安排也是一个字典，除了“开始时间”外，还可能包含其他辅导相关的信息。
    :param status: 数据排序，默认为1 正序
    :return: 合并后的日程安排列表，包括原始日程和相关的辅导安排，列表按照“开始时间”排序。
    """

    # 为每个常规日程添加一个用于存储相关辅导信息的空列表
    for schedule in schedules:
        schedule['related_schedule'] = []

    # 遍历辅导安排列表，尝试将每个辅导安排添加到符合条件的常规日程中
    for interview_schedule in interview_schedules[:]:  # 使用[:]创建辅导安排列表的副本，以便在遍历时可以安全修改原列表

        interview_info = interview_schedule.get('interview_info', {})
        if not interview_info:
            continue
        else:
            interview_id = interview_info.get('interview_id')
            interview = ProjectInterview.objects.filter(
                id=interview_id, deleted=False).first()
        if not interview:
            continue
        place_category = interview.place_category
        project_id = interview.public_attr.project_id
        activity_interview = interview.activity_interview.filter(deleted=False).exists()

        for schedule in schedules:
            # 检查辅导的时间是否在某个常规日程的时间范围内
            if (schedule['start_time'] <= interview_schedule['start_time'] and
                    schedule['end_time'] >= interview_schedule['end_time']):

                # 如果常规日程没有指定适用类型，则直接添加辅导到相关日程列表中
                if not schedule.get('apply_type'):
                    schedule['related_schedule'].append(interview_schedule)
                    interview_schedules.remove(interview_schedule)
                    break

                # 根据辅导的地点类别和其他条件，确定是否将辅导加入到常规日程的相关列表中
                if place_category == ProjectInterviewPlaceCategoryEnum.offline_group_coach.value:
                    if ScheduleApplyTypeEnum.project.value in schedule.get('apply_type'):
                        schedule['related_schedule'].append(interview_schedule)
                        interview_schedules.remove(interview_schedule)
                        break

                elif place_category == ProjectInterviewPlaceCategoryEnum.online_one_to_one.value:
                    if project_id:
                        if ScheduleApplyTypeEnum.project.value in schedule.get('apply_type'):
                            schedule['related_schedule'].append(interview_schedule)
                            interview_schedules.remove(interview_schedule)
                            break

                    else:
                        if activity_interview:
                            if ScheduleApplyTypeEnum.personal.value in schedule.get('apply_type') or ScheduleApplyTypeEnum.activity.value in schedule.get('apply_type'):
                                schedule['related_schedule'].append(interview_schedule)
                                interview_schedules.remove(interview_schedule)
                                break
                        else:
                            if ScheduleApplyTypeEnum.personal.value in schedule.get('apply_type'):
                                schedule['related_schedule'].append(interview_schedule)
                                interview_schedules.remove(interview_schedule)
                                break

    # 对每个常规日程的相关辅导列表按照开始时间进行排序
    for schedule in schedules:
        schedule['related_schedule'].sort(key=lambda x: x['start_time'])

    # 将处理过的常规日程和未添加到任何日程中的剩余辅导合并
    combined_schedules = schedules + interview_schedules

    # 按照开始时间对合并后的日程列表进行排序
    if status == 1:
        combined_schedules.sort(key=lambda x: x['start_time'])
    else:
        combined_schedules.sort(key=lambda x: x['start_time'],  reverse=True)
    return combined_schedules


def detect_schedule_overlap(user_id, start_time, end_time, repeat_type, end_repeat_date, apply_type, excluded_schedule_id=0):
    """
    检查给定用户的新增日程是否与现有的辅导日程有冲突。
    :param user_id: 用户ID
    :param start_time: 新增日程的开始时间
    :param end_time: 新增日程的结束时间
    :param repeat_type: 日程的重复类型，如无重复则为None
    :param end_repeat_date: 重复日程的结束日期，非重复日程则为None
    :param apply_type: 应用类型，type：list 不限则为None
    :param excluded_schedule_id: 排除在外的日程ID，用于更新日程时排除自身
    :return: 如果有冲突，返回第一个冲突的日程对象；否则返回None
    """

    # 查询所有未删除、指定用户的辅导日程，时间结束后仍有效的日程
    existing_schedules = Schedule.objects.filter(
        public_attr__end_time__gt=start_time,  # 结束时间大于新增日程的开始时间
        recurring_schedule__isnull=True,  # 确保是非重复日程
        public_attr__user_id=user_id,  # 确保是当前用户的日程
        type=ScheduleTypeEnum.interview.value,  # 日程类型为辅导
        deleted=False  # 确保日程未被删除
    ).exclude(id=excluded_schedule_id)  # 排除特定的日程ID

    # 如果指定了重复日程的结束日期，则进一步限制查询条件
    if end_repeat_date:
        end_repeat_date_obj = parse_date(end_repeat_date)  # 将字符串日期转换为日期对象
        existing_schedules = existing_schedules.filter(
            public_attr__start_time__date__gte=end_repeat_date_obj  # 确保日程的开始时间在重复结束日期之前
        )

    # 提取所有符合条件的日程
    all_data = set()  # 使用集合去重
    for schedule_item in existing_schedules:
        all_data.add((schedule_item.public_attr.start_time, schedule_item.public_attr.end_time,
                      getattr(schedule_item, 'main_id', schedule_item.id), schedule_item.public_attr_id))  # 添加日程的开始时间、结束时间和ID

    # 判断是否有日程存在
    if all_data:
        all_data_sorted = sorted(list(all_data), key=lambda x: x[0])  # 按开始时间排序

        if repeat_type:
            # 处理重复日程的结束日期
            data_end_date = all_data_sorted[-1][0].strftime('%Y-%m-%d')
            if end_repeat_date:
                end_repeat_date = end_repeat_date if data_end_date > end_repeat_date else data_end_date
            else:
                end_repeat_date = all_data_sorted[-1][0].strftime('%Y-%m-%d')

            # 生成重复日程的时间序列，用于冲突检查
            schedule_datetime_list = recurring_schedule_public.generate_recurring_datetime(
                repeat_type, start_time, end_time, end_repeat_date)

            schedule_datetime_list = [schedule_datetime_item + [apply_type] for schedule_datetime_item in schedule_datetime_list]
        else:
            schedule_datetime_list = [[start_time, end_time, apply_type]]

        existing_schedule_id = find_first_conflict(schedule_datetime_list, all_data_sorted)

        # 如果存在冲突，获取冲突的日程对象
        if existing_schedule_id:
            existing_schedule = Schedule.objects.get(id=existing_schedule_id)
        else:
            existing_schedule = None
    else:
        existing_schedule = None

    return existing_schedule


def check_schedule_overlap(schedule_item, data_item):
    """
    检查两个时间段是否存在重叠。

    :param schedule_item: 来自 recurring_datetime_list 的元素，格式为[开始时间, 结束时间, 应用类型]
    :param data_item: 来自 all_data_sorted 的元素，格式为[开始时间, 结束时间，辅导ID, public_attr标识]
    :return: 布尔值或者辅导ID，True表示存在重叠且无特殊条件排除，False表示不存在重叠，辅导ID在特定条件下返回。
    """
    # 展开两个时间段
    schedule_start, schedule_end, schedule_apply_type = schedule_item
    data_start, data_end, data_id, data_public_attr_id = data_item

    # 判断时间段是否紧挨着但不重叠
    if schedule_end == data_start or schedule_start == data_end:
        return False

    # 判断两个时间段是否有重叠
    overlap_conditions = any([
        data_start < schedule_start < data_end,  # 日程开始时间在辅导时间段内
        data_start < schedule_end < data_end,  # 日程结束时间在辅导时间段内
        schedule_start > data_start and schedule_end < data_end,  # 日程完全被辅导时间段包含
    ])

    # 如果存在重叠，直接返回不可行
    if overlap_conditions:
        return data_id
    # 如果不存在重叠，根据进一步条件判断是否有例外
    else:
        # 如果辅导的时间段完全包含日程的时间段，进一步检查相关属性
        if schedule_start <= data_start and schedule_end >= data_end:
            # 如果日程有应用类型
            if schedule_apply_type:
                # 查询辅导的详细信息
                interview = ProjectInterview.objects.filter(public_attr_id=data_public_attr_id).first()
                if interview:
                    public_attr = interview.public_attr
                    if public_attr.project_id:
                        # 如果是项目类型日程，根据日程的应用类型决定是否有冲突
                        if ScheduleApplyTypeEnum.project.value in schedule_apply_type:
                            return None
                        else:
                            return data_id

                    else:
                        # 如果是个人类型日程
                        if ScheduleApplyTypeEnum.personal.value in schedule_apply_type:
                            return None
                        else:
                            activity_interview = interview.activity_interview.filter(deleted=False).exists()
                            # 如果是活动类型日程
                            if activity_interview:
                                if ScheduleApplyTypeEnum.activity.value in schedule_apply_type:
                                    return None
                                else:
                                    return data_id
                            else:
                                return data_id
            else:
                return None
        else:
            return None


def find_first_conflict(schedule_datetime_list, all_data_sorted):
    """
    在给定的日程列表和已排序的现有日程列表之间查找第一个冲突。

    此函数遍历重复日程列表（schedule_datetime_list）中的每个时间段，
    并将其与已排序的现有日程列表（all_data_sorted）中的时间段进行比较，
    以找到可能存在的第一个冲突。一旦找到冲突，立即返回冲突日程的ID。

    :param schedule_datetime_list: 重复日程的find_first_conflict时间列表，其中每个元素为 [开始时间, 结束时间] 的列表。
    :param all_data_sorted: 已经根据开始时间排序的现有日程时间列表，其中每个元素为 [开始时间, 结束时间, 日程ID，public_attr标识] 的列表。
    :return: 第一个冲突的日程ID，如果没有找到冲突，则返回 None。
    """
    # 遍历重复日程列表
    for schedule_time in schedule_datetime_list:
        # 再遍历现有日程列表
        for data_item in all_data_sorted:
            data_id = check_schedule_overlap(schedule_time, data_item)
            if data_id:
                return data_id  # 找到冲突，返回冲突的日程ID
    return None  # 没有


def adjust_event_times(start_time_str, end_time_str, is_all_day):
    """
    将日期字符串转换为datetime数据，根据是否为全天事件调整开始和结束时间。

    :param start_time_str: 开始时间字符串
    :param end_time_str: 结束时间字符串
    :param is_all_day: 布尔值，指示是否为全天事件
    :return: 返回一个元组，包含错误信息（如果有），以及调整后的开始和结束时间对象
    """
    try:
        # 解析起始和结束时间
        start_time = pendulum.parse(start_time_str)
        end_time = pendulum.parse(end_time_str)

        if is_all_day:
            start_time = start_time.replace(hour=8, minute=00, second=00, microsecond=000000)
            end_time = end_time.replace(hour=22, minute=00, second=00, microsecond=000000)
        # 将时间设置为本地时间（去除时区信息）
        start_time = start_time.naive()
        end_time = end_time.naive()

        # 检测开始结束时间
        if start_time >= end_time:
            return '结束时间应大于开始时间', None, None

    except ValueError as e:
        # 处理日期解析错误
        return f"日期解析错误", None, None
    except TypeError as e:
        # 处理类型错误，比如参数类型不正确
        return f"参数类型错误", None, None

    return None, start_time, end_time
