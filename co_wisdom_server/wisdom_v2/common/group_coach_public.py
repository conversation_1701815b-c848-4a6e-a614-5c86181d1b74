from datetime import datetime

from utils import utc_date_time
from utils.api_response import WisdomValidationError
from wisdom_v2.common import business_order_public
from wisdom_v2.enum.business_order_enum import BusinessOrderTypeEnum, WorkTypeEnum
from wisdom_v2.enum.project_interview_enum import GroupCoachTypeEnum, ProjectInterviewTypeEnum, \
    ProjectInterviewPlaceCategoryEnum, InterviewRecordTypeEnum, ProjectInterviewPlaceTypeEnum, InterviewSubjectEnum
from wisdom_v2.enum.service_content_enum import ScheduleTypeEnum
from wisdom_v2.models import ProjectCoach, ProjectGroupCoach, GroupCoach, Schedule, PowerTag, ProjectInterview, \
    PublicAttr
from wisdom_v2.views.constant import ATTR_STATUS_INTERVIEW_CANCEL, ATTR_TYPE_INTERVIEW, ATTR_STATUS_INTERVIEW_CONFIRM


def get_group_coach_details(group_coach):
    project_coach = ProjectCoach.objects.filter(
        project_group_coach__coach_group_module=group_coach,
        coach__isnull=False,
        deleted=False,
    ).first()
    if project_coach:
        coach_name = project_coach.coach.user.cover_name
        coach_id = project_coach.coach_id
    else:
        coach_name = None
        coach_id = None

    group_coach_data = {
        "id": group_coach.id,
        "theme": group_coach.project_group_coach.theme,
        "course_place": group_coach.project_group_coach.place,
        "course_date": datetime.strftime(group_coach.project_group_coach.start_time, "%Y-%m-%d"),
        "start_course_time": datetime.strftime(group_coach.project_group_coach.start_time, "%H:%M"),
        "end_course_time": datetime.strftime(group_coach.project_group_coach.end_time, "%H:%M"),
        "template_id": group_coach.inter_view_record_template_id,
        "template_name": group_coach.inter_view_record_template.name,
        "coach_id": coach_id,
        "coach_name": coach_name}

    if group_coach.power_tag.filter(deleted=False).exists():
        tag_list = []
        for tag in group_coach.power_tag.filter(deleted=False):
            tag_list.append(tag.tag)
        group_coach_data['power_tag'] = tag_list

    return group_coach_data


def create_group_coach_details(content, project_bundle, group_coach_type):
    """
    创建集体辅导流程
    ：content dict 集体辅导数据
    ：project_bundle object 用户标识
    ：group_coach_type int 辅导类型 1-工作坊 2-小组辅导
    : return object group_coach对象
    """

    start_course_time = content['start_course_time']
    end_course_time = content['end_course_time']
    course_place = content['course_place']
    theme = content['theme']
    template_id = content['template_id']
    coach_id = content.get('coach_id')
    power_tag = content.get('power_tag')

    # 项目创建content会有project_group_coach_id，优先看是否是存在
    # 没有的情况下再去通过数据精准匹配
    project_group_coach_id = content.get('project_group_coach_id')
    if not project_group_coach_id:
        # 项目集体辅导
        project_group_coach, status = ProjectGroupCoach.objects.get_or_create(
            project_id=project_bundle.project_id,
            start_time=start_course_time,
            type=group_coach_type,
            end_time=end_course_time,
            place=course_place,
            theme=theme,
            deleted=False
        )
        project_group_coach_id = project_group_coach.id
    else:
        # 已有的要更新数据，保持一致
        project_group_coach = ProjectGroupCoach.objects.get(id=project_group_coach_id, deleted=False)
        project_group_coach.start_time = start_course_time
        project_group_coach.end_time = end_course_time
        project_group_coach.place = course_place
        project_group_coach.theme = theme
        project_group_coach.save()

    if GroupCoach.objects.filter(project_bundle_id=project_bundle.id,
                                 project_group_coach_id=project_group_coach_id, deleted=False).exists():
        raise WisdomValidationError('当前集体辅导已存在，请勿重复添加')

    # 拼接辅导基础数据
    public_attr_dict = {
        "project_id": project_bundle.project_id, "type": ATTR_TYPE_INTERVIEW,
        "target_user_id": project_bundle.project_member.user_id, "status": ATTR_STATUS_INTERVIEW_CONFIRM,
        "start_time": start_course_time, "end_time": end_course_time}

    # 绑定教练数据
    project_coach, is_create = ProjectCoach.objects.get_or_create(
        project_group_coach_id=project_group_coach_id,
        project_id=project_bundle.project_id,
        deleted=False)
    if is_create and coach_id:
        project_coach.coach_id = coach_id
        project_coach.save()
    if not is_create and not project_coach.coach_id:
        project_coach.coach_id = coach_id
        project_coach.save()

    # 如果当前集体已匹配教练后续加入当前集体辅导的用户自动匹配教练
    if project_coach:
        public_attr_dict['user_id'] = project_coach.coach.user.id

    # 创建辅导
    public_attr = PublicAttr.objects.create(**public_attr_dict)

    minis = utc_date_time.get_total_time(start_course_time, end_course_time).minute
    # 创建集体辅导记录
    project_interview_dict = {
        "public_attr_id": public_attr.pk, "topic": theme,
        "place_category": ProjectInterviewPlaceCategoryEnum.offline_group_coach.value,
        "place": course_place, "type": ProjectInterviewTypeEnum.formal_interview.value,
        "times": minis,
        "interview_subject": InterviewSubjectEnum.one_to_many.value,  # 一对多约谈
        'record_type': InterviewRecordTypeEnum.questionnaire.value,
        'place_type': ProjectInterviewPlaceTypeEnum.offline.value,
    }
    interview = ProjectInterview.objects.create(**project_interview_dict)

    # 创建用户集体辅导服务配置，绑定interview
    group_coach = GroupCoach.objects.create(**{
        "interview": interview,
        "project_bundle_id": project_bundle.id,
        "inter_view_record_template_id": template_id,
        "project_group_coach_id": project_group_coach_id
    })

    if power_tag:
        tag_list = []
        for tag in power_tag:
            tag_list.append(PowerTag(group_coach=group_coach, tag=tag))
        if tag_list:
            PowerTag.objects.bulk_create(tag_list)

    # 如果是新创建集体辅导，就给教练创建日程。
    # 一个集体辅导只对应一个教练日程
    if is_create:
        Schedule.objects.create(public_attr_id=public_attr.pk, type=ScheduleTypeEnum.interview)

    return group_coach


def update_group_coach_details(content, now_group_coach):
    start_course_time = content['course_date'] + ' ' + content['start_course_time'] + ':00'
    end_course_time = content['course_date'] + ' ' + content['end_course_time'] + ':00'
    course_place = content['course_place']
    theme = content['theme']
    template_id = content['template_id']
    power_tag = content.get('power_tag')
    minis = utc_date_time.get_total_time(start_course_time, end_course_time).minute

    # 查询当前集体辅导的辅导记录
    project_interview = ProjectInterview.objects.filter(
        coach_group_module__project_group_coach_id=now_group_coach.project_group_coach_id, deleted=False)
    
    # 更新辅导开始-结束时间
    PublicAttr.objects.filter(pk__in=project_interview.values_list('public_attr_id', flat=True)).update(
        start_time=start_course_time, end_time=end_course_time
    )
    # 更新辅导主题，地址，时长
    project_interview.update(topic=theme, place=course_place, times=minis)

    now_group_coach.inter_view_record_template_id = template_id
    now_group_coach.save()
    # 同步修改项目集体辅导表数据
    project_group_coach = now_group_coach.project_group_coach
    project_group_coach.theme = theme
    project_group_coach.start_time = start_course_time
    project_group_coach.end_time = end_course_time
    project_group_coach.place = course_place
    project_group_coach.save()

    if power_tag:
        old_power_tag_list = [tag.tag for tag in now_group_coach.power_tag.filter(
            deleted=False)]
        now_power_tag = power_tag
        deleted_tag_list = list(set(old_power_tag_list).difference(set(now_power_tag)))
        # 查询删除的标签，并删除
        if deleted_tag_list:
            PowerTag.objects.filter(tag__in=deleted_tag_list, deleted=False,
                                    group_coach=now_group_coach).update(deleted=True)
        # 查询添加的标签，并添加
        add_tag_list = list(set(now_power_tag).difference(set(old_power_tag_list)))
        if add_tag_list:
            tag_list = []
            for tag in add_tag_list:
                tag_list.append(PowerTag(group_coach=now_group_coach, tag=tag))
            if tag_list:
                PowerTag.objects.bulk_create(tag_list)
    else:
        if now_group_coach.power_tag.filter(deleted=False).exists():
            now_group_coach.power_tag.filter(deleted=False).update(deleted=True)


def del_group_coach(group_coach):

    if group_coach.project_group_coach.type == GroupCoachTypeEnum.collective_tutoring.value:
        work_type_type = WorkTypeEnum.group_coach.value
    elif group_coach.project_group_coach.type == GroupCoachTypeEnum.group_tutoring.value:
        work_type_type = WorkTypeEnum.group_counseling.value
    else:
        work_type_type = None

    if work_type_type:
        if business_order_public.get_object_settlement_status(
                [group_coach.project_group_coach_id],
                BusinessOrderTypeEnum.enterprise.value, work_type_type):
            raise WisdomValidationError('已发起提现不可删除')

    project_interview = group_coach.interview
    if project_interview:
        public_attr = project_interview.public_attr
        public_attr.status = ATTR_STATUS_INTERVIEW_CANCEL
        public_attr.save()

        # 查询剩余的集体辅导里面没有取消的
        other_project_interview = ProjectInterview.objects.filter(
            coach_group_module__project_group_coach_id=group_coach.project_group_coach_id, deleted=False).exclude(
            id=project_interview.id).exclude(public_attr__status=ATTR_STATUS_INTERVIEW_CANCEL).first()

        # 查询集体辅导是否对应的教练日程
        schedule = Schedule.objects.filter(
            public_attr=public_attr, type=ScheduleTypeEnum.interview.value, deleted=False).first()

        #  如果有则更新日程对应的public_attr
        if other_project_interview:
            if schedule:
                schedule.public_attr = project_interview.public_attr
                schedule.save()

        # 如果没有则删除日程和关联的教练
        else:
            ProjectCoach.objects.filter(project_group_coach__coach_group_module__id=group_coach.id).update(deleted=True)
            ProjectGroupCoach.objects.filter(id=group_coach.project_group_coach_id).update(deleted=True)
            if schedule:
                schedule.deleted = True
                schedule.save()

            if work_type_type:
                business_order_public.del_business_order(
                    [group_coach.project_group_coach_id], BusinessOrderTypeEnum.enterprise.value, work_type_type)

    group_coach.deleted = True
    group_coach.save()
