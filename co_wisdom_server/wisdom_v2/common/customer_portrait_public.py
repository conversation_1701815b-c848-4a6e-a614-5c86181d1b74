from django.db.models import Q

from wisdom_v2.common import project_service_public
from wisdom_v2.enum.service_content_enum import CustomerPortraitTypeEnum, PortraitInterviewTypeEnum
from wisdom_v2.models import User, CustomerPortrait


def customer_portrait_data_check(data):
    if data.get('customer_name') and len(data.get('customer_name', '')) > 50:
        return '姓名限制50个字以内'
    if data.get('customer_position') and len(data.get('customer_position')) > 50:
        return '职位限制50个字以内'
    if data.get('customer_age') and len(data.get('customer_age', '')) > 50:
        return '年龄限制50个字以内'
    if data.get('city') and len(data.get('city', '')) > 500:
        return '客户所在城市限制500个字以内'
    if data.get('customer_style') and len(data.get('customer_style', '')) > 500:
        return '个性及特点限制500个字以内'
    if data.get('challenge') and len(data.get('challenge', '')) > 500:
        return '当前面临的挑战限制500个字以内'
    if data.get('coach_expect') and len(data.get('coach_expect', '')) > 500:
        return '对教练认识的期待限制500个字以内'
    if data.get('coach_ability') and len(data.get('coach_ability', '')) > 500:
        return '专业教练能力限制500个字以内'
    if data.get('coach_experience') and len(data.get('coach_experience', '')) > 500:
        return '教练擅长领域过往经验限制500个字以内'
    if data.get('coach_style') and len(data.get('coach_style', '')) > 500:
        return '教练风格或特点限制500个字以内'
    if data.get('coach_extra') and len(data.get('coach_extra', '')) > 500:
        return '供教练参考的其他信息限制500个字以内'
    if data.get('group_target') and len(data.get('group_target', '')) > 500:
        return '组织方的目的限制500个字以内'
    if data.get('personal_target') and len(data.get('personal_target', '')) > 500:
        return '被教练者的目的限制500个字以内'

    if data.get('type'):
        if not isinstance(data['type'], int):
            return '客户类型参数的数据类型错误'
        if data['type'] not in CustomerPortraitTypeEnum.get_describe_keys():
            return '客户类型不在已有类型范围'

    if data.get('interview_type'):
        if not isinstance(data['interview_type'], int):
            return '教练类型参数的数据类型错误'
        if data['interview_type'] not in PortraitInterviewTypeEnum.get_describe_keys():
            return '教练类型不在已有类型范围'

    if data.get('user_id'):
        if not isinstance(data['user_id'], int):
            return '绑定用户的用户信息的数据类型错误'
        try:
            User.objects.get(pk=data['user_id'], deleted=False)
        except Exception:
            return '用户信息错误，对应用户不存在'


def create_coach_customer_portrait(request_data, coach_user, coachee_user, existing_portrait):
    """
    创建教练客户画像
    :param request_data: 请求数据
    :param coach_user: 教练用户
    :param coachee_user: 被教练用户
    :param existing_portrait: 已存在的画像
    :return: 新画像
    """

    # 如果是项目画像，需要将项目画像的特征赋值给个性及特点
    if existing_portrait and existing_portrait.type == CustomerPortraitTypeEnum.group and existing_portrait.project_id:
        existing_portrait.customer_style = existing_portrait.group_features

    # 生成新画像的属性字典
    new_customer_portrait_fields = {
        field: request_data.get(field, getattr(existing_portrait, field) if existing_portrait else None)
        for field in ['customer_style', 'customer_experience', 'coach_expect',
                      'city', 'challenge', 'coach_extra', 'gender']}

    # 添加coach和user
    new_customer_portrait_fields['coach'] = coach_user
    new_customer_portrait_fields['user'] = coachee_user

    return CustomerPortrait.objects.create(**new_customer_portrait_fields)


def update_customer_portrait_coach_extra(coach_user, customer_user, note, project_id):
    """
    更新或创建客户画像记录。

    :param coach_user: 教练的用户对象，表示执行更新操作的教练。
    :param customer_user: 客户的用户对象，表示需要更新画像的客户。
    :param note: 要添加到客户画像中的注释或笔记。
    :param project_id: 项目ID，用于在关联项目的情况下筛选或创建画像记录。
    :return:
    """

    # 查询是否已有教练和用户对应的画像记录，且该记录未被删除
    customer_portrait = CustomerPortrait.objects.filter(
        coach_id=coach_user.id, user_id=customer_user.id, deleted=False).first()

    # 如果存在对应的画像记录
    if customer_portrait:
        # 在已有的教练额外信息后添加新的笔记
        customer_portrait.coach_extra = customer_portrait.coach_extra + '\n' + note if customer_portrait.coach_extra else note
        customer_portrait.save()
    # 如果不存在对应的画像记录
    else:
        # 查询是否有与项目关联的画像记录
        customer_portrait = CustomerPortrait.objects.filter(
            Q(type=CustomerPortraitTypeEnum.group) | Q(user=customer_user),
            project_id=project_id, deleted=False).order_by('type').first()

        # 如果存在项目画像记录
        if customer_portrait:
            # 获取全部的教练额外信息，并添加新的笔记
            coach_extra = customer_portrait.coach_extra + '\n' + note if customer_portrait.coach_extra else note
            create_coach_customer_portrait(
                {'coach_extra': coach_extra}, coach_user, customer_user, customer_portrait)
        else:
            # 如果没有项目画像，则创建一个新的画像记录
            CustomerPortrait.objects.create(
                coach=coach_user, user=customer_user, coach_extra=note)


def chemical_interview_update_customer_portrait(project_interview):
    """
    根据化学面试项目更新客户画像信息，包括客户个性特征、客户对教练的了解度以及教练的初步策略。

    参数:
        project_interview (ProjectInterview): 化学面试项目实例。
    返回:
        CustomerPortrait: 更新后的客户画像实例。
    """
    # 获取学员和教练的用户ID
    coachee_user_id = project_interview.public_attr.target_user_id
    coach_user_id = project_interview.public_attr.user_id

    # 尝试获取或创建客户画像实例
    coach_customer_portrait, is_create = CustomerPortrait.objects.get_or_create(
        user_id=coachee_user_id,
        coach_id=coach_user_id,
        project_id__isnull=True,
        deleted=False
    )

    # 更新客户个性特征及面临的挑战
    focus_topic_answer = project_service_public.get_chemical_interview_coach_question(project_interview, 5)
    if focus_topic_answer and focus_topic_answer.answer:
        if coach_customer_portrait.challenge:
            coach_customer_portrait.challenge += f'\n{focus_topic_answer.answer}'
        else:
            coach_customer_portrait.challenge = focus_topic_answer.answer

    # 更新客户对教练的了解度
    coach_knowledge_answer = project_service_public.get_chemical_interview_coach_question(project_interview, 1)
    if coach_knowledge_answer and coach_knowledge_answer.option and coach_knowledge_answer.option.title:
        if coach_customer_portrait.coach_expect:
            coach_customer_portrait.coach_expect += f'\n{coach_knowledge_answer.option.title}'
        else:
            coach_customer_portrait.coach_expect = f'{coach_knowledge_answer.option.title}'
        # 增加教练额外补充数据
        if coach_knowledge_answer.option_custom:
            coach_customer_portrait.coach_expect += f'，{coach_knowledge_answer.option_custom}'

    # 更新教练针对客户的初步策略
    strategy_answer = project_service_public.get_chemical_interview_coach_question(project_interview, 6)
    if strategy_answer and strategy_answer.answer:
        if coach_customer_portrait.coach_extra:
            coach_customer_portrait.coach_extra += f'\n{strategy_answer.answer}'
        else:
            coach_customer_portrait.coach_extra = strategy_answer.answer
    # 保存客户画像实例
    coach_customer_portrait.save()

    # 返回更新后的客户画像实例
    return coach_customer_portrait
