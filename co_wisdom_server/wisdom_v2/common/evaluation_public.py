import datetime

from wisdom_v2.enum.service_content_enum import EvaluationWriteRoleEnum
from wisdom_v2.models import Evaluation, EvaluationQuestion, EvaluationOption, EvaluationReport, ProjectInterested, \
    EvaluationModule, PublicAttr
from wisdom_v2.views import constant
from wisdom_v2.views.constant import ATTR_TYPE_EVALUATION_REPORT


def copy_evaluation(evaluation, evaluation_name=None, role=None):
    """
    复制一个现有的测评对象到一个新的测评对象中，并可以指定新测评的名称。

    参数:
        evaluation (Evaluation): 要复制的原始测评对象。
        evaluation_name (str, optional): 新测评的名称。如果提供，将使用此名称；如果未提供，则使用原测评的名称。
        role (str, optional): 新测评的填写人角色。

    返回:
        Evaluation: 新创建的测评对象。

    功能说明:
        1. 创建一个新的测评对象，复制原测评的基本信息，如简介、相关信息、图片URL、代码，角色。
        2. 复制所有关联的未被删除的测评问题到新测评中，包括问题的顺序、标题和是否多选。
        3. 对每个测评问题，复制其所有未被删除的选项，包括选项的顺序、得分、标题和类型，并绑定到相应的新问题上。
        4. 使用`bulk_create`批量创建选项，以提高数据库操作效率。
        5. 函数最终返回新创建的测评对象，供进一步使用或操作。
    """

    # 创建新的测评model对象
    new_evaluation = Evaluation.objects.create(
        name=evaluation_name if evaluation_name else evaluation.name,
        brief=evaluation.brief,
        about=evaluation.about,
        image_url=evaluation.image_url,
        code=evaluation.code,
        role=role if role else evaluation.role,
    )

    # 创建测评问题并绑定
    evaluation_question = EvaluationQuestion.objects.filter(evaluation=evaluation, deleted=False).all()

    for question in evaluation_question:
        new_evaluation_question = EvaluationQuestion.objects.create(
                evaluation=new_evaluation,
                order=question.order,
                title=question.title,
                multi=question.multi)

        new_evaluation_option = []
        # 创建测评问题对应的选择并绑定
        evaluation_option = EvaluationOption.objects.filter(question=question, deleted=False).all()
        for option in evaluation_option:
            new_evaluation_option.append(
                EvaluationOption(
                    question=new_evaluation_question,
                    order=option.order,
                    score=option.score,
                    title=option.title,
                    type=option.type)
            )
        EvaluationOption.objects.bulk_create(new_evaluation_option)

    return new_evaluation


def check_report_and_expiration(evaluation, user, project, evaluation_module):
    """
    检查测评报告和测评模块是否达到结束时间。

    参数:
    evaluation (Evaluation): 当前的测评对象。
    user (User): 当前用户对象。
    project (Project): 当前项目对象。
    evaluation_module (EvaluationModule): 当前测评模块对象。

    返回:
    tuple: 包含两个元素的元组 (report, is_expired)。
        report (EvaluationReport or None): 如果找到对应的测评报告，则返回测评报告对象，否则返回 None。
        is_expired (bool): 如果当前时间晚于测评模块的结束时间，则返回 True，否则返回 False。
    """
    # 查找当前测评和用户在指定项目中的测评报告
    report = EvaluationReport.objects.filter(
        evaluation=evaluation,
        public_attr__user=user,
        public_attr__project=project,
        public_attr__type=ATTR_TYPE_EVALUATION_REPORT
    ).first()

    # 判断测评模块是否达到结束时间（当前时间是否晚于测评结束时间）
    is_expired = evaluation_module.end_time < datetime.datetime.now().date()

    return report, is_expired


def get_master_users_evaluation(user_id):
    """
    利益相关者获取需要填写的测评信息
    参数:
    user_id (int): 利益相关者用户的ID

    返回:
    list: 与用户相关的测评信息列表
    """

    # 获取利益相关者的主用户
    master_users = ProjectInterested.objects.filter(
        interested_id=user_id, deleted=False
    ).values_list('master', flat=True)

    # 主用户是否有需要填写的lbi测评
    raw_users_evaluation = EvaluationModule.objects.filter(
        evaluation__code=constant.LBI_EVALUATION,
        evaluation__role=EvaluationWriteRoleEnum.coachee_stakeholder.value,
        deleted=False,
        project_bundle__project_member__user_id__in=list(master_users)
    ).order_by('end_time').all()

    users_evaluation = []
    # 遍历所有的测评信息
    for item in raw_users_evaluation:
        # 检查是否已经填写
        public_attr = PublicAttr.objects.filter(
            project=item.project_bundle.project,
            target_user_id=item.project_bundle.project_member.user.id,
            user=user_id,
            type=constant.ATTR_TYPE_EVALUATION_ANSWER
        ).exists()
        # 如果未填写，添加到需要返回的数据列表中
        if not public_attr:
            users_evaluation.append(item)

    # 返回过滤后的评估信息列表
    return users_evaluation


