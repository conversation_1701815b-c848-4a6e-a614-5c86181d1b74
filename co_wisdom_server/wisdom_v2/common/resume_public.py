import json

import redis
import datetime
import requests
from django.conf import settings

from utils.message.lark_message import LarkMessageCenter
from wisdom_v2.enum.message_type_enum import LarkMessageTypeEnum
from wisdom_v2.enum.user_enum import CoachUserTypeEnum
from wisdom_v2.models import Coach, Resume

data_redis = redis.Redis.from_url(settings.DATA_REDIS)


def update_resume_share_url(resume_id, head_image_url):
    """
    调用云函数更新简历分享链接
    :param resume_id Resume 简历对象标识
    :param head_image_url 头像信息
    :return 简历链接
    """
    if not head_image_url:
        return

    background_image = settings.RESUME_BACKGROUND_IMAGE_URL
    url = settings.RESUME_SHARE_IMAGE_URL

    # 背景图和用户简历头像
    params = {
        'background_image': background_image,
        'main_image': head_image_url,
    }

    req = requests.get(url, params=params)

    if req.ok:
        resume = Resume.objects.filter(id=resume_id).first()
        if not resume:
            LarkMessageCenter().send_other_backend_message(
                '获取简历分享图失败', 'error',
                f"resume_id:{resume_id}; "
                f"params: {params}, "
                f"text': 未获取到简历数据")
            return
        data = req.json()
        resume.share_image_url = data.get('url')
        resume.save()
        return data
    else:
        LarkMessageCenter().send_other_backend_message(
            '获取简历分享图失败', 'error',
            f"resume_id:{resume_id}; "
            f"params: {params}, "
            f"text': {req.json()}")
        return


def record_coach_update_resume(resume):

    # 将更新过教练简历的用户记录到缓存中
    # 第二天早上九点发消息，所以保存时间得超过一天
    redis_key = f"{datetime.datetime.now().strftime('%Y_%m_%d')}_update_resume_coach_id_list"
    raw_update_resume_coach_id_list = data_redis.get(redis_key)
    if raw_update_resume_coach_id_list:
        update_resume_coach_id_list = json.loads(raw_update_resume_coach_id_list.decode())
        # 如果教练已存在缓存中，不重复计数
        if resume.coach_id not in update_resume_coach_id_list:
            update_resume_coach_id_list.append(resume.coach_id)
            data_redis.set(redis_key, json.dumps(update_resume_coach_id_list), ex=3600 * 24 * 2)
    else:
        update_resume_coach_id_list = [resume.coach_id]
        data_redis.set(redis_key, json.dumps(update_resume_coach_id_list), ex=3600 * 24 * 2)


def send_date_coach_update_resume_msg(date):
    """
    根据提供的日期，发送更新简历的教练信息。
    :param date: 用于生成 Redis 键的日期
    """

    # 从 Redis 获取与日期相关的教练 ID 列表
    redis_key = f"{date}_update_resume_coach_id_list"
    raw_update_resume_coach_id_list = data_redis.get(redis_key)

    # 初始化存储不同类型教练名称的列表
    student_name = []
    personal_name = []
    enterprise_name = []

    # 如果从 Redis 获取到数据
    if raw_update_resume_coach_id_list:
        # 解码获取到的数据并转换为列表
        update_resume_coach_id_list = json.loads(raw_update_resume_coach_id_list.decode())
        update_resume_coach_id_list = list(set(update_resume_coach_id_list))

        # 遍历教练 ID 列表
        for coach_id in update_resume_coach_id_list:
            # 获取教练对象
            coach = Coach.objects.filter(id=coach_id).first()
            if coach:
                coach_type = coach.coach_type

                # 根据教练类型分类，并添加教练名称到相应列表
                if coach_type == CoachUserTypeEnum.student.value:
                    student_name.append(coach.user.cover_name)
                elif coach_type in CoachUserTypeEnum.personal_coach_value():
                    personal_name.append(coach.user.cover_name)
                elif coach_type in CoachUserTypeEnum.enterprise_coach_value():
                    enterprise_name.append(coach.user.cover_name)

    # 构建包含不同类型教练名称的数据字典
    data = {}
    if student_name:
        data['student_name'] = student_name
    if personal_name:
        data['personal_name'] = personal_name
    if enterprise_name:
        data['enterprise_name'] = enterprise_name

    # 如果数据字典不为空，则发送业务消息
    if data:
        LarkMessageCenter().send_business_message(data, LarkMessageTypeEnum.coach_update_resume.value)
    return


def verify_resume_required_completion(resume):
    """
    校验简历必填项是否完成填写

    参数:
    resume (Resume): 简历对象

    返回:
    bool: 如果所有必填项都已填写，返回 True，否则返回 False
    """

    # 获取教练类型
    coach = resume.coach
    user = resume.coach.user
    coach_type = coach.coach_type

    # 如果教练类型是学生
    if coach_type == CoachUserTypeEnum.student.value:
        return all([
            user.email,  # 用户邮箱
            user.phone,  # 用户电话
            coach.personal_name,  # 教练个人名称
            coach.city,  # 教练所在城市
            coach.price,  # 教练收费
            coach.posters_text,  # 教练海报文字
            resume.coach_language,  # 简历中的教练语言
            resume.head_image_url or user.head_image_url,  # 简历头像URL或用户头像URL
            resume.job_profile,  # 工作简介
            resume.work_experience,  # 工作经验
            resume.coach_experience,  # 教练经验
            resume.coach_style,  # 教练风格
            resume.customer_evaluate,  # 客户评价
        ])
    # 如果教练类型是私人教练
    elif coach_type in CoachUserTypeEnum.personal_coach_value():
        return all([
            user.email,  # 用户邮箱
            user.phone,  # 用户电话
            coach.personal_name,  # 教练个人名称
            coach.city,  # 教练所在城市
            coach.price,  # 教练收费
            coach.posters_text,  # 教练海报文字
            resume.coach_language,  # 简历中的教练语言
            resume.head_image_url or user.head_image_url,  # 简历头像URL或用户头像URL
            resume.job_profile,  # 工作简介
            resume.work_experience,  # 工作经验
            resume.working_years,  # 工作年限
            resume.coach_industry,  # 教练行业
            resume.coach_domain,  # 教练领域
            resume.coach_experience,  # 教练经验
            resume.coach_style,  # 教练风格
            resume.customer_evaluate,  # 客户评价
        ])
    # 其他教练类型不做校验，直接返回 True
    else:
        return True
