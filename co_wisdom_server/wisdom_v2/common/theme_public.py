from wisdom_v2.enum.service_content_enum import AppSelectArticleTypeEnum


def format_articles_and_coaches(articles, coaches, non_platform_articles):
    """
    将文章和教练格式化为字典列表，并避免重复项。

    参数:
    articles (QuerySet): 包含 Article 实例的 Django QuerySet。
    coaches (QuerySet): 包含 Coach 实例的 Django QuerySet。
    non_platform_articles (QuerySet): 包含 NonPlatformArticles 实例的 Django QuerySet。

    返回:
    tuple: 分别包含文章和教练数据的两个字典列表。
    """

    all_article_list = []

    non_platform_article_ids = []
    for non_platform_article in non_platform_articles:
        # 确保每篇非平台文章只被添加一次
        if non_platform_article.id not in non_platform_article_ids:
            all_article_list.append({
                "id": non_platform_article.id,
                "title": non_platform_article.title,
                "category": None,
                'type': AppSelectArticleTypeEnum.wechat_official_account.value,  # 微信公众号文章
                "image_url": non_platform_article.image_url,
                "jump_link": non_platform_article.jump_link,
                "is_video": non_platform_article.is_video
            })
            non_platform_article_ids.append(non_platform_article.id)

    article_ids = []
    for article in articles:
        # 确保每篇文章只被添加一次
        if article.id not in article_ids:
            all_article_list.append({
                "id": article.id,
                "title": article.title,
                "category": article.category,
                'type': AppSelectArticleTypeEnum.platform_articles.value,  # 平台内文章
                "image_url": article.image_url,
                "is_video": "<video" in article.content,  # 检查内容中是否包含视频标签
                "jump_link": None
            })
            article_ids.append(article.id)

    coach_list, coach_ids = [], []
    for coach in coaches:
        # 确保每位教练只被添加一次
        if coach.id not in coach_ids:
            resume = coach.resumes.first()  # 获取与教练相关的第一份简历
            if resume:
                # 收集教练数据，对缺失字段进行条件检查
                tmp_data = {
                    "id": coach.id,
                    "true_name": coach.personal_name,
                    "city": coach.city,
                    "resume_id": resume.id,
                    "head_image_url": resume.head_image_url if resume.head_image_url else coach.user.head_image_url,
                    "price": coach.display_price,
                    "domain": resume.coach_domain[:3] if resume.coach_domain else []
                }
                coach_list.append(tmp_data)
                coach_ids.append(coach.id)
    return all_article_list, coach_list
