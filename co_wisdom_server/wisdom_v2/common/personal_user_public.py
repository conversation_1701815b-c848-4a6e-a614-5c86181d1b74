from wisdom_v2.enum.service_content_enum import PersonaSourceEnum, UserInviteTypeEnum, ActivityCoachStatusEnum, \
    PersonalActivityTypeEnum
from wisdom_v2.models import PersonalUser, UserInviteRecord
from wisdom_v2.models_file import ActivityCoach, PersonalActivity


def get_to_c_user_invite_info(interview):
    """
    获取辅导对象的个人用户来源信息。

    此函数根据提供的辅导信息，查询相关的个人用户（PersonalUser）记录，
    并返回该用户的来源信息（如果存在）。

    :param interview: 面试对象，包含指向目标用户的信息。
    :return: 返回个人用户的来源信息。如果没有找到相应的个人用户记录，返回空字符串。
    """

    # 根据面试对象中的目标用户 ID 查询个人用户记录
    personal_user = PersonalUser.objects.filter(
        user_id=interview.public_attr.target_user_id, deleted=False).first()

    # 如果找到了个人用户记录，返回其来源信息
    if personal_user:
        if personal_user.invite and personal_user.invite.referrer:
            referrer_name = personal_user.invite.referrer.true_name
        else:
            referrer_name = None
        return PersonaSourceEnum.get_display(personal_user.source), referrer_name

    # 如果没有找到个人用户记录，返回空字符串
    return '', None


def invite_code_displace_channel(invite_code):
    """
    根据邀请码返回对应的邀请渠道。

    参数:
    - invite_code: 邀请码

    返回:
    - channel1: 一级渠道
    - channel2: 二级渠道
    - channel3: 三级渠道
    """
    channel1 = '1'  # 默认自然流量
    channel2 = None
    channel3 = None

    # 查询邀请码对应的邀请记录
    invite = UserInviteRecord.objects.filter(pk=invite_code, deleted=False).first()
    if not invite:
        channel3 = invite_code
        return channel1, channel2, channel3
    invite_type = invite.type

    if invite_type in [
        UserInviteTypeEnum.coach_resume.value,
        UserInviteTypeEnum.wechat_official_account.value,
        UserInviteTypeEnum.wechat_official_account_article.value,
        UserInviteTypeEnum.video_live.value,
    ]:
        # 微信公众号相关邀请类型
        if invite_type == UserInviteTypeEnum.wechat_official_account.value:
            channel2 = '1001'  # 公众号
        elif invite_type == UserInviteTypeEnum.wechat_official_account_article.value:
            channel2 = '1002'  # 公众号文章
        elif invite_type == UserInviteTypeEnum.video_live.value:
            channel2 = '1003'  # 直播
        elif invite_type == UserInviteTypeEnum.coach_resume.value:
            channel2 = '1004'  # 客户邀请
            channel3 = invite.referrer.true_name

    elif invite_type == UserInviteTypeEnum.interview.value:
        # 教练邀请
        channel1 = '3'  # 教练邀请
        channel2 = '3002'  # 个人邀请
        channel3 = invite.referrer.true_name
    elif invite_type == UserInviteTypeEnum.activity.value:
        # 活动邀请
        if invite.referrer_id:
            true_name = invite.referrer.true_name
            channel3 = true_name  # 邀请人姓名

            if ActivityCoach.objects.filter(
                    status__in=[ActivityCoachStatusEnum.joined.value,
                                ActivityCoachStatusEnum.not_invitation.value],
                    coach__user_id=invite.referrer_id,
                    activity_id=invite.object_id, deleted=False).exists():
                channel1 = '3'  # 教练邀请
                channel2 = '3001'  # 活动邀请
            else:
                channel1 = '2'
                if true_name == '视频号':
                    channel2 = '2003'  # 视频号
                elif true_name == '公众号':
                    channel2 = '2004'  # 公众号
                elif true_name == '公众号文章':
                    channel2 = '2006'  # 公众号文章
                else:
                    channel2 = '2005'  # 个人邀请
        else:
            channel1 = '2'  # 活动邀请
            channel2 = '2001'  # 平台邀请

    elif invite_type == UserInviteTypeEnum.coach_share.value:
        personal_activity = PersonalActivity.objects.filter(deleted=False, id=invite.object_id).first()

        if personal_activity and personal_activity.type == PersonalActivityTypeEnum.wechat_share.value:
            # 教练分享
            channel1 = '3'  # 教练邀请
            channel2 = '3003'  # 微信邀请
            channel3 = invite.referrer.true_name  # 教练姓名
        else:
            # 教练分享
            channel1 = '3'  # 教练邀请
            channel2 = '3002'  # 个人邀请
            channel3 = invite.referrer.true_name  # 教练姓名
    elif invite_type == UserInviteTypeEnum.sms.value:
        # 短信邀请
        channel1 = '2'  # 活动邀请
        channel2 = '2002'  # 短信邀请

    return channel1, channel2, channel3


def get_to_c_interview_source(interview):
    """
    获取到C端的预约来源。
    : param interview: 辅导对象
    : return: 预约来源
    """
    source = '个人预约'
    if interview.order:
        if interview.order.activity:
            source = f'公益教练活动-{interview.order.activity.theme}'
        elif interview.order.personal_activity:
            source = f'教练专属海报'
    return source
