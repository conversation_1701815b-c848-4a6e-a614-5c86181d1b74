from ast import literal_eval

from django.db.models import When, Case, Q

from wisdom_v2.models import InterviewRecordTemplate, InterviewRecordTemplateQuestion, InterviewRecordTemplateOption, \
    RelatedQuestion, TotalTemplateReport
from wisdom_v2.enum.service_content_enum import InterviewRecordTemplateQuestionTypeEnum as QuestionType<PERSON><PERSON>


def cp_interview_record_template(template):
    """
    复制辅导报告模板

    template：被复制的模板对象 InterviewRecordTemplate

    return: 新的模板对象 InterviewRecordTemplate

    1 - 按照指定顺序获取模板对应的所有问题
    2 - 按照指定问题顺序创建新的问题，并生成新有序的问题id列表
    3 - 如果类型是单选&多选，需要创建对应的选项。
    4 - 新的InterviewRecordTemplate更新有序的问题id列表
    """

    new_template = InterviewRecordTemplate.objects.create(
        name=template.name,
        role=template.role,
        type=template.type,
        preface=template.preface,
    )
    # 查询所有问题并按指定顺序排序
    order = Case(*[When(pk=order, then=pos) for pos, order in enumerate(eval(template.questions_order))])
    questions = InterviewRecordTemplateQuestion.objects.filter(
        template_id=template.pk, deleted=False).order_by(order).all()

    # 创建新的问题，保存排序好的问题列表
    questions_order = []

    for question in questions:

        # 新建一个问题
        new_question = InterviewRecordTemplateQuestion.objects.create(
            template_id=new_template.pk,
            answer_type=question.answer_type,
            rating_type=question.rating_type,
            type=question.type,
            title=question.title,
            required=question.required,
            answer_tips=question.answer_tips,
            sub_tips=question.sub_tips,
        )
        questions_order.append(new_question.pk)

        # 如果是选择题查询问题下所有选项
        if question.type in [QuestionTypeEnum.single, QuestionTypeEnum.multiple]:
            options = InterviewRecordTemplateOption.objects.filter(question_id=question.pk).all()
            # 批量添加新的选择题
            new_options = []
            for option in options:
                new_options.append(InterviewRecordTemplateOption(
                    question_id=new_question.pk,
                    title=option.title,
                    user_defined=option.user_defined,
                    deleted=option.deleted,
                    order=option.order
                ))
            InterviewRecordTemplateOption.objects.bulk_create(new_options)
    new_template.questions_order = str(questions_order)
    new_template.save()
    return new_template


def get_coach_and_coachee_template_ids(total_template):
    """
    根据提供的总模板，提取教练和被教练的问题模板ID列表。
    :param total_template: 总模板实例
    :return: 返回教练问题ID列表和被教练问题ID列表
    """
    # 检查并获取教练模板的问题ID列表
    if total_template.coach_template_id:
        coach_question_ids = total_template.coach_template.questions_order
        coach_question_ids = literal_eval(coach_question_ids) if coach_question_ids else []
    else:
        coach_question_ids = []

    # 检查并获取被教练模板的问题ID列表
    if total_template.coachee_template_id:
        coachee_question_ids = total_template.coachee_template.questions_order
        coachee_question_ids = literal_eval(coachee_question_ids) if coachee_question_ids else []
    else:
        coachee_question_ids = []
        
    return coach_question_ids, coachee_question_ids


def cp_template_related_question(total_template, new_total_template):
    """
    复制与总模板相关联的问题和报告，并创建新的关联模板实例。
    :param total_template: 原总模板实例
    :param new_total_template: 新总模板实例
    :return: 返回新的总模板实例


    1 - 根据旧模板的问题关联记录，创建新模板的问题关联记录related_question
    2 - 新旧版的问题关联记录做个映射，方便后续查询使用
    3 - 循环旧模板的报告记录，根据之前问题映射的数据，创建新模板的报告记录

    """
    # 查询相关的问题对象
    all_related_question = RelatedQuestion.objects.filter(
        Q(coach_question__isnull=True, coachee_question__template__total_template_coachee__id=total_template.id) |
        Q(coachee_question__isnull=True, coach_question__template__total_template_coach__id=total_template.id) |
        Q(coach_question__template__total_template_coach__id=total_template.id,
          coachee_question__template__total_template_coachee__id=total_template.id),
        deleted=False).all()

    # 获取原始和新模板的问题ID列表
    coach_question_ids, coachee_question_ids = get_coach_and_coachee_template_ids(total_template)
    new_coach_question_ids, new_coachee_question_ids = get_coach_and_coachee_template_ids(new_total_template)

    # 准备好需要添加辅导报告时需要的关系关联表的id
    # all_related_question_ids是旧模板关系关联表的id，new_related_question_ids是新模板关系关联表的id
    # all_related_question_ids和new_related_question_ids存在映射关系，每一题下标都一一对应
    # all_related_question_ids和new_related_question_ids的数量会保持一致。
    all_related_question_ids = []
    new_related_question_ids = []

    # 根据已有的问题之间的关联，创新新模板的创建
    for related_question in all_related_question:

        # 为每个关联问题创建新的对应项
        # 根据之前问题下标，取出对应新的教练问题id。
        if related_question.coach_question_id:
            new_coach_question_id = new_coach_question_ids[coach_question_ids.index(related_question.coach_question_id)]
        else:
            new_coach_question_id = None

        # 根据之前问题下标，取出对应新的被教练者问题id。
        if related_question.coachee_question_id:
            new_coachee_question_id = new_coachee_question_ids[coachee_question_ids.index(related_question.coachee_question_id)]
        else:
            new_coachee_question_id = None

        # 创建新的关联数据
        new_related_question = RelatedQuestion.objects.create(
            coach_question_id=new_coach_question_id,
            coachee_question_id=new_coachee_question_id,
            template_type=related_question.template_type,
            description=related_question.description
        )
        # 保存到新的id列表中
        all_related_question_ids.append(related_question.id)
        new_related_question_ids.append(new_related_question.id)

    # 查询所有报告并按指定顺序排序
    order = Case(*[When(pk=order, then=pos) for pos, order in enumerate(eval(total_template.questions_order))])
    all_total_template_report = TotalTemplateReport.objects.filter(total_template=total_template).order_by(order).all()

    # 用来存储新辅导模板的报告id列表
    new_total_template_report_ids = []
    for total_template_report in all_total_template_report:

        # 根据报告绑定的关联表id，同下标获取新的关联表id
        new_related_question_id = new_related_question_ids[all_related_question_ids.index(
            total_template_report.related_question_id)]

        # 获取到新的关联表id后，创建新的辅导模板报告数据。
        new_total_template_report = TotalTemplateReport.objects.create(
            title=total_template_report.title,
            title_type=total_template_report.title_type,
            description=total_template_report.description,
            related_question_id=new_related_question_id,
            total_template=new_total_template
        )
        # 存入新的id列表中
        new_total_template_report_ids.append(new_total_template_report.id)

    # 保存更新
    new_total_template.questions_order = str(new_total_template_report_ids)
    new_total_template.save()
    return new_total_template
