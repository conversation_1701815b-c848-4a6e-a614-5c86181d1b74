import datetime
import math
from collections import defaultdict

import pendulum
from django.conf import settings
from django.db import transaction
from decimal import Decimal
from django.db.models import Case, When, F, OuterRef, Subquery, CharField, Value, Prefetch
from django.db.models import Q, Sum

from utils import randomPassword, excel_pubilc, aliyun
from utils.message.lark_message import LarkMessageCenter
from wisdom_v2.common import coach_public, business_settlement_public
from wisdom_v2.enum.business_order_enum import WorkTypeEnum, BusinessOrderTypeEnum, BusinessOrderPayStatusEnum, \
    BusinessOrderSettlementStatusEnum, BusinessOrderWithdrawalStatusEnum, BusinessOrderDurationTypeEnum, \
    BusinessOrderDataTypeEnum
from wisdom_v2.enum.pay_enum import OrderStatusEnum
from wisdom_v2.enum.project_interview_enum import ProjectInterviewTypeEnum, ProjectInterviewPlaceCategoryEnum, \
    GroupCoachTypeEnum, InterviewSubjectEnum
from wisdom_v2.enum.service_content_enum import Coach<PERSON>ffer<PERSON>tatus<PERSON>num, ActivityTypeEnum, CoachType<PERSON>num
from wisdom_v2.enum.user_enum import CoachUser<PERSON>ype<PERSON>num
from wisdom_v2.models import ProjectInterview, Coach, ProjectOfferPrice, ProjectGroupCoach, User, Order
from wisdom_v2.models_file import BusinessOrder2Object, BusinessOrder, PublicCoursesCoach, ActivityInterview, \
    BusinessSettlement
from wisdom_v2.models_file.business_order import BusinessOrderExtraCost
from wisdom_v2.models_file.project import ProjectSettlement
from wisdom_v2.views.business_settlement_actions import BusinessSettlementOrdersSerializer
from wisdom_v2.views.constant import ATTR_STATUS_INTERVIEW_CANCEL, ORDER_TAX_POINT


def is_order_duplicate(objects_id, work_type):
    business_order = BusinessOrder2Object.objects.filter(
        object_id=str(objects_id), type=work_type, deleted=False)

    is_business_order = business_order.exists()
    if is_business_order:
        LarkMessageCenter().send_other_backend_message(
            '结算订单创建失败', 'error',
            {'error': f'对象id：{objects_id} 类型：{work_type}的结算订单已存在，重复生成预警'})
        return False
    return True


def backend_generate_business_order():

    # 2.26版本数据来源新增项目手动结算数据，都归属于项目id和uuid不能混合，排除处理。
    exists_order = BusinessOrder2Object.objects.filter(deleted=False).exclude(
        data_type=BusinessOrderDataTypeEnum.project_settlement.value).values_list('object_id', 'type', 'business_order__type')
    exists_data = defaultdict(list)
    for id, type, business_order__type in exists_order:
        exists_data[f"{business_order__type}_{type}"].append(id)
    interviews = ProjectInterview.objects.filter(
        public_attr__start_time__gt='2023-09-21 00:00:00',
        deleted=False, public_attr__end_time__lt=datetime.datetime.now()
    ).exclude(public_attr__status=ATTR_STATUS_INTERVIEW_CANCEL)

    # B端一对一辅导
    toB_one_to_one = interviews.filter(
        type=ProjectInterviewTypeEnum.formal_interview,
        coach_record_status=True,
        place_category=ProjectInterviewPlaceCategoryEnum.online_one_to_one,
        public_attr__project__isnull=False).exclude(id__in=exists_data.get(f"{BusinessOrderTypeEnum.enterprise.value}_"
                                                                           f"{WorkTypeEnum.one_to_one.value}", []))

    # C端一对一辅导
    toC_one_to_one = interviews.filter(
        coach_record_status=True,
        order__isnull=False,
        type=ProjectInterviewTypeEnum.formal_interview,
        place_category=ProjectInterviewPlaceCategoryEnum.online_one_to_one,
        public_attr__project__isnull=True).exclude(id__in=exists_data.get(f"{BusinessOrderTypeEnum.personal.value}_"
                                                                          f"{WorkTypeEnum.one_to_one.value}", []))

    # 利益相关者访谈
    stakeholder_interview = interviews.filter(
        coach_record_status=True,
        type=ProjectInterviewTypeEnum.stakeholder_interview).exclude(
        id__in=exists_data.get(f"{BusinessOrderTypeEnum.enterprise.value}_"
                               f"{WorkTypeEnum.stakeholder_interview.value}", []))

    # 集体辅导(工作坊)
    work_shop = interviews.filter(
        type=ProjectInterviewTypeEnum.formal_interview,
        coach_group_module__project_group_coach__type=GroupCoachTypeEnum.collective_tutoring,
        place_category=ProjectInterviewPlaceCategoryEnum.offline_group_coach).exclude(
        coach_group_module__project_group_coach_id__in=exists_data.get(
            f"{BusinessOrderTypeEnum.enterprise.value}_{WorkTypeEnum.group_coach.value}", [])).distinct()
    work_shop_project_group_coach_ids = work_shop.values_list('coach_group_module__project_group_coach_id',
                                                              flat=True)
    # 小组辅导
    group_counseling = interviews.filter(
        type=ProjectInterviewTypeEnum.formal_interview,
        coach_group_module__project_group_coach__type=GroupCoachTypeEnum.group_tutoring,
        place_category=ProjectInterviewPlaceCategoryEnum.offline_group_coach).exclude(
        coach_group_module__project_group_coach_id__in=exists_data.get(
            f"{BusinessOrderTypeEnum.enterprise.value}_{WorkTypeEnum.group_counseling.value}", [])).distinct()
    group_counseling_project_group_coach_ids = group_counseling.values_list(
        'coach_group_module__project_group_coach_id', flat=True)

    # 结束日期小于当前日期 或 (结束日期等于当前日期且结束时间的小时小于等于当前小时)
    public_courses_coach = PublicCoursesCoach.objects.filter(
        Q(end_time__date__lt=datetime.datetime.now().date()) | (
                Q(end_time__date=datetime.datetime.now().date()) & Q(end_time__hour__lte=datetime.datetime.now().hour)),
        deleted=False)

    # 公开课 授课
    public_courses_teaching = public_courses_coach.filter(
        type=WorkTypeEnum.teaching.value).exclude(
        id__in=exists_data.get(f"{BusinessOrderTypeEnum.public_course.value}_"
                                               f"{WorkTypeEnum.teaching.value}", []))

    # 公开课 口试
    public_courses_evaluation = public_courses_coach.filter(
        type=WorkTypeEnum.evaluation.value).exclude(
        id__in=exists_data.get(f"{BusinessOrderTypeEnum.public_course.value}_"
                                               f"{WorkTypeEnum.evaluation.value}", []))

    # 公开课 一对一辅导
    public_courses_one_to_one = public_courses_coach.filter(
        type=WorkTypeEnum.one_to_one.value).exclude(
        id__in=exists_data.get(f"{BusinessOrderTypeEnum.public_course.value}_"
                                               f"{WorkTypeEnum.one_to_one.value}", []))

    # 公开课 小组辅导
    public_courses_group_counseling = public_courses_coach.filter(
        type=WorkTypeEnum.group_counseling.value).exclude(
        id__in=exists_data.get(f"{BusinessOrderTypeEnum.public_course.value}_"
                                               f"{WorkTypeEnum.group_counseling.value}", []))

    # 查询到所有的已存在的来源于项目结算的订单
    all_project_settlement_object_id = list(BusinessOrder2Object.objects.filter(
        deleted=False, data_type=BusinessOrderDataTypeEnum.project_settlement.value).values_list('object_id', flat=True))

    # 结束日期小于当前日期 或 (结束日期等于当前日期且结束时间的小时小于等于当前小时)
    project_settlement = ProjectSettlement.objects.filter(
        Q(work_end_time__date__lt=datetime.datetime.now().date()) | (
                Q(work_end_time__date=datetime.datetime.now().date()) & Q(work_end_time__hour__lte=datetime.datetime.now().hour)),
        deleted=False).exclude(id__in=all_project_settlement_object_id)  # 排除掉已存在的订单

    # 创建订单数据
    with transaction.atomic():
        if toB_one_to_one.exists():
            generate_toB_interview_order(toB_one_to_one)
        if toC_one_to_one.exists():
            generate_toC_interview_order(toC_one_to_one)
        if stakeholder_interview.exists():
            generate_stakeholder_interview_order(stakeholder_interview)
        if public_courses_teaching.exists():
            generate_public_courses_order(public_courses_teaching)
        if public_courses_evaluation.exists():
            generate_public_courses_order(public_courses_evaluation)
        if public_courses_one_to_one.exists():
            generate_public_courses_order(public_courses_one_to_one)
        if public_courses_group_counseling.exists():
            generate_public_courses_order(public_courses_group_counseling)
        if work_shop_project_group_coach_ids:
            generate_work_shop_order(work_shop_project_group_coach_ids, WorkTypeEnum.group_coach)
        if group_counseling_project_group_coach_ids:
            generate_work_shop_order(group_counseling_project_group_coach_ids, WorkTypeEnum.group_counseling)
        if project_settlement:
            generate_project_settlement_order(project_settlement)


def generate_project_settlement_order(project_settlement):
    for item in project_settlement:
        status = is_order_duplicate(item.pk, item.work_type)
        if not status:
            continue
        data = get_project_settlement_info(item)
        tax_amount, coach_actual_income = calculation_tax_amount(data['price'], data['time'], ORDER_TAX_POINT)
        order_data = {
            "type": BusinessOrderTypeEnum.enterprise.value, "work_type": item.work_type,
            "coach": item.coach, "project": item.project,
            "coach_price": data['price'], "duration": data['time'], "duration_type": data['time_type'],
            "tax_point": ORDER_TAX_POINT, "tax_amount": tax_amount, "coach_actual_income": coach_actual_income,
            "pay_status": BusinessOrderPayStatusEnum.non,
            "settlement_status": BusinessOrderSettlementStatusEnum.unsettled,
            "withdrawal_status": BusinessOrderWithdrawalStatusEnum.non_withdrawable,
            "work_start_time": item.work_start_time,
            "work_end_time": item.work_end_time
        }
        business_order = BusinessOrder.objects.create(**order_data)
        BusinessOrder2Object.objects.create(
            business_order=business_order, object_id=str(item.pk), type=item.work_type,
            data_type=BusinessOrderDataTypeEnum.project_settlement.value)


def generate_work_shop_order(ids, work_type):
    for project_group_coach in ProjectGroupCoach.objects.filter(id__in=ids):
        status = is_order_duplicate(project_group_coach.id, work_type)
        if not status:
            continue
        group_coach = project_group_coach.coach_group_module.filter(deleted=False).first()
        if group_coach:
            interview = group_coach.interview
            data = get_interview_settlement_info(interview, work_type)
            coach = Coach.objects.filter(user=interview.public_attr.user, deleted=False).first()
            if coach:
                tax_amount, coach_actual_income = calculation_tax_amount(data['price'], data['time'], ORDER_TAX_POINT)
                order_data = {
                    "type": BusinessOrderTypeEnum.enterprise.value, "work_type": work_type,
                    "project": interview.public_attr.project,
                    "coach": coach, "coach_price": data['price'], "duration": data['time'],
                    "duration_type": data['time_type'],
                    "tax_point": ORDER_TAX_POINT, "tax_amount": tax_amount, "coach_actual_income": coach_actual_income,
                    "pay_status": BusinessOrderPayStatusEnum.non,
                    "settlement_status": BusinessOrderSettlementStatusEnum.unsettled,
                    "withdrawal_status": BusinessOrderWithdrawalStatusEnum.non_withdrawable,
                    "work_start_time": interview.public_attr.start_time,
                    "work_end_time": interview.public_attr.end_time
                }
                business_order = BusinessOrder.objects.create(**order_data)
                BusinessOrder2Object.objects.create(
                    business_order=business_order, object_id=project_group_coach.id, type=work_type,
                    data_type=BusinessOrderDataTypeEnum.group_coach.value)


def generate_public_courses_order(public_courses_coach):
    for item in public_courses_coach:
        status = is_order_duplicate(item.pk, item.type)
        if not status:
            continue
        data = get_public_course_settlement_info(item)
        tax_amount, coach_actual_income = calculation_tax_amount(data['price'], data['time'], ORDER_TAX_POINT)
        order_data = {
            "type": BusinessOrderTypeEnum.public_course.value, "work_type": item.type,
            "coach": item.coach,
            "coach_price": data['price'], "duration": data['time'], "duration_type": data['time_type'],
            "tax_point": ORDER_TAX_POINT, "tax_amount": tax_amount, "coach_actual_income": coach_actual_income,
            "pay_status": BusinessOrderPayStatusEnum.non,
            "settlement_status": BusinessOrderSettlementStatusEnum.unsettled,
            "withdrawal_status": BusinessOrderWithdrawalStatusEnum.non_withdrawable,
            "work_start_time": item.start_time,
            "work_end_time": item.end_time
        }
        business_order = BusinessOrder.objects.create(**order_data)
        BusinessOrder2Object.objects.create(
            business_order=business_order, object_id=item.pk, type=item.type,
            data_type=BusinessOrderDataTypeEnum.public_course.value)


def generate_toB_interview_order(interviews):
    for interview in interviews:
        status = is_order_duplicate(interview.pk, WorkTypeEnum.one_to_one)
        if not status:
            continue
        data = get_interview_settlement_info(interview, WorkTypeEnum.one_to_one.value)
        coach = Coach.objects.get(user=interview.public_attr.user, deleted=False)
        tax_amount, coach_actual_income = calculation_tax_amount(data['price'], data['time'], ORDER_TAX_POINT)

        # 一对一辅导分为正式约谈，多方会谈，影子观察三种工作形式
        # 根据interview_subject在创建的时候区分形式
        if interview.interview_subject == InterviewSubjectEnum.regular.value:  # 正式约谈对应一对一辅导
            work_type = WorkTypeEnum.one_to_one.value
        elif interview.interview_subject == InterviewSubjectEnum.multi_party.value:  # 多方会谈
            work_type = WorkTypeEnum.multi_party.value
        elif interview.interview_subject == InterviewSubjectEnum.shadow_observation.value:  # 影子观察
            work_type = WorkTypeEnum.shadow_observation.value
        else:
            work_type = WorkTypeEnum.one_to_one.value  # 默认一对一辅导

        order_data = {
            "type": BusinessOrderTypeEnum.enterprise.value, "work_type": work_type,
            "project": interview.public_attr.project, "user": interview.public_attr.target_user,
            "coach": coach, "coach_price": data['price'], "duration": data['time'], "duration_type": data['time_type'],
            "tax_point": ORDER_TAX_POINT, "tax_amount": tax_amount, "coach_actual_income": coach_actual_income,
            "pay_status": BusinessOrderPayStatusEnum.non,
            "settlement_status": BusinessOrderSettlementStatusEnum.unsettled,
            "withdrawal_status": BusinessOrderWithdrawalStatusEnum.non_withdrawable,
            "work_start_time": interview.public_attr.start_time,
            "work_end_time": interview.public_attr.end_time
            }
        business_order = BusinessOrder.objects.create(**order_data)
        BusinessOrder2Object.objects.create(
            business_order=business_order, object_id=interview.pk, type=WorkTypeEnum.one_to_one,
            data_type=BusinessOrderDataTypeEnum.interview.value)


def generate_toC_interview_order(interviews):
    for interview in interviews:
        if not interview.order:
            continue
        status = is_order_duplicate(interview.pk, WorkTypeEnum.one_to_one.value)
        if not status:
            continue
        data = get_interview_settlement_info(interview, WorkTypeEnum.one_to_one.value)
        coach = Coach.objects.get(user=interview.public_attr.user, deleted=False)
        platform_scale,  coach_actual_income, tax_amount, platform_service_amount, tax_point = coach_public.calculate_coach_income_details(interview)

        # C端辅导一个订单多次辅导的情况下，平均计算每单的客户支付和平台服务费。
        member_paid = Decimal(str(interview.order.payer_amount)) / Decimal(str(interview.order.count))
        order_data = {
            "type": BusinessOrderTypeEnum.personal.value, "work_type": WorkTypeEnum.one_to_one.value,
            "user": interview.public_attr.target_user,
            "coach": coach, "coach_price": data['price'], "duration": data['time'], "duration_type": data['time_type'],
            "member_paid": member_paid, "tax_amount": tax_amount, 'tax_point': tax_point,
            "platform_scale": platform_scale, "platform_service_amount": platform_service_amount,
            "coach_actual_income": coach_actual_income,
            "pay_status": BusinessOrderPayStatusEnum.paid,
            "settlement_status": BusinessOrderSettlementStatusEnum.unsettled,
            "withdrawal_status": BusinessOrderWithdrawalStatusEnum.can_withdraw,
            "pay_time": interview.order.success_time,
            "work_start_time": interview.public_attr.start_time,
            "work_end_time": interview.public_attr.end_time
        }
        business_order = BusinessOrder.objects.create(**order_data)
        BusinessOrder2Object.objects.create(
            business_order=business_order, object_id=interview.pk, type=WorkTypeEnum.one_to_one,
            data_type=BusinessOrderDataTypeEnum.interview.value)
        admin_user = User.objects.filter(pk=settings.ADMIN_USER_ID, deleted=False).first()
        # 如果订单金额是0，改成已结算状态
        if member_paid == Decimal('0'):
            # 标记允许提现时间和允许提现处理人
            business_order.apply_withdrawal_time = datetime.datetime.now()
            business_order.apply_withdrawal_processor = admin_user
            business_order.save()

            # 改为已提现
            business_order = BusinessOrder.objects.filter(pk=str(business_order.id))
            business_settlement_public.generate_business_settlements(coach, business_order, admin_user)
            # 标记结算状态
            business_settlements = BusinessSettlement.objects.filter(business_order=business_order.first(), deleted=False).all()
            business_settlement_public.mark_settlement_status(business_settlements, admin_user)

        # 公益活动订单，需要默认是已提现状态，进行提现操作
        # 新的公益活动订单，教练自己提现
        # elif ActivityInterview.objects.filter(
        #         interview_id=interview.id, deleted=False,
        #         activity__type=ActivityTypeEnum.public_welfare_coach.value).exists():
        #     # 标记允许提现时间和允许提现处理人
        #     business_order.apply_withdrawal_time = datetime.datetime.now()
        #     business_order.apply_withdrawal_processor = admin_user
        #     business_order.save()

        #     settlement_business_order = BusinessOrder.objects.filter(pk=str(business_order.id))
        #     business_settlement_public.generate_business_settlements(coach, settlement_business_order, admin_user)


def generate_stakeholder_interview_order(interviews):
    for interview in interviews:
        status = is_order_duplicate(interview.pk, WorkTypeEnum.stakeholder_interview.value)
        if not status:
            continue
        data = get_interview_settlement_info(interview, WorkTypeEnum.stakeholder_interview.value)
        coach = Coach.objects.get(user=interview.public_attr.user, deleted=False)
        tax_amount, coach_actual_income = calculation_tax_amount(data['price'], data['time'], ORDER_TAX_POINT)
        order_data = {
            "type": BusinessOrderTypeEnum.enterprise.value, "work_type": WorkTypeEnum.stakeholder_interview.value,
            "project": interview.public_attr.project, "user": interview.public_attr.target_user,
            "coach": coach, "coach_price": data['price'], "duration": data['time'], "duration_type": data['time_type'],
            "tax_point": ORDER_TAX_POINT, "tax_amount": tax_amount, "coach_actual_income": coach_actual_income,
            "pay_status": BusinessOrderPayStatusEnum.non,
            "settlement_status": BusinessOrderSettlementStatusEnum.unsettled,
            "withdrawal_status": BusinessOrderWithdrawalStatusEnum.non_withdrawable,
            "work_start_time": interview.public_attr.start_time,
            "work_end_time": interview.public_attr.end_time
        }
        business_order = BusinessOrder.objects.create(**order_data)
        BusinessOrder2Object.objects.create(
            business_order=business_order, object_id=interview.pk, type=WorkTypeEnum.stakeholder_interview,
            data_type=BusinessOrderDataTypeEnum.interview.value)


def calculation_tax_amount(coach_price, duration, tax_point, business_order=None):
    """
    计算税金

    :param coach_price: 教练的价格
    :param duration: 服务时长
    :param tax_point: 税率
    :param business_order: 结算订单，可选参数

    :return: 返回税金金额和教练的实际收入
    """
    # 计算总金额（教练价格乘以服务时长）
    total_amount = Decimal(str(coach_price)) * Decimal(str(duration))

    # 根据税率计算税金
    tax_amount = (total_amount * Decimal(str(tax_point))) / Decimal('100')

    # 如果有额外花费，则增加额外成本
    if business_order:
        extra_cost = get_all_business_order_extra_cost(business_order)
        total_amount += extra_cost * Decimal('100')

    # 向上取整计算税金
    tax_amount = math.ceil(tax_amount)
    # 计算教练的实际收入（总金额减去税金）
    coach_actual_income = total_amount - tax_amount
    return tax_amount, coach_actual_income


def get_object_settlement_status(object_ids, order_type, work_type):
    """
    获取某个对象的结算状态。

    :param object_ids: 对象的ID 列表
    :param order_type: 业务订单类型
    :param work_type: 工作类型
    :return: 如果该对象已经结算返回True，否则返回False
    """
    return BusinessOrder2Object.objects.filter(
        business_order__type=order_type,
        business_order__business_settlement__isnull=False,
        business_order__deleted=False, object_id__in=object_ids, type=work_type, deleted=False
    ).exists()


def update_business_order(object_ids, order_type, work_type, data):
    """
    修改与指定对象关联的业务订单。

    :param object_ids: 对象的ID列表
    :param order_type: 业务订单类型
    :param work_type: 工作类型
    :param data: 修改数据
    """
    if BusinessOrder.objects.filter(
        type=order_type, deleted=False, work_type=work_type, business_order2object__object_id__in=object_ids
    ).exclude(business_settlement__isnull=False).exists():
        # 更新BusinessOrder中与指定对象ID关联的订单的时间
        orders = BusinessOrder.objects.filter(
            type=order_type, deleted=False, work_type=work_type, business_order2object__object_id__in=object_ids
        ).exclude(business_settlement__isnull=False).all()

        # 修改时间的时候需要修改结算时长
        for business_order in orders:
            if data.get('start_time') and data.get('end_time'):
                business_order.work_start_time = data.get('start_time')
                business_order.work_end_time = data.get('end_time')
                if data.get('time_type'):
                    business_order.duration_type = data.get('time_type')
                duration = get_business_order_time(
                    business_order.work_start_time, business_order.work_end_time, business_order.duration_type)
                business_order.duration = duration
                # 时间变动需要修改教练收益和税费
                tax_amount, coach_actual_income = calculation_tax_amount(
                    business_order.coach_price, duration, business_order.tax_point, business_order)
                business_order.coach_actual_income = coach_actual_income
                business_order.tax_amount = tax_amount

            if data.get('type'):
                business_order.work_type = data.get('type')
                BusinessOrder2Object.objects.filter(
                    business_order=business_order, deleted=False).update(type=data.get('type'))
            business_order.save()
    return


def del_business_order(object_ids, order_type, work_type):
    """
    删除与指定对象关联的业务订单。

    :param object_ids: 对象的ID列表
    :param order_type: 业务订单类型
    :param work_type: 工作类型
    """
    if BusinessOrder.objects.filter(
        type=order_type, deleted=False, work_type=work_type, business_order2object__object_id__in=object_ids
    ).exclude(business_settlement__isnull=False).exists():

        # 更新BusinessOrder中与指定对象ID关联的订单的删除状态
        business_order = BusinessOrder.objects.filter(
            type=order_type, deleted=False, work_type=work_type, business_order2object__object_id__in=object_ids
        ).exclude(business_settlement__isnull=False)
        business_order.update(deleted=True)
        
        # 更新BusinessOrder2Object中与指定对象ID关联的订单的删除状态
        BusinessOrder2Object.objects.filter(
            business_order__type=order_type, deleted=False, business_order__work_type=work_type, object_id__in=object_ids
        ).exclude(business_order__business_settlement__isnull=False).update(deleted=True)

        # 获取被删除的订单id列表
        business_order_id = list(business_order.values_list('id', flat=True))
        # 删除额外花费
        BusinessOrderExtraCost.objects.filter(deleted=False, business_order_id__in=business_order_id).update(deleted=True)

    return


def get_interview_settlement_info(interview, work_type):
    """
    根据辅导信息和工作类型，获取结算信息。
    :param interview: 辅导对象
    :param work_type: 工作类型
    :return: 返回字典，包含结算时长，时长类型和价格
    """
    interval_time, time_type, price = None, BusinessOrderDurationTypeEnum.hour.value, 0
    # 获取辅导关联的用户信息
    coach_user = interview.public_attr.user
    # 如果没有关联的用户，直接返回
    if not coach_user:
        return
    if interview.public_attr.project_id:
        # 根据辅导用户和项目ID等条件，查询项目的报价信息
        project_offer_price = ProjectOfferPrice.objects.filter(
            project_offer__coach_offers__coach__user=coach_user,
            project_offer__project_id=interview.public_attr.project_id,
            project_offer__coach_offers__status=CoachOfferStatusEnum.joined.value,
            work_type=work_type,
            place_type=interview.place_type,
            project_offer__deleted=False, project_offer__coach_offers__deleted=False, deleted=False,
        ).first()
        # 如果找到项目的报价信息
        if project_offer_price:
            price = project_offer_price.price
            # 获取报价的时长类型（小时或天）
            time_type = project_offer_price.time_type

    interval_time = get_business_order_time(
        interview.public_attr.start_time, interview.public_attr.end_time, time_type)
    # 返回结算信息
    return {
        'time': interval_time,
        'time_type': time_type,
        'price': price
    }


def get_business_order_time(start_time, end_time, duration_type):
    """
    获取业务订单的时间范围。

    :param start_time: 开始时间
    :param end_time: 结束时间
    :param duration_type: 时间范围类型
    :return: 业务订单时间范围

    """
    delta = end_time - start_time
    raw_hours = delta.total_seconds() / 3600
    int_hours = int(raw_hours)

    interval_time = 0

    if duration_type == BusinessOrderDurationTypeEnum.hour.value:
        remainder = raw_hours % 1
        # 根据余数调整计费时长
        # 0-15分钟不收费，15-45分钟收费0.5小时，45分钟以上收费1小时
        if 0.25 < remainder <= 0.75:
            interval_time = int_hours + 0.5
        elif remainder > 0.75:
            interval_time = int_hours + 1
        else:
            interval_time = int_hours

    elif duration_type == BusinessOrderDurationTypeEnum.day.value:
        """
        注意：
        - 如果开始和结束时间是同一天，并且时长小于等于4小时，那么计为0.5天
        - 如果开始和结束时间是同一天，并且时长大于4小时，那么计为1天
        - 对于第一天，如果小时数小于等于4小时，则计为0.5天，否则计为1天
        - 对于中间的完整天数，每一天都计为1天
        - 对于最后一天，如果小时数小于等于4小时，则加上0.5天；如果小时数大于4小时，则加上1天
        - 排除0点到9点，13点以后算一天，13点以前算半天
        """
        # 如果开始和结束时间是同一天 or 结束时间是第二天的0点0分
        if (start_time.date() == end_time.date() or
                ((end_time.date() - start_time.date()).days == 1 and end_time.hour == 0 and end_time.minute == 0)):
            # 根据时长计费
            interval_time = 0.5 if raw_hours <= 4 else 1
        else:
            # 处理第一天
            # 根据第一天的时长计费
            interval_time += 0.5 if start_time.hour > 13 else 1

            # 加上中间的完整天数
            interval_time += (delta.days - 1)

            # 处理最后一天
            # 根据最后一天的时长计费
            interval_time += 0.5 if end_time.hour <= 13 else 1
    return interval_time


def get_public_course_settlement_info(public_course_coach):
    """
    根据教练公开课的开始和结束时间，计算结算的天数。

    :param public_course: 公开课对象，包含start_time和end_time属性
    :return: 返回字典，包含结算时长、时长类型和价格
    """
    order_duration_type = (
        BusinessOrderDurationTypeEnum.hour.value
        if public_course_coach.type not in [WorkTypeEnum.teaching.value, WorkTypeEnum.evaluation.value]
        else BusinessOrderDurationTypeEnum.day.value
    )

    total_time = get_business_order_time(
        public_course_coach.start_time, public_course_coach.end_time, order_duration_type)

    # 教练价格表
    # 教练姓名和ID对应关系：
    # 吴彦群: 225, 周巍嬿: 222, 陈莉: 219, 高文慧: 6648, 田岚: 245,
    # 薛松: 241, 赵磊: 262, 安洋: 521, 王冬梅: 504
    # 设置价格表
    price_table = {
        225: {WorkTypeEnum.teaching.value: 1500000},
        222: {WorkTypeEnum.teaching.value: 1000000, WorkTypeEnum.group_counseling.value: 150000, WorkTypeEnum.one_to_one.value: 150000, WorkTypeEnum.evaluation.value: 1000000},
        219: {WorkTypeEnum.teaching.value: 1000000, WorkTypeEnum.group_counseling.value: 150000, WorkTypeEnum.one_to_one.value: 150000, WorkTypeEnum.evaluation.value: 1000000},
        6648: {WorkTypeEnum.teaching.value: 1000000, WorkTypeEnum.group_counseling.value: 100000, WorkTypeEnum.one_to_one.value: 100000},
        245: {WorkTypeEnum.teaching.value: 1000000, WorkTypeEnum.group_counseling.value: 100000, WorkTypeEnum.one_to_one.value: 100000},
        241: {WorkTypeEnum.teaching.value: 750000, WorkTypeEnum.group_counseling.value: 80000, WorkTypeEnum.one_to_one.value: 100000},
        262: {WorkTypeEnum.teaching.value: 750000, WorkTypeEnum.group_counseling.value: 50000, WorkTypeEnum.one_to_one.value: 80000},
        521: {WorkTypeEnum.teaching.value: 750000, WorkTypeEnum.group_counseling.value: 50000, WorkTypeEnum.one_to_one.value: 80000},
        504: {WorkTypeEnum.teaching.value: 750000, WorkTypeEnum.group_counseling.value: 50000, WorkTypeEnum.one_to_one.value: 80000},
    }

    # 获取教练ID和工作类型
    coach_id = public_course_coach.coach_id
    work_type = public_course_coach.type

    # 根据教练ID和工作类型获取价格，如果没有对应的价格则默认为0
    price = price_table.get(coach_id, {}).get(work_type, 0)
    
    # 返回计费信息，包括结算时长、时长类型和价格
    return {
        'time': total_time,
        'time_type': order_duration_type,
        'price': price,
    }


def get_project_settlement_info(project_settlement):
    """
    根据项目结算信息的开始和结束时间，计算结算的天数。

    :param project_settlement: 项目结算信息对象，包含work_start_time和work_end_time属性
    :return: 返回字典，包含结算时长、时长类型和价格
    """
    price = 0
    project_offer_price = ProjectOfferPrice.objects.filter(
        project_offer__coach_offers__coach_id=project_settlement.coach_id,
        project_offer__project_id=project_settlement.project_id,
        project_offer__coach_offers__status=CoachOfferStatusEnum.joined.value,
        work_type=project_settlement.work_type,
        project_offer__deleted=False, project_offer__coach_offers__deleted=False, deleted=False,
    )
    if project_settlement.time_type:
        project_offer_price = project_offer_price.filter(time_type=project_settlement.time_type)
    if project_settlement.place_type:
        project_offer_price = project_offer_price.filter(place_type=project_settlement.place_type)
    # 如果找到项目的报价信息
    if project_offer_price.exists():
        price = project_offer_price.first().price

    total_time = get_business_order_time(
        project_settlement.work_start_time, project_settlement.work_end_time, project_settlement.time_type)

    # 返回计费信息，包括结算时长、时长类型和价格
    return {
        'time': total_time,
        'time_type': project_settlement.time_type if project_settlement.time_type else BusinessOrderDurationTypeEnum.hour.value,
        'price': price,
    }


def get_coach_remain_withdrawable_orders(coach):
    """
    获取教练待提现的订单数量
    :param coach: 教练对象
    :return: 教练待提现的订单数量
    """
    not_withdrawn_orders = BusinessOrder.objects.filter(
        business_settlement__isnull=True,  # 结算记录为空，表示尚未进行结算
        deleted=False,  # 订单未被删除
        coach=coach,  # 指定教练的订单
        settlement_status=BusinessOrderSettlementStatusEnum.unsettled,  # 订单结算状态为“未结算”
        pay_status__in=[BusinessOrderPayStatusEnum.paid, BusinessOrderPayStatusEnum.non_payment]  # 订单支付状态为“已支付”或“未支付”
    )
    return not_withdrawn_orders.count()  # 返回待提现订单的数量


def get_coach_remain_withdrawable_amount(coach):
    """
    获取教练待提现的总金额
    :param coach: 教练对象
    :return: 教练待提现的总金额（单位：元）
    """
    not_withdrawn_income = BusinessOrder.objects.filter(
        business_settlement__isnull=True,  # 结算记录为空，表示尚未进行结算
        deleted=False,  # 订单未被删除
        coach=coach,  # 指定教练的订单
        settlement_status=BusinessOrderSettlementStatusEnum.unsettled,  # 订单结算状态为“未结算”
        pay_status__in=[BusinessOrderPayStatusEnum.paid, BusinessOrderPayStatusEnum.non_payment]  # 订单支付状态为“已支付”或“未支付”
    ).aggregate(not_withdrawn_income=Sum('coach_actual_income'))  # 聚合计算教练的实际收入总和

    not_withdrawn_income = not_withdrawn_income.get('not_withdrawn_income', 0) or 0  # 获取未提现收入总额，默认为0
    not_withdrawn_income = Decimal(str(not_withdrawn_income)) / Decimal('100')  # 将金额单位从分转换为元
    return not_withdrawn_income  # 返回待提现总金额


def get_not_selected_business_order(business_order_ids, business_settlement_ids):
    """
    使用部分订单id，查询对应的所有未选中的结算订单
    :param business_order_ids: 部分订单id
    :param business_settlement_ids: 涉及的结算单id
    ：:return: 结算单对应的订单列表, 未选中的订单数量

    """
    all_business_order = []
    not_selected_business_order_count = 0
    for item in business_settlement_ids:
        not_selected = BusinessOrder.objects.filter(
            business_settlement_id=item, deleted=False)
        if not_selected.exclude(id__in=business_order_ids).exists():
            not_selected_business_order_count += not_selected.exclude(id__in=business_order_ids).count()
            all_business_order.append('、 '.join([str(item) for item in list(not_selected.values_list('id', flat=True))]))

    return all_business_order, not_selected_business_order_count


def get_business_order_to_public_course(business_order):
    """
    获取结算订单对应的公开课对象课程
    :param business_order: 订单对象
    :return: 课程对象
    """
    business_order2object = business_order.business_order2object.filter(deleted=False).first()
    if business_order2object:
        public_course_coach = PublicCoursesCoach.objects.filter(
            id=business_order2object.object_id, deleted=False).first()
        return public_course_coach
    return


def get_business_order_member_name(business_order):
    """
    获取结算订单对应的用户名称
    """
    if business_order.user_id:
        if business_order.type != BusinessOrderTypeEnum.public_course:
            return business_order.user.cover_name if business_order.user.cover_name else business_order.user.name
    elif business_order.extra_info and 'member_name' in business_order.extra_info:
        return business_order.extra_info['member_name']
    # 如果是公开课订单
    elif business_order.type == BusinessOrderTypeEnum.public_course.value:
        public_course_coach = get_business_order_to_public_course(business_order)
        if public_course_coach:
            return public_course_coach.student_name
    # 如果是项目结算单
    elif business_order.business_order2object.filter(
            deleted=False, data_type=BusinessOrderDataTypeEnum.project_settlement.value).exists():
        # 找到关联的结算单
        business_order2object = business_order.business_order2object.filter(
            deleted=False, data_type=BusinessOrderDataTypeEnum.project_settlement.value).first()
        # 找到对应的项目结算单
        project_settlement = ProjectSettlement.objects.filter(pk=business_order2object.object_id, deleted=False).first()
        if project_settlement:
            return  project_settlement.coachee_name
    return


def get_all_business_order_extra_cost(business_order):
    """
    获取订单所有额外费用
    """
    all_extra_costs = BusinessOrderExtraCost.objects.filter(
        business_order=business_order, deleted=False
    ).aggregate(times_minute=Sum('cost'))

    # 检查是否有匹配的记录
    if all_extra_costs['times_minute'] is not None:
        return Decimal(str(all_extra_costs['times_minute'])) / Decimal('100')
    else:
        return Decimal('0')


def prepare_business_orders_for_sorting(business_orders):
    """
    准备业务订单以供后续排序。
    这个函数根据订单的不同类型（普通项目或公开课）添加一个排序关键字字段 `sorting_key`，
    用于后续的排序逻辑。

    注意：此函数只添加排序所需的排序关键字字段，但不执行实际的排序操作。
    实际的排序操作需要在此函数调用后，对返回的查询集进行。

    :param business_orders: BusinessOrder 的查询集。
    :return: 为排序操作准备好的订单列表。
    """

    # 为每个订单添加排序关键字
    for order in business_orders:
        if order.type == BusinessOrderTypeEnum.public_course:
            # 如果订单类型为公开课，获取相关的公开课名称作为排序关键字
            bo2o = BusinessOrder2Object.objects.filter(
                business_order=order,
                deleted=False
            ).first()
            if bo2o:
                public_course_coach = PublicCoursesCoach.objects.filter(
                    id=bo2o.object_id,
                    deleted=False
                ).first()
                if public_course_coach and public_course_coach.class_name:
                    order.sort_name = public_course_coach.class_name
                else:
                    order.sort_name = ''  # 如果没有找到公开课，则设置为''
            else:
                order.sort_name = ''
        else:
            # 对于非公开课订单，使用关联的项目名称作为排序关键字
            order.sort_name = order.project.name if order.project else ''

    return business_orders


def get_business_order_activity(business_order):
    """
    获取与业务订单相关联的活动。

    :param business_order: 业务订单对象
    :return: 活动对象或者在找不到相应活动时返回 None
    """

    # 检查业务订单类型是否为个人类型
    if business_order.type == BusinessOrderTypeEnum.personal.value:
        # 查找与业务订单相关联且未被删除的 BusinessOrder2Object 实例
        bo2o = BusinessOrder2Object.objects.filter(
            business_order=business_order, deleted=False, data_type=BusinessOrderDataTypeEnum.interview.value).first()
        if bo2o:
            # 根据 BusinessOrder2Object 实例中的 object_id 查找对应的未被删除的 ActivityInterview 实例
            activity_interview = ActivityInterview.objects.filter(interview_id=bo2o.object_id, deleted=False).first()
            if activity_interview:
                # 如果找到了对应的 ActivityInterview 实例，则返回其活动
                return activity_interview.activity
    # 如果没有找到符合条件的活动或业务订单类型不是个人类型，则返回 None
    return None


def get_business_settlement_to_order(data):
    """
    根据提供的筛选条件获取业务结算订单列表，并进行排序。

    参数:
    - data: 包含各种筛选条件的字典，用于过滤业务订单。可包含的键有：
            'id', 'type', 'work_type', 'pay_status', 'settlement_status', 'withdrawal_status',
            'pay_start_date', 'pay_end_date', 'settlement_start_date', 'settlement_end_date',
            'coachee_user_id', 'coach_user_id', 'company_id', 'project_id', 'apply_start_date',
            'apply_end_date', 'work_start_date', 'work_end_date', 'coach_type', 'class_name'

    返回值:
    - 排序后的业务结算订单列表。
    """

    business_order = BusinessOrder.objects.filter(
        pay_status__in=[BusinessOrderPayStatusEnum.paid.value, BusinessOrderPayStatusEnum.non_payment.value],
        deleted=False).order_by('-work_end_time')

    if data.get('id'):
        business_order = business_order.filter(id__icontains=data.get('id'))
    if data.get('type'):
        business_order = business_order.filter(type__in=data.get('type').split(','))
    if data.get('work_type'):
        business_order = business_order.filter(work_type__in=data.get('work_type').split(','))
    if data.get('pay_status'):
        business_order = business_order.filter(pay_status__in=data.get('pay_status').split(','))
    if data.get('settlement_status'):
        business_order = business_order.filter(
            settlement_status__in=data.get('settlement_status').split(','))
    if data.get('withdrawal_status'):
        business_order = business_order.filter(withdrawal_status__in=data.get('withdrawal_status').split(','))
    if data.get('pay_start_date') and data.get('pay_end_date'):
        pay_start_date = datetime.datetime.strptime(data.get('pay_start_date'), '%Y-%m-%d').date()
        pay_end_date = datetime.datetime.strptime(data.get('pay_end_date'), '%Y-%m-%d').date()
        business_order = business_order.filter(pay_time__date__range=[pay_start_date, pay_end_date])
    if data.get('settlement_start_date') and data.get('settlement_end_date'):
        settlement_start_date = datetime.datetime.strptime(data.get('settlement_start_date'), '%Y-%m-%d').date()
        settlement_end_date = datetime.datetime.strptime(data.get('settlement_end_date'), '%Y-%m-%d').date()
        business_order = business_order.filter(
            business_settlement__settlement_time__date__range=[settlement_start_date, settlement_end_date])
    if data.get('coachee_user_id'):
        business_order = business_order.filter(user_id=data.get('coachee_user_id'))
    if data.get('coach_user_id'):
        business_order = business_order.filter(coach__user_id=data.get('coach_user_id'))
    if data.get('company_id'):
        business_order = business_order.filter(project__company_id=data.get('company_id'))
    if data.get('project_id'):
        business_order = business_order.filter(project_id=data.get('project_id'))
    if data.get('apply_start_date') and data.get('apply_end_date'):
        apply_start_date = datetime.datetime.strptime(data.get('apply_start_date'), '%Y-%m-%d').date()
        apply_end_date = datetime.datetime.strptime(data.get('apply_end_date'), '%Y-%m-%d').date()
        business_order = business_order.filter(
            business_settlement__apply_time__date__range=[apply_start_date, apply_end_date])
    if data.get('work_start_date') and data.get('work_end_date'):
        work_start_date = datetime.datetime.strptime(data.get('work_start_date'), '%Y-%m-%d').date()
        work_end_date = datetime.datetime.strptime(data.get('work_end_date'), '%Y-%m-%d').date()
        business_order = business_order.filter(work_start_time__date__range=[work_start_date, work_end_date])
    if data.get('coach_type'):
        business_order = business_order.filter(coach__coach_type__in=data.get('coach_type').split(','))
    if data.get('class_name'):
        current_date = datetime.datetime.now().date()
        current_hour = datetime.datetime.now().hour
        # 查询班级名相等的已完成的公开课关联教练
        public_course_coach_id = PublicCoursesCoach.objects.filter(
            Q(end_time__date__lt=current_date) | (Q(end_time__date=current_date) & Q(
                end_time__hour__lte=current_hour)),
            class_name__icontains=data.get('class_name'), deleted=False, ).values_list('id', flat=True)
        # uuid转字符串
        public_course_coach_str_id = [str(item) for item in public_course_coach_id]
        # 包含这些关联id的结算单，反向查询，去重。
        business_order = business_order.filter(
            type=BusinessOrderTypeEnum.public_course.value,
            business_order2object__deleted=False,
            business_order2object__object_id__in=public_course_coach_str_id,
        ).distinct()
    
    # business_order = prepare_business_orders_for_sorting(business_order)
    # sorted_orders = sorted(business_order, key=lambda o: (
    #     -o.settlement_status,
    #     (o.sort_name == '', o.sort_name),  # 有值的项先排序 由于 False < True，所以这将确保有值的 sorting_key 会排在没有值的 sorting_key 前面。
    #     -o.created_at.timestamp()  # sorted不可用对datetime使用负号排序，将 datetime 对象转换为时间戳进行倒序排序
    # ))
    # return sorted_orders

    # 优化方案：使用prefetch_related和select_related预加载关联数据
    business_order = business_order.select_related('project').prefetch_related(
        Prefetch(
            'business_order2object',
            queryset=BusinessOrder2Object.objects.filter(deleted=False).select_related(),
            to_attr='prefetched_bo2o'
        )
    )

    # 获取所有相关的PublicCoursesCoach记录，避免N+1查询
    bo2o_ids = [bo.id for bo in business_order if bo.type == BusinessOrderTypeEnum.public_course.value]
    public_courses_map = {}
    if bo2o_ids:
        bo2o_objects = BusinessOrder2Object.objects.filter(
            business_order_id__in=bo2o_ids, 
            deleted=False
        ).values('business_order_id', 'object_id')
        
        object_ids = [bo['object_id'] for bo in bo2o_objects]
        if object_ids:
            public_courses = PublicCoursesCoach.objects.filter(
                id__in=object_ids, 
                deleted=False
            ).values('id', 'class_name')
            
            # 创建映射关系
            public_courses_map = {str(pc['id']): pc['class_name'] for pc in public_courses}
            bo2o_map = {bo['business_order_id']: bo['object_id'] for bo in bo2o_objects}

    # 在Python中进行排序，避免复杂的数据库查询
    for order in business_order:
        if order.type == BusinessOrderTypeEnum.public_course.value:
            object_id = bo2o_map.get(order.id)
            order.sort_name = public_courses_map.get(object_id, '')
        else:
            order.sort_name = order.project.name if order.project else ''

    # 使用Python排序
    business_order = sorted(
        business_order,
        key=lambda x: (
            -x.settlement_status,  # 先按settlement_status降序排序
            1 if not x.sort_name else 0,  # 有值的项先排序
            x.sort_name or '',
            -x.created_at.timestamp()  # 按创建时间降序排序
        )
    )

    return business_order


def business_settlement_order_to_xlsx(business_order):

    serializer = BusinessSettlementOrdersSerializer(business_order, many=True).data

    title = [
        '教练姓名', '教练类型', '工作类型', '工作形势', '项目名称', '教练单价', '税点',
        '其他费用金额', '客户姓名', '班级名称', '工作时间', '结算时长', '应付金额（元）', '客户实付（元）',
        '订单编号', '支付状态', '提现状态', '结算状态', '运营活动主题', '平台扣除比例',
        '平台服务费', '税费', '教练实际收益', '申请发起人', '申请提现时间', '结算处理人',
        '结算时间', '剩余可提现订单', '剩余可提现金额（元）', '个人辅导总收入（元）', '个人辅导总结算金额（元）'
    ]
    data = [title]

    for item in serializer:
        data.append([
            item.get('coach_name'),
            CoachUserTypeEnum.get_display(item.get('coach_type')),
            BusinessOrderTypeEnum.get_display(item.get('type')),
            WorkTypeEnum.get_display(item.get('work_type')),
            item.get('project_name'),
            item.get('coach_price'),
            item.get('tax_point'),
            item.get('total_extra_cost'),
            item.get('member_name'),
            item.get('class_name'),
            item.get('work_time'),
            item.get('duration'),
            item.get('payable_amount'),
            item.get('member_paid'),
            item.get('id'),
            BusinessOrderPayStatusEnum.get_display(item.get('pay_status')),
            BusinessOrderWithdrawalStatusEnum.get_display(item.get('withdrawal_status')),
            BusinessOrderSettlementStatusEnum.get_display(item.get('settlement_status')),
            item.get('activity_theme'),
            item.get('platform_scale'),
            item.get('platform_service_amount'),
            item.get('tax_amount'),
            item.get('coach_actual_income'),
            item.get('create_user'),
            item.get('apply_settlement_time'),
            item.get('processor'),
            item.get('settlement_time'),
            item.get('remain_withdrawable_orders'),
            item.get('remain_withdrawable_amount'),
            item.get('to_c_order_actual_income'),
            item.get('to_c_settlement_actual_income'),
        ])

    #  文件名
    name = f'admin/business_settlement_order_detail/{pendulum.now().to_date_string()}/{pendulum.now().to_date_string()}{randomPassword()}.xlsx'

    # 获取文件数据
    file = excel_pubilc.save_excel(data, 'interview_detail')

    # 上传阿里云
    aliyun.AliYun('cwcoach').send_file(name, file.getvalue())

    return f'{settings.ALIYUN_SDN_BASE_URL}/{name}'


def get_coach_to_c_order_actual_income(coach):
    """
    获取教练C端订单总收入
    :param coach: 教练对象
    :return: 教练待提现的订单数量
    """
    orders = Order.objects.filter(
        public_attr__target_user_id=coach.user_id, deleted=False, status=OrderStatusEnum.paid.value
    )
    actual_income = 0
    for item in orders:
        platform_service_scale, real_income, tax_amount, platform_service_amount = coach_public.calculate_coach_order_income_details(
            item, item.public_attr.target_user_id, item.public_attr.user_id)
        actual_income += real_income
    actual_income = Decimal(str(actual_income)) / Decimal('100')  # 将金额单位从分转换为元
    return actual_income  # 返回获取教练C端订单总收入


def get_coach_to_c_settlement_actual_income(coach):
    """
    获取教练C端结算单总收入
    :param coach: 教练对象
    :return: 教练待提现的订单数量
    """
    all_actual_income = BusinessOrder.objects.filter(
        business_settlement__isnull=False,  # 结算记录不为空，表示已经进行结算
        type=BusinessOrderTypeEnum.personal.value,  # 订单类型为个人订单
        work_type=WorkTypeEnum.one_to_one.value,  # 工作形式为“一对一辅导”
        deleted=False,  # 订单未被删除
        coach_id=coach.id,  # 指定教练的订单
        settlement_status=BusinessOrderSettlementStatusEnum.settled.value,  # 订单结算状态为“已结算”
    ).aggregate(all_actual_income=Sum('coach_actual_income'))

    all_actual_income = all_actual_income.get('all_actual_income', 0) or 0  # 获取未提现收入总额，默认为0
    all_actual_income = Decimal(str(all_actual_income)) / Decimal('100')  # 将金额单位从分转换为元
    return all_actual_income
