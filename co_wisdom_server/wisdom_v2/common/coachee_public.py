from utils.message.lark_message import Lark<PERSON>essageC<PERSON>
from wisdom_v2.enum.pay_enum import OrderStatusEnum
from wisdom_v2.enum.project_enum import ProjectStatusEnum
from wisdom_v2.enum.project_interview_enum import ProjectInterviewTypeEnum
from wisdom_v2.models import ProjectInterview, ProjectCoach, ProjectMember
from wisdom_v2.views import constant



def get_personal_coach_customer_list(user_id):
    """
    查询个人教练的客户id列表
    :param user_id: 教练用户id  type: int
    :return: 客户的user_id列表  type: list
    """
    coachee_id_list_interview = ProjectInterview.objects.filter(
        deleted=False,
        public_attr__project__isnull=True,
        public_attr__user=user_id, public_attr__type=constant.ATTR_TYPE_INTERVIEW,
        public_attr__target_user_id__isnull=False).exclude(
        order__status__in=[OrderStatusEnum.pending_pay, OrderStatusEnum.under_refund, OrderStatusEnum.refunded])
    coachee_id_list = set(coachee_id_list_interview.values_list('public_attr__target_user_id', flat=True))

    return coachee_id_list


def get_coach_customer_list(user_id):
    """
    查询教练的全部客户id列表
    :param user_id: 教练用户id  type: int
    :return: 客户的user_id列表  type: list
    """

    coachee_id_list_interview = set(ProjectInterview.objects.filter(
        deleted=False,
        public_attr__user=user_id, public_attr__type=constant.ATTR_TYPE_INTERVIEW,
        public_attr__target_user_id__isnull=False).exclude(
        order__status__in=[OrderStatusEnum.pending_pay, OrderStatusEnum.under_refund, OrderStatusEnum.refunded]
    ).exclude(type=ProjectInterviewTypeEnum.chemical_interview).values_list('public_attr__target_user_id', flat=True))

    coachee_id_list_relation = set(ProjectCoach.objects.filter(
        coach__user_id=user_id,
        project__isnull=False,
        project__deleted=False,
        member__isnull=False,
        project_group_coach__isnull=True,
        deleted=False).values_list('member_id', flat=True))
    coachee_id_list = set.union(coachee_id_list_interview, coachee_id_list_relation)

    return coachee_id_list


def get_project_coach_customer_list(user_id, project_id):
    """
    查询指定项目下，教练的客户id列表
    :param user_id: 教练用户id  type: int
    :param project_id: 项目id  type: int
    :return: 客户的user_id列表  type: list
    """

    coachee_id_list_interview = set(ProjectInterview.objects.filter(
        deleted=False,
        public_attr__project_id=project_id, type=ProjectInterviewTypeEnum.formal_interview,
        public_attr__user=user_id, public_attr__type=constant.ATTR_TYPE_INTERVIEW,
        public_attr__target_user_id__isnull=False).exclude(
        public_attr__status=constant.ATTR_STATUS_INTERVIEW_CANCEL).exclude(
        order__status__in=[OrderStatusEnum.pending_pay, OrderStatusEnum.under_refund, OrderStatusEnum.refunded]
    ).values_list('public_attr__target_user_id', flat=True))

    coachee_id_list_relation = set(ProjectCoach.objects.filter(
        coach__user_id=user_id,
        project_id=project_id,
        project__deleted=False,
        member__isnull=False,
        project_group_coach__isnull=True,
        deleted=False).values_list('member_id', flat=True))
    coachee_id_list = set.union(coachee_id_list_interview, coachee_id_list_relation)

    return coachee_id_list


def user_is_login_allowed(user_id):
    """
    检查指定用户是否参与了至少一个非已输单状态的项目。

    :param user_id: 一个整数，表示用户的ID。
    :return: 如果找到已输单状态的项目，则返回False。如果没有找到返回False。

    注意: 如果在尝试获取项目状态的过程中发生异常，会通过LarkMessageCenter将错误信息发送飞书。
    """
    # 如果没有提供有效的用户ID，返回False
    if not user_id or not str(user_id).isdigit():
        return False

    try:
        # 查询未被删除的、包含指定用户的项目
        project_member = ProjectMember.objects.filter(user=user_id, deleted=False)
        # 如果第一个是已输单状态的项目，返回False
        if project_member.exists() and project_member.first().project.status == ProjectStatusEnum.lost:
            return False
    except Exception as e:
        LarkMessageCenter().send_other_backend_message(
            'user_is_login_allowed', 'error',
            {'msg:': str(e), 'params': f'user_id:{user_id}'}
        )
    return True
