from django.db.models import Sum

from wisdom_v2.models import OneToOneCoach


def get_project_member_one_to_one_time(project_member):
    """
    获取项目用户配置的全部的一对一辅导时长
    : project_member 项目用户对象
    : return 项目用户配置的一对一辅导时间
    """
    one_to_one_time = OneToOneCoach.objects.filter(
        project_bundle__project_member_id=project_member.id, deleted=False).aggregate(total_online_time=Sum('online_time'))
    total_time = one_to_one_time.get('total_online_time', 0)
    return total_time
