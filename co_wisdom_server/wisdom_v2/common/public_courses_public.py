from datetime import datetime


def get_status_display(public_course):
    """
    获取公开课状态
    :param public_course: 对象
    :return: 公开课状态 1-未开始 2-进行中 3-已结束 4-已删除
    """
    # 获取当前日期和时间
    current_date = datetime.now().date()
    current_hour = datetime.now().hour

    # 默认设置为未开始
    status = 1

    # 判断公开课是否已删除
    if public_course.deleted:
        status = 4

    # 判断公开课是否未开始
    elif ((public_course.start_time.date() > current_date) or
          (public_course.start_time.date() == current_date and public_course.start_time.hour > current_hour)):
        status = 1

    # 判断公开课是否进行中
    elif ((public_course.start_time.date() < current_date or
          (public_course.start_time.date() == current_date and public_course.start_time.hour <= current_hour)) and
          (public_course.end_time.date() > current_date or
           (public_course.end_time.date() == current_date and public_course.end_time.hour > current_hour))):
        status = 2

    # 判断公开课是否已结束
    elif ((public_course.end_time.date() < current_date) or
          (public_course.end_time.date() == current_date and public_course.end_time.hour <= current_hour)):
        status = 3

    return status


def get_coach_content(public_course):
    """
    获取公开课教练信息
    :param public_course: 公开课对象
    :return: 公开课教练信息
    """
    coach_date = []
    for public_courses_coach in public_course.public_courses_coach.filter(deleted=False).order_by('created_at').all():
        try:

            resume = public_courses_coach.resume
            domain = resume.coach_domain[:3] if resume.coach_domain else []

            date = {
                'id': public_courses_coach.coach_id,
                'user_id': public_courses_coach.coach.user_id,
                'true_name': public_courses_coach.coach.user.cover_name,
                'gender': public_courses_coach.coach.user.gender,
                'domain': domain,
                'price': public_courses_coach.coach.display_price,
                'working_years': resume.working_years if resume else None,
                'coach_auth': resume.coach_auth if resume else None,
                'resume_id': resume.id if resume else None,

            }
            coach_date.append(date)
        except:
            continue
    return coach_date
