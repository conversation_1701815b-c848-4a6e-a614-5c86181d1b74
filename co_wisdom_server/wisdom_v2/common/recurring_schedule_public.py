import datetime
from random import randint

import pendulum
from copy import deepcopy
from datetime import timedelta

from wisdom_v2.enum.schedule_enum import RecurringScheduleRepeatTypeEnum


def get_next_repeat_date(target_date, original_start_date, repeat_type, delta, direction):
    """
    根据指定的重复类型计算下一个重复日期。

    :param target_date: datetime对象，表示目标日期。
    :param original_start_date: datetime对象，表示原始日程的开始日期。
    :param repeat_type: int, 表示重复类型（每天、每周、每月）。
    :param delta: int, 表示需要跳过的天数（如1表示跳过1天）。
    :param direction: int, 表示方向，1为向后，-1为向前。
    :return: 返回计算得到的下一个重复日期。
    """

    new_date = target_date

    # 如果重复类型是每天
    if repeat_type == RecurringScheduleRepeatTypeEnum.daily:

        new_date = target_date + timedelta(days=delta * direction)

    # 如果重复类型是每周
    elif repeat_type == RecurringScheduleRepeatTypeEnum.weekly:
        # 计算目标日期和原始开始日期之间的天数差
        day_diff = (original_start_date.weekday() - target_date.weekday()) % 7

        if direction == 1:  # 如果方向是向前
            # 如果day_diff为0，表示目标日期和原始日期是同一天，则直接跳过 delta 周
            # 否则，跳到下一个与原始日期相同的星期，并考虑 delta
            days_to_add = (day_diff if day_diff != 0 else 7) + 7 * (delta - 1)
            new_date = target_date + timedelta(days=days_to_add)
        else:  # 如果方向是向后
            # 如果day_diff为0，表示目标日期和原始日期是同一天，则直接回退 delta 周
            # 否则，回退到上一个与原始日期相同的星期，并考虑 delta
            days_to_subtract = (7 - day_diff if day_diff != 0 else 7) - 7 * (delta - 1)
            new_date = target_date - timedelta(days=days_to_subtract)

    elif repeat_type == RecurringScheduleRepeatTypeEnum.monthly:
        # 如果是同一个月，向后寻找，日程的开始日期在当前日期之前，则更新为开始日期
        if new_date.month == original_start_date.month and new_date.day > original_start_date.day and direction == -1:
            new_date = new_date + timedelta(days=original_start_date.day - new_date.day)
        # 如果是同一个月，向前寻找，日程的开始日期在当前日期之后，则更新为开始日期
        elif new_date.month == original_start_date.month and new_date.day < original_start_date.day and direction == 1:
            new_date = new_date + timedelta(days=original_start_date.day - new_date.day)

        else:
            # 使用 pendulum 创建日期对象
            pendulum_date = pendulum.datetime(
                new_date.year, new_date.month, new_date.day,
                new_date.hour, new_date.minute, new_date.second)

            # 根据方向增加或减少月份
            pendulum_date = pendulum_date.add(months=delta * direction)

            # 如果新日期的日与原始日期的日不同，持续跳到下一个月，直到日期相同
            while True:
                try:
                    # 尝试设置日期为原始日期的日期
                    pendulum_date = pendulum_date.set(day=original_start_date.day)
                    break
                except ValueError:
                    # 如果设置的日期无效（例如尝试设置2月31日），则继续增加或减少月份
                    pendulum_date = pendulum_date.add(months=direction)

            # 将 pendulum 对象转换回 datetime 对象
            new_date = pendulum_date.in_tz("UTC").naive()

    return new_date


def generate_repeated_instances(recurring, target_date, status=1, max_instances=15):
    """
    根据RecurringSchedule实例和目标日期生成重复的日程实例。

    :param recurring: RecurringSchedule实例。这是用来定义重复规则的对象。
    :param target_date: datetime对象，表示要从哪一天开始生成重复的日程实例。
    :param status: int, 1表示往前推算，2表示往后推算。
    :param max_instances: int, 默认为15。表示最多生成的日程实例的数量。
    :return: 返回一个日程实例列表。
    """

    instances = []  # 存储生成的日程实例
    original_schedule = recurring.schedule  # 原始的日程
    count = 0
    direction = 1 if status == 1 else -1

    while count < max_instances:

        # 如果日期对齐并且当前日期不在排除的日期列表中
        if (target_date.strftime('%Y-%m-%d') not in (recurring.excluded_dates or []) and
                date_in_recurring_schedule(recurring, target_date.date())):
            # 复制原始日程并为当前日期创建新的日程实例
            instance = deepcopy(original_schedule)
            instance.id = int(f'{instance.id}000{randint(100000, 999999)}')
            instance.main_id = original_schedule.id
            instance.public_attr.start_time = datetime.datetime.combine(
                target_date.date(), original_schedule.public_attr.start_time.time())
            instance.public_attr.end_time = datetime.datetime.combine(
                target_date.date(), original_schedule.public_attr.end_time.time())
            instances.append(instance)
            count += 1

        # 更新当前日期为下一个日期，并计算下一个重复日期
        target_date = get_next_repeat_date(target_date, original_schedule.public_attr.start_time, recurring.repeat_type, 1, direction)
        # 如果超过截止日期，则终止循环
        if recurring.end_repeat_date is not None and status == 1 and target_date.date() > recurring.end_repeat_date:
            break
        # 向前寻找需要包含结束时间
        elif status == 2 and target_date.date() < original_schedule.public_attr.start_time.date():
            break

    return instances


def date_in_recurring_schedule(recurring, target_date):
    """
    检查给定的日期是否在指定的重复日程中。

    :param recurring: RecurringSchedule实例
    :param target_date: datetime.date对象，表示目标日期。
    :return: True如果日期在重复日程中，否则False
    """
    original_start_date = recurring.schedule.public_attr.start_time.date()

    # 如果目标日期在原始开始日期之前，直接返回False
    if target_date < original_start_date:
        return False

    # 如果存在结束日期，并且目标日期在结束日期之后，直接返回False
    if recurring.end_repeat_date and target_date > recurring.end_repeat_date:
        return False

    # 检查日期是否在排除的日期列表中
    if recurring.excluded_dates:
        if isinstance(target_date, str):
            tmp_target_date = target_date
        elif isinstance(target_date, datetime.datetime):
            tmp_target_date = target_date.date().strftime('%Y-%m-%d')
        else:
            tmp_target_date = target_date.strftime('%Y-%m-%d')
        if tmp_target_date in recurring.excluded_dates:
            return False

    # 根据重复类型进行检查
    if recurring.repeat_type == RecurringScheduleRepeatTypeEnum.daily:
        return True  # 每天都重复，所以直接返回True
    elif recurring.repeat_type == RecurringScheduleRepeatTypeEnum.weekly:
        return (target_date - original_start_date).days % 7 == 0  # 检查是否是7的倍数
    elif recurring.repeat_type == RecurringScheduleRepeatTypeEnum.monthly:
        return target_date.day == original_start_date.day  # 检查日期是否相同

    return False


def time_within_recurring_schedule(
        excluded_dates, repeat_type, end_repeat_date, schedule_start_time, schedule_end_time, start_date, end_date):
    """
    根据重复日程的规则计算在指定时间范围内的所有实际日程时间。

    参数:
    - excluded_dates: 排除的日期列表。
    - repeat_type: 重复类型，如每日、每周、每月。
    - end_repeat_date: 重复结束日期。
    - schedule_start_time: 日程开始时间。
    - schedule_end_time: 日程结束时间。
    - start_date: 查询开始日期。
    - end_date: 查询结束日期。

    返回:
    - data: 一个字典，键为日期，值为该日期内日程的开始时间和结束时间列表。
    """
    data = {}
    excluded_dates = excluded_dates if excluded_dates else []  # 确保排除日期列表已初始化
    while start_date <= end_date:
        next_date = start_date + timedelta(days=1)  # 准备下一次迭代的日期
        tmp_target_date = start_date.strftime('%Y-%m-%d')  # 格式化当前日期

        if start_date < schedule_start_time.date():  # 跳过还未开始的日期
            start_date = next_date
            continue

        if tmp_target_date in excluded_dates:  # 跳过排除的日期
            start_date = next_date
            continue

        if end_repeat_date and start_date > end_repeat_date:  # 如果当前日期超过重复结束日期，则停止
            break

        # 根据重复类型添加当前日期的日程时间到数据字典
        if repeat_type == RecurringScheduleRepeatTypeEnum.daily:
            data.update(recurring_schedule_date_to_str(start_date, schedule_start_time, schedule_end_time))
        elif repeat_type == RecurringScheduleRepeatTypeEnum.weekly and (start_date - schedule_start_time.date()).days % 7 == 0:
            data.update(recurring_schedule_date_to_str(start_date, schedule_start_time, schedule_end_time))
        elif repeat_type == RecurringScheduleRepeatTypeEnum.monthly and start_date.day == schedule_start_time.day:
            data.update(recurring_schedule_date_to_str(start_date, schedule_start_time, schedule_end_time))

        start_date = next_date  # 更新开始日期为下一天

    return data


def recurring_schedule_date_to_str(start_date, schedule_start_time, schedule_end_time):
    """
    根据提供的日期和时间生成日程的时间戳。

    参数:
    - start_date: 日程的日期。
    - schedule_start_time: 日程开始的时间。
    - schedule_end_time: 日程结束的时间。

    返回:
    - 一个字典，键为日期字符串，值为包含开始时间和结束时间的列表。
    """
    k = start_date.strftime("%Y-%m-%d")  # 日程日期
    v = [
        datetime.datetime(
            start_date.year, start_date.month, start_date.day, schedule_start_time.hour,
            schedule_start_time.minute, schedule_start_time.second),
        datetime.datetime(
            start_date.year, start_date.month, start_date.day, schedule_end_time.hour,
            schedule_end_time.minute, schedule_end_time.second)]
    return {k: v}  # 返回日期对应的时间戳


def generate_recurring_datetime(repeat_type, start_time, end_time, end_repeat_date, max_count=1000):
    """
    生成指定重复类型和时间范围内的日程日期列表。

    :param repeat_type: 重复类型枚举，如每天、每周、每月。
    :param start_time: 重复开始的日期，datetime对象。
    :param end_time: 重复开始的日期，datetime对象。
    :param end_repeat_date: 重复结束的日期，str对象。
    :param max_count: 生成日期的最大数量，默认为1000。
    :return: 生成的日期列表
    """
    date_list = []  # 存储生成的日程日期
    count = 0
    while count < max_count:

        # 检查是否达到结束日期或已经包含最大数量的日期
        if start_time.strftime('%Y-%m-%d') > end_repeat_date or count >= max_count:
            break

        date_list.append([
            start_time,
            datetime.datetime.combine(start_time.date(), end_time.time())])
        count += 1

        # 计算下一个重复日期
        if repeat_type == RecurringScheduleRepeatTypeEnum.daily:
            start_time += timedelta(days=1)
        elif repeat_type == RecurringScheduleRepeatTypeEnum.weekly:
            start_time += timedelta(days=7)
        elif repeat_type == RecurringScheduleRepeatTypeEnum.monthly:
            # 使用pendulum处理月份加法以避免跨月日不一致问题
            pendulum_date = pendulum.instance(start_time).add(months=1)
            while True:
                try:
                    # 尝试设置日期为原始日期的日期
                    pendulum_date = pendulum_date.set(day=start_time.day)
                    break
                except ValueError:
                    # 如果设置的日期无效（例如尝试设置2月31日），则继续增加或减少月份
                    pendulum_date = pendulum_date.add(months=1)
            start_time = pendulum_date.naive()
    return date_list
