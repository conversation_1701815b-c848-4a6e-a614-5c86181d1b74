from django.conf import settings

from utils import qr_code as get_qr_code
from utils.aliyun_sls_log import AliyunSlsLogLayout
from utils.message import email as message_email
from utils.messagecenter.sms import SMS
from utils.wechat_oauth import WeChatMiniProgram
from wisdom_v2.common.stakeholder_interview_public import check_elements_exist
from wisdom_v2.enum.service_content_enum import MultipleAssociationRelationTypeEnum
from wisdom_v2.enum.user_backend_enum import UserBackendTypeEnum
from wisdom_v2.models import ChangeObservation, MultipleAssociationRelation, ProjectInterested, ChangeObservationAnswer, \
    CompanyMember, UserBackend
from wisdom_v2.views.constant import ADMIN_SEND_SMS, ADMIN_SEND_EMAIL


def get_change_observation_title(project_member):
    """
    根据给定的项目成员生成一个“改变观察”的标题。

    如果此成员没有与之相关的“改变观察”，则返回的标题为其真实姓名后跟上“的改变观察”。
    否则，它将添加一个计数表示这是他们的第几次“改变观察”。

    : project_member (Model) 项目成员的对象实例。
    : return 生成的“改变观察”的标题。
    """
    count = ChangeObservation.objects.filter(project_member=project_member, deleted=False).count()
    if count == 0:
        return f'{project_member.user.cover_name}的改变观察'
    else:
        return f'{project_member.user.cover_name}的第{count + 1}次改变观察'


def get_change_observation_to_stakeholders_all(change_observation):
    """
    获取改变观察反馈的全部利益相关者
    """
    stakeholders_ids = list(MultipleAssociationRelation.objects.filter(
        type=MultipleAssociationRelationTypeEnum.stakeholder_change_observation.value, deleted=False,
        main_id=str(change_observation.pk)).values_list('secondary_id', flat=True))

    interested = ProjectInterested.objects.filter(is__in=stakeholders_ids, deleted=False).all()
    return interested


def check_change_observation_update_stakeholder(stakeholders, change_observation):
    """
    检查修改参与的利益相关者数据，并返回错误信息,新增,删除的利益相关者数据
    stakeholders：利益相关者管理id列表
    stakeholder_interview_module：利益相关者访谈配置对象
    """
    err_msg, deleted_list, add_list = None, [], []
    stakeholder_ids = list(ProjectInterested.objects.filter(
        master_id=change_observation.project_member.user_id,
        project_id=change_observation.project_member.project_id,
        deleted=False).values_list('id', flat=True))

    if len(stakeholders) > change_observation.max_stakeholders_count:
        err_msg = '参与访谈的人员数量错误'
        return err_msg, deleted_list, add_list

    if not check_elements_exist(stakeholders, stakeholder_ids):  # 检查传入的利益相关者关系id是否在被教练者的所有利益相关者关系id中
        err_msg = '参与访谈的人员错误'
        return err_msg, deleted_list, add_list

    exists_project_interested_ids = list(MultipleAssociationRelation.objects.filter(
        main_id=str(change_observation.pk), type=MultipleAssociationRelationTypeEnum.stakeholder_change_observation.value,
        deleted=False).values_list('secondary_id', flat=True))
    deleted_list = list(set(exists_project_interested_ids).difference(set(stakeholders)))
    add_list = list(set(stakeholders).difference(set(exists_project_interested_ids)))
    if deleted_list:
        # 移除前检查有没有已经填写了的利益相关者
        project_interested_user_id = list(ProjectInterested.objects.filter(
            id__in=deleted_list, deleted=False).values_list('interested_id', flat=True))
        if ChangeObservationAnswer.objects.filter(
                change_observation=change_observation, public_attr__user_id__in=project_interested_user_id).exists():
            err_msg = '当前参与人员已填写无法移除'
    return err_msg, deleted_list, add_list


def update_change_observation_stakeholders(add_list, deleted_list, change_observation):
    """
    修改参与的利益相关者
    add_list：新增利益相关者id关系列表
    deleted_list：删除利益相关者id关系列表
    """
    if add_list:
        change_observation_list = []
        for project_interested_id in add_list:
            change_observation_list.append(
                MultipleAssociationRelation(
                    main_id=str(change_observation.pk), secondary_id=project_interested_id,
                    type=MultipleAssociationRelationTypeEnum.stakeholder_change_observation.value
                ))
        # 创建利益相关者访谈
        MultipleAssociationRelation.objects.bulk_create(change_observation_list)
    if deleted_list:
        # 移除利益相关者访谈
        MultipleAssociationRelation.objects.filter(
            main_id=str(change_observation.pk), secondary_id__in=deleted_list, deleted=False,
            type=MultipleAssociationRelationTypeEnum.stakeholder_change_observation.value
        ).update(deleted=True)


def get_invite_stakeholders(relation):
    """
    : param relation: 改变观察反馈利益相关者关系 MultipleAssociationRelation的用例列表
    : return: 利益相关者列表
    """

    data = []

    for item in relation:

        project_interested = ProjectInterested.objects.filter(
            id=item.secondary_id, deleted=False).first()

        if not project_interested:
            return data , '错误的利益相关者关系'

        if not item.send_wechat_invite_at:
            status = 1  # 未发送
        elif ChangeObservationAnswer.objects.filter(
                change_observation=item.main_id, public_attr__user_id=project_interested.interested_id).exists():
            status = 3  # 已填写
        elif item.send_wechat_invite_at:
            status = 2  # 已发送
        else:
            return data, '改变观察反馈状态错误'

        company_member = CompanyMember.objects.filter(
            company=project_interested.project.company, user=project_interested.interested, deleted=False).first()
        position = company_member.position if company_member else None

        data.append({
            'id': str(item.pk),
            'status': status,
            'true_name': project_interested.interested.cover_name,
            'relation': project_interested.relation,
            'position': position,
        })
    return data, None


def send_change_observation_stakeholder_notice(change_observation, is_click_to_send=False):
    """
    发送改变观察反馈通知
    : param change_observation: 改变观察反馈对象
    : param is_click_to_send: 是否是点击发送 如果是点击发送之前发送过的用户需要再次通知
    ：return: 修改后的改变观察反馈对象
    """

    # 查询已填写的利益相关者对应的关联表id
    answer_user_id = list(ChangeObservationAnswer.objects.filter(
        change_observation=change_observation,
    ).values_list('public_attr__user__interested_user', flat=True))

    # 查询到所有需要发送的
    project_interested = MultipleAssociationRelation.objects.filter(
        type=MultipleAssociationRelationTypeEnum.stakeholder_change_observation.value,
        main_id=change_observation.pk, deleted=False).exclude(
        secondary_id__in=answer_user_id  # 排除已填写的利益相关者
    )

    # 需要发送短信的利益相关者id
    sms_project_interested_ids = []
    if ADMIN_SEND_SMS in change_observation.remind_type:  # 短信
        # 手动点击发送则之前发送过的也同步发送
        if is_click_to_send:
            sms_project_interested_ids = project_interested.values_list('secondary_id', flat=True)
        # 默认只给没有发送过的用户发
        else:
            sms_project_interested_ids = project_interested.exclude(
                is_send_sms_notice=True).values_list('secondary_id', flat=True)

    if sms_project_interested_ids:
        project_interested_objs = ProjectInterested.objects.filter(
            id__in=sms_project_interested_ids, deleted=False).all()
        send_change_observation_sms_notice(project_interested_objs, change_observation)

    # 需要发送邮件的利益相关者id列表
    email_project_interested_ids = []
    if ADMIN_SEND_EMAIL in change_observation.remind_type:  # 邮件
        # 手动点击发送则之前发送过的也同步发送
        if is_click_to_send:
            email_project_interested_ids = project_interested.values_list('secondary_id', flat=True)
        # 默认只给没有发送过的用户发
        else:
            email_project_interested_ids = project_interested.exclude(
                is_send_email_notice=True).values_list('secondary_id', flat=True)
    if email_project_interested_ids:
        project_interested_objs = ProjectInterested.objects.filter(
            id__in=email_project_interested_ids, deleted=False).all()
        send_change_observation_email_notice(project_interested_objs, change_observation)
    change_observation.is_send = True
    change_observation.save()
    return change_observation


def send_change_observation_email_notice(
        stakeholder_users, change_observation, all_change_observation=None, all_interested=None, sender_id=None):
    """
    改变观察反馈发送邮件通知
    针对利益相关者有多个改变观察反馈，只发送一条提醒消息，而不是每一个改变观察都发送一条（利益相关者可在点击连接后查看到所有的待填写改变观察反馈）
    传入all_change_observation与all_interested目的是在只发送一条消息时，也要同步修改所有的改变观察关系的提醒消息发送情况

    : param stakeholder_users: 发送邮件的利益相关者
    : param change_observation: 改变观察反馈
    : param all_change_observation: 所有改变观察反馈
    : param all_interested: 所有利益相关者
    : return 发送失败的利益相关者名称列表
    """
    change_observation_id = [item.id for item in all_change_observation] if all_change_observation else [
        change_observation.id]
    project_member = change_observation.project_member

    end_date = change_observation.stakeholders_write_end_date.strftime("%Y.%m.%d") if change_observation.stakeholders_write_end_date else ''

    # 获取项目管理员信息
    project_backend = UserBackend.objects.filter(project_id=project_member.project_id,
                                                 user_type=UserBackendTypeEnum.admin.value,
                                                 role__name='项目运营', deleted=False).first()
    err_name = []
    for stakeholder in stakeholder_users:
        stakeholder_id = [item.id for item in all_interested] if all_interested else [stakeholder.id]
        if not stakeholder.interested.email:
            err_name.append(stakeholder.interested.cover_name)
            continue

        # 生成短链接
        url_state, short_link = WeChatMiniProgram().get_url_link(
            'pages_evaluation/feedback_list/feedback_list',
            f'stakeholder_id={stakeholder.interested_id}&project_id={project_member.project_id}')

        data = f'{settings.SITE_URL}feedback/?stakeholder_id={stakeholder.interested_id}&project_id={project_member.project_id}'
        qr_code = get_qr_code.get_qr_code(data)
        param = {
            'stakeholder_name': stakeholder.interested.cover_name,
            'company_name': project_member.project.company.short,
            'user_name': project_member.user.cover_name,
            'qr_code': qr_code,
            'short_link': short_link,
            'project_manage_name': project_backend.user.cover_name,
            'project_manage_email': project_backend.user.email,
            'end_date': end_date,
        }
        state = message_email.message_send_email_base('send_change_observation', param, [stakeholder.interested.email], 
                                                     project_id=project_member.project_id, receiver_ids=stakeholder.interested_id, 
                                                     sender_id=sender_id)
        if state:
            MultipleAssociationRelation.objects.filter(
                main_id__in=change_observation_id,
                secondary_id__in=stakeholder_id,
                deleted=False,
                type=MultipleAssociationRelationTypeEnum.stakeholder_change_observation,
            ).update(is_send_email_notice=True)
        else:
            AliyunSlsLogLayout().send_third_api_log(
                user_id=stakeholder.interested.id,
                user_name=stakeholder.interested.cover_name,
                project_id=change_observation.project_member.project.id,
                message='改变观察反馈发送邮件通知',
                content=state,
            )
            err_name.append(stakeholder.interested.cover_name)
    return err_name


# 改变观察反馈发送短信通知
def send_change_observation_sms_notice(
        stakeholder_users, change_observation, all_change_observation=None, all_interested=None):
    """
    改变观察反馈发送短信通知
    针对利益相关者有多个改变观察反馈，只发送一条提醒消息，而不是每一个改变观察都发送一条（利益相关者可在点击连接后查看到所有的待填写改变观察反馈）
    传入all_change_observation与all_interested目的是在只发送一条消息时，也要同步修改所有的改变观察关系的提醒消息发送情况

    : param stakeholder_users: 发送短信的利益相关者
    : param change_observation: 改变观察反馈对象
    : param all_change_observation: 所有需要修改的改变观察反馈
    : param all_interested: 所有需要修改的利益相关者
    : return 发送失败的利益相关者名称列表
    """

    change_observation_id = [item.id for item in all_change_observation] if all_change_observation else [change_observation.id]
    short = change_observation.project_member.project.company.short
    name = change_observation.project_member.user.cover_name
    end_date = change_observation.stakeholders_write_end_date.strftime("%Y.%m.%d") if change_observation.stakeholders_write_end_date else ''

    path = 'pages_evaluation/feedback_list/feedback_list'

    err_name = []
    for stakeholder in stakeholder_users:
        stakeholder_id = [item.id for item in all_interested] if all_interested else [stakeholder.id]
        if not stakeholder.interested.phone:
            err_name.append(stakeholder.interested.cover_name)
            continue
        state, url = WeChatMiniProgram().get_url_link(
            path,
            f'stakeholder_id={stakeholder.interested.id}&project_id={change_observation.project_member.project_id}')
        if not state:
            err_name.append(stakeholder.interested.cover_name)
            continue
        content = f"{short}正在进行的教练项目邀请您为{name}提供反馈，点击 {url} 并在{end_date}完成填写!"
        msg = SMS().sendMsg(stakeholder.interested.phone, content)
        if msg.get('status') == 'success':
            MultipleAssociationRelation.objects.filter(
                main_id__in=change_observation_id,
                secondary_id__in=stakeholder_id,
                deleted=False,
                type=MultipleAssociationRelationTypeEnum.stakeholder_change_observation,
            ).update(is_send_sms_notice=True)
        else:
            AliyunSlsLogLayout().send_third_api_log(
                user_id=stakeholder.interested.id,
                user_name=stakeholder.interested.cover_name,
                project_id=change_observation.project_member.project.id,
                message='改变观察反馈发送短信通知',
                content=msg.get('msg'),
            )
            err_name.append(stakeholder.interested.cover_name)
    return err_name
