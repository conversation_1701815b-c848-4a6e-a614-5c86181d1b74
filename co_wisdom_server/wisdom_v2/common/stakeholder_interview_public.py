from datetime import datetime

from django.conf import settings
from django.db.models import Q

from wisdom_v2.common import coachee_public
from wisdom_v2.enum.project_interview_enum import DataType, ProjectInterviewTypeEnum, ProjectInterviewPlaceCategoryEnum
from wisdom_v2.enum.service_content_enum import InterviewRecordTemplateQuestionAnswerTypeEnum
from wisdom_v2.enum.user_enum import UserR<PERSON>Enum, UserTmpEnum
from wisdom_v2.models import ProjectMember, ProjectInterested, User, ProjectInterview, UserTmp, CompanyMember
from wisdom_v2.models_file import StakeholderInterview
from wisdom_v2.views.coach_task_action import CoachTaskQuestionnaireSerializer
from wisdom_v2.views.constant import ATTR_STATUS_INTERVIEW_CANCEL


def growth_node_get_stakeholder_interview_module_data(user_id):
    """
    获取用户成长节点列表中利益相关者访谈内容及利益相关者访谈的教练任务id
    user_id: 用户id
    """
    stakeholder_interview_module_data = []
    coach_task_id = None
    project_member = ProjectMember.objects.filter(user_id=user_id, deleted=False).first()
    if project_member:
        stakeholder_interview_module = project_member.stakeholder_interview_module.filter(deleted=False).first()
        if stakeholder_interview_module:
            coach_task_id = stakeholder_interview_module.coach_task_id
            data = {"data_type": DataType.stakeholder_interview,
                    "stakeholder_interview_module_id": stakeholder_interview_module.id,
                    "coach_task_id": stakeholder_interview_module.coach_task_id,
                    "name": "利益相关者访谈",
                    "created_at": stakeholder_interview_module.created_at,
                    'image_url': settings.NOT_SELECT_IMAGE_URL}
            # 访谈未开始
            if stakeholder_interview_module.start_date > datetime.now().date():
                data['describe'] = f"将在{stakeholder_interview_module.start_date.strftime('%Y年%m月%d日')}开启"
                data['status'] = 1  # 未开始，不跳转 点击toast访谈未开始
            # 访谈进行中
            elif stakeholder_interview_module.start_date <= datetime.now().date() <= stakeholder_interview_module.end_date:
                data['describe'] = "已开启，请前往邀请"
                data['status'] = 2  # 进行中，跳转邀请利益相关者洁面
            # 访谈已结束
            elif stakeholder_interview_module.end_date < datetime.now().date():
                # 教练已填写报告
                if stakeholder_interview_module.coach_task.coach_submit_time:
                    data['describe'] = "已完成，请查看报告"
                    data['status'] = 4  # 已结束教练已填写，点击进入访谈报告查看洁面（教练任务报告查看界面）
                else:  # 教练未填写
                    data['describe'] = '已结束，报告生成中'
                    data['status'] = 3  # 已结束教练未填写，点击toast请等待报告生成
            stakeholder_interview_module_data.append(data)
    return stakeholder_interview_module_data, coach_task_id


def get_stakeholder_interview_is_set(project_member, project_interested):
    is_set = False
    if project_member:
        if StakeholderInterview.objects.filter(
                project_interested=project_interested, deleted=False,
                stakeholder_interview_module__project_member=project_member).exists():
            is_set = True
    return is_set


def get_stakeholder_interview_is_cancel(project_member, project_interested):
    is_cancel = True
    if project_member:
        stakeholder_interview = StakeholderInterview.objects.filter(
            project_interested=project_interested, deleted=False, interview__isnull=False,
            stakeholder_interview_module__project_member=project_member).exists()
        if stakeholder_interview:
            is_cancel = False
    return is_cancel


def get_stakeholder_interview_is_complete(project_member, project_interested):
    """
    获取利益相关者访谈是否完成
    """
    if project_member:
        stakeholder_interview = StakeholderInterview.objects.filter(
            project_interested=project_interested, deleted=False, interview__isnull=False,
            stakeholder_interview_module__project_member=project_member).first()
        if stakeholder_interview:
            if stakeholder_interview.interview.coach_record_status:
                return True
            else:
                return False
    return


def get_stakeholder_interview_complete_count(project_member):
    """
    获取利益相关者访谈完成数量
    """
    stakeholder_interview_count = StakeholderInterview.objects.filter(
        deleted=False, interview__coach_record_status=True,
        stakeholder_interview_module__project_member=project_member).count()
    return stakeholder_interview_count


def check_update_stakeholder(stakeholders, stakeholder_interview_module, stakeholder_interview_number=None):
    """
    检查修改参与的利益相关者数据，并返回错误信息,新增,删除的利益相关者数据
    stakeholders：利益相关者管理id列表
    stakeholder_interview_module：利益相关者访谈配置对象
    """
    err_msg, deleted_list, add_list = None, [], []
    stakeholder_ids = list(ProjectInterested.objects.filter(
        master_id=stakeholder_interview_module.project_member.user_id,
        project_id=stakeholder_interview_module.project_member.project_id,
        deleted=False).values_list('id', flat=True))
    if stakeholder_interview_number:
        if len(stakeholders) > stakeholder_interview_number:
            err_msg = '参与访谈的人员数量错误'
            return err_msg, deleted_list, add_list
    if not check_elements_exist(stakeholders, stakeholder_ids):  # 检查传入的利益相关者关系id是否在被教练者的所有利益相关者关系id中
        err_msg = '参与访谈的人员错误'
        return err_msg, deleted_list, add_list
    exists_project_interested_ids = list(stakeholder_interview_module.stakeholder_interview.filter(
        deleted=False).values_list('project_interested_id', flat=True))
    deleted_list = list(set(exists_project_interested_ids).difference(set(stakeholders)))
    add_list = list(set(stakeholders).difference(set(exists_project_interested_ids)))
    if deleted_list:
        # 移除检查有没有已经预约了辅导的利益相关者访谈
        if stakeholder_interview_module.stakeholder_interview.filter(
                project_interested_id__in=deleted_list, interview__isnull=False).exists():
            err_msg = '当前参与人员已预约辅导无法移除'
    return err_msg, deleted_list, add_list


def update_stakeholder_interview(add_list, deleted_list, stakeholder_interview_module):
    """
    修改参与的利益相关者
    add_list：新增利益相关者id关系列表
    deleted_list：删除利益相关者id关系列表
    """
    if add_list:
        stakeholder_interview_list = []
        for project_interested_id in add_list:
            stakeholder_interview_list.append(
                StakeholderInterview(stakeholder_interview_module=stakeholder_interview_module,
                                     project_interested_id=project_interested_id))
        # 创建利益相关者访谈
        StakeholderInterview.objects.bulk_create(stakeholder_interview_list)
    if deleted_list:
        # 移除利益相关者访谈
        stakeholder_interview_module.stakeholder_interview.filter(
            deleted=False, project_interested_id__in=deleted_list).update(deleted=True)


def get_stakeholder_interview_data(user_id):
    """
    待反馈列表获取利益相关者访谈信息
    user_id: 利益相关者id
    """
    data = []
    # 查询当前user未预约、已预约未结束的利益相关者访谈
    stakeholder_interview = StakeholderInterview.objects.filter(
        Q(interview__isnull=True) | Q(interview__public_attr__end_time__gte=datetime.now()),
        project_interested__interested_id=user_id, deleted=False).order_by('-created_at')
    if stakeholder_interview.exists():
        for s in stakeholder_interview:
            data.append(
                {
                    "user": {
                        "id": s.stakeholder_interview_module.project_member.user_id,
                        "name": s.stakeholder_interview_module.project_member.user.cover_name
                    },
                    "title": "利益相关者访谈",
                    "data_type": DataType.stakeholder_interview,
                    "image_url": settings.NOT_SELECT_IMAGE_URL,
                    "stakeholder_interview_detail": {
                        "id": s.id,
                        "interview_time": f"{s.interview.public_attr.start_time.strftime('%Y-%m-%d %H:%M')}-"
                                          f"{s.interview.public_attr.end_time.strftime('%H:%M')}"
                        if s.interview else '点击预约教练时间'
                    }
                }
            )
    return data


def get_stakeholder_interview_detail_data(interview):
    if interview.public_attr.status != ATTR_STATUS_INTERVIEW_CANCEL:
        stakeholder_interview = interview.stakeholder_interview.filter(deleted=False).first()
    else:
        cancel_stakeholder_interview = interview.cancel_stakeholder_interview.filter(deleted=False).first()
        if not cancel_stakeholder_interview:
            return
        stakeholder_interview = cancel_stakeholder_interview.stakeholder_interview
    if not stakeholder_interview:
        return
    manage_list = stakeholder_interview.project_interested.project.manager_list
    project_manager_phone = settings.DEFAULT_PROJECT_MANAGER_PHONE if not manage_list else manage_list[0]['phone']
    company_member = CompanyMember.objects.filter(
        user_id=stakeholder_interview.project_interested.interested_id,
        company_id=stakeholder_interview.stakeholder_interview_module.project_member.project.company_id).first()
    data = {
        "relation": stakeholder_interview.project_interested.relation,
        "true_name": stakeholder_interview.project_interested.master.cover_name,
        "head_image_url": stakeholder_interview.project_interested.master.head_image_url,
        "project_manager_phone": project_manager_phone,
        "start_date": stakeholder_interview.stakeholder_interview_module.start_date.strftime('%Y-%m-%d'),
        "end_date": stakeholder_interview.stakeholder_interview_module.end_date.strftime('%Y-%m-%d'),
        "duration": stakeholder_interview.stakeholder_interview_module.duration,
        "stakeholder_id": stakeholder_interview.project_interested.interested_id,
        "main_id": stakeholder_interview.project_interested.master_id,
        "position": company_member.position if company_member else ''
    }
    return data


def add_stakeholder_interview_coach_task_record(interview_id):
    """
    利益相关者访谈，将教练每次填写完成辅导记录添加入缓存数据中
    interview_id: 辅导id
    """
    from wisdom_v2.app_views.app_interview_actions import InterviewRecordQuestionAnswerEditDetailTraineeCoachSerializer

    interview = ProjectInterview.objects.filter(id=interview_id, deleted=False,
                                                type=ProjectInterviewTypeEnum.stakeholder_interview).first()
    if not interview:
        return
    stakeholder_interview = StakeholderInterview.objects.filter(interview=interview, deleted=False).first()
    if not stakeholder_interview:
        return
    coach_task = stakeholder_interview.stakeholder_interview_module.coach_task
    if not coach_task:
        return
    if coach_task.coach_submit_time:
        return
    # 教练没有提交利益相关者访谈报告时追加缓存数据
    coach_task_data = CoachTaskQuestionnaireSerializer(
        context={'role': UserRoleEnum.coach}).get_question_detail(coach_task)
    if not coach_task_data:
        return
    # 查出教练已填写完成的利益相关者访谈辅导记录
    stakeholder_interview_module = stakeholder_interview.stakeholder_interview_module
    is_submit_interview_ids = list(StakeholderInterview.objects.filter(
        stakeholder_interview_module=stakeholder_interview_module, deleted=False,
        interview__coach_record_status=True).values_list('interview_id', flat=True))
    interviews = ProjectInterview.objects.filter(id__in=is_submit_interview_ids).order_by('public_attr__end_time')
    if not interviews.exists():
        return
    interview_answers = [
        InterviewRecordQuestionAnswerEditDetailTraineeCoachSerializer(
            context={'role': UserRoleEnum.coach}).get_question_detail(submit_interview)
        for submit_interview in interviews]
    for index, question in enumerate(coach_task_data):
        # 只有普通文本需要写到缓存中，总结报告目前只有普通文本
        if question.get('answer_type') == InterviewRecordTemplateQuestionAnswerTypeEnum.normal_text.value:
            for interview_answer in interview_answers:
                if interview_answer[index]['answer']:
                    content = interview_answer[index]['answer']['content']
                    if not question['answer']:
                        question['answer']['content'] = content
                        question['content'] = content
                    else:
                        question['answer']['content'] += f"\n{content}"
                        question['content'] += f"\n{content}"
        question['isOppositeAnswered'] = False

    user_tmp = UserTmp.objects.filter(data_id=coach_task.id, type=UserTmpEnum.coach_tasks,
                                      user=coach_task.public_attr.user).first()
    if user_tmp:
        user_tmp.content = coach_task_data
        user_tmp.save()
    else:
        UserTmp.objects.create(data_id=coach_task.id, type=UserTmpEnum.coach_tasks, user=coach_task.public_attr.user,
                               content=coach_task_data)


def check_elements_exist(list1, list2):
    set1 = set(list1)
    set2 = set(list2)
    return set1.issubset(set2)


def get_stakeholder_interview(coach_users, coachee_users, stakeholder_users, project_id, start_date, end_date):
    """
    获取项目下利益相关者访谈列表
    可选参数：教练、被教练者、利益相关者，开始时间，结束时间
    :param coach_users: 教练用户列表
    :param coachee_users: 被教练者用户列表
    :param stakeholder_users: 利益相关者用户列表
    :param project_id: 项目id
    :param start_date: 开始时间 type: date
    :param end_date: 结束时间 type: date
    :return: 利益相关者访谈列表
    """
    queryset = StakeholderInterview.objects.filter(
        interview__type=ProjectInterviewTypeEnum.stakeholder_interview.value,
        interview__place_category=ProjectInterviewPlaceCategoryEnum.online_one_to_one.value,
        interview__public_attr__project_id=project_id,
        stakeholder_interview_module__deleted=False,
        interview__deleted=False,
        deleted=False,
    ).exclude(interview__public_attr__status=ATTR_STATUS_INTERVIEW_CANCEL).order_by('interview__public_attr__start_time')

    if start_date:
        queryset = queryset.filter(interview__public_attr__start_time__date__gte=start_date)
    if end_date:
        queryset = queryset.filter(interview__public_attr__end_time__date__lte=end_date)

    if coach_users:
        queryset = queryset.filter(interview__public_attr__user__in=coach_users)

    # 如果有被教练者，则查指定被教练者的利益相关者访谈
    if coachee_users:
        queryset.filter(stakeholder_interview_module__project_member__user__in=coachee_users)

    # 如果有利益相关者，则查指定利益相关者的利益相关者访谈
    if stakeholder_users:
        queryset = queryset.filter(interview__public_attr__target_user__in=stakeholder_users)

    return queryset
