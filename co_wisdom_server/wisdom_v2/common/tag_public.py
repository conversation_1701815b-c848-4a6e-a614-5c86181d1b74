from datetime import datetime
from django.db.models import Case, When, Q, Prefetch

from utils.queryset import multiple_field_distinct
from wisdom_v2.common import project_coach_public
from wisdom_v2.enum.service_content_enum import TagObjectTypeEnum, TagRequirementConfigTypeEnum
from wisdom_v2.models import ProjectCoach, Project, Coach
from wisdom_v2.models_file import ProjectTagGroup
from wisdom_v2.models_file.tag import TagObject, Tag, ProjectTagConfig, TagRequirementConfig
from wisdom_v2.views.constant import TEST_USER_NAME


def get_all_sub_tags(requirement_config_tag, selected_tag_ids, mandatory_tag_ids):
    """
    获取项目标签的配置信息。

    参数:
    requirement_config_tag (list): 要获取子标签的父标签列表。
    selected_tag_ids (list): 已选择的标签ID列表。
    mandatory_tag_ids (list): 必须选择的标签ID列表。

    返回值:
    list: 一个包含当前标签和所有子标签的列表，每个子标签是一个字典， 用于需求配置时的数据回显。
    """
    sub_tags = []
    for item in requirement_config_tag:
        # 初始化子标签信息字典
        sub_tag_item = {
            'id': item.tag_id,  # 标签ID
            'name': item.tag.name,  # 标签名称
            'sub_type': item.sub_type,  # 标签的子类型
            'selected_ids': [],  # 已选择的子标签ID列表
            'is_required': False,  # 是否为必选标签
            'sub': [],  # 子标签列表
        }

        # 获取所有未删除的子标签
        children = Tag.objects.filter(parent_id=item.tag_id, deleted=False).order_by("created_at").all()
        for child in children:
            child_id = str(child.id)
            # 检查子标签是否在必选标签ID列表中
            if child_id in mandatory_tag_ids and not sub_tag_item['is_required']:
                sub_tag_item['is_required'] = True
            # 检查子标签是否在已选择的标签ID列表中
            if child_id in selected_tag_ids:
                sub_tag_item['selected_ids'].append(child_id)

            # 添加子标签信息到子标签列表
            sub_tag_item['sub'].append({
                'id': child_id,  # 子标签ID
                'name': child.name,  # 子标签名称
            })
        # 将子标签信息添加到最终的子标签列表中
        sub_tags.append(sub_tag_item)

    return sub_tags


def build_tag_hierarchy(all_tag_data, selected_tag_ids, mandatory_tag_ids):
    """
    构建标签的层次结构，并标记选中的叶标签。

    参数:
    all_tag_data (list): 一个包含元组的列表，每个元组包含标签ID (UUID)，父级标签ID (UUID 或 None)，标签名称 (str)，和是否是叶标签 (bool) 子标签类型（int）。
    selected_tag_ids (list): 一个包含选中标签ID的列表。
    mandatory_tag_ids (list): 一个包含必须匹配的标签ID的列表。

    返回值:
    list: 一个包含标签层次结构的列表，每个标签是一个字典，包含标签ID，名称，子标签列表，以及是否选中的标记（仅限于叶标签）。
    """
    tag_dict = {}

    # 创建一个字典，其中键是标签ID，值是标签的详细信息
    for tag_id, parent_id, tag_name, is_leaf_node, sub_type in all_tag_data:
        tag_id_str = str(tag_id)
        parent_id_str = str(parent_id) if parent_id else None
        tag_dict[tag_id_str] = {
            'id': tag_id_str,
            'parent_id': parent_id_str,
            'name': tag_name,
            'is_leaf_node': is_leaf_node,
            'selected_ids': [],
            'sub_type': sub_type,
            'sub': []
        }

    # 构建树状结构
    result = []
    for tag_id, tag in tag_dict.items():
        parent_id = tag['parent_id']
        if parent_id and parent_id in tag_dict:
            tag_dict[parent_id]['sub'].append(tag)
            # 如果子标签在mandatory_tag_ids中，标记父标签
            if tag_id in mandatory_tag_ids:
                tag_dict[parent_id]['is_required'] = True
            if tag_id in selected_tag_ids:
                tag_dict[parent_id]['selected_ids'].append(tag_id)
        else:
            result.append(tag)

    return result


def get_company_tag_info(company_id):
    """
    获取企业标签
    参数：
    company_id（int）： 企业唯一标识

    返回值:
    dict 企业的基础标签信息
    """

    company_tag = TagObject.objects.filter(
        deleted=False, object_type=TagObjectTypeEnum.company.value, object_id=company_id,
    ).values_list('tag_id', 'tag__name').order_by('created_at')

    selected_tag_ids = []
    selected_tag_names = []
    for tag_id, tag_name in company_tag:
        selected_tag_ids.append(str(tag_id))
        selected_tag_names.append(tag_name)

    data = {
        'selected_tag_ids': selected_tag_ids,
        'selected_tag_names': selected_tag_names
    }
    return data


def get_project_tag(project_tag_group_id, is_matching_tag=True):
    # 获取项目附带的企业标签
    project_tag_group = ProjectTagGroup.objects.filter(id=project_tag_group_id, deleted=False).first()
    company_tag_data = get_company_tag_info(project_tag_group.project.company_id)
    company_selected_tag_ids = company_tag_data.get('selected_tag_ids', [])
    company_selected_tag_names = company_tag_data.get('selected_tag_names', [])

    # 获取项目标签
    project_tag_group_data = get_project_tag_group_info(project_tag_group_id, is_matching_tag)
    selected_tag_ids = project_tag_group_data.get('selected_tag_ids', [])
    selected_tag_names = project_tag_group_data.get('selected_tag_names', [])
    mandatory_tag_ids = project_tag_group_data.get('mandatory_tag_ids', [])

    # 合并成一个标签列表
    all_selected_tag_ids = company_selected_tag_ids + selected_tag_ids
    all_selected_tag_names = company_selected_tag_names + selected_tag_names

    return all_selected_tag_names, all_selected_tag_ids, mandatory_tag_ids


def get_project_tag_list(project_tag_group_id):
    """
    获取项目标签列表。

    参数:
    project_tag_group_id (UUID): 项目标签组ID

    返回值:
    list: 包含标签名称路径和是否必选的字典列表
    """
    # 获取项目标签组中选中的标签ID和必选标签ID
    all_selected_tag_names, all_selected_tag_ids, mandatory_tag_ids = get_project_tag(
        project_tag_group_id, is_matching_tag=False)

    # 获取tag对应父级标签
    all_tag = Tag.objects.filter(id__in=all_selected_tag_ids, deleted=False).values_list('id', 'parent__name')
    all_tag_dict = {str(tag_id): parent_name for tag_id, parent_name in all_tag}

    # 构建项目标签列表
    raw_result = {}
    for tag_name, tag_id in zip(all_selected_tag_names, all_selected_tag_ids):
        # 是否必须选
        is_require = tag_id in mandatory_tag_ids
        # 如果有父级标签就拼接名称
        parent_name = all_tag_dict.get(tag_id)
        if raw_result.get(parent_name):
            raw_result[parent_name]['name'] += f'，{tag_name}'
        else:
            raw_result[parent_name] = {'name': f"{parent_name} - {tag_name}", 'is_require': is_require}

    # 转成列表返回
    result = [item for item in raw_result.values()]
    return result


def get_project_tag_group_info(project_tag_group_id, is_matching_tag=True):
    """
    获取项目标签组信息，包括选中的标签ID和必选的标签ID，必选的标签name。

    参数:
    project_tag_group_id (UUID): 项目标签组ID
    is_matching_tag (BOOL): 是否只需要数据匹配标签

    返回值:
    dict: 包含选中标签ID列表和必选标签ID列表，必选的标签name的字典
    """
    # 获取项目选中的所有标签

    project_config_tag_query = ProjectTagConfig.objects.filter(
        deleted=False, project_tag_group_id=project_tag_group_id
    )

    # 是否只获取匹配标签
    if is_matching_tag:
        # 获取项目需求需要进行匹配的父tag标签
        parent_matching_tag = get_requirement_config_matching_tag(
            TagRequirementConfigTypeEnum.project_requirements.value)
        project_config_tag_query = project_config_tag_query.filter(tag_object__tag__parent_id__in=parent_matching_tag)

    project_config_tag = project_config_tag_query.values_list('tag_object__tag_id', 'is_mandatory', 'tag_object__tag__name').order_by('created_at')

    selected_tag_ids = []
    mandatory_tag_ids = []
    selected_tag_names = []
    for tag_id, is_mandatory, tag_name in project_config_tag:
        selected_tag_ids.append(str(tag_id))
        selected_tag_names.append(tag_name)
        if is_mandatory:
            mandatory_tag_ids.append(str(tag_id))

    data = {
        'selected_tag_ids': selected_tag_ids,
        'selected_tag_names': selected_tag_names,
        'mandatory_tag_ids': mandatory_tag_ids
    }
    return data


def delete_specific_tag_configs_and_objects(tag_config_ids):
    """
    逻辑删除特定标签配置和标签对象。

    参数：
    tag_config_ids (list): 需要删除的标签配置ID列表。
    """
    if not tag_config_ids:
        return

    project_tag_config = ProjectTagConfig.objects.filter(
        id__in=tag_config_ids, deleted=False
    )
    all_tag_object_ids = project_tag_config.values_list('tag_object_id', flat=True)
    # 逻辑删除标签对象
    TagObject.objects.filter(id__in=all_tag_object_ids).update(deleted=True)
    # 逻辑删除标签配置
    project_tag_config.update(deleted=True)


def get_coach_content(project_tag_group_id):
    """
    获取项目标签组中教练的内容。

    参数:
    project_tag_group_id (UUID): 项目标签组ID。

    返回值:
    dict: 包含教练内容的字典，包括生成时间、状态、标题、内容和教练ID列表。

    功能描述:
    该函数根据项目标签组ID，获取与项目相关的所有标签，包括选中的标签和必选的标签。
    然后，根据这些标签查询对应的教练信息，包括教练的背景匹配度和教练经验匹配度。
    最后，将这些信息构建成一个字典返回，用于展示教练与项目标签的匹配情况。
    """

    # 获取项目标签组中的选中标签和必选标签
    all_selected_tag_names, all_selected_tag_ids, mandatory_tag_ids = get_project_tag(project_tag_group_id)

    # 根据必选标签或选中标签获取项目需求匹配的教练标签
    # 分开查询教练背景和教练经验标签，为后续计算匹配度作准备
    # 如果有必选标签，教练必须满足必选标签的要求匹配
    # 如果没有必选标签，教练需要满足项目已选标签
    if mandatory_tag_ids:
        # 查询教练背景是否有标签
        tag_object_coach = TagObject.objects.filter(
            tag_id__in=mandatory_tag_ids, deleted=False, object_type=TagObjectTypeEnum.coach.value,
            source__isnull=True
        ).distinct()
        # 查询教练经验是否有标签
        tag_object_project_require_coach = TagObject.objects.filter(
            tag_id__in=mandatory_tag_ids, deleted=False, object_type=TagObjectTypeEnum.coach.value,
            source__object_type__in=[TagObjectTypeEnum.project.value, TagObjectTypeEnum.company.value]
        ).distinct()
    else:
        # 查询教练背景是否有标签
        tag_object_coach = TagObject.objects.filter(
            tag_id__in=all_selected_tag_ids, deleted=False, object_type=TagObjectTypeEnum.coach.value,
            source__isnull=True
        ).distinct()
        # 查询教练经验是否有标签
        tag_object_project_require_coach = TagObject.objects.filter(
            tag_id__in=all_selected_tag_ids, deleted=False, object_type=TagObjectTypeEnum.coach.value,
            source__object_type__in=[TagObjectTypeEnum.project.value, TagObjectTypeEnum.company.value]
        ).distinct()

    # 按选中的标签ID顺序排序标签对象，排序为了保证title和coach_content数据顺序一致
    order = Case(*[When(id=_id, then=pos) for pos, _id in enumerate(all_selected_tag_ids)])

    # 查询到需要进行匹配的父tag标签
    selected_tag = Tag.objects.filter(id__in=all_selected_tag_ids, deleted=False).order_by(order)

    # 需要的是父标签，按照父标签去重复
    selected_tag = multiple_field_distinct(selected_tag, ['parent_id'])
    selected_tag = [[str(item.parent_id), str(item.parent.name)] for item in selected_tag]

    # 开始准备数据
    # 教练推荐信息的表头，title列表。
    titles = ['教练名称', '背景匹配度', '教练经验匹配度', '综合匹配度']
    # 每个父标签下选中的子标签id列表, 方便后面计算适配度
    parent = {}
    # 获取每个父标签下的子标签信息
    for parent_id, parent__name in selected_tag:
        sub_tag = Tag.objects.filter(
            parent_id=parent_id, deleted=False, id__in=all_selected_tag_ids).values_list('name', 'id')
        sub_tag_names = []
        sub_tag_ids = []
        for sub_tag_name_item, sub_tag_id_item in sub_tag:
            sub_tag_names.append(sub_tag_name_item)
            sub_tag_ids.append(str(sub_tag_id_item))

        # 获取父标签下选中的子标id, 存id是为了好对比是否匹配，不能直接存储数量
        parent[parent_id] = sub_tag_ids

        # 将父标签+选中的子标签的名称添加到titles列表中
        titles.append(
            f"{parent__name}({'，'.join(sub_tag_names)})" if sub_tag_names else parent__name)

    # 获取所有教练的ID
    coach_ids = list(tag_object_coach.values_list('object_id', flat=True))
    project_require_coach_ids = list(tag_object_project_require_coach.values_list('object_id', flat=True))
    # 去重后的教练id列表
    all_coach_ids = list(set(coach_ids + project_require_coach_ids))

    # 获取需要排除的测试教练id列表，生成新可用教练id列表
    test_coach_ids = list(Coach.objects.filter(user__true_name__icontains=TEST_USER_NAME, deleted=False).values_list('id', flat=True))
    str_test_coach_ids = [str(item) for item in test_coach_ids]

    filtered_coach_ids = list(set(all_coach_ids) - set(str_test_coach_ids))

    # 查询所有标签对应的教练数据
    tag_objects = TagObject.objects.filter(
        tag_id__in=all_selected_tag_ids,
        object_type=TagObjectTypeEnum.coach,
        object_id__in=filtered_coach_ids
    ).select_related('tag').distinct()

    # 整理标签数据，按教练ID分组
    coach_tag_mapping = {}
    for tag_object in tag_objects:
        coach_id = tag_object.object_id
        if coach_id not in coach_tag_mapping:
            coach_tag_mapping[coach_id] = {
                'background_tags': [],
                'experience_tags': [],
                'all_tags': {},
            }
        # 假设source为null代表背景标签，否则为经验标签
        if tag_object.source is None:
            coach_tag_mapping[coach_id]['background_tags'].append(tag_object.tag_id)
        else:
            coach_tag_mapping[coach_id]['experience_tags'].append(tag_object.tag_id)

        if coach_tag_mapping[coach_id]['all_tags'].get(str(tag_object.tag.parent_id)):
            coach_tag_mapping[coach_id]['all_tags'][str(tag_object.tag.parent_id)].append(str(tag_object.tag_id))
        else:
            coach_tag_mapping[coach_id]['all_tags'][str(tag_object.tag.parent_id)] = [str(tag_object.tag_id)]

    # 获取所有相关的教练
    coach_ids = list(coach_tag_mapping.keys())
    coaches = Coach.objects.filter(id__in=coach_ids, deleted=False)
    # 构建教练内容
    sub_tag_content = []
    # 当前项目匹配的总标签数量，用来计算背景匹配度，教练经验匹配度
    all_sub_tag_count = len(all_selected_tag_ids)
    # 处理每个教练的匹配度
    for coach in coaches:
        coach_tags = coach_tag_mapping[str(coach.id)]
        coach_name = coach.user.cover_name
        coach_id = coach.id

        # 计算背景匹配度和经验匹配度
        all_sub_resume_count = len(coach_tags['background_tags'])
        all_sub_project_require_resume_count = len(coach_tags['experience_tags'])

        # 满足匹配或部分匹配的的父标签名称。后续用来做教练排除
        matching_parent_list = []
        # 提前计算每个标签的匹配描述，特殊的标签（如对教练的性别偏好）满足任意一个子标签就可，
        matching_degree_list = []
        for parent_id, parent_name in selected_tag:
            # 读取当前父标签下啊项目选中的tag_id列表
            sub_tag_ids = parent[parent_id]

            # 读取当前父标签下教练已有的tag_id列表
            coach_tag = coach_tags['all_tags'].get(parent_id, [])

            # 比较是否完全相等
            if set(sub_tag_ids) == set(coach_tag):
                matching_degree = '匹配'
                matching_parent_list.append(parent_name)  # 匹配成功时 将父标签名称加入matching_parent_list
            else:
                # 检查是否有元素重复
                if bool(set(sub_tag_ids) & set(coach_tag)):
                    matching_degree = '部分匹配'
                    if parent_name == '对教练的性别偏好':
                        matching_degree = '匹配'
                        all_sub_resume_count += 1  # 该标签匹配任意一个=匹配全部，影响教练匹配度计算

                    matching_parent_list.append(parent_name)  # 部分匹配成功时 将父标签名称加入matching_parent_list
                else:
                    matching_degree = '不匹配'

            matching_degree_list.append(matching_degree)
        # 如果只是性别匹配，不计入教练匹配数据
        if len(matching_parent_list) == 1 and matching_parent_list[0] == '对教练的性别偏好':
            continue

        # 计算背景匹配度
        background_matching_degree = int(all_sub_resume_count / all_sub_tag_count * 100) if all_sub_tag_count else 0
        # 计算经验匹配度
        coach_experience_matching_degree = int(all_sub_project_require_resume_count / all_sub_tag_count * 100) if all_sub_tag_count else 0

        # 计算综合匹配度用于排序和展示，优先展示匹配度高的教练
        total_matching_degree = int((background_matching_degree + coach_experience_matching_degree) / 2)

        # 准备基础数据结构，为了保证同层级coach_id列表和教练标签信息sub_content_data的教练顺序一致，排序后，再将coach_id提取出去用于教练唯一标识
        sub_tag_content.append([
            coach_id,
            coach_name,
            background_matching_degree,  # 背景匹配度
            coach_experience_matching_degree,  # 经验匹配度
            total_matching_degree,  # 综合匹配度
            *matching_degree_list  # 具体的标签匹配描述
        ])

    # 根据综合匹配度从大到小排序
    sub_tag_content.sort(key=lambda x: x[4], reverse=True)

    # 提取排序后的教练id列表，确保顺序一致
    coach_ids = []
    for sublist in sub_tag_content:
        if sublist:  # 确保子列表不为空
            coach_id = sublist.pop(0)  # 提取并移除子列表的第一个元素（教练id标识）
            sublist[1] = f"{sublist[1]}%"  # '背景匹配度'
            sublist[2] = f"{sublist[2]}%"  # '教练经验匹配度'
            sublist[3] = f"{sublist[3]}%"  # '综合匹配度'
            coach_ids.append(coach_id)

    data = {
        "tag_created_at": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
        "status": 1,
        "titles": titles,
        "content": sub_tag_content,
        "coach_ids": coach_ids,
    }
    return data


def update_project_coach_tag(project_id):
    """
    更新项目教练的标签。

    参数:
    project_id (UUID): 项目ID。

    功能描述:
    该函数首先根据项目类型获取与项目匹配的教练ID列表。
    然后根据这些教练ID，更新每个教练的标签信息，包括项目基础标签、项目全部标签以及项目对应的企业标签。
    最终将这些标签更新到对应的教练对象上。
    """

    project = Project.objects.filter(id=project_id, deleted=False).first()

    # 项目不存在，无需更新标签
    if not project:
        return

    project_coach_data = project_coach_public.get_project_coach_and_coachee_info(project_id)
    project_coach_user_ids = project_coach_data.get('all_coach_ids')
    project_coach_ids = Coach.objects.filter(user_id__in=project_coach_user_ids, deleted=False).values_list('id', flat=True)

    # 获取项目集体辅导教练ID列表
    group_project_coach_ids = list(ProjectCoach.objects.filter(
        project_group_coach_id__isnull=False, project_id=project_id, deleted=False
    ).exclude(coach__user_id__in=project_coach_ids).values_list('coach_id', flat=True).distinct())

    # 如果没有教练，直接退出
    if not group_project_coach_ids and not project_coach_ids:
        return

    # 获取到可以给教练同步的教练列表
    sync_tag = get_requirement_config_sync_tag(
        TagRequirementConfigTypeEnum.project_requirements.value)

    # 获取项目基础需要标签,(只匹配需要数据同步的)
    project_tag = ProjectTagConfig.objects.filter(
        tag_object__tag__parent_id__in=sync_tag,
        project_tag_group__project_id=project_id,
        project_tag_group__project_member_id__isnull=True, deleted=False).values_list(
        'tag_object_id', flat=True)

    # 获取项目的全部标签对象
    project_tag_object = TagObject.objects.filter(id__in=project_tag, deleted=False)

    # 获取项目对应的企业标签对象
    company_tag_object = TagObject.objects.filter(
        object_type=TagObjectTypeEnum.company.value, deleted=False, object_id=project.company_id)

    # 合成完整的项目标签对象
    project_tag_object = project_tag_object | company_tag_object

    # 更新集体辅导教练的标签
    for coach_id in group_project_coach_ids:
        update_coach_tag(coach_id, project_tag_object)

    # 更新项目教练的标签
    for coach_id in project_coach_ids:
        member_tag_object = get_project_coach_to_member_tag(project, coach_id, sync_tag)
        update_coach_tag(coach_id, project_tag_object, member_tag_object=member_tag_object)

    return


def update_coach_tag(coach_id, project_tag_object, member_tag_object=None):
    """
    更新教练的标签。

    参数:
    coach_id (UUID): 教练ID。
    project_tag_object (QuerySet): 项目标签对象的查询集。
    member_tag_object (QuerySet): 成员标签对象的查询集，可选。

    功能描述:
    该函数首先逻辑删除教练现有的项目来源标签，然后将项目标签和成员标签（如果存在）合并去重，
    并将新的标签批量创建并更新到教练对象上。
    """

    # 逻辑删除教练现有的项目来源标签
    TagObject.objects.filter(
        object_id=coach_id, object_type=TagObjectTypeEnum.coach.value,
        source__object_type__in=[TagObjectTypeEnum.project.value, TagObjectTypeEnum.company.value], deleted=False
    ).update(deleted=True)

    # 合并项目标签和成员标签
    if member_tag_object:
        combined_queryset = member_tag_object | project_tag_object
    else:
        combined_queryset = project_tag_object

    # 去重查询集对象
    distinct_queryset_object = multiple_field_distinct(combined_queryset, ['tag_id'])
    all_project_tag_data = distinct_queryset_object.values_list('tag_id', 'id').distinct()
    # 创建新的标签对象
    add_tag_object = []
    for tag_id, tag_object_id in all_project_tag_data:
        add_tag_object.append(
            TagObject(
                tag_id=tag_id,
                object_id=coach_id,
                object_type=TagObjectTypeEnum.coach.value,
                source_id=tag_object_id)
        )
    # 批量创建标签对象
    TagObject.objects.bulk_create(add_tag_object)


def get_project_coach_to_member_tag(project, coach_id, sync_tag):
    """
    获取项目教练匹配的用户标签对象。

    参数:
    project (Project): 项目实例对象。
    coach_id (int): 教练唯一标识。
    sync_tag (list): 项目需要数据同步的标签id列表。

    返回值:
    QuerySet: 匹配的用户标签对象集合，如果没有匹配的用户则返回空列表。

    功能描述:
    该函数根据教练是否和用户有辅导，是否有一对一匹配获取需要辅导的用户ID列表，并通过这些用户ID获取与项目教练匹配的标签对象。
    最后，通过这些用户ID从 `ProjectTagConfig` 表中获取对应的标签对象，并返回这些标签对象。
    """

    # 获取项目辅导对象的用户ID列表
    project_coach_data = project_coach_public.get_project_coach_and_coachee_info(project.id, [coach_id])
    coachee_user_ids = project_coach_data.get('all_coachee_ids')

    if coachee_user_ids:
        # 获取项目成员对应的标签对象ID列表, 只获取需要同步的标签
        project_member_tag = ProjectTagConfig.objects.filter(
            project_tag_group__project_id=project.id, project_tag_group__project_member__user_id__in=coachee_user_ids,
            deleted=False, tag_object__tag__parent_id__in=sync_tag).values_list(
            'tag_object_id', flat=True)

        # 获取未删除的标签对象
        member_tag_object = TagObject.objects.filter(id__in=project_member_tag, deleted=False)

        return member_tag_object

    # 如果没有匹配的用户ID，返回空列表
    return []


def get_requirement_config_sync_tag(config_type):
    """
    获取指定类型的需求同步标签

    参数:
    config_type (int) : TagRequirementConfigTypeEnum

    """
    project_tag_config = TagRequirementConfig.objects.filter(
        deleted=False, config_type=config_type, is_sync=True).values_list('tag_id', flat=True)
    return project_tag_config


def get_requirement_config_matching_tag(config_type):
    """
    获取指定类型的数据匹配标签

    参数:
    config_type (int) : TagRequirementConfigTypeEnum

    """

    project_tag_config = TagRequirementConfig.objects.filter(
        deleted=False, config_type=config_type, is_data_matching=True).values_list('tag_id', flat=True)
    return project_tag_config


def tag_name_update_object_tag(object_id, object_type, tag_name, parent_tag_name):
    """
    通过名称更新对象的标签数据标签名称。

    参数:
    object_id (str): 对象标识。
    object_type (UUID): 对象类型。
    tag_name (str): 新的标签名称。
    parent_tag_name (str): 父标签名称。

    功能描述:
    该函数根据给定的对象ID，对象类型和父标签名称，更新或创建新的标签。
    如果提供了新的标签名称，则首先检查是否存在匹配的标签，如果存在则更新标签对象；
    否则，创建一个新的标签对象。
    如果没有提供新的标签名称，则逻辑删除与父标签名称匹配的所有标签对象。

    """

    if not tag_name:
        return

    # 查找匹配的新标签
    tag = Tag.objects.filter(
        name=tag_name, deleted=False, parent__name=parent_tag_name).first()

    # 名称未找到标签，直接跳过即可（没有匹配的标签=数据无需匹配），不用抛出错误
    if not tag:
        return
    # 创建标签信息
    TagObject.objects.create(
        tag=tag, object_type=object_type, object_id=object_id)
