import io

from ast import literal_eval
from django.db.models import When, Case
from docx import Document
from docx.enum.text import WD_PARAGRAPH_ALIGNMENT
from docx.oxml.ns import qn
from docx.shared import Pt

from wisdom_v2.enum.project_interview_enum import DataType
from wisdom_v2.enum.service_content_enum import InterviewRecordTemplateQuestionTypeEnum, \
    InterviewRecordTemplateQuestionAnswerTypeEnum, PersonalReportTypeEnum
from wisdom_v2.enum.user_enum import UserTmpEnum
from wisdom_v2.models import Coach, CoachTask, TotalTemplateReport, InterviewRecordTemplateAnswer, UserTmp, \
    PersonalReport
from wisdom_v2.views import constant


def update_coach_task_coach_user_id(coach_task, coach_id):
    """
    根据传入的教练任务(coach_task)和新的教练ID(coach_id)，更新教练任务关联的用户ID。
    如果教练ID有效，且与当前任务关联的用户ID不同，则更新公共属性(user_id)为新教练的用户ID。
    若教练ID为空，则将公共属性的用户ID设为None，表示解除与教练的关联。

    参数:
        coach_task: 教练任务对象，需包含public_attr属性以访问用户ID信息。
        coach_id: 新的教练ID，用于替换现有关联。如果为None，则解除与任何教练的关联。

    返回:
        无直接返回值。通过副作用修改数据库中的CoachTask关联用户ID。
    """

    # 检查教练任务是否存在，若不存在则直接返回
    if not coach_task:
        return

    # 如果提供了新的教练ID
    if coach_id:
        # 查询数据库中对应的教练是否存在且未被删除
        coach_user = Coach.objects.filter(id=coach_id, deleted=False).first()

        # 如果找到了有效的教练，并且该教练与当前任务关联的用户不同
        if coach_user and coach_task.public_attr.user_id != coach_user.user_id:
            # 获取教练任务的公共属性并更新其用户ID
            public_attr = coach_task.public_attr
            public_attr.user_id = coach_user.user_id
            public_attr.save()

    else:
        # 若教练ID为空，将任务的公共属性用户ID设为None，解除与教练的关联
        public_attr = coach_task.public_attr
        public_attr.user_id = None
        public_attr.save()


def stakeholder_task_data_to_word(coach_task_id):
    """
    将指定教练任务的利益相关者任务数据生成Word文档。

    本函数接收一个教练任务ID，获取该任务的问答数据并生成一个Word文档。具体步骤如下：
    1. 根据教练任务ID从数据库中查找相应的教练任务对象，并确保其模板包含问题顺序且未被删除。
    2. 如果未找到符合条件的教练任务对象，返回 (None, None)。
    3. 尝试从临时用户数据表中获取教练任务的问答数据。如果没有找到临时数据，则从正式数据表中获取。
    4. 如果未能获取到任何问答数据，返回 (None, None)。
    5. 获取教练任务所属项目的名称，并创建一个新的Word文档。
    6. 遍历问答数据，将每个问题的标题和答案添加到Word文档中，标题加粗并使用宋体14号字，答案使用宋体12号字并左对齐。
    7. 使用 `io.BytesIO` 在内存中创建文件，并返回文件对象和文件名。

    :param coach_task_id: 教练任务ID
    :return: 包含Word文档文件对象和文件名的元组，如果未能生成文档则返回 (None, None)
    """

    # 根据教练任务ID查找教练任务对象，确保模板包含问题顺序且未被删除
    coach_task = CoachTask.objects.filter(
        id=coach_task_id, template__questions_order__isnull=False, deleted=False
    ).first()

    # 如果未找到教练任务对象，返回 (None, None)
    if not coach_task:
        return None, None

    # 尝试从临时用户数据表中获取教练任务的问答数据
    coach_task_data = user_tmp_get_stakeholder_task_word_data(coach_task)

    # 如果未找到临时数据，则从正式数据表中获取
    if not coach_task_data:
        coach_task_data = get_stakeholder_task_word_data(coach_task)

        # 如果未能获取到任何问答数据，返回 (None, None)
        if not coach_task_data:
            return None, None

    # 获取教练任务所属项目的名称
    project_name = coach_task.project_bundle.project.name

    # 创建一个新的Word文档
    doc = Document()

    # 遍历问答数据，将每个问题的标题和答案添加到Word文档中
    for idx, item in enumerate(coach_task_data, 1):
        # 添加 question_title 并加粗
        question_title = item.get('question_title')
        if question_title:
            question_title_paragraph = doc.add_paragraph()
            question_title_run = question_title_paragraph.add_run(f"{idx}. {question_title}")
            question_title_run.bold = True
            question_title_run.font.size = Pt(14)
            question_title_run.font.name = 'SimSun'  # 使用宋体
            r = question_title_run._element
            r.rPr.rFonts.set(qn('w:eastAsia'), '宋体')

        # 添加 question_title_answer
        question_title_answer = item.get('answer')
        if question_title_answer:
            question_title_answer_paragraph = doc.add_paragraph()
            question_title_answer_run = question_title_answer_paragraph.add_run(question_title_answer)
            question_title_answer_run.font.size = Pt(12)
            question_title_answer_run.font.name = 'SimSun'  # 使用宋体
            r = question_title_answer_run._element
            r.rPr.rFonts.set(qn('w:eastAsia'), '宋体')
            question_title_answer_paragraph.paragraph_format.alignment = WD_PARAGRAPH_ALIGNMENT.LEFT
            question_title_answer_paragraph.paragraph_format.left_indent = Pt(28)

    # 使用 io.BytesIO 在内存中创建文件
    byte_io = io.BytesIO()
    doc.save(byte_io)
    byte_io.seek(0)  # 将文件指针移到开头

    # 生成文件名，格式为 "项目名称_目标用户名称_利益相关者访谈总结报告.docx"
    file_base_name = f"{project_name}_{coach_task.public_attr.target_user.cover_name}_利益相关者访谈总结报告.docx"

    # 返回文件对象和文件名
    return byte_io, file_base_name


def get_stakeholder_task_word_data(coach_task):
    """
    获取利益相关者任务的问答数据。

    本函数接收一个教练任务对象，提取并返回与该任务相关的问题及其答案。具体步骤如下：
    1. 检查传入的教练任务对象是否为空。如果为空，则直接返回。
    2. 获取教练任务对象的模板（total_template），该模板包含了问题的顺序。
    3. 根据模板中的问题顺序创建一个排序条件（order），用于对问题进行排序。
    4. 查询总模板报告（total_template_report），过滤出问题类型为填空（blank）且答案类型为普通文本（normal_text）的记录，并按问题顺序排序。
    5. 遍历查询结果，获取每个问题的标题和对应的答案。如果找到答案，则将问题标题和答案添加到结果列表中。
    6. 返回包含问题标题和答案的结果列表。如果没有找到任何数据，则返回空列表。

    :param coach_task: 教练任务对象，包含任务的相关信息和模板
    :return: 包含问题标题和答案的列表，如果没有找到任何数据则返回空列表
    """

    # 如果coach_task为空，直接返回
    if not coach_task:
        return []

    coach_task_data = []

    # 获取教练任务的模板
    total_template = coach_task.template

    # 获取问题顺序，并按顺序排列
    order = Case(*[When(pk=order, then=pos) for pos, order in enumerate(literal_eval(total_template.questions_order))])

    # 获取总模板报告，按问题顺序排序
    total_template_report = TotalTemplateReport.objects.filter(
        total_template_id=total_template.id,
        related_question__coach_question__type=InterviewRecordTemplateQuestionTypeEnum.blank.value,
        related_question__coach_question__answer_type=InterviewRecordTemplateQuestionAnswerTypeEnum.normal_text.value,
    ).order_by(order)

    # 遍历总模板报告，获取每个问题的标题和答案
    for report in total_template_report:
        question_id = report.related_question.coach_question_id
        question_title = report.related_question.coach_question.title

        # 查找对应问题的答案对象
        answer_obj = InterviewRecordTemplateAnswer.objects.filter(
            public_attr=coach_task.public_attr, question_id=question_id).first()

        # 如果找到答案对象，提取答案并添加到结果列表中
        if answer_obj:
            answer = answer_obj.answer
            coach_task_data.append({
                'question_title': question_title,
                'answer': answer
            })

    return coach_task_data


def user_tmp_get_stakeholder_task_word_data(coach_task):
    """
    获取利益相关者任务的临时问答数据。

    本函数用于从临时用户数据表中提取指定教练任务的问答数据，并将其转换为包含问题标题和答案的格式。具体步骤如下：
    1. 根据教练任务ID、类型、用户ID及内容是否为空的条件，从UserTmp表中查找相关记录。
    2. 如果没有找到对应的临时数据，则返回None。
    3. 如果找到对应的临时数据，将内容提取出来。
    4. 遍历提取出的数据，将每个问题的标题和答案进行重新组织，存储在结果列表中。
    5. 返回包含问题标题和答案的结果列表。

    :param coach_task: 教练任务对象，包含任务的相关信息
    :return: 包含问题标题和答案的列表，如果没有找到对应的临时数据则返回None
    """

    # 查找符合条件的临时用户数据
    user_tmp = UserTmp.objects.filter(
        data_id=coach_task.id,
        type=UserTmpEnum.coach_tasks,
        user_id=coach_task.public_attr.user_id,
        content__isnull=False
    ).first()

    # 如果没有找到对应的临时数据，返回None
    if not user_tmp:
        return None

    # 提取临时数据的内容
    coach_task_data = user_tmp.content

    now_coach_task_data = []

    # 遍历临时数据内容，将问题标题和答案重新组织
    for item in coach_task_data:
        title = item.get('title')  # 获取问题标题
        answer_content = item.get('answer', {}).get('content')  # 获取答案内容

        # 将问题标题和答案添加到结果列表中
        now_coach_task_data.append({
            'question_title': title,
            'answer': answer_content
        })

    # 返回结果列表
    return now_coach_task_data


def check_coach_task_is_stakeholder_interview_summary(coach_task_id):
    """
    检查指定的教练任务是否是利益相关者访谈总结任务。

    本函数接收一个教练任务ID，通过以下步骤检查任务是否符合利益相关者访谈总结任务的条件：
    1. 根据教练任务ID从数据库中查找对应的教练任务对象，并确保其未被删除。
    2. 如果未找到符合条件的教练任务对象，返回None。
    3. 检查教练任务的模板标题中是否包含指定的排除字符串。
    4. 如果模板标题包含指定的排除字符串，返回True。
    5. 如果模板标题不包含指定的排除字符串，返回None。

    :param coach_task_id: 教练任务ID
    :return: 如果教练任务是利益相关者访谈总结任务，则返回True；否则返回None
    """

    # 根据教练任务ID查找教练任务对象，并确保其未被删除
    coach_task = CoachTask.objects.filter(id=coach_task_id, deleted=False).first()

    # 如果未找到教练任务对象，返回None
    if not coach_task:
        return

    # 检查教练任务的模板标题中是否包含指定的排除字符串
    if constant.EXCLUDE_COACH_TASK_TEMPLATE_NAME in coach_task.template.title:
        return True

    # 如果模板标题不包含指定的排除字符串，返回None
    return


def get_now_coach_tasks_data(coachee_user_id, coach_tasks_data):
    """
    获取当前教练任务的数据，并根据任务类型生成相关报告。

    本函数接收受训用户ID和教练任务数据列表，通过以下步骤处理教练任务数据：
    1. 遍历教练任务数据列表，获取每个任务的详细信息ID。
    2. 调用 `check_coach_task_is_stakeholder_interview_summary` 检查任务是否是利益相关者访谈总结任务。
    3. 如果是利益相关者访谈总结任务，创建或获取个人报告（notes_report和summary_report）。
    4. 将个人报告的相关信息添加到任务数据列表中。
    5. 返回处理后的教练任务数据列表。

    :param coachee_user_id: 受训用户ID
    :param coach_tasks_data: 教练任务数据列表
    :return: 处理后的教练任务数据列表
    """

    now_coach_tasks_data = []

    # 遍历教练任务数据列表
    for coach_tasks_data_item in coach_tasks_data:
        # 获取任务的详细信息ID
        coach_tasks_data_item_id = coach_tasks_data_item.get('details', {}).get('id')
        now_coach_tasks_data.append(coach_tasks_data_item)

        # 检查任务是否是利益相关者访谈总结任务
        is_summary_report_status = check_coach_task_is_stakeholder_interview_summary(coach_tasks_data_item_id)

        if is_summary_report_status:
            # 创建或获取个人报告（notes_report）
            notes_report, is_create = PersonalReport.objects.get_or_create(
                object_id=coach_tasks_data_item_id, user_id=coachee_user_id, deleted=False,
                type=PersonalReportTypeEnum.notes_report.value, project_id=coach_tasks_data_item.get('project_id'),
                name=PersonalReportTypeEnum.get_display(PersonalReportTypeEnum.notes_report.value)
            )

            # 创建或获取个人报告（summary_report）
            summary_report, is_create = PersonalReport.objects.get_or_create(
                object_id=coach_tasks_data_item_id, user_id=coachee_user_id, deleted=False,
                type=PersonalReportTypeEnum.summary_report.value, project_id=coach_tasks_data_item.get('project_id'),
                name=PersonalReportTypeEnum.get_display(PersonalReportTypeEnum.summary_report.value)
            )

            # 将 notes_report 的信息添加到任务数据列表中
            now_coach_tasks_data.append({
                'data_type': DataType.personal_report.value,
                'image_url': coach_tasks_data_item.get('image_url'),
                'describe': '客户将会查看此文档',
                'title': notes_report.name,
                'details': {
                    "file_path": notes_report.pdf_url if notes_report else None,
                    'type': PersonalReportTypeEnum.notes_report.value,
                    "id": notes_report.id,
                }
            })

            # 将 summary_report 的信息添加到任务数据列表中
            now_coach_tasks_data.append({
                'data_type': DataType.personal_report.value,
                'image_url': coach_tasks_data_item.get('image_url'),
                'describe': '企业方将会查看此文档',
                'title': summary_report.name,
                'details': {
                    "file_path": summary_report.pdf_url if summary_report else None,
                    'type': PersonalReportTypeEnum.summary_report.value,
                    "id": summary_report.id,
                }
            })

    # 返回处理后的教练任务数据列表
    return now_coach_tasks_data
