import uuid
from django.db import models

from wisdom_v2.enum.service_content_enum import PersonalActivityTypeEnum, CoachAuthEnum
from wisdom_v2.models_file.helper import HelperBaseModel


class PersonalActivity(HelperBaseModel):
    id = models.UUIDField(default=uuid.uuid4, primary_key=True, help_text="UUID")
    type = models.IntegerField(
        choices=PersonalActivityTypeEnum.choices(), default=PersonalActivityTypeEnum.default(), help_text='活动类型')
    price = models.PositiveIntegerField(help_text='支付金额', default=0, null=True)
    expire_time = models.DateTimeField(default=None, null=True, help_text='过期时间，过期逻辑已去掉')
    coach = models.ForeignKey('Coach', related_name='personal_activity_coach', on_delete=models.DO_NOTHING,
                              db_constraint=False, null=True, help_text='教练id')
    resume = models.ForeignKey('Resume', related_name='personal_activity_resume', on_delete=models.DO_NOTHING,
                               db_constraint=False, null=True, help_text='简历id')
    personal_name = models.CharField(max_length=255, null=True, help_text='个人客户可见姓名')
    city = models.TextField(null=True, default=None, help_text='所在城市')
    coach_auth = models.IntegerField(
        choices=CoachAuthEnum.choices(), default=None,
        null=True, help_text='教练认证 ｜ 1：ICF-ACC, 2：ICF-PCC, 3：ICF-MCC, 4：通过PCC口试, 5：通过ACC口试')
    qualification = models.JSONField(null=True, default=None, help_text='教练证书')
    working_years = models.IntegerField(
        null=True, help_text='工作年限 | 1：一年以上，2：三年以上，3：五年以上')
    coach_domain = models.JSONField(null=True, default=None, help_text='擅长的教练领域')
    highest_position = models.JSONField(null=True, default=None, help_text='曾担任过的最高职位')
    job_profile = models.TextField(null=True, default=None, blank=True, help_text='一句话介绍工作经历')
    posters_text = models.TextField(null=True, default=None, help_text='个人海报中的文本信息')
    look_all_count = models.IntegerField(default=0, null=True, help_text='海报图浏览次数/不去重', blank=True)
    look_all_user = models.JSONField(default=None, null=True, help_text='活动图用户浏览id列表/去重', blank=True)
    limit_count = models.IntegerField(default=None, null=True, help_text='单个用户限制次数')
    platform_fee_rate = models.IntegerField(default=None, null=True, help_text='平台抽成比例，如果为空代表按照规则计算')

    class Meta:
        db_table = 'v2_personal_activity'
        verbose_name = '个人活动表'
        verbose_name_plural = verbose_name


class PersonalActivityInterview(HelperBaseModel):
    id = models.UUIDField(default=uuid.uuid4, primary_key=True, help_text="UUID")
    personal_activity = models.ForeignKey(
        'PersonalActivity', related_name='personal_activity_interview',
        on_delete=models.DO_NOTHING, db_constraint=False, default=None, null=True)
    interview = models.ForeignKey(
        'ProjectInterview', related_name='personal_activity_interview', on_delete=models.DO_NOTHING, default=None,
        null=True)
    poster = models.ForeignKey('Poster', related_name='personal_activity_interview', on_delete=models.DO_NOTHING,
                               db_constraint=False, null=True, help_text='海报id')

    class Meta:
        db_table = 'v2_personal_activity_interview'
        verbose_name = '辅导关联个人活动表'
        verbose_name_plural = verbose_name


class PersonalActivityFlow(HelperBaseModel):
    id = models.UUIDField(default=uuid.uuid4, primary_key=True, help_text="UUID")
    personal_activity = models.ForeignKey(
        'PersonalActivity', related_name='personal_activity_flow',
        on_delete=models.DO_NOTHING, db_constraint=False, default=None, null=True)
    user = models.ForeignKey('User', related_name='personal_activity_flow', on_delete=models.DO_NOTHING,
                             db_constraint=False, null=True, help_text='教练id')
    poster = models.ForeignKey('Poster', related_name='personal_activity_flow', on_delete=models.DO_NOTHING,
                               db_constraint=False, null=True, help_text='海报id')

    class Meta:
        db_table = 'v2_personal_activity_flow'
        verbose_name = '个人活动额外数据记录表'
        verbose_name_plural = verbose_name
