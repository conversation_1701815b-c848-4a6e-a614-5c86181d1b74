import uuid
from django.db import models

from wisdom_v2.enum.project_interview_enum import DataType
from wisdom_v2.models_file.helper import HelperBaseModel


# 服务阶段
class ServiceStage(HelperBaseModel):
    id = models.UUIDField(default=uuid.uuid4, primary_key=True, help_text="UUID")
    project = models.ForeignKey('Project', related_name='service_stage', on_delete=models.DO_NOTHING, db_constraint=False, default=None, null=True)
    project_member = models.ForeignKey('ProjectMember', related_name='service_stage', on_delete=models.SET_NULL, null=True, default=None)
    is_stage = models.BooleanField(default=None, null=False, help_text="是否分阶段")
    class Meta:
        db_table = 'v2_service_stage'
        verbose_name = '是否配置阶段'
        verbose_name_plural = verbose_name


class ProjectServiceStage(HelperBaseModel):
    id = models.UUIDField(default=uuid.uuid4, primary_key=True, help_text="UUID")
    stage_name = models.TextField(null=False, help_text='阶段名称')
    project = models.ForeignKey('Project', related_name='project_service_stage', on_delete=models.DO_NOTHING, db_constraint=False, default=None, null=True)
    project_member = models.ForeignKey('ProjectMember', related_name='project_service_stage', on_delete=models.SET_NULL, null=True, default=None)
    order = models.IntegerField(default=0, null=False, help_text='阶段顺序')

    class Meta:
        db_table = 'v2_project_service_stage'
        verbose_name = '项目服务阶段'
        verbose_name_plural = verbose_name


# 项目服务内容
class ProjectServiceContent(HelperBaseModel):
    id = models.UUIDField(default=uuid.uuid4, primary_key=True, help_text="UUID")
    project = models.ForeignKey('Project', related_name='project_service_content', on_delete=models.DO_NOTHING, db_constraint=False, default=None, null=True)
    service_stage = models.ForeignKey(
        'ProjectServiceStage', related_name='project_service_content', on_delete=models.DO_NOTHING, db_constraint=False, default=None, null=True)
    content_type = models.IntegerField(
        choices=DataType.choices(),
        default=DataType.default(), null=False, help_text='服务内容类型')
    content = models.JSONField(null=True, help_text='服务内容')
    order = models.IntegerField(default=0, null=False, help_text='模块顺序')

    class Meta:
        db_table = 'v2_project_service_content'
        verbose_name = '项目服务内容'
        verbose_name_plural = verbose_name


# 项目用户的服务内容
class ProjectMemberServiceContent(HelperBaseModel):
    id = models.UUIDField(default=uuid.uuid4, primary_key=True, help_text="UUID")
    project_member = models.ForeignKey('ProjectMember', related_name='project_member_service_content',
                                       on_delete=models.DO_NOTHING, db_constraint=False, default=None, null=True)
    service_stage = models.ForeignKey(
        'ProjectServiceStage', related_name='project_member_service_content', on_delete=models.DO_NOTHING, db_constraint=False, default=None, null=True)
    content_type = models.IntegerField(
        choices=DataType.choices(),
        default=DataType.default(), null=False, help_text='服务内容类型')
    object_ids = models.JSONField(default=None, null=True, help_text='对象id')

    class Meta:
        db_table = 'v2_project_service_member_content'
        verbose_name = '项目用户服务内容'
        verbose_name_plural = verbose_name


# 项目服务的成员
class ProjectServiceMember(HelperBaseModel):
    project_service = models.ForeignKey('ProjectServiceContent', related_name='project_service_members', on_delete=models.SET_NULL, null=True)
    member_service = models.ForeignKey('ProjectMemberServiceContent', related_name='project_service_members', on_delete=models.SET_NULL, null=True)

    class Meta:
        db_table = 'v2_project_service_member'
        verbose_name = '项目服务成员记录表'
