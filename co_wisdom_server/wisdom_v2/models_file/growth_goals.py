import uuid
from django.db import models

from wisdom_v2.enum.service_content_enum import GrowthGoalsModelTypeEnum
from wisdom_v2.models_file.helper import HelperBaseModel
from wisdom_v2.models import GrowthGoals


class GrowthGoalsModule(HelperBaseModel):
    id = models.UUIDField(default=uuid.uuid4, primary_key=True, help_text="UUID")
    project_bundle = models.ForeignKey(
        'ProjectBundle', related_name='growth_goals_module',
        on_delete=models.DO_NOTHING, db_constraint=False)
    growth_goals = models.ForeignKey(
        GrowthGoals, related_name='growth_goals', on_delete=models.DO_NOTHING, default=None, null=True)
    type = models.IntegerField(
        choices=GrowthGoalsModelTypeEnum.choices(),
        default=GrowthGoalsModelTypeEnum.default(), help_text='成长目标类型')
    hours = models.FloatField(null=True, help_text='限定辅导时长')
    start_date = models.DateField(null=True, help_text='开始日期')
    end_date = models.DateField(null=True, help_text='结束日期')

    class Meta:
        db_table = 'v2_growth_goals_module'
        verbose_name = '成长目标配置表'
        verbose_name_plural = verbose_name
