"""model init"""

from wisdom_v2.models_file.chemical_interview import ChemicalInterviewModule, ChemicalInterview2Coach
from wisdom_v2.models_file.stakeholder_interview import StakeholderInterviewModule, StakeholderInterview, CancelInterview2StakeholderInterview
from wisdom_v2.models_file.project_service import ProjectMemberServiceContent, ProjectServiceContent, ProjectServiceStage, ProjectServiceMember, ServiceStage
from wisdom_v2.models_file.growth_goals import GrowthGoalsModule
from wisdom_v2.models_file.activity import Activity, ActivityInterview, ActivityCoach
from wisdom_v2.models_file.public_courses import PublicCourses, PublicCoursesCoach
from wisdom_v2.models_file.business_order import BusinessOrder, BusinessOrder2Object
from wisdom_v2.models_file.business_settlement import BusinessSettlement
from wisdom_v2.models_file.recurring_schedule import RecurringSchedule
from wisdom_v2.models_file.personal_activity import PersonalActivity, PersonalActivityInterview, PersonalActivityFlow
from wisdom_v2.models_file.poster import Poster
from wisdom_v2.models_file.coach import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, CoachAssociatedCompany
from wisdom_v2.models_file.non_platform import NonPlatformArticles
from wisdom_v2.models_file.evaluation import EvaluationReportConfig
from wisdom_v2.models_file.interview import InterviewMeeting
from wisdom_v2.models_file.project import ProjectSettlement, ProjectTagGroup
from wisdom_v2.models_file.tag import TagObject, Tag, ProjectTagConfig
from wisdom_v2.models_file.platform_notification import PlatformNotification
