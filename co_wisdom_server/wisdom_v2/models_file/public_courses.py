import uuid
from django.db import models

from wisdom_v2.enum.business_order_enum import WorkTypeEnum
from wisdom_v2.models_file.helper import HelperBaseModel


class PublicCourses(HelperBaseModel):
    id = models.UUIDField(default=uuid.uuid4, primary_key=True, help_text="UUID")
    class_name = models.TextField(default=None, null=True, help_text='班级名称')
    creator = models.ForeignKey('User', related_name='public_courses', on_delete=models.DO_NOTHING,
                                db_constraint=False, null=True, default=None, help_text='创建人')
    type = models.IntegerField(choices=WorkTypeEnum.choices(), default=WorkTypeEnum.default(), null=True, help_text='工作类型')
    start_time = models.DateTimeField(default=None, null=True, help_text='开始时间')
    end_time = models.DateTimeField(default=None, null=True, help_text='结束时间')

    class Meta:
        db_table = 'v2_public_courses'
        verbose_name = '公开课表'
        verbose_name_plural = verbose_name


class PublicCoursesCoach(HelperBaseModel):
    id = models.UUIDField(default=uuid.uuid4, primary_key=True, help_text="UUID")
    public_courses = models.ForeignKey(
        'PublicCourses', related_name='public_courses_coach',
        on_delete=models.DO_NOTHING, db_constraint=False)
    coach = models.ForeignKey(
        'Coach', related_name='public_courses_coach', on_delete=models.DO_NOTHING, default=None, null=True)
    resume = models.ForeignKey(
        'Resume', related_name='public_courses_coach', on_delete=models.DO_NOTHING, default=None, null=True)
    # 2.24版本改动，公开课主题表暂时用不上，主要数据迁移至关联表，修改数据只修改关联表数据
    # 创建公开课时依然会创建一个PublicCourses对象，只是存储创建时候的关联，后续不做任何改动。
    class_name = models.TextField(default=None, null=True, help_text='班级名称')
    student_name = models.TextField(default=None, null=True, help_text='学员名称')
    type = models.IntegerField(choices=WorkTypeEnum.choices(), default=None, null=True, help_text='工作类型')
    start_time = models.DateTimeField(default=None, null=True, help_text='开始时间')
    end_time = models.DateTimeField(default=None, null=True, help_text='结束时间')
    class Meta:
        db_table = 'v2_public_courses_coach'
        verbose_name = '公开课关联教练表'
        verbose_name_plural = verbose_name
