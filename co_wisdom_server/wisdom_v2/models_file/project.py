import uuid
from django.db import models

from wisdom_v2.enum.project_interview_enum import ProjectInterviewPlaceTypeEnum
from wisdom_v2.enum.business_order_enum import BusinessOrderDurationTypeEnum, WorkTypeEnum
from wisdom_v2.models_file.helper import HelperBaseModel
from wisdom_v2.models import Coach


class ProjectSettlement(HelperBaseModel):
    id = models.UUIDField(default=uuid.uuid4, primary_key=True, help_text="UUID")
    project = models.ForeignKey('Project', related_name='project_settlement', on_delete=models.DO_NOTHING,
                                db_constraint=False)
    work_type = models.IntegerField(choices=WorkTypeEnum.choices(), default=WorkTypeEnum.default(), help_text='工作形式')
    coachee_name = models.TextField(default=None, null=True, help_text='客户名')
    coach = models.ForeignKey(Coach, related_name='project_settlement', on_delete=models.DO_NOTHING,
                              db_constraint=False, null=True, help_text='教练')
    work_start_time = models.DateTimeField(null=True, help_text='工作开始时间')
    work_end_time = models.DateTimeField(null=True, help_text='工作结束时间')
    time_type = models.IntegerField(choices=BusinessOrderDurationTypeEnum.choices(),
                                    default=BusinessOrderDurationTypeEnum.default(), null=True, help_text='时间间隔类型')
    place_type = models.IntegerField(choices=ProjectInterviewPlaceTypeEnum.choices(),
                                     default=None, null=True, help_text='1: 线上 2: 线下')

    class Meta:
        db_table = 'v2_project_settlement'
        verbose_name = '项目结算信息'
        verbose_name_plural = verbose_name


class ProjectTagGroup(HelperBaseModel):
    id = models.UUIDField(default=uuid.uuid4, primary_key=True, help_text="UUID")
    project = models.ForeignKey(
        'Project', related_name='project_tag_group', on_delete=models.DO_NOTHING, db_constraint=False)
    title = models.TextField(default=None, null=True, help_text='需求标签标题')
    project_member = models.ForeignKey(
        'ProjectMember', related_name='project_tag_group', on_delete=models.DO_NOTHING,
        db_constraint=False, default=None, null=True)
    coach_content = models.JSONField(default=None, null=True, help_text='教练匹配信息')
    stakeholder_feedback_and_expectations = models.TextField(default=None, null=True, help_text='利益相关者对客户的反馈和期待')
    other_info = models.TextField(default=None, null=True, help_text='其他信息')
    other_info_from_company_background = models.TextField(default=None, null=True, help_text='客户公司 HR 填写的背景信息补充')
    other_info_from_company_customer_info = models.TextField(default=None, null=True, help_text='客户公司 HR 填写的客户信息补充')
    other_info_from_company_coach_requirements = models.TextField(default=None, null=True, help_text='客户公司 HR 填写的教练要求补充')
    stakeholder_feedback_from_company_background = models.TextField(default=None, null=True, help_text='客户公司 HR 填写的关于客户背景的利益相关者反馈补充')
    stakeholder_feedback_from_company_customer_info = models.TextField(default=None, null=True, help_text='客户公司 HR 填写的关于客户信息的利益相关者反馈补充')
    stakeholder_feedback_from_company_coach_requirements = models.TextField(default=None, null=True, help_text='客户公司 HR 填写的关于教练要求的利益相关者反馈补充')

    class Meta:
        db_table = 'v2_project_tag_group'
        verbose_name = '项目需求标签组'
        verbose_name_plural = verbose_name
