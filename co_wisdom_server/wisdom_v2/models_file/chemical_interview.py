import uuid
from django.db import models
from wisdom_v2.models_file.helper import HelperBaseModel
from wisdom_v2.models import Project<PERSON>ember, Coach, ProjectInterview
from wisdom_v2.enum.chemical_interview_enum import ChemicalInterviewCoachSourceEnum, ChemicalInterviewStatusEnum


class ChemicalInterviewModule(HelperBaseModel):
    id = models.UUIDField(default=uuid.uuid4, primary_key=True, help_text="UUID")
    project_member = models.ForeignKey(ProjectMember, related_name='chemical_interview', on_delete=models.DO_NOTHING,
                                       db_constraint=False, null=True)
    max_interview_number = models.IntegerField(null=True, blank=True, help_text='最多面谈次数')
    duration = models.FloatField(null=True, help_text='化学访谈时长 0.5,1,1.5,2')
    start_time = models.DateField(null=True, help_text='面谈开始日期')
    end_time = models.DateField(null=True, help_text='面谈结束日期')
    coach_source = models.IntegerField(choices=ChemicalInterviewCoachSourceEnum.choices(),
                                       default=None, help_text='面谈教练来源', null=True)

    class Meta:
        db_table = 'v2_chemical_interview_module'
        verbose_name = '化学面谈配置表'
        verbose_name_plural = verbose_name


class ChemicalInterview2Coach(HelperBaseModel):
    id = models.UUIDField(default=uuid.uuid4, primary_key=True, help_text="UUID")
    chemical_interview_module = models.ForeignKey(ChemicalInterviewModule, related_name='coaches',
                                                  on_delete=models.DO_NOTHING, db_constraint=False)
    coach = models.ForeignKey(Coach, related_name='chemical_interview', on_delete=models.DO_NOTHING,
                              db_constraint=False, null=True, help_text='教练')
    chemical_interview_status = models.IntegerField(choices=ChemicalInterviewStatusEnum.choices(),
                                                    default=None, help_text='面谈状态', null=True)
    coach_aspect = models.CharField(max_length=256, null=True, default=None, help_text='教练特点')
    coach_impression = models.TextField(null=True, default=None, help_text='教练评价')
    interview = models.ForeignKey(
        ProjectInterview, related_name='chemical_interview', on_delete=models.DO_NOTHING,
        db_constraint=False, null=True, default=None)

    class Meta:
        db_table = 'v2_chemical_interview'
        verbose_name = '化学面谈教练关系表'
        verbose_name_plural = verbose_name

