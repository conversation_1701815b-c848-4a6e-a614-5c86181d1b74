import uuid
from decimal import Decimal

from django.db import models

from wisdom_v2.enum.service_content_enum import ActivityTypeEnum, ActivityCoachStatusEnum
from wisdom_v2.models_file.helper import HelperBaseModel


class Activity(HelperBaseModel):
    id = models.UUIDField(default=uuid.uuid4, primary_key=True, help_text="UUID")
    theme = models.TextField(default=None, null=True, help_text='主题')
    brief = models.TextField(default=None, null=True, help_text='简介', blank=True)
    rule = models.TextField(default=None, null=True, help_text='规则', blank=True)
    notes = models.TextField(default=None, null=True, help_text='活动额外描述', blank=True)
    bg_color = models.TextField(default=None, null=True, help_text='小程序活动背景颜色', blank=True)
    type = models.IntegerField(
        choices=ActivityTypeEnum.choices(),default=ActivityTypeEnum.default(), help_text='活动类型')
    start_date = models.DateField(default=None, null=True, help_text='开始日期')
    end_date = models.DateField(default=None, null=True, help_text='结束日期')
    interview_start_date = models.DateField(default=None, null=True, help_text='可预约辅导开始日期')
    interview_end_date = models.DateField(default=None, null=True, help_text='可预约辅导结束日期')
    price = models.IntegerField(null=True, default=0, help_text='活动价格，单位：分')
    head_image_url = models.TextField(default=None, null=True, help_text='小程序活动头图链接', blank=True)
    poster_image_url = models.TextField(default=None, null=True, help_text='海报链接', blank=True)
    model_image_url = models.TextField(default=None, null=True, help_text='模板图链接', blank=True)
    guide_image_url = models.TextField(default=None, null=True, help_text='引导图链接', blank=True)
    limit_count = models.IntegerField(default=None, null=True, help_text='单个用户限制次数')
    accum_limit_count = models.IntegerField(default=None, null=True, help_text='活动累计用户限制次数')
    hours = models.IntegerField(default=1, null=True, help_text='活动时长')
    
    @property
    def display_price(self):
        # 如果是None或者0，直接返回。
        # 如果是其他数字，需要除以100，转换成元。
        if self.price:
            return Decimal(str(self.price)) / Decimal('100')
        return self.price

    class Meta:
        db_table = 'v2_activity'
        verbose_name = '活动表'
        verbose_name_plural = verbose_name


class ActivityCoach(HelperBaseModel):
    id = models.UUIDField(default=uuid.uuid4, primary_key=True, help_text="UUID")
    image_url = models.TextField(default=None, null=True, help_text='活动图', blank=True)
    look_all_count = models.IntegerField(default=0, null=True, help_text='活动图浏览次数/不去重', blank=True)
    look_all_user = models.JSONField(default=None, null=True, help_text='活动图用户浏览id列表/去重', blank=True)
    additional_count = models.IntegerField(null=True, default=0, help_text='活动中添加的额外辅导次数')
    status = models.IntegerField(
        choices=ActivityCoachStatusEnum.choices(),default=ActivityCoachStatusEnum.default(), help_text='教练状态')
    activity = models.ForeignKey(
        'Activity', related_name='activity_coach',
        on_delete=models.DO_NOTHING, db_constraint=False)
    coach = models.ForeignKey(
        'Coach', related_name='activity_coach', on_delete=models.DO_NOTHING, default=None, null=True)
    resume = models.ForeignKey(
        'Resume', related_name='activity_coach', on_delete=models.DO_NOTHING, default=None, null=True)
    is_send_invite = models.BooleanField('是否发送邀请邮件', default=False)

    class Meta:
        db_table = 'v2_coach_activity'
        verbose_name = '教练关联活动表'
        verbose_name_plural = verbose_name


class ActivityInterview(HelperBaseModel):
    id = models.UUIDField(default=uuid.uuid4, primary_key=True, help_text="UUID")
    activity = models.ForeignKey(
        'Activity', related_name='activity_interview',
        on_delete=models.DO_NOTHING, db_constraint=False, default=None, null=True)
    interview = models.ForeignKey(
        'ProjectInterview', related_name='activity_interview', on_delete=models.DO_NOTHING, default=None, null=True)

    class Meta:
        db_table = 'v2_activity_interview'
        verbose_name = '辅导关联活动表'
        verbose_name_plural = verbose_name

