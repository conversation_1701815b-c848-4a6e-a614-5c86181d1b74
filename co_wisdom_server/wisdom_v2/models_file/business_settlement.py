import uuid
from django.db import models

from wisdom_v2.enum.business_order_enum import SettlementUserTypeEnum, BusinessOrderSettlementStatusEnum
from wisdom_v2.models_file.helper import HelperBaseModel
from wisdom_v2.models import User, Coach


class BusinessSettlement(HelperBaseModel):
    id = models.UUIDField(default=uuid.uuid4, primary_key=True, help_text="UUID")
    user_type = models.IntegerField(choices=SettlementUserTypeEnum.choices(), default=SettlementUserTypeEnum.default(),
                                    help_text='用户类型')
    apply_withdrawal_amount = models.PositiveIntegerField(help_text='申请提现金额，单位：分', default=0)
    member_paid = models.PositiveIntegerField(help_text='客户实付，单位：分', default=0)
    apply_time = models.DateTimeField(null=True, help_text='申请时间')
    platform_service_amount = models.PositiveIntegerField(help_text='平台服务费，单位：分', default=0)
    tax_amount = models.PositiveIntegerField(help_text='税费，单位：分', default=0)
    create_user = models.ForeignKey(User, related_name='settlement_creator', on_delete=models.DO_NOTHING,
                                    db_constraint=False, null=True, help_text='发起人')
    settlement_status = models.IntegerField(choices=BusinessOrderSettlementStatusEnum.choices(),
                                            default=BusinessOrderSettlementStatusEnum.default(), help_text='结算状态')
    processor = models.ForeignKey(User, related_name='settlement_processor', on_delete=models.DO_NOTHING,
                                  db_constraint=False, null=True, help_text='处理人')
    settlement_time = models.DateTimeField(null=True, help_text='提现时间')
    coach = models.ForeignKey(Coach, related_name='business_settlement', on_delete=models.DO_NOTHING,
                              db_constraint=False, null=True, help_text='教练')

    class Meta:
        db_table = 'v2_business_settlement'
        verbose_name = '业务结算单表'
        verbose_name_plural = verbose_name
