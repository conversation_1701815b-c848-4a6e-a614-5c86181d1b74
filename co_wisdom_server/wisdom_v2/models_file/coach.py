import uuid
from django.db import models

from wisdom_v2.enum.coach_enum import NonPlatformInterviewTypeEnum, NonPlatformInterviewPayTypeEnum
from wisdom_v2.enum.user_enum import BrowseRecordObjectEnum
from wisdom_v2.models_file.helper import HelperBaseModel


class CoachToClientNotes(HelperBaseModel):
    id = models.UUIDField(default=uuid.uuid4, primary_key=True, help_text="UUID")
    coach = models.ForeignKey('Coach', related_name='coach_to_client_notes', on_delete=models.DO_NOTHING,
                                db_constraint=False, default=None, null=True)
    user = models.ForeignKey('User', related_name='coach_to_client_notes', on_delete=models.DO_NOTHING,
                              db_constraint=False, default=None, null=True)
    notes = models.TextField(null=True, default=None, help_text="备注")

    class Meta:
        db_table = 'v2_coach_to_client_notes'
        verbose_name = '教练给用户的备注表'
        verbose_name_plural = verbose_name


class NonPlatformInterview(HelperBaseModel):
    id = models.UUIDField(default=uuid.uuid4, primary_key=True, help_text="UUID")
    coach_user = models.ForeignKey('User', related_name='coach_user_non_platform_interview', on_delete=models.DO_NOTHING,
                                db_constraint=False, default=None, null=True)
    topic = models.TextField(null=True, default=None, help_text='议题')
    customer_name = models.TextField(null=True, default=None, help_text='客户名称')
    customer_email = models.TextField(null=True, default=None, help_text='客户邮箱')
    user_count = models.IntegerField(null=True, default=None, help_text='辅导用户数')

    start_time = models.DateTimeField(null=True, help_text='开始时间', default=None)
    end_time = models.DateTimeField(null=True, help_text='结束时间', default=None)
    hour = models.FloatField(null=True, help_text='辅导时长', default=0)

    pay_type = models.SmallIntegerField(
        choices=NonPlatformInterviewPayTypeEnum.choices(), default=None, null=True, help_text='1: 付费辅导 2: 免费辅导')
    type = models.SmallIntegerField(
        choices=NonPlatformInterviewTypeEnum.choices(), default=None, null=True, help_text='1: 一对一辅导 2: 团队辅导')

    class Meta:
        db_table = 'v2_non_platform_interview'
        verbose_name = '教练创建辅导数据表'
        verbose_name_plural = verbose_name


class BrowseRecord(HelperBaseModel):
    id = models.UUIDField(default=uuid.uuid4, primary_key=True, help_text="UUID")
    user = models.ForeignKey(
        'User', related_name='browse_record_user', on_delete=models.DO_NOTHING,
        db_constraint=False, default=None, null=True)
    object_id = models.TextField(null=True, default=None, help_text='关联对象id')
    object_type = models.SmallIntegerField(
        choices=BrowseRecordObjectEnum.choices(), null=True, default=BrowseRecordObjectEnum.default(), help_text='关联对象类型')
    total_views = models.IntegerField(default=0, null=True, help_text="总查看次数")
    total_shares = models.IntegerField(default=0, null=True, help_text="总分享次数")

    class Meta:
        db_table = 'v2_browse_record'
        verbose_name = '浏览记录'
        verbose_name_plural = verbose_name


class CoachAssociatedCompany(HelperBaseModel):
    id = models.UUIDField(default=uuid.uuid4, primary_key=True, help_text="UUID")
    coach_user = models.ForeignKey(
        'User', related_name='coach_user_company', on_delete=models.DO_NOTHING,
        db_constraint=False, default=None, null=True)
    company = models.ForeignKey('Company', related_name='company_coach', on_delete=models.DO_NOTHING,
                                db_constraint=False, null=True, help_text='公司')
    other_background_information = models.TextField(null=True, default=None, help_text='其他背景信息')

    class Meta:
        db_table = 'v2_coach_associated_company'
        verbose_name = '教练关联公司信息记录表'
        verbose_name_plural = verbose_name


class DismissedTodoItem(HelperBaseModel):
    """
    记录用户不再提醒（忽略）的待办事项
    """

    # 定义待办事项的类型，方便区分和管理
    class ItemType(models.TextChoices):
        INTERVIEW = 'interview', '辅导/访谈'
        GROWTH_GOAL = 'growth_goal', '成长目标提醒'
        COACH_TASK = 'coach_task', '教练任务'
        # 如果未来还有其他类型的待办，可以加在这里

    user = models.ForeignKey(
        'User', # 关联到你的用户模型
        on_delete=models.CASCADE, # 如果用户被删除，相关的忽略记录也删除
        related_name='dismissed_todos', # 可以通过 user.dismissed_todos 反向查询
        verbose_name="用户"
    )
    item_type = models.CharField(
        max_length=20,
        choices=ItemType.choices, # 使用上面定义的类型选项
        verbose_name="待办类型"
    )
    # 使用 CharField 存储唯一标识符，因为不同类型的 ID 格式可能不同
    item_identifier = models.CharField(
        max_length=255,
        verbose_name="待办项唯一标识符",
        help_text="例如: 'interview_123', 'growth_goal_member_45_project_6', 'task_789'"
    )

    class Meta:
        verbose_name = "不再提醒的待办项"
        verbose_name_plural = verbose_name
        # 关键：确保同一个用户对同一个待办项只能有一条“不再提醒”记录
        unique_together = ('user', 'item_type', 'item_identifier')
        indexes = [
            # 添加索引以优化查询
            models.Index(fields=['user', 'item_type', 'item_identifier']),
        ]

    def __str__(self):
        # 后台管理界面显示的名称
        return f"{self.user} 不再提醒 {self.get_item_type_display()}: {self.item_identifier}"
