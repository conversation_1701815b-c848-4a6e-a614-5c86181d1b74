from django.db import models


class HelperBaseModel(models.Model):
    created_at = models.DateTimeField('创建时间', auto_now_add=True)
    updated_at = models.DateTimeField('更新时间', auto_now=True, null=True)
    deleted = models.BooleanField('是否删除', default=False)

    class Meta:
        abstract = True
        db_table = 'v2_helper_base_model'
        verbose_name = '新基础表'
        verbose_name_plural = verbose_name