import uuid
from django.db import models

from wisdom_v2.enum.coach_enum import NonPlatformArticlesTypeEnum, NonPlatformArticlesCategoryEnum
from wisdom_v2.models_file.helper import HelperBaseModel


class NonPlatformArticles(HelperBaseModel):
    id = models.UUIDField(default=uuid.uuid4, primary_key=True, help_text="UUID")
    title = models.CharField(max_length=255, null=True, help_text='文章标题')
    brief = models.TextField(null=True, help_text='文章简介')
    content = models.TextField(null=True, help_text='文字内容')
    tag = models.TextField(null=True, help_text='文章标签')
    image_url = models.CharField(max_length=500, null=True, help_text='文章图片')
    is_video = models.BooleanField('是否有视频', default=False)
    jump_link = models.TextField(null=True, help_text='跳转链接')
    category = models.SmallIntegerField(
        choices=NonPlatformArticlesCategoryEnum.choices(), default=None, null=True)
    type = models.SmallIntegerField(
        choices=NonPlatformArticlesTypeEnum.choices(), default=None, null=True, help_text='1: 微信公众号')

    class Meta:
        db_table = 'v2_non_platform_articles'
        verbose_name = '非平台的文章'
        verbose_name_plural = verbose_name
