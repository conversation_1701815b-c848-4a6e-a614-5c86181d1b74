import uuid
from django.db import models
from wisdom_v2.models_file.helper import HelperBaseModel
from wisdom_v2.models import InterviewRecordTemplate, TotalTemplate, ProjectInterview, CoachTask, ProjectMember, \
    ProjectInterested


class StakeholderInterviewModule(HelperBaseModel):
    id = models.UUIDField(default=uuid.uuid4, primary_key=True, help_text="UUID")
    project_member = models.ForeignKey(ProjectMember, related_name='stakeholder_interview_module',
                                       on_delete=models.DO_NOTHING, db_constraint=False, null=True)
    stakeholder_interview_number = models.IntegerField(null=True, blank=True, help_text='访谈人数')
    duration = models.FloatField(null=True, help_text='利益相关者访谈时长 0.5,1,1.5,2')
    start_date = models.DateField(null=True, help_text='访谈开始日期')
    end_date = models.DateField(null=True, help_text='访谈结束日期')
    coach_task = models.ForeignKey(CoachTask, related_name='stakeholder_interview_module',
                                   on_delete=models.DO_NOTHING, db_constraint=False, null=True, help_text='教练任务')
    coach_template = models.ForeignKey(InterviewRecordTemplate, related_name='stakeholder_interview_module',
                                       on_delete=models.DO_NOTHING, db_constraint=False, help_text='访谈记录模版')
    report_template = models.ForeignKey(TotalTemplate, related_name='stakeholder_interview_module',
                                        on_delete=models.DO_NOTHING, db_constraint=False, help_text='访谈报告模版')

    class Meta:
        db_table = 'v2_stakeholder_interview_module'
        verbose_name = '利益相关者访谈配置表'
        verbose_name_plural = verbose_name


class StakeholderInterview(HelperBaseModel):
    id = models.UUIDField(default=uuid.uuid4, primary_key=True, help_text="UUID")
    stakeholder_interview_module = models.ForeignKey(StakeholderInterviewModule, related_name='stakeholder_interview',
                                                     on_delete=models.DO_NOTHING, db_constraint=False)
    project_interested = models.ForeignKey(ProjectInterested, related_name='stakeholder_interview',
                                           on_delete=models.DO_NOTHING, db_constraint=False, null=True)
    interview = models.ForeignKey(
        ProjectInterview, related_name='stakeholder_interview', on_delete=models.DO_NOTHING,
        db_constraint=False, null=True, default=None)
    is_send_message = models.BooleanField('是否发送邀请', default=False)

    class Meta:
        db_table = 'v2_stakeholder_interview'
        verbose_name = '利益相关者访谈表'
        verbose_name_plural = verbose_name


class CancelInterview2StakeholderInterview(HelperBaseModel):
    interview = models.ForeignKey(
        ProjectInterview, related_name='cancel_stakeholder_interview', on_delete=models.DO_NOTHING,
        db_constraint=False, null=True, default=None)
    stakeholder_interview = models.ForeignKey(
        StakeholderInterview, related_name='cancel_interview', on_delete=models.DO_NOTHING,
        db_constraint=False, null=True, default=None)

    class Meta:
        db_table = 'v2_cancel_interview_2_stakeholder_interview'
        verbose_name = '已取消辅导与利益相关者访谈关系表'
        verbose_name_plural = verbose_name


