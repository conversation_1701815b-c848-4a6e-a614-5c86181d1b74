import uuid
from decimal import Decimal

from django.db import models

from wisdom_v2.enum.business_order_enum import BusinessOrderTypeEnum, BusinessOrderPayStatusEnum, WorkTypeEnum, \
    BusinessOrderSettlementStatusEnum, BusinessOrderWithdrawalStatusEnum, BusinessOrderDurationTypeEnum, \
    BusinessOrderDataTypeEnum
from wisdom_v2.models_file.helper import HelperBaseModel
from wisdom_v2.models import Project, User, Coach
from wisdom_v2.models_file.business_settlement import BusinessSettlement


class BusinessOrder(HelperBaseModel):
    id = models.UUIDField(default=uuid.uuid4, primary_key=True, help_text="UUID")
    type = models.IntegerField(choices=BusinessOrderTypeEnum.choices(), default=BusinessOrderTypeEnum.default(),
                               help_text='工作类型')
    work_type = models.IntegerField(choices=WorkTypeEnum.choices(), default=WorkTypeEnum.default(), help_text='工作形式')
    project = models.ForeignKey(Project, related_name='business_order', on_delete=models.DO_NOTHING,
                                db_constraint=False, null=True)
    user = models.ForeignKey(User, related_name='business_order', on_delete=models.DO_NOTHING,
                             db_constraint=False, null=True, help_text='对应用户id')
    coach = models.ForeignKey(Coach, related_name='business_order', on_delete=models.DO_NOTHING,
                              db_constraint=False, null=True, help_text='教练')
    coach_price = models.PositiveIntegerField(help_text='教练单价，单位：分', default=0)
    duration = models.FloatField(null=True, help_text='结算时长')
    duration_type = models.IntegerField(choices=BusinessOrderDurationTypeEnum.choices(),
                                        default=BusinessOrderDurationTypeEnum.default(), help_text='结算时长类型')
    member_paid = models.PositiveIntegerField(help_text='客户实付，单位：分', default=0)
    platform_scale = models.FloatField(null=True, help_text='平台扣除比例')
    platform_service_amount = models.PositiveIntegerField(help_text='平台服务费，单位：分', default=0)
    tax_point = models.FloatField(null=True, help_text='税点')
    tax_amount = models.PositiveIntegerField(help_text='税费，单位：分', default=0)
    coach_actual_income = models.PositiveIntegerField(help_text='教练实际收益，单位：分', default=0)
    pay_status = models.IntegerField(choices=BusinessOrderPayStatusEnum.choices(),
                                     default=BusinessOrderPayStatusEnum.default(), help_text='支付状态，记录客户付款状态')
    settlement_status = models.IntegerField(choices=BusinessOrderSettlementStatusEnum.choices(),
                                            default=BusinessOrderSettlementStatusEnum.default(), help_text='结算状态，记录教练结算状态')
    withdrawal_status = models.IntegerField(choices=BusinessOrderWithdrawalStatusEnum.choices(),
                                            default=BusinessOrderWithdrawalStatusEnum.default(), help_text='提现状态，记录教练提现状态')
    settlement_time = models.DateTimeField(null=True, help_text='结算时间')
    apply_withdrawal_time = models.DateTimeField(null=True, help_text='申请提现时间')
    apply_withdrawal_processor = models.ForeignKey(User, related_name='business_order_processor', on_delete=models.DO_NOTHING,
                                  db_constraint=False, null=True, help_text='提现处理人')
    pay_time = models.DateTimeField(null=True, help_text='标记支付时间，非实际支付时间')
    history_record = models.JSONField(null=True, default=None, help_text='信息变更记录')
    business_settlement = models.ForeignKey(
        BusinessSettlement, related_name='business_order',  on_delete=models.DO_NOTHING, db_constraint=False,
        null=True, help_text='结算单')
    work_start_time = models.DateTimeField(null=True, help_text='工作开始时间')
    work_end_time = models.DateTimeField(null=True, help_text='工作结束时间')
    extra_info = models.JSONField(null=True, default=None, help_text='额外信息，当关联外键不存在时可从本字段获取企业名称等信息')

    class Meta:
        db_table = 'v2_business_order'
        verbose_name = '业务订单表'
        verbose_name_plural = verbose_name


class BusinessOrder2Object(HelperBaseModel):
    business_order = models.ForeignKey(BusinessOrder, related_name='business_order2object', on_delete=models.DO_NOTHING,
                                       db_constraint=False, null=True, help_text='业务订单')
    type = models.IntegerField(choices=WorkTypeEnum.choices(), default=WorkTypeEnum.default(), help_text='工作形式')
    data_type = models.IntegerField(choices=BusinessOrderDataTypeEnum.choices(), default=None, null=True, help_text='数据类型')
    object_id = models.CharField(max_length=64, null=True, help_text='关联对象id')

    class Meta:
        db_table = 'v2_business_order2_object'
        verbose_name = '业务订单中间关系表'
        verbose_name_plural = verbose_name


class BusinessOrderExtraCost(HelperBaseModel):
    id = models.UUIDField(default=uuid.uuid4, primary_key=True, help_text="UUID")
    business_order = models.ForeignKey(BusinessOrder, related_name='business_order_extra_cost', on_delete=models.DO_NOTHING,
                                       db_constraint=False, null=True, help_text='业务订单')
    describe = models.TextField(default=None, null=True, help_text='费用描述')
    marking_date = models.DateField(null=True, help_text='对应日期')
    cost = models.PositiveIntegerField(help_text='费用，单位：分', default=0)
    notes = models.TextField(help_text='备注', default=0)

    @property
    def actual_cost(self):
        if self.cost:
            return Decimal(str(self.cost)) / Decimal('100')
        return self.cost

    class Meta:
        db_table = 'v2_business_order_extra_cost'
        verbose_name = '业务订额外费用记录'
        verbose_name_plural = verbose_name
