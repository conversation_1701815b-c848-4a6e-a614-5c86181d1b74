import uuid
from django.db import models

from wisdom_v2.enum.schedule_enum import RecurringScheduleRepeatTypeEnum
from wisdom_v2.enum.service_content_enum import ActivityTypeEnum
from wisdom_v2.models_file.helper import HelperBaseModel


class RecurringSchedule(HelperBaseModel):
    schedule = models.ForeignKey(
        'Schedule', related_name='recurring_schedule', on_delete=models.DO_NOTHING,
        db_constraint=False, default=None, null=True)

    # 重复类型字段
    repeat_type = models.IntegerField(
        choices=RecurringScheduleRepeatTypeEnum.choices(), default=RecurringScheduleRepeatTypeEnum.default(),
        verbose_name='重复类型')

    # 重复截止日期，如果为None表示永不截止
    end_repeat_date = models.DateField(verbose_name='重复截止日期', null=True, blank=True, default=None)

    # 排除的日期，例如用户手动删除了某些重复日程
    excluded_dates = models.JSONField(null=True, verbose_name='排除的日期', default=None, blank=True)

    class Meta:
        db_table = 'v2_recurring_schedule'
        verbose_name = '重复日程'
        verbose_name_plural = verbose_name
