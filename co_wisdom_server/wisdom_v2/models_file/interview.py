import uuid
from django.db import models

from wisdom_v2.enum.project_interview_enum import Interview<PERSON>eetingChannelTypeEnum
from wisdom_v2.models_file.helper import HelperBaseModel


class InterviewMeeting(HelperBaseModel):
    id = models.UUIDField(default=uuid.uuid4, primary_key=True, help_text="UUID")
    channel_type = models.IntegerField(
        choices=InterviewMeetingChannelTypeEnum.choices(),
        default=InterviewMeetingChannelTypeEnum.default(), help_text='工作形式')
    meeting_id = models.TextField(null=True, default=None, help_text='房间id')
    meeting_code = models.TextField(null=True, default=None, help_text='房间号')
    interview = models.ForeignKey(
        'ProjectInterview', related_name='interview_meeting',
        on_delete=models.DO_NOTHING, db_constraint=False, null=True)

    class Meta:
        db_table = 'v2_interview_meeting'
        verbose_name = '辅导视频会议信息'
        verbose_name_plural = verbose_name
