import uuid

from django.db import models

from wisdom_v2.enum.service_content_enum import TagObjectTypeEnum, TagSubTypeEnum, TagRequirementConfigTypeEnum
from wisdom_v2.models_file.helper import HelperBaseModel


class Tag(HelperBaseModel):
    id = models.UUIDField(default=uuid.uuid4, primary_key=True, help_text="UUID")
    name = models.TextField(null=True, blank=True, help_text='标签名称')
    is_leaf_node = models.BooleanField("是否叶节点（结束节点）", default=False)
    parent = models.ForeignKey(
        'self', null=True, blank=True, related_name='children', on_delete=models.CASCADE, help_text='父级标签')

    class Meta:
        db_table = 'v2_tag'
        verbose_name = '标签'
        verbose_name_plural = verbose_name


class TagRequirementConfig(HelperBaseModel):
    id = models.UUIDField(default=uuid.uuid4, primary_key=True, help_text="UUID")
    tag = models.ForeignKey(Tag, on_delete=models.DO_NOTHING, db_constraint=False, help_text='标签')
    sub_type = models.IntegerField(
        choices=TagSubTypeEnum.choices(), default=TagSubTypeEnum.default(), help_text='关联标签的子标签类型')
    order = models.IntegerField(null=True, help_text='排序')
    is_sync = models.BooleanField("是否同步需求标签", default=True)
    is_data_matching = models.BooleanField("是否进行数据匹配", default=True)
    config_type = models.IntegerField(
        choices=TagRequirementConfigTypeEnum.choices(), default=TagRequirementConfigTypeEnum.default(), help_text='需求标签类型')

    class Meta:
        db_table = 'v2_requirement_tag_config'
        verbose_name = '需求标签配置表'
        verbose_name_plural = verbose_name


class TagObject(HelperBaseModel):
    id = models.UUIDField(default=uuid.uuid4, primary_key=True, help_text="UUID")
    tag = models.ForeignKey(Tag, on_delete=models.DO_NOTHING, db_constraint=False, help_text='标签')
    object_type = models.IntegerField(
        choices=TagObjectTypeEnum.choices(), default=TagObjectTypeEnum.default(), help_text='标签类型')
    object_id = models.TextField(help_text='关联对象id')
    source = models.ForeignKey(
        'self', null=True, blank=True, related_name='object_source', on_delete=models.CASCADE, help_text='关联标签')

    class Meta:
        db_table = 'v2_tag_object'
        verbose_name = '标签应用表'
        verbose_name_plural = verbose_name


class ProjectTagConfig(HelperBaseModel):
    id = models.UUIDField(default=uuid.uuid4, primary_key=True, help_text="UUID")
    tag_object = models.ForeignKey(TagObject, on_delete=models.DO_NOTHING, db_constraint=False, help_text='标签')
    project_tag_group = models.ForeignKey(
        'ProjectTagGroup', related_name='project_tag_config', on_delete=models.DO_NOTHING, db_constraint=False)
    is_mandatory = models.BooleanField(default=False, help_text='是否为必选标签')

    class Meta:
        db_table = 'v2_project_tag_config'
        verbose_name = '项目标签配置'
        verbose_name_plural = verbose_name

