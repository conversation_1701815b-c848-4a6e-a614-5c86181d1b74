import uuid
from django.db import models

from wisdom_v2.enum.user_enum import Coach<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, PosterTypeEnum
from wisdom_v2.models_file.helper import HelperBaseModel



class Poster(HelperBaseModel):
    id = models.UUIDField(default=uuid.uuid4, primary_key=True, help_text="UUID")
    auto_id = models.IntegerField(null=True, blank=True)
    type = models.IntegerField(
        choices=PosterTypeEnum.choices(),
        default=None, help_text='指定海报类型 默认全部', null=True)
    name = models.CharField(max_length=255, help_text="海报名称")

    class Meta:
        db_table = 'v2_poster'
        verbose_name = '海报表'
        verbose_name_plural = verbose_name

    def save(self, *args, **kwargs):
        if not self.auto_id:
            max_auto_id = Poster.objects.all().aggregate(models.Max('auto_id'))['auto_id__max']
            self.auto_id = 1 if max_auto_id is None else max_auto_id + 1
        super().save(*args, **kwargs)
