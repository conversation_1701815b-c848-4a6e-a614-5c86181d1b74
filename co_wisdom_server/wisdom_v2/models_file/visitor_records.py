import uuid
from django.db import models

from wisdom_v2.models_file.helper import HelperBaseModel


class VisitorRecords(HelperBaseModel):
    id = models.UUIDField(default=uuid.uuid4, primary_key=True, help_text="UUID")
    openid = models.CharField(max_length=255, help_text="用户标识")

    class Meta:
        db_table = 'v2_visitor_records'
        verbose_name = '游客记录'
        verbose_name_plural = verbose_name
