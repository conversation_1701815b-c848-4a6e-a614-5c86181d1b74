import uuid
from django.db import models

from wisdom_v2.models_file.helper import HelperBaseModel


class CompanyInterview(HelperBaseModel):
    id = models.UUIDField(default=uuid.uuid4, primary_key=True, help_text="UUID")
    schedule = models.ForeignKey(
        'Schedule', related_name='company_interview', help_text='面试时间',
        on_delete=models.DO_NOTHING, db_constraint=False)
    project = models.ForeignKey(
        'Project', related_name='company_interview',
        on_delete=models.DO_NOTHING, db_constraint=False, help_text='项目中包含了企业信息')
    coach = models.ForeignKey(
        'Coach', related_name='company_interview', help_text='教练',
        on_delete=models.DO_NOTHING, db_constraint=False)
    customer = models.ForeignKey(
        'CompanyMember', related_name='company_interview',
        on_delete=models.DO_NOTHING, db_constraint=False, null=True, help_text='教练的客户，可能在面试时候还没确定')
    interviewer = models.ForeignKey(
        'CompanyMember', related_name='company_interview_interviewer',
        on_delete=models.DO_NOTHING, db_constraint=False, null=True, help_text='面试官')
    remark = models.TextField(null=True, help_text='给教练的备注，记录面试方式，参考信息等')

    class Meta:
        db_table = 'v2_company_interview'
        verbose_name = '企业面试'
        verbose_name_plural = verbose_name
