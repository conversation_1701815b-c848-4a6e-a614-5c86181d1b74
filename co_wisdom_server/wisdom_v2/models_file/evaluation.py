import uuid

from django.db import models

from wisdom_v2.enum.service_content_enum import EvaluationReportTypeEnum
from wisdom_v2.models_file.helper import HelperBaseModel


class EvaluationReportConfig(HelperBaseModel):
    id = models.UUIDField(default=uuid.uuid4, primary_key=True, help_text="UUID")
    evaluation = models.ForeignKey(
        'Evaluation', related_name='evaluation_report_config', on_delete=models.DO_NOTHING, db_constraint=False)
    type = models.IntegerField(
        choices=EvaluationReportTypeEnum.choices(), default=None, help_text='测评报告类型')

    class Meta:
        db_table = 'v2_evaluation_report_config'
        verbose_name = '测评报告模板配置'
        verbose_name_plural = verbose_name
