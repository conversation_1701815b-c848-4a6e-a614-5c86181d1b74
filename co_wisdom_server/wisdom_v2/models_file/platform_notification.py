import uuid
from django.db import models

from wisdom_v2.models_file.helper import HelperBaseModel
from wisdom_v2.enum.service_content_enum import NoticeChannelTypeEnum
from wisdom_v2.models import Project, User


class PlatformNotification(HelperBaseModel):
    """
    平台通知记录模型
    用于记录所有通过平台发送的通知，包括邮件、短信、企业微信、飞书等
    """
    id = models.UUIDField(default=uuid.uuid4, primary_key=True, help_text="UUID")

    # 通知基本信息
    subject = models.CharField(max_length=255, null=True, help_text="通知主题")
    content = models.TextField(null=True, help_text="通知内容")
    template_name = models.CharField(max_length=100, null=True, help_text="模板名称")

    # 通知渠道
    channel = models.IntegerField(
        choices=NoticeChannelTypeEnum.choices(),
        default=NoticeChannelTypeEnum.default(),
        help_text="发送渠道: 1-企业微信 2-短信 3-邮件 4-飞书 5-小程序"
    )

    # 关联信息
    project = models.ForeignKey(
        Project,
        related_name='project_notifications',
        on_delete=models.SET_NULL,
        db_constraint=False,
        null=True,
        help_text='关联项目'
    )

    # 发送者信息
    sender = models.ForeignKey(
        User,
        related_name='sent_notifications',
        on_delete=models.SET_NULL,
        db_constraint=False,
        null=True,
        help_text='发送者'
    )
    sender_name = models.CharField(max_length=100, null=True, help_text="发送者名称")

    # 接收者信息
    receiver = models.ForeignKey(
        User,
        related_name='received_notifications',
        on_delete=models.SET_NULL,
        db_constraint=False,
        null=True,
        help_text='接收者'
    )
    receiver_name = models.CharField(max_length=100, null=True, help_text="接收者名称")
    receiver_contact = models.CharField(max_length=255, null=True, help_text="接收者联系方式(邮箱/手机号/企微ID)")

    # 发送状态
    status = models.IntegerField(default=1, help_text="发送状态: 1-成功 2-失败")
    error_message = models.TextField(null=True, help_text="错误信息")

    # 额外数据
    extra_data = models.JSONField(null=True, default=dict, help_text="额外数据")

    class Meta:
        db_table = 'v2_platform_notification'
        verbose_name = '平台通知记录'
        verbose_name_plural = verbose_name

    @classmethod
    def create_notification(cls, subject, template_name, channel,
                           project_id=None, content=None,
                           sender_id=None, sender_name=None,
                           receiver_id=None, receiver_name=None, receiver_contact=None,
                           status=1, error_message=None, extra_data=None):
        """
        创建通知记录
        """
        # 获取项目对象
        project = None
        if project_id:
            try:
                project = Project.objects.get(id=project_id, deleted=False)
            except Project.DoesNotExist:
                pass

        # 获取发送者对象
        sender = None
        if sender_id:
            try:
                sender = User.objects.get(id=sender_id, deleted=False)
                if not sender_name and sender:
                    sender_name = sender.cover_name
            except User.DoesNotExist:
                pass

        # 获取接收者对象
        receiver = None
        if receiver_id:
            try:
                receiver = User.objects.get(id=receiver_id, deleted=False)
                if not receiver_name and receiver:
                    receiver_name = receiver.cover_name
            except User.DoesNotExist:
                pass

        notification = cls(
            subject=subject,
            content=content,
            template_name=template_name,
            channel=channel,
            project=project,
            sender=sender,
            sender_name=sender_name,
            receiver=receiver,
            receiver_name=receiver_name,
            receiver_contact=receiver_contact,
            status=status,
            error_message=error_message,
            extra_data=extra_data or {}
        )
        notification.save()
        return notification
