import redis
import json
import logging

from functools import reduce

from django.conf import settings
from django.db.models import Q

from co_wisdom_server.base import NOT_SELECT_IMAGE_URL
from data.models import AppMessagetemplates
from utils import aesdecrypt
from utils.aliyun_sls_log import AliyunSlsLogLayout
from utils import feishu_robot
from utils.message import email as message_email
from utils.messagecenter import center
from utils.messagecenter.email import Email
from utils.messagecenter.sms import SMS
from utils.qr_code import get_qr_code
from utils.send_account_email import get_project_manage_qr_code
from utils.wechat_oauth import WeChatMiniProgram
from utils import work_wechat
from utils.messagecenter import getui
from wisdom_v2.common import user_public
from wisdom_v2.enum.service_content_enum import MultipleAssociationRelationTypeEnum, PersonalReportTypeEnum, \
    InterviewRecordTemplateTypeEnum
from wisdom_v2.enum.user_backend_enum import UserBackendTypeEnum
from wisdom_v2.enum.project_interview_enum import DataType
from wisdom_v2.enum.user_enum import UserTmpEnum
from wisdom_v2.models import WorkWechatUser, ProjectInterviewRecord, \
    ActionPlan, Habit, ChangeObservation, \
    MultipleAssociationRelation, UserBackend, PersonalReport, TotalTemplate, CoachTask, UserTmp
from wisdom_v2.views import constant

third_party_redis = redis.Redis.from_url(settings.THIRD_PARTY_DATA_REDIS)
data_redis = redis.Redis.from_url(settings.DATA_REDIS)
error_logging = logging.getLogger('api_action')


def add_work_wechat_department_user(phone, name, department_id, email):

    try:
        state, msg = work_wechat.WorkWechat().add_department_user(
            phone, name, phone, [department_id], email
        )

        # 请求失败
        if not state:
            return False, msg
        # 账号被占用
        elif state and isinstance(msg, dict):
            return False, msg
        # 请求成功
        else:
            # 更新部门缓存
            redis_key = f'work_wechat_department_{department_id}'
            redis_data = third_party_redis.get(redis_key)
            if redis_data:
                redis_data = json.loads(redis_data.decode())
                redis_data.append({'work_wechat_user_id': phone, 'name': name})
                third_party_redis.set(redis_key, json.dumps(redis_data))
            return True, msg
    except Exception as e:
        return False, str(e)


def add_work_wechat_user(user, department_id, content_type=None):
    try:
        # 如果已有绑定账号，禁止创建。
        work_wechat_user = WorkWechatUser.objects.filter(
            wx_user_id__isnull=False, user=user, deleted=False).first()
        if work_wechat_user:
            feishu_robot.push_wx_error_message(name='用户创建企业微信账号错误', level='error', content={
                'phone': user.phone,
                'name': user.name,
                'department_id': department_id,
                'error': str("该用户已绑定企业微信账号，禁止重复绑定。")
            })
            return {'state': False, 'msg': '该用户已绑定企业微信账号，禁止重复绑定。'}

        # 新建教练企业微信账号
        state, msg = add_work_wechat_department_user(
            user.phone, user.true_name, department_id, user.email)
        if not state:
            if isinstance(msg, dict):
                msg.update({'user_id': user.id})
            return msg

        # 如果之前就有只绑定了外部联系人id的数据，就进行更新操作。
        not_wx_user_work_wechat = WorkWechatUser.objects.filter(
            wx_user_id__isnull=True, user=user, deleted=False).first()
        if not_wx_user_work_wechat:
            not_wx_user_work_wechat.wx_user_id = user.phone
            not_wx_user_work_wechat.save()
        else:
            WorkWechatUser.objects.create(
                user_id=user.id, wx_user_id=user.phone, department_id=department_id)
        # 发送企业微信消息通知
        if content_type:
            getui.send_work_wechat_coach_notice.apply_async(kwargs=dict(
                user=user.phone, content_type=content_type, coach_id=user.pk, coach_name=user.cover_name),
                countdown=180, expires=360)
    except Exception as e:
        feishu_robot.push_wx_error_message(name='用户创建企业微信账号错误', level='error', content={
            'phone': user.phone,
            'name': user.name,
            'department_id': department_id,
            'error': str(e)
        })


# 绑定企微账号
def update_work_wechat_user(work_wechat_user_id, user, content_type='add_user'):
    try:
        if work_wechat_user_id:
            work_wechat_user = WorkWechatUser.objects.filter(
                user_id=user.id,
                deleted=False
            )
            if work_wechat_user.exists():
                work_wechat_user = work_wechat_user.first()
                # 如果当前用户wx_user_id和work_wechat_user_id不匹配，触发变更操作
                if work_wechat_user.wx_user_id and work_wechat_user.wx_user_id != work_wechat_user_id:
                    work_wechat_user.wx_user_id = work_wechat_user_id
                    work_wechat_user.qr_code = None
                    work_wechat_user.save()
                # 如果当前用户没有wx_user_id，则进行绑定操作
                elif not work_wechat_user.wx_user_id:
                    work_wechat_user.wx_user_id = work_wechat_user_id
                    work_wechat_user.save()
                # 如果当前用户wx_user_id和work_wechat_user_id匹配，则不进行操作
                else:
                    return  # 无修改操作跳过消息通知
            else:
                WorkWechatUser.objects.create(user_id=user.id, wx_user_id=work_wechat_user_id)
            # 刷新用户token
            user_public.mark_user_as_terminated([user.id])
            # 发送消息通知
            getui.send_work_wechat_coach_notice.delay(
                work_wechat_user_id, content_type=content_type, coach_id=user.id, coach_name=user.cover_name)
            AliyunSlsLogLayout().send_third_api_log(
                user_id=user.id,
                user_name=user.cover_name,
                content=f'work_wechat_user_id:{work_wechat_user_id}, user_id:{user.id}, content_type:{content_type}',
                message='教练的企业微信账号变更绑定用户')
        # 如果没传企业微信id，则认为是解绑操作。
        else:
            work_wechat_user = WorkWechatUser.objects.filter(user_id=user.id, deleted=False).first()
            if work_wechat_user:
                # 如果账号绑定了外部联系人信息，清理成员id，保留和外部联系人的关联
                if work_wechat_user.external_user_id:
                    work_wechat_user.wx_user_id = None
                    work_wechat_user.qr_code = None
                    work_wechat_user.save()
                else:
                    # 如果账号没有绑定了外部联系人信息，数据物理删除
                    work_wechat_user.deleted = True
                    work_wechat_user.save()
                # 刷新用户token
                user_public.mark_user_as_terminated([user.id])
                AliyunSlsLogLayout().send_third_api_log(
                    user_id=user.id,
                    user_name=user.cover_name,
                    content=f'user_id:{user.id}, content_type:{content_type}',
                    message='教练的企业微信账号解除绑定')
    except Exception as e:
        feishu_robot.push_wx_error_message(name='修改用户绑定的企业微信信息错误', level='error', content={
            'phone': user.phone,
            'name': user.name,
            'work_wechat_user_id': work_wechat_user_id.name,
            'error': str(e)
        })


def get_work_wechat_department_users(include=None, exclude=None):
    """
    include: 列表 只查询的部门id
    exclude: 列表 不查询的部门id
    """
    try:

        if include:
            department_ids = include
        else:
            state, department_ids = work_wechat.WorkWechat().get_department_ids()
            if not state:
                return False, '企业微信部门列表获取失败'
            if exclude:
                # 循环需要移除的部门id
                for item in exclude:
                    # 如果存在，则移除
                    if item in [department_ids]:
                        department_ids.remove(item)

        # 获取最终所需要的部门数据
        all_department_user_data = []
        for item in department_ids:

            # 当前部门的用户数据有没有缓存
            redis_department_user =  third_party_redis.get(f'work_wechat_department_{item}')
            if redis_department_user:
                data = json.loads(redis_department_user.decode()) if json.loads(redis_department_user.decode()) else []
            else:
                item_state, raw_department_users = work_wechat.WorkWechat().get_department_users(item)
                if not item_state:
                    return False, '部门成员获取失败'
                data = [{'work_wechat_user_id': item.get('userid'), 'name': item.get('name')} for item in
                        raw_department_users]
                third_party_redis.set(f'work_wechat_department_{item}', json.dumps(data))
            all_department_user_data += data


        # 去除不同部门下同一个用户
        all_department_user_data = reduce(lambda x, y: x if y in x else x + [y], [[], *all_department_user_data])

        return True, all_department_user_data
    except Exception as e:
        feishu_robot.push_wx_error_message(name='查询部门成员错误', level='error', content={
            'include': include,
            'exclude': exclude,
            'error': str(e)
        })
        return False, str(e)


def get_interview_record_detail(interview):

    record = ProjectInterviewRecord.objects.filter(deleted=False, interview=interview).first()

    data = {
        'leader_capacity': '不涉及能力方面的提升',
        'coachee_habit_content': [],
        'coach_habit_content': [],
        'coach_ponder': [],
        'coachee_ponder': [],
        'coach_action_plan': [],
        'coachee_action_plan': [],
    }
    if record:
        if record.leader_capacity:
            data['leader_capacity'] = record.leader_capacity.title
        data['target_progress'] = record.target_progress
        data['harvest_score'] = record.harvest_score
        data['satisfaction_score'] = record.satisfaction_score

    # 行为转变
    raw_coachee_habit = Habit.objects.filter(creator_role=constant.ROLE_COACHEE, interview=interview, deleted=False).all()
    raw_coach_habit = Habit.objects.filter(creator_role=constant.ROLE_COACH, interview=interview, deleted=False).all()
    for sub, item in enumerate(raw_coachee_habit):
        content = f'当{item.when}会停止{item.stop}转变为{item.change}'
        if len(raw_coachee_habit) > 1:
            content = f'{sub + 1}.{content}'
        data['coachee_habit_content'].append(content)
    for sub, item in enumerate(raw_coach_habit):
        content = f'当{item.when}会停止{item.stop}转变为{item.change}'
        if len(raw_coach_habit) > 1:
            content = f'{sub + 1}.{content}'
        data['coach_habit_content'].append(content)

    # 思考与反思
    raw_coachee_ponder = record.discover if record else None
    raw_coach_ponder = record.observation if record else None
    if raw_coach_ponder:
        data['coach_ponder'].append(raw_coach_ponder)
    if raw_coachee_ponder:
        data['coachee_ponder'].append(raw_coachee_ponder.content)

    # 行动计划
    raw_coach_action_plan = ActionPlan.objects.filter(interview=interview, creator_role=constant.ROLE_COACH, deleted=False)
    raw_coachee_action_plan = ActionPlan.objects.filter(interview=interview, creator_role=constant.ROLE_COACHEE, deleted=False)
    for sub, item in enumerate(raw_coach_action_plan):
        content = item.content
        if content:
            if len(raw_coach_action_plan) > 1:
                content = f'{sub + 1}.{content}'
            data['coach_action_plan'].append(content)
    for sub, item in enumerate(raw_coachee_action_plan):
        content = item.content
        if content:
            if len(raw_coachee_action_plan) > 1:
                content = f'{sub + 1}.{content}'
            data['coachee_action_plan'].append(content)

    if not data.get('coach_habit_content') and not data.get('coachee_habit_content'):
        data['habit_text'] = '暂无要转变的行为'
    if not data.get('coach_action_plan') and not  data.get('coachee_action_plan'):
        data['action_plan_text'] = '没有制定行动计划'

    detail = [
        {
            "title": "本次辅导议题：",
            "text": None,
            "coach": {
                "title": None,
                "content": [interview.topic] if interview.topic else []
            },
            "coachee": {
                "title": None,
                "content": [interview.coachee_topic] if interview.coachee_topic else []
            },
            "score": None
        },
        {
            "title": "本次教练辅导是为了提升以下能力：",
            "text": data.get('leader_capacity'),
            "coach": {
                "title": None,
                "content": []
            },
            "coachee": {
                "title": None,
                "content": []
            },
            "score": None
        },
        {
            "title": "思考和观察",
            "text": None,
            "coach": {
                "title": "在本次教练辅导中有这些观察：",
                "content": data.get('coach_ponder')
            },
            "coachee": {
                "title": "在本次教练辅导中有这些思考和收获：",
                "content": data.get('coachee_ponder')
            },
            "score": None
        },
        {
            "title": "本次教练辅导中提到的行为转变",
            "text": data.get('habit_text'),
            "coach": {
                "title": None,
                "content": data.get('coach_habit_content')
            },
            "coachee": {
                "title": None,
                "content": data.get('coachee_habit_content')
            },
            "score": None
        },
        {
            "title": "本次教练辅导中提到的行动计划",
            "text": data.get('action_plan_text'),
            "coach": {
                "title": None,
                "content": data.get('coach_action_plan')
            },
            "coachee": {
                "title": None,
                "content": data.get('coachee_action_plan')
            },
            "score": None
        },
    ]
    if data.get('target_progress'):
        detail.append({
            "title": "有效度：",
            "text": None,
            "coach": {
                "title": None,
                "content": []
            },
            "coachee": {
                "title": None,
                "content": []
            },
            "score": data.get('target_progress')
        })
    if data.get('harvest_score'):
        detail.append({
            "title": "投入度：",
            "text": None,
            "coach": {
                "title": None,
                "content": []
            },
            "coachee": {
                "title": None,
                "content": []
            },
            "score": data.get('harvest_score')
            })
    if data.get('satisfaction_score'):
        detail.append({
            "title": "满意度：",
            "text": None,
            "coach": {
                "title": None,
                "content": []
                },
            "coachee": {
                "title": None,
                "content": []
            },
            "score": data.get('satisfaction_score')
            })
    return detail


def get_tasks_state(task_obj, redis_data):
    data = {
        'id': task_obj.id,
        'interval': 3,
        'result': None,
        'state': None,
        'errmsg': None,
    }
    if not redis_data:
        #  判断任务状态
        if task_obj.status == 'PENDING' or task_obj.status == 'RETRY':
            data['state'] = 'running'
        elif task_obj.status == 'SUCCESS':
            data['interval'] = 0
            state, msg = task_obj.get()
            if state:
                data['state'] = 'completed'
                data['result'] = msg
            else:
                data['state'] = 'failed'
                data['errmsg'] = msg
        else:
            data['state'] = 'failed'
            data['interval'] = 0
            data['errmsg'] = task_obj.get()
    else:
        state, msg = redis_data.split('&')
        data['interval'] = 0
        if state == 'True':
            data['state'] = 'completed'
            data['result'] = msg
        else:
            data['state'] = 'failed'
            data['errmsg'] = msg
    return data


# 创建账号发送邮件通知
def send_project_user_add_notice(project_member, sender_id=None):
    # 获取二维码图片链接
    project_manage_image = get_project_manage_qr_code(project_member.project.id)
    if not project_manage_image:
        project_manage_image = settings.PROJECT_OPERAE_QR_CODE

    url_state, app_short_link = WeChatMiniProgram().get_url_link(
        'pages/index/index',
        f'refer=2')
    short_link = f'https://static.qzcoach.com/app/miniapp_qrcode.html?qrcode=https://static.qzcoach.com/app/b_login_qrcode.jpg&shortlink={app_short_link}'
    params = {"company_name": project_member.project.company.real_name,
              "project_name": project_member.project.name,
              "project_member_name": project_member.user.cover_name,
              "account": project_member.user.email,
              "password": aesdecrypt(project_member.user.password),
              "project_manage_image": project_manage_image,
                "short_link": short_link
              }
    attachments = [
        # ('小程序使用指南-润英联.pdf', open('intro.pdf', 'rb').read(), 'application/pdf')
    ]
    state = message_email.message_send_email_base('5alc_project_member_password_message',
                                                   params, [project_member.user.email], attachments=attachments, 
                                                   project_id=project_member.project_id, receiver_ids=project_member.user_id,
                                                   sender_id=sender_id)
    if state:
        project_member.is_send_email = True
        project_member.save()
        return
    else:
        return project_member.user.cover_name


# 创建企业管理员发送邮件通知
def send_company_manage_add_notice(user_backend, sender_id=None):
    params = {
        "name": user_backend.user.name,
        "password": aesdecrypt(user_backend.user.password),
        "title_name": user_backend.role.name,
    }
    state = message_email.message_send_email_base('add_admin_user', params, [user_backend.user.email], 
                                                  project_id=user_backend.project_id, receiver_ids=user_backend.user_id, 
                                                  sender_id=sender_id)
    if state:
        user_backend.is_send_email = True
        user_backend.save()
        return
    else:
        return user_backend.user.cover_name


# 利益相关者调研发送邮件通知
def send_stakeholder_coach_task_email_notice(stakeholder_users, coach_task, all_coach_task=None, all_interested=None, sender_id=None):
    coach_task_id = [item.id for item in all_coach_task] if all_coach_task else [coach_task.id]

    project_member = coach_task.project_bundle.project_member
    # 获取项目管理员信息
    project_backend = UserBackend.objects.filter(project_id=project_member.project_id,
                                                 user_type=UserBackendTypeEnum.admin.value,
                                                 role__name='项目运营', deleted=False).first()
    err_name = []
    for stakeholder in stakeholder_users:
        stakeholder_id = [item.id for item in all_interested] if all_interested else [stakeholder.id]

        if not stakeholder.interested.email:
            err_name.append(stakeholder.interested.cover_name)
            continue

        # 生成短链接
        url_state, short_link = WeChatMiniProgram().get_url_link(
            'pages_evaluation/feedback_list/feedback_list',
            f'stakeholder_id={stakeholder.interested_id}&project_id={project_member.project_id}')

        data = f'{settings.SITE_URL}feedback/?stakeholder_id={stakeholder.interested_id}&project_id={project_member.project_id}'
        qr_code = get_qr_code(data)
        param = {
            'stakeholder_name': stakeholder.interested.cover_name,
            'company_name': project_member.project.company.real_name,
            'user_name': project_member.user.cover_name,
            'qr_code': qr_code,
            'short_link': short_link,
            'project_manage_name': project_backend.user.cover_name,
            'project_manage_email': project_backend.user.email,
        }
        state = message_email.message_send_email_base('send_stakeholder_coach_task', param, [stakeholder.interested.email], 
                                                     project_id=project_member.project_id, receiver_ids=stakeholder.interested_id, 
                                                     sender_id=sender_id)
        if state:
            query_data = MultipleAssociationRelation.objects.filter(
                main_id__in=coach_task_id,
                secondary_id__in=stakeholder_id,
                deleted=False,
                type=MultipleAssociationRelationTypeEnum.stakeholder_coach_task,
            )
            if query_data.exists():
                query_data.update(is_send_email_notice=True)
        else:
            AliyunSlsLogLayout().send_third_api_log(
                user_id=stakeholder.interested.id,
                user_name=stakeholder.interested.cover_name,
                project_id=coach_task.project_bundle.project_member.project.id,
                message='利益相关者调研发送邮件通知',
                content=state,
            )
            err_name.append(stakeholder.interested.cover_name)
    return err_name


# 利益相关者调研发送短信通知
def send_stakeholder_coach_task_sms_notice(stakeholder_users, coach_task, all_coach_task=None, all_interested=None):
    coach_task_id = [item.id for item in all_coach_task] if all_coach_task else [coach_task.id]

    name = coach_task.project_bundle.project_member.user.cover_name
    path = 'pages_evaluation/feedback_list/feedback_list'

    err_name = []
    for stakeholder in stakeholder_users:
        stakeholder_id = [item.id for item in all_interested] if all_interested else [stakeholder.id]

        if not stakeholder.interested.phone:
            err_name.append(stakeholder.interested.cover_name)
            continue
        state, url = WeChatMiniProgram().get_url_link(
            path,
            f'stakeholder_id={stakeholder.interested.id}&project_id={coach_task.project_bundle.project_member.project_id}')
        if not state:
            err_name.append(stakeholder.interested.cover_name)
            continue
        content = f"{coach_task.project_bundle.project_member.project.company.real_name}正在进行的教练项目邀请您为{name}提供反馈，点击 {url} 完成填写"
        msg = SMS().sendMsg(stakeholder.interested.phone, content)
        if msg.get('status') == 'success':
            query_data = MultipleAssociationRelation.objects.filter(
                main_id__in=coach_task_id,
                secondary_id__in=stakeholder_id,
                deleted=False,
                type=MultipleAssociationRelationTypeEnum.stakeholder_coach_task,
            )
            if query_data.exists():
                query_data.update(is_send_sms_notice=True)
        else:
            AliyunSlsLogLayout().send_third_api_log(
                user_id=stakeholder.interested.id,
                user_name=stakeholder.interested.cover_name,
                project_id=coach_task.project_bundle.project_member.project.id,
                message='利益相关者调研发送短信通知',
                content=msg.get('msg'),
            )
            err_name.append(stakeholder.interested.cover_name)
    return err_name





def get_change_observation_report_data(user_id):
    change_observation_report_data = []
    change_observation_report = PersonalReport.objects.filter(
        user_id=user_id,
        type=PersonalReportTypeEnum.change_observation_report,
    ).order_by('-created_at')
    if change_observation_report.exists():
        for item in change_observation_report.all():
            change_observation = ChangeObservation.objects.filter(
                id=item.object_id,
                project_member__user_id=user_id,
                deleted=False
            ).first()
            if change_observation:
                change_observation_report_data.append({
                    'data_type': DataType.change_observation,
                    'image_url': NOT_SELECT_IMAGE_URL,
                    'title': item.name,
                    'details': {
                        'id': change_observation.id,
                        "report_id": item.id,
                        "report_name": item.name,
                        "report_created_at": item.created_at.strftime('%Y-%m-%d %H:%M:%S'),
                    },
                    'describe': f'生成时间：{item.created_at.strftime("%Y/%m/%d")}',
                })
    return change_observation_report_data


# 修改辅导记录模板后删除对应模版的用户缓存数据
def del_template_user_tmp(template_id, template_type):

    # 根据模板类型清理对应数据
    if template_type == InterviewRecordTemplateTypeEnum.coach_task:
        total_template = TotalTemplate.objects.filter(
            Q(coach_template_id=template_id) |
            Q(coachee_template_id=template_id) |
            Q(stakeholder_template_id=template_id)
        )

        # 清理教练任务记录
        coach_task = CoachTask.objects.filter(
            template__in=total_template,
            deleted=False
        )
        if coach_task.exists():
            coach_task_ids = coach_task.values_list('id', flat=True)

            UserTmp.objects.filter(
                data_id__in=coach_task_ids,
                type=UserTmpEnum.coach_tasks
            ).delete()
    else:
        #  清理辅导模板记录
        UserTmp.objects.filter(
            extra_id=template_id,
            type=UserTmpEnum.interview
        ).delete()


# 新增项目相关角色用户，
def add_project_user_data(user_id, role_id, project_id):

    for item in user_id:
        # 因为删除关连是将project_id改为空
        # 先看看有没有没绑定企业/项目，但是已存在的项目用户角色信息
        user_backend = UserBackend.objects.filter(
            project_id__isnull=True,
            role_id=role_id,
            user_id=item).first()
        # 有则更新
        if user_backend:
            user_backend.project_id=project_id
            user_backend.deleted = False
            user_backend.save()
            return

        # 有没有被删除的项目用户角色信息
        user, is_create = UserBackend.objects.get_or_create(
            project_id=project_id,
            role_id=role_id,
            user_id=item)
        # 有则更新
        if not is_create:
            user.deleted = False
            user.save()
    return


# 比对新的user_id与之前项目已有user_id， 新建/删除项目相关角色用户。
def update_project_user_data(user_id, role_id, project_id):
    exist_user_ids = UserBackend.objects.filter(
        deleted=False,
        project_id=project_id,
        role_id=role_id).values_list('user_id')
    exist_user_ids = [i[0] for i in exist_user_ids]
    # 新增的用户
    new_project_user = list(set(user_id).difference(set(exist_user_ids)))
    # 删除的用户
    del_project_user = list(set(exist_user_ids).difference(set(user_id)))

    if del_project_user:
        UserBackend.objects.filter(
            user_id__in=del_project_user,
            role_id=role_id,
            project_id=project_id).update(project=None)

    if new_project_user:
        add_project_user_data(new_project_user, role_id, project_id)
    return
