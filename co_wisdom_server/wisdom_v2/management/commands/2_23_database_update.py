from django.core.management.base import BaseCommand
from django.db import transaction

from wisdom_v2.models import UserContract, ProjectCoach


class Command(BaseCommand):
    help = 'My custom command to run Python file'

    def handle(self, *args, **options):

        with transaction.atomic():

            # 用户合同是否签约使用的新字段做判断，旧的数据需要刷字段值，使用创建时间
            # 更新合同签订时间
            contract = UserContract.objects.filter(sign_url__isnull=False, deleted=False).all()
            for item in contract:
                item.personal_empower_at = item.created_at
                item.save()

            # 项目教练在小程序给用户默认展示的简历使用新字段，旧的数据需要刷新字段值，默认使用resume列表最后一个
            # 刷新项目教练的默认简历
            coach = ProjectCoach.objects.filter(deleted=False, resume__isnull=False, member__isnull=True)
            for item in coach.all():
                item.show_resume_id = item.resume[-1]
                item.save()