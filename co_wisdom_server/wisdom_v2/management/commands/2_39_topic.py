from django.core.management.base import BaseCommand
from django.db import transaction
from django.db.models import F, Q  # Add this import

from wisdom_v2.models import ProjectInterview


class Command(BaseCommand):
    help = 'My custom command to run Python file'

    
    def handle(self, *args, **options):

        with transaction.atomic():
            # 获取符合条件的记录 ID
            project_interviews = ProjectInterview.objects.filter(
                (Q(topic='') | Q(topic__isnull=True)),
                deleted=False
            )
            ids_to_update = list(project_interviews.values_list('id', flat=True))
            
            # 执行更新操作
            updated_count = project_interviews.update(topic=F('coachee_topic'))
            
            print(f"Total number of ProjectInterviews updated: {updated_count}")
            print("IDs of updated ProjectInterviews:")
            for id in ids_to_update:
                print(id)

            if updated_count != len(ids_to_update):
                print(f"Warning: Number of updated records ({updated_count}) "
                      f"doesn't match the number of IDs ({len(ids_to_update)})")

