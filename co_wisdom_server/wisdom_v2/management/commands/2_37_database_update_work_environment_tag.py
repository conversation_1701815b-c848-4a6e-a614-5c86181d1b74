from django.core.management.base import BaseCommand
from django.db import transaction
from wisdom_v2.models_file import Tag


class Command(BaseCommand):
    help = 'My custom command to run Python file'

    # 更新教练工作环境标签
    def handle(self, *args, **options):

        with transaction.atomic():
            Tag.objects.filter(name='刚加入一家新公司或过渡到一个新角色', parent__name="当前工作环境", deleted=False).update(name="加入新公司/过渡到新角色")
            Tag.objects.filter(name='正在协助组织进行重大变革计划', parent__name="当前工作环境", deleted=False).update(name="参与重大变革")
            Tag.objects.filter(name='工作中正在发生一些对自己影响很大的变化', parent__name="当前工作环境", deleted=False).update(name="应对工作变化")
            Tag.objects.filter(name='工作中有很大的不确定性和模糊性', parent__name="当前工作环境", deleted=False).update(name="应对模糊和不确定")
            Tag.objects.filter(name='近期工作负责的范围或工作量明显增大', parent__name="当前工作环境", deleted=False).update(name="管理范围/工作量短期激增")
            Tag.objects.filter(name='个人或团队的业绩不佳', parent__name="当前工作环境", deleted=False).update(name="业绩不佳")