from datetime import datetime

from django.core.management.base import BaseCommand
from django.db import transaction

from wisdom_v2.models_file import Tag


class Command(BaseCommand):
    help = 'My custom command to run Python file'

    # 新建tag标签
    def handle(self, *args, **options):

        with transaction.atomic():
            to_parent_tag = Tag.objects.get(name="管理挑战", deleted=False)
            Tag.objects.create(name='建立团队信任', parent=to_parent_tag, deleted=False)
            Tag.objects.create(name='提供负面反馈', parent=to_parent_tag, deleted=False)
