from django.core.management.base import BaseCommand
from django.db import transaction

from wisdom_v2.enum.service_content_enum import ChangeObservationInviteTypeEnum
from wisdom_v2.models import ChangeObservation, MultipleAssociationRelation
from wisdom_v2.models_file import PublicCourses


class Command(BaseCommand):
    help = 'My custom command to run Python file'

    def handle(self, *args, **options):

        with transaction.atomic():
            # 查询所有的未被删除公开课
            # 查询公开课关联的教练数据
            # 将公开课数据覆盖到教练数据上
            # 防止后续误启动，增加class_name__isnull判断
            for public_courses in PublicCourses.objects.filter(deleted=False, class_name__isnull=False).all():
                public_courses_coach = public_courses.public_courses_coach.filter(deleted=False, class_name__isnull=True)
                public_courses_coach.update(
                    class_name=public_courses.class_name,
                    start_time=public_courses.start_time,
                    end_time=public_courses.end_time,
                    type=public_courses.type,
                )

            # 更新改变观察反馈的最大可预约人数以及邀请类型
            for change_observation in ChangeObservation.objects.filter(deleted=False).all():
                stakeholders_count = MultipleAssociationRelation.objects.filter(
                    deleted=False, main_id=str(change_observation.pk)).count()
                change_observation.invite_type = ChangeObservationInviteTypeEnum.stakeholders.value
                change_observation.max_stakeholders_count = stakeholders_count
                change_observation.save()
