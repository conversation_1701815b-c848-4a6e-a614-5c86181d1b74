from django.core.management.base import BaseCommand
from dateutil.relativedelta import relativedelta
from django.db import transaction

from wisdom_v2.common import action_plan_public
from wisdom_v2.enum.user_enum import PersonalApplyTypeEnum, PersonalApplyStatusEnum
from wisdom_v2.models import ActionPlan, PersonalApply
from wisdom_v2.models_file import Activity


class Command(BaseCommand):
    help = 'My custom command to run Python file'

    def handle(self, *args, **options):

        with transaction.atomic():

            # 刷新行动计划历史数据的开始时间
            action_plan_all = ActionPlan.objects.filter(start_date__isnull=True, deleted=False).all()

            for action_plan_item in action_plan_all:
                start_date, error = action_plan_public.get_base_default_start_date(
                    action_plan_item.end_date.strftime("%Y-%m-%d"), action_plan_item.created_at.strftime("%Y-%m-%d"))
                if error:
                    print(f'行动计划：{action_plan_item.id}, error: {error}')
                else:
                    action_plan_item.start_date = start_date
                    action_plan_item.save()
            print('行动计划开始时间更新完成')

            # 刷新C端用户学员申请表的type类型
            # 申请成为个人教练却没有coach的来源一定是用户申请成为个人教练
            PersonalApply.objects.filter(
                type=PersonalApplyTypeEnum.personal.value, coach__isnull=True, deleted=False).update(
                type=PersonalApplyTypeEnum.user_to_coach.value)

            # 已通过的用户申请成为个人教练，会有coach的值
            personal_apply = PersonalApply.objects.filter(
                type=PersonalApplyTypeEnum.personal.value, coach__isnull=False, deleted=False)
            for personal_apply_item in personal_apply:
                # 如果该教练没有见习入驻流程的通过申请，来源就是用户申请成为个人教练
                if not PersonalApply.objects.filter(
                    type=PersonalApplyTypeEnum.internship.value, coach=personal_apply_item.coach,
                        status=PersonalApplyStatusEnum.passed.value, deleted=False).exists():
                    personal_apply_item.type = PersonalApplyTypeEnum.user_to_coach.value
                    personal_apply_item.save()
            print('C端用户学员申请表的type类型更新完成')

            #  活动新增辅导可预约开始时间和辅导可预约结束时间，历史数据默认使用活动开始时间和结束时间
            activity = Activity.objects.filter(deleted=False, interview_start_date__isnull=True).all()
            for activity_item in activity:
                activity_item.interview_start_date = activity_item.start_date
                activity_item.interview_end_date = activity_item.end_date + relativedelta(years=1)  # 默认活动结束日期+1年
                activity_item.save()
            print('活动辅导可预约开始时间和辅导可预约结束时间更新完成')
