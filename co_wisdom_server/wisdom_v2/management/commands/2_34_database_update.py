from django.core.management.base import BaseCommand
from django.db import transaction

from utils.work_wechat import WorkWechat
from wisdom_v2.common import company_public
from wisdom_v2.models import Company, WorkWechatUser


class Command(BaseCommand):
    help = 'My custom command to run Python file'

    # 1-刷新企业标签
    # 2-刷新教练二维码信息
    def handle(self, *args, **options):

        with transaction.atomic():

            # 刷新项目标签
            print('刷新企业标签')
            all_company = Company.objects.filter(deleted=False)
            company_count = 0
            for company in all_company.all():
                try:
                    company_public.update_company_tag(company.id)
                    print(f'刷新{company.name}企业标签')
                    company_count += 1
                except Exception as e:
                    print(f'刷新{company.name}企业标签失败, error:{str(e)}')
            print(f'需要刷新{all_company.count()}个企业标签')
            print(f'成功刷新{company_count}个企业标签')

        with transaction.atomic():

            print('刷新二维码链接')

            work_wechat_user = WorkWechatUser.objects.filter(
                wx_user_id__isnull=False, user_id__isnull=False, qr_code__isnull=True, deleted=False)

            work_wechat_user_count = 0
            for user_item in work_wechat_user.all():
                user_info_state, user_info_msg = WorkWechat().get_contact_way_info(user_item.wx_user_id)
                if user_info_state:
                    user_item.qr_code = user_info_msg.get('qr_code')
                    user_item.save()
                    work_wechat_user_count += 1
                else:
                    print(f'获取二维码信息失败, user_id:{user_item.user_id}, wx_user_id:{user_item.wx_user_id}, error:{user_info_msg}')

            print(f'需要刷新{work_wechat_user.count()}个二维码链接')
            print(f'成功刷新{work_wechat_user_count}个二维码链接')



