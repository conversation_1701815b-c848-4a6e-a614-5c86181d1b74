from django.core.management.base import BaseCommand
from django.db import transaction

from wisdom_v2.models import Permission, Role, Role2Permission


class Command(BaseCommand):
    help = 'Add notification management page to project management menu'

    def handle(self, *args, **options):
        with transaction.atomic():
            # 获取项目管理菜单
            project_management = Permission.objects.filter(
                name='QzProjectManagement', deleted=False).first()
            
            if not project_management:
                self.stdout.write(self.style.ERROR('Project management menu not found'))
                return
            
            # 添加通知管理页面
            notification_management = project_management.add_permission_page(
                name='QzNotificationManagement',
                text='通知管理',
                order=20  # 设置一个较大的顺序值，确保它显示在菜单的末尾
            )
            
            # 添加通知管理页面的操作权限
            notification_management.add_permission_element(
                name='QzNotificationManagementView',
                text='查看',
                order=1
            )
            
            # 将权限分配给超级管理员角色
            admin_role = Role.objects.filter(name='超级管理员', deleted=False).first()
            if admin_role:
                Role2Permission.objects.get_or_create(
                    role=admin_role,
                    permission=notification_management,
                    defaults={'deleted': False}
                )
            
            # 将权限分配给项目运营角色
            project_operation_role = Role.objects.filter(name='项目运营', deleted=False).first()
            if project_operation_role:
                Role2Permission.objects.get_or_create(
                    role=project_operation_role,
                    permission=notification_management,
                    defaults={'deleted': False}
                )
            
            # 将权限分配给客户顾问角色
            account_manager_role = Role.objects.filter(name='客户顾问', deleted=False).first()
            if account_manager_role:
                Role2Permission.objects.get_or_create(
                    role=account_manager_role,
                    permission=notification_management,
                    defaults={'deleted': False}
                )
            
            self.stdout.write(self.style.SUCCESS('Successfully added notification management page'))
