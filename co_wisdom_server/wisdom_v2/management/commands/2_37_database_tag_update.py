from django.core.management.base import BaseCommand
from django.db import transaction
from django.db.models import Q

from wisdom_v2.enum.company_enum import CompanyIndustryEnum
from wisdom_v2.models_file import Tag, TagObject
from wisdom_v2.models import Resume, Company


class Command(BaseCommand):
    help = 'My custom command to run Python file'

    # 新建tag标签
    def handle(self, *args, **options):

        with transaction.atomic():
            # 更新教练简历数据
            resumes = Resume.objects.filter(
                Q(coach_industry__isnull=False) |
                Q(work_industry__isnull=False),
                deleted=False).all()

            for item in resumes:

                def update_industry_item(industry_item):
                    if industry_item == 'IT/互联网':
                        industry_item = '互联网/AI'
                    elif industry_item == '采购贸易':
                        industry_item = '消费品/零售/批发'
                    elif industry_item == '娱乐传媒':
                        industry_item = '广告/传媒/文化/体育'
                    elif industry_item == '法律咨询':
                        industry_item = '专业服务'
                    elif industry_item == '医疗健康':
                        industry_item = '制药/医疗'
                    elif industry_item == '供应链/物流':
                        industry_item = '交通运输/物流'
                    return industry_item

                coach_industry = item.coach_industry
                now_coach_industry = []
                if coach_industry:
                    for coach_industry_item in coach_industry:
                        now_coach_industry_item = update_industry_item(coach_industry_item)
                        now_coach_industry.append(now_coach_industry_item)

                work_industry = item.work_industry
                now_work_industry = []
                if work_industry:
                    for work_industry_item in work_industry:
                        now_work_industry_item = update_industry_item(work_industry_item)
                        now_work_industry.append(now_work_industry_item)

                item.coach_industry = now_coach_industry
                item.work_industry = now_work_industry
                item.save()

            # 更新企业行业数据
            all_company = Company.objects.filter(deleted=False, industry__isnull=False).all()
            for company_item in all_company:
                industry = company_item.industry

                # 金融 -> 金融
                if industry == 1:
                    industry = CompanyIndustryEnum.financial.value

                # 咨询 -> 专业服务
                elif industry == 2:
                    industry = CompanyIndustryEnum.professional_services.value

                # 零售 -> 消费品/零售/批发
                elif industry == 3:
                    industry = CompanyIndustryEnum.consumer_goods_retail_wholesale.value

                # 公用事业/高校, 非营利事业, 政府制造业, 其他 -> 政府/非盈利机构/其它
                elif industry in [4, 10, 11, 17]:
                    industry = CompanyIndustryEnum.government_non_profit_other.value

                # 医疗 -> 制药/医疗
                elif industry == 5:
                    industry = CompanyIndustryEnum.pharmaceutical_medical.value

                # 能源 -> 能源/化工/环保
                elif industry == 6:
                    industry = CompanyIndustryEnum.energy_chemical_environmental.value

                # 服务业 -> 服务业
                elif industry == 7:
                    industry = CompanyIndustryEnum.services.value

                # 媒体, 娱乐 -> 广告/传媒/文化/体育
                elif industry in [8, 9]:
                    industry = CompanyIndustryEnum.advertising_media_culture_sports.value

                # 信息、电信、通信、高科技 改为 电子/通信/半导体
                elif industry in [12, 14, 15, 16]:
                    industry = CompanyIndustryEnum.electronics_communication_semiconductor.value

                # 教育 -> 教育培训
                elif industry == 13:
                    industry = CompanyIndustryEnum.education_training.value

                company_item.industry = industry
                company_item.save()

            parent_tag = Tag.objects.filter(name="所属行业", deleted=False).first()
            # 删除原有旧标签
            Tag.objects.filter(parent=parent_tag, deleted=False).update(deleted=True)

            # 获取新标签的文本列表
            industry_descriptions = CompanyIndustryEnum.choice_dict()
            descriptions_only = list(industry_descriptions.values())

            # 创建新标签，并且将原有旧标签数据更新到新标签
            for item in descriptions_only:
                now_tag = Tag.objects.create(name=item, parent=parent_tag, deleted=False)
                print(f'{item}已成创建')
                count = 0

                if item == "金融":
                    tag_object = TagObject.objects.filter(
                        tag__parent__name="所属行业", deleted=False, tag__name='金融')
                    count = tag_object.count()
                    tag_object.update(tag=now_tag)
                if item == "专业服务":
                    tag_object = TagObject.objects.filter(
                        tag__parent__name="所属行业", deleted=False, tag__name='咨询')
                    count = tag_object.count()
                    tag_object.update(tag=now_tag)
                if item == "消费品/零售/批发":
                    tag_object = TagObject.objects.filter(
                        tag__parent__name="所属行业", deleted=False, tag__name='零售')
                    count = tag_object.count()
                    tag_object.update(tag=now_tag)
                if item == "政府/非盈利机构/其它":
                    tag_object = TagObject.objects.filter(
                        tag__parent__name="所属行业", deleted=False, tag__name__in=['公用事业/高校', '非营利事业', '政府制造业'])
                    count = tag_object.count()
                    tag_object.update(tag=now_tag)
                if item == "制药/医疗":
                    tag_object = TagObject.objects.filter(
                        tag__parent__name="所属行业", deleted=False, tag__name='医疗')
                    count = tag_object.count()
                    tag_object.update(tag=now_tag)
                if item == "能源/化工/环保":
                    tag_object = TagObject.objects.filter(
                        tag__parent__name="所属行业", deleted=False, tag__name='能源')
                    count = tag_object.count()
                    tag_object.update(tag=now_tag)
                if item == "服务业":
                    tag_object = TagObject.objects.filter(
                        tag__parent__name="所属行业", deleted=False, tag__name='服务业')
                    count = tag_object.count()
                    tag_object.update(tag=now_tag)
                if item == "广告/传媒/文化/体育":
                    tag_object = TagObject.objects.filter(
                        tag__parent__name="所属行业", deleted=False, tag__name__in=['媒体', '娱乐'])
                    count = tag_object.count()
                    tag_object.update(tag=now_tag)
                if item == "电子/通信/半导体":
                    tag_object = TagObject.objects.filter(
                        tag__parent__name="所属行业", deleted=False, tag__name__in=['信息', '电信', '通信', '高科技'])
                    count = tag_object.count()
                    tag_object.update(tag=now_tag)
                if item == "教育培训":
                    tag_object = TagObject.objects.filter(
                        tag__parent__name="所属行业", deleted=False, tag__name='教育')
                    count = tag_object.count()
                    tag_object.update(tag=now_tag)
                print(f'更新tag_object标签{count}个')

            to_one_parent_tag = Tag.objects.get(name="职责覆盖的业务体量", deleted=False)
            Tag.objects.create(name='十万', parent=to_one_parent_tag, deleted=False)

            to_two_parent_tag = Tag.objects.get(name="职位", deleted=False)
            Tag.objects.create(name='创业者/合伙人', parent=to_two_parent_tag, deleted=False)


