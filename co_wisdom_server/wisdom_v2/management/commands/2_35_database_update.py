from django.core.management.base import BaseCommand
from django.db import transaction

from wisdom_v2.enum.project_interview_enum import DataType
from wisdom_v2.enum.service_content_enum import OneToOneMatchTypeEnum
from wisdom_v2.models import OneToOneCoach
from wisdom_v2.models_file import ProjectServiceContent


class Command(BaseCommand):
    help = 'My custom command to run Python file'

    # 1-刷新一对一辅导数据类型
    def handle(self, *args, **options):

        with transaction.atomic():
            one_to_one_coach = OneToOneCoach.objects.filter(
                deleted=False, project_bundle__isnull=False).all()

            print(f'需要刷新{len(one_to_one_coach)}条一对一辅导数据')
            one_to_one_coach_count = 0
            for one_to_one_coach_item in one_to_one_coach:
                try:
                    project_type = one_to_one_coach_item.project_bundle.project.type
                    if project_type == 1:  # 5alc
                        one_to_one_coach_item.coach_match_type = OneToOneMatchTypeEnum.appoint_coach.value
                    elif project_type == 2:  # mop
                        one_to_one_coach_item.coach_match_type = OneToOneMatchTypeEnum.all_project_coach.value
                    one_to_one_coach_item.save()
                    one_to_one_coach_count += 1
                except Exception as e:
                    print(f"project_bundle_id:{one_to_one_coach_item.project_bundle_id},project_id:{one_to_one_coach_item.project_bundle.project_id},error:{str(e)}")
            print(f'成功刷新{one_to_one_coach_count}条一对一辅导数据')

            service_content = ProjectServiceContent.objects.filter(
                deleted=False, content_type=DataType.interview.value, content__isnull=False).all()
            print(f'需要刷新{len(service_content)}条服务配置数据')
            service_content_count = 0
            for service_content_item in service_content:
                try:
                    for item in service_content_item.content:
                        if project_type == 1:  # 5alc
                            item['coach_match_type'] = OneToOneMatchTypeEnum.appoint_coach.value
                        elif project_type == 2:  # mop
                            item['coach_match_type'] = OneToOneMatchTypeEnum.all_project_coach.value
                    service_content_item.save()
                    service_content_count += 1
                except Exception as e:
                    print(f"project_id:{service_content_item.project_id}, error: {str(e)}")
            print(f'成功刷新{one_to_one_coach_count}条服务配置数据')
