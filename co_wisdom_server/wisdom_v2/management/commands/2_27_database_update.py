from django.core.management.base import BaseCommand
from django.db import transaction
import openpyxl

from utils import work_wechat
from wisdom_v2.models import PersonalUser, WorkWechatUser, Coach, User
from wisdom_v2.models_file import PublicCourses
from wisdom_v2.models_file.coach import CoachToClientNotes


class Command(BaseCommand):
    help = 'My custom command to run Python file'

    def handle(self, *args, **options):

        with transaction.atomic():

            # 刷新个人用户的邮箱
            personal_user = PersonalUser.objects.filter(
                deleted=False, user__email__isnull=False).all()
            for personal_user_item in personal_user:
                personal_user_item.email = personal_user_item.user.email
                personal_user_item.save()

            # 获取项目中注册的企业微信用户
            work_wechat_user = WorkWechatUser.objects.filter(user_id__isnull=False, wx_user_id__isnull=False, deleted=False)

            for wechat_user_item in work_wechat_user:

                # 可能是教练/项目运营
                # 只有是教练的需要刷新
                coach = Coach.objects.filter(user_id=wechat_user_item.user_id, deleted=False).first()
                if coach:

                    # 查找到教练所有的外部联系人信息
                    is_success, all_external_user_list = work_wechat.WorkWechat().get_external_user_list([wechat_user_item.wx_user_id])
                    for item in all_external_user_list:
                        unionid = item.get('external_contact').get('unionid')
                        name = item.get('external_contact').get('name')
                        remark = item.get('follow_info').get('remark')

                        # 通过uuid匹配用户
                        user = User.objects.filter(unionid=unionid, deleted=False).first()
                        if user:
                            # 更新昵称备注
                            if remark:
                                CoachToClientNotes.objects.create(coach=coach, user=user, notes=remark)

                            # 只有是个人用户才更新昵称
                            personal_user = PersonalUser.objects.filter(user=user).first()
                            if personal_user:
                                personal_user.nickname = name
                                personal_user.save()

        with transaction.atomic():

            wb = openpyxl.load_workbook("public_courses_creator.xlsx")['Sheet1']

            public_courses_creator = {}

            for pos, item in enumerate(wb.rows):
                if all(cell.value is None for cell in item):
                    continue
                if pos == 0:
                    continue
                public_course_id, creator_user_id = item[0].value, item[8].value,

                if public_courses_creator.get(public_course_id):
                    continue
                else:
                    public_courses_creator[public_course_id] = creator_user_id

                if pos == wb.max_row:
                    break

            for public_course_id, creator_user_id in public_courses_creator.items():

                if not creator_user_id:

                    creator_user_id = 1282  # 正式环境管理员账号

                user = User.objects.filter(id=creator_user_id, deleted=False).first()
                if not user:
                    print(f'{public_course_id}对应用户{creator_user_id}不存在')
                    continue

                public_courses = PublicCourses.objects.filter(id=public_course_id, deleted=False).first()
                if not public_courses:
                    print(f'{public_course_id}对应公开课不存在')
                    continue
                public_courses.creator = user
                public_courses.save()
