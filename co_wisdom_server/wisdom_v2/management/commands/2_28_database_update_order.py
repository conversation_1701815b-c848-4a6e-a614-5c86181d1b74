from datetime import datetime

from django.core.management.base import BaseCommand
from django.db import transaction

from wisdom_v2.common import business_settlement_public
from wisdom_v2.enum.business_order_enum import BusinessOrderTypeEnum, BusinessOrderDataTypeEnum, \
    BusinessOrderWithdrawalStatusEnum
from wisdom_v2.enum.project_interview_enum import ProjectInterviewTypeEnum, ProjectInterviewPlaceCategoryEnum
from wisdom_v2.models_file import BusinessOrder, ActivityInterview, BusinessOrder2Object
from wisdom_v2.views.constant import ATTR_STATUS_INTERVIEW_CANCEL


class Command(BaseCommand):
    help = 'My custom command to run Python file'

    def handle(self, *args, **options):

        with transaction.atomic():

            # 历史活动结算订单是可提现状态的，需要改成已提现。
            # 获取到所有的活动id
            interview_ids = list(ActivityInterview.objects.filter(
                interview__deleted=False,  # 辅导还存在
                interview__public_attr__end_time__lt=datetime.now(),  # 辅导时间已结束
                interview__type=ProjectInterviewTypeEnum.formal_interview.value,  # 辅导类型是面谈
                interview__place_category=ProjectInterviewPlaceCategoryEnum.online_one_to_one,  # 一对一辅导
                interview__order__isnull=False, deleted=False,  # 辅导有订单， 并且辅导和活动还关联
                interview__coach_record_status=True,  # 教练已经填写完毕
                interview__public_attr__project__isnull=True  # 没有项目
            ).exclude(
                interview__public_attr__status=ATTR_STATUS_INTERVIEW_CANCEL  # 排除已取消的
            ).values_list('interview_id', flat=True))

            # 获取到对应的结算关联订单
            business_order_obj_all = BusinessOrder2Object.objects.filter(
                business_order__type=BusinessOrderTypeEnum.personal.value,  # 订单类型是个人
                business_order__withdrawal_status=BusinessOrderWithdrawalStatusEnum.can_withdraw.value,  # 提现状态是可提现
                data_type=BusinessOrderDataTypeEnum.interview,  # 数据来源是辅导
                deleted=False,  # 没有被删除
                object_id__in=interview_ids  # 适配活动辅导id列表
            )

            for business_order_obj in business_order_obj_all:
                # 将对象转成查询集
                settlement_business_order = BusinessOrder.objects.filter(pk=str(business_order_obj.business_order.pk))
                if settlement_business_order:
                    business_settlement_public.generate_business_settlements(
                        business_order_obj.business_order.coach, settlement_business_order, business_order_obj.business_order.coach.user)
            print(f'历史活动结算订单状态修改完成，修改数量: {business_order_obj_all.count()}')
