import re

from django.core.management.base import BaseCommand
from django.db import transaction

from wisdom_v2.common import customer_portrait_public
from wisdom_v2.enum.chemical_interview_enum import ChemicalInterviewStatusEnum
from wisdom_v2.models import CustomerPortrait
from wisdom_v2.models_file import ChemicalInterview2Coach


class Command(BaseCommand):
    help = 'My custom command to run Python file'

    # 1-更新用户画像信息
    def handle(self, *args, **options):

        with transaction.atomic():
            # 查询全部的用户画像（群体画像无需刷新）
            # 更新字段“客户管理经验”和“客户风格及特点”到“其他信息”，
            # 如果是教练-对应的客户画像字段追加更新化学面谈记录的信息
            all_customer_portrait = CustomerPortrait.objects.filter(deleted=False, user_id__isnull=False).all()

            for customer_portrait in all_customer_portrait:
                user = customer_portrait.user

                # 更新字段“客户管理经验”和“客户风格及特点”到“其他信息。
                if customer_portrait.customer_experience:
                    if customer_portrait.coach_extra:
                        customer_portrait.coach_extra += f'\n{customer_portrait.customer_experience}'
                    else:
                        customer_portrait.coach_extra = f'{customer_portrait.customer_experience}'

                if customer_portrait.customer_style:
                    if customer_portrait.coach_extra:
                        customer_portrait.coach_extra += f'\n{customer_portrait.customer_style}'
                    else:
                        customer_portrait.coach_extra = f'{customer_portrait.customer_style}'

                if customer_portrait.customer_age and not user.age:
                    # 正则表达式模式，用于匹配任何数字序列
                    pattern = r'\d+'
                    # 使用findall方法查找所有匹配项
                    digits = re.findall(pattern, customer_portrait.customer_age)
                    # 将找到的数字字符串连接起来
                    customer_age = ''.join(digits)
                    if customer_age:
                        user.age = int(customer_age)

                if customer_portrait.gender and not user.gender:
                    user.gender = customer_portrait.gender

                customer_portrait.save()
                user.save()

            # 如果教练和客户有已选择的化学面谈，客户画像对应字段追加化学面谈记录信息
            all_chemical_interview = ChemicalInterview2Coach.objects.filter(
                deleted=False,
                chemical_interview_status=ChemicalInterviewStatusEnum.selected.value
            ).all()
            for chemical_interview in all_chemical_interview:
                customer_portrait_public.chemical_interview_update_customer_portrait(chemical_interview.interview)

