from datetime import datetime

from django.core.management.base import BaseCommand
from django.db import transaction

from wisdom_v2.models_file import Tag, TagObject, ProjectTagConfig
from wisdom_v2.models_file.tag import TagRequirementConfig


class Command(BaseCommand):
    help = 'My custom command to run Python file'

    # 新建tag标签
    def handle(self, *args, **options):

        with transaction.atomic():
            to_one_parent_tag = Tag.objects.get(name="职位", deleted=False)
            Tag.objects.create(name='员工', parent=to_one_parent_tag, deleted=False)

            to_two_parent_tag = Tag.objects.get(name="管理挑战", deleted=False)
            Tag.objects.create(name='人才培养', parent=to_two_parent_tag, deleted=False)
