from django.core.management.base import BaseCommand
from django.db import transaction

from wisdom_v2.enum.user_enum import UserTmpEnum, PosterTypeEnum
from wisdom_v2.models import UserTmp
from wisdom_v2.models_file import Poster


class Command(BaseCommand):
    help = 'My custom command to run Python file'

    # 1-清理教练简历页面和教练入住信息填写页面的缓存。防止信息展示不全。
    # 2-新增一条见习项目海报数据。
    def handle(self, *args, **options):

        with transaction.atomic():
            user_tmp = UserTmp.objects.filter(
                type__in=[UserTmpEnum.resume.value, UserTmpEnum.entrant.value],
            ).exclude(extra_id=2)  # 排除见习教练的入驻缓存
            print(f'删除的数据数量：{user_tmp.count()}')
            user_tmp.delete()

            poster = Poster.objects.create(name='04', type=PosterTypeEnum.internship_project.value, auto_id=4)
            print(f'新增海报数据成功：{poster}')
