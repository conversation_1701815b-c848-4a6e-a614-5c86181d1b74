from django.core.management.base import BaseCommand
from django.db import transaction
from django.utils import timezone

from data.models import AppGrowthdiary, SysUser
from wisdom_v2.models import User, Coach, Diary, PublicAttr
from wisdom_v2.views.constant import ATTR_TYPE_DIARY


class Command(BaseCommand):
    help = 'My custom command to run Python file'  # 自定义命令的帮助信息

    # 更新教练旧版成长笔记到新版
    def handle(self, *args, **options):
        with transaction.atomic():  # 事务管理，确保操作的原子性
            # 查询app_growthdiary表的数据
            app_growthdiary_records = AppGrowthdiary.objects.filter(
                Coach_Id__in=SysUser.objects.filter(
                    PhoneNo__in=User.objects.values_list('phone', flat=True)
                ).values_list('User_Id', flat=True)
            )
            # 遍历查询结果，为每个有v2_coach数据的app_growthdiary对象在Diary表创建一条数据
            count = 0
            for item in app_growthdiary_records:
                sys_user = SysUser.objects.get(User_Id=item.Coach_Id)  # 通过User_Id找到对应的SysUser
                print(f"{sys_user.UserTrueName}-{sys_user.PhoneNo}")
                try:
                    v2_user = User.objects.get(phone=sys_user.PhoneNo)  # 通过PhoneNo找到对应的v2用户
                    Coach.objects.get(user_id=v2_user.id)  # 确保对应的v2用户在Coach表中存在
                except (User.DoesNotExist, Coach.DoesNotExist):
                    print(f"{sys_user.UserTrueName}-{sys_user.PhoneNo}未找到对应用户信息")
                    continue
                # 创建PublicAttr对象
                public_attr = PublicAttr.objects.create(
                    type=ATTR_TYPE_DIARY,
                    user_id=v2_user.id,
                )
                # 创建Diary对象
                Diary.objects.create(
                    public_attr=public_attr,
                    content=item.Remark,
                    creator_role=6,  # 教练创建
                    status=2,  # 已完成
                    deleted=False,
                    created_at=timezone.now(),
                    updated_at=timezone.now()
                )
                count += 1
            print(f"成功更新{count}条数据")
