import math
from decimal import Decimal

from django.core.management.base import BaseCommand
from django.db import transaction

from wisdom_v2.enum.business_order_enum import BusinessOrderTypeEnum, BusinessOrderWithdrawalStatusEnum
from wisdom_v2.models_file import BusinessOrder
from wisdom_v2.views.constant import NON_PLATFORM_SERVICE_SCALE, PLATFORM_SERVICE_SCALE, ORDER_TAX_POINT


class Command(BaseCommand):
    help = 'My custom command to run Python file'

    # 刷新C端辅导订单数据
    def handle(self, *args, **options):

        with transaction.atomic():
            business_order = BusinessOrder.objects.filter(
                type=BusinessOrderTypeEnum.personal.value, deleted=False,
                withdrawal_status=BusinessOrderWithdrawalStatusEnum.can_withdraw.value
            )
            count = 0

            for item in business_order.all():
                member_paid = item.member_paid
                tax_point = ORDER_TAX_POINT
                
                # 根据已有的服务费匹配新版服务费
                if int(item.platform_scale) == 5:
                    platform_service_scale = NON_PLATFORM_SERVICE_SCALE
                elif int(item.platform_scale) == 25:
                    platform_service_scale = PLATFORM_SERVICE_SCALE
                else:
                    print(f'订单{item.pk}平台服务费不匹配：{item.platform_scale}')
                    continue

                # 金额计算
                platform_service_amount = math.floor(
                    member_paid * (Decimal(platform_service_scale) / Decimal('100')))
                tax_amount = math.floor(member_paid * (Decimal(tax_point) / Decimal('100')))
                real_income = math.ceil(member_paid - platform_service_amount - tax_amount)

                item.platform_scale = platform_service_scale  # 平台扣除比例
                item.platform_service_amount = platform_service_amount  # 平台服务费

                item.tax_point = tax_point  # 税点
                item.tax_amount = tax_amount  # 税费

                item.coach_actual_income = real_income  # 实际收益
                item.save()
                print(f'订单{item.pk}更新成功')
                count += 1
            print(f'总共{count}个订单更新成功')

