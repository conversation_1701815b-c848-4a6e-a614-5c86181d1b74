from datetime import datetime

from django.core.management.base import BaseCommand
import openpyxl
from django.db import transaction
from wisdom_v2.models import Coach, User

from wisdom_v2.enum.business_order_enum import BusinessOrderDurationTypeEnum, BusinessOrderTypeEnum, \
    WorkTypeEnum, BusinessOrderPayStatusEnum, BusinessOrderSettlementStatusEnum, BusinessOrderWithdrawalStatusEnum
from wisdom_v2.models_file.business_order import BusinessOrder

from wisdom_v2.models_file.business_settlement import BusinessSettlement

from wisdom_v2.enum.business_order_enum import SettlementUserTypeEnum


class Command(BaseCommand):
    help = 'My custom command to run Python file'

    def handle(self, *args, **options):
        # Call your function or execute desired functionality here
        path = '/Users/<USER>/Desktop/order6.xlsx'
        # Load the XLSX file
        workbook = openpyxl.load_workbook(path)

        # Get the first sheet
        sheet = workbook.active
        user = User.objects.get(pk=1282)
        # Loop through each row and column and print the content
        with transaction.atomic():
            for row in sheet.iter_rows(values_only=True):
                coach_name, type, work_type, company_name, company_short, project_name, member_name, is_on_site, work_time,\
                   work_during, unit_price, total, tax_rate, real_total, pay_date = row[0], row[1], row[2], row[3], row[4],\
                    row[5], row[6], row[7], row[8], row[9], row[10], row[11], row[12], row[14], row[15]
                # print('current row', row)
                if coach_name == '教练姓名':
                    # print('coach name is header')
                    continue
                if not Coach.objects.filter(user__true_name=coach_name).exists():
                    print('###coach not exist with name:', coach_name)
                    continue

                coach = Coach.objects.get(user__true_name=coach_name)

                platform_scale = 0
                member_paid = 0
                platform_service_amount = 0

                extra_info = {
                    'company_name': company_short,
                    'project_name': project_name,
                    'member_name': member_name if member_name else '',
                    'work_time': work_time
                }
                # 工作时长 work_during = 6小时 2天
                if '天' in work_during:
                    duration = work_during.replace('天', '')
                    duration_type = BusinessOrderDurationTypeEnum.day.value
                else:
                    duration = work_during.replace('小时', '')
                    duration_type = BusinessOrderDurationTypeEnum.hour.value

                # 教练单价 unit_price = '1000元/小时', coach_price = 1000
                coach_price = int(unit_price[:unit_price.index("元")]) * 100 if unit_price else 0
                try:
                    coach_actual_income = float(real_total.replace(',', '')) * 100 if real_total else None
                except ValueError as e:
                    print(e)  # Output: invalid literal for float(): 'not a number'
                    continue

                tax_point = round(100 * (total - coach_actual_income/100) / total, 1) if coach_actual_income else 0
                tax_amount = total * 100 - coach_actual_income if coach_actual_income else 0

                pay_datetime = datetime.strptime(pay_date, "%Y-%m-%d") if pay_date and pay_date != '未支付' else None
                pay_time = pay_datetime if pay_datetime else None

                if pay_time:
                    pay_status = BusinessOrderPayStatusEnum.paid.value
                    withdrawal_status = BusinessOrderWithdrawalStatusEnum.withdrawn.value
                else:
                    pay_status = BusinessOrderPayStatusEnum.non.value
                    withdrawal_status = BusinessOrderWithdrawalStatusEnum.non_withdrawable.value

                if type == '企业项目':
                    order_type = BusinessOrderTypeEnum.enterprise.value
                elif type == '公开课':
                    order_type = BusinessOrderTypeEnum.public_course.value
                elif type == '个人':
                    order_type = BusinessOrderTypeEnum.personal.value
                    platform_scale = 25
                    member_paid = total * 100

                if work_type == '一对一辅导':
                    order_work_type = WorkTypeEnum.one_to_one.value
                elif work_type == '工作坊':
                    order_work_type = WorkTypeEnum.group_coach.value
                elif work_type == '授课':
                    order_work_type = WorkTypeEnum.teaching.value
                elif work_type == '小组辅导' or work_type == '公开课-组内辅导':
                    order_work_type = WorkTypeEnum.group_counseling.value
                elif work_type == '利益相关者访谈':
                    order_work_type = WorkTypeEnum.stakeholder_interview.value

                order_data = {
                    "platform_scale": platform_scale,
                    "type": order_type, "work_type": order_work_type,
                    "coach": coach, "coach_price": coach_price, "duration": duration,
                    "duration_type": duration_type,
                    "tax_point": tax_point, "tax_amount": tax_amount, "coach_actual_income": coach_actual_income,
                    "pay_status": pay_status,
                    "settlement_status": BusinessOrderSettlementStatusEnum.unsettled,
                    "withdrawal_status": withdrawal_status,
                    "extra_info": extra_info,
                }
                print(order_data)
                business_order = BusinessOrder.objects.create(**order_data)
                print('###order create###')
                # 如果已经支付就生成结算单
                if pay_datetime:
                    enterprise_exists, public_course_exists, personal_exists = False, False, False
                    if business_order.type == BusinessOrderTypeEnum.personal:
                        personal_exists = True
                    elif business_order.type == BusinessOrderTypeEnum.enterprise:
                        enterprise_exists = True
                    elif business_order.type == BusinessOrderTypeEnum.public_course:
                        public_course_exists = True

                    if enterprise_exists and not public_course_exists and not personal_exists:
                        user_type = SettlementUserTypeEnum.enterprise
                    elif not enterprise_exists and not personal_exists and public_course_exists:
                        user_type = SettlementUserTypeEnum.enterprise
                    elif not enterprise_exists and not public_course_exists and personal_exists:
                        user_type = SettlementUserTypeEnum.personal
                    else:
                        user_type = SettlementUserTypeEnum.others

                    apply_withdrawal_amount = coach_actual_income



                    business_settlement = BusinessSettlement.objects.create(
                        user_type=user_type, apply_withdrawal_amount=apply_withdrawal_amount, member_paid=member_paid,
                        apply_time=datetime.now(), platform_service_amount=platform_service_amount,
                        tax_amount=tax_amount, create_user=user, settlement_time=datetime.now(),
                        settlement_status=BusinessOrderSettlementStatusEnum.settled, coach=coach)

                    business_order.business_settlement = business_settlement
                    business_order.withdrawal_status = BusinessOrderWithdrawalStatusEnum.withdrawn
                    business_order.save()


        # Close the workbook
        workbook.close()