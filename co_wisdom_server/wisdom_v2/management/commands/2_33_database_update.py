import os
import jwt

import pendulum
import redis

from django.core.management.base import BaseCommand
from django.db import transaction
from django.conf import settings

from file_read_backwards import FileReadBackwards

from utils import utc_date_time
from wisdom_v2.enum.service_content_enum import UserInviteTypeEnum
from wisdom_v2.enum.user_enum import BrowseRecordObjectEnum
from wisdom_v2.models_file.coach import BrowseRecord

data_redis = redis.Redis.from_url(settings.DATA_REDIS)

class Command(BaseCommand):
    help = 'My custom command to run Python file'

    # 刷新教练简历信息
    def handle(self, *args, **options):

        with transaction.atomic():

            # 获取目录下所有文件
            path_list = os.listdir(settings.LOG_PATH)
            # 删除gz结尾的压缩文件和非all_log的其他日志文件
            path_list = [item for item in path_list if
                         'gz' not in item.split('.')[-1] and 'all_log' in item.split('.')[0]]

            days_start_datetime = pendulum.now().subtract(days=30)
            # 排序读取最新文件
            path_list.sort()
            resume_data = {}
            for log_path in path_list:
                # 拼接需要读取的完整文件路径
                file_path = f'{settings.LOG_PATH}/{log_path}'
                with FileReadBackwards(file_path, encoding="utf-8") as BigFile:
                    for item in BigFile:
                        log_datetime, log_data_str = pendulum.from_format(
                            item[6:16], 'YYYY-MM-DD', tz='Asia/Shanghai'), item[30:]
                        log_data = eval(log_data_str)
                        if 'app/resume/detail' in log_data.get('url', ''):
                            coach_user_id = log_data.get('response_body', {}).get('data', {}).get('user_id')
                            resume_id = log_data.get('response_body', {}).get('data', {}).get('id')
                            token = log_data.get('token')
                            if token:
                                try:
                                    data = jwt.decode(token[7:], settings.SECRET_KEY, algorithms=['HS256'])
                                    user_id = data.get('id')
                                except Exception as e:
                                    user_id = None
                            else:
                                user_id = None

                            # 非本人本人查看，简历查看次数增加
                            if user_id != coach_user_id:
                                if resume_data.get(f'{coach_user_id}_{resume_id}'):
                                    resume_data[f'{coach_user_id}_{resume_id}'][0] += 1
                                else:
                                    resume_data[f'{coach_user_id}_{resume_id}'] = [1, 0]

                                # 30天内的日志，增加缓存
                                if days_start_datetime.date() <= log_datetime.date():
                                    date = log_datetime.to_date_string()
                                    redis_key = f"browse_record_user:{coach_user_id}:resume:{resume_id}:date:{date}"
                                    # 如果已经有值-自增。
                                    if data_redis.get(redis_key):
                                        data_redis.incr(redis_key)
                                    else:
                                        # 设置过期时间为30天后的0点0分0秒
                                        days = int(30 - (pendulum.now() - log_datetime).days)
                                        if days:
                                            data_redis.set(redis_key, 1, ex=days * 24 * 60 * 60)
                        if 'app/user/invite/add' in log_data.get('url', '') and log_data.get('request_body', {}).get('type') == UserInviteTypeEnum.coach_resume.value:
                            coach_user_id = log_data.get('request_body', {}).get('object_id')
                            user_id = log_data.get('request_body', {}).get('user_id')
                            resume_id = log_data.get('response_body', {}).get('data', {}).get('resume_id')

                            # 非本人本人分享，分享次数增加
                            if user_id != coach_user_id:
                                if resume_data.get(f'{coach_user_id}_{resume_id}'):
                                    resume_data[f'{coach_user_id}_{resume_id}'][1] += 1
                                else:
                                    resume_data[f'{coach_user_id}_{resume_id}'] = [0, 1]
                print(f"{file_path}文件读取完成")
                print(resume_data)

            add_browse_record = []
            for k, v in resume_data.items():
                coach_user_id, resume_id = k.split('_')
                total_views, total_shares = v
                add_browse_record.append(
                    BrowseRecord(
                        user_id=coach_user_id,
                        object_type=BrowseRecordObjectEnum.resume.value,
                        object_id=resume_id,
                        total_views=total_views,
                        total_shares=total_shares
                    )
                )
            BrowseRecord.objects.bulk_create(add_browse_record)







