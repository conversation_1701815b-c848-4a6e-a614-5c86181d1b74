from django.core.management.base import BaseCommand
from django.db import transaction

from wisdom_v2.enum.project_interview_enum import ProjectInterviewPlaceCategoryEnum, InterviewSubjectEnum, \
    ProjectInterviewPlaceTypeEnum
from wisdom_v2.models import ProjectInterview


class Command(BaseCommand):
    help = 'My custom command to run Python file'

    # 1-集体辅导，ProjectInterview辅导地点类型place_type默认线下
    # 2-一对一辅导，ProjectInterview辅导形式interview_subject从目标约谈改为一对一辅导
    def handle(self, *args, **options):

        with transaction.atomic():
            # 集体辅导，ProjectInterview辅导地点类型默认线下
            ProjectInterview.objects.filter(
                place_category=ProjectInterviewPlaceCategoryEnum.offline_group_coach.value, deleted=False
            ).update(place_type=ProjectInterviewPlaceTypeEnum.offline.value)

            # 一对一辅导，ProjectInterview辅导形式interview_subject从目标约谈改为一对一辅导
            ProjectInterview.objects.filter(
                interview_subject=InterviewSubjectEnum.goal_oriented.value, deleted=False
            ).update(interview_subject=InterviewSubjectEnum.regular.value)

