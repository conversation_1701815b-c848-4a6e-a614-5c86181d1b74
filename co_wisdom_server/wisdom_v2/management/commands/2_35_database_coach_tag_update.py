from django.core.management.base import BaseCommand
from django.db import transaction
from django.db.models import Q

from wisdom_v2.common import coach_public
from wisdom_v2.models import Resume

class Command(BaseCommand):
    help = 'My custom command to run Python file'

    # 更新教练tag标签
    def handle(self, *args, **options):

        with transaction.atomic():

            # 三个标签数据，查询满足任意一个条件的教练。
            coach_ids = Resume.objects.filter(
                Q(coach_course__isnull=False) |  # 受训经验
                Q(coach_auth__isnull=False) |  # 教练资质
                Q(coach__user__gender__isnull=False),  # 性别
                is_customization=False, deleted=False
            ).values_list('coach_id', flat=True).distinct()

            print(f'需要刷新{len(coach_ids)}数据')
            count = 0
            for coach_id in coach_ids:
                try:
                    coach_public.update_resume_to_coach_tag(coach_id)
                    count += 1
                except Exception as e:
                    print(f'教练：{coach_id}数据标签刷新失败，error：{str(e)}')
            print(f'刷新成功{count}条数据')
