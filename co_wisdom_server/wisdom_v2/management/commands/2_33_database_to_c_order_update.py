from django.core.management.base import BaseCommand
from django.db import transaction

from wisdom_v2.enum.business_order_enum import BusinessOrderDataTypeEnum, WorkTypeEnum
from wisdom_v2.enum.pay_enum import OrderStatusEnum
from wisdom_v2.enum.service_content_enum import ActivityTypeEnum
from wisdom_v2.models_file import BusinessOrder2Object
from wisdom_v2.models import ProjectInterview
from wisdom_v2.views.constant import ATTR_STATUS_INTERVIEW_CANCEL


class Command(BaseCommand):
    help = 'My custom command to run Python file'

    # 刷新公益活动结算单的抽成数据
    def handle(self, *args, **options):

        with transaction.atomic():
            project_interview_ids = ProjectInterview.objects.filter(
                order__activity__type=ActivityTypeEnum.public_welfare_coach.value, order__deleted=False,
                order__status=OrderStatusEnum.paid.value, deleted=False
            ).exclude(public_attr__status=ATTR_STATUS_INTERVIEW_CANCEL).values_list('id', flat=True)

            business_order_2_object = BusinessOrder2Object.objects.filter(
                object_id__in=project_interview_ids,data_type=BusinessOrderDataTypeEnum.interview.value,
                type=WorkTypeEnum.one_to_one.value, deleted=False,
            )

            for item in business_order_2_object:
                if item.business_order:
                    business_order = item.business_order
                    member_paid = business_order.member_paid
                    business_settlement = business_order.business_settlement
                    if business_settlement:
                        platform_service_amount = business_settlement.platform_service_amount - business_order.platform_service_amount
                        tax_amount = business_settlement.tax_amount - business_order.tax_amount

                        business_settlement.platform_service_amount = platform_service_amount if platform_service_amount > 0 else 0
                        business_settlement.tax_amount = tax_amount if tax_amount > 0 else 0
                        business_settlement.save()
                    business_order.platform_scale = 0
                    business_order.platform_service_amount = 0
                    business_order.tax_point = 0
                    business_order.tax_amount = 0
                    business_order.coach_actual_income = member_paid
                    business_order.save()
