from django.core.management.base import BaseCommand
from django.db import transaction

from wisdom_v2.common import evaluation_public
from wisdom_v2.enum.service_content_enum import EvaluationWriteRoleEnum, EvaluationReportTypeEnum
from wisdom_v2.models import Evaluation
from wisdom_v2.models_file.evaluation import EvaluationReportConfig
from wisdom_v2.views.constant import LBI_EVALUATION, MANAGE_EVALUATION


class Command(BaseCommand):
    help = 'My custom command to run Python file'

    # 新增lbi自评模板
    def handle(self, *args, **options):

        with transaction.atomic():
            coach_report_evaluation = Evaluation.objects.filter(code=MANAGE_EVALUATION, deleted=False).first()
            EvaluationReportConfig.objects.create(
                evaluation=coach_report_evaluation,
                type=EvaluationReportTypeEnum.manage_evaluation_report.value,
            )
            print(f'{coach_report_evaluation.name}新增测评报告配置')

            lbi_evaluation = Evaluation.objects.filter(code=LBI_EVALUATION, deleted=False).first()
            EvaluationReportConfig.objects.create(
                evaluation=lbi_evaluation,
                type=EvaluationReportTypeEnum.lbi_personal_report.value,
            )
            print(f'{lbi_evaluation.name}新增测评报告配置')

            lbi_self_evaluation = evaluation_public.copy_evaluation(
                lbi_evaluation, 'LBI领导力行为指数评估（自评）', EvaluationWriteRoleEnum.coachee.value)
            print(f'新增测评：{lbi_self_evaluation.name}')
            EvaluationReportConfig.objects.create(
                evaluation=lbi_self_evaluation,
                type=EvaluationReportTypeEnum.lbi_self_evaluation_report.value,
            )
            print(f'{lbi_self_evaluation.name}新增测评报告配置')
