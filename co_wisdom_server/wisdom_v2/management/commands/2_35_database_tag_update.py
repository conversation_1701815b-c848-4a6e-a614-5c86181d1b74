from datetime import datetime

from django.core.management.base import BaseCommand
from django.db import transaction

from wisdom_v2.models_file import Tag, TagObject, ProjectTagConfig
from wisdom_v2.models_file.tag import TagRequirementConfig


class Command(BaseCommand):
    help = 'My custom command to run Python file'

    # 更新tag标签
    # 处理需要删除的基础标签
    # 处理需要删除的父标签
    # 修已有标签名
    # 创建新标签
    def handle(self, *args, **options):

        with transaction.atomic():
            update_parent_name = "管理能力挑战"
            del_parent_name = "领导力发展方向"

            update_parent_tag = Tag.objects.get(name=update_parent_name, deleted=False)
            del_parent_tag = Tag.objects.get(name=del_parent_name, deleted=False)

            # 处理需要删除的基础标签
            del_base_tag_name = ['管理团队', '人际沟通', '建立流程机制', '个人影响力', '视野格局 ']
            del_base_tag = Tag.objects.filter(
                name__in=del_base_tag_name, parent__name__in=[update_parent_name, del_parent_name], deleted=False)

            # 转换成id列表。方便后续关联数据删除时使用
            del_base_tag_id = del_base_tag.values_list('id', flat=True)

            # 删除项目需求配置标签
            ProjectTagConfig.objects.filter(tag_object__tag_id__in=del_base_tag_id, deleted=False).update(deleted=True)
            # 删除项目需求标签，教练经验标签
            TagObject.objects.filter(tag_id__in=del_base_tag_id, deleted=False).update(deleted=True)
            # 删除基础标签
            del_base_tag.update(deleted=True)

            # 处理需要删除的父标签
            # 1-移动子标签
            move_base_tag_name = ['战略眼光', '系统整合', '市场敏锐度', '跨文化领导力']

            for item in move_base_tag_name:
                Tag.objects.filter(
                    name=item, parent__name=del_parent_name, deleted=False
                ).update(parent=update_parent_tag, created_at=datetime.now())  # 更新创建时间时为了排序

            # 2-删除父标签和需求配置标签数据
            TagRequirementConfig.objects.filter(tag=del_parent_tag, deleted=False).update(deleted=True)
            del_parent_tag.deleted = True
            del_parent_tag.save()

            # 修已有标签名
            Tag.objects.filter(name='激励员工', parent__name=update_parent_name, deleted=False).update(name='激励下属')
            Tag.objects.filter(name='培养下属', parent__name=update_parent_name, deleted=False).update(name='绩效辅导')
            Tag.objects.filter(name='合理授权', parent__name=update_parent_name, deleted=False).update(name='有效授权')
            Tag.objects.filter(name='果断决策', parent__name=update_parent_name, deleted=False).update(name='果敢决策')
            Tag.objects.filter(name='横向协作', parent__name=update_parent_name, deleted=False).update(name='多方协同')
            Tag.objects.filter(name='战略眼光', parent__name=update_parent_name, deleted=False).update(name='战略远见')
            Tag.objects.filter(name='系统整合', parent__name=update_parent_name, deleted=False).update(name='系统思考')
            Tag.objects.filter(name='市场敏锐度', parent__name=update_parent_name, deleted=False).update(name='领导变革')
            Tag.objects.filter(name='跨文化领导力', parent__name=update_parent_name, deleted=False).update(name='跨文化管理')

            # 创建新标签
            Tag.objects.create(name='开拓创新', parent=update_parent_tag, deleted=False)
            Tag.objects.create(name='应对复杂局面', parent=update_parent_tag, deleted=False)

            # 修改父标签名称
            update_parent_tag.name = '管理挑战'
            update_parent_tag.save()
