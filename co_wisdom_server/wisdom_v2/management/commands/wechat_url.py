from django.core.management.base import BaseCommand

from utils.wechat_oauth import WeChatMiniProgram


class Command(BaseCommand):
    help = 'My custom command to run Python file'

    def add_arguments(self, parser):
        parser.add_argument('--page', type=str, help='页面路径')
        parser.add_argument('--scene', type=str, help='场景值')
        
    def handle(self, *args, **options):

        page = options.get('page')
        scene = options.get('scene')
        print(f"Generating short link for page: {page}, scene: {scene}")
        # 生成短链接
        url_state, short_link = WeChatMiniProgram().get_url_link(page, scene)
        print(f"URL generation status: {url_state}")
        print(f"Generated short link: {short_link}")

