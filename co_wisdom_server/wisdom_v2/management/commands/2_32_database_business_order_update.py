from django.core.management.base import BaseCommand
from django.db import transaction

from wisdom_v2.common import business_settlement_public
from wisdom_v2.enum.business_order_enum import BusinessOrderTypeEnum, BusinessOrderSettlementStatusEnum
from wisdom_v2.models_file import BusinessOrder, BusinessSettlement


class Command(BaseCommand):
    help = 'My custom command to run Python file'  # 自定义命令的帮助信息

    # 线上0元订单标记结算。
    def handle(self, *args, **options):
        with transaction.atomic():  # 事务管理，确保操作的原子性
            # 查询C端，支付金额是0，未结算的订单
            business_order = BusinessOrder.objects.filter(
                type=BusinessOrderTypeEnum.personal.value, member_paid=0, deleted=False,
                settlement_status=BusinessOrderSettlementStatusEnum.unsettled.value).all()
            for item in business_order:

                # 如果没有结算单，改为已提现并生成结算单（标记成已提现和生成结算单是同步绑定关系）
                if not item.business_settlement_id:
                    business_settlement_public.generate_business_settlements(item.coach, business_order,
                                                                             item.coach.user)
                    print(f"{item.coach.user.true_name} 教练的 {item.work_start_time.strftime('%Y-%m-%d %H:%M')}-{item.work_end_time.strftime('%H:%M')} 辅导提现")

                # 标记结算状态
                business_settlements = BusinessSettlement.objects.filter(
                    business_order=business_order.first(), deleted=False).all()
                business_settlement_public.mark_settlement_status(business_settlements, item.coach.user)
                print(f"{item.coach.user.true_name} 教练的 {item.work_start_time.strftime('%Y-%m-%d %H:%M')}-{item.work_end_time.strftime('%H:%M')} 辅导结算")
