from django.core.management.base import BaseCommand
from django.db import transaction

from wisdom_v2.common import customer_portrait_public
from wisdom_v2.enum.business_order_enum import BusinessOrderTypeEnum, BusinessOrderDataTypeEnum, WorkTypeEnum
from wisdom_v2.models import ProjectNote, CustomerPortrait
from wisdom_v2.models_file import BusinessOrder2Object


class Command(BaseCommand):
    help = 'My custom command to run Python file'

    def handle(self, *args, **options):

        with transaction.atomic():

            # 查询所有未删除且关联到辅导的项目笔记
            exist_note = ProjectNote.objects.filter(interview__isnull=False, deleted=False).all()

            for item in exist_note:
                try:
                    # 从每条笔记关联的辅导中提取教练、被辅导者
                    coach_user = item.interview.public_attr.user
                    customer_user = item.interview.public_attr.target_user
                    project_id = item.interview.public_attr.project_id
                    # 更新画像信息
                    customer_portrait_public.update_customer_portrait_coach_extra(coach_user, customer_user, item.content, project_id)
                except Exception as e:
                    print(f'exist_note: {item.pk}, {str(e)}')

            # 查询所有的结算订单关联表data_type为空的
            business_order_2_object = BusinessOrder2Object.objects.filter(
                deleted=False, data_type__isnull=True).all()
            for item in business_order_2_object:
                try:
                    business_order = item.business_order
                    # 赋值公开课类型
                    if business_order.type == BusinessOrderTypeEnum.public_course.value:
                        item.data_type = BusinessOrderDataTypeEnum.public_course.value

                    # 企业来源并且是工作坊和小组辅导的的，刷新成集体辅导类型
                    elif business_order.type == BusinessOrderTypeEnum.enterprise.value and business_order.work_type in [WorkTypeEnum.group_coach.value, WorkTypeEnum.group_counseling.value]:
                        item.data_type = BusinessOrderDataTypeEnum.group_coach.value

                    # 剩余的一对一辅导，利益相关者访谈，刷新成辅导类型
                    else:
                        item.data_type = BusinessOrderDataTypeEnum.interview.value
                    item.save()
                except Exception as e:
                    print(f'business_order_2_object: {item.pk}, {str(e)}')
