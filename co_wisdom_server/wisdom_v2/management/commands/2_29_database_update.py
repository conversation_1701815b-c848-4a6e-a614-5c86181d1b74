from django.core.management.base import BaseCommand
from django.db import transaction
from wisdom_v2.models import Order, Order2Commodity, Coach


class Command(BaseCommand):
    help = 'My custom command to run Python file'

    # 历史订单对应的public_attr没有target_user_id，这类订单如果出现取消/退款操作，需要在辅导列表中展示，
    # 辅导列表中获取教练信息的时候会报错，所以需要刷新历史订单的public_attr.target_user字段
    def handle(self, *args, **options):

        with transaction.atomic():
            # 查询所有public_attr没有target_user_id的订单
            order = Order.objects.filter(public_attr__target_user__isnull=True, deleted=False)

            update_count = 0
            for item in order:
                try:
                    public_attr = item.public_attr

                    # 获取订单和商品的关联表
                    order_to_commodity = Order2Commodity.objects.filter(order=item).first()
                    if order_to_commodity:

                        # 获取商品信息
                        commodity = order_to_commodity.commodity
                        # 获取教练信息
                        coach = Coach.objects.filter(id=commodity.object_id, deleted=False).first()
                        if coach:
                            # 修改订单对应的public_attr的target_user_id字段
                            public_attr.target_user_id = coach.user_id
                            public_attr.save()
                            update_count += 1
                        else:
                            print(f"更新失败：{item.order_no}对应商品{commodity.object_id}不存在教练数据")
                    else:
                        print(f"更新失败：{item.order_no}对应商品不存在")
                except Exception as e:
                    print(f"更新失败：{item.order_no}，错误信息：{e}")
            print(f"更新数量：{update_count}")

