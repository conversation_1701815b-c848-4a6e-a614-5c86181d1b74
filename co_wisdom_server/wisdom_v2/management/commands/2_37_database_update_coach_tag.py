import openpyxl

from django.core.management.base import BaseCommand
from django.db import transaction
from wisdom_v2.enum.service_content_enum import TagObjectTypeEnum
from wisdom_v2.models_file import TagObject, Tag


class Command(BaseCommand):
    help = 'My custom command to run Python file'

    # 新建tag标签
    def handle(self, *args, **options):

        with transaction.atomic():

            add_tag_object_list = []

            # 读取Excel文件
            background_file_path = '教练背景数据.xlsx'
            background_wb = openpyxl.load_workbook(background_file_path)
            background_ws = background_wb.active

            # 获取第一行的教练ID
            background_coach_ids = [cell.value for cell in background_ws[1][2:26]]

            background_count = 0
            print('创建教练背景标签')
            # 遍历数据行，从第二行开始
            for row in background_ws.iter_rows(min_row=2, max_col=27, values_only=True):
                tag_id = row[-2]
                tag_name = row[1]

                for col_index, cell_value in enumerate(row[2:26]):
                    if cell_value == '√':  # 检查是否有对勾
                        coach_info = background_coach_ids[col_index]

                        coach_name, coach_id = coach_info.split('｜')

                        print(coach_id, coach_name, tag_name, tag_id)

                        add_tag_object_list.append(
                            TagObject(
                                tag_id=tag_id,
                                object_id=coach_id,
                                object_type=TagObjectTypeEnum.coach.value))
                        background_count += 1

            base_tag = Tag.objects.create(name="教练经验标签", deleted=False)
            tag_object = TagObject.objects.create(tag=base_tag, object_id="", object_type=TagObjectTypeEnum.coach.value)

            # 读取Excel文件
            experience_file_path = '教练经验数据.xlsx'
            experience_wb = openpyxl.load_workbook(experience_file_path)
            experience_ws =experience_wb.active

            # 获取第一行的教练ID
            experience_coach_ids = [cell.value for cell in background_ws[1][2:26]]

            experience_count = 0

            print('创建教练经验标签')
            # 遍历数据行，从第二行开始
            for row in experience_ws.iter_rows(min_row=2, max_col=27, values_only=True):
                tag_id = row[-2]
                tag_name = row[1]

                for col_index, cell_value in enumerate(row[2:26]):
                    if cell_value == '√':  # 检查是否有对勾
                        coach_info = experience_coach_ids[col_index]

                        coach_name, coach_id = coach_info.split('｜')

                        print(coach_id, coach_name, tag_name, tag_id)

                        add_tag_object_list.append(
                            TagObject(
                                tag_id=tag_id,
                                object_id=coach_id,
                                object_type=TagObjectTypeEnum.coach.value,
                                source=tag_object
                            ))
                        experience_count += 1
            TagObject.objects.bulk_create(add_tag_object_list)

            print(f"成功创建{background_count}个背景标签")
            print(f"成功创建{experience_count}个经验标签")
            print(f"总共创建{experience_count + background_count}个标签")
