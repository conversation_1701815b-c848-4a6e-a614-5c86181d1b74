from django.core.management.base import BaseCommand
from django.db import transaction

from wisdom_v2.enum.user_enum import PosterTypeEnum
from wisdom_v2.models_file import Poster


class Command(BaseCommand):
    help = 'My custom command to run Python file'

    # 新增2条见习项目海报数据。
    def handle(self, *args, **options):

        with transaction.atomic():
            poster_6 = Poster.objects.create(name='06', type=None, auto_id=6)
            print(f'新增海报数据成功：{poster_6}')
            poster_7 = Poster.objects.create(name='07', type=None, auto_id=7)
            print(f'新增海报数据成功：{poster_7}')

