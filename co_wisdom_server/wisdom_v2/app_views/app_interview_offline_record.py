from drf_yasg import openapi
from rest_framework.decorators import action
from django.db import transaction
from rest_framework.viewsets import GenericViewSet

from utils.messagecenter import center
from drf_yasg.utils import swagger_auto_schema
from .app_coachee_action_plan import ActionPlanSerializer
from .app_coachee_habit import HabitSerializer
from ..common import interview_public
from ..models import ProjectInterview, PublicAttr, ActionPlan, Diary, Habit,  \
    ProjectInterviewOffLineRecord
from .app_interview_record_actions import ProjectInterviewOffLineRecordSerializers
from utils.api_response import success_response, parameter_error_response
from ..views.constant import ATTR_TYPE_DIARY, DIARY_FROM_RECORD, ROLE_COACHEE, ATTR_TYPE_HABIT, \
    ATTR_TYPE_ACTIONPLAN,  BUNDLE_INTEWVIEW_TYPE_OFFLINE_GROUP


def send_scoring_risk_alert_email_pre(harvest_score, satisfaction_score, interview_id, target_progress=None):

    describe = '投入度打{}分、满意度打{}分'.format(
        harvest_score,
        satisfaction_score,
    )
    if target_progress:
        if target_progress < 7 or harvest_score < 7 or satisfaction_score < 7:
            describe = '有效度打{}分、'.format(target_progress) + describe
            center.send_scoring_risk_alert_email.delay(
                describe=describe,
                interview_id=interview_id,
                message_type='scoring_risk_alert')
    else:
        if harvest_score < 7 or satisfaction_score < 7:
            center.send_scoring_risk_alert_email.delay(
                describe=describe,
                interview_id=interview_id,
                message_type='scoring_risk_alert')


def save_public_attr(project,  save_type, user=None, target_user=None):
    return PublicAttr.objects.create(project=project, user=user, type=save_type, target_user=target_user)


def save_trainee_member_public_attr(save_type, user=None, target_user=None):
    return PublicAttr.objects.create(user=user, type=save_type, target_user=target_user)


class AppOfflineInterviewRecordViewSet(GenericViewSet):
    queryset = ProjectInterviewOffLineRecord.objects.all().order_by('-created_at')
    serializer_class = ProjectInterviewOffLineRecordSerializers

    @swagger_auto_schema(
        operation_id='app线下集体辅导约谈记录详情',
        operation_summary='app线下集体辅导约谈记录详情',
        manual_parameters=[
            openapi.Parameter('interview_id', openapi.IN_QUERY, description='约谈id', type=openapi.TYPE_NUMBER, required=True)
        ],
        tags=['app约谈记录相关']
    )
    @action(methods=['get'], detail=False, url_path='detail')
    def interview_record_detail(self, request, *args, **kwargs):
        interview_id = request.query_params.get('interview_id', 0)
        instance = ProjectInterviewOffLineRecord.objects.filter(interview_id=interview_id,
                                                                deleted=False).order_by('-updated_at').first()
        serializer = ProjectInterviewOffLineRecordSerializers(instance)
        data = serializer.data
        return success_response(data, request=request)



    @swagger_auto_schema(
        operation_id='app线下辅导创建约谈记录',
        operation_summary='app线下辅导创建约谈记录',
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'interview_id': openapi.Schema(type=openapi.TYPE_NUMBER, description='约谈id'),
                'coachee_thought': openapi.Schema(type=openapi.TYPE_STRING, description='最大收获/成长笔记'),
                'coachee_change': openapi.Schema(type=openapi.TYPE_ARRAY, description='新的习惯/习惯养成',
                                                 items=openapi.Schema(type=openapi.TYPE_OBJECT)),
                'resistance': openapi.Schema(type=openapi.TYPE_STRING, description='阻力'),
                'action_plan': openapi.Schema(type=openapi.TYPE_ARRAY, description='解决阻力/行动计划-Q2.3',
                                              items=openapi.Schema(type=openapi.TYPE_OBJECT)),
                'change': openapi.Schema(type=openapi.TYPE_ARRAY, description='行为转变/行动计划-Q3.1',
                                              items=openapi.Schema(type=openapi.TYPE_OBJECT)),
                'input_score': openapi.Schema(type=openapi.TYPE_NUMBER, description='投入度评分'),
                'coach_score': openapi.Schema(type=openapi.TYPE_NUMBER, description='被教给教练的评价分'),
                'coach_comment': openapi.Schema(type=openapi.TYPE_STRING, description='被教给教练的评价'),
                'satisfaction_score': openapi.Schema(type=openapi.TYPE_NUMBER, description='满意度评分')
            }
        ),
        tags=['app约谈记录相关']
    )
    @action(methods=['post'], detail=False, url_path='add')
    def offline_interview_record_add(self, request, *args, **kwargs):
        try:
            interview_id = int(request.data.get('interview_id', 0))
            coachee_thought = request.data.get('coachee_thought', None)
            coachee_change = request.data.get('coachee_change', [])
            resistance = request.data.get('resistance', None)
            action_plan = request.data.get('action_plan', [])
            change = request.data.get('change', [])
            input_score = request.data.get('input_score', 0)
            satisfaction_score = request.data.get('satisfaction_score', 0)
            interview = ProjectInterview.objects.get(pk=interview_id)

        except Exception as e:
            return parameter_error_response()
        if interview.place_category != BUNDLE_INTEWVIEW_TYPE_OFFLINE_GROUP:
            return parameter_error_response('约谈id错误')

        try:
            with transaction.atomic():
                record = ProjectInterviewOffLineRecord.objects.filter(interview_id=interview_id, deleted=False).first()
                if record:
                    return parameter_error_response('约谈记录已存在')
                else:
                    save_serializer = ProjectInterviewOffLineRecordSerializers
                    serializer = save_serializer(data=request.data)
                    serializer.is_valid(raise_exception=True)
                    coach_record_obj = serializer.save()
                    coach_record_obj.interview = interview
                    coach_record_obj.save()
                # 最大收获/成长笔记
                if coachee_thought:
                    public_attr = save_public_attr(project=interview.public_attr.project,
                                                   user=interview.public_attr.target_user,
                                                   save_type=ATTR_TYPE_DIARY)
                    Diary.objects.create(content=coachee_thought, type=DIARY_FROM_RECORD, public_attr=public_attr,
                                         interview=interview, creator_role=ROLE_COACHEE)

                # 新的习惯/习惯养成
                if coachee_change:
                    for coachee_item in coachee_change:
                        public_attr = save_public_attr(project=interview.public_attr.project,
                                                       user=interview.public_attr.target_user,
                                                       save_type=ATTR_TYPE_HABIT)
                        Habit.objects.create(interview=interview, public_attr=public_attr,
                                             when=coachee_item['when'],
                                             stop=coachee_item['stop'], change=coachee_item['change'],
                                             start_date=coachee_item['start_date'],
                                             end_date=coachee_item['end_date'],
                                             creator_role=ROLE_COACHEE)
                # 解决阻力/行动计划-Q2.3
                if action_plan:
                    for coachee_plan in action_plan:
                        public_attr = save_public_attr(project=interview.public_attr.project,
                                                       user=interview.public_attr.target_user,
                                                       save_type=ATTR_TYPE_ACTIONPLAN)
                        ActionPlan.objects.create(interview=interview, public_attr=public_attr,
                                                  content=coachee_plan['content'],
                                                  end_date=coachee_plan['end_date'],
                                                  creator_role=ROLE_COACHEE,
                                                  is_problem_resolve=True)

                # 行为转变/行动计划-Q3.1
                if change:
                    for plan in change:
                        public_attr = save_public_attr(project=interview.public_attr.project,
                                                       user=interview.public_attr.target_user,
                                                       save_type=ATTR_TYPE_ACTIONPLAN)
                        ActionPlan.objects.create(interview=interview, public_attr=public_attr,
                                                  content=plan['content'],
                                                  end_date=plan['end_date'],
                                                  creator_role=ROLE_COACHEE,
                                                  is_problem_resolve=False)

                interview_public.fill_record(interview, is_coach=False)
                coach_record_obj.save()

                # 教练填写不发送邮件
                if input_score or satisfaction_score:
                    send_scoring_risk_alert_email_pre(
                        input_score,
                        satisfaction_score,
                        interview_id)
                    return success_response(request=request)
        except Exception as e:
            return parameter_error_response()

    @swagger_auto_schema(
        operation_id='app线下辅导修改约谈记录',
        operation_summary='app线下辅导修改约谈记录',
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'interview_id': openapi.Schema(type=openapi.TYPE_NUMBER, description='约谈id'),
                'coachee_thought': openapi.Schema(type=openapi.TYPE_STRING, description='最大收获/成长笔记'),
                'coachee_change': openapi.Schema(type=openapi.TYPE_ARRAY, description='新的习惯/习惯养成',
                                                 items=openapi.Schema(type=openapi.TYPE_OBJECT)),
                'resistance': openapi.Schema(type=openapi.TYPE_STRING, description='阻力'),
                'action_plan': openapi.Schema(type=openapi.TYPE_ARRAY, description='解决阻力/行动计划-Q2.3',
                                              items=openapi.Schema(type=openapi.TYPE_OBJECT)),
                'change': openapi.Schema(type=openapi.TYPE_ARRAY, description='行为转变/行动计划-Q3.1',
                                         items=openapi.Schema(type=openapi.TYPE_OBJECT)),
                'input_score': openapi.Schema(type=openapi.TYPE_NUMBER, description='投入度评分'),
                'satisfaction_score': openapi.Schema(type=openapi.TYPE_NUMBER, description='满意度评分'),
                'coach_score': openapi.Schema(type=openapi.TYPE_NUMBER, description='被教给教练的评价分'),
                'coach_comment': openapi.Schema(type=openapi.TYPE_STRING, description='被教给教练的评价'),
            }
        ),
        tags=['app约谈记录相关']
    )
    @action(methods=['post'], detail=False, url_path='update')
    def offline_interview_record_update(self, request, *args, **kwargs):
        try:
            interview_id = int(request.data.get('interview_id', 0))
            coachee_thought = request.data.get('coachee_thought', None)
            coachee_change = request.data.get('coachee_change', [])
            resistance = request.data.get('resistance', None)
            action_plan = request.data.get('action_plan', [])
            change = request.data.get('change', [])
            input_score = request.data.get('input_score', 0)
            satisfaction_score = request.data.get('satisfaction_score', 0)
            interview = ProjectInterview.objects.get(pk=interview_id)
            record = ProjectInterviewOffLineRecord.objects.get(interview=interview)

        except Exception as e:
            return parameter_error_response()
        if interview.place_category != BUNDLE_INTEWVIEW_TYPE_OFFLINE_GROUP:
            return parameter_error_response('约谈id错误')

        try:
            with transaction.atomic():
                serializer = ProjectInterviewOffLineRecordSerializers(record, data=request.data, partial=True)
                serializer.is_valid(raise_exception=True)
                coach_record_obj = serializer.save()
                # 最大收获/成长笔记
                if coachee_thought:
                    diary = Diary.objects.filter(interview=interview,
                                                 creator_role=ROLE_COACHEE, deleted=False
                                                 ).order_by('-updated_at').first()
                    diary.content = coachee_thought
                    diary.deleted = False
                    diary.save()


                # 新的习惯/习惯养成
                if coachee_change:
                    # # 修改
                    exist_change = Habit.objects.filter(interview=interview, creator_role=ROLE_COACHEE, deleted=False,
                                                        public_attr__user=interview.public_attr.target_user)
                    change_list = list(exist_change.values_list('id', flat=True))
                    for coachee_item in coachee_change:
                        if coachee_item.get('id'):
                            change_obj = exist_change.get(pk=coachee_item.get('id'))
                            serializer_change = HabitSerializer(change_obj, data=coachee_item)
                            serializer_change.is_valid(raise_exception=True)
                            serializer_change.save()
                            # 从现有数组删除对象，剩余的是需要删除的
                            change_list.remove(change_obj.id)
                        else:
                            # 新的内容
                            public_attr = save_public_attr(project=interview.public_attr.project,
                                                           user=interview.public_attr.target_user,
                                                           save_type=ATTR_TYPE_HABIT)
                            Habit.objects.create(interview=interview, public_attr=public_attr,
                                                 when=coachee_item['when'],
                                                 stop=coachee_item['stop'], change=coachee_item['change'],
                                                 start_date=coachee_item['start_date'],
                                                 end_date=coachee_item['end_date'],
                                                 creator_role=ROLE_COACHEE)
                    # 没有上传的id需要删除
                    exist_change.filter(pk__in=change_list).update(deleted=True)
                # 解决阻力/行动计划-Q2.3
                if action_plan:
                    exist_list = ActionPlan.objects.filter(interview=interview, creator_role=ROLE_COACHEE,
                                                           deleted=False,
                                                           public_attr__user=interview.public_attr.target_user,
                                                           is_problem_resolve=True)
                    id_list = list(exist_list.values_list('id', flat=True))
                    for coachee_plan in action_plan:
                        if coachee_plan.get('id'):
                            exist_obj = exist_list.get(pk=coachee_plan.get('id'))
                            serializer_obj = ActionPlanSerializer(exist_obj, data=coachee_plan)
                            serializer_obj.is_valid(raise_exception=True)
                            serializer_obj.save()
                            # 从现有数组删除对象，剩余的是需要删除的
                            id_list.remove(exist_obj.id)
                        else:
                            # 新的内容
                            public_attr = save_public_attr(project=interview.public_attr.project,
                                                           user=interview.public_attr.target_user,
                                                           save_type=ATTR_TYPE_ACTIONPLAN)
                            ActionPlan.objects.create(interview=interview, public_attr=public_attr,
                                                      content=coachee_plan['content'],
                                                      end_date=coachee_plan['end_date'],
                                                      creator_role=ROLE_COACHEE,
                                                      is_problem_resolve=True)
                    #   没有上传的id需要删除
                    exist_list.filter(pk__in=id_list).update(deleted=True)

                # 行为转变/行动计划-Q3.1
                if change:
                    # 修改
                    plan_list = ActionPlan.objects.filter(interview=interview, creator_role=ROLE_COACHEE,
                                                          deleted=False,
                                                          public_attr__user=interview.public_attr.target_user,
                                                          is_problem_resolve=False)
                    plan_id_list = list(plan_list.values_list('id', flat=True))
                    for plan in change:
                        if plan.get('id'):
                            exist_obj = plan_list.get(pk=plan.get('id'))
                            serializer_obj = ActionPlanSerializer(exist_obj, data=plan)
                            serializer_obj.is_valid(raise_exception=True)
                            serializer_obj.save()
                            # 从现有数组删除对象，剩余的是需要删除的
                            plan_id_list.remove(exist_obj.id)
                        else:
                            #     新的内容
                            public_attr = save_public_attr(project=interview.public_attr.project,
                                                           user=interview.public_attr.target_user,
                                                           save_type=ATTR_TYPE_ACTIONPLAN)
                            ActionPlan.objects.create(interview=interview, public_attr=public_attr,
                                                      content=plan['content'],
                                                      end_date=plan['end_date'],
                                                      creator_role=ROLE_COACHEE,
                                                      is_problem_resolve=False)
                    #   没有上传的id需要删除
                    plan_list.filter(pk__in=plan_id_list).update(deleted=True)
                # 教练填写不发送邮件
                if input_score or satisfaction_score:
                    send_scoring_risk_alert_email_pre(
                        coach_record_obj.input_score,
                        coach_record_obj.satisfaction_score,
                        interview_id)
                return success_response(request=request)
        except Exception as e:
            return parameter_error_response()