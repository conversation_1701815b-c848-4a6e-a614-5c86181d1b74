import datetime
from drf_yasg import openapi
from drf_yasg.utils import swagger_auto_schema
from rest_framework.decorators import action
from django.db.models import Q

from rest_framework import serializers
from rest_framework import viewsets

from wisdom_v2.models import LearnArticle, Article

from utils.pagination import StandardResultsSetPagination
from utils.api_response import success_response, parameter_error_response


class ArticleSerializer(serializers.ModelSerializer):
    created_at = serializers.DateTimeField(format='%Y-%m-%d %H:%M')
    read_time = serializers.SerializerMethodField()

    class Meta:
        model = Article
        exclude = ('updated_at', 'deleted', 'enabled')

    # 文章数字÷400字/分钟
    def get_read_time(self, obj):
        return int(len(obj.content) / 400) or 1


class LearnArticleSerializer(serializers.ModelSerializer):
    tag = serializers.CharField(source='article.tag')
    created_at = serializers.DateTimeField(format='%Y-%m-%d %H:%M')
    article = ArticleSerializer()
    tag_type = serializers.SerializerMethodField()
    capacity_tag = serializers.SerializerMethodField()


    class Meta:
        model = LearnArticle
        exclude = ('updated_at', 'deleted', 'public_attr')

    def get_tag_type(self, obj):
        return 3

    def get_capacity_tag(self, obj):
        if obj.capacity_tag:
            return obj.capacity_tag.title
        return




class LearnArticleViewSet(viewsets.ModelViewSet):
    queryset = LearnArticle.objects.filter(deleted=False).order_by('-created_at')
    serializer_class = LearnArticleSerializer

    @swagger_auto_schema(
        operation_id='app被教拓展学习列表',
        operation_summary='app被教拓展学习列表',
        manual_parameters=[
            openapi.Parameter('user_id', openapi.IN_QUERY, description='用户id', type=openapi.TYPE_NUMBER,
                              required=True),
            openapi.Parameter('page', openapi.IN_QUERY, description='第几页', type=openapi.TYPE_NUMBER),
            openapi.Parameter('page_size', openapi.IN_QUERY, description='每页显示条数默认15条', type=openapi.TYPE_NUMBER)
        ],
        tags=['app被教拓展学习']
    )
    def list(self, request, *args, **kwargs):
        user_id = request.query_params.get('user_id')
        queryset = self.get_queryset().filter(public_attr__user__pk=user_id, deleted=False)

        # 分页
        paginator = StandardResultsSetPagination()
        page_list = paginator.paginate_queryset(queryset, self.request)
        serializer = self.serializer_class(page_list, many=True, context={'queryset': queryset})
        response = paginator.get_paginated_response(serializer.data)
        return success_response(response, request=request)

    @swagger_auto_schema(
        operation_id='app被教拓展学习详情',
        operation_summary='app被教拓展学习详情',
        manual_parameters=[
            openapi.Parameter('action_plan_id', openapi.IN_QUERY, description='拓展学习id',
                              type=openapi.TYPE_NUMBER, required=True)
        ],
        tags=['app被教拓展学习']
    )
    @action(methods=['get'], detail=False, url_path='detail')
    def action_plan_detail(self, request, *args, **kwargs):
        try:
            instance = LearnArticle.objects.get(pk=request.query_params.get('action_plan_id', 0), deleted=False)
        except LearnArticle.DoesNotExist:
            return parameter_error_response('拓展学习id错误')
        serializer = self.get_serializer(instance)
        return success_response(serializer.data, request=request)

    @swagger_auto_schema(
        operation_id='app被教修改拓展学习',
        operation_summary='app被教修改拓展学习',
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'action_plan_id': openapi.Schema(type=openapi.TYPE_NUMBER, description='拓展学习id'),
                'content': openapi.Schema(type=openapi.TYPE_STRING, description='内容'),
                'status': openapi.Schema(type=openapi.TYPE_NUMBER, description='1: 进行中 2:已完成 3:已取消'),
            }
        ),
        tags=['app被教拓展学习']
    )
    @action(methods=['post'], detail=False, url_path='update')
    def learn_article_update(self, request, *args, **kwargs):
        try:
            instance = LearnArticle.objects.get(pk=request.data.get('action_plan_id'), deleted=False)
        except LearnArticle.DoesNotExist:
            return parameter_error_response('拓展学习不存在')
        data = request.data.copy()
        serializer = self.get_serializer(instance, data=data, partial=True)
        serializer.is_valid(raise_exception=True)
        serializer.save()
        return success_response(serializer.data, request=request)


    @swagger_auto_schema(
        operation_id='app被教删除拓展学习',
        operation_summary='app被教删除拓展学习',
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'learn_article_id': openapi.Schema(type=openapi.TYPE_NUMBER, description='拓展学习id'),
            }
        ),
        tags=['app被教拓展学习']
    )
    @action(methods=['post'], detail=False, url_path='delete')
    def learn_article_delete(self, request, *args, **kwargs):
        try:
            instance = LearnArticle.objects.get(pk=request.data.get('action_plan_id'), deleted=False)
        except LearnArticle.DoesNotExist:
            return parameter_error_response('拓展学习不存在')
        instance.deleted = True
        instance.save()
        return success_response(request=request)
