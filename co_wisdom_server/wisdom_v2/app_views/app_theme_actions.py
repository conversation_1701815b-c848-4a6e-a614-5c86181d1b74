from rest_framework import serializers
from wisdom_v2.models import Theme, Article, ArticleThemeRelation, Resume, Coach


class AppThemeSerializer(serializers.ModelSerializer):
    id = serializers.CharField(read_only=True)
    name = serializers.CharField(read_only=True, help_text='主题名称')
    brief = serializers.CharField(read_only=True, help_text='主题简介')
    image_url = serializers.CharField(read_only=True, help_text='主题图片')
    article = serializers.SerializerMethodField(read_only=True, help_text='文章列表')
    coach = serializers.SerializerMethodField(read_only=True, help_text='教练列表')

    class Meta:
        model = Theme
        fields = ('id', 'name', 'brief', 'image_url', 'article', 'coach')

    def get_article(self, obj):
        article_list = Article.objects.filter(
            deleted=False, article_theme_relation__theme=obj, enabled=True,
            article_theme_relation__deleted=False).distinct().order_by('-article_theme_relation__weight', '-created_at')
        data = []
        for article in article_list:
            tmp_data = {"id": article.id, 
                        "title": article.title, 
                        "image_url": article.image_url, 
                        'brief': article.brief,
                        "category": article.category, 
                        "is_video": True if "<video" in article.content else False,
                        "type": 1
                        }
            data.append(tmp_data)
        return data

    def get_coach(self, obj):
        coach_list = Coach.objects.filter(
            deleted=False, coach_article_relation__theme=obj,
            coach_article_relation__deleted=False).distinct().order_by('-coach_article_relation__weight', '-created_at')
        data = []
        for coach in coach_list:
            resume = Resume.objects.filter(coach=coach, deleted=False, is_customization=False).first()
            tmp_data = {"id": coach.id, "true_name": coach.user.cover_name, "city": coach.city, "resume_id": resume.id,
                        "head_image_url": resume.head_image_url if resume.head_image_url else coach.user.head_image_url,
                        "price": coach.display_price, "domain": resume.coach_domain[:3] if resume.coach_domain else []}

            data.append(tmp_data)
        return data
