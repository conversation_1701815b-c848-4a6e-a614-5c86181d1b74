import datetime

from drf_yasg import openapi

from rest_framework.decorators import action
from drf_yasg.utils import swagger_auto_schema

from wisdom_v2.enum.pay_enum import StockBusinessTypeEnum, StockTypesEnum, CouponStatusEnum, StockStatusEnum
from wisdom_v2.models import Coupon, Stock, User
from rest_framework import viewsets

from utils.api_response import success_response, parameter_error_response
from wisdom_v2.app_views.app_coupon_action import AppCouponSerializer, send_coupon
from utils import task


class CouponViewSet(viewsets.ModelViewSet):
    queryset = Coupon.objects.filter(deleted=False)
    serializer_class = AppCouponSerializer

    @swagger_auto_schema(
        operation_id='首页优惠券查询',
        operation_summary='首页优惠券查询',
        manual_parameters=[
            openapi.Parameter(
                'user_id', openapi.IN_QUERY, description='用户id', type=openapi.TYPE_NUMBER, required=True),
            openapi.Parameter(
                'type', openapi.IN_QUERY, description='优惠券类型 1-新人礼', type=openapi.TYPE_NUMBER, required=True),
        ],
        tags=['小程序优惠券相关']
    )
    @action(methods=['get'], detail=False, url_path='gift', authentication_classes=[])
    def gift_select(self, request, *args, **kwargs):
        try:
            user_id = request.query_params.get('user_id', 0)
            # user = User.objects.get(pk=request.query_params.get('user_id'))
            type = int(request.query_params.get('type', 0))
        except:
            return parameter_error_response()

        data = {"coupon": None}
        now = datetime.datetime.now()

        if type == 1:  # 新人礼
            # 查询是否有新人礼批次优惠券
            stock = Stock.objects.filter(deleted=False, stock_type=StockTypesEnum.DISCOUNT,
                                         available_begin_time__lt=now,
                                         available_end_time__gt=now,
                                         status=StockStatusEnum.running,
                                         type=StockBusinessTypeEnum.new_gift)
            if stock.exists():   # 存在批次新人礼优惠券
                if user_id:
                    try:
                        user = User.objects.get(pk=user_id)
                    except:
                        return success_response(data)
                    coupon_used = Coupon.objects.filter(deleted=False,
                                                        user_id=user_id,
                                                        stock__type=StockBusinessTypeEnum.new_gift,
                                                        status=CouponStatusEnum.USED).first()
                    if coupon_used:
                    # 使用过新人券，不再发放
                        pass
                    else:
                        coupon = Coupon.objects.filter(deleted=False, user_id=user.pk,
                                                       stock__stock_type=StockTypesEnum.DISCOUNT,
                                                       stock__available_begin_time__lt=now,
                                                       stock__available_end_time__gt=now,
                                                       stock__status=StockStatusEnum.running,
                                                       stock__type=StockBusinessTypeEnum.new_gift
                                                    )
                        if not coupon.exists():  # 当前没有发放过新人礼，返回新人礼代金券批次信息
                            stock = stock.order_by('-created_at').first()
                            data['coupon'] = {
                                                "id": stock.pk,
                                                "stock_id": stock.stock_id,
                                                "stock_name": stock.stock_name,
                                                "transaction_minimum": None,
                                                "coupon_amount": str(int(stock.discount/10)),
                                                "image": None,
                                                "is_send": False
                                            }
                        # 领取过返回优惠券领取的优惠券信息
                        else:
                            coupon = coupon.filter(status=CouponStatusEnum.SENDED).first()
                            if coupon:
                                stock = coupon.stock
                                data['coupon'] = {
                                                    "id": stock.pk,
                                                    "stock_id": stock.stock_id,
                                                    "stock_name": stock.stock_name,
                                                    "transaction_minimum": None,
                                                    "coupon_amount": str(int(stock.discount/10)),
                                                    "image": None,
                                                    "is_send": True
                                                }

                else:  # 没有登录返回优惠券信息
                    stock = stock.order_by('-created_at').first()
                    data['coupon'] = {
                        "id": stock.pk,
                        "stock_id": stock.stock_id,
                        "stock_name": stock.stock_name,
                        "transaction_minimum": None,
                        "coupon_amount": str(int(stock.discount / 10)),
                        "image": None,
                        "is_send": True
                    }
        return success_response(data)

    @swagger_auto_schema(
        operation_id='发放代金券',
        operation_summary='发放代金券',
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'id': openapi.Schema(type=openapi.TYPE_STRING, description='批次代金券id(当前系统内)'),
                'user_id': openapi.Schema(type=openapi.TYPE_STRING, description='用户id'),
                'type': openapi.Schema(type=openapi.TYPE_NUMBER, description='券类型 1-折扣 2-满减')
            }
        ),
        tags=['小程序优惠券相关']
    )
    def create(self, request, *args, **kwargs):
        try:
            stock = Stock.objects.get(pk=request.data.get('id'))
            user = User.objects.get(pk=request.data.get('user_id'))
            type = int(request.data['type'])
        except:
            return parameter_error_response()
        if not user.openid:
            return parameter_error_response('当前用户未绑定微信，请先绑定微信后领取')
        # 发放优惠券
        is_send, msg = send_coupon(stock, user, type)
        if not is_send:
            return parameter_error_response(msg)
        return success_response()

    @swagger_auto_schema(
        operation_id='同步代金券信息',
        operation_summary='同步代金券信息',
        manual_parameters=[
        ],
        tags=['小程序优惠券相关']
    )
    @action(methods=['get'], detail=False, url_path='synchronization')
    def synchronization(self, request, *args, **kwargs):
        task.synchronization_stock_coupon.delay()
        return success_response()