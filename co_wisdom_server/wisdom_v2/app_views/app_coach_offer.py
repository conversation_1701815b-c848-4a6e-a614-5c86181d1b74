from drf_yasg import openapi
from rest_framework.decorators import action
from drf_yasg.utils import swagger_auto_schema
from rest_framework import viewsets

from wisdom_v2.app_views.app_coach_offer_actions import AppCoachOfferSerializer, AppCoachOfferUpdateSerializer
from wisdom_v2.enum.service_content_enum import CoachOfferStatusEnum
from wisdom_v2.models import CoachOffer
from utils.api_response import success_response, parameter_error_response


class AppCoachOfferViewSet(viewsets.ModelViewSet):
    queryset = CoachOffer.objects.filter(deleted=False).order_by('-created_at')
    serializer_class = AppCoachOfferSerializer

    @swagger_auto_schema(
        operation_id='小程序端教练offer详情',
        operation_summary='小程序端教练offer详情',
        manual_parameters=[
            openapi.Parameter('offer_id', openapi.IN_QUERY, description='offer_id', type=openapi.TYPE_STRING),
        ],
        tags=['后台项目offer']
    )
    @action(methods=['get'], detail=False, url_path='detail')
    def project_offer_detail(self, request, *args, **kwargs):
        try:
            offer_id = request.query_params.get('offer_id')
            coach_offer = CoachOffer.objects.get(id=offer_id)
        except CoachOffer.DoesNotExist:
            return parameter_error_response()
        serializer = self.get_serializer(coach_offer)
        return success_response(serializer.data)


    @swagger_auto_schema(
        operation_id='小程序端编辑教练offer',
        operation_summary='小程序端编辑教练offer',
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'offer_id': openapi.Schema(type=openapi.TYPE_STRING, description='offer_id'),
                'max_customer_count': openapi.Schema(type=openapi.TYPE_NUMBER, description='最大服务客户数量'),
                'status': openapi.Schema(type=openapi.TYPE_NUMBER, description='状态'),
                'refuse_reason': openapi.Schema(type=openapi.TYPE_NUMBER, description='拒绝原因'),
            }
        ),
        tags=['后台项目offer']
    )
    @action(methods=['post'], detail=False, url_path='update', serializer_class=AppCoachOfferUpdateSerializer)
    def edit_coach_offer(self, request, *args, **kwargs):
        try:
            offer_id = request.data['offer_id']
            coach_offer = CoachOffer.objects.get(id=offer_id, deleted=False)
        except CoachOffer.DoesNotExist:
            return parameter_error_response()
        if coach_offer.status == CoachOfferStatusEnum.expired:
            return parameter_error_response('当前邀请已超时')
        data = request.data
        serializer = self.get_serializer(data=data)
        serializer.is_valid(raise_exception=True)
        validated_data = serializer.validated_data
        return success_response(serializer.update_offer(validated_data))

