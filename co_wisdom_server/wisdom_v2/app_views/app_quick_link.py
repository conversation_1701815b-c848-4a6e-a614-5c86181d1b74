import json

from drf_yasg import openapi
from drf_yasg.utils import swagger_auto_schema
from rest_framework import viewsets

from wisdom_v2.views.quick_link_action import QuickLinkSerializer
from wisdom_v2.models import QuickLink
from utils.pagination import StandardResultsSetPagination
from utils.api_response import success_response
from wisdom_v2.enum.service_content_enum import QuickLinkStatusEnum


class APPQuickLinkViewSet(viewsets.ModelViewSet):
    queryset = QuickLink.objects.filter(deleted=False,
                                        status=QuickLinkStatusEnum.shelf).order_by('display_position', '-created_at')
    serializer_class = QuickLinkSerializer
    authentication_classes = []

    @swagger_auto_schema(
        operation_id='导航列表',
        operation_summary='导航列表',
        manual_parameters=[
            openapi.Parameter('page', openapi.IN_QUERY, description='第几页', type=openapi.TYPE_NUMBER),
            openapi.Parameter('page_size', openapi.IN_QUERY, description='每页显示条数默认15条', type=openapi.TYPE_NUMBER),
        ],
        tags=['首页金刚区']
    )
    def list(self, request, *args, **kwargs):

        quick_link = self.get_queryset()
        suitable_object = request.query_params.get('suitable_object', None)
        if suitable_object:
            quick_link = quick_link.filter(suitable_object=suitable_object, image_url__isnull=False)
        else:
            # 获取创建时间 最早的三条数据
            quick_links = list(QuickLink.objects.filter(
                deleted=False, status=QuickLinkStatusEnum.shelf
            ).order_by('created_at')[:3])

            # 按照display_position排序
            quick_link = sorted(quick_links, key=lambda ql: ql.display_position)

            request.GET._mutable = True
            self.request.query_params['page_size'] = 3

        # 分页
        paginator = StandardResultsSetPagination()
        page_list = paginator.paginate_queryset(quick_link, self.request)
        serializer = self.get_serializer(page_list, many=True)
        response = paginator.get_paginated_response(serializer.data)
        return success_response(response)