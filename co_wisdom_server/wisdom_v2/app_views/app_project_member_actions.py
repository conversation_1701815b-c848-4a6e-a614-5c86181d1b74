

from rest_framework import serializers
from ..models import User, ProjectMember


def get_user_role(user_id, project_id):
    try:
        return ProjectMember.objects.filter(user_id=user_id, project_id=project_id, deleted=False).first().role
    except Exception as e:
        return None



class AppUserSerializers(serializers.ModelSerializer):

    class Meta:
        model = User
        exclude = ('created_at', 'updated_at')
