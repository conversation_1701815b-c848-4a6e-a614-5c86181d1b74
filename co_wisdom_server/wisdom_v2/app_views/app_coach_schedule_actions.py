import datetime

from rest_framework import serializers
from wisdom_v2.models import Schedule, ProjectInterview, GroupCoach
from wisdom_v2.views.constant import SCHEDULE_TYPE_INTERVIEW, \
    SCHEDULE_PLATFORM_DEFAULT, SCHEDULE_PLATFORM_V1
from wisdom_v2.enum.pay_enum import OrderStatusEnum
from wisdom_v2.enum.project_interview_enum import ProjectInterviewPlaceCategoryEnum, ProjectInterviewTypeEnum, \
    GroupCoachTypeEnum
from wisdom_v2.enum.service_content_enum import ScheduleTypeEnum, ScheduleApplyTypeEnum
from wisdom_v2.enum.chemical_interview_enum import ChemicalInterviewStatusEnum
from wisdom_v2.enum.schedule_enum import RecurringScheduleRepeatTypeEnum
from wisdom_v2.views.constant import BUNDLE_INTEWVIEW_TYPE_OFFLINE_GROUP


class ScheduleSerializer(serializers.ModelSerializer):
    start_time = serializers.SerializerMethodField()
    end_time = serializers.SerializerMethodField()
    week = serializers.SerializerMethodField()
    topic = serializers.SerializerMethodField()
    interview_info = serializers.SerializerMethodField()
    date_detail = serializers.SerializerMethodField()
    main_id = serializers.SerializerMethodField()
    display_details = serializers.SerializerMethodField()
    is_jump = serializers.SerializerMethodField()
    is_recurring_schedule = serializers.SerializerMethodField()
    repeat_type = serializers.SerializerMethodField()
    apply_type = serializers.SerializerMethodField()
    end_repeat_date = serializers.SerializerMethodField()
    related_info = serializers.SerializerMethodField(help_text='相关对象信息比如企业面试id')

    class Meta:
        model = Schedule
        exclude = ('updated_at', 'public_attr', 'created_at', 'schedule_id', 'platform', 'title')

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # 初始化用于缓存的字典
        self._main_schedule_cache = {}
        self._recurring_schedule_cache = {}

    def get_main_schedule_and_recurring(self, obj):
        # 获取主日程安排及其重复日程安排，如果它们还没有被缓存
        main_id = obj.main_id if hasattr(obj, 'main_id') else obj.id
        if main_id not in self._main_schedule_cache:
            # 缓存main_schedule
            main_schedule = Schedule.objects.get(id=main_id, deleted=False)
            self._main_schedule_cache[main_id] = main_schedule
            # 尝试获取并缓存recurring_schedule
            recurring_schedule = main_schedule.recurring_schedule.filter(deleted=False).first()
            self._recurring_schedule_cache[main_id] = recurring_schedule
        return self._recurring_schedule_cache[main_id]


    def get_apply_type(self, obj):
        return obj.apply_type

    def get_is_recurring_schedule(self, obj):
        recurring_schedule = self.get_main_schedule_and_recurring(obj)
        return recurring_schedule is not None

    def get_repeat_type(self, obj):
        recurring_schedule = self.get_main_schedule_and_recurring(obj)
        return recurring_schedule.repeat_type if recurring_schedule else None

    def get_end_repeat_date(self, obj):
        recurring_schedule = self.get_main_schedule_and_recurring(obj)
        return recurring_schedule.end_repeat_date if recurring_schedule else None

    def get_is_jump(self, obj):
        if obj.type == ScheduleTypeEnum.interview:
            interview = ProjectInterview.objects.filter(public_attr_id=obj.public_attr_id, deleted=False).first()
            if interview and interview.type == ProjectInterviewTypeEnum.chemical_interview.value:
                return interview.chemical_interview.filter(
                    deleted=False, chemical_interview_status=ChemicalInterviewStatusEnum.selected.value).exists()
        return True

    def get_display_details(self, obj):
        title = '忙碌'
        if obj.type == ScheduleTypeEnum.unavailable:
            title = obj.title

        elif obj.type == ScheduleTypeEnum.available:
            if obj.apply_type:
                apply_type = [ScheduleApplyTypeEnum.get_display(item) for item in obj.apply_type]
                title = f'可预约时段（{"，".join(apply_type)}）'
            else:
                title = '可预约时段'

        elif obj.type == ScheduleTypeEnum.interview:
            interview = ProjectInterview.objects.filter(public_attr_id=obj.public_attr_id, deleted=False).first()
            if interview:
                if interview.place_category == ProjectInterviewPlaceCategoryEnum.online_one_to_one:
                    if interview.type == ProjectInterviewTypeEnum.formal_interview:
                        prefix = ''
                        # 获取标辅导议题 优先获取教练的，没有教练的获取用户的。
                        topic = interview.topic if interview.topic else interview.coachee_topic
                        # C端加前缀描述
                        if not interview.public_attr.project_id:
                            # 有订单且是未删除的待支付的。
                            if interview.order and interview.order.status == OrderStatusEnum.pending_pay and not interview.order.deleted:
                                prefix = '待支付：'
                            # 教练未同意的
                            elif not interview.is_coach_agree:
                                prefix = '待确认：'
                        title = f'{prefix}【{interview.public_attr.target_user.cover_name}】{topic}'
                    elif interview.type == ProjectInterviewTypeEnum.chemical_interview:
                        title = f'【{interview.public_attr.target_user.cover_name}】化学面谈'
                    elif interview.type == ProjectInterviewTypeEnum.stakeholder_interview:
                        title = f'【{interview.public_attr.target_user.cover_name}】利益相关者访谈'
                elif interview.place_category == ProjectInterviewPlaceCategoryEnum.offline_group_coach:
                    if GroupCoach.objects.filter(
                            interview=interview, deleted=False,
                            project_group_coach__type=GroupCoachTypeEnum.collective_tutoring.value).exists():
                        title = f'工作坊：{interview.topic}'
                    if GroupCoach.objects.filter(
                            interview=interview, deleted=False,
                            project_group_coach__type=GroupCoachTypeEnum.group_tutoring.value).exists():
                        title = f'小组辅导：{interview.topic}'
        elif obj.type == ScheduleTypeEnum.company_interview:
            # Get company short name from company interview
            company_interview = obj.company_interview.first()
            company_short = company_interview.project.company.short if company_interview else None
            title = f'【{company_short}】{obj.title}' if company_short else obj.title

        if obj.is_all_day:
            describe = '全天'

            if obj.public_attr.start_time.date() == obj.public_attr.end_time.date():
                time_describe = f'{obj.public_attr.start_time.date()} 00:00-24:00'
            else:
                time_describe = f'{obj.public_attr.start_time.date()} 00:00-' \
                                f'{obj.public_attr.end_time.date()} 24:00'

        else:
            start_time = obj.public_attr.start_time.strftime('%H:%M')
            end_time = obj.public_attr.end_time.strftime('%H:%M')
            end_time = '24:00' if end_time == '23:59' else end_time
            describe = f'{start_time}-{end_time}'

            if obj.public_attr.start_time.date() == obj.public_attr.end_time.date():
                time_describe = f'{obj.public_attr.start_time.date()} {start_time}-{end_time}'
            else:
                time_describe = f'{obj.public_attr.start_time.date()} {start_time}-' \
                                f'{obj.public_attr.end_time.date()} {end_time}'

        main_id = obj.main_id if hasattr(obj, 'main_id') else obj.id
        main_schedule = Schedule.objects.get(id=main_id, deleted=False)
        if main_schedule.recurring_schedule.filter(deleted=False).exists():
            recurring_schedule = main_schedule.recurring_schedule.filter(deleted=False).first()
            display = RecurringScheduleRepeatTypeEnum.get_display(recurring_schedule.repeat_type)
            describe = f'{display}｜{describe}'
            time_describe = f'{display}｜{time_describe}'

        return {
            'title': title,
            'time_describe': time_describe,
            'describe': describe
        }

    def get_main_id(self, obj):
        return obj.main_id if hasattr(obj, 'main_id') else obj.id

    def get_date_detail(self, obj):
        if obj.public_attr.start_time.isoweekday() == 1:
            return {'week': '第' + str(obj.public_attr.start_time.isocalendar()[1]) + '周',
                    'date': obj.public_attr.start_time.strftime('%m月%d日') + '-' +
                            (obj.public_attr.start_time + datetime.timedelta(days=7)).strftime('%m月%d日')
                    }

    def get_interview_info(self, obj):
        if obj.type == SCHEDULE_TYPE_INTERVIEW and obj.platform == SCHEDULE_PLATFORM_DEFAULT:
            interview = ProjectInterview.objects.filter(
                public_attr=obj.public_attr, deleted=False).first()
            if interview:
                return {'interview_status': obj.public_attr.status,
                        'interview_id': interview.pk,
                        'order_status': interview.order.status if interview.order else None,
                        'interview_place_category': interview.place_category,
                        'type': interview.type}

    def get_start_time(self, obj):
        return obj.public_attr.start_time.strftime('%Y-%m-%d %H:%M')

    def get_end_time(self, obj):
        return obj.public_attr.end_time.strftime('%Y-%m-%d %H:%M')

    def get_week(self, obj):
        return obj.public_attr.start_time.isoweekday()

    def get_topic(self, obj):
        # 1:日程 2:约谈 3:忙碌(线上平台)
        if obj.type == SCHEDULE_TYPE_INTERVIEW:
            interview = ProjectInterview.objects.filter(public_attr_id=obj.public_attr_id, deleted=False).first()
            if not interview:
                title = '教练辅导'
            else:
                title = interview.topic if interview.topic else interview.coachee_topic
            company_short = obj.public_attr.project.company.short if obj.public_attr.project_id else None

            if obj.platform == SCHEDULE_PLATFORM_V1:
                return '忙碌'
            elif interview and interview.place_category == BUNDLE_INTEWVIEW_TYPE_OFFLINE_GROUP:
                return '【%s】%s' % (company_short, title) if company_short else title
            else:
                return '【%s】%s' % (obj.public_attr.target_user.cover_name, title) if obj.public_attr.target_user_id else title
        else:
            return obj.title if obj.title else '教练辅导'

    
    def get_related_info(self, obj):
        if obj.type == ScheduleTypeEnum.company_interview:
            interview = obj.company_interview.first()
            if interview:
                return {'interview_id': interview.pk}