import datetime
import json

from django.db.models import Sum
from drf_yasg import openapi
from drf_yasg.utils import swagger_auto_schema

from co_wisdom_server.base import NOT_SELECT_IMAGE_URL
from utils.api_response import success_response, parameter_error_response
from utils.miniapp_version_judge import compare_version
from .. import utils
from ..common import interview_public, evaluation_public
from ..enum.project_interview_enum import DataType, ProjectInterviewPlaceCategoryEnum
from ..enum.service_content_enum import MultipleAssociationRelationTypeEnum, NewCoachTaskTypeEnum
from ..models import Evaluation, Project, ProjectBundle, User, EvaluationReport, EvaluationModule, ProjectInterested, \
    PublicAttr, MultipleAssociationRelation, ChangeObservation, ChangeObservationAnswer, CoachTask, ProjectInterview
from rest_framework import serializers, viewsets
from rest_framework.decorators import action
from wisdom_v2.common.stakeholder_interview_public import get_stakeholder_interview_data
from utils.pagination import StandardResultsSetPagination

from ..views.constant import ATTR_TYPE_EVALUATION_REPORT, LBI_EVALUATION, ATTR_TYPE_EVALUATION_ANSWER, \
    INTERVIEW_TYPE_COACHING, ATTR_STATUS_INTERVIEW_CANCEL


class EvaluationSerializers(serializers.ModelSerializer):
    created_at = serializers.DateTimeField(format='%Y-%m-%d %H:%M')
    available = serializers.SerializerMethodField()
    evaluation_code = serializers.CharField(source='code', help_text='测评编号')

    class Meta:
        model = Evaluation
        exclude = ('updated_at', 'deleted')

    # 项目是否开通了测评功能，测评数量是否超过配置次数
    def get_available(self, obj):
        enable = False
        times = 0
        state = False
        project_id = self.context.get('pid', None)
        user_id = self.context.get('uid', None)
        if user_id and project_id:
            bundle = ProjectBundle.objects.filter(
                project_id=project_id,
                project_member__user_id=user_id,
                deleted=False
            ).first()
            report_count = EvaluationReport.objects.filter(
                deleted=False,
                public_attr__type=ATTR_TYPE_EVALUATION_REPORT,
                public_attr__project_id=project_id,
                public_attr__user_id=user_id,
                evaluation_id=obj.id).count()
            if bundle:
                # 这里应该有一个时间判断 先不加
                times = bundle.evaluation_module.filter(
                    evaluation_id=obj.id
                ).count()
                if times:
                    times -= report_count
                    state = True
                if state and times:
                    enable = True
        return {'enable': enable, 'times': times, 'state': state}


class EvaluationModuleSerializer(serializers.ModelSerializer):
    id = serializers.IntegerField(source='evaluation.id')
    created_at = serializers.DateTimeField(source='evaluation.created_at', format='%Y-%m-%d %H:%M')
    is_submit = serializers.BooleanField(help_text='是否提交')
    evaluation_report_id = serializers.SerializerMethodField()
    name = serializers.CharField(source='evaluation.name')
    brief = serializers.CharField(source='evaluation.brief')
    about = serializers.CharField(source='evaluation.about')
    image_url = serializers.CharField(source='evaluation.image_url')
    code = serializers.CharField(source='evaluation.code')
    submit_time = serializers.DateTimeField(format='%Y-%m-%d %H:%M:%S')
    deleted = serializers.BooleanField(source='evaluation.deleted')
    evaluation_code = serializers.CharField(source='evaluation.code', help_text='测评编号')
    is_user_fills_in_lbi_evaluation = serializers.SerializerMethodField()
    data_type = serializers.SerializerMethodField(help_text='数据类型')

    def get_data_type(self, obj):
        return DataType.evaluation

    def get_evaluation_report_id(self, obj):
        evaluation_report = EvaluationReport.objects.filter(
            deleted=False,
            public_attr__type=ATTR_TYPE_EVALUATION_REPORT,
            public_attr__project_id=obj.project_bundle.project_id,
            public_attr__user_id=obj.project_bundle.project_member.user_id,
            evaluation_id=obj.evaluation_id)

        if evaluation_report.exists():
            return evaluation_report.first().pk
        return

    def get_is_user_fills_in_lbi_evaluation(self, obj):
        if obj.evaluation.code == LBI_EVALUATION:
            public_attr = PublicAttr.objects.filter(
                project=obj.project_bundle.project,
                user=obj.project_bundle.project_member.user,
                target_user=obj.project_bundle.project_member.user,
                type=ATTR_TYPE_EVALUATION_ANSWER
            ).first()
            if public_attr:
                return True
        return False

    class Meta:
        model = EvaluationModule
        fields = ('id', 'created_at', 'is_submit', 'is_user_fills_in_lbi_evaluation',
                  'evaluation_report_id', 'name', 'brief', 'about', 'image_url', 'code',
                  'deleted', 'submit_time', 'evaluation_code', 'data_type')


class OtherEvaluationModuleSerializer(serializers.ModelSerializer):
    id = serializers.IntegerField(source='evaluation.id')
    created_at = serializers.DateTimeField(source='evaluation.created_at', format='%Y-%m-%d %H:%M')
    about = serializers.CharField(source='evaluation.about')
    image_url = serializers.CharField(source='evaluation.image_url')
    code = serializers.CharField(source='evaluation.code')
    submit_time = serializers.DateTimeField(format='%Y-%m-%d %H:%M:%S')
    deleted = serializers.BooleanField(source='evaluation.deleted')
    project_id = serializers.CharField(source='project_bundle.project.id')
    evaluation_code = serializers.CharField(source='evaluation.code', help_text='测评编号')
    master_user_id = serializers.SerializerMethodField()
    master_user_name = serializers.SerializerMethodField()
    name = serializers.SerializerMethodField()
    brief = serializers.SerializerMethodField()
    data_type = serializers.SerializerMethodField()

    def get_data_type(self, obj):
        return DataType.evaluation

    def get_master_user_id(self, obj):
        return obj.project_bundle.project_member.user.id

    def get_master_user_name(self, obj):
        return obj.project_bundle.project_member.user.cover_name

    def get_name(self, obj):
        return f'{obj.evaluation.name}-他评'

    def get_brief(self, obj):
        return f'测评对象：{obj.project_bundle.project_member.user.cover_name}'

    class Meta:
        model = EvaluationModule
        exclude = ('updated_at',)


class EvaluationViewSet(viewsets.ModelViewSet):
    queryset = Evaluation.objects.filter(deleted=False)
    serializer_class = EvaluationSerializers
    authentication_classes = []

    @swagger_auto_schema(
        operation_id='测评列表',
        operation_summary='测评列表',
        manual_parameters=[
            openapi.Parameter('user_id', openapi.IN_QUERY, description='用户id', type=openapi.TYPE_NUMBER,
                              required=True),
            openapi.Parameter('project_id', openapi.IN_QUERY, description='项目id', type=openapi.TYPE_NUMBER,
                              required=True),
            openapi.Parameter('page', openapi.IN_QUERY, description='第几页', type=openapi.TYPE_NUMBER),
            openapi.Parameter('page_size', openapi.IN_QUERY, description='每页显示条数默认15条', type=openapi.TYPE_NUMBER)
        ],
        tags=['app测评']
    )
    def list(self, request):
        try:
            project_id = int(request.query_params.get('project_id', 0))
            user_id = int(request.query_params.get('user_id', 0))
            Project.objects.get(pk=project_id)
            User.objects.get(pk=user_id)
        except Project.DoesNotExist:
            return parameter_error_response('项目不存在')
        except User.DoesNotExist:
            return parameter_error_response('用户不存在')

        queryset = self.get_queryset()

        # 分页
        paginator = StandardResultsSetPagination()
        page_list = paginator.paginate_queryset(queryset, self.request)
        serializer = self.serializer_class(page_list, many=True, context={'pid': project_id, 'uid': user_id})
        response = paginator.get_paginated_response(serializer.data)
        return success_response(response, request=request)

    @action(methods=['get'], detail=False, url_path='evaluation_list')
    def evaluation_list(self, request):
        try:
            project_id = int(request.query_params.get('project_id', 0))
            user_id = int(request.query_params.get('user_id', 0))
            Project.objects.get(pk=project_id)
            User.objects.get(pk=user_id)
        except Project.DoesNotExist:
            return parameter_error_response('项目不存在')
        except User.DoesNotExist:
            return parameter_error_response('用户不存在')
        # 改变观察反馈数据
        change_observation_data = get_change_observation_data(user_id)

        # 教练任务-利益相关者调研数据
        stakeholder_coach_task_data = get_stakeholder_coach_task_data(user_id)

        queryset = EvaluationModule.objects.filter(
            project_bundle__project_id=project_id,
            deleted=False,
            start_time__lte=datetime.datetime.now().date(),
            project_bundle__project_member__user_id=user_id).order_by('submit_time', '-start_time')

        user_evaluation_data = EvaluationModuleSerializer(queryset, many=True,
                                                          context={'pid': project_id, 'uid': user_id}).data

        master_users_evaluation = evaluation_public.get_master_users_evaluation(user_id)
        master_evaluation_data = OtherEvaluationModuleSerializer(master_users_evaluation, many=True).data

        paginator = StandardResultsSetPagination()
        page_list = paginator.paginate_queryset(
            [*change_observation_data, *stakeholder_coach_task_data, *user_evaluation_data, *master_evaluation_data], self.request)
        response = paginator.get_paginated_response(page_list)
        return success_response(response, request=request)

    @swagger_auto_schema(
        operation_id='查看利益相关者测评待反馈测评列表',
        operation_summary='查看利益相关者测评待反馈测评列表',
        manual_parameters=[
            openapi.Parameter('user_id', openapi.IN_QUERY, description='利益相关者用户id', type=openapi.TYPE_NUMBER,
                              required=True),
        ],
        tags=['app测评']
    )
    @action(methods=['get'], detail=False, url_path='other', authentication_classes=[])
    def get_other_user_evaluation_list(self, request, *args, **kwargs):

        try:
            user_id = request.query_params.get('user_id')
            master_users_evaluation = evaluation_public.get_master_users_evaluation(user_id)
            mp = json.loads(request.headers.get('mp')) if request.headers.get('mp') else None
        except Exception as e:
            return parameter_error_response(str(e))

        # 改变观察反馈数据
        change_observation_data = get_change_observation_data(user_id)

        # 利益相关者访谈
        if mp and compare_version(mp.get('version'), '2.16.2') < 0:
            stakeholder_interview_data = []
        else:
            stakeholder_interview_data = get_stakeholder_interview_data(user_id)

        # 教练任务-利益相关者调研数据
        stakeholder_coach_task_data = get_stakeholder_coach_task_data(user_id)

        # 测评数据
        evaluation_data = []
        for item in master_users_evaluation:
            evaluation_report_config = item.evaluation.evaluation_report_config.filter(deleted=False).first()
            evaluation_data.append({
                'image_url': item.evaluation.image_url,
                'data_type': DataType.evaluation,
                'user': {
                    'name': item.project_bundle.project_member.user.cover_name,
                    'id': item.project_bundle.project_member.user.id
                },
                'end_date': item.end_time.strftime('%Y.%m.%d'),
                'title': item.evaluation.name,
                'details': {
                    'id': item.evaluation.id,
                    'code': item.evaluation.code,
                    "evaluation_report_type": evaluation_report_config.type if evaluation_report_config else None,
                }
            })

        paginator = StandardResultsSetPagination()
        page_list = paginator.paginate_queryset(
            [*change_observation_data, *stakeholder_interview_data, *stakeholder_coach_task_data,
             *evaluation_data], self.request)
        response = paginator.get_paginated_response(page_list)
        return success_response(response, request=request)


def get_change_observation_data(user_id):
    # 改变观察反馈数据
    change_observation_data = []
    interested_id = ProjectInterested.objects.filter(
        interested_id=user_id,
        deleted=False,
    ).values_list('id', flat=True)

    query_data = MultipleAssociationRelation.objects.filter(
        secondary_id__in=interested_id,
        deleted=False,
        type=MultipleAssociationRelationTypeEnum.stakeholder_change_observation,
    ).values_list('main_id', flat=True)
    if query_data:
        # 找到未填写的改变观察反馈
        change_observation = ChangeObservation.objects.filter(
            pk__in=query_data,
            deleted=False,
            is_complete=False
        )

        if change_observation.exists():
            for item in change_observation.order_by('-created_at').all():

                interview_time = interview_public.get_user_interview_time(item.project_member)

                # 判断是否满足填写条件
                write_condition = item.write_condition if item.write_condition else 0
                if write_condition <= interview_time:

                    answer = ChangeObservationAnswer.objects.filter(
                        change_observation_id=item.id,
                        public_attr__user_id=user_id
                    )

                    # 没有回答的返回
                    if not answer.exists():
                        change_observation_data.append({
                            'title': item.name,
                            'data_type': DataType.change_observation,
                            'image_url': NOT_SELECT_IMAGE_URL,
                            'end_date': item.stakeholders_write_end_date.strftime('%Y.%m.%d') if item.stakeholders_write_end_date else None,
                            'details': {
                                'id': item.id,
                            },
                            'user': {
                                'name': item.project_member.user.cover_name,
                                'id': item.project_member.user.id
                            },
                        })
    return change_observation_data


def get_stakeholder_coach_task_data(user_id):
    data = []
    interested = ProjectInterested.objects.filter(
        interested_id=user_id, deleted=False).values_list('pk', flat=True)

    # 通过关系表获取所有利益相关者调研
    query_data = MultipleAssociationRelation.objects.filter(
        secondary_id__in=list(interested),
        deleted=False,
        public_attr__isnull=True,
        type=MultipleAssociationRelationTypeEnum.stakeholder_coach_task,
    ).values_list('main_id', flat=True)
    if query_data:
        coach_task = CoachTask.objects.filter(
            pk__in=query_data,
            deleted=False,
            stakeholder_submit_time__isnull=True
        )
        if coach_task.exists():
            for item in coach_task.order_by('-created_at').all():
                interview_time = interview_public.get_user_interview_time(item.project_bundle.project_member)

                # 判断是否满足填写条件
                write_condition = item.hours if item.hours else 0
                if write_condition <= interview_time:
                    data.append({
                        'title': item.template.title,
                        'data_type': DataType.coach_tasks,
                        'image_url': item.template.image_url,
                        'end_date': None,
                        'details': {
                            'id': item.id,
                            'type': NewCoachTaskTypeEnum.stakeholder_research,
                        },
                        'user': {
                            'name': item.project_bundle.project_member.user.cover_name,
                            'id': item.project_bundle.project_member.user.id
                        },
                    })
    return data

