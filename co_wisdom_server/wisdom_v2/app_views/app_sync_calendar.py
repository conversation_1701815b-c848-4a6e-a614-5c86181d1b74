import time, datetime

from django.db import transaction
from rest_framework.views import APIView
from drf_yasg import openapi
from django.db.models import Q, Sum
from django.utils import timezone

from drf_yasg.utils import swagger_auto_schema
from utils.api_response import success_response, parameter_error_response
from ..models import Schedule, PublicAttr, User, MobileSchedule
from wisdom_v2.views.constant import ATTR_TYPE_ACTIONPLAN, ROLE_COACHEE, ATTR_TYPE_SCHEDULE, SCHEDULE_TYPE_INTERVIEW, \
    SCHEDULE_TYPE_OTHER, SCHEDULE_TYPE_SCHEDULE, ATTR_TYPE_INTERVIEW, ATTR_STATUS_INTERVIEW_CANCEL

from ..views.constant import SCHEDULE_PLATFORM_USER, SCHEDULE_PLATFORM_DEFAULT, SCHEDULE_PLATFORM_V1
from data.models import AppCoachschedule


def isDateEqual(schedule, event):
    start_time = datetime.datetime.fromtimestamp(int(event['start']) / 1000).strftime('%Y-%m-%d %H:%M')
    end_time = datetime.datetime.fromtimestamp(int(event['end']) / 1000).strftime('%Y-%m-%d %H:%M')

    if schedule.public_attr.start_time.strftime('%Y-%m-%d %H:%M') == start_time and schedule.public_attr.end_time.strftime('%Y-%m-%d %H:%M') == end_time:
        return True
    return False


def conflictCoaching(schedule_ids, event):
    start = datetime.datetime.fromtimestamp(int(event['start']) / 1000)
    end = datetime.datetime.fromtimestamp(int(event['end']) / 1000)
    conflict_schedule = Schedule.objects.filter(Q(public_attr__start_time__lte=end, public_attr__end_time__gte=start) |
                            Q(public_attr__start_time__gte=start, public_attr__end_time__lte=end),
                            pk__in=schedule_ids, deleted=False)
    return conflict_schedule.filter(public_attr__type=ATTR_TYPE_INTERVIEW) | conflict_schedule.filter(platform=SCHEDULE_PLATFORM_V1, type=SCHEDULE_TYPE_INTERVIEW)


def conflictEvent(schedule, events):
    conflict_events = []
    start_time = schedule.public_attr.start_time
    end_time = schedule.public_attr.end_time
    for event in events:
        start = datetime.datetime.fromtimestamp(int(event['start']) / 1000)
        end = datetime.datetime.fromtimestamp(int(event['end']) / 1000)
        if (start <= end_time and end >= start_time) or (start >= start_time and end <= end_time):
            conflict_events.append(event)
    return conflict_events


def syncDate(schedule, event):
    start = datetime.datetime.fromtimestamp(int(event['start']) / 1000)
    end = datetime.datetime.fromtimestamp(int(event['end']) / 1000)
    schedule.public_attr.start_time = start
    schedule.public_attr.end_time = end
    schedule.public_attr.save()
    schedule.title = event['title']
    schedule.save()
    if schedule.schedule_id:
        # 同步线上平台的教练日程到v1
        v1_schedule = AppCoachschedule.objects.filter(schedule_id=schedule.pk).first()
        if v1_schedule:
            v1_schedule.Starttime = start
            v1_schedule.EndTime = end
            v1_schedule.save()


def sameDate(schedule_ids, event):
    start = datetime.datetime.fromtimestamp(int(event['start']) / 1000)
    end = datetime.datetime.fromtimestamp(int(event['end']) / 1000)
    return Schedule.objects.filter(public_attr__start_time=start, public_attr__end_time=end,
                                   pk__in=schedule_ids, deleted=False).first()


def sameType(schedule, event):
    isCoaching = isInterview(schedule) and event['title'] == '教练辅导（请勿修改）'
    isDate = not isInterview(schedule) and event['title'] != '教练辅导（请勿修改）'
    return isCoaching or isDate


def isInterview(schedule):
    return schedule.type == SCHEDULE_TYPE_INTERVIEW


class SyncCalendar(APIView):
    authentication_classes = []

    @swagger_auto_schema(
        operation_id='app教练日历同步',
        operation_summary='app教练日历同步',
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'coach_user_id': openapi.Schema(type=openapi.TYPE_NUMBER, description='用户id'),
                'device_id': openapi.Schema(type=openapi.TYPE_STRING, description='设备id'),
                'event': openapi.Schema(type=openapi.TYPE_ARRAY, description='日历数组',
                                        items=openapi.Schema(type=openapi.TYPE_OBJECT)),
            }
        ),
        tags=['app教练日历同步']
    )
    def post(self, request, *args, **kwargs):
        try:
            coach_user_id = request.data.get('coach_user_id')
            device_id = request.data.get('device_id')
            event = request.data.get('event')
            user = User.objects.get(pk=coach_user_id, deleted=False)
        except User.DoesNotExist:
            return parameter_error_response()
        sync_event = []
        conflict = []
        event_ids = [_['device_event_id'] for _ in event]
        time_now = timezone.now()
        nightyDays = datetime.timedelta(days=90)
        schedule_90d = Schedule.objects.filter(public_attr__user=user,
                                               public_attr__end_time__range=(time_now, (time_now + nightyDays)))
        schedule_ids = schedule_90d.filter(deleted=False).values_list('id', flat=True)
        mobile_ids = MobileSchedule.objects.filter(deleted=False, device_id=device_id,
                                                   schedule__in=schedule_90d.values_list('id', flat=True))
        try:
            # 1. 有对应关系，且都存在
            # 2. 有对应关系，但平台日程不存在。说明平台日程被删（教练辅导被取消，或者忙碌被删除）
            # 3. 有对应关系，但手机日程不存在
            # 4. 没有对应关系，手机日程处理
            relation_event_ids = mobile_ids.values_list('device_event_id', flat=True)
            new_event_ids = set(event_ids) - set(relation_event_ids)
            # 5. 没有对应关系，平台日程处理
            relation_schedule_ids = mobile_ids.values_list('schedule_id', flat=True)
            new_schedule_ids = set(schedule_ids) - set(relation_schedule_ids)
            with transaction.atomic():
                for relation in mobile_ids:
                    if relation.device_event_id in event_ids and relation.schedule.deleted is False:
                        # 1
                        event_item = next(x for x in event if x['device_event_id'] == relation.device_event_id)

                        if isDateEqual(relation.schedule, event_item):
                            # 时间没有变化，只判断标题是否变化
                            if relation.schedule.title != event_item['title']:
                                relation.schedule.title = event_item['title']
                                relation.schedule.save()
                        elif isInterview(relation.schedule):
                            # 修改手机日程的时间
                            sync_event.append({
                                'operate': 1,
                                'device_event_id': event_item['device_event_id'],
                                'start': relation.schedule.public_attr.start_time.timestamp(),
                                'end': relation.schedule.public_attr.end_time.timestamp(),
                                'title': '教练辅导（请勿修改）'
                            })
                        else:
                            # 修改平台日程的时间
                            syncDate(relation.schedule, event_item)
                            conflict_coaching = conflictCoaching(schedule_ids, event_item)
                            for conflict_schedule in conflict_coaching:
                                # 提醒时间冲突
                                conflict.append({
                                    'id': conflict_schedule.pk,
                                    'start_time': conflict_schedule.public_attr.start_time,
                                    'end_time': conflict_schedule.public_attr.end_time
                                })

                    elif relation.device_event_id in event_ids and relation.schedule.deleted is True:
                        # 2 删除手机日程
                        event_item = next(x for x in event if x['device_event_id'] == relation.device_event_id)

                        sync_event.append({
                            'operate': 2,
                            'device_event_id': event_item['device_event_id']
                        })
                        relation.deleted = True
                        relation.save()
                    elif relation.device_event_id not in event_ids and relation.schedule.deleted is False:
                        # 3
                        # 新建教练辅导到手机
                        if isInterview(relation.schedule):
                            sync_event.append({
                                'operate': 0,
                                'start': relation.schedule.public_attr.start_time.timestamp(),
                                'end': relation.schedule.public_attr.end_time.timestamp(),
                                'title': '教练辅导（请勿修改）',
                                'schedule_id': relation.schedule.pk
                            })
                        else:
                            # 删除平台的忙碌日程
                            relation.schedule.deleted = True
                            relation.schedule.save()
                            relation.deleted = True
                            relation.save()
                            # 删除v1日程
                            AppCoachschedule.objects.filter(schedule_id=relation.schedule.pk).delete()


                # 相同设备，其他用户同步平台日程到手机来的，不能再次同步回平台
                other_user_schedule_ids = MobileSchedule.objects.filter(schedule__platform=SCHEDULE_PLATFORM_DEFAULT,
                                                                        device_id=device_id, device_event_id__in=new_event_ids).\
                    exclude(schedule__public_attr__user=user).values_list('device_event_id', flat=True)
                new_event_ids = set(new_event_ids) - set(other_user_schedule_ids)
                for new_event_id in new_event_ids:
                    # 4
                    event_item = next(x for x in event if x['device_event_id'] == new_event_id)

                    # 同步手机日程到平台
                    same_schedule = sameDate(schedule_ids, event_item)

                    if same_schedule and sameType(same_schedule, event_item):
                        # 建立关系
                        MobileSchedule.objects.create(schedule=same_schedule, device_event_id=event_item['device_event_id'],
                                                      device_id=device_id, title=event_item['title'])
                    else:
                        # 同步手机日程到平台
                        start = datetime.datetime.fromtimestamp(int(event_item['start']) / 1000)
                        end = datetime.datetime.fromtimestamp(int(event_item['end']) / 1000)
                        public_attr = PublicAttr.objects.create(user=user, type=ATTR_TYPE_SCHEDULE,
                                                                start_time=start, end_time=end)

                        schedule = Schedule.objects.create(type=SCHEDULE_TYPE_OTHER, remark='日程由手机日历同步，不可删除',
                                                           platform=SCHEDULE_PLATFORM_USER, public_attr=public_attr, title=event_item['title'])

                        MobileSchedule.objects.create(schedule=schedule,
                                                      device_event_id=event_item['device_event_id'],
                                                      device_id=device_id, title=event_item['title'])

                        # 检测冲突日程
                        conflict_coaching = conflictCoaching(schedule_ids, event_item)
                        for conflict_schedule in conflict_coaching:
                            # 提醒时间冲突
                            conflict.append({
                                'id': conflict_schedule.pk,
                                'start_time': conflict_schedule.public_attr.start_time,
                                'end_time': conflict_schedule.public_attr.end_time
                            })

                new_schedule_list = Schedule.objects.filter(pk__in=new_schedule_ids).exclude(platform=SCHEDULE_PLATFORM_USER)
                for new_schedule in new_schedule_list:
                    # 5
                    if isInterview(new_schedule):
                        sync_event.append({
                            'operate': 0,
                            'start': new_schedule.public_attr.start_time.timestamp(),
                            'end': new_schedule.public_attr.end_time.timestamp(),
                            'title': '教练辅导（请勿修改）',
                            'schedule_id': new_schedule.pk
                        })
                        # 与手机日程重叠判断
                        conflict_events = conflictEvent(new_schedule, event)
                        for conflict_event in conflict_events:
                            # 提醒时间冲突
                            conflict.append({
                                'start_time': datetime.datetime.fromtimestamp(int(conflict_event['start']) / 1000),
                                'end_time': datetime.datetime.fromtimestamp(int(conflict_event['end']) / 1000)
                            })
                    else:
                        sync_event.append({
                            'operate': 0,
                            'start': new_schedule.public_attr.start_time.timestamp(),
                            'end': new_schedule.public_attr.end_time.timestamp(),
                            'title': new_schedule.title,
                            'schedule_id': new_schedule.pk
                        })


        except Exception as e:
            print(e)
            return parameter_error_response()
        data = {'sync_event': sync_event,
                'conflict': conflict}
        return success_response(data)


class DeviceUpdate(APIView):
    authentication_classes = []

    @swagger_auto_schema(
        operation_id='app教练日历同步-设置ID关系',
        operation_summary='app教练日历同步-设置ID关系',
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'device_id': openapi.Schema(type=openapi.TYPE_STRING, description='设备id'),
                'event': openapi.Schema(type=openapi.TYPE_ARRAY, description='日历数组',
                                        items=openapi.Schema(type=openapi.TYPE_OBJECT)),
            }
        ),
        tags=['app教练日历同步']
    )
    def post(self, request, *args, **kwargs):
        try:
            event = request.data.get('event')
            device_id = request.data.get('device_id')
        except User.DoesNotExist:
            return parameter_error_response()
        for item in event:
            # 如果有设置过关系则更新
            mobile_exist = MobileSchedule.objects.filter(schedule_id=item['schedule_id'], device_id=device_id, deleted=False).first()
            if mobile_exist:
                mobile_exist.device_event_id = item['device_event_id']
                mobile_exist.save()
            else:
                MobileSchedule.objects.create(schedule_id=item['schedule_id'], device_id=device_id,
                                              device_event_id=item['device_event_id'])
        return success_response()
