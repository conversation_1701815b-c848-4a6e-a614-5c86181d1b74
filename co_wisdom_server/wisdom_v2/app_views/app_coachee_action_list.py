from rest_framework.views import APIView

from drf_yasg import openapi
from drf_yasg.utils import swagger_auto_schema
from utils.api_response import success_response, parameter_error_response
from utils.pagination import StandardResultsSetPagination

from ..models import ActionPlan, Habit, LearnArticle
from .app_coachee_action_plan import ActionPlanSerializer
from .app_coachee_habit import HabitSerializer
from .app_coachee_learn_article import LearnArticleSerializer


class CoacheeActionListView(APIView):

    @swagger_auto_schema(
        operation_id='app被教行动列表(包括行动计划/习惯养成/拓展学习)',
        operation_summary='app被教行动列表(包括行动计划/习惯养成/拓展学习)',
        manual_parameters=[
            openapi.Parameter('user_id', openapi.IN_QUERY, description='用户id', type=openapi.TYPE_NUMBER),
            openapi.Parameter('page', openapi.IN_QUERY, description='第几页', type=openapi.TYPE_NUMBER),
            openapi.Parameter('page_size', openapi.IN_QUERY, description='每页显示条数默认15条', type=openapi.TYPE_NUMBER)
        ],
        tags=['app被教行动列表(包括行动计划/习惯养成/拓展学习)']
    )
    def get(self, request, *args, **kwargs):
        try:
            user_id = int(request.query_params.get('user_id', 0))
            action_plan = ActionPlan.objects.filter(public_attr__user__pk=user_id, deleted=False).order_by('-end_date')
            habit = Habit.objects.filter(public_attr__user__pk=user_id, deleted=False).order_by('-end_date')
            learn_article = LearnArticle.objects.filter(deleted=False, public_attr__user__pk=user_id).order_by('-created_at')
            is_done_count = action_plan.filter(status=2).count() + habit.filter(status=2).count() + learn_article.filter(status=2).count()
            all_count = action_plan.count() + habit.count() + learn_article.count()
            if is_done_count == 0 or all_count == 0:
                rate = 0
            else:
                rate = int(round((is_done_count / all_count), 2) * 100)
            action_list = []
            action_list += HabitSerializer(habit, many=True).data
            action_list += ActionPlanSerializer(action_plan, many=True).data
            action_list += LearnArticleSerializer(learn_article, many=True).data
            count_data = {'is_done_count': is_done_count,
                          'all_count': all_count,
                          'rate': rate}
        except Exception as e:
            return parameter_error_response(str(e))

        # 分页
        paginator = StandardResultsSetPagination()
        page_list = paginator.paginate_queryset(action_list, self.request)
        response = paginator.get_paginated_response(page_list)
        return success_response(response, request, {'count_data': count_data})
