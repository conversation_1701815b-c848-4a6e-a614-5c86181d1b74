import datetime
import math

from django.conf import settings
from django.db.models import Sum
from rest_framework import serializers

from utils.messagecenter import getui
from wisdom_v2.common import order_public
from wisdom_v2.enum.service_content_enum import ActivityTypeEnum, PersonalActivityTypeEnum
from wisdom_v2.models import Coupon, Order, Commodity, Schedule, RefundOrder, Coach, User, PublicAttr, Order2Commodity, \
    PersonalUser, WorkWechatUser
from wisdom_v2.enum.pay_enum import CommodityTypeEnum, CommodityStatusEnum, \
    CouponStatusEnum, StockTypesEnum, OrderStatusEnum, RefundStatusEnum, StockStatusEnum, SettlementChannelEnum
from django.db import transaction
from decimal import Decimal
from utils.third_party_payment import ThirdPartyPayment
from utils.feishu_robot import push_wx_error_message
from wisdom_v2.models_file import ActivityInterview
from wisdom_v2.views.constant import ATTR_TYPE_ORDER, ATTR_STATUS_INTERVIEW_CANCEL


class AppOrderSerializer(serializers.ModelSerializer):

    class Meta:
        model = Order
        fields = "__all__"


def update_coach_commodity(coach, price):
    """
    更新或新建教练商品
    """
    commodity = Commodity.objects.filter(type=CommodityTypeEnum.coaching, object_id=coach.pk, deleted=False)
    if commodity.exists():
        commodity = commodity.first()
        commodity.price = price
        commodity.name = f'{coach.personal_name if coach.personal_name else coach.user.cover_name}-教练辅导'
        commodity.desc = f'{coach.personal_name if coach.personal_name else coach.user.cover_name}-教练辅导'
        commodity.save()
    else:
        Commodity.objects.create(type=CommodityTypeEnum.coaching,
                                 object_id=coach.pk, price=price, total_count=1,
                                 available_count=1, status=CommodityStatusEnum.shelf,
                                 name=f'{coach.personal_name if coach.personal_name else coach.user.cover_name}-教练辅导',
                                 desc=f'{coach.personal_name if coach.personal_name else coach.user.cover_name}-教练辅导')


def get_pending_order_detail(user, coach, type, activity=None):
    """
    获取待付款订单金额详情， 金额单位（分）
    """
    if type == 'coach_interview' and activity:
        if activity.type == ActivityTypeEnum.public_welfare_coach:
            # 1. 更新或创建教练对应的商品信息
            update_coach_commodity(coach, 0)
            data = {
                "amount": 0,
                "total_amount": 0,
                "pay_text": pay_text,
                "discount_amount": 0,
                "coupon_id": None,
                "coupon_no": None,
                "coupon_name": None
            }
            return data

    elif type == 'coach_interview':
        # 1. 更新或创建教练对应的商品信息
        update_coach_commodity(coach, coach.price)
        # 2. 获取用户可用最大优惠的优惠券,并计算当前优惠券优惠后的金额;返回优惠金额及优惠券信息
        amount, coupon = get_use_coupon(coach.price, user)
        if not coupon:
            data = {
                "amount": str(Decimal(str(coach.price)) / Decimal('100')),
                "total_amount": str(Decimal(str(coach.price)) / Decimal('100')),
                "pay_text": pay_text,
                "discount_amount": 0,
                "coupon_id": None,
                "coupon_no": None,
                "coupon_name": None
            }
            return data
        data = {
            "amount": str((Decimal(str(coach.price)) - amount) / Decimal('100')),  # 实付金额
            "total_amount": str(Decimal(str(coach.price)) / Decimal('100')),  # 总金额
            "pay_text": pay_text,
            "discount_amount": str(amount / Decimal('100')),  # 优惠金额
            "coupon_id": coupon.coupon_id if coupon.coupon_id else None,
            "coupon_no": coupon.coupon_no,
            "coupon_name": coupon.coupon_name
        }
        return data
    elif type == 'fellow_interview':
        # 1. 更新或创建教练对应的商品信息
        update_coach_commodity(coach, 0)
        data = {
            "amount": 0,
            "total_amount": 0,
            "pay_text": pay_text,
            "discount_amount": 0,
            "coupon_id": None,
            "coupon_no": None,
            "coupon_name": None
        }
        return data
    return {}


def get_use_coupon(total_amount, user):
    """
    获取用户可用最大优惠的优惠券
    """
    if not total_amount:
        return Decimal('0'), None

    now = datetime.datetime.now()
    coupons = Coupon.objects.filter(user=user, status=CouponStatusEnum.SENDED, deleted=False,
                                    stock__available_begin_time__lt=now,
                                    stock__available_end_time__gt=now,
                                    stock__status=StockStatusEnum.running,
                                    )
    if not coupons.exists():
        return Decimal('0'), None
    # 计算最大优惠金额，并查询是否可用
    tmp_list = []
    for coupon in coupons:
        amount, available = get_coupon_available(total_amount, coupon)
        if available:
            tmp_list.append([amount, coupon])
    if not tmp_list:
        return Decimal('0'), None
    # 根据amount排序取最大优惠
    tmp_list = sorted(tmp_list, key=lambda k: k[0], reverse=True)
    return tmp_list[0][0], tmp_list[0][1]


def get_coupon_available(total_amount, coupon):
    """
        获取优惠券扣减金额及是否可用
        """
    total_amount = Decimal(str(total_amount))
    if coupon.stock.stock_type == StockTypesEnum.DISCOUNT:
        # 检查总金额是否大于优惠券使用门槛金额
        if total_amount < Decimal(str(coupon.stock.transaction_minimum)):
            return Decimal('0'), False
        discount = Decimal(str(coupon.stock.discount)) / Decimal('100')
        amount = total_amount - total_amount * discount
        tmp_discount_amount = total_amount - amount
        tmp_discount_amount = Decimal(str(math.ceil(tmp_discount_amount)))
        amount = total_amount - tmp_discount_amount
        amount = amount.quantize(Decimal('0'))
        # 全场折扣券最大扣减500元
        if amount > Decimal('50000'):
            amount = Decimal('50000')
    elif coupon.stock.stock_type == StockTypesEnum.CUT:
        transaction_minimum = Decimal(str(coupon.stock.transaction_minimum))
        amount = Decimal(str(coupon.stock.coupon_amount))
        if total_amount < transaction_minimum:  # 总金额未过门槛
            return Decimal('0'), False
    else:
        return Decimal('0'), False
    # 查询已使用的优惠券总额
    coupons = Coupon.objects.filter(stock_id=coupon.stock_id, status=CouponStatusEnum.USED, deleted=False)
    used_amount = Decimal('0')
    for c in coupons:
        order = Order.objects.filter(coupon_no=c.coupon_no, status=OrderStatusEnum.paid, deleted=False).first()
        if order:
            used_amount += Decimal(str(order.discount_amount))
    if used_amount + amount > Decimal(str(coupon.stock.max_amount)):
        push_wx_error_message(name='批次优惠券预算不足提醒', level='error', content=f'stock_id为'
                                                                         f'{coupon.stock.stock_id}的批次优惠券当前预算不足')
        return amount, False
    return amount, True


def create_order_msg(data, type, activity=None):
    """
    创建订单
    :param data: 订单信息
    :param type: 订单类型 1:预约教练订单 2:公益教练订单
    :return:
    """
    try:
        interview_msg = data.get('interview')
        coach = Coach.objects.get(user_id=interview_msg.get('coach_user_id'), deleted=False)
        user = User.objects.get(pk=interview_msg.get('user_id'))
        start_time = interview_msg.get('start_time')
        end_time = interview_msg.get('end_time')
        start_time = start_time + ':00'
        end_time = end_time + ':00'
        topic = interview_msg.get('topic')
    except:
        return False, None, None, '参数错误'
    if not user.openid:
        return False, None, None, '请先绑定微信'
    # 检查辅导结束时间是否早于当前时间，如果早于当前时间无法下单（不能预约当前时间之前的辅导）
    tmp_end_time = datetime.datetime.strptime(end_time, '%Y-%m-%d %H:%M:%S')
    if tmp_end_time < datetime.datetime.now():
        return False, None, None, '无法预约当前时间之前的教练辅导'

    if type == 1 and activity:  # 预约教练订单, 参与公益教练活动
        if activity.type == ActivityTypeEnum.public_welfare_coach.value:
            # 查找商品
            commodity = Commodity.objects.filter(object_id=coach.pk,
                                                 type=CommodityTypeEnum.coaching, deleted=False).first()
            if not commodity:
                return False, None, None, '教练服务商品错误'

            # 查询是否有未付款订单，若有未付款订单且订单商品，辅导等信息是否与当前一致，若一致返回当前未付款订单信息，不再创建新订单
            pending_pay_orders = Order.objects.filter(status=OrderStatusEnum.pending_pay,
                                                      public_attr__user_id=user.pk, deleted=False)
            if pending_pay_orders.exists():
                for pending_pay_order in pending_pay_orders:
                    pending_pay_interview = pending_pay_order.interview.all().first()
                    pending_pay_public_attr = pending_pay_interview.public_attr
                    if pending_pay_public_attr.user_id == coach.user_id and \
                            pending_pay_public_attr.start_time.strftime('%Y-%m-%d %H:%M:%S') == start_time \
                            and pending_pay_public_attr.end_time.strftime('%Y-%m-%d %H:%M:%S') == end_time:
                        return True, pending_pay_order, pending_pay_interview, None
            with transaction.atomic():
                order_public_attr = PublicAttr.objects.create(user_id=user.pk, type=ATTR_TYPE_ORDER)
                # 创建订单
                order_data = {
                    "total_amount": coach.price,
                    "public_attr_id": order_public_attr.pk,
                    "status": OrderStatusEnum.paid,
                    "channel": SettlementChannelEnum.wechat,
                    "success_time": datetime.datetime.now()
                }
                order = Order.objects.create(**order_data)
                # 创建订单-商品关系
                Order2Commodity.objects.create(commodity=commodity, order=order)

                # 创建辅导
                interview = order_public.create_interview(start_time, end_time, coach.user_id, user.pk, topic)
                interview.order = order
                interview.save()

                # 创建辅导-活动关系
                ActivityInterview.objects.create(
                    activity=activity, interview=interview)
            return True, order, interview, None
        else:
            return False, None, None, '无效的活动类型'

    elif type == 1:  # 预约教练订单
        # 查找商品
        commodity = Commodity.objects.filter(object_id=coach.pk,
                                             type=CommodityTypeEnum.coaching, deleted=False).first()
        if not commodity:
            return False, None, None, '教练服务商品错误'

        # 查询是否有未付款订单，若有未付款订单且订单商品，辅导等信息是否与当前一致，若一致返回当前未付款订单信息，不再创建新订单
        pending_pay_orders = Order.objects.filter(status=OrderStatusEnum.pending_pay,
                                                  public_attr__user_id=user.pk, deleted=False)
        if pending_pay_orders.exists():
            for pending_pay_order in pending_pay_orders:
                pending_pay_interview = pending_pay_order.interview.all().first()
                pending_pay_public_attr = pending_pay_interview.public_attr
                if pending_pay_public_attr.user_id == coach.user_id and \
                        pending_pay_public_attr.start_time.strftime('%Y-%m-%d %H:%M:%S') == start_time \
                        and pending_pay_public_attr.end_time.strftime('%Y-%m-%d %H:%M:%S') == end_time:
                    return True, pending_pay_order, pending_pay_interview, None
        with transaction.atomic():
            order_public_attr = PublicAttr.objects.create(user_id=user.pk, type=ATTR_TYPE_ORDER)
            # 创建订单
            order_data = {
                "total_amount": coach.price,
                "public_attr_id": order_public_attr.pk,
                "status": OrderStatusEnum.paid if coach.price == 0 else OrderStatusEnum.pending_pay,
                "channel": SettlementChannelEnum.wechat,
                "success_time": datetime.datetime.now() if coach.price == 0 else None
            }
            order = Order.objects.create(**order_data)
            # 创建订单-商品关系
            Order2Commodity.objects.create(commodity=commodity, order=order)

            # 创建辅导
            interview = order_public.create_interview(start_time, end_time, coach.user_id, user.pk, topic)
            interview.order = order
            interview.save()
        return True, order, interview, None
    elif type == 2:

        # 查找商品
        commodity = Commodity.objects.filter(object_id=coach.pk,
                                             type=CommodityTypeEnum.coaching, deleted=False).first()
        if not commodity:
            return False, None, None, '教练服务商品错误'

        with transaction.atomic():
            order_public_attr = PublicAttr.objects.create(user_id=user.pk, type=ATTR_TYPE_ORDER)
            # 创建订单
            order_data = {
                "total_amount": 0,
                "public_attr_id": order_public_attr.pk,
                "status": OrderStatusEnum.paid,
                "channel": SettlementChannelEnum.wechat,
                "success_time": datetime.datetime.now()
            }
            order = Order.objects.create(**order_data)
            # 创建订单-商品关系
            Order2Commodity.objects.create(commodity=commodity, order=order)

            # 创建辅导
            interview = order_public.create_interview(start_time, end_time, coach.user_id, user.pk, topic)
            interview.order = order
            interview.save()
        return True, order, interview, None
    else:
        return False, None, None, '订单类型错误'


def third_party_payment(channel, order, coupon_no):
    if channel == 1:  # 微信支付
        pay_data = get_pay_data(order, coupon_no)
        success, result = ThirdPartyPayment(channel=channel,
                                            user_id=order.public_attr.user_id,
                                            user_name=order.public_attr.user.true_name).pay(pay_data)
        if not success:
            return False, result
        result['out_trade_no'] = order.order_no.hex
        return True, result
    else:
        return False, '支付通道错误'


def get_pay_data(order, coupon_no):
    payer = {'openid': order.public_attr.user.openid}
    amount = order.total_amount
    description = order.order_commodities.all().first().commodity.desc
    out_trade_no = order.order_no.hex
    pay_data = {
        "description": description,
        "out_trade_no": out_trade_no,
        "amount": {'total': amount},
        "payer": payer
    }
    if coupon_no:
        attach = coupon_no
        pay_data['attach'] = attach
        coupon = Coupon.objects.get(coupon_no=coupon_no)
        if coupon.stock.stock_type == StockTypesEnum.DISCOUNT:
            goods_tag = coupon.stock.goods_tag
            pay_data['goods_tag'] = goods_tag
    return pay_data


def remove_order_data(order, interview, is_delete=True):
    with transaction.atomic():
        order.deleted = True
        order.save()
        order.order_commodities.all().update(deleted=True)
        if interview:
            public_attr = interview.public_attr
            if is_delete:
                interview.deleted = True
                interview.save()
            Schedule.objects.filter(public_attr=public_attr).update(deleted=True)


def create_refund_order(order, reason, refund_type):
    if order.payer_amount == 0:
        order.status = OrderStatusEnum.refunded
        order.save()
        # 订单退款给教练发送通知
        work_wechat_user = WorkWechatUser.objects.filter(
            wx_user_id__isnull=False, user_id=order.public_attr.target_user, deleted=False).first()
        if work_wechat_user:
            coach_user = order.public_attr.target_user
            getui.send_work_wechat_coach_notice.delay(
                work_wechat_user.wx_user_id,
                'to_c_order_refund_notice',
                coach_name=coach_user.cover_name,
                coach_id=coach_user.id,
                refund_money=str(Decimal(str(order.payer_amount)) / Decimal('100')),
            )
        return True, None

    # 当前一笔订单最多对应一个退款单，后期如有一个订单多笔退款需优化
    if refund_type == 'wechat':
        if order.refunds.all().filter(status=RefundStatusEnum.PROCESSING, deleted=False).exists():
            refund_order = order.refunds.all().filter(status=RefundStatusEnum.PROCESSING, deleted=False).first()
        else:
            refund_order = RefundOrder.objects.create(order=order, reason=reason, status=RefundStatusEnum.PROCESSING)
        # 退款直接使用总金额
        order = refund_order.order
        refund_amount = order.total_amount

        refund_data = {
            "out_refund_no": refund_order.refund_no.hex,
            "amount": {'refund': refund_amount, 'total': order.total_amount, 'currency': 'CNY', },
            "out_trade_no": order.order_no.hex,
            "notify_url": settings.REFUNDS_NOTIFY_URL,
            "reason": reason,
            "funds_account": "AVAILABLE"
        }
        success, result = ThirdPartyPayment(channel=1,
                                            user_id=order.public_attr.user_id,
                                            user_name=order.public_attr.user.cover_name).refunds(refund_data)
        if not success:
            return False, result
        order.status = OrderStatusEnum.under_refund
        order.save()
    return True, None


pay_text = """<view style="font-size: 28rpx;font-weight: 400;color:#83869B;line-height: 50rpx;">1、购买之后，可以预约教练的时间；<br/>2、教练将在收到预约申请后的24小时内回复您。<br/>3、付款成功后，如需更改或取消该次预约，需在本次预约咨询开始前，至少提前4小时联系教练进行更改或取消；<br/>4、如预约失败或取消预约，辅导次数将返还到原账户；<br/>5、辅导开始时间4小时内不可申请取消本次预约；<br/>6、取消辅导申请，以群智企业教练小程序提交时间为准；<br/> 7、教练辅导服务仅限本人预约使用，不可代他人预约；<br/> 8、如果预约遇到问题，请联系添加企业微信获得帮助；</view>"""


def get_order_coach_info(coach, user=None, activity_id=None, order_id=None):
    """
    获取指定教练的头像、姓名、价格等信息，并根据提供的用户和活动ID获取相关订单信息。

    :param coach: 教练对象
    :param user: 用户对象，可选
    :param activity_id: 活动ID，可选
    :param order_id: 订单号，可选
    :return: 包含教练信息和订单信息的字典
    """
    resume = coach.resumes.filter(is_customization=False, deleted=False).first()
    work_wechat_qrcode = WorkWechatUser.objects.filter(
        wx_user_id__isnull=False,
        deleted=False,
        user_id=coach.user_id).values_list('qr_code', flat=True).first()

    data = {"true_name": coach.personal_name if coach.personal_name else coach.user.cover_name,
            "head_image_url": resume.head_image_url if resume.head_image_url else coach.user.head_image_url,
            "price": coach.display_price, 'coach_type': coach.coach_type, 'nickname': None, 'coachee_email': None,
            "interview_start_date": None, "interview_end_date": None, 'work_wechat_qrcode': work_wechat_qrcode,
            "activity_id": None
            }
    if user:
        if order_id:
            orders = Order.objects.filter(pk=order_id, deleted=False).order_by('created_at')
        else:
            orders = Order.objects.filter(public_attr__user=user, deleted=False,
                                          public_attr__target_user=coach.user,
                                          public_attr__type=ATTR_TYPE_ORDER,
                                          status=OrderStatusEnum.paid).exclude(
                                          activity__interview_end_date__lt=datetime.datetime.now()
                                          ).order_by('created_at')
            
        total_remain_hours = 0

        # 当有用户从活动详情进入预约辅导页面时，活动数据要使用适配当前活动的订单数据
        # 从可用辅导时长入口来，查询未使用的第一个订单的活动数据
        if activity_id:
            tmp_order = orders.filter(activity_id=activity_id).first()
        else:
            tmp_order = None

        if orders.exists():
            for order in orders:
                interview_time = order.interview.filter(
                    deleted=False).exclude(
                    public_attr__status=ATTR_STATUS_INTERVIEW_CANCEL).aggregate(times_minute=Sum('times'))
                if interview_time['times_minute']:
                    interview_hour = round(interview_time['times_minute'] / 60, 1)
                else:
                    interview_hour = 0
                remain_hours = order.count - interview_hour
                if remain_hours > 0:
                    total_remain_hours += remain_hours
                    if not tmp_order:
                        tmp_order = order

            if total_remain_hours:
                data['hours'] = total_remain_hours

        # 获取个人用户信息
        personal_user = PersonalUser.objects.filter(user_id=user.id, deleted=False).first()
        if personal_user:
            data['nickname'] = personal_user.nickname
            data['coachee_email'] = personal_user.email

        # 如果第一单是活动单，则返回活动的辅导可预约时间
        if tmp_order and tmp_order.activity:
            data['interview_start_date'] = tmp_order.activity.interview_start_date.strftime('%Y-%m-%d')
            data['interview_end_date'] = tmp_order.activity.interview_end_date.strftime('%Y-%m-%d')
            data['activity_id'] = str(tmp_order.activity_id)
    return data


def get_verify_order_detail(user, coach, type, hours, activity=None, personal_activity=None):
    """
    获取待付款订单金额详情，金额单位（分）
    :param user: 用户实例
    :param coach: 教练实例
    :param type: 订单类型
    :param hours: 预定小时数
    :param activity: 活动实例
    :param personal_activity: 个人活动实例
    :return: 包含订单金额详情的字典
    """
    # 面试教练类型订单并且有公益活动关联
    if type == 'coach_interview' and activity:
        if activity.type == ActivityTypeEnum.public_welfare_coach.value:
            # 更新或创建教练对应的商品信息，并根据活动设置的金额修改价格为
            update_coach_commodity(coach, activity.price)

            total_amount = Decimal(str(activity.price)) * Decimal(str(hours))
            data = {
                "amount": str(total_amount / Decimal('100')),  # 实付金额
                "total_amount": str(total_amount / Decimal('100')),  # 总金额
                "pay_text": pay_text,  # 支付文本说明
                "discount_amount": 0,  # 优惠金额
                "coupon_id": None,  # 优惠券ID
                "coupon_no": None,  # 优惠券编号
                "coupon_name": None,  # 优惠券名称
                "hours": hours  # 预定小时数
            }
            return data

    # 面试教练类型订单并且有私人活动关联
    elif type == 'coach_interview' and personal_activity:
        if personal_activity.type in [PersonalActivityTypeEnum.coach_share.value, PersonalActivityTypeEnum.wechat_share.value]:
            # 更新或创建教练对应的商品信息，价格为活动价格
            update_coach_commodity(coach, personal_activity.price)
            # 教练分享海报不能使用折扣劵 获取用户可以使用的优惠券及其金额
            # amount, coupon = get_use_coupon(coach.price, user)

            # 计算总金额和实付金额，转换为“分”为单位
            total_amount = Decimal(str(personal_activity.price)) * Decimal(str(hours))
            data = {
                "discount_amount": 0,
                "coupon_id": None,
                "coupon_no": None,
                "coupon_name": None,
                "amount": str(total_amount / Decimal('100')),  # 实付金额
                "total_amount": str(total_amount / Decimal('100')),  # 总金额
                "pay_text": pay_text,  # 支付文本说明
                "hours": hours  # 预定小时数
            }
            return data

    # 普通的面试教练类型订单
    elif type == 'coach_interview':
        # 1. 更新或创建教练对应的商品信息
        update_coach_commodity(coach, coach.price)
        # 2. 获取用户可用最大优惠的优惠券,并计算当前优惠券优惠后的金额;返回优惠金额及优惠券信息
        if hours == 1 and coach.price and coach.price > 0:
            amount, coupon = get_use_coupon(coach.price, user)
        else:
            amount, coupon = None, None

        if not coupon:
            data = {
                "amount": str(Decimal(str(coach.price)) * Decimal(str(hours)) / Decimal('100')),
                "total_amount": str(Decimal(str(coach.price)) * Decimal(str(hours)) / Decimal('100')),
                "pay_text": pay_text,
                "discount_amount": 0,
                "coupon_id": None,
                "coupon_no": None,
                "coupon_name": None,
                "hours": hours
            }
            return data
        data = {
            "amount": str((Decimal(str(coach.price)) * Decimal(str(hours)) - amount) / Decimal('100')),  # 实付金额
            "total_amount": str(Decimal(str(coach.price)) * Decimal(str(hours)) / Decimal('100')),  # 总金额
            "pay_text": pay_text,
            "discount_amount": str(amount / Decimal('100')),  # 优惠金额
            "coupon_id": coupon.coupon_id if coupon.coupon_id else None,
            "coupon_no": coupon.coupon_no,
            "coupon_name": coupon.coupon_name,
            "hours": hours
        }
        return data
    return {}


def create_order_info(data, type, hours, activity=None, personal_activity=None, poster=None):
    """
    创建订单
    :param data: 订单信息，包含教练和学员的用户ID
    :param type: 订单类型。1:预约教练订单；2:公益教练订单
    :param hours: 预订小时数
    :param activity: 活动实例，如果有的话 可以是PersonalActivity或Activity的实例
    :return: 返回一个元组，包含操作是否成功的布尔值、订单对象、错误信息
    """
    try:
        coach = Coach.objects.get(user_id=data['coach_user_id'], deleted=False)
        user = User.objects.get(pk=data['coachee_user_id'], deleted=False)
    except (KeyError, Coach.DoesNotExist, User.DoesNotExist):
        return False, None, '参数错误'
    if not user.openid:
        return False, None, '请先绑定微信'

    if type == 1 and activity:  # 预约教练订单, 参与公益教练活动
        if activity.type == ActivityTypeEnum.public_welfare_coach.value:
            # 查找商品
            commodity = Commodity.objects.filter(object_id=coach.pk,
                                                 type=CommodityTypeEnum.coaching, deleted=False).first()
            if not commodity:
                return False, None, '教练服务商品错误'
            with transaction.atomic():
                order_public_attr = PublicAttr.objects.create(user_id=user.pk, type=ATTR_TYPE_ORDER,
                                                              target_user_id=coach.user_id)

                if activity.price:
                    total_amount = int(Decimal(str(activity.price)) * Decimal(str(hours)))
                else:
                    total_amount = 0
                # 创建订单
                order_data = {
                    "total_amount": total_amount,
                    "public_attr_id": order_public_attr.pk,
                    "status": OrderStatusEnum.pending_pay.value if total_amount else OrderStatusEnum.paid.value,
                    "channel": SettlementChannelEnum.wechat.value,
                    "count": hours,
                    "activity_id": activity.pk
                }
                if order_data['status'] == OrderStatusEnum.paid.value:
                    order_data['success_time'] = datetime.datetime.now()
                order = Order.objects.create(**order_data)
                # 创建订单-商品关系
                Order2Commodity.objects.create(commodity=commodity, order=order)

            return True, order, None
        else:
            return False, None, '无效的活动类型'
    elif personal_activity:
        if personal_activity.type in [PersonalActivityTypeEnum.coach_share.value, PersonalActivityTypeEnum.wechat_share.value]:
            # 查找商品
            commodity = Commodity.objects.filter(object_id=coach.pk,
                                                 type=CommodityTypeEnum.coaching, deleted=False).first()
            if not commodity:
                return False, None, '教练服务商品错误'
            with transaction.atomic():
                order_public_attr = PublicAttr.objects.create(user_id=user.pk, type=ATTR_TYPE_ORDER,
                                                              target_user_id=coach.user_id)
                # 创建订单
                order_data = {
                    "total_amount": int(Decimal(str(personal_activity.price)) * Decimal(str(hours))),
                    "public_attr_id": order_public_attr.pk,
                    "status": OrderStatusEnum.pending_pay.value if personal_activity.price else OrderStatusEnum.paid.value,
                    "channel": SettlementChannelEnum.wechat,
                    "personal_activity": personal_activity,
                    "poster": poster,
                    "count": hours
                }
                if order_data['status'] == OrderStatusEnum.paid.value:
                    order_data['success_time'] = datetime.datetime.now()
                order = Order.objects.create(**order_data)
                # 创建订单-商品关系
                Order2Commodity.objects.create(commodity=commodity, order=order)

            return True, order, None
        else:
            return False, None, '无效的个人活动类型'
    elif type == 1:  # 预约教练订单
        # 查找商品
        commodity = Commodity.objects.filter(object_id=coach.pk,
                                             type=CommodityTypeEnum.coaching, deleted=False).first()
        if not commodity:
            return False, None, '教练服务商品错误'

        with transaction.atomic():
            order_public_attr = PublicAttr.objects.create(user_id=user.pk, type=ATTR_TYPE_ORDER,
                                                          target_user_id=coach.user_id)
            # 创建订单
            order_data = {
                "total_amount": int(Decimal(str(coach.price)) * Decimal(str(hours))),
                "public_attr_id": order_public_attr.pk,
                "status": OrderStatusEnum.pending_pay.value if coach.price else OrderStatusEnum.paid.value,
                "channel": SettlementChannelEnum.wechat,
                "count": hours
            }
            order = Order.objects.create(**order_data)
            # 创建订单-商品关系
            Order2Commodity.objects.create(commodity=commodity, order=order)

        return True, order, None
    else:
        return False, None, '订单类型错误'


def remove_order(order):
    with transaction.atomic():
        order.deleted = True
        order.save()
        order.order_commodities.all().update(deleted=True)