from ast import literal_eval
from datetime import datetime, timedelta

import pendulum
import redis
import json
from drf_yasg import openapi
from rest_framework.decorators import action
from django.db import transaction
from django.db.models import Q, Sum
from django.conf import settings

from drf_yasg.utils import swagger_auto_schema

from wisdom_v2.models_file.coach import DismissedTodoItem
from utils import task
from utils.miniapp_version_judge import compare_version
from wisdom_v2.common import schedule_public, action_plan_public, order_public
from wisdom_v2.common import interview_public
from wisdom_v2.enum.chemical_interview_enum import ChemicalInterviewStatusEnum, ChemicalInterviewCoachSourceEnum
from wisdom_v2.enum.user_enum import UserTmpEnum
from wisdom_v2.models import ProjectInterview, PublicAttr, Schedule, ProjectMember, User, Project, \
    InterviewRecordTemplateAnswer, QuestionObjectRelationship, ActionPlan, Diary, Habit, CoachAppraise, \
    ProjectInterviewRecord, ProjectCoach, UserTmp, Coach<PERSON><PERSON>, WorkWechat<PERSON><PERSON>, <PERSON><PERSON>o<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, \
    InterviewRecordTemplateQuestion, ProjectNote, CustomerPortrait
from .app_interview_actions import AppInterviewDetailSerializers, AppInterviewListSerializers, \
    time_hour, InterviewRecordQuestionAnswerEditDetailSerializer, \
    check_answer_data, InterviewRecordQuestionAnswerEditDetailTraineeCoachSerializer, \
    AppTodoListSerializers, InterviewRecordReportDetailSerializer, AppTodoListCoachTaskViewSerializers, \
    AppStakeholderInterviewDetailSerializer, AppInterviewListOrderSerializer
from rest_framework.viewsets import GenericViewSet

from utils.pagination import StandardResultsSetPagination
from utils.send_account_email import get_project_manager_wx_qr_code
from utils.api_response import success_response, parameter_error_response
from wisdom_v2.models_file import ChemicalInterview2Coach, StakeholderInterview, StakeholderInterviewModule, \
    CancelInterview2StakeholderInterview, GrowthGoalsModule, CoachAssociatedCompany
from wisdom_v2.views.constant import ATTR_TYPE_INTERVIEW, INTERVIEW_TYPE_COACHING, ATTR_STATUS_INTERVIEW_CANCEL, \
    BUNDLE_INTEWVIEW_TYPE_ONLINE_1V1, ATTR_TYPE_COACH_TASK, ATTR_STATUS_INTERVIEW_CONFIRM
from utils.messagecenter.center import push_v2_message, send_scoring_risk_alert_email
from utils.messagecenter.getui import push_to_single, send_work_wechat_coach_notice
from wisdom_v2.enum.project_interview_enum import ProjectInterviewPlaceCategoryEnum, ObjectTypeEnum, \
    InterviewRecordTypeEnum, ProjectInterviewTypeEnum, GroupCoachTypeEnum, DataType, InterviewSubjectEnum
from wisdom_v2.enum.service_content_enum import InterviewRecordTemplateQuestionTypeEnum, \
    InterviewRecordTemplateQuestionAnswerTypeEnum, CoachTypeEnum, InterviewRecordTemplateRoleEnum, ScheduleTypeEnum, \
    CoachOfferStatusEnum, ScheduleApplyTypeEnum, InterviewRecordTemplateQuestionRatingTypeEnum, OneToOneMatchTypeEnum
from wisdom_v2.app_views.app_interview_offline_record import save_public_attr
from wisdom_v2.views.constant import ATTR_TYPE_HABIT,  \
    BUNDLE_INTEWVIEW_TYPE_OFFLINE_GROUP
from wisdom_v2.app_views.app_order_action import create_refund_order
from utils.queryset import multiple_field_distinct
from utils.task import send_interview_view_message, send_coach_coachee_record_finish_message, \
    send_stakeholder_create_interview_notice, coach_agree_interview_message, \
    update_stakeholder_interview_coach_task_tmp_data
from wisdom_v2.enum.user_enum import UserRoleEnum
from rest_framework import exceptions
from wisdom_v2.enum.message_type_enum import LarkMessageTypeEnum

interview_redis = redis.Redis.from_url(settings.DATA_REDIS)


class AppInterviewViewSet(GenericViewSet):
    queryset = ProjectInterview.objects.filter(deleted=False).exclude(public_attr__status=ATTR_STATUS_INTERVIEW_CANCEL).order_by('-public_attr__start_time')
    serializer_class = AppInterviewDetailSerializers

    @swagger_auto_schema(
        operation_id='app创建约谈信息',
        operation_summary='app创建约谈信息',
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'project_id': openapi.Schema(type=openapi.TYPE_NUMBER, description='项目id, 普通被教练者预约辅导必填'),
                'coach_user_id': openapi.Schema(type=openapi.TYPE_NUMBER, description='教练id'),
                'user_id': openapi.Schema(type=openapi.TYPE_NUMBER, description='用户id'),
                'topic': openapi.Schema(type=openapi.TYPE_STRING, description='约谈议题'),
                'place_category': openapi.Schema(type=openapi.TYPE_NUMBER, description='约谈分类1:线上平台2:线上其它3:线下'),
                'start_time': openapi.Schema(type=openapi.TYPE_STRING, description='约谈开始时间'),
                'end_time': openapi.Schema(type=openapi.TYPE_STRING, description='约谈结束时间'),
                'place': openapi.Schema(type=openapi.TYPE_STRING, description='其它平台或线下地址'),
                'status': openapi.Schema(type=openapi.TYPE_NUMBER, description='状态'),
            }
        ),
        tags=['app约谈相关']
    )
    @action(methods=['post'], detail=False, url_path='add')
    def interview_add(self, request, *args, **kwargs):
        try:
            project_id = int(request.data.get('project_id', 0))
            coach_user_id = int(request.data.get('coach_user_id', 0))
            user_id = int(request.data.get('user_id', 0))
            topic = request.data.get('topic', '教练辅导')
            start_time = request.data.get('start_time', None)
            end_time = request.data.get('end_time', None)
            place = request.data.get('place', None)
            place_category = int(request.data.get('place_category', 0))
            status = int(request.data.get('status', 3))
            coach_user = User.objects.get(pk=coach_user_id)
            user = User.objects.get(pk=user_id)
            user_role = int(request.headers.get('role'))
            mp = json.loads(request.headers.get('mp')) if request.headers.get('mp') else None
        except Exception as e:
            return parameter_error_response()

        # 获取教练可预约日程
        if not start_time or not end_time:
            return {
                    'is_add': False,
                    'is_show_qrcode': False,
                    'message': '时间参数错误'
                }

        start_date_time = datetime.strptime(start_time, '%Y-%m-%d %H:%M')
        end_date_time = datetime.strptime(end_time, '%Y-%m-%d %H:%M')
        time_slots = schedule_public.get_schedule_day_list(coach_user_id, start_date_time, apply_type=ScheduleApplyTypeEnum.project.value)
        if user_role == 2:   # 普通被教练者预约辅导
            project = Project.objects.get(pk=project_id)
            project_member = ProjectMember.objects.filter(user_id=user_id, project=project, deleted=False,
                                                          is_forbidden=False).first()

            if not project_member or not project_member.coach_match_type:
                qr_code = get_project_manager_wx_qr_code(project_id)
                data = {
                    'is_add': False,
                    'is_show_qrcode': True,
                    'message': '无一对一辅导请扫码联系管理员添加辅导',
                    'qr_code': qr_code
                }
                return success_response(data)

            # 如果配置的是教练池，教练是否在教练池中
            elif project_member.coach_match_type == OneToOneMatchTypeEnum.all_project_coach.value:
                if not ProjectCoach.objects.filter(project=project, deleted=False, coach__user_id=coach_user_id,
                                                   project_group_coach__isnull=True).exists():
                    qr_code = get_project_manager_wx_qr_code(project_id)
                    data = {
                        'is_add': False,
                        'is_show_qrcode': True,
                        'message': '当前项目未匹配该教练',
                        'qr_code': qr_code
                    }
                    return success_response(data)

            # 如果配置的是指定教练，是否指定教练
            if project_member.coach_match_type == OneToOneMatchTypeEnum.appoint_coach.value:
                if not ProjectCoach.objects.filter(project=project, coach__user_id=coach_user_id, member_id=user_id,
                                                   project_group_coach__isnull=True, deleted=False).exists():
                    qr_code = get_project_manager_wx_qr_code(project_id)
                    data = {
                        'is_add': False,
                        'is_show_qrcode': True,
                        'message': '当前被教练者未与当前教练匹配',
                        'qr_code': qr_code
                    }
                    return success_response(data)

            flag, times = time_hour(start_time, end_time, user_id, project_id)

            project_bundle = project_member.project_bundle.filter(deleted=False).first()

            if not project_bundle:
                qr_code = get_project_manager_wx_qr_code(project_id)
                data = {
                    'is_add': False,
                    'is_show_qrcode': True,
                    'message': '当前被教练者未配置服务内容',
                    'qr_code': qr_code
                }
                return success_response(data)

            if not project_bundle.one_to_one_coach.filter(deleted=False).exists():
                qr_code = get_project_manager_wx_qr_code(project_id)
                data = {
                    'is_add': False,
                    'is_show_qrcode': True,
                    'message': '当前被教练者未配置1对1辅导模块',
                    'qr_code': qr_code
                }
                return success_response(data)

            if place_category == ProjectInterviewPlaceCategoryEnum.online_one_to_one.value:  # 线上一对一
                if not project_bundle.one_to_one_coach.filter(deleted=False, type=CoachTypeEnum.online.value).exists():
                    qr_code = get_project_manager_wx_qr_code(project_id)
                    data = {
                        'is_add': False,
                        'is_show_qrcode': True,
                        'message': '当前被教练者服务内容不包含线上1对1辅导',
                        'qr_code': qr_code
                    }
                    return success_response(data)
                # 判断项目被教练者一对一辅导时长限制
                result, available_time = interview_public.check_member_interview_times(project_member, times)
                if not result:
                    qr_code = get_project_manager_wx_qr_code(project_id)
                    data = {
                        "is_add": False,
                        'is_show_qrcode': True,
                        "message": "您目前没有可用的教练辅导时长",
                        "qr_code": qr_code
                    }
                    return success_response(data)

            if place_category == ProjectInterviewPlaceCategoryEnum.offline_one_to_one.value:  # 线下一对一

                if not project_bundle.one_to_one_coach.filter(deleted=False, type=CoachTypeEnum.offline.value).exists():
                    qr_code = get_project_manager_wx_qr_code(project_id)
                    data = {
                        "is_add": False,
                        'is_show_qrcode': True,
                        "message": "当前被教练者服务内容不包含线下1对1辅导",
                        'qr_code': qr_code
                    }
                    return success_response(data)

                # 判断项目被教练者一对一辅导时长限制
                result, available_time = interview_public.check_member_interview_times(
                    project_member, times, CoachTypeEnum.offline.value)
                if not result:
                    qr_code = get_project_manager_wx_qr_code(project_id)
                    data = {
                        "is_add": False,
                        'is_show_qrcode': True,
                        "message": "您目前没有可用的教练辅导时长",
                        "qr_code": qr_code
                    }
                    return success_response(data)

            # 判断时间是否可用
            status = schedule_public.is_time_slot_available(time_slots, start_date_time, end_date_time)
            if not status:
                data = {
                    'is_add': False,
                    'is_show_qrcode': False,
                    'message': '教练该时间段时间已预约',
                    'qr_code': ''
                }
                return success_response(data)

            try:
                with transaction.atomic():
                    if mp and compare_version(mp.get('version'), '2.32') >= 0:
                        record_type = InterviewRecordTypeEnum.questionnaire.value
                    else:
                        record_type = InterviewRecordTypeEnum.question_and_answer.value

                    public_attr = PublicAttr.objects.create(start_time=start_time, end_time=end_time, project_id=project_id,
                                                            user_id=coach_user_id, target_user_id=user_id,
                                                            type=ATTR_TYPE_INTERVIEW, status=status)
                    Schedule.objects.create(public_attr_id=public_attr.pk, type=ScheduleTypeEnum.interview, title=topic)

                    project_interview = ProjectInterview.objects.create(public_attr_id=public_attr.pk,
                                                                        type=INTERVIEW_TYPE_COACHING,
                                                                        place_category=place_category,
                                                                        record_type=record_type,
                                                                        is_coach_agree=True,  # B端默认教练同意
                                                                        times=times)
                    if topic:
                        project_interview.coachee_topic = topic
                        project_interview.topic = topic
                    if place:
                        project_interview.place = place
                    project_interview.save()
                    interview_time = project_interview.public_attr.start_time.strftime('%Y-%m-%d %H:%M') +\
                                     '-' + project_interview.public_attr.end_time.strftime('%H:%M')
                    push_v2_message.delay(coach_user, 'interview_add', param={'interview_time': interview_time,
                                                                              'target_user': user.cover_name}, project_id=project_id)
                    push_v2_message.delay(user, 'interview_add', param={'interview_time': interview_time,
                                                                        'target_user': coach_user.cover_name}, project_id=project_id)
                                                                        
                    # 去掉app通知
                    # push_to_single.delay(user.user_cid, title='%s, 你和%s有一个教练辅导预约成功!' % (
                    #     user.cover_name, coach_user.cover_name), body='想知道更多的日程详情，快去前往查看吧')
                    # push_to_single.delay(coach_user.user_cid, title='%s, 你和%s有一个教练辅导预约成功!' % (
                    #     coach_user.cover_name, user.cover_name), body='想知道更多的日程详情，快去前往查看吧')

                    # 教练通知
                    work_wechat_user = WorkWechatUser.objects.filter(
                        wx_user_id__isnull=False,
                        user_id=coach_user_id,
                        deleted=False
                    ).first()
                    if work_wechat_user:
                        company = project_interview.public_attr.project.company
                        company_name = company.real_name
                        send_work_wechat_coach_notice.delay(
                            work_wechat_user.wx_user_id,
                            'coach_add_interview',
                            coachee_name=user.cover_name,
                            company=company_name,
                            date=interview_time,
                            coach_id=project_interview.public_attr.user.id,
                            project_id=project_interview.public_attr.project_id,
                            project_name=project_interview.public_attr.project.name,
                            coachee_id=project_interview.public_attr.target_user.id,
                            coach_name=project_interview.public_attr.user.cover_name,
                            content_item=[
                                {
                                    "key": "客户名称", "value": user.cover_name
                                },
                                {
                                    "key": "所属企业", "value": company_name
                                },
                                {
                                    "key": "所属项目", "value": project_interview.public_attr.project.name
                                },
                                {
                                    "key": "辅导时长", "value": '{}分钟'.format(project_interview.times)
                                },
                                {
                                    "key": "辅导时间", "value": interview_time,
                                },
                            ])
                        if mp and compare_version(mp.get('version'), '2.33') >= 0:
                            # 创建腾讯会议
                            time_delta = end_date_time - start_date_time
                            task.create_project_interview_meeting.delay(
                                project_interview.id, work_wechat_user.wx_user_id, '教练辅导',
                                int(start_date_time.timestamp()), time_delta.seconds)
                    # else:
                    #     msg = f'预约辅导通知 通过企业微信发送客户提醒消息时，' \
                    #           f'ID为{project_interview.public_attr.user.id}的' \
                    #           f'{project_interview.public_attr.user.cover_name}教练没有绑定企业微信，无法发送'
                    #     push_wx_error_message(name='消息发送失败提醒', level='warning', content=msg)

                    # 教练前准备-客户
                    member_send_msg_status = ProjectMember.objects.filter(role=6, user=user, project_id=project_id,
                                                                          coachee_first_msg_status=False, deleted=False)
                    if member_send_msg_status.first():
                        push_v2_message.delay(user, 'coachee_first_msg', param={}, project_id=project_id)
                        # push_to_single.delay(user.user_cid, title='%s, 你为即将开始的教练辅导做好准备了吗？' % (
                        #     user.cover_name), body='掌握这些要点，最大化教练辅导的投入产出比')
                        member_send_msg_status.update(coachee_first_msg_status=True)

                    # 发送飞书消息给项目运营
                    params = {'project_name': f'{project.full_name}',
                              'user_name': project_interview.public_attr.target_user.cover_name,
                              'coach_name': project_interview.public_attr.user.cover_name,
                              'interview_time': interview_time,
                              'at_info': project.project_manager_feishu_at_info}
                    task.send_lark_business_message.delay(LarkMessageTypeEnum.project_interview_add.value, params)

                    return success_response(request=request)
            except Exception as e:
                return parameter_error_response()

        elif user_role == 4:  # 三无学员预约辅导
            # 判断当前三无学员能否预约当前教练
            interview = PublicAttr.objects.filter(project__isnull=True, target_user_id=user_id, type=1).exclude(
                status=6)
            user_ids = list(interview.values_list('user_id', flat=True))
            if coach_user_id not in user_ids:
                data = {
                    'is_add': False,
                    'is_show_qrcode': False,
                    'message': '无法预约当前教练'
                }
                return success_response(data)
            status = schedule_public.is_time_slot_available(time_slots, start_date_time, end_date_time)
            if not status:
                data = {
                    'is_add': False,
                    'is_show_qrcode': False,
                    'message': '教练该时间段时间已预约'
                }
                return success_response(data)
            start_time = datetime.strptime(start_time, '%Y-%m-%d %H:%M')
            end_time = datetime.strptime(end_time, '%Y-%m-%d %H:%M')
            time_delta = end_time - start_time
            times = int(round(time_delta.seconds / 60, 1))

            try:
                with transaction.atomic():
                    public_attr = PublicAttr.objects.create(start_time=start_time, end_time=end_time,
                                                            user_id=coach_user_id, target_user_id=user_id,
                                                            type=ATTR_TYPE_INTERVIEW, status=status)
                    Schedule.objects.create(public_attr_id=public_attr.pk, type=ScheduleTypeEnum.interview, title=topic)

                    project_interview = ProjectInterview.objects.create(public_attr_id=public_attr.pk,
                                                                        type=INTERVIEW_TYPE_COACHING,
                                                                        place_category=place_category,
                                                                        times=times, record_type=1)

                    if topic:
                        project_interview.coachee_topic = topic
                        project_interview.topic = topic
                    if place:
                        project_interview.place = place
                    project_interview.save()
                    interview_time = project_interview.public_attr.start_time.strftime('%Y-%m-%d %H:%M') + \
                                     '-' + project_interview.public_attr.end_time.strftime('%H:%M')
                    push_v2_message.delay(coach_user, 'interview_add', param={'interview_time': interview_time,
                                                                              'target_user': user.cover_name}, project_id=project_id)
                    push_v2_message.delay(user, 'interview_add', param={'interview_time': interview_time,
                                                                        'target_user': coach_user.cover_name}, project_id=project_id)
                    # push_to_single.delay(user.user_cid, title='%s, 你和%s有一个教练辅导预约成功!' % (
                    #     user.cover_name, coach_user.cover_name), body='想知道更多的日程详情，快去前往查看吧')
                    # push_to_single.delay(coach_user.user_cid, title='%s, 你和%s有一个教练辅导预约成功!' % (
                    #     coach_user.cover_name, user.cover_name), body='想知道更多的日程详情，快去前往查看吧')
                    return success_response(request=request)
            except Exception as e:
                return parameter_error_response()

        else:
            return parameter_error_response('未知当前用户')


    @swagger_auto_schema(
        operation_id='app创建化学面谈信息',
        operation_summary='app创建化学面谈信息',
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'project_id': openapi.Schema(type=openapi.TYPE_NUMBER, description='项目id, 普通被教练者预约辅导必填'),
                'coach_user_id': openapi.Schema(type=openapi.TYPE_NUMBER, description='教练id'),
                'user_id': openapi.Schema(type=openapi.TYPE_NUMBER, description='用户id'),
                'topic': openapi.Schema(type=openapi.TYPE_STRING, description='约谈议题'),
                'start_time': openapi.Schema(type=openapi.TYPE_STRING, description='约谈开始时间'),
                'end_time': openapi.Schema(type=openapi.TYPE_STRING, description='约谈结束时间'),
                'place': openapi.Schema(type=openapi.TYPE_STRING, description='其它平台或线下地址'),
            }
        ),
        tags=['app约谈相关']
    )
    @action(methods=['post'], detail=False, url_path='chemical_interview_add')
    def chemical_interview_add(self, request, *args, **kwargs):
        try:
            project_id = request.data['project_id']
            coach_user_id = request.data['coach_user_id']
            user_id = request.data['user_id']
            topic = request.data.get('topic', '进行初步的沟通')
            start_time = request.data['start_time']
            end_time = request.data['end_time']
            place = request.data.get('place')
            project_member = ProjectMember.objects.get(project_id=project_id, user_id=user_id, deleted=False)
            coach = Coach.objects.get(user_id=coach_user_id, deleted=False)
            mp = json.loads(request.headers.get('mp')) if request.headers.get('mp') else None
        except (KeyError, ProjectMember.DoesNotExist, Coach.DoesNotExist):
            return parameter_error_response()
        # 查看当前教练是否已约满
        coach_offer = CoachOffer.objects.filter(project_offer__project_id=project_id, coach=coach,
                                                status=CoachOfferStatusEnum.joined, deleted=False,
                                                project_offer__deleted=False).first()
        if coach_offer:
            count = ChemicalInterview2Coach.objects.filter(coach=coach, interview__isnull=False, deleted=False,
                                                           interview__public_attr__project_id=project_id). \
                exclude(chemical_interview_status=ChemicalInterviewStatusEnum.unselected).count()
            if coach_offer.max_customer_count and count >= coach_offer.max_customer_count:
                data = {
                    "is_add": False,
                    'is_show_qrcode': False,
                    'message': '教练已约满',
                    'qr_code': ''
                }
                return success_response(data)

        start_date_time = datetime.strptime(start_time, '%Y-%m-%d %H:%M')
        end_date_time = datetime.strptime(end_time, '%Y-%m-%d %H:%M')
        time_slots = schedule_public.get_schedule_day_list(coach_user_id, start_date_time, apply_type=ScheduleApplyTypeEnum.project.value)
        status = schedule_public.is_time_slot_available(time_slots, start_date_time, end_date_time)
        if not status:
            data = {
                'is_add': False,
                'is_show_qrcode': False,
                'message': '教练该时间段时间已预约',
                'qr_code': ''
            }
            return success_response(data)
        start_time = datetime.strptime(start_time, '%Y-%m-%d %H:%M')
        end_time = datetime.strptime(end_time, '%Y-%m-%d %H:%M')
        time_delta = end_time - start_time
        times = int(round(time_delta.seconds / 60, 1))
        chemical_interview_module = project_member.chemical_interview.filter(deleted=False).first()
        if not chemical_interview_module:
            data = {
                'is_add': False,
                'is_show_qrcode': False,
                'message': '当前被教练者未配置化学面谈',
                'qr_code': ''
            }
            return success_response(data)
        chemical_interview = chemical_interview_module.coaches.filter(
            coach__user_id=coach_user_id, deleted=False, interview__isnull=True,
            chemical_interview_status=ChemicalInterviewStatusEnum.not_feedback).first()
        if chemical_interview_module.coach_source == ChemicalInterviewCoachSourceEnum.system_random:
            if not chemical_interview:
                data = {
                    'is_add': False,
                    'is_show_qrcode': False,
                    'message': '当前教练未配置',
                    'qr_code': ''
                }
                return success_response(data)
        else:
            if not chemical_interview:
                chemical_interview = ChemicalInterview2Coach(
                    chemical_interview_module=chemical_interview_module, coach=coach,
                    chemical_interview_status=ChemicalInterviewStatusEnum.not_feedback)

        with transaction.atomic():
            public_attr = PublicAttr.objects.create(start_time=start_time, end_time=end_time,
                                                    project=project_member.project,
                                                    user_id=coach_user_id, target_user_id=user_id,
                                                    type=ATTR_TYPE_INTERVIEW, status=ATTR_STATUS_INTERVIEW_CONFIRM)
            Schedule.objects.create(public_attr_id=public_attr.pk, type=ScheduleTypeEnum.interview, title=topic)
            project_interview = ProjectInterview(
                public_attr_id=public_attr.pk, type=ProjectInterviewTypeEnum.chemical_interview,
                place_category=ProjectInterviewPlaceCategoryEnum.online_one_to_one, times=times,
                record_type=InterviewRecordTypeEnum.questionnaire, coachee_topic=topic, place=place, topic=topic)
            project_interview.save()
            chemical_interview.interview = project_interview
            chemical_interview.save()

        work_wechat_user = WorkWechatUser.objects.filter(
            wx_user_id__isnull=False,
            user_id=coach_user_id,
            deleted=False
        ).first()
        if work_wechat_user:
            company = project_interview.public_attr.project.company
            company_name = company.real_name
            send_work_wechat_coach_notice.apply_async(kwargs=dict(
                user=work_wechat_user.wx_user_id, content_type='coachee_add_chemical_interview',
                coachee_name=public_attr.target_user.cover_name,
                company=company_name,
                interview_id=project_interview.id,
                coach_id=project_interview.public_attr.user.id,
                project_id=project_interview.public_attr.project_id,
                project_name=project_interview.public_attr.project.name,
                coachee_id=project_interview.public_attr.target_user.id,
                coach_name=project_interview.public_attr.user.cover_name,
                content_item=[
                    {
                        "key": "客户名称", "value": public_attr.target_user.cover_name
                    },
                    {
                        "key": "所属企业", "value": company_name
                    },
                    {
                        "key": "所属项目", "value": project_interview.public_attr.project.name
                    }
                ]
            ), countdown=30, expires=360)

            if mp and compare_version(mp.get('version'), '2.33') >= 0:
                # 创建腾讯会议
                task.create_project_interview_meeting.delay(
                    project_interview.id, work_wechat_user.wx_user_id, '化学面谈',
                    int(start_date_time.timestamp()), time_delta.seconds)

        return success_response(request=request)

    @swagger_auto_schema(
        operation_id='预约利益相关者访谈',
        operation_summary='预约利益相关者访谈',
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'stakeholder_interview_id': openapi.Schema(type=openapi.TYPE_NUMBER, description='项目id, 普通被教练者预约辅导必填'),
                'coach_user_id': openapi.Schema(type=openapi.TYPE_NUMBER, description='教练id'),
                'start_time': openapi.Schema(type=openapi.TYPE_STRING, description='约谈开始时间'),
                'end_time': openapi.Schema(type=openapi.TYPE_STRING, description='约谈结束时间'),
            }
        ),
        tags=['app约谈相关']
    )
    @action(methods=['post'], detail=False, url_path='stakeholder_interview_add', authentication_classes=[])
    def stakeholder_interview_add(self, request, *args, **kwargs):
        try:
            stakeholder_interview_id = request.data['stakeholder_interview_id']
            coach_user_id = request.data['coach_user_id']
            start_time = request.data['start_time']
            end_time = request.data['end_time']
            stakeholder_interview = StakeholderInterview.objects.get(id=stakeholder_interview_id, deleted=False)
            coach = Coach.objects.get(user_id=coach_user_id, deleted=False)
            mp = json.loads(request.headers.get('mp')) if request.headers.get('mp') else None
        except (KeyError, StakeholderInterview.DoesNotExist, Coach.DoesNotExist, User.DoesNotExist):
            return parameter_error_response()

        start_date_time = datetime.strptime(start_time, '%Y-%m-%d %H:%M')
        end_date_time = datetime.strptime(end_time, '%Y-%m-%d %H:%M')

        time_slots = schedule_public.get_schedule_day_list(coach_user_id, start_date_time, apply_type=ScheduleApplyTypeEnum.project.value)

        status = schedule_public.is_time_slot_available(time_slots, start_date_time, end_date_time)
        if not status:
            data = {
                'is_add': False,
                'is_show_qrcode': False,
                'message': '教练该时间段时间已预约',
                'qr_code': ''
            }
            return success_response(data)

        exists_stakeholder_interview = ProjectInterview.objects.filter(
            Q(public_attr__start_time__range=(start_date_time, end_date_time)) |  # 查询开始时间在当前预约时间范围内的Interview对象
            Q(public_attr__end_time__range=(start_date_time, end_date_time)) |  # 查询结束时间在当前预约时间范围内的Interview对象
            Q(public_attr__start_time__lte=start_date_time, public_attr__end_time__gte=end_date_time),  # 查询开始时间早于当前预约时间且结束时间晚于当前预约时间的Interview对象。
            type=ProjectInterviewTypeEnum.stakeholder_interview, deleted=False,
            public_attr__target_user_id=stakeholder_interview.project_interested.interested_id,
        ).exclude(
            public_attr__status=ATTR_STATUS_INTERVIEW_CANCEL)
        if exists_stakeholder_interview.exists():
            data = {
                'is_add': False,
                'is_show_qrcode': False,
                'message': f'当前时间已预约{exists_stakeholder_interview.first().public_attr.user.cover_name}教练，'
                           f'请选择其他时间预约',
                'qr_code': ''
            }
            return success_response(data)
        time_delta = end_date_time - start_date_time
        times = int(round(time_delta.seconds / 60, 1))
        project_member = stakeholder_interview.stakeholder_interview_module.project_member
        topic = f"{project_member.user.cover_name}的行为反馈"

        with transaction.atomic():
            public_attr = PublicAttr.objects.create(
                start_time=start_date_time, end_time=end_date_time, project=project_member.project,
                user_id=coach.user_id, target_user_id=stakeholder_interview.project_interested.interested_id,
                type=ATTR_TYPE_INTERVIEW, status=ATTR_STATUS_INTERVIEW_CONFIRM)
            Schedule.objects.create(public_attr_id=public_attr.pk, type=ScheduleTypeEnum.interview, title=topic)
            project_interview = ProjectInterview(
                public_attr_id=public_attr.pk, type=ProjectInterviewTypeEnum.stakeholder_interview,
                place_category=ProjectInterviewPlaceCategoryEnum.online_one_to_one, times=times,
                record_type=InterviewRecordTypeEnum.questionnaire, coachee_topic=topic, topic=topic)
            project_interview.save()
            stakeholder_interview.interview = project_interview
            stakeholder_interview.save()

            # 给教练，利益相关者，项目运营发送通知
            send_stakeholder_create_interview_notice.apply_async(
                    kwargs=dict(stakeholder_interview=stakeholder_interview), countdown=5)

            if mp and compare_version(mp.get('version'), '2.33') >= 0:
                work_wechat_user = WorkWechatUser.objects.filter(
                    wx_user_id__isnull=False, user_id=coach_user_id, deleted=False).first()
                if work_wechat_user:
                    # 创建腾讯会议
                    task.create_project_interview_meeting.delay(
                        project_interview.id, work_wechat_user.wx_user_id, '利益相关者访谈',
                        int(start_date_time.timestamp()), time_delta.seconds)

        return success_response(request=request, data={"interview_id": project_interview.id})


    @swagger_auto_schema(
        operation_id='app约谈详情',
        operation_summary='app约谈详情',
        manual_parameters=[
            openapi.Parameter('interview_id', openapi.IN_QUERY, description='约谈id', type=openapi.TYPE_NUMBER, required=True),
        ],
        tags=['app约谈相关']
    )
    @action(methods=['get'], detail=False, url_path='detail', authentication_classes=[])
    def interview_detail(self, request, *args, **kwargs):
        try:
            instance = ProjectInterview.objects.get(pk=request.query_params.get('interview_id', 0))
            user_role = request.headers.get('role', 0)
            mp = json.loads(request.headers.get('mp')) if request.headers.get('mp') else None
        except ProjectInterview.DoesNotExist:
            return parameter_error_response('约谈id错误')
        # if not user_role:
            # return parameter_error_response('未知用户角色')
        serializer = self.get_serializer(instance, context={'user_role': user_role, 'mp': mp})
        return success_response(serializer.data, request=request)

    @swagger_auto_schema(
        operation_id='app修改约谈信息',
        operation_summary='app修改约谈信息',
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'interview_id': openapi.Schema(type=openapi.TYPE_NUMBER, description='约谈id'),
                'topic': openapi.Schema(type=openapi.TYPE_STRING, description='约谈议题'),
                'start_time': openapi.Schema(type=openapi.TYPE_STRING, description='开始时间'),
                'end_time': openapi.Schema(type=openapi.TYPE_STRING, description='结束时间'),
                'is_coach_agree': openapi.Schema(type=openapi.TYPE_BOOLEAN, description='教练是否同意'),
                'is_time_validate': openapi.Schema(type=openapi.TYPE_BOOLEAN, description='是否检测时间可用'),
            }
        ),
        tags=['app约谈相关']
    )
    @action(methods=['post'], detail=False, url_path='update')
    def interview_update(self, request, *args, **kwargs):
        try:
            interview_id = int(request.data.get('interview_id', 0))
            topic = request.data.get('topic', None)
            start_time = request.data.get('start_time')
            end_time = request.data.get('end_time')
            is_time_validate = request.data.get('is_time_validate')
            instance = ProjectInterview.objects.get(pk=interview_id)
            role = int(request.headers.get('role', None))
            mp = json.loads(request.headers.get('mp')) if request.headers.get('mp') else None
        except (TypeError, ValueError, ProjectInterview.DoesNotExist):
            return parameter_error_response('请求参数错误')
        try:
            with transaction.atomic():
                if topic:
                    instance.topic = topic
                    instance.coachee_topic = topic

                if 'is_coach_agree' in request.data.keys():
                    instance.is_coach_agree = request.data.get('is_coach_agree')
                    # 教练同意辅导 给客户和教练发通知
                    if request.data.get('is_coach_agree'):
                        coach_agree_interview_message.delay(interview_id)
                        if mp and compare_version(mp.get('version'), '2.33') >= 0:
                            # 获取教练企业微信
                            work_wechat_user = WorkWechatUser.objects.filter(
                                wx_user_id__isnull=False, user_id=instance.public_attr.user_id, deleted=False).first()
                            if work_wechat_user:
                                #  教练同意辅导，创建腾讯会议
                                time_delta = instance.public_attr.end_time - instance.public_attr.start_time
                                task.create_project_interview_meeting.delay(
                                    interview_id, work_wechat_user.wx_user_id, '教练辅导',
                                    int(instance.public_attr.start_time.timestamp()), time_delta.seconds)
                instance.save()
                if start_time and end_time:
                    # 检测时间是否冲突，时间冲突需要弹窗提示。
                    if is_time_validate:
                        conflict_interview = interview_public.coach_interview_time_validate(
                            request.user.id, start_time, end_time, interview_id=interview_id)
                        # 如果存在冲突的日程，返回描述信息，弹窗提示
                        if conflict_interview:
                            time_str = (f'{conflict_interview.public_attr.start_time.strftime("%Y年%m月%d日%H:%M")}-'
                                        f'{conflict_interview.public_attr.end_time.strftime("%H:%M")}')
                            return success_response({
                                'is_interview_conflict': True,
                                'msg': f'新的日程与{time_str}的日程有冲突，确认要修改吗？'})

                    interview_public.user_update_interview_time(instance, start_time, end_time, role, mp)
                return success_response(request=request)
        except Exception as e:
            return parameter_error_response()

    @swagger_auto_schema(
        operation_id='利益相关者修改辅导时间',
        operation_summary='利益相关者修改辅导时间',
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'interview_id': openapi.Schema(type=openapi.TYPE_NUMBER, description='约谈id'),
                'start_time': openapi.Schema(type=openapi.TYPE_STRING, description='开始时间'),
                'end_time': openapi.Schema(type=openapi.TYPE_STRING, description='结束时间'),
            }
        ),
        tags=['app约谈相关']
    )
    @action(methods=['post'], detail=False, url_path='stakeholder_change_time', authentication_classes=[])
    def stakeholder_change_time(self, request, *args, **kwargs):
        try:
            interview_id = request.data['interview_id']
            start_time = request.data['start_time']
            end_time = request.data['end_time']
            is_time_validate = request.data.get('is_time_validate')
            instance = ProjectInterview.objects.get(pk=interview_id)
            mp = json.loads(request.headers.get('mp')) if request.headers.get('mp') else None
        except (KeyError, ProjectInterview.DoesNotExist):
            return parameter_error_response()
        with transaction.atomic():
            # 检测时间是否冲突，时间冲突需要弹窗提示。
            if is_time_validate:
                conflict_interview = interview_public.coach_interview_time_validate(
                    request.user.id, start_time, end_time, interview_id=interview_id)
                # 如果存在冲突的日程，返回描述信息，弹窗提示
                if conflict_interview:
                    time_str = (f'{conflict_interview.public_attr.start_time.strftime("%Y年%m月%d日%H:%M")}-'
                                f'{conflict_interview.public_attr.end_time.strftime("%H:%M")}')
                    return success_response({
                        'is_interview_conflict': True,
                        'msg': f'新的日程与{time_str}的日程有冲突，确认要修改吗？'})

            interview_public.user_update_interview_time(instance, start_time, end_time, mp)
        return success_response(request=request)

    @swagger_auto_schema(
        operation_id='app取消约谈',
        operation_summary='app取消约谈',
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'interview_id': openapi.Schema(type=openapi.TYPE_NUMBER, description='约谈id'),
                'reason': openapi.Schema(type=openapi.TYPE_STRING, description='reason'),
            }
        ),
        tags=['app约谈相关']
    )
    @action(methods=['post'], detail=False, url_path='cancel')
    def interview_cancel(self, request, *args, **kwargs):
        try:
            interview_id = int(request.data.get('interview_id', 0))
            reason = request.data.get('reason', None)
            instance = ProjectInterview.objects.get(pk=interview_id)
        except (TypeError, ValueError, ProjectInterview.DoesNotExist):
            return parameter_error_response('请求参数错误')
        if not instance.public_attr.project_id:
            last_time = instance.public_attr.start_time - timedelta(hours=4)
            if datetime.now() > last_time:
                return parameter_error_response('当前距离辅导开始不足4小时，无法取消')
            # if instance.order:
            #     if not instance.order.public_attr.target_user:   # 2.21版本前的订单取消辅导直接退款（2.21版本之前的订单tartget_user为空）
            #         is_success, msg = create_refund_order(instance.order, reason, refund_type='wechat')
            #         if not is_success:
            #             return parameter_error_response('取消辅导失败')

        interview_public.cancel_interview(instance, reason)
        interview_time = instance.public_attr.start_time.strftime('%Y-%m-%d %H:%M') + '-' + instance.public_attr.end_time.strftime('%H:%M')

        if instance.type == ProjectInterviewTypeEnum.chemical_interview:
            chemical_interview = instance.chemical_interview.filter(deleted=False).first()
            if chemical_interview:
                chemical_interview.interview = None
                chemical_interview.chemical_interview_status = ChemicalInterviewStatusEnum.not_feedback
                chemical_interview.save()
        push_v2_message.delay(instance.public_attr.user,  'coach_interview_cancel',
                              param={'target_user': instance.public_attr.target_user.cover_name,
                                     'interview_time': interview_time}, project_id=instance.public_attr.project_id)
        # push_to_single.delay(instance.public_attr.user.user_cid, title='%s,你和%s的教练辅导已取消' % (
        #     instance.public_attr.user.cover_name, instance.public_attr.target_user.cover_name), body='查看取消详情')
        return success_response(request=request)

    @swagger_auto_schema(
        operation_id='利益相关者app取消约谈',
        operation_summary='利益相关者app取消约谈',
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'interview_id': openapi.Schema(type=openapi.TYPE_NUMBER, description='约谈id'),
                'reason': openapi.Schema(type=openapi.TYPE_STRING, description='reason'),
            }
        ),
        tags=['app约谈相关']
    )
    @action(methods=['post'], detail=False, url_path='stakeholder_cancel_interview', authentication_classes=[])
    def stakeholder_cancel_interview(self, request, *args, **kwargs):
        try:
            interview_id = request.data['interview_id']
            reason = request.data.get('reason', None)
            instance = ProjectInterview.objects.get(pk=interview_id)
            stakeholder_interview = StakeholderInterview.objects.get(interview=instance, deleted=False)
        except (ValueError, ProjectInterview.DoesNotExist, StakeholderInterview.DoesNotExist):
            return parameter_error_response()
        with transaction.atomic():
            interview_public.cancel_interview(instance, reason)
            stakeholder_interview.interview = None
            stakeholder_interview.save()
            CancelInterview2StakeholderInterview.objects.create(interview=instance,
                                                                stakeholder_interview=stakeholder_interview)
        interview_time = instance.public_attr.start_time.strftime(
            '%Y-%m-%d %H:%M') + '-' + instance.public_attr.end_time.strftime('%H:%M')
        push_v2_message.delay(instance.public_attr.user, 'coach_interview_cancel',
                              param={'target_user': instance.public_attr.target_user.cover_name,
                                     'interview_time': interview_time}, project_id=instance.public_attr.project_id)
        # push_to_single.delay(instance.public_attr.user.user_cid, title='%s,你和%s的教练辅导已取消' % (
        #     instance.public_attr.user.cover_name, instance.public_attr.target_user.cover_name), body='查看取消详情')
        return success_response(request=request)

    @swagger_auto_schema(
        operation_id='小程序利益相关者辅导详情',
        operation_summary='小程序利益相关者辅导详情',
        manual_parameters=[
            openapi.Parameter('interview_id', openapi.IN_QUERY, description='辅导id', type=openapi.TYPE_NUMBER,
                              required=True),
        ],
        tags=['app约谈相关']
    )
    @action(methods=['get'], detail=False, url_path='stakeholder_interview_detail', authentication_classes=[])
    def stakeholder_interview_detail(self, request, *args, **kwargs):
        try:
            interview_id = request.query_params['interview_id']
            interview = ProjectInterview.objects.get(id=interview_id, deleted=False)
            mp = json.loads(request.headers.get('mp')) if request.headers.get('mp') else None
        except (KeyError, ProjectInterview.DoesNotExist):
            return parameter_error_response()
        serializer = AppStakeholderInterviewDetailSerializer(interview, context={'mp': mp})
        return success_response(serializer.data)

    @swagger_auto_schema(
        operation_id='app约谈确认/完成',
        operation_summary='app约谈确认/完成',
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'interview_id': openapi.Schema(type=openapi.TYPE_NUMBER, description='约谈id'),
                'status': openapi.Schema(type=openapi.TYPE_NUMBER, description='status 4:待记录 5:已结束'),
            }
        ),
        tags=['app约谈相关']
    )
    @action(methods=['post'], detail=False, url_path='update/status', authentication_classes=[])
    def update_status(self, request, *args, **kwargs):
        try:
            interview_id = int(request.data.get('interview_id', 0))
            status = int(request.data.get('status', 0))
            instance = ProjectInterview.objects.get(pk=interview_id)
        except (TypeError, ValueError, ProjectInterview.DoesNotExist):
            return parameter_error_response('请求参数错误')

        instance.public_attr.status = status
        instance.public_attr.save()
        return success_response(request=request)

    @swagger_auto_schema(
        operation_id='app待办事项列表',
        operation_summary='app待办事项列表',
        manual_parameters=[
            openapi.Parameter('user_id', openapi.IN_QUERY, description='用户id', type=openapi.TYPE_NUMBER,
                              required=True),
            openapi.Parameter('page', openapi.IN_QUERY, description='第几页', type=openapi.TYPE_NUMBER,
                              required=False),
            openapi.Parameter('page_size', openapi.IN_QUERY, description='每页显示条数默认15条', type=openapi.TYPE_NUMBER,
                              required=False),
        ],
        tags=['app约谈相关']
    )
    @action(methods=['get'], detail=False, url_path='todo_list')
    def todo_list(self, request, *args, **kwargs):
        try:
            user_id = int(request.query_params.get('user_id', 0))
            user_role = int(request.headers.get('role', None))
            # 建议: 如果你的认证系统设置好了，可以直接用 request.user 获取用户
            # user = request.user
            # 否则按原方式获取:
            user = User.objects.get(pk=user_id)

            # --- 1. 获取当前用户已设置不再提醒的项的标识符 ---
            dismissed_identifiers = set(
                DismissedTodoItem.objects.filter(user=user).values_list('item_identifier', flat=True)
            )
            # -------------------------------------------

        except (TypeError, ValueError):
            return parameter_error_response('请求参数错误')
        except User.DoesNotExist:
            return parameter_error_response('用户不存在') # 更明确的错误
        except Exception as e:
            # 建议记录错误 e
            print(f"获取用户/角色或不再提醒项时出错: {e}")
            return parameter_error_response('获取用户信息出错')

        # === 2. 获取并初步处理各类待办事项 ===

        # --- 2.1 辅导/访谈 (Interviews) ---
        queryset = user.get_miniapp_interview(user_role, is_sort=False)
        queryset = queryset.exclude(
            chemical_interview__chemical_interview_status=ChemicalInterviewStatusEnum.unselected.value)
        queryset = queryset.filter(
            Q(place_category=BUNDLE_INTEWVIEW_TYPE_ONLINE_1V1, public_attr__start_time__gt=datetime.now()) |
            Q(place_category=BUNDLE_INTEWVIEW_TYPE_ONLINE_1V1, public_attr__start_time__lt=datetime.now(), public_attr__end_time__gt=datetime.now()) |
            Q(place_category=BUNDLE_INTEWVIEW_TYPE_OFFLINE_GROUP, public_attr__start_time__gt=datetime.now()) |
            Q(place_category=BUNDLE_INTEWVIEW_TYPE_OFFLINE_GROUP, public_attr__start_time__lt=datetime.now(), public_attr__end_time__gt=datetime.now()) |
            Q(place_category=BUNDLE_INTEWVIEW_TYPE_ONLINE_1V1, public_attr__end_time__lt=datetime.now(), coachee_record_status=True, coach_record_status=False) |
            Q(place_category=BUNDLE_INTEWVIEW_TYPE_ONLINE_1V1, public_attr__end_time__lt=datetime.now(), coachee_record_status=False, coach_record_status=False)
        )
        queryset = queryset.exclude(public_attr__status=ATTR_STATUS_INTERVIEW_CANCEL)
        # distinct 和 order_by 暂时保持原位，但注意最终可能需要调整顺序或在合并后排序
        queryset = queryset.order_by('is_coach_agree', 'public_attr__start_time')
        queryset = multiple_field_distinct(queryset, ['topic', 'times', 'place', 'place_category',
                                                    'public_attr.start_time', 'public_attr.end_time'])

        # 序列化辅导/访谈数据
        # *** 确保 AppTodoListSerializers 包含了 interview 的主键 id ***
        interview_serializer = AppTodoListSerializers(
            queryset, many=True,
            context={'queryset': queryset, 'user_role': user_role})
        interview_data = interview_serializer.data

        # --- 2.2 成长目标 (Growth Goals) ---
        coach_projects = ProjectCoach.objects.filter(
            coach__user_id=user_id,
            member__isnull=False,
            deleted=False
        ).order_by('-member__created_at') # 不用 .all()

        growth_data_raw = [] # 临时存储原始数据
        for item in coach_projects:
            has_coaching = ProjectInterview.objects.filter(
                place_category=ProjectInterviewPlaceCategoryEnum.online_one_to_one.value,
                public_attr__type=ATTR_TYPE_INTERVIEW,
                type=INTERVIEW_TYPE_COACHING,
                public_attr__project_id=item.project_id,
                public_attr__end_time__lt=datetime.now(),
                deleted=False,
                public_attr__target_user_id=item.member_id
            ).exclude(public_attr__status=ATTR_STATUS_INTERVIEW_CANCEL).exists()

            if has_coaching:
                needs_growth_goal = GrowthGoalsModule.objects.filter(
                    deleted=False, growth_goals__isnull=True,
                    project_bundle__project_id=item.project_id,
                    # 这里需要确认 project_bundle__project_member__user_id 是否正确关联到 item.member_id
                    project_bundle__project_member__user_id=item.member_id, # 假设关联正确
                ).exists()

                if needs_growth_goal:
                    # *** 生成成长目标的唯一标识符 ***
                    identifier = f"growth_goal_member_{item.member_id}_project_{item.project_id}"
                    growth_data_raw.append(
                        {
                            'data_type': DataType.growth_goals, # 来自你的枚举
                            'user_id': item.member.id, # 关联的学员 ID
                            'user_name': item.member.true_name, # 关联的学员姓名
                            # *** 添加 item_type 和 item_identifier ***
                            'item_type': DismissedTodoItem.ItemType.GROWTH_GOAL.value,
                            'item_identifier': identifier,
                        }
                    )

        # --- 2.3 教练任务 (Coach Tasks) ---
        time_now_str = pendulum.now().strftime('%Y-%m-%d %H:%M:%S') # 格式化当前时间
        base_coach_tasks = CoachTask.objects.filter(
            public_attr__user=user, # 使用 user 对象
            public_attr__type=ATTR_TYPE_COACH_TASK,
            coach_submit_time__isnull=True, # 简化写法
            template__write_role__in=[
                InterviewRecordTemplateRoleEnum.coach.value, # 使用 .value
                InterviewRecordTemplateRoleEnum.coach_student.value # 使用 .value
            ],
            deleted=False
        )

        # 处理普通任务和利益相关者任务
        stakeholder_modules = StakeholderInterviewModule.objects.filter(
            deleted=False,
            coach_task__deleted=False,
            coach_task__public_attr__user=user,
            coach_task__coach_submit_time__isnull=True
        )
        stakeholder_coach_task_ids_to_exclude = list(stakeholder_modules.values_list('coach_task_id', flat=True))
        stakeholder_coach_task_ids_to_include = []

        # 过滤掉利益相关者任务，稍后单独处理
        regular_coach_tasks_qs = base_coach_tasks.exclude(id__in=stakeholder_coach_task_ids_to_exclude)

        # 检查哪些利益相关者任务应该出现
        for module in stakeholder_modules:
            all_interviews_done = StakeholderInterview.objects.filter(
                stakeholder_interview_module=module,
                deleted=False
            ).count() == StakeholderInterview.objects.filter(
                stakeholder_interview_module=module,
                deleted=False,
                interview__public_attr__end_time__lt=datetime.now() # 直接用 datetime.now()
            ).count()

            if all_interviews_done:
                stakeholder_coach_task_ids_to_include.append(module.coach_task_id)

        # 获取需要包含的利益相关者任务 QuerySet
        stakeholder_coach_tasks_qs = CoachTask.objects.filter(id__in=stakeholder_coach_task_ids_to_include)

        # 过滤掉辅导时长不足的任务 (这部分逻辑需要仔细检查，特别是 coachee_user_id 的管理)
        # 原始逻辑可能因为 exclude 后再 filter 而出问题，我们尝试先合并再过滤或调整逻辑
        # 这里的过滤逻辑比较复杂，暂时保持，但需要注意其准确性
        valid_coachee_ids_for_regular = []
        raw_regular_tasks = list(regular_coach_tasks_qs) # 获取列表以便迭代和移除
        coachee_ids_to_check = list(set(task.public_attr.target_user_id for task in raw_regular_tasks))

        # 批量查询辅导时长
        coachee_tutoring_times = ProjectInterview.objects.filter(
            public_attr__target_user_id__in=coachee_ids_to_check,
            public_attr__end_time__lt=datetime.now(), # 使用 datetime.now()
            public_attr__type=ATTR_TYPE_INTERVIEW,
            type=ProjectInterviewTypeEnum.formal_interview.value,
            deleted=False
        ).exclude(
            public_attr__status=6 # 假设 6 是取消状态
        ).values(
            'public_attr__target_user_id'
        ).annotate(
            total_tutoring_time=Sum('times')
        )

        # 创建一个字典方便查找
        tutoring_time_map = {
            item['public_attr__target_user_id']: item['total_tutoring_time'] or 0
            for item in coachee_tutoring_times
        }

        # 过滤普通任务
        valid_regular_task_ids = []
        for task in raw_regular_tasks:
            coachee_id = task.public_attr.target_user_id
            coachee_time = tutoring_time_map.get(coachee_id, 0)
            # 假设 task.hours 存储的是所需的小时数
            if task.hours is None or task.hours <= (coachee_time / 60):
                valid_regular_task_ids.append(task.id)

        # 最终合并的任务 QuerySet
        final_coach_tasks_qs = CoachTask.objects.filter(id__in=valid_regular_task_ids) | stakeholder_coach_tasks_qs
        final_coach_tasks_qs = final_coach_tasks_qs.distinct() # 确保去重

        # 序列化教练任务数据
        # *** 确保 AppTodoListCoachTaskViewSerializers 包含了 task 的主键 id ***
        coach_tasks_serializer = AppTodoListCoachTaskViewSerializers(final_coach_tasks_qs, many=True)
        coach_task_data = coach_tasks_serializer.data

        # === 3. 合并所有待办事项并添加标识符 ===
        all_items = []

        # 处理辅导/访谈数据
        for item in interview_data:
            identifier = f"interview_{item.get('id')}_{item.get('status_text')['status']}" # 假设序列化器输出的主键字段是 'id'
            if item.get('id') is not None: # 确保有 ID
                item['item_type'] = DismissedTodoItem.ItemType.INTERVIEW.value
                item['item_identifier'] = identifier
                all_items.append(item)

        # 处理成长目标数据 (已在上面添加了标识符)
        all_items.extend(growth_data_raw)

        # 处理教练任务数据
        for item in coach_task_data:
            identifier = f"task_{item.get('id')}" # 假设序列化器输出的主键字段是 'id'
            if item.get('id') is not None: # 确保有 ID
                item['item_type'] = DismissedTodoItem.ItemType.COACH_TASK.value
                item['item_identifier'] = identifier
                all_items.append(item)

        # === 4. 过滤掉已设置“不再提醒”的项 ===
        final_items_to_show = [
            item for item in all_items
            if item.get('item_identifier') not in dismissed_identifiers
        ]

        # === 5. 分页和返回 ===
        # 可选：在这里对 final_items_to_show 进行最终排序，如果需要的话
        # 例如：final_items_to_show.sort(key=lambda x: x.get('some_common_sort_field', default_value))

        paginator = StandardResultsSetPagination()
        page_list = paginator.paginate_queryset(final_items_to_show, self.request) # 对过滤后的列表分页
        response_data = paginator.get_paginated_response(page_list) # 获取分页响应数据

        return success_response(response_data) # 使用你的成功响应函数


    @swagger_auto_schema(
        operation_id='app教练端客户详情约谈列表',
        operation_summary='app教练端客户详情约谈列表',
        manual_parameters=[
            openapi.Parameter('coachee_user_id', openapi.IN_QUERY, description='被教练者id', type=openapi.TYPE_NUMBER,
                              required=True),
            openapi.Parameter('page', openapi.IN_QUERY, description='第几页', type=openapi.TYPE_NUMBER,
                              required=False),
            openapi.Parameter('page_size', openapi.IN_QUERY, description='每页显示条数默认15条', type=openapi.TYPE_NUMBER,
                              required=False),
        ],
        tags=['app约谈相关']
    )
    @action(methods=['get'], detail=False, url_path='customer_interview_list')
    def customer_interview_list(self, request, *args, **kwargs):
        try:
            coachee_user_id = int(request.query_params.get('coachee_user_id', 0))
            user_role = int(request.headers.get('role', 0))
            mp = json.loads(request.headers.get('mp')) if request.headers.get('mp') else None
        except:
            return parameter_error_response()
        user = request.user
        if not user:
            return parameter_error_response('未获取到教练信息')
        queryset = user.get_miniapp_interview(user_role, coachee_user_id)

        # 排除未被选中的化学面谈
        queryset = queryset.exclude(
            chemical_interview__chemical_interview_status=ChemicalInterviewStatusEnum.unselected.value)

        if mp and compare_version(mp.get('version'), '2.16') < 0:
            queryset = queryset.exclude(type=ProjectInterviewTypeEnum.chemical_interview)
        if mp and compare_version(mp.get('version'), '2.16.2') < 0:
            queryset = queryset.exclude(type=ProjectInterviewTypeEnum.stakeholder_interview)
        if mp and compare_version(mp.get('version'), '2.20') < 0:
            queryset = queryset.exclude(
                coach_group_module__project_group_coach__type=GroupCoachTypeEnum.group_tutoring).distinct()

        paginator = StandardResultsSetPagination()
        # B端教练，C端教练，返回未完成的订单
        if user_role in [UserRoleEnum.coach.value, UserRoleEnum.trainee_coach.value] and mp and compare_version(
                mp.get('version'), '2.32') >= 0:
            interview_serializer = AppInterviewListSerializers(
                queryset, many=True,
                context={'queryset': queryset, 'user_role': user_role}).data
            # 获取未完成的订单数据
            available_order = order_public.get_user_available_order(coachee_user_id=coachee_user_id, coach_user_id=user.id)
            available_order = AppInterviewListOrderSerializer(available_order, many=True).data
            # 组合数据
            data = paginator.paginate_queryset([*available_order, *interview_serializer], self.request)
        else:

            page_list = paginator.paginate_queryset(queryset, self.request)
            serializer = AppInterviewListSerializers(
                page_list, many=True,
                context={'queryset': queryset, 'user_role': user_role})
            data = serializer.data
        response = paginator.get_paginated_response(data)
        return success_response(response)

    @swagger_auto_schema(
        operation_id='app约谈列表',
        operation_summary='app约谈列表',
        manual_parameters=[
            openapi.Parameter('user_id', openapi.IN_QUERY, description='用户id', type=openapi.TYPE_NUMBER,
                              required=True),
            openapi.Parameter('status', openapi.IN_QUERY, description='1:进行中 2:已结束',
                              type=openapi.TYPE_NUMBER, required=False),
            openapi.Parameter('page', openapi.IN_QUERY, description='第几页', type=openapi.TYPE_NUMBER,
                              required=False),
            openapi.Parameter('page_size', openapi.IN_QUERY, description='每页显示条数默认15条', type=openapi.TYPE_NUMBER,
                              required=False),
        ],
        tags=['app约谈相关']
    )
    @action(methods=['get'], detail=False)
    def interview_list(self, request, *args, **kwargs):
        try:
            user_id = int(request.query_params.get('user_id', 0))
            status = int(request.query_params.get('status', 0))  # 默认0，查询全部辅导
            User.objects.get(pk=user_id)
        except (TypeError, ValueError):
            return parameter_error_response('请求参数错误')
        except Exception as e:
            return parameter_error_response('用户角色错误')
        try:
            user_role = int(request.headers.get('role'))
        except:
            raise exceptions.AuthenticationFailed({'retCode': 401, 'retMsg': "登录过期 | Fail", 'err': '登录过期'})

        queryset = interview_public.get_miniapp_interview_list(user_id, status, user_role)
        
        args = {}
        # 根据用户身份返回对应数据
        if user_role in [UserRoleEnum.coach, UserRoleEnum.trainee_coach]:
            args.setdefault('coach_record_status', True)
            # 排除未被选中的化学面谈
            queryset = queryset.exclude(
                chemical_interview__chemical_interview_status=ChemicalInterviewStatusEnum.unselected.value)
        else:
            args.setdefault('coachee_record_status', True)


        # --- Start: Pagination Logic ---
        paginator = StandardResultsSetPagination() # Or your specific pagination class
        try:
            page_number = int(request.query_params.get(paginator.page_query_param, 1))
            if page_number <= 0:
                page_number = 1
        except ValueError:
            page_number = 1
        page_size = paginator.get_page_size(request)
    
        # 如果是C端被教练者，并且状态是全部辅导，读取所有辅导数据后，拼接订单数据，再进行分页
        # 版本得大于2.29
        if user_role == UserRoleEnum.trainee_coachee.value and not status:
            # interview_serializer = AppInterviewListSerializers(
            #     queryset, many=True,
            #     context={'queryset': queryset, 'user_role': user_role}).data

            # # 获取未完成的订单数据
            # pay_order = order_public.get_user_available_order(coachee_user_id=user_id)
            # pay_order_data = AppInterviewListOrderSerializer(pay_order, many=True).data

            # # 获取已退款的订单数据
            # refund_order = order_public.get_user_refund_order(user_id)
            # refund_order_data = AppInterviewListOrderSerializer(refund_order, many=True).data

            # # 组合数据
            # data = paginator.paginate_queryset([*pay_order_data, *interview_serializer, *refund_order_data], self.request)

            # 1. Get QuerySets & Counts
            pay_order_qs = order_public.get_user_available_order(coachee_user_id=user_id)
            interview_qs = queryset # Use already filtered/ordered queryset
            refund_order_qs = order_public.get_user_refund_order(user_id)

            pay_order_count = pay_order_qs.count()
            interview_count = interview_qs.count()
            refund_order_count = refund_order_qs.count()
            total_items = pay_order_count + interview_count + refund_order_count

            # 2. Determine Page Slice
            offset = (page_number - 1) * page_size
            limit = page_size

            # 3. Fetch Data for the Current Page
            pay_orders_for_page = []
            interviews_for_page = []
            refund_orders_for_page = []
            limit_remaining = limit
            current_offset_marker = offset # Tracks the starting point for the current source relative to the combined list

            # Fetch Available Orders slice
            pay_orders_in_slice_count = min(limit_remaining, max(0, pay_order_count - current_offset_marker))
            if pay_orders_in_slice_count > 0:
                pay_orders_for_page = list(pay_order_qs[current_offset_marker : current_offset_marker + pay_orders_in_slice_count])
                limit_remaining -= len(pay_orders_for_page)
            current_offset_marker += pay_orders_in_slice_count # Adjust marker past these orders

            # Fetch Interviews slice
            if limit_remaining > 0:
                interview_offset_local = max(0, current_offset_marker - pay_order_count) # Offset within interviews
                interviews_in_slice_count = min(limit_remaining, max(0, interview_count - interview_offset_local))
                if interviews_in_slice_count > 0:
                    interviews_for_page = list(interview_qs[interview_offset_local : interview_offset_local + interviews_in_slice_count])
                    limit_remaining -= len(interviews_for_page)
                current_offset_marker += interviews_in_slice_count # Adjust marker past these interviews

            # Fetch Refunded Orders slice
            if limit_remaining > 0:
                refund_offset_local = max(0, current_offset_marker - pay_order_count - interview_count) # Offset within refunds
                refunds_in_slice_count = min(limit_remaining, max(0, refund_order_count - refund_offset_local))
                if refunds_in_slice_count > 0:
                    refund_orders_for_page = list(refund_order_qs[refund_offset_local : refund_offset_local + refunds_in_slice_count])
                    # No need to adjust limit_remaining further

            # 4. Serialize and Combine
            pay_order_data = AppInterviewListOrderSerializer(pay_orders_for_page, many=True).data
            interview_data = AppInterviewListSerializers(
                interviews_for_page, many=True,
                context={'queryset': interview_qs, 'user_role': user_role} # Pass context
            ).data
            refund_data = AppInterviewListOrderSerializer(refund_orders_for_page, many=True).data
            results = [*pay_order_data, *interview_data, *refund_data] # Combine in correct order

            return success_response(results, request, {'count': total_items}) # Adjust 'meta=' key if needed

        # B端教练，C端教练，返回未完成的
        elif user_role in [UserRoleEnum.coach.value, UserRoleEnum.trainee_coach.value] and not status:
            # interview_serializer = AppInterviewListSerializers(
            #     queryset, many=True,
            #     context={'queryset': queryset, 'user_role': user_role}).data
            # # 获取未完成的订单数据
            # available_order = order_public.get_user_available_order(coach_user_id=user_id)
            # available_order = AppInterviewListOrderSerializer(available_order, many=True).data
            # # 组合数据
            # data = paginator.paginate_queryset([*available_order, *interview_serializer], self.request)
            
            # 1. Get QuerySets & Counts
            order_queryset = order_public.get_user_available_order(coach_user_id=user_id)

            # Use the already filtered and ordered interview queryset
            interview_queryset = queryset

            order_count = order_queryset.count()
            interview_count = interview_queryset.count() # Use the count from the already filtered queryset
            total_items = order_count + interview_count

            # 2. Determine Page Slice
            offset = (page_number - 1) * page_size
            limit = page_size

            # 3. Fetch Data for the Current Page
            orders_for_page = []
            interviews_for_page = []

            # Fetch Orders slice
            orders_in_slice_count = min(limit, max(0, order_count - offset))
            if orders_in_slice_count > 0:
                orders_for_page = list(order_queryset[offset : offset + orders_in_slice_count])

            # Fetch Interviews slice
            interviews_needed = limit - len(orders_for_page)
            if interviews_needed > 0:
                interview_offset = max(0, offset + len(orders_for_page) - order_count)
                interviews_for_page = list(interview_queryset[interview_offset : interview_offset + interviews_needed])

            # 4. Serialize and Combine
            order_data = AppInterviewListOrderSerializer(orders_for_page, many=True).data
            interview_data = AppInterviewListSerializers(
                interviews_for_page, many=True,
                context={'queryset': interview_queryset, 'user_role': user_role} # Pass context if serializer needs it
            ).data
            results = [*order_data, *interview_data]

            
            # Assuming success_response expects pagination dict and meta dict separately
            return success_response(results, request, {'count': total_items}) # Adjust 'meta=' key if needed

        # 无订单数据，分页后再序列化辅导数据
        else:
            page_list = paginator.paginate_queryset(queryset, self.request)
            data = AppInterviewListSerializers(
                page_list, many=True,
                context={'queryset': queryset, 'user_role': user_role}).data
        response = paginator.get_paginated_response(data)
        return success_response(response, request)

    @swagger_auto_schema(
        operation_id='app约谈列表剩余时长',
        operation_summary='app约谈列表剩余时长',
        manual_parameters=[
            openapi.Parameter('user_id', openapi.IN_QUERY, description='用户id', type=openapi.TYPE_NUMBER,
                              required=True),

        ],
        tags=['app约谈相关']
    )
    @action(methods=['get'], detail=False, url_path='surplus_time')
    def surplus_time(self, request, *args, **kwargs):
        try:
            user_id = int(request.query_params.get('user_id', 0))
            project_member = ProjectMember.objects.filter(user_id=user_id, deleted=False).first()
        except:
            return parameter_error_response()

        online = OneToOneCoach.objects.filter(project_bundle__project_member=project_member,
                                              type=CoachTypeEnum.online.value, deleted=False)
        if not online.exists():
            return success_response({'msg': None})
        else:
            used_times = ProjectInterview.objects.filter(
                place_category=1, type=1, public_attr__project=project_member.project,
                public_attr__target_user=project_member.user, deleted=False,
                public_attr__type=ATTR_TYPE_INTERVIEW).exclude(public_attr__status=ATTR_STATUS_INTERVIEW_CANCEL).aggregate(used_times=Sum('times'))
            used_times = used_times.get('used_times', 0) if used_times.get('used_times', 0) else 0
            used_times = round(used_times / 60, 1)

            all_times = online.aggregate(used_times=Sum('online_time'))
            all_times = all_times.get('used_times', 0) if all_times.get('used_times', 0) else 0
            surplus_time = all_times - used_times if all_times > used_times else 0
            if surplus_time:
                text = f'剩余可预约时长：{surplus_time}小时'
                return success_response({'msg': text})
            # 如果没有就不展示
            else:
                return success_response()

    @swagger_auto_schema(
        operation_id='辅导开始检查/接收用户加入状态接口',
        operation_summary='辅导开始检查/接收用户加入状态接口',
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'interview_id': openapi.Schema(type=openapi.TYPE_NUMBER, description='辅导id'),
                'user_id': openapi.Schema(type=openapi.TYPE_NUMBER, description='用户id'),
            }
        ),
        tags=['后台化学面谈相关']
    )
    @action(methods=['post'], detail=False, url_path='checkin')
    def interview_checkin(self, request, *args, **kwargs):
        try:
            interview_id = request.data['interview_id']
            user_id = request.data['user_id']
        except KeyError:
            return parameter_error_response()
        if interview_redis.get(f'interview_start_{interview_id}'):
            data = literal_eval(interview_redis.get(f'interview_start_{interview_id}').decode())
            if user_id not in data:
                data.append(user_id)
                interview_redis.set(f'interview_start_{interview_id}', str(data), ex=3600)
        else:
            interview_redis.set(f'interview_start_{interview_id}', str([user_id]), ex=3600)
        return success_response()

    @swagger_auto_schema(
        operation_id='获取教练和客户的观察反思',
        operation_summary='教练和客户的观察反思',
        manual_parameters=[
            openapi.Parameter('coachee_user_id', openapi.IN_QUERY, description='被教练者用户id', type=openapi.TYPE_NUMBER,
                              required=True),
            openapi.Parameter('coach_user_id', openapi.IN_QUERY, description='教练用户id', type=openapi.TYPE_NUMBER,
                              required=True),
        ],
        tags=['app约谈记录相关']
    )
    @action(methods=['get'], detail=False, url_path='thinking_and_reflection')
    def get_interview_thinking_and_reflection(self, request, *args, **kwargs):
        try:
            coachee_user_id = request.query_params.get('coachee_user_id')
            coach_user_id = request.query_params.get('coach_user_id')
            User.objects.get(pk=coachee_user_id, deleted=False)
            User.objects.get(pk=coach_user_id, deleted=False)
        except User.DoesNotExist:
            return parameter_error_response('用户不存在')

        interview = ProjectInterview.objects.filter(
            Q(coach_record_status=True) | Q(coachee_record_status=True),
            public_attr__user_id=coach_user_id, public_attr__target_user_id=coachee_user_id,
            type=ProjectInterviewTypeEnum.formal_interview.value
        ).exclude(public_attr__status=ATTR_STATUS_INTERVIEW_CANCEL).order_by(
            '-public_attr__start_time').all()
        data = interview_public.get_interview_thinking_and_reflection(interview)

        # 分页处理
        paginator = StandardResultsSetPagination()
        page_list = paginator.paginate_queryset(data, self.request)
        response = paginator.get_paginated_response(page_list)
        return success_response(response, request)


class AppInterviewRecordTemplateViewSet(GenericViewSet):
    queryset = ProjectInterview.objects.filter(deleted=False).all().exclude(public_attr__status=ATTR_STATUS_INTERVIEW_CANCEL).order_by(
        '-public_attr__start_time')
    serializer_class = AppInterviewDetailSerializers

    @swagger_auto_schema(
        operation_id='集体辅导记录详情',
        operation_summary='集体辅导记录详情',
        manual_parameters=[
            openapi.Parameter('interview_id', openapi.IN_QUERY, description='约谈id', type=openapi.TYPE_NUMBER,
                              required=True),
        ],
        tags=['app约谈记录填写相关']
    )
    @action(methods=['get'], detail=False, url_path='interview_detail')
    def group_coach_interview_detail(self, request, *args, **kwargs):
        try:
            project_interview_id = request.query_params.get('interview_id')
            project_interview = ProjectInterview.objects.get(pk=project_interview_id)
            role = int(request.headers.get('role', 0))
        except ProjectInterview.DoesNotExist:
            return parameter_error_response('辅导不存在')
        if project_interview.place_category == ProjectInterviewPlaceCategoryEnum.offline_group_coach.value:
            serializer = InterviewRecordQuestionAnswerEditDetailSerializer(project_interview, context={'role': role})
            data = serializer.data
            return success_response(data)
        else:
            serializers = InterviewRecordQuestionAnswerEditDetailTraineeCoachSerializer(project_interview,
                                                                                        context={'role': role})
            data = serializers.data
            return success_response(data)

    @swagger_auto_schema(
        operation_id='问卷形式一对一辅导报告详情',
        operation_summary='问卷形式一对一辅导报告详情',
        manual_parameters=[
            openapi.Parameter('interview_id', openapi.IN_QUERY, description='约谈id', type=openapi.TYPE_NUMBER,
                              required=True),
        ],
        tags=['app约谈记录填写相关']
    )
    @action(methods=['get'], detail=False, url_path='report_interview_detail')
    def report_interview_detail(self, request, *args, **kwargs):
        try:
            project_interview_id = request.query_params.get('interview_id')
            project_interview = ProjectInterview.objects.get(pk=project_interview_id)
            role = int(request.headers.get('role', 0))
            mp = json.loads(request.headers.get('mp')) if request.headers.get('mp') else None
        except ProjectInterview.DoesNotExist:
            return parameter_error_response('辅导不存在')
        if project_interview.place_category == ProjectInterviewPlaceCategoryEnum.online_one_to_one.value:
            if project_interview.type == ProjectInterviewTypeEnum.formal_interview:
                serializers = InterviewRecordReportDetailSerializer(
                    project_interview, context={'role': role, "mp": mp})
                data = serializers.data
                return success_response(data)
            elif project_interview.type in [ProjectInterviewTypeEnum.chemical_interview,
                                            ProjectInterviewTypeEnum.stakeholder_interview]:
                serializers = InterviewRecordQuestionAnswerEditDetailTraineeCoachSerializer(project_interview,
                                                                                            context={'role': role})
                data = serializers.data
                return success_response(data)
        return success_response()

    @swagger_auto_schema(
        operation_id='app编辑添加辅导记录模版问题',
        operation_summary='app编辑添加辅导记录模版问题',
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'interview_id': openapi.Schema(type=openapi.TYPE_NUMBER, description='约谈id'),
                'question_detail': openapi.Schema(type=openapi.TYPE_ARRAY, items=openapi.TYPE_OBJECT,
                                                  description='回答问题详情'),
            }
        ),
        tags=['app约谈记录填写相关']
    )
    @action(methods=['post'], detail=False, url_path='edit_answer_interview_record_template_question')
    def edit_answer_interview_record_template_question(self, request, *args, **kwargs):
        try:
            project_interview_id = request.data['interview_id']
            project_interview = ProjectInterview.objects.get(pk=project_interview_id)
            question_detail = request.data['question_detail']
            is_single = request.data.get('is_single', 0)
            role = int(request.headers.get('role'))
            user = project_interview.public_attr.target_user
        except Exception as e:
            return parameter_error_response(str(e))
        err = check_answer_data(question_detail, project_interview_id, is_single)
        if err:
            return success_response({'status': 1, 'msg': err})
        user_id = user.pk if role in [UserRoleEnum.coachee.value,
                                      UserRoleEnum.trainee_coachee.value] else project_interview.public_attr.user_id

        project_id = project_interview.public_attr.project_id
        # project_member = ProjectMember.objects.filter(user_id=request.user.pk,
        #                                               project=project_interview.public_attr.project).first()
        # user_role = project_member.role if project_member else None
        required_list = list(set([question['id'] for question in question_detail if question['required']]))
        question_list = []
        user_score = {}
        with transaction.atomic():
            for question in question_detail:
                # 填空题判断
                if question['type'] == InterviewRecordTemplateQuestionTypeEnum.blank.value:

                    # 小程序传参，进行数据处理时，指定题目id转成对应类型处理。
                    # 版本兼容处理，详情查看question_id_to_answer_type方法
                    if 'answer' in question.keys() and question['answer']:
                            answer_type = interview_public.question_id_to_answer_type(question['id'])
                            if answer_type:
                                question['answer_type'] = answer_type
                    # 普通文本框
                    if question['answer_type'] == InterviewRecordTemplateQuestionAnswerTypeEnum.normal_text.value:
                        # edit : {"id":1, "content" : "xxx"}    add: {"content": "xxx"}
                        if 'answer' in question.keys() and question['answer']:
                            if 'id' in question['answer'].keys():
                                InterviewRecordTemplateAnswer.objects.filter(pk=question['answer']['id']).update(
                                    answer=question['answer']['content'])
                            else:
                                InterviewRecordTemplateAnswer.objects.create(
                                    interview_id=project_interview_id, public_attr=project_interview.public_attr,
                                    question_id=question['id'], answer=question['answer']['content'])
                            if question['required']:
                                question_list.append(question['id'])

                    # 能力标签
                    if question['answer_type'] == InterviewRecordTemplateQuestionAnswerTypeEnum.power_tag.value:
                        # edit : {"id":1, "content" : "xxx"}
                        if 'answer' in question.keys() and question['answer']:
                            if 'id' in question['answer'].keys():
                                InterviewRecordTemplateAnswer.objects.filter(
                                    pk=question['answer']['id']).update(
                                    answer=str(question['answer']['content']))
                            else:
                                InterviewRecordTemplateAnswer.objects.create(
                                    interview_id=project_interview_id,
                                    public_attr=project_interview.public_attr,
                                    question_id=question['id'], answer=str(question['answer']['content']))
                            if question['required']:
                                question_list.append(question['id'])

                    # 行动计划
                    if question['answer_type'] == InterviewRecordTemplateQuestionAnswerTypeEnum.action_plan.value:
                        # 根据传入id 和 现有问题 判断是否又删除的行动计划
                        if 'answer' in question.keys():
                            now_answer_ids = [i['id'] for i in question['answer'] if 'id' in i.keys()]
                            exists_answer_ids = list(InterviewRecordTemplateAnswer.objects.filter(
                                interview=project_interview, question_id=question['id'],
                                question__type=InterviewRecordTemplateQuestionTypeEnum.blank.value,
                                question__answer_type=InterviewRecordTemplateQuestionAnswerTypeEnum.action_plan.value).\
                                values_list('id', flat=True))
                            deleted_list = list(set(exists_answer_ids).difference(set(now_answer_ids)))
                            if deleted_list:
                                del_action_plan_lst = list(QuestionObjectRelationship.objects.filter(
                                    answer_id__in=deleted_list, deleted=False).values_list('obj_id', flat=True))
                                ActionPlan.objects.filter(id__in=del_action_plan_lst).delete()
                                QuestionObjectRelationship.objects.filter(answer_id__in=deleted_list).delete()
                                InterviewRecordTemplateAnswer.objects.filter(id__in=deleted_list).delete()

                        if 'answer' in question.keys() and question['answer']:
                            for answer_f in question['answer']:
                                start_date, error = action_plan_public.get_default_start_date(answer_f)
                                if error:
                                    return parameter_error_response(error)

                                if 'id' in answer_f.keys():
                                    answer = InterviewRecordTemplateAnswer.objects.get(pk=answer_f['id'])
                                    relationship = answer.object_relationship.filter(
                                        type=ObjectTypeEnum.action_plan.value, deleted=False).first()
                                    obj_id = relationship.obj_id
                                    ActionPlan.objects.filter(pk=obj_id).update(content=answer_f['content'],
                                                                                end_date=answer_f['end_date'],
                                                                                start_date=start_date)

                                else:
                                    answer = InterviewRecordTemplateAnswer.objects.create(
                                        interview_id=project_interview_id, public_attr=project_interview.public_attr,
                                        question_id=question['id'])
                                    public_attr = save_public_attr(project=project_interview.public_attr.project,
                                                                   user=user,
                                                                   save_type=ATTR_TYPE_HABIT)

                                    action_plan = ActionPlan.objects.create(
                                        interview=project_interview, public_attr=public_attr,
                                        content=answer_f['content'], end_date=answer_f['end_date'],
                                        start_date=start_date,
                                        creator_role=6 if role in [UserRoleEnum.coachee.value,
                                                                   UserRoleEnum.trainee_coachee.value] else 4)

                                    QuestionObjectRelationship.objects.create(
                                        type=ObjectTypeEnum.action_plan.value, obj_id=action_plan.pk, answer=answer)
                            if question['required']:
                                question_list.append(question['id'])

                    # 习惯养成
                    if question['answer_type'] == InterviewRecordTemplateQuestionAnswerTypeEnum.habit_formation.value:
                        # 根据传入id 和 现有问题 判断是否又删除的行动计划
                        if 'answer' in question.keys():
                            now_answer_ids = [i['id'] for i in question['answer'] if 'id' in i.keys()]
                            exists_answer_ids = list(InterviewRecordTemplateAnswer.objects.filter(
                                interview=project_interview, question_id=question['id'],
                                question__type=InterviewRecordTemplateQuestionTypeEnum.blank.value,
                                question__answer_type=InterviewRecordTemplateQuestionAnswerTypeEnum.habit_formation.value).\
                                                     values_list('id', flat=True))
                            deleted_list = list(set(exists_answer_ids).difference(set(now_answer_ids)))
                            if deleted_list:
                                del_habit_lst = list(QuestionObjectRelationship.objects.filter(
                                    answer_id__in=deleted_list, deleted=False).values_list('obj_id', flat=True))
                                Habit.objects.filter(id__in=del_habit_lst).delete()
                                QuestionObjectRelationship.objects.filter(answer_id__in=deleted_list).delete()
                                InterviewRecordTemplateAnswer.objects.filter(id__in=deleted_list).delete()

                        if 'answer' in question.keys() and question['answer']:
                            for answer_f in question['answer']:
                                if 'id' in answer_f.keys():
                                    answer = InterviewRecordTemplateAnswer.objects.get(pk=answer_f['id'])
                                    relationship = answer.object_relationship.filter(
                                        type=ObjectTypeEnum.habit.value, deleted=False).first()
                                    obj_id = relationship.obj_id
                                    Habit.objects.filter(pk=obj_id).update(
                                        when=answer_f['when'], stop=answer_f['stop'],
                                        change=answer_f['change'], start_date=answer_f['start_date'],
                                        end_date=answer_f['end_date']
                                    )
                                else:
                                    answer = InterviewRecordTemplateAnswer.objects.create(
                                        interview_id=project_interview_id, public_attr=project_interview.public_attr,
                                        question_id=question['id'])
                                    public_attr = save_public_attr(project=project_interview.public_attr.project,
                                                                   user=user,
                                                                   save_type=ATTR_TYPE_HABIT)

                                    habit = Habit.objects.create(
                                        interview=project_interview, public_attr=public_attr,
                                        stop=answer_f['stop'], when=answer_f['when'],
                                        change=answer_f['change'], start_date=answer_f['start_date'],
                                        end_date=answer_f['end_date'], creator_role=6 if
                                        role in [UserRoleEnum.coachee.value, UserRoleEnum.trainee_coachee.value] else 4
                                    )
                                    QuestionObjectRelationship.objects.create(
                                        type=ObjectTypeEnum.habit.value, obj_id=habit.pk, answer=answer)
                            if question['required']:
                                question_list.append(question['id'])

                    # 成长笔记
                    if question['answer_type'] == InterviewRecordTemplateQuestionAnswerTypeEnum.growth_notes.value:
                        if 'answer' in question.keys() and question['answer']:
                            # {"id": answer.id, "content": diary.content}
                            if 'id' in question['answer'].keys():
                                answer = InterviewRecordTemplateAnswer.objects.get(pk=question['answer']['id'])
                                relationship = answer.object_relationship.filter(
                                    type=ObjectTypeEnum.diary.value, deleted=False).first()
                                obj_id = relationship.obj_id
                                Diary.objects.filter(pk=obj_id).update(content=question['answer']['content'])
                            else:
                                answer = InterviewRecordTemplateAnswer.objects.create(
                                    interview_id=project_interview_id, public_attr=project_interview.public_attr,
                                    question_id=question['id'])

                                # 填写人是教练，成长笔记使用public_attr.user
                                # 填写人是客户，成长笔记使用public_attr.target_user
                                if role in [UserRoleEnum.coach.value, UserRoleEnum.trainee_coach.value]:
                                    diary_attr_user = project_interview.public_attr.user
                                else:
                                    diary_attr_user = project_interview.public_attr.target_user

                                public_attr = save_public_attr(project=project_interview.public_attr.project,
                                                               user=diary_attr_user,
                                                               save_type=ATTR_TYPE_HABIT)
                                diary = Diary.objects.create(
                                    interview=project_interview, public_attr=public_attr,
                                    content=question['answer']['content'], type=2,
                                    creator_role=6 if role in [UserRoleEnum.coachee.value,
                                                               UserRoleEnum.trainee_coachee.value] else 4
                                )
                                QuestionObjectRelationship.objects.create(
                                    type=ObjectTypeEnum.diary.value, obj_id=diary.pk, answer=answer)
                            if question['required']:
                                question_list.append(question['id'])

                    # 项目信息-项目笔记
                    if question['answer_type'] == InterviewRecordTemplateQuestionAnswerTypeEnum.project_info.value:
                        if 'answer' in question.keys() and question['answer']:

                            content = question['answer']['content']

                            # 有id标识更新，没有id标识创建
                            if 'id' in question['answer'].keys():
                                answer_obj = InterviewRecordTemplateAnswer.objects.get(pk=question['answer']['id'])
                                answer_obj.answer = content
                                answer_obj.save()
                                relationship = answer_obj.object_relationship.filter(
                                    type=ObjectTypeEnum.project_note.value, deleted=False).first()
                                obj_id = relationship.obj_id
                                ProjectNote.objects.filter(pk=obj_id).update(content=content)
                            else:
                                # 创建项目笔记
                                project_note = interview_public.create_or_update_interview_project_note(
                                    project_interview, content)
                                # 创建回答记录
                                answer = InterviewRecordTemplateAnswer.objects.create(
                                    interview_id=project_interview_id, public_attr=project_interview.public_attr,
                                    question_id=question['id'], answer=content)
                                # 创建关联表数据
                                QuestionObjectRelationship.objects.create(
                                    type=ObjectTypeEnum.project_note.value, obj_id=project_note.pk, answer=answer)

                            if question['required']:
                                question_list.append(question['id'])

                    # 企业信息-企业背景信息
                    if question['answer_type'] == InterviewRecordTemplateQuestionAnswerTypeEnum.company_info.value:
                        if 'answer' in question.keys() and question['answer']:

                            content = question['answer']['content']

                            # 有id标识更新，没有id标识创建
                            if 'id' in question['answer'].keys():
                                InterviewRecordTemplateAnswer.objects.filter(pk=question['answer']['id']).update(
                                    answer=content)
                            else:
                                InterviewRecordTemplateAnswer.objects.create(
                                    interview_id=project_interview_id, public_attr=project_interview.public_attr,
                                    question_id=question['id'], answer=content)

                            # 企业背景信息同一个教练对同一个企业只有一条数据，每次新建/更新对已有数据末尾进行追加更新的操作
                            if project_id:
                                project = Project.objects.filter(pk=project_id, deleted=False).first()
                                if project and project.company_id:
                                    coach_associated_company, is_create = CoachAssociatedCompany.objects.get_or_create(
                                        coach_user_id=project_interview.public_attr.user_id,
                                        company_id=project.company_id, deleted=False)

                                    if is_create:
                                        coach_associated_company.other_background_information = content
                                    else:
                                        if coach_associated_company.other_background_information:
                                            coach_associated_company.other_background_information += f'\n{content}'
                                        else:
                                            coach_associated_company.other_background_information = content
                                    coach_associated_company.save()
                            if question['required']:
                                question_list.append(question['id'])

                    # 客户信息-客户背景信息
                    if question['answer_type'] == InterviewRecordTemplateQuestionAnswerTypeEnum.customer_info.value:
                        if 'answer' in question.keys() and question['answer']:
                            content = question['answer']['content']
                            
                            # 有id标识更新，没有id标识创建
                            if 'id' in question['answer'].keys():
                                InterviewRecordTemplateAnswer.objects.filter(pk=question['answer']['id']).update(
                                    answer=question['answer']['content'])
                            else:
                                InterviewRecordTemplateAnswer.objects.create(
                                    interview_id=project_interview_id, public_attr=project_interview.public_attr,
                                    question_id=question['id'], answer=question['answer']['content'])

                            # 如果是利益相关者访谈编辑，数据应该更新到用户的背景信息，不能直接使用public_attr.target_user_id
                            if project_interview.type == ProjectInterviewTypeEnum.stakeholder_interview.value:
                                stakeholder_interview = StakeholderInterview.objects.filter(
                                    interview=project_interview, deleted=False).first()
                                if stakeholder_interview:
                                    coachee_user_id = stakeholder_interview.project_interested.master_id
                                else:
                                    coachee_user_id = None
                            else:
                                coachee_user_id = project_interview.public_attr.target_user_id

                            # 客户背景信息同一个教练对同一个客户只有一条数据，每次新建/更新对已有数据末尾进行追加更新的操作
                            customer_portrait, is_create = CustomerPortrait.objects.get_or_create(
                                user_id=coachee_user_id, coach_id=project_interview.public_attr.user_id,
                                project_id__isnull=True, deleted=False)
                            if is_create:
                                customer_portrait.coach_extra = content
                            else:
                                customer_portrait.coach_extra += f'\n{content}'
                            customer_portrait.save()
                            if question['required']:
                                question_list.append(question['id'])

                # 单选
                if question['type'] == InterviewRecordTemplateQuestionTypeEnum.single.value:
                    # option : [{'id':1, 'option_custom': 'xxx'}]
                    if 'answer' in question.keys() and question['answer']:
                        # {'option': answer.option_id, 'option_custom': answer.option_custom}

                        answer = InterviewRecordTemplateAnswer.objects.filter(
                            question__type=InterviewRecordTemplateQuestionTypeEnum.single.value,
                            question_id=question['id'],
                            interview_id=project_interview_id, public_attr=project_interview.public_attr).first()
                        if answer:
                            if answer.option_id != question['answer'][0]['option']:
                                answer.option_id = question['answer'][0]['option']
                            if 'option_custom' in question['answer'][0] and \
                                    question['answer'][0]['option_custom']:
                                answer.option_custom = question['answer'][0]['option_custom']
                            answer.save()
                        else:
                            if 'option_custom' in question['answer'][0] and \
                                    question['answer'][0]['option_custom']:
                                InterviewRecordTemplateAnswer.objects.create(
                                    interview_id=project_interview_id, public_attr=project_interview.public_attr,
                                    question_id=question['id'], option_id=question['answer'][0]['option'],
                                    option_custom=question['answer'][0]['option_custom']
                                    )
                            else:
                                InterviewRecordTemplateAnswer.objects.create(
                                    interview_id=project_interview_id, public_attr=project_interview.public_attr,
                                    question_id=question['id'], option_id=question['answer'][0]['option'])
                        if question['required']:
                            question_list.append(question['id'])
                # 多选
                if question['type'] == InterviewRecordTemplateQuestionTypeEnum.multiple.value:
                    if 'answer' in question.keys() and question['answer']:
                        exists_option_ids = [answer.option_id for answer in InterviewRecordTemplateAnswer.objects.
                            filter(interview_id=project_interview_id, public_attr=project_interview.public_attr,
                                   question__type=InterviewRecordTemplateQuestionTypeEnum.multiple.value,
                                   question_id=question['id'])]

                        now_option_ids = [option['option'] for option in question['answer']]

                        deleted_list = list(set(exists_option_ids).difference(set(now_option_ids)))

                        if deleted_list:
                            InterviewRecordTemplateAnswer.objects.filter(
                                interview_id=project_interview_id, public_attr=project_interview.public_attr,
                                question__type=InterviewRecordTemplateQuestionTypeEnum.multiple.value,
                                question_id=question['id'], option_id__in=deleted_list).delete()
                        if question['answer']:
                            for option in question['answer']:
                                answer = InterviewRecordTemplateAnswer.objects.filter(
                                    interview_id=project_interview_id, public_attr=project_interview.public_attr,
                                    option_id=option['option'], question_id=question['id'],
                                    question__type=InterviewRecordTemplateQuestionTypeEnum.multiple.value).first()
                                if answer:
                                    if 'option_custom' in option and option['option_custom']:
                                        answer.option_custom = option['option_custom']
                                        answer.save()
                                else:
                                    if 'option_custom' in option and option['option_custom']:
                                        InterviewRecordTemplateAnswer.objects.create(
                                            interview_id=project_interview_id, public_attr=project_interview.public_attr,
                                            question_id=question['id'], option_id=option['option'],
                                            option_custom=option['option_custom']
                                        )
                                    else:
                                        InterviewRecordTemplateAnswer.objects.create(
                                            interview_id=project_interview_id, public_attr=project_interview.public_attr,
                                            question_id=question['id'], option_id=option['option'])
                            if question['required']:
                                question_list.append(question['id'])

                # 评分
                if question['type'] == InterviewRecordTemplateQuestionTypeEnum.rating.value:
                    if 'answer' in question.keys() and question['answer']:

                        if 'id' in question['answer'].keys():
                            InterviewRecordTemplateAnswer.objects.filter(pk=question['answer']['id']).update(
                                score=question['answer']['score'])
                        else:
                            InterviewRecordTemplateAnswer.objects.create(
                                interview_id=project_interview_id, public_attr=project_interview.public_attr,
                                question_id=question['id'], score=question['answer']['score']
                            )
                        if question['required']:
                            question_list.append(question['id'])

                        # 如果是项目并且评分过低，触发邮件通知
                        if int(question['answer']['score']) < 7 and project_id:
                            user_score[question['id']] = question['answer']['score']
            UserTmp.objects.filter(
                data_id=project_interview_id,
                user_id=user_id,
                type=UserTmpEnum.interview,
            ).delete()

        question_list = list(set(question_list))
        if role in [UserRoleEnum.coachee.value, UserRoleEnum.trainee_coachee.value]:
            # 题全部都答了才算已结束
            if question_list == required_list:
                project_interview.coachee_record_status = True

                if project_interview.place_category == ProjectInterviewPlaceCategoryEnum.online_one_to_one:
                    # 教练通知
                    send_coach_coachee_record_finish_message.delay(project_interview.pk)
            else:
                project_interview.coachee_record_status = False

            # B端项目问卷类型同步议题
            # if project_id:
            #     coachee_answer = interview_public.get_interview_to_question_answer(project_interview, 1, is_coach=False)
            #     if coachee_answer:
            #         project_interview.coachee_topic = coachee_answer

            project_interview.save()

            # 评分过低，触发邮件通知
            if user_score:
                score_email = []
                for question_id, score in user_score.items():
                    raw_question = InterviewRecordTemplateQuestion.objects.get(pk=question_id)
                    score_email.append(
                        f"{InterviewRecordTemplateQuestionRatingTypeEnum.get_display(raw_question.rating_type)}打{score}分")

                describe = '、'.join(score_email)
                send_scoring_risk_alert_email.delay(
                    describe=describe, interview_id=project_interview_id, message_type='scoring_risk_alert')

        elif role in [UserRoleEnum.coach, UserRoleEnum.trainee_coach]:
            if question_list == required_list:
                project_interview.coach_record_status = True
                # 利益相关者访谈，记录教练填写的每一道题的答案到利益相关者报告的缓存中
                update_stakeholder_interview_coach_task_tmp_data.apply_async(
                        kwargs=dict(interview_id=project_interview_id), countdown=5, expires=120)
                # 签约教练填写辅导记录后立即提醒客户查看
                if role == 1 and project_id and not project_interview.coachee_record_status:
                    send_interview_view_message.delay(project_interview.pk)

                if (project_interview.place_category == ProjectInterviewPlaceCategoryEnum.online_one_to_one and
                        project_interview.type == ProjectInterviewTypeEnum.formal_interview.value):
                    interview_public.fill_record(project_interview, is_coach=True)

            else:
                project_interview.coach_record_status = False

            # B端项目问卷类型同步议题
            # if project_id:
            #     coach_answer = interview_public.get_interview_to_question_answer(project_interview, 1, is_coach=True)
            #     if coach_answer:
            #         project_interview.topic = coach_answer

            project_interview.save()

            # 更新教练的客户画像信息
            task.interview_record_update_customer_portrait.delay(project_interview.id)

        return success_response()

    @swagger_auto_schema(
        operation_id='app完成辅导记录模版问题后填写教练评价',
        operation_summary='app完成辅导记录模版问题后填写教练评价',
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'interview_id': openapi.Schema(type=openapi.TYPE_NUMBER, description='约谈id'),
                'score': openapi.Schema(type=openapi.TYPE_NUMBER, description='评分'),
                'appraise': openapi.Schema(type=openapi.TYPE_STRING, description='评价')
            }
        ),
        tags=['app约谈记录填写相关']
    )
    @action(methods=['post'], detail=False, url_path='add_coach_appraise')
    def add_coach_appraise(self, request, *args, **kwargs):
        try:
            project_interview_id = request.data['interview_id']
            project_interview = ProjectInterview.objects.get(pk=project_interview_id)
            score = int(request.data['score'])
            appraise = request.data['appraise']
        except:
            return parameter_error_response()

        if project_interview.record_type == InterviewRecordTypeEnum.questionnaire.value:
            coach_appraise = CoachAppraise.objects.filter(interview_id=project_interview_id, deleted=False).first()
            if coach_appraise:
                coach_appraise.score = score
                coach_appraise.appraise = appraise
                coach_appraise.save()
            else:
                data = {'interview_id': project_interview.pk, "score": score, "appraise": appraise}
                CoachAppraise.objects.create(**data)
        elif project_interview.record_type == InterviewRecordTypeEnum.question_and_answer.value:
            project_interview_record = ProjectInterviewRecord.objects.filter(deleted=False, interview_id=project_interview_id).first()
            if project_interview_record:
                project_interview_record.coach_score = score
                project_interview_record.coach_comment = appraise
                project_interview_record.save()
            else:
                data = {'interview_id': project_interview.pk, "coach_score": score, "coach_comment": appraise}
                ProjectInterviewRecord.objects.create(**data)
        return success_response()
