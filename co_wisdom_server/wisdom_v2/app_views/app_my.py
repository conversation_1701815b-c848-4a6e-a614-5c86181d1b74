import re
import jieba

from datetime import datetime

import redis
from django.conf import settings
from drf_yasg import openapi
from rest_framework.decorators import action
from django.db.models import Sum, Q, Case, When

from drf_yasg.utils import swagger_auto_schema

from utils import redis_public
from utils.send_account_email import get_project_manager_wx_qr_code
from wisdom_v2.common import coach_public, resume_public
from wisdom_v2.enum.project_enum import ProjectDocsTypeEnum
from wisdom_v2.enum.service_content_enum import CoachTypeEnum
from wisdom_v2.enum.user_enum import UserRoleEnum, BrowseRecordObjectEnum
from wisdom_v2.common import coachee_public, interview_public
from wisdom_v2.models import ProjectInterview, User, Project, Coach, ProjectMember, ActionPlan, PublicAttr, \
    ProjectInterviewRecord, Article, SlideShow, CapacityTag, Habit, Diary, LearnArticle, EvaluationReport, \
    EvaluationModule, UserAdditionalInfo, OneToOneCoach, ProjectCoach, ProjectDocs, PersonalUser
from wisdom_v2.app_views.app_my_actions import MyCoachSerializers, SlideSerializers
from rest_framework.viewsets import GenericViewSet
from utils.pagination import StandardResultsSetPagination
from wisdom_v2.models_file.coach import BrowseRecord
from wisdom_v2.views.article_actions import ArticleSerializers
from rest_framework import exceptions
from wisdom_v2.common import project_service_public

from utils.api_response import success_response, parameter_error_response

from wisdom_v2.views.constant import BUNDLE_INTEWVIEW_TYPE_ONLINE_1V1, LBI_EVALUATION, \
    ATTR_TYPE_EVALUATION_ANSWER

user_data_redis = redis.Redis.from_url(settings.DATA_REDIS)


class AppMyViewSet(GenericViewSet):
    queryset = Coach.objects.all().order_by('-created_at')
    serializer_class = MyCoachSerializers

    @swagger_auto_schema(
        operation_id='app教练我的接口',
        operation_summary='app教练我的接口',
        manual_parameters=[
            openapi.Parameter('coach_user_id', openapi.IN_QUERY, description='用户id',
                              type=openapi.TYPE_NUMBER, required=True),
        ],
        tags=['app用户相关']
    )
    @action(methods=['get'], detail=False, url_path='coach')
    def coach_main(self, request, *args, **kwargs):
        try:
            instance = User.objects.get(pk=request.query_params.get('coach_user_id', 0))
            role = int(request.headers.get('role'))
            coach = Coach.objects.get(deleted=False, user_id=instance.pk)
            resume = coach.resumes.get(is_customization=False, deleted=False)
        except:
            return parameter_error_response('请检查传入参数')
        try:
            if role == UserRoleEnum.coach:
                coachee_id_list = coachee_public.get_coach_customer_list(instance.id)
                interview_hour = interview_public.get_project_coach_interview_time(instance.id)

            elif role == UserRoleEnum.trainee_coach:
                coachee_id_list = coachee_public.get_personal_coach_customer_list(instance.id)
                interview_hour = interview_public.get_personal_coach_interview_time(instance.id)
            else:
                return parameter_error_response('错误的用户权限')

            # 见习教练申请状态，1-可以查看，2-弹窗提示无通过的入驻申请， 默认没有按钮
            intern_apply_status = coach_public.get_intern_apply_status(coach)

            data = {
                'interview_hour': interview_hour,  # 辅导小时数
                'customer_count': len(coachee_id_list),  # 客户数量
                'intern_apply_status': intern_apply_status,
            }
            if role == 1:
                project_list = Project.objects.filter(
                    project_user__coach__user_id=instance.id,
                    project_user__deleted=False,
                    deleted=False
                ).distinct().count()
                data['project_count'] = project_list
            data['true_name'] = coach.personal_name
            data['head_image_url'] = resume.head_image_url if resume.head_image_url else coach.user.head_image_url
            data['resume_id'] = resume.pk
            cumulative_income, withdrawn_income, not_withdrawn_income = coach_public.get_coach_income(coach)
            data['cumulative_income'] = cumulative_income
            data['withdrawn_income'] = withdrawn_income
            data['not_withdrawn_income'] = not_withdrawn_income

            aggregates = BrowseRecord.objects.filter(
                user_id=instance.pk, object_type=BrowseRecordObjectEnum.resume.value, deleted=False).aggregate(
                total_views=Sum('total_views'),
                total_shares=Sum('total_shares')
            )
            data['total_views'] = aggregates.get('total_views')
            data['total_shares'] = aggregates.get('total_shares')

            #  获取指定用户的所有浏览记录的总数
            pattern = f"browse_record_user:{instance.pk}:resume:*"
            views_last_30_days = 0
            for key in user_data_redis.scan_iter(match=pattern):
                views_last_30_days += int(user_data_redis.get(key) or 0)
            data['views_last_30_days'] = views_last_30_days
            data['is_complete_resume'] = resume_public.verify_resume_required_completion(resume)  # 是否完成简历

            return success_response(data, request=request)
        except Exception as e:
            return parameter_error_response(e)

    @swagger_auto_schema(
        operation_id='app教练客户详情项目路径图接口',
        operation_summary='app教练客户详情项目路径图接口',
        manual_parameters=[
            openapi.Parameter('user_id', openapi.IN_QUERY, description='用户id',
                              type=openapi.TYPE_NUMBER, required=True),
        ],
        tags=['app用户相关']
    )
    @action(methods=['get'], detail=False, url_path='coach/path_diagram')
    def coach_path_diagram(self, request, *args, **kwargs):
        try:
            user_id = request.query_params.get('user_id')
            project_member = ProjectMember.objects.filter(user__id=user_id, deleted=False, is_forbidden=False).first()
            role = request.headers.get('role')
            coach_user_id = request.user.pk
            coach = Coach.objects.filter(user_id=coach_user_id, deleted=False).first()
        except (KeyError, ProjectMember.DoesNotExist):
            return parameter_error_response('请检查传入参数')
        is_5alc = ProjectCoach.objects.filter(
            coach=coach, member=project_member.user, project=project_member.project,
            project_group_coach__isnull=True, deleted=False).exists()
        path_diagram = project_service_public.get_path_diagram(project_member, role, is_5alc, coach_user_id)
        return success_response(path_diagram)

    @swagger_auto_schema(
        operation_id='app被教练者我的接口',
        operation_summary='app被教练者我的接口',
        manual_parameters=[
            openapi.Parameter('user_id', openapi.IN_QUERY, description='用户id',
                              type=openapi.TYPE_NUMBER, required=True),
        ],
        tags=['app用户相关']
    )
    @action(methods=['get'], detail=False, url_path='coachee')
    def coachee_main(self, request, *args, **kwargs):
        try:
            instance = User.objects.get(pk=request.query_params.get('user_id', 0))
        except:
            return parameter_error_response('请检查传入参数')
        try:
            role = int(request.headers.get('role'))
        except:
            raise exceptions.AuthenticationFailed({'retCode': 401, 'retMsg': "登录过期 | Fail", 'err': '登录过期'})

        is_personal = True if role == UserRoleEnum.trainee_coachee else False
        try:
            action = ActionPlan.objects.filter(public_attr__user__id=instance.pk, deleted=False)
            note = Habit.objects.filter(public_attr__user__id=instance.pk, deleted=False)
            diary = Diary.objects.filter(public_attr__user__id=instance.pk, deleted=False)
            learn = LearnArticle.objects.filter(public_attr__user__id=instance.pk, deleted=False)
            report = EvaluationReport.objects.filter(deleted=False, public_attr__user__id=instance.pk)

            if is_personal:
                action_count = action.filter(public_attr__project__isnull=True).count()
                note_count = note.filter(public_attr__project__isnull=True).count()
                diary_count = diary.filter(public_attr__project__isnull=True).count()
                learn_count = learn.filter(public_attr__project__isnull=True).count()
                report_count = report.filter(public_attr__project__isnull=True).count()
                action_count += note_count
            else:
                action_count = action.filter(public_attr__project__isnull=False).count()
                note_count = note.filter(public_attr__project__isnull=False).count()
                diary_count = diary.filter(public_attr__project__isnull=False).count()
                learn_count = learn.filter(public_attr__project__isnull=False).count()
                report_count = report.filter(public_attr__project__isnull=False).count()
                action_count += note_count
                project_docs = ProjectDocs.objects.filter(
                    project_member__user_id=instance.pk, file__deleted=False,
                    project_docs_type=ProjectDocsTypeEnum.report, deleted=False).count()
                report_count += project_docs

            leader_capacity_times = ProjectInterviewRecord.objects.filter(
                interview__public_attr__target_user__id=instance.pk, leader_capacity__isnull=False,
                deleted=False, leader_capacity__tag_type=1).values('leader_capacity').annotate(
                all_times=Sum('interview__times')).order_by('-all_times')
            leader_capacity_list = []
            for times in leader_capacity_times:
                res = {}
                res['leader_capacity'] = CapacityTag.objects.get(pk=times['leader_capacity'], tag_type=1).title
                res['times'] = round(times['all_times'], 1)
                res['question_count'] = ProjectInterviewRecord.objects.filter(
                    deleted=False, interview__public_attr__target_user__id=instance.pk,
                    leader_capacity__id=times['leader_capacity'], target_progress__gt=5).count()
                if res:
                    leader_capacity_list.append(res)
            data = {
                'action_count': action_count,  # 行动任务数
                'note_count': note_count,  # 习惯养成
                'study_count': learn_count,  # 拓展学习
                'diary_count': diary_count,  # 成长笔记
                'report_count': report_count,  # 测评报告数量
                'leader_capacity': leader_capacity_list,  # 领导力分类成果
                'true_name': instance.cover_name,
                'head_image_url': instance.head_image_url,
                'nickname': None,
                'personal_email': None
            }
            personal_user = PersonalUser.objects.filter(user_id=instance.id, deleted=False).first()
            if personal_user:
                data['nickname'] = personal_user.nickname if personal_user.nickname else instance.cover_name
                data['personal_email'] = personal_user.email
            return success_response(data, request=request)
        except Exception as e:
            return parameter_error_response(e)

    @swagger_auto_schema(
        operation_id='app被教练者首页项目路径图接口',
        operation_summary='app被教练者首页项目路径图接口',
        manual_parameters=[
            openapi.Parameter('user_id', openapi.IN_QUERY, description='用户id',
                              type=openapi.TYPE_NUMBER, required=True),
        ],
        tags=['app用户相关']
    )
    @action(methods=['get'], detail=False, url_path='coachee/path_diagram')
    def path_diagram(self, request, *args, **kwargs):
        try:
            user_id = request.query_params.get('user_id')
            project_member = ProjectMember.objects.filter(user_id=user_id, deleted=False, is_forbidden=False).first()
        except (KeyError, ProjectMember.DoesNotExist):
            return parameter_error_response('请检查传入参数')
        path_diagram = project_service_public.get_path_diagram(project_member)
        return success_response(path_diagram)

    @swagger_auto_schema(
        operation_id='app教练首页',
        operation_summary='app教练首页',
        manual_parameters=[
            openapi.Parameter('coach_user_id', openapi.IN_QUERY, description='用户id',
                              type=openapi.TYPE_NUMBER, required=True),
        ],
        tags=['app用户相关']
    )
    @action(methods=['get'], detail=False, url_path='coach/home')
    def coach_home(self, request, *args, **kwargs):
        try:
            instance = User.objects.get(pk=request.query_params.get('coach_user_id', 0))
        except User.DoesNotExist:
            return parameter_error_response('请检查传入参数')
        try:
            interview_queryset = ProjectInterview.objects.filter(public_attr__user__id=instance.pk,
                                                                 public_attr__end_time__lt=datetime.now(),
                                                                 public_attr__target_user__isnull=False,
                                                                 coach_record_status__isnull=True,
                                                                 place_category=BUNDLE_INTEWVIEW_TYPE_ONLINE_1V1,
                                                                 deleted=False
                                                                 ).exclude(public_attr__status__in=[5, 6]
                                                                           ).order_by('-public_attr__start_time')
            slideshow = SlideShow.objects.filter(deleted=False, enabled=True).first()
            if not slideshow:
                slideshow = SlideShow.objects.filter(deleted=False, enabled=True).last()
            image = SlideSerializers(slideshow).data
            interview_list = []
            for interview in interview_queryset:
                interview_list.append({"coachee_name": interview.public_attr.target_user.cover_name,
                                       "interview_time": interview.public_attr.start_time.strftime('%Y-%m-%d %H:%M') +
                                                         '-' + interview.public_attr.end_time.strftime('%H:%M'),
                                       "interview_id": interview.pk})
            return success_response(interview_list, request, {'image': image})
        except Exception as e:
            return parameter_error_response(str(e))

    @swagger_auto_schema(
        operation_id='app被教首页',
        operation_summary='app被教首页',
        manual_parameters=[
            openapi.Parameter('user_id', openapi.IN_QUERY, description='用户id',
                              type=openapi.TYPE_NUMBER, required=True),
        ],
        tags=['app用户相关']
    )
    @action(methods=['get'], detail=False, url_path='coachee/home', authentication_classes=[])
    def coachee_home(self, request, *args, **kwargs):
        try:
            slideshow = SlideShow.objects.filter(deleted=False, enabled=True).first()
            if not slideshow:
                slideshow = SlideShow.objects.filter(deleted=False, enabled=True).last()
            image = SlideSerializers(slideshow).data
            data = {
                'image': image,
                'is_can_coach_interview': False,  # 用户是否可以预约教练
                'qrcode_url': None,  # 项目运营二维码
                'evaluation': None,
                'is_collect_user_data': False,  # 用户是否触发开屏信息收集
            }
            # 游客和个人客户直接返回，不需要查询用户数据
            if int(request.headers.get('role', UserRoleEnum.trainee_coachee)) == UserRoleEnum.trainee_coachee:
                return success_response(data)

            user_id = request.query_params.get('user_id', None)

            if user_id:
                user = User.objects.get(pk=user_id)
                project_member = ProjectMember.objects.filter(user_id=user_id, deleted=False).first()
                if project_member:
                    # 查询是否要做开屏信息收集
                    if project_member.project.is_collect_info:
                        # 查询有没有填写过数据
                        user_data = UserAdditionalInfo.objects.filter(
                            user_id=user_id
                        ).first()
                        # 没有填写的前提下查询今天有没有触发过
                        if not user_data:
                            data['is_collect_user_data'] = True
                            # is_trigger = user_data_redis.get(f'{user_id}_is_trigger_user_data')

                            # # 没有触发过则返回题目数据且在缓存中记录
                            # if not is_trigger:
                            #     data['is_collect_user_data'] = True
                                # redis_public.save_today_end_redis(
                                #     user_data_redis, f'{user_id}_is_trigger_user_data', 1)
                                
                        lbi_evaluation_module = EvaluationModule.objects.filter(
                            project_bundle__project_member=project_member,
                            evaluation__code=LBI_EVALUATION,
                            start_time__lte=datetime.now().date(), end_time__gte=datetime.now().date(),
                            deleted=False).first()
                        if lbi_evaluation_module:
                            # LBI测评需要教育信息
                            if user_data and not user_data.education:
                                data['is_collect_user_data'] = True
                                # redis_public.save_today_end_redis(
                                #     user_data_redis, f'{user_id}_is_trigger_user_data', 1)
                            
                    # 查询是否有开屏测评
                    evaluation_module = EvaluationModule.objects.filter(
                        project_bundle__project_member=project_member,
                        start_time__lte=datetime.now().date(), end_time__gte=datetime.now().date(),
                        deleted=False, is_open_screen=True, is_submit=False).first()
                    if evaluation_module:
                        public_attr = PublicAttr.objects.filter(
                            user=user,
                            project=evaluation_module.project_bundle.project.id,
                            evaluation_answer_public_attr__option__question__evaluation=evaluation_module.evaluation,
                            type=ATTR_TYPE_EVALUATION_ANSWER
                        )
                        if evaluation_module.evaluation.code == LBI_EVALUATION:
                            public_attr = public_attr.filter(target_user=user)
                        if not public_attr:
                            evaluation = {
                                'evaluation_id': evaluation_module.evaluation_id,
                                'evaluation_code': evaluation_module.evaluation.code,
                                'user_name': user.name,
                                'project_id': evaluation_module.project_bundle.project_id,
                                'user_id': user_id,
                                'welcome': "邀请您完成《%s》，该测评将帮助您提升自我认知，并带着更明确的目标参与学习" % \
                                           evaluation_module.evaluation.name,
                            }
                            data['evaluation'] = evaluation

                    

                online = OneToOneCoach.objects.filter(project_bundle__project_member=project_member,
                                                      type=CoachTypeEnum.online.value, deleted=False).first()
                if online:
                    data['is_can_coach_interview'] = True
                else:
                    data['qrcode_url'] = get_project_manager_wx_qr_code(project_member.project.id)

            return success_response(data, request=request)
        except Exception as e:
            return parameter_error_response(str(e))

    @swagger_auto_schema(
        operation_id='app被教首页搜索',
        operation_summary='app被教首页搜索',
        manual_parameters=[
            openapi.Parameter('keyword', openapi.IN_QUERY, description='搜索关键字/视野拓展文章标题',
                              type=openapi.TYPE_STRING, required=True),
        ],
        tags=['app用户相关']
    )
    @action(methods=['get'], detail=False, url_path='coachee/search')
    def coachee_search(self, request, *args, **kwargs):
        keyword = request.query_params.get('keyword', '')
        try:
            if keyword:
                keyword = re.sub(r'[\W]', ' ', keyword)
            seg = [item for item in jieba.cut(keyword) if item != ' ']
            # 1，3 为经验和工具类型的文章，2 为关于教练文章
            # 标题关键字查询
            temp = Q()
            temp.connector = 'OR'
            for item in seg:
                temp.children.append(('title__icontains', item))
            title_article = Article.objects.filter(
                Q(deleted=False) & Q(enabled=True, category__in=[1, 3])
                & temp).order_by('-created_at')
            title_article_id = [item.id for item in title_article]

            # 内容关键字查询
            temp = Q()
            temp.connector = 'OR'
            for item in seg:
                temp.children.append(('content__icontains', item))
            content_article = Article.objects.filter(
                Q(deleted=False) & Q(enabled=True, category__in=[1, 3])
                & ~Q(id__in=title_article_id) & temp).order_by('-created_at')
            content_article_id = [item.id for item in content_article]

            # 数据合并
            article = title_article | content_article

            # 指定id列表排序
            article_id = Case(*[When(pk=order, then=pos) for pos, order in enumerate(
                title_article_id + content_article_id)])
            article = article.order_by(article_id)

            data = ArticleSerializers(article, many=True).data

            # 分页
            paginator = StandardResultsSetPagination()
            page_list = paginator.paginate_queryset(data, self.request)
            response = paginator.get_paginated_response(page_list)
            return success_response(response, request=request)
        except Exception as e:
            return parameter_error_response(str(e))
