import datetime
from drf_yasg import openapi
from drf_yasg.utils import swagger_auto_schema
from rest_framework.decorators import action
from django.db.models import Q

from rest_framework import serializers
from rest_framework import viewsets

from wisdom_v2.common import user_public
from wisdom_v2.enum.user_enum import UserRoleEnum
from wisdom_v2.models import Habit, ProjectInterviewRecord, CapacityTag, PublicAttr, User, Project, SignIn

from utils.pagination import StandardResultsSetPagination
from utils.api_response import success_response, parameter_error_response
from wisdom_v2.views.constant import ATTR_TYPE_HABIT, ROLE_COACHEE


class HabitSerializer(serializers.ModelSerializer):
    capacity = serializers.SerializerMethodField()
    created_at = serializers.DateTimeField(format='%Y-%m-%d %H:%M')
    sign_info = serializers.SerializerMethodField()
    tag_type = serializers.SerializerMethodField()

    class Meta:
        model = Habit
        exclude = ('updated_at', 'creator_role', 'public_attr', 'deleted')

    def get_capacity(self, obj):
        try:
            if obj.interview:
                capacity = CapacityTag.objects.get(interview_id=obj.interview.pk, select_type=1, deleted=False)
                return capacity.title
        except CapacityTag.DoesNotExist:
            return

    def get_sign_info(self, obj):
        sign = SignIn.objects.filter(habit=obj).order_by('-created_at')
        sign_info = {'sign_status': sign.filter(sign_date=datetime.datetime.today().date()).exists(),
                     'continue_sign_count': 0,
                     'all_count': sign.count()}
        if sign:
            sign_info['continue_sign_count'] = sign.first().continue_sign_count
            sign_info['last_continue_sign_count'] = sign.first().last_continue_sign_count
        return sign_info

    def get_tag_type(self, obj):
        return 1


class HabitViewSet(viewsets.ModelViewSet):
    queryset = Habit.objects.filter(deleted=False).order_by('-end_date')
    serializer_class = HabitSerializer

    @swagger_auto_schema(
        operation_id='app被教习惯养成列表',
        operation_summary='app被教习惯养成列表',
        manual_parameters=[
            openapi.Parameter('user_id', openapi.IN_QUERY, description='用户id', type=openapi.TYPE_NUMBER,
                              required=True),
            openapi.Parameter('page', openapi.IN_QUERY, description='第几页', type=openapi.TYPE_NUMBER),
            openapi.Parameter('page_size', openapi.IN_QUERY, description='每页显示条数默认15条', type=openapi.TYPE_NUMBER)
        ],
        tags=['app被教习惯养成']
    )
    def list(self, request, *args, **kwargs):
        try:
            user_id = request.query_params.get('user_id')
            role = int(request.headers.get('role'), 0)
            queryset = self.get_queryset().filter(public_attr__user__pk=user_id)
            if role == UserRoleEnum.trainee_coachee:
                queryset = queryset.filter(public_attr__project__isnull=True)
            elif role == UserRoleEnum.coachee:
                queryset = queryset.filter(public_attr__project__isnull=False)

        except Exception as e:
            return parameter_error_response(str(e))

        # 分页
        paginator = StandardResultsSetPagination()
        page_list = paginator.paginate_queryset(queryset, self.request)
        serializer = self.serializer_class(page_list, many=True, context={'queryset': queryset})
        response = paginator.get_paginated_response(serializer.data)
        return success_response(response, request=request)

    @swagger_auto_schema(
        operation_id='app被教添加习惯养成',
        operation_summary='app被教添加习惯养成',
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'user_id': openapi.Schema(type=openapi.TYPE_NUMBER, description='用户id'),
                'project_id': openapi.Schema(type=openapi.TYPE_NUMBER, description='项目id 普通被教练者必传'),
                'stop': openapi.Schema(type=openapi.TYPE_STRING, description='当...'),
                'when': openapi.Schema(type=openapi.TYPE_STRING, description='停止...'),
                'change': openapi.Schema(type=openapi.TYPE_STRING, description='转变...'),
                'start_date': openapi.Schema(type=openapi.TYPE_STRING, description='开始日期'),
                'end_date': openapi.Schema(type=openapi.TYPE_STRING, description='结束日期'),
            }
        ),
        tags=['app被教习惯养成']
    )
    def create(self, request, *args, **kwargs):
        try:
            user_id = request.data.get('user_id')
            stop = request.data.get('stop')
            when = request.data.get('when')
            change = request.data.get('change')
            start_date = request.data.get('start_date')
            end_date = request.data.get('end_date')
            user = User.objects.get(pk=user_id, deleted=False)
            user_role = int(request.headers.get('role'))
        except (ValueError, TypeError):
            return parameter_error_response()
        except User.DoesNotExist:
            return parameter_error_response('用户不存在')

        if user_role == 2:  # 普通被教练者
            # 读取用户项目
            project_member = user_public.get_user_to_project_member_all(user_id)
            project_id = project_member[0].project_id if project_member else None

            public_attr = PublicAttr.objects.create(project_id=project_id, user=user, type=ATTR_TYPE_HABIT)
            Habit.objects.create(when=when, stop=stop, change=change, start_date=start_date, end_date=end_date,
                                 creator_role=ROLE_COACHEE, public_attr=public_attr)
        elif user_role == 4:  # 三无学员
            public_attr = PublicAttr.objects.create(user=user, type=ATTR_TYPE_HABIT)
            Habit.objects.create(when=when, stop=stop, change=change, start_date=start_date, end_date=end_date,
                                 creator_role=ROLE_COACHEE, public_attr=public_attr)
        else:
            return parameter_error_response()
        return success_response(request=request)

    @swagger_auto_schema(
        operation_id='app被教习惯养成详情',
        operation_summary='app被教习惯养成详情',
        manual_parameters=[
            openapi.Parameter('habit_id', openapi.IN_QUERY, description='习惯养成id',
                              type=openapi.TYPE_NUMBER, required=True)
        ],
        tags=['app被教习惯养成']
    )
    @action(methods=['get'], detail=False, url_path='detail')
    def habit_detail(self, request, *args, **kwargs):
        try:
            instance = Habit.objects.get(pk=request.query_params.get('habit_id', 0), deleted=False)
        except Habit.DoesNotExist:
            return parameter_error_response('习惯养成id错误')
        serializer = self.get_serializer(instance)
        return success_response(serializer.data, request=request)

    @swagger_auto_schema(
        operation_id='app被教修改习惯养成',
        operation_summary='app被教修习惯养成',
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'habit_id': openapi.Schema(type=openapi.TYPE_NUMBER, description='行动计划id'),
                'stop': openapi.Schema(type=openapi.TYPE_STRING, description='当...'),
                'when': openapi.Schema(type=openapi.TYPE_STRING, description='停止...'),
                'change': openapi.Schema(type=openapi.TYPE_STRING, description='转变...'),
                'start_date': openapi.Schema(type=openapi.TYPE_STRING, description='开始日期'),
                'end_date': openapi.Schema(type=openapi.TYPE_STRING, description='结束日期'),
                'status': openapi.Schema(type=openapi.TYPE_NUMBER, description='1: 进行中 2:已完成 3:已取消'),
            }
        ),
        tags=['app被教习惯养成']
    )
    @action(methods=['post'], detail=False, url_path='update')
    def habit_update(self, request, *args, **kwargs):
        try:
            instance = Habit.objects.get(pk=request.data.get('habit_id'), deleted=False)
        except Habit.DoesNotExist:
            return parameter_error_response('习惯养成不存在')
        data = request.data.copy()
        serializer = self.get_serializer(instance, data=data, partial=True)
        serializer.is_valid(raise_exception=True)
        serializer.save()
        return success_response(serializer.data, request=request)

    @swagger_auto_schema(
        operation_id='app被教习惯养成打卡',
        operation_summary='app被教习惯养成打卡',
        manual_parameters=[
            openapi.Parameter('habit_id', openapi.IN_QUERY, description='习惯养成id',
                              type=openapi.TYPE_NUMBER, required=True)
        ],
        tags=['app被教习惯养成']
    )
    @action(methods=['get'], detail=False, url_path='habit_sign_in')
    def habit_sign_in(self, request, *args, **kwargs):
        try:
            instance = Habit.objects.get(pk=request.query_params.get('habit_id', 0), deleted=False)
        except Habit.DoesNotExist:
            return parameter_error_response('习惯养成id错误')
        today_date = datetime.datetime.today().date()
        yesterday = datetime.datetime.today().date() - datetime.timedelta(days=1)
        sign_in = SignIn.objects.filter(habit=instance, sign_date=today_date)
        if sign_in:
            return parameter_error_response('今日已经打卡过了')
        last_sign = SignIn.objects.filter(habit=instance).order_by('-created_at').first()
        continue_sign_count = 1
        last_continue_sign_count = 1
        if last_sign:
            if last_sign.sign_date == yesterday:
                # 昨天打卡过，今天打卡时间各加一
                continue_sign_count = last_sign.continue_sign_count + 1
                last_continue_sign_count = last_sign.last_continue_sign_count + 1
            else:
                # 昨天没有打卡，已打卡时间加一，连续打卡时间为一
                last_continue_sign_count = last_sign.last_continue_sign_count + 1
        SignIn.objects.create(habit=instance, continue_sign_count=continue_sign_count,
                              last_continue_sign_count=last_continue_sign_count)
        sign_in_count = SignIn.objects.filter(habit=instance).count()
        all_count = (instance.end_date - instance.start_date).days
        if sign_in_count == all_count + 1:
            instance.status = 2
            instance.save()
        return success_response(request=request)


    @swagger_auto_schema(
        operation_id='app被教删除习惯养成',
        operation_summary='app被教删除习惯养成',
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'habit_id': openapi.Schema(type=openapi.TYPE_NUMBER, description='习惯养成id'),
            }
        ),
        tags=['app被教习惯养成']
    )
    @action(methods=['post'], detail=False, url_path='delete')
    def habit_delete(self, request, *args, **kwargs):
        try:
            instance = Habit.objects.get(pk=request.data.get('habit_id'), deleted=False)
        except Habit.DoesNotExist:
            return parameter_error_response('习惯养成不存在')
        instance.deleted = True
        instance.save()
        return success_response(request=request)
