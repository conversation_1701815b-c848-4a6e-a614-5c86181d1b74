import datetime
import numpy as np
from rest_framework import serializers
from ..models import <PERSON>, PublicAttr, ProjectInterview, Article, SlideShow
from django.db.models import Sum, Q


class MyCoachSerializers(serializers.ModelSerializer):
    user_id = serializers.IntegerField()

    class Meta:
        model = Coach
        exclude = ('created_at', 'updated_at')


class ArticleSerializers(serializers.ModelSerializer):
    capacity_tag = serializers.CharField(source='capacity_tag.title')

    class Meta:
        model = Article
        exclude = ('created_at', 'updated_at', 'deleted', 'enabled')


class SlideSerializers(serializers.ModelSerializer):

    class Meta:
        model = SlideShow
        exclude = ('created_at', 'updated_at', 'deleted', 'enabled')
