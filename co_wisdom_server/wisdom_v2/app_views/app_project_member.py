from datetime import datetime
from drf_yasg import openapi
from rest_framework.decorators import action
from django.db.models import Sum

from drf_yasg.utils import swagger_auto_schema
from ..models import ProjectInterview, User, ProjectCoach, Project, Coach, ProjectMember
from .app_project_member_actions import AppUserSerializers
from rest_framework import mixins
from rest_framework import serializers
from rest_framework.viewsets import GenericViewSet

from utils.pagination import StandardResultsSetPagination
from utils.api_response import success_response, parameter_error_response


class AppProjectMemberViewSet(GenericViewSet):
    queryset = ProjectMember.objects.all().order_by('-created_at')
    serializer_class = AppUserSerializers

    @swagger_auto_schema(
        operation_id='app用户对应教练',
        operation_summary='app用户对应教练',
        manual_parameters=[
            openapi.Parameter('user_id', openapi.IN_QUERY, description='用户id', type=openapi.TYPE_NUMBER, required=True),
            openapi.Parameter('project_id', openapi.IN_QUERY, description='项目id', type=openapi.TYPE_NUMBER, required=True)
        ],
        tags=['app用户相关']
    )
    @action(methods=['get'], detail=False)
    def user_coach(self, request, *args, **kwargs):
        try:
            instance = User.objects.get(pk=request.query_params.get('user_id', 0))
            project = Project.objects.get(pk=request.query_params.get('project_id', 0))
        except (User.DoesNotExist, Project.DoesNotExist):
            return parameter_error_response('请检查传入参数')
        try:
            coach_id = ProjectCoach.objects.get(member=instance, project=project, deleted=False).coach_id
            coach = Coach.objects.get(pk=coach_id, deleted=False)
            times = ProjectInterview.objects.filter(public_attr__user_id=coach_id,
                                                    public_attr__target_user_id=instance.pk, deleted=False,
                                                    public_attr__project_id=project.pk).aggregate(sum=Sum("times"))
            data = {
                'coach_id': coach.user.pk,
                'name': coach.user.name,
                'head_image_url': coach.user.head_image_url,
                'interview_times': times['sum']
            }
            return success_response(data, request=request)
        except Exception as e:
            print(e)
            return parameter_error_response('该用户尚未匹配教练')
