from drf_yasg import openapi
from rest_framework.decorators import action
from drf_yasg.utils import swagger_auto_schema
from rest_framework import viewsets

from wisdom_v2.app_views.app_slide_show_actions import AppSlideShowSerializers
from wisdom_v2.models import SlideShow
from utils.pagination import StandardResultsSetPagination
from utils.api_response import success_response, parameter_error_response


class AppSlideShowViewSet(viewsets.ModelViewSet):
    queryset = SlideShow.objects.filter(deleted=False, enabled=True).order_by('-enabled', 'order', '-created_at')
    serializer_class = AppSlideShowSerializers
    authentication_classes = []

    @swagger_auto_schema(
        operation_id='轮播图列表',
        operation_summary='轮播图列表',
        manual_parameters=[
            openapi.Parameter('keyword', openapi.IN_QUERY, description='关键字（轮播图的文字）', type=openapi.TYPE_STRING),
            openapi.Parameter('page', openapi.IN_QUERY, description='第几页', type=openapi.TYPE_NUMBER),
            openapi.Parameter('page_size', openapi.IN_QUERY, description='每页显示条数默认15条', type=openapi.TYPE_NUMBER),
            openapi.Parameter('suitable_object', openapi.IN_QUERY, description='适用对象', type=openapi.TYPE_NUMBER),

        ],
        tags=['轮播图相关']
    )
    def list(self, request, *args, **kwargs):

        slide_show = self.get_queryset()
        if request.query_params.get('keyword', None):
            slide_show = slide_show.filter(title__icontains=request.query_params.get('keyword').lower())
        suitable_object = request.query_params.get('suitable_object', 4)
        slide_show = slide_show.filter(suitable_object=suitable_object)

        # 分页
        paginator = StandardResultsSetPagination()
        page_list = paginator.paginate_queryset(slide_show, self.request)
        serializer = self.get_serializer(page_list, many=True)
        response = paginator.get_paginated_response(serializer.data)
        return success_response(response)