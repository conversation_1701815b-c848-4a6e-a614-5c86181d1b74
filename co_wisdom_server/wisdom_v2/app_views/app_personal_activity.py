from drf_yasg import openapi
from drf_yasg.utils import swagger_auto_schema
from rest_framework.decorators import action
from rest_framework import viewsets

from utils.api_response import success_response, parameter_error_response
from wisdom_v2.app_views.app_personal_activity_actions import AppPersonalActivityCoachShareSerializer, \
    AppPersonalActivityDetailSerializer, AppPersonalActivityCoachUpdateResumeShareSerializer
from wisdom_v2.common import personal_activity_public

from wisdom_v2.models import User, Coach
from wisdom_v2.models_file import PersonalActivity


class AppPersonalActivityViewSet(viewsets.ModelViewSet):
    queryset = PersonalActivity.objects.filter(deleted=False)
    serializer_class = AppPersonalActivityDetailSerializer

    @swagger_auto_schema(
        operation_id='获取个人活动详情',
        operation_summary='获取个人活动详情',
        manual_parameters=[
            openapi.Parameter('user_id', openapi.IN_QUERY, description='用户标识', type=openapi.TYPE_NUMBER,
                              required=True),
            openapi.Parameter('type', openapi.IN_QUERY, description='活动类型', type=openapi.TYPE_NUMBER,
                              required=True),
        ],
        tags=['个人活动']
    )
    @action(methods=['get'], detail=False, url_path='detail')
    def get_personal_activity_detail(self, request, *args, **kwargs):
        try:
            user_id = request.query_params.get('user_id')
            activity_type = request.query_params.get('type')
            User.objects.get(id=user_id, deleted=False)
        except User.DoesNotExist:
            return parameter_error_response('用户不存在')
        except Exception as e:
            return parameter_error_response()

        activity = self.queryset.filter(
            type=activity_type, coach__user_id=user_id
        ).order_by('-created_at').first()
        if not activity:
            return success_response({})
        serializer = AppPersonalActivityCoachShareSerializer(activity).data
        return success_response(serializer)

    @swagger_auto_schema(
        operation_id='创建个人活动',
        operation_summary='创建个人活动',
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'user_id': openapi.Schema(type=openapi.TYPE_STRING, description='用户标识'),
                'type': openapi.Schema(type=openapi.TYPE_NUMBER, description='活动类型'),
                'price': openapi.Schema(type=openapi.TYPE_STRING, description='金额'),
            }),
        tags=['个人活动']
    )
    @action(methods=['post'], detail=False, url_path='add')
    def add_personal_activity(self, request, *args, **kwargs):
        try:
            user_id = request.data.get('user_id')
            user = User.objects.get(id=user_id, deleted=False)
            coach = Coach.objects.get(user_id=user.id, deleted=False)
        except User.DoesNotExist:
            return parameter_error_response('未获取到用户信息')
        except Coach.DoesNotExist:
            return parameter_error_response('未获取到教练信息')
        except Exception as e:
            return parameter_error_response()
        personal_activity, error_mag = personal_activity_public.add_personal_activity(coach, request.data)
        if error_mag:
            return parameter_error_response(error_mag)
        serializer = AppPersonalActivityCoachShareSerializer(personal_activity)
        return success_response(serializer.data)

    @swagger_auto_schema(
        operation_id='更新个人活动',
        operation_summary='更新个人活动',
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'personal_activity_id': openapi.Schema(type=openapi.TYPE_STRING, description='个人活动标识'),
                'price': openapi.Schema(type=openapi.TYPE_STRING, description='金额'),
                'limit_count': openapi.Schema(type=openapi.TYPE_STRING, description='单个用户限制次数'),
                'personal_name': openapi.Schema(type=openapi.TYPE_STRING, description='个人客户可见姓名'),
                'city': openapi.Schema(type=openapi.TYPE_STRING, description='所在城市'),
                'coach_domain': openapi.Schema(type=openapi.TYPE_STRING, description='擅长的教练领域'),
                'highest_position': openapi.Schema(type=openapi.TYPE_STRING, description='曾担任过的最高职位'),
                'job_profile': openapi.Schema(type=openapi.TYPE_STRING, description='一句话介绍工作经历'),
                'posters_text': openapi.Schema(type=openapi.TYPE_STRING, description='个人海报中的文本信息'),
                'is_sync_to_resume': openapi.Schema(type=openapi.TYPE_BOOLEAN, description='是否同步到简历'),
            }),
        tags=['个人活动']
    )
    @action(methods=['post'], detail=False, url_path='update')
    def update_personal_activity(self, request, *args, **kwargs):
        try:
            personal_activity = PersonalActivity.objects.get(id=request.data.get('personal_activity_id'), deleted=False)
        except PersonalActivity.DoesNotExist:
            return parameter_error_response('未获取到活动信息')
        except Exception as e:
            return parameter_error_response()
        personal_activity = personal_activity_public.update_personal_activity(personal_activity, request.data)
        serializer = AppPersonalActivityCoachShareSerializer(personal_activity)
        return success_response(serializer.data)

    @swagger_auto_schema(
        operation_id='获取简历格式个人活动数据详情',
        operation_summary='获取简历格式个人活动数据详情',
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'personal_activity_id': openapi.Schema(type=openapi.TYPE_STRING, description='个人活动标识'),
            }),
        tags=['个人活动']
    )
    @action(methods=['get'], detail=False, url_path='to_resume_detail')
    def personal_activity_to_resume_detail(self, request, *args, **kwargs):
        try:
            personal_activity = PersonalActivity.objects.get(id=request.query_params.get('personal_activity_id'), deleted=False)
        except PersonalActivity.DoesNotExist:
            return parameter_error_response('未获取到活动信息')
        except Exception as e:
            return parameter_error_response()
        serializer = AppPersonalActivityCoachUpdateResumeShareSerializer(personal_activity)
        return success_response(serializer.data)

