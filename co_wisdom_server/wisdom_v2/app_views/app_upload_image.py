import os
import uuid
import datetime
from django.conf import settings

from rest_framework.views import APIView
from drf_yasg import openapi
from rest_framework.decorators import action

from drf_yasg.utils import swagger_auto_schema
from utils.api_response import success_response, parameter_error_response




class UploadImage(APIView):
    authentication_classes = []

    @swagger_auto_schema(
        operation_id='上传图片',
        operation_summary='上传图片',
        tags=['app上传图片']
    )
    def post(self, request, *args, **kwargs):
        try:
            file_info = request.FILES.get('fileInput')
        except Exception as e:
            return parameter_error_response()
        allow_types = ['.gif', '.jpeg', '.jpg', '.png', '.xlsx', '.zip', '.mp4', '.pdf', '.doc', '.docx', '.xls',
                       '.ppt', '.pptx']
        filename, file_extension = os.path.splitext(file_info.name)
        if file_extension not in allow_types:
            return parameter_error_response('不支持此文件格式上传')
        model_name = 'app'
        time = datetime.datetime.now().strftime('%Y%m%d%H')
        filename = str(uuid.uuid4())[:8] + file_extension
        filepath = os.path.join(settings.MEDIA_ROOT, model_name, time, filename)
        parent_path = os.path.dirname(filepath)
        if not os.path.exists(parent_path):
            os.makedirs(parent_path)
        try:
            destination = open(filepath, 'wb+')
            for chunk in file_info.chunks():
                destination.write(chunk)
            destination.close()
        except Exception as e:
            return parameter_error_response('上传文件失败')
        relative_path = settings.SITE_URL + 'coapi/' + settings.UPLOAD_URL + model_name + '/' + time + '/' + filename
        return success_response(relative_path)


