from drf_yasg import openapi
from drf_yasg.utils import swagger_auto_schema
from rest_framework.decorators import action

from rest_framework import serializers
from rest_framework import viewsets

from wisdom_v2.models import PublicAttr, User, Project, ProjectNote

from utils.pagination import StandardResultsSetPagination
from utils.api_response import success_response, parameter_error_response
from wisdom_v2.views.constant import ATTR_TYPE_PROJECTNOTE


class ProjectNoteSerializer(serializers.ModelSerializer):
    created_at = serializers.DateTimeField(format='%Y-%m-%d %H:%M')

    class Meta:
        model = ProjectNote
        exclude = ('deleted', 'updated_at', 'public_attr')


class ProjectNoteViewSet(viewsets.ModelViewSet):
    queryset = ProjectNote.objects.filter(deleted=False).order_by('-created_at')
    serializer_class = ProjectNoteSerializer

    @swagger_auto_schema(
        operation_id='app教练 项目笔记列表',
        operation_summary='app教练 项目笔记列表',
        manual_parameters=[
            openapi.Parameter('coach_user_id', openapi.IN_QUERY, description='用户id', type=openapi.TYPE_NUMBER,
                              required=True),
            openapi.Parameter('project_id', openapi.IN_QUERY, description='项目笔记id', type=openapi.TYPE_NUMBER,
                              required=True),
            openapi.Parameter('page', openapi.IN_QUERY, description='第几页', type=openapi.TYPE_NUMBER),
            openapi.Parameter('page_size', openapi.IN_QUERY, description='每页显示条数默认15条', type=openapi.TYPE_NUMBER)
        ],
        tags=['app教练项目笔记']
    )
    def list(self, request, *args, **kwargs):
        queryset = self.get_queryset().filter(public_attr__user__pk=request.query_params.get('coach_user_id'),
                                              public_attr__project_id=request.query_params.get('project_id'),
                                              public_attr__type=ATTR_TYPE_PROJECTNOTE)

        # 分页
        paginator = StandardResultsSetPagination()
        page_list = paginator.paginate_queryset(queryset, self.request)
        serializer = self.serializer_class(page_list, many=True, context={'queryset': queryset})
        response = paginator.get_paginated_response(serializer.data)
        return success_response(response, request)


    @swagger_auto_schema(
        operation_id='app教练项目笔记详情',
        operation_summary='app教练项目笔记详情',
        manual_parameters=[
            openapi.Parameter('project_note_id', openapi.IN_QUERY, description='项目笔记id', type=openapi.TYPE_NUMBER,
                              required=True)
        ],
        tags=['app教练项目笔记']
    )
    @action(methods=['get'], detail=False, url_path='detail')
    def project_note_detail(self, request, *args, **kwargs):
        try:
            instance = ProjectNote.objects.get(pk=request.query_params.get('project_note_id', 0), deleted=False)
        except ProjectNote.DoesNotExist:
            return parameter_error_response('项目笔记id错误')
        serializer = self.get_serializer(instance)
        return success_response(serializer.data, request=request)


    @swagger_auto_schema(
        operation_id='app教练 项目笔记创建',
        operation_summary='app教练 项目笔记创建',
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'coach_user_id': openapi.Schema(type=openapi.TYPE_NUMBER, description='用户id'),
                'project_id': openapi.Schema(type=openapi.TYPE_NUMBER, description='项目id'),
                'content': openapi.Schema(type=openapi.TYPE_STRING, description='内容'),
            }
        ),
        tags=['app教练项目笔记']
    )
    def create(self, request, *args, **kwargs):
        try:
            user_id = request.data.get('coach_user_id')
            project_id = request.data.get('project_id')
            content = request.data.get('content')
            user = User.objects.get(pk=user_id, deleted=False)
            project = Project.objects.get(pk=project_id, deleted=False)
        except (ValueError, TypeError):
            return parameter_error_response()
        except User.DoesNotExist:
            return parameter_error_response('用户不存在')
        except Project.DoesNotExist:
            return parameter_error_response('项目不存在')

        public_attr = PublicAttr.objects.create(project=project, type=ATTR_TYPE_PROJECTNOTE, user=user)
        ProjectNote.objects.create(content=content, public_attr=public_attr)
        return success_response(request=request)

    @swagger_auto_schema(
        operation_id='app被教修改成长笔记',
        operation_summary='app被教修改成长笔记',
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'project_note_id': openapi.Schema(type=openapi.TYPE_NUMBER, description='项目笔记id'),
                'content': openapi.Schema(type=openapi.TYPE_STRING, description='内容'),
                'status': openapi.Schema(type=openapi.TYPE_NUMBER, description='1: 进行中 2:已完成 3:已取消'),
            }
        ),
        tags=['app教练项目笔记']
    )
    @action(methods=['post'], detail=False, url_path='update')
    def project_note_update(self, request, *args, **kwargs):
        try:
            instance = ProjectNote.objects.get(pk=request.data.get('project_note_id'), deleted=False)
        except ProjectNote.DoesNotExist:
            return parameter_error_response('项目笔记不存在')
        data = request.data.copy()
        serializer = self.get_serializer(instance, data=data, partial=True)
        serializer.is_valid(raise_exception=True)
        serializer.save()
        return success_response(serializer.data, request=request)

    @swagger_auto_schema(
        operation_id='app教练删除项目笔记',
        operation_summary='app教练删除项目笔记',
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'project_note_id': openapi.Schema(type=openapi.TYPE_NUMBER, description='项目笔记id'),
            }
        ),
        tags=['app教练项目笔记']
    )
    @action(methods=['post'], detail=False, url_path='delete')
    def project_note_delete(self, request, *args, **kwargs):
        try:
            instance = ProjectNote.objects.get(pk=request.data.get('project_note_id'), deleted=False)
        except ProjectNote.DoesNotExist:
            return parameter_error_response('项目笔记不存在')
        instance.deleted = True
        instance.save()
        return success_response(request=request)


