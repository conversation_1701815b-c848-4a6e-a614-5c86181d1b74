import datetime

from django.db import transaction
from drf_yasg import openapi
from rest_framework.decorators import action
from drf_yasg.utils import swagger_auto_schema
from rest_framework.viewsets import GenericViewSet

from utils.api_response import success_response, parameter_error_response, biz_error_response, WisdomValidationError
from utils.lbi_evaluation_action import lbi_evaluation
from utils.task import save_lbi_evaluation, generate_manage_project_report
from wisdom_v2.common import evaluation_public
from wisdom_v2.enum.service_content_enum import EvaluationReportTypeEnum
from wisdom_v2.enum.user_enum import UserTmpEnum

from wisdom_v2.views.constant import ATTR_TYPE_EVALUATION_REPORT, MANAGE_EVALUATION, LBI_EVALUATION
from .app_evaluation_report_action import coach_prepare
from wisdom_v2.models import Evaluation, User, EvaluationAnswer, PublicAttr, Project, EvaluationOption, \
    EvaluationReport, EvaluationModule, ProjectInterested, UserTmp
from rest_framework import serializers

from wisdom_v2.views.constant import ATTR_TYPE_EVALUATION_ANSWER, BIZ_CODE_EVALUATION_REPORT_COUNT_LIMIT


class EvaluationAnswerSerializers(serializers.ModelSerializer):
    created_at = serializers.DateTimeField(format='%Y-%m-%d %H:%M')

    class Meta:
        model = EvaluationAnswer
        exclude = ('updated_at')


class EvaluationAnswerViewSet(GenericViewSet):
    queryset = EvaluationAnswer.objects.all()
    serializer_class = EvaluationAnswerSerializers

    @swagger_auto_schema(
        operation_id='测评回答',
        operation_summary='测评回答',
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'evaluation_id': openapi.Schema(type=openapi.TYPE_NUMBER, description='测评id'),
                'user_id': openapi.Schema(type=openapi.TYPE_NUMBER, description='用户id'),
                'project_id': openapi.Schema(type=openapi.TYPE_NUMBER, description='项目id'),
                'option': openapi.Schema(type=openapi.TYPE_ARRAY, description='回答选项',
                                         items=openapi.Schema(type=openapi.TYPE_OBJECT)),
            }
        ),
        tags=['app测评']
    )
    @action(methods=['post'], detail=False, url_path='add', authentication_classes=[])
    def add(self, request):

        try:
            evaluation_id = int(request.data.get('evaluation_id', 0))
            project_id = int(request.data.get('project_id', 0))
            user_id = int(request.data.get('user_id', 0))
            option_list = request.data.get('option')
            user = User.objects.get(pk=user_id)
            project = Project.objects.filter(pk=project_id, deleted=False).first()

            other_user_id = int(request.data.get('other_user_id', 0))
            is_fill_in_all = False
            if not project:
                return parameter_error_response('项目不存在')
            evaluation = Evaluation.objects.filter(pk=evaluation_id).first()
            if not evaluation:
                return parameter_error_response('测评不存在')
        except Exception:
            return parameter_error_response('请求参数错误')

        evaluation_report_config = evaluation.evaluation_report_config.filter(deleted=False).first()
        if not evaluation_report_config:
            return parameter_error_response('测评未配置报告模板')

        try:
            with transaction.atomic():
                if evaluation.code == MANAGE_EVALUATION:
                    report_count = EvaluationReport.objects.filter(
                        deleted=False,
                        public_attr__type=ATTR_TYPE_EVALUATION_REPORT,
                        public_attr__project_id=project_id,
                        public_attr__user_id=user_id,
                        evaluation_id=evaluation_id).count()
                    times = EvaluationModule.objects.filter(
                        deleted=False,
                        evaluation_id=evaluation_id,
                        project_bundle__project_id=project_id,
                        project_bundle__project_member__user_id=user_id,
                    ).count()
                    if not times or times - report_count <= 0:
                        return biz_error_response(
                            BIZ_CODE_EVALUATION_REPORT_COUNT_LIMIT, '超过测评最大次数:{}'.format(
                                request.data))
                    public_attr = PublicAttr.objects.create(
                        project=project,
                        user=user,
                        type=ATTR_TYPE_EVALUATION_ANSWER)
                elif evaluation.code == LBI_EVALUATION:
                    public_attr = PublicAttr.objects.filter(
                        project=project,
                        user_id=other_user_id if other_user_id else user.id,
                        target_user=user,
                        type=ATTR_TYPE_EVALUATION_ANSWER).first()
                    if public_attr:
                        return biz_error_response(
                            BIZ_CODE_EVALUATION_REPORT_COUNT_LIMIT, '超过测评最大次数:{}'.format(
                                request.data))
                    public_attr = PublicAttr.objects.create(
                        project=project,
                        user_id=other_user_id if other_user_id else user.id,
                        target_user=user,
                        type=ATTR_TYPE_EVALUATION_ANSWER)
                else:
                    return parameter_error_response('测评编号错误')

                for option in option_list:
                    option_instance = EvaluationOption.objects.get(pk=option.get('id'))
                    EvaluationAnswer.objects.create(public_attr=public_attr,
                                                    option_id=option_instance.id, answer=option.get('answer', None))

                report_id = None
                evaluation_module = EvaluationModule.objects.filter(
                    evaluation_id=evaluation_id,
                    project_bundle__project_id=project_id,
                    project_bundle__project_member__user_id=user_id,
                    deleted=False
                ).first()
                if evaluation.code == MANAGE_EVALUATION:
                    report_id = coach_prepare(evaluation, public_attr)
                    generate_manage_project_report.apply_async(
                        kwargs=dict(project_id=project_id), countdown=5, expires=120)
                elif evaluation.code == LBI_EVALUATION:

                    public_attr = PublicAttr.objects.filter(
                        project=project, user=user, target_user=user, type=ATTR_TYPE_EVALUATION_ANSWER).first()
                    if public_attr:  # 有自评，生成报告
                        if evaluation_report_config.type == EvaluationReportTypeEnum.lbi_self_evaluation_report.value:
                            state, msg = lbi_evaluation(evaluation, public_attr)
                            if not state:
                                raise WisdomValidationError(msg)
                            report_id = msg
                            is_fill_in_all = True

                        elif evaluation_report_config.type == EvaluationReportTypeEnum.lbi_personal_report.value:
                            other_users = ProjectInterested.objects.filter(
                                project_id=public_attr.project.pk,
                                deleted=False,
                                master_id=public_attr.target_user.id).count()
                            if other_users:
                                report, is_expired = evaluation_public.check_report_and_expiration(
                                    evaluation, user, project, evaluation_module)
                                report_id = report.id if report else None
                                if report or (not report_id and is_expired):
                                    if other_user_id != user_id:
                                        save_lbi_evaluation.delay(evaluation, public_attr)
                                    else:
                                        state, msg = lbi_evaluation(evaluation, public_attr)
                                        if not state:
                                            raise WisdomValidationError(msg)
                                        report_id = msg
                                    is_fill_in_all = True
                        else:
                            raise WisdomValidationError('未找到该测评类型报告')
                data = {'evaluation_report_id': report_id} if report_id else {}
                if report_id or is_fill_in_all:
                    evaluation_module.is_submit = 1
                    evaluation_module.submit_time = datetime.datetime.now()
                    evaluation_module.save()

                UserTmp.objects.filter(
                    data_id=evaluation_id,
                    extra_id=user.id,
                    user_id=other_user_id if other_user_id else user.id,
                    type=UserTmpEnum.evaluation,
                ).delete()
                data['evaluation_report_type'] = evaluation_report_config.type if evaluation_report_config else None
                return success_response(request=request, data=data)
        except Exception as e:
            return parameter_error_response(str(e))
