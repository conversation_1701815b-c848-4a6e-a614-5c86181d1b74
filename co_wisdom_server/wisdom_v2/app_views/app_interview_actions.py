import datetime
from decimal import Decimal

import redis

from django.db.models import Q
from django.utils import timezone
from django.conf import settings
from rest_framework import serializers


from utils.message.lark_message import LarkMessageCenter
from utils.miniapp_version_judge import compare_version
from utils.send_account_email import get_project_manage_qr_code, get_project_manage_wx_user_id
from wisdom_v2.app_views.app_coach_actions import CoachTaskSerializer

from wisdom_v2.common import coach_public, stakeholder_interview_public, interview_public, order_public
from wisdom_v2.enum.business_order_enum import BusinessOrderWithdrawalStatusEnum, WorkTypeEnum, BusinessOrderSettlementStatusEnum
from wisdom_v2.enum.pay_enum import OrderStatusEnum
from wisdom_v2.models import ProjectInterview, PublicAttr, User, ProjectInterviewRecord, ProjectMember, CapacityTag, \
    ProjectInterviewOffLineRecord, Company, InterviewRecordTemplateOption, \
    InterviewRecordTemplateAnswer, InterviewRecordTemplateQuestion, Interview<PERSON><PERSON>ordTemplate, ActionPlan, Habit, Diary, \
    CoachAppraise, PowerTag, RelatedQuestion, TotalTemplate, TotalTemplateReport, CoachTask, WorkWechatUser, Coach, \
    Resume, CustomerPortrait, Schedule, Order, ProjectNote
from wisdom_v2.models_file import BusinessOrder2Object, InterviewMeeting
from wisdom_v2.utils import get_interview_record_detail
from wisdom_v2.views.constant import ATTR_STATUS_INTERVIEW_CANCEL, BUNDLE_INTEWVIEW_TYPE_OFFLINE_GROUP
from wisdom_v2.app_views.app_interview_record_actions import AppInterviewRecordDetailSerializers
from wisdom_v2.app_views.app_interview_offline_record import ProjectInterviewOffLineRecordSerializers

from wisdom_v2.enum.service_content_enum import InterviewRecordTemplateQuestionTypeEnum, \
    InterviewRecordTemplateQuestionAnswerTypeEnum, InterviewRecordTemplateRoleEnum, CustomerPortraitTypeEnum, \
    InterviewRecordTemplateTypeEnum, ScheduleTypeEnum
from wisdom_v2.views.interview_question_actions import InterviewRecordTemplateQuestionSerializers
from django.db.models import Case, When
from wisdom_v2.enum.project_interview_enum import ObjectTypeEnum, \
    InterviewListStatusEnum, TodoListStatusEnum, InterviewMessageColorEnum, InterviewDetailHeadEnum, \
    InterviewDetailContentEnum, InterviewDetailButtonEnum, TotalTemplateTypeEnum, \
    TemplateTypeEnum, DataType, ProjectInterviewTypeEnum, ProjectInterviewPlaceCategoryEnum, GroupCoachTypeEnum, \
    AppInterviewListTypeEnum, InterviewSubjectEnum, ProjectInterviewPlaceTypeEnum, InterviewMeetingChannelTypeEnum
from wisdom_v2.enum.service_content_enum import PowerTagEnum
from wisdom_v2.enum.user_enum import UserRoleEnum
from wisdom_v2.views.constant import ATTR_TYPE_INTERVIEW, INTERVIEW_TYPE_COACHING
from wisdom_v2.enum.project_interview_enum import InterviewRecordTypeEnum

third_party_redis = redis.Redis.from_url(settings.THIRD_PARTY_DATA_REDIS)

def get_interview_tag(interview):
    if interview.place_category == BUNDLE_INTEWVIEW_TYPE_OFFLINE_GROUP:
        return CapacityTag.objects.filter(interview=interview, deleted=False).values_list('title', flat=True) or []
    else:
        return []





def time_hour(start_time, end_time, user_id, project_id):
    project_member = ProjectMember.objects.filter(project_id=project_id, deleted=False,
                                                  user_id=user_id,
                                                  one_interview_time__isnull=False
                                                  ).exclude(one_interview_time=-1)
    start_time = datetime.datetime.strptime(start_time, '%Y-%m-%d %H:%M')
    end_time = datetime.datetime.strptime(end_time, '%Y-%m-%d %H:%M')
    time_delta = end_time - start_time
    if project_member.first() and project_member.first().one_interview_time < time_delta.seconds / 60:
        return True, round(time_delta.seconds / 60, 1)
    return False, round(time_delta.seconds / 60)


def get_current_week(start_time):
    start_time = datetime.datetime.strptime(start_time, '%Y-%m-%d %H:%M')
    monday, sunday = start_time.replace(hour=0, minute=0), start_time.replace(hour=23, minute=59)
    one_day = datetime.timedelta(days=1)
    while monday.weekday() != 0:
        monday -= one_day
    while sunday.weekday() != 6:
        sunday += one_day
    return monday, sunday


def check_week_count(user_id, project_id, start_time):
    project_member = ProjectMember.objects.filter(project_id=project_id, deleted=False,
                                                  user_id=user_id,
                                                  week_interview_count__isnull=False
                                                  ).exclude(week_interview_count=-1)
    if project_member.exists():
        monday, sunday = get_current_week(start_time)
        week_count = PublicAttr.objects.filter(type=ATTR_TYPE_INTERVIEW,
                                               target_user_id=user_id,
                                               start_time__range=[monday, sunday]
                                               ).exclude(status=ATTR_STATUS_INTERVIEW_CANCEL).count()
        if project_member.first().week_interview_count <= week_count:
            return True


def get_interview_coach(interview_id, role=0):
    interview = ProjectInterview.objects.get(pk=interview_id)
    user = User.objects.get(pk=interview.public_attr.user_id)
    work_wechat_user = WorkWechatUser.objects.filter(wx_user_id__isnull=False,user=user, deleted=False).first()
    coach_user = Coach.objects.filter(user=user, deleted=False).first()
    if coach_user and role == UserRoleEnum.trainee_coachee:
        true_name = coach_user.personal_name if coach_user.personal_name else user.cover_name
    else:
        true_name = user.cover_name
    state, resume = coach_public.get_project_coach_resume(user.id, interview.public_attr.project_id)
    if state and resume.head_image_url:
        head_image_url = resume.head_image_url
    else:
        resume = Resume.objects.filter(coach=coach_user, deleted=False, is_customization=False).first()
        head_image_url = resume.head_image_url if resume.head_image_url else user.head_image_url
    return {
        'id': user.pk,
        'name': true_name,
        'head_image_url': head_image_url,
        'coach_work_wechat_qrcode_url': work_wechat_user.qr_code if work_wechat_user else None,
        'price': coach_user.display_price if coach_user else None,
        'resume_id': resume.id if resume else None
        }
        


def get_interview_coachee(interview_id):
    project_interview = ProjectInterview.objects.get(pk=interview_id)

    public_attr = project_interview.public_attr

    # 查到教练和用户
    coachee_user = public_attr.target_user
    coach_user = public_attr.user

    # 默认没加好友
    is_relevance = False
    external_user_id = None

    # 查找到用户企业微信外部联系人id和教练企业微信id
    work_wechat_coachee_user = WorkWechatUser.objects.filter(external_user_id__isnull=False, user=coachee_user, deleted=False).first()
    work_wechat_coach_user = WorkWechatUser.objects.filter(wx_user_id__isnull=False, user=coach_user, deleted=False).first()
    if work_wechat_coach_user:
        is_relevance, external_user_id = coach_public.work_wechat_coach_customer_info(
            work_wechat_coach_user, work_wechat_coachee_user, coachee_user)

    coachee_name = interview_public.get_interview_coachee_nickname(project_interview)
    return {
        'id': coachee_user.pk,
        'name': coachee_name,
        'head_image_url': coachee_user.head_image_url,
        'external_userid': external_user_id,
        'phone': coachee_user.phone,
        'is_relevance_work_wechat_coach': is_relevance
    }


def get_time_slot(start_time):
    now_time = timezone.now()
    res_time = start_time - now_time
    days = res_time.days
    hours = int(res_time.seconds / 60 / 60)
    minutes = float(res_time.seconds / 60)
    return days, hours, minutes


class AppInterviewDetailSerializers(serializers.ModelSerializer):
    coach_info = serializers.SerializerMethodField()
    coachee_info = serializers.SerializerMethodField()
    start_time = serializers.DateTimeField(source='public_attr.start_time', format='%Y-%m-%d %H:%M')
    end_time = serializers.DateTimeField(source='public_attr.end_time', format='%Y-%m-%d %H:%M')
    status = serializers.IntegerField(source='public_attr.status')
    project_id = serializers.IntegerField(source='public_attr.project_id')
    record = serializers.SerializerMethodField()
    capacity_tag = serializers.SerializerMethodField()
    detail_status = serializers.SerializerMethodField()
    record_type = serializers.IntegerField()
    isAppraise = serializers.SerializerMethodField(help_text='是否评价')
    record_detail = serializers.SerializerMethodField()
    topic = serializers.SerializerMethodField()
    state_describe = serializers.SerializerMethodField()
    coaching_status = serializers.SerializerMethodField()
    fillin_status = serializers.SerializerMethodField()
    detail_text = serializers.SerializerMethodField()
    real_income = serializers.SerializerMethodField(help_text='实际收入')
    pay_amount = serializers.SerializerMethodField(help_text='支付金额')
    service_charge = serializers.SerializerMethodField(help_text='平台服务费')
    tax_amount = serializers.SerializerMethodField(help_text='税费')
    discount_amount = serializers.SerializerMethodField(help_text='代金劵价格')
    original_price = serializers.SerializerMethodField(help_text='原价')
    stakeholder = serializers.SerializerMethodField(help_text='利益相关者访谈信息')
    activity = serializers.SerializerMethodField(help_text='活动信息')
    personal_activity = serializers.SerializerMethodField(help_text='教练活动信息')
    group_coach_type = serializers.SerializerMethodField(help_text='集体辅导类型')
    prompt = serializers.SerializerMethodField(help_text='提示信息')
    meeting_info = serializers.SerializerMethodField(help_text='会议信息')
    interview_type_describe = serializers.SerializerMethodField(help_text='辅导类型描述')
    place = serializers.SerializerMethodField(help_text='辅导地址')

    class Meta:
        model = ProjectInterview
        fields = (
            'id', 'topic', 'coach_info', 'coachee_info', 'start_time', 'end_time', 'place_category', 'place', 'status',
            'record', 'capacity_tag', 'detail_status', 'record_type', 'isAppraise', 'record_detail', 'is_coach_agree',
            'state_describe', 'project_id', 'close_reason', 'fillin_status', 'coaching_status', 'type', 'detail_text',
            'real_income', 'pay_amount', 'stakeholder', 'activity', 'group_coach_type', 'prompt', 'personal_activity',
            'service_charge', 'discount_amount', 'original_price', 'tax_amount', 'meeting_info', 'place', 'place_type',
            'interview_subject', 'interview_type_describe'
        )

    # 初始化缓存
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self._cache = {}

    # 计算或检索订单详情缓存
    def calculate_order_details(self, obj):
        if 'order_details' not in self._cache:
            if obj.order:
                order_details = coach_public.calculate_coach_income_details(obj)
                # 数据库单位是分，展示时改为元
                order_details = [Decimal(str(item)) / Decimal('100').quantize(Decimal('0.00'))for item in order_details]
                self._cache['order_details'] = order_details
            else:
                self._cache['order_details'] = [None, None, None, None, None]  # 默认值
        return self._cache['order_details']

    def get_place(self, obj):
        # 没有设置地址，就显示腾讯会议号
        display_meeting = not obj.place or obj.place == '腾讯会议'
        if display_meeting and obj.place_type == ProjectInterviewPlaceTypeEnum.online.value:
            meeting = InterviewMeeting.objects.filter(interview=obj, deleted=False).first()
            if meeting:
                return '腾讯会议号： ' + meeting.meeting_code
            return '线上视频会议'
        return obj.place

    def get_interview_type_describe(self, obj):
        return interview_public.get_interview_type_describe(obj)

    def get_meeting_info(self, obj):
        return interview_public.get_interview_meeting_info(obj)

    def get_prompt(self, obj):
        return interview_public.get_interview_prompt(obj, self.context.get('user_role'))

    def get_group_coach_type(self, obj):
        return interview_public.get_group_coach_type(obj)

    def get_activity(self, obj):
        return interview_public.get_activity_data(obj)

    def get_personal_activity(self, obj):
        return interview_public.get_personal_activity_data(obj)

    def get_service_charge(self, obj):
        if obj.order:
            mp = self.context.get('mp')
            service_scale, real_income, tax_amount, service_charge, tax_point = self.calculate_order_details(obj)
            if mp and compare_version(mp.get('version'), '2.31') < 0:
                return service_charge + tax_amount
            return service_charge
        return

    def get_discount_amount(self, obj):
        if obj.order:
            return interview_public.get_interview_to_order_discount_amount(obj)

    def get_original_price(self, obj):
        if obj.order:
            return interview_public.get_interview_to_order_original_price(obj)

    def get_real_income(self, obj):
        if obj.order:
            service_scale, real_income, tax_amount, service_charge, tax_point = self.calculate_order_details(obj)
            return real_income
        return

    def get_tax_amount(self, obj):
        service_scale, real_income, tax_amount, service_charge, tax_point = self.calculate_order_details(obj)
        return tax_amount

    def get_pay_amount(self, obj):
        if obj.order:
            return interview_public.get_interview_to_order_original_price(obj) - interview_public.get_interview_to_order_discount_amount(obj)

    def get_detail_text(self, obj):
        role = int(self.context.get('user_role', 0))
        return detail_text(role, obj.pk)

    def get_fillin_status(self, obj):
        return obj.get_fillin_status

    def get_coaching_status(self, obj):
        return obj.get_coaching_status

    def get_state_describe(self, obj):
        role = int(self.context.get('user_role', 0))
        return get_interview_state_describe(role, obj)

    def get_topic(self, obj):
        return interview_public.get_interview_topic(self.context.get('user_role'), obj)

    def get_record_detail(self, obj):
        if not obj.coach_record_status and not obj.coachee_record_status:
            return []
        return get_interview_record_detail(obj)

    def get_isAppraise(self, obj):

        # 问卷模式的教练评价和问答模式的取值不一样
        if obj.record_type == InterviewRecordTypeEnum.question_and_answer.value:
            if ProjectInterviewRecord.objects.filter(deleted=False, interview=obj, coach_score__isnull=False).exists():
                return 1
        elif obj.record_type == InterviewRecordTypeEnum.questionnaire.value:
            if CoachAppraise.objects.filter(interview=obj, deleted=False).exists():
                return 1
        return 0


    def get_capacity_tag(self, obj):
        return get_interview_tag(obj)

    def get_coach_info(self, obj):
        role = int(self.context.get('user_role', 0))
        return get_interview_coach(obj.pk, role)

    def get_coachee_info(self, obj):
        return get_interview_coachee(obj.pk)

    def get_record(self, obj):
        data = None
        try:
            if obj.place_category == BUNDLE_INTEWVIEW_TYPE_OFFLINE_GROUP:
                record = ProjectInterviewOffLineRecord.objects.filter(interview=obj, deleted=False).first()
                if record:
                    data = ProjectInterviewOffLineRecordSerializers(record).data
            else:
                if obj.type == ProjectInterviewTypeEnum.formal_interview and \
                    obj.record_type == InterviewRecordTypeEnum.question_and_answer:
                    record = ProjectInterviewRecord.objects.filter(interview=obj, deleted=False).first()
                    if record:
                        data = AppInterviewRecordDetailSerializers(record).data
            return data
        except Exception as e:
            LarkMessageCenter().send_other_backend_message('接口错误AppInterviewDetailSerializers.get_record', 'error',
                                                           f'content:{str(e)}')
            return

    def get_detail_status(self, obj):
        role = int(self.context['user_role'])
        mp = self.context.get('mp')
        is_coach = True if int(role) in [UserRoleEnum.coach.value, UserRoleEnum.trainee_coach.value] else False

        # 高于2.33版本，问卷模式一对一辅导，拆成辅导详情和辅导记录页面
        if (mp and compare_version(mp.get('version'), '2.33') >= 0 and
                obj.type == ProjectInterviewTypeEnum.formal_interview.value and
                obj.place_category == ProjectInterviewPlaceCategoryEnum.online_one_to_one.value and
                obj.record_type == InterviewRecordTypeEnum.questionnaire.value):

            result = interview_public.get_one_to_one_interview_detail_status(obj, is_coach)

        # 高于2.36版本，化学面谈一对一辅导，拆成辅导详情和辅导记录页面
        elif (mp and compare_version(mp.get('version'), '2.36') >= 0 and
                obj.type == ProjectInterviewTypeEnum.chemical_interview.value and
                obj.place_category == ProjectInterviewPlaceCategoryEnum.online_one_to_one.value):
            result = interview_public.get_chemical_interview_detail_status(obj, is_coach)

        # 高于2.36版本，利益相关者访谈一对一辅导，拆成辅导详情和辅导记录页面
        elif (mp and compare_version(mp.get('version'), '2.36') >= 0 and
                obj.type == ProjectInterviewTypeEnum.stakeholder_interview.value and
                obj.place_category == ProjectInterviewPlaceCategoryEnum.online_one_to_one.value):
            result = interview_public.get_stakeholder_interview_detail_status(obj, is_coach)

        else:
            result = detail_status(role, obj.pk)

        return result

    def get_stakeholder(self, obj):
        data = stakeholder_interview_public.get_stakeholder_interview_detail_data(obj)
        return data


class AppStakeholderInterviewDetailSerializer(serializers.ModelSerializer):
    coach_info = serializers.SerializerMethodField()
    start_time = serializers.DateTimeField(source='public_attr.start_time', format='%Y-%m-%d %H:%M')
    end_time = serializers.DateTimeField(source='public_attr.end_time', format='%Y-%m-%d %H:%M')
    status = serializers.IntegerField(source='public_attr.status')
    project_id = serializers.IntegerField(source='public_attr.project_id')
    detail_status = serializers.SerializerMethodField()
    record_type = serializers.IntegerField()
    topic = serializers.CharField(help_text='辅导议题')
    state_describe = serializers.SerializerMethodField()
    coaching_status = serializers.SerializerMethodField()
    fillin_status = serializers.SerializerMethodField()
    detail_text = serializers.SerializerMethodField()
    stakeholder = serializers.SerializerMethodField(help_text='利益相关者访谈信息')
    meeting_info = serializers.SerializerMethodField(help_text='会议详情')

    class Meta:
        model = ProjectInterview
        fields = (
            'id', 'topic', 'coach_info', 'start_time', 'end_time', 'place_category', 'place', 'status',
            'detail_status', 'record_type', 'is_coach_agree', 'state_describe', 'project_id', 'close_reason',
            'fillin_status', 'coaching_status', 'type', 'detail_text', 'stakeholder', 'meeting_info', 'place',
            'place_type', 'interview_subject')

    def get_meeting_info(self, obj):
        return interview_public.get_interview_meeting_info(obj)

    def get_detail_text(self, obj):
        role = int(self.context.get('user_role', 0))
        return detail_text(role, obj.pk)

    def get_fillin_status(self, obj):
        return obj.get_fillin_status

    def get_coaching_status(self, obj):
        return obj.get_coaching_status

    def get_state_describe(self, obj):
        return get_interview_state_describe(0, obj)

    def get_coach_info(self, obj):
        return get_interview_coach(obj.pk, role=0)

    def get_detail_status(self, obj):
        mp = self.context.get('mp')
        # 高于2.36版本，利益相关者访谈一对一辅导，拆成辅导详情和辅导记录页面
        if (mp and compare_version(mp.get('version'), '2.36') >= 0 and
                obj.type == ProjectInterviewTypeEnum.stakeholder_interview.value and
                obj.place_category == ProjectInterviewPlaceCategoryEnum.online_one_to_one.value):
            result = interview_public.get_stakeholder_interview_detail_status(obj, is_coach=False)
        else:
            result = detail_status(0, obj.pk)
        return result

    def get_stakeholder(self, obj):
        data = stakeholder_interview_public.get_stakeholder_interview_detail_data(obj)
        return data


class AppInterviewListSerializers(serializers.ModelSerializer):
    coach_info = serializers.SerializerMethodField()
    coachee_info = serializers.SerializerMethodField()
    start_time = serializers.DateTimeField(source='public_attr.start_time', format='%Y-%m-%d %H:%M')
    end_time = serializers.DateTimeField(source='public_attr.end_time', format='%Y-%m-%d %H:%M')
    status = serializers.IntegerField(source='public_attr.status')
    project_id = serializers.IntegerField(source='public_attr.project_id')
    date = serializers.SerializerMethodField()
    status_text = serializers.SerializerMethodField()
    capacity_tag = serializers.SerializerMethodField()
    company_info = serializers.SerializerMethodField()
    record_type = serializers.IntegerField()
    topic = serializers.SerializerMethodField()
    coaching_status = serializers.SerializerMethodField()
    fillin_status = serializers.SerializerMethodField()
    is_show_withdraw = serializers.SerializerMethodField()
    group_coach_type = serializers.SerializerMethodField()
    prompt = serializers.SerializerMethodField()
    discount_amount = serializers.SerializerMethodField(help_text="代金劵价格")
    pay_amount = serializers.SerializerMethodField(help_text='支付金额')
    data_type = serializers.SerializerMethodField(help_text='数据类型')
    meeting_info = serializers.SerializerMethodField(help_text='会议信息')
    interview_type_describe = serializers.SerializerMethodField(help_text='辅导类型描述')

    class Meta:
        model = ProjectInterview
        fields = ('id', 'topic', 'coach_info', 'coachee_info', 'interview_number', 'date', 'times', 'status',
                  'place_category', 'status_text', 'start_time', 'end_time', 'coach_record_status', 'discount_amount',
                  'coachee_record_status', 'capacity_tag', 'place', 'company_info', 'record_type', 'pay_amount',
                  'project_id', 'is_coach_agree', 'close_reason', 'fillin_status', 'coaching_status', 'type',
                  'is_show_withdraw', 'group_coach_type', 'prompt', 'data_type', 'meeting_info', 'place', 'place_type',
                  'interview_subject', 'interview_type_describe')
        
    def get_interview_type_describe(self, obj):
        return interview_public.get_interview_type_describe(obj)

    def get_meeting_info(self, obj):
        return interview_public.get_interview_meeting_info(obj)

    def get_data_type(self,  obj):
        return AppInterviewListTypeEnum.interview.value

    def get_prompt(self, obj):
        return interview_public.get_interview_prompt(obj, self.context.get('user_role'))

    def get_group_coach_type(self, obj):
        return interview_public.get_group_coach_type(obj)

    def get_is_show_withdraw(self, obj):
        # 控制辅导列表的提现按钮是否展示
        role = self.context.get('user_role')
        if role not in [UserRoleEnum.coach.value, UserRoleEnum.trainee_coach.value]:
            return False
        if obj.type in [ProjectInterviewTypeEnum.chemical_interview, ProjectInterviewTypeEnum.enterprise_interview]:
            return False
        if obj.activity_interview.filter(deleted=False).exists():
            return False
        if obj.public_attr.end_time < datetime.datetime.now():
            if obj.place_category == ProjectInterviewPlaceCategoryEnum.online_one_to_one and obj.coach_record_status:
                type = WorkTypeEnum.one_to_one if obj.type == ProjectInterviewTypeEnum.formal_interview \
                    else WorkTypeEnum.stakeholder_interview
                business_order2object = BusinessOrder2Object.objects.filter(
                    object_id=obj.id, type=type, deleted=False).first()
                if business_order2object:
                    business_order = business_order2object.business_order
                    if business_order.settlement_status == BusinessOrderSettlementStatusEnum.settled:
                        return False
                    # 可以提现，但是金额为0，也不用显示按钮
                    if business_order.coach_actual_income == 0 and business_order.withdrawal_status == BusinessOrderWithdrawalStatusEnum.can_withdraw.value:
                        return False
            elif obj.place_category == ProjectInterviewPlaceCategoryEnum.offline_group_coach:
                group_coach = obj.coach_group_module.filter(deleted=False).first()
                if group_coach:
                    project_group_coach = group_coach.project_group_coach
                    type = WorkTypeEnum.group_coach \
                        if project_group_coach.type == GroupCoachTypeEnum.collective_tutoring else WorkTypeEnum.group_counseling

                    business_order2object = BusinessOrder2Object.objects.filter(
                        object_id=project_group_coach.id, type=type, deleted=False).first()
                    if business_order2object:
                        business_order = business_order2object.business_order
                        if business_order.settlement_status == BusinessOrderSettlementStatusEnum.settled:
                            return False
                        if business_order.coach_actual_income == 0:
                            return False
        return True

    def get_fillin_status(self, obj):
        return obj.get_fillin_status

    def get_coaching_status(self, obj):
        return obj.get_coaching_status

    def get_discount_amount(self, obj):
        if obj.order:
            return interview_public.get_interview_to_order_discount_amount(obj)

    def get_pay_amount(self, obj):
        if obj.order:
            return interview_public.get_interview_to_order_original_price(obj) - interview_public.get_interview_to_order_discount_amount(obj)

    def get_topic(self, obj):
        return interview_public.get_interview_topic(self.context.get('user_role'), obj)

    def get_company_info(self, obj):
        now = datetime.datetime.now()
        if (obj.place_category == BUNDLE_INTEWVIEW_TYPE_OFFLINE_GROUP and
            obj.public_attr.status !=ATTR_STATUS_INTERVIEW_CANCEL and obj.public_attr.start_time > now) \
            or (obj.place_category == BUNDLE_INTEWVIEW_TYPE_OFFLINE_GROUP and
                obj.public_attr.start_time < now < obj.public_attr.end_time and obj.public_attr.status != ATTR_STATUS_INTERVIEW_CANCEL):
            try:
                company = Company.objects.get(pk=obj.public_attr.project.company_id)
                return {'company_name': company.short, 'company_logo': company.logo}
            except Exception as e:
                LarkMessageCenter().send_other_backend_message('接口错误AppInterviewListSerializers.get_company_info', 'error',
                                                               f'content:{str(e)}')
                return

    def get_capacity_tag(self, obj):
        return get_interview_tag(obj)

    def get_coachee_info(self, obj):
        coachee_user = obj.public_attr.target_user
        coachee_name = interview_public.get_interview_coachee_nickname(obj)
        data = {
            'id': coachee_user.pk,
            'name': coachee_name,
            'head_image_url': coachee_user.head_image_url,
            'phone': coachee_user.phone,
        }
        return data

    def get_coach_info(self, obj):
        role = self.context.get('user_role')
        return get_interview_coach(obj.pk, role)

    def get_status_text(self, obj):
        """
            # coach端
            # 线上1对1辅导 辅导状态为未开始 显示“距开始还有XX小时    ✅
            # 线上1对1辅导 辅导状态为进行中 显示“辅导已开始”   ✅
            # 线上1对1辅导 辅导状态为完成，且辅导记录状态为双方未完成 显示“辅导还未完成记录”   ✅
            # 线上1对1辅导 辅导状态为完成，且辅导记录状态为教练未完成 显示“辅导还未完成记录”    ✅
            # 线上1对1辅导 辅导状态为完成，且辅导记录状态为被教练者未完成 显示“对方未完成记录”   ✅
            # 线上1对1辅导 辅导状态为完成，且辅导记录状态为完成 显示“本次辅导已结束” ✅
            # 线下集体辅导 辅导状态为未开始 显示“本次辅导距离开始还有XX小时”  ✅
            # 线下集体辅导 辅导状态为进行中 显示“本次辅导已开始”  ✅
            # 线下集体辅导 辅导状态为完成，且辅导记录状态为被教练者未完成 显示“对方未完成记录”  ✅
            # 线下集体辅导 辅导状态为完成，且辅导记录状态为完成 显示“本次辅导已结束”  ✅

            # coachee端
            # 线上1对1辅导 辅导状态为未开始 显示“距开始还有XX小时  ✅
            # 线上1对1辅导 辅导状态为进行中 显示“辅导已开始”   ✅
            # 线上1对1辅导 辅导状态为完成，且辅导记录状态为双方未完成或被教练者未完成 显示“辅导还未完成记录”✅
            # 线上1对1辅导 辅导状态为完成，且辅导记录状态为教练未完成 显示“对方未完成记录”   ✅
            # 线上1对1辅导 辅导状态为完成，且辅导记录状态为完成 显示“本次辅导已结束”  ✅
            # 线下集体辅导 辅导状态为未开始 显示“本次辅导距离开始还有XX小时”  ✅
            # 线下集体辅导 辅导状态为进行中 显示“本次辅导已开始”  ✅
            # 线下集体辅导 辅导状态为完成，且辅导记录状态为完成 显示“本次辅导已结束”  ✅
            # 线下集体辅导 辅导状态未完成，且辅导记录状态为被教练者未完成 显示“辅导还未完成记录”  ✅
        """
        # 9 填写反馈  10查看反馈
        days, hours, minutes = get_time_slot(obj.public_attr.start_time)
        if obj.public_attr.status == ATTR_STATUS_INTERVIEW_CANCEL:
            if obj.record_type == InterviewRecordTypeEnum.question_and_answer:
                return {'status': InterviewListStatusEnum.answer_and_question_detail.value,
                        'msg': '已取消', 'color': InterviewMessageColorEnum.grey.value}
            else:
                return {'status': InterviewListStatusEnum.wait_agree_questionnaire_detail.value,
                        'msg': '已取消', 'color': InterviewMessageColorEnum.grey.value}
        if not obj.is_coach_agree:
            if self.context['user_role'] in [UserRoleEnum.coach.value, UserRoleEnum.trainee_coach.value]:
                return {'status': InterviewListStatusEnum.wait_agree_questionnaire_detail.value,
                        'msg': '待确认', 'color': InterviewMessageColorEnum.purple.value}
            else:
                return {'status': InterviewListStatusEnum.wait_agree_questionnaire_detail.value,
                        'msg': '教练确认中', 'color': InterviewMessageColorEnum.purple.value}

        if obj.place_category != BUNDLE_INTEWVIEW_TYPE_OFFLINE_GROUP:  # 线上一对一
            #  未开始   当前日期和时间早于辅导开始日期和时间，且未取消
            if obj.public_attr.start_time > datetime.datetime.now() and obj.public_attr.status != ATTR_STATUS_INTERVIEW_CANCEL:  # 未开始
                if days >= 1:
                    if obj.record_type == InterviewRecordTypeEnum.question_and_answer:
                        # 未开始进行中的一对一辅导/化学面谈展示进入会谈按钮点击进入视频页面
                        return {'status': InterviewListStatusEnum.video_link.value,
                                'msg': '距开始还有%s天' % days,
                                'color': InterviewMessageColorEnum.yellow.value}
                    else:
                        # 未开始进行中的一对一辅导/化学面谈展示进入会谈按钮点击进入视频页面
                        return {'status': InterviewListStatusEnum.video_link.value,
                                'msg': '距开始还有%s天' % days,
                                'color': InterviewMessageColorEnum.yellow.value}
                elif days == 0 and hours > 0:
                    if obj.record_type == InterviewRecordTypeEnum.question_and_answer:
                        # 未开始进行中的一对一辅导/化学面谈展示进入会谈按钮点击进入视频页面
                        return {'status': InterviewListStatusEnum.video_link.value,
                                'msg': '距离开始还有%s小时' % hours,
                                'color': InterviewMessageColorEnum.yellow.value}
                    else:
                        # 未开始进行中的一对一辅导/化学面谈展示进入会谈按钮点击进入视频页面
                        return {'status': InterviewListStatusEnum.video_link.value,
                                'msg': '距离开始还有%s小时' % hours,
                                'color': InterviewMessageColorEnum.yellow.value}
                elif days == 0 and hours == 0 and minutes > 0:
                    # 未开始进行中的一对一辅导/化学面谈展示进入会谈按钮点击进入视频页面
                    return {'status': InterviewListStatusEnum.video_link.value,
                            'msg': '距离开始还有%s分钟' % (int(minutes) or 1),
                            'color': InterviewMessageColorEnum.yellow.value}
            # 进行中：当前时间等于晚于开始时间，未取消且不符合辅导结束条件    辅导结束条件： 当前时间等于晚于辅导结束时
            if obj.public_attr.start_time < datetime.datetime.now() < obj.public_attr.end_time and \
                    obj.public_attr.status != ATTR_STATUS_INTERVIEW_CANCEL:
                # 未开始进行中的一对一辅导/化学面谈展示进入会谈按钮点击进入视频页面
                return {'status': InterviewListStatusEnum.video_link.value,
                        'msg': '进行中', 'color': InterviewMessageColorEnum.purple.value}

            # 辅导状态为完成 完成：符合辅导结束条件
            if obj.public_attr.end_time < datetime.datetime.now() and obj.public_attr.status != ATTR_STATUS_INTERVIEW_CANCEL:
                # 辅导记录状态为双方未完成
                if not obj.coachee_record_status and not obj.coach_record_status:
                    if obj.record_type == InterviewRecordTypeEnum.question_and_answer:
                        return {'status': InterviewListStatusEnum.write_answer_and_question_record.value,
                                'msg': '已结束', 'color': InterviewMessageColorEnum.grey.value}
                    else:
                        if obj.type == ProjectInterviewTypeEnum.formal_interview:
                            return {'status': InterviewListStatusEnum.write_questionnaire_record.value,
                                    'msg': '已结束', 'color': InterviewMessageColorEnum.grey.value}
                        elif obj.type == ProjectInterviewTypeEnum.stakeholder_interview:
                            if self.context['user_role'] == UserRoleEnum.coach:
                                return {'status': InterviewListStatusEnum.write_questionnaire_record.value,
                                        'msg': '已结束', 'color': InterviewMessageColorEnum.grey.value}
                            else:
                                # 利益相关者访谈被教练者跳转到问卷形式详情页面
                                return {'status': InterviewListStatusEnum.questionnaire_detail.value,
                                        'msg': '已结束', 'color': InterviewMessageColorEnum.grey.value}
                        else:  # 化学面谈已完成，双方未填写
                            if self.context['user_role'] == UserRoleEnum.coachee:  # 被教练者
                                # 化学面谈已完成，双方未完成的被教练者展示填写反馈按钮点击进入填写反馈页面
                                return {'status': InterviewListStatusEnum.chemical_interview_write_feedback.value,
                                        'msg': '已结束', 'color': InterviewMessageColorEnum.grey.value}
                            else:  # 教练
                                # 化学面谈已完成，双方未完成的教练展示记录收获按钮点击进入写问卷形式辅导记录页面
                                return {'status': InterviewListStatusEnum.write_questionnaire_record.value,
                                        'msg': '已结束', 'color': InterviewMessageColorEnum.grey.value}

                # 辅导记录状态为教练未完成
                if obj.coachee_record_status and not obj.coach_record_status:
                    if obj.record_type == InterviewRecordTypeEnum.question_and_answer:
                        if self.context['user_role'] in [UserRoleEnum.coach.value, UserRoleEnum.trainee_coach.value]:
                            return {'status': InterviewListStatusEnum.write_answer_and_question_record.value,
                                    'msg': '已结束', 'color': InterviewMessageColorEnum.grey.value}
                        else:
                            return {'status': InterviewListStatusEnum.completed_answer_and_question_detail.value,
                                    'msg': '已结束', 'color': InterviewMessageColorEnum.grey.value}
                    else:
                        if obj.type == ProjectInterviewTypeEnum.formal_interview:
                            if self.context['user_role'] in [UserRoleEnum.coach.value,
                                                             UserRoleEnum.trainee_coach.value]:
                                return {'status': InterviewListStatusEnum.write_questionnaire_record.value,
                                        'msg': '已结束', 'color': InterviewMessageColorEnum.grey.value}
                            else:
                                return {'status': InterviewListStatusEnum.completed_questionnaire_detail.value,
                                        'msg': '已结束', 'color': InterviewMessageColorEnum.grey.value}
                        elif obj.type == ProjectInterviewTypeEnum.stakeholder_interview:
                            if self.context['user_role'] == UserRoleEnum.coach:
                                return {'status': InterviewListStatusEnum.write_questionnaire_record.value,
                                        'msg': '已结束', 'color': InterviewMessageColorEnum.grey.value}
                            else:
                                # 利益相关者访谈被教练者跳转到问卷形式详情页面
                                return {'status': InterviewListStatusEnum.questionnaire_detail.value,
                                        'msg': '已结束', 'color': InterviewMessageColorEnum.grey.value}
                        else:  # 化学面谈
                            if self.context['user_role'] == UserRoleEnum.coachee:  # 被教练者
                                # 化学面谈已完成，教练未完成的被教练者展示查看反馈按钮点击进入查看反馈页面
                                return {'status': InterviewListStatusEnum.chemical_interview_feedback_detail.value,
                                        'msg': '已结束', 'color': InterviewMessageColorEnum.grey.value}
                            else:
                                # 化学面谈已完成，教练未完成的教练展示记录收获按钮点击进入写问卷形式辅导记录页面
                                return {'status': InterviewListStatusEnum.write_questionnaire_record.value,
                                        'msg': '已结束', 'color': InterviewMessageColorEnum.grey.value}

                # 且辅导记录状态为被教练者未完成
                if not obj.coachee_record_status and obj.coach_record_status:
                    if obj.record_type == InterviewRecordTypeEnum.question_and_answer:
                        if self.context['user_role'] in [UserRoleEnum.coach.value, UserRoleEnum.trainee_coach.value]:
                            return {'status': InterviewListStatusEnum.completed_answer_and_question_detail.value,
                                    'msg': '已结束', 'color': InterviewMessageColorEnum.grey.value}
                        else:
                            return {'status': InterviewListStatusEnum.write_answer_and_question_record.value,
                                    'msg': '已结束', 'color': InterviewMessageColorEnum.grey.value}
                    else:
                        if obj.type == ProjectInterviewTypeEnum.formal_interview:
                            if self.context['user_role'] in [UserRoleEnum.coach.value, UserRoleEnum.trainee_coach.value]:
                                return {'status': InterviewListStatusEnum.completed_questionnaire_detail.value,
                                        'msg': '已结束', 'color': InterviewMessageColorEnum.grey.value}
                            else:
                                return {
                                    'status': InterviewListStatusEnum.write_questionnaire_record.value,
                                    'msg': '已结束', 'color': InterviewMessageColorEnum.grey.value}
                        elif obj.type == ProjectInterviewTypeEnum.stakeholder_interview:
                            if self.context['user_role'] == UserRoleEnum.coach:
                                return {'status': InterviewListStatusEnum.completed_questionnaire_detail.value,
                                        'msg': '已结束', 'color': InterviewMessageColorEnum.grey.value}
                            else:
                                # 利益相关者访谈被教练者跳转到问卷形式详情页面
                                return {'status': InterviewListStatusEnum.questionnaire_detail.value,
                                        'msg': '已结束', 'color': InterviewMessageColorEnum.grey.value}
                        else:  # 化学面谈
                            if self.context['user_role'] == UserRoleEnum.coachee:  # 被教练者
                                # 化学面谈已完成，被教练者未完成的被教练者展示填写反馈按钮点击进入填写反馈页面
                                return {'status': InterviewListStatusEnum.chemical_interview_write_feedback.value,
                                        'msg': '已结束', 'color': InterviewMessageColorEnum.grey.value}
                            else:
                                # 化学面谈已完成，被教练者未完成的教练不展示按钮
                                return {'status': InterviewListStatusEnum.completed_questionnaire_detail.value,
                                        'msg': '已结束', 'color': InterviewMessageColorEnum.grey.value}


                # 且辅导记录状态为完成
                if obj.coachee_record_status and obj.coach_record_status:
                    if obj.record_type == InterviewRecordTypeEnum.question_and_answer:
                        return {'status': InterviewListStatusEnum.completed_answer_and_question_detail.value,
                                'msg': '已结束', 'color': InterviewMessageColorEnum.grey.value}
                    else:
                        if obj.type == ProjectInterviewTypeEnum.formal_interview:
                            return {'status': InterviewListStatusEnum.completed_questionnaire_detail.value,
                                    'msg': '已结束', 'color': InterviewMessageColorEnum.grey.value}

                        elif obj.type == ProjectInterviewTypeEnum.stakeholder_interview:
                            if self.context['user_role'] == UserRoleEnum.coach:
                                return {'status': InterviewListStatusEnum.completed_questionnaire_detail.value,
                                        'msg': '已结束', 'color': InterviewMessageColorEnum.grey.value}
                            else:
                                # 利益相关者访谈被教练者跳转到问卷形式详情页面
                                return {'status': InterviewListStatusEnum.questionnaire_detail.value,
                                        'msg': '已结束', 'color': InterviewMessageColorEnum.grey.value}
                        else:  # 化学面谈
                            if self.context['user_role'] == UserRoleEnum.coachee:
                                # 化学面谈已完成，双方都完成的被教练者展示查看反馈按钮点击进入查看反馈页面
                                return {'status': InterviewListStatusEnum.chemical_interview_feedback_detail.value,
                                        'msg': '已结束', 'color': InterviewMessageColorEnum.grey.value}
                            else:  # 教练
                                # 化学面谈已完成，双方都完成的教练不展示按钮
                                return {'status': InterviewListStatusEnum.completed_questionnaire_detail.value,
                                        'msg': '已结束', 'color': InterviewMessageColorEnum.grey.value}
            return 1

        else:  # 集体辅导
            #  未开始   当前日期和时间早于辅导开始日期和时间，且未取消
            if obj.public_attr.start_time > datetime.datetime.now() and obj.public_attr.status != ATTR_STATUS_INTERVIEW_CANCEL:  # 未开始
                if days >= 1:
                    return {'status': InterviewListStatusEnum.questionnaire_detail.value,
                            'msg': '距离开始还有%s天' % days, 'color': InterviewMessageColorEnum.yellow.value}
                elif days == 0 and hours > 0:
                    return {'status': InterviewListStatusEnum.questionnaire_detail.value,
                            'msg': '距离开始还有%s小时' % hours, 'color': InterviewMessageColorEnum.yellow.value}
                elif days == 0 and hours == 0 and minutes > 0:
                    return {'status': InterviewListStatusEnum.questionnaire_detail.value,
                            'msg': '距离开始还有%s分钟' % (int(minutes) or 1), 'color': InterviewMessageColorEnum.yellow.value}
            # 进行中：当前时间等于晚于开始时间，未取消且不符合辅导结束条件    辅导结束条件： 当前时间等于晚于辅导结束时
            if obj.public_attr.start_time < datetime.datetime.now() < obj.public_attr.end_time and obj.public_attr.status != ATTR_STATUS_INTERVIEW_CANCEL:
                if self.context['user_role'] in [UserRoleEnum.coach.value, UserRoleEnum.trainee_coach.value]:
                    return {'status': InterviewListStatusEnum.questionnaire_detail.value,
                            'msg': '进行中', 'color': InterviewMessageColorEnum.purple.value}
                else:
                    return {'status': InterviewListStatusEnum.write_questionnaire_record.value,
                            'msg': '进行中', 'color': InterviewMessageColorEnum.purple.value}

            # 完成
            if obj.public_attr.status != ATTR_STATUS_INTERVIEW_CANCEL and obj.public_attr.end_time < datetime.datetime.now():
                if not obj.coachee_record_status:
                    if self.context['user_role'] in [UserRoleEnum.coach.value, UserRoleEnum.trainee_coach.value]: # 签约教练、个人教练
                        return {'status': InterviewListStatusEnum.completed_questionnaire_detail.value,
                                'msg': '已结束', 'color': InterviewMessageColorEnum.grey.value}
                    else:
                        return {'status': InterviewListStatusEnum.write_questionnaire_record.value,
                                'msg': '已结束', 'color': InterviewMessageColorEnum.grey.value}

                else:
                    return {'status': InterviewListStatusEnum.completed_questionnaire_detail.value,
                            'msg': '已结束', 'color': InterviewMessageColorEnum.grey.value}
            return 1

    def get_date(self, obj):
        try:
            return obj.public_attr.start_time.strftime('%Y-%m-%d %H:%M') + '-' + obj.public_attr.end_time.strftime(
                '%H:%M')
        except Exception as e:
            LarkMessageCenter().send_other_backend_message('接口错误AppInterviewListSerializers.get_date', 'error',
                                                           f'content:{str(e)}')
            return


class AppInterviewListOrderSerializer(serializers.ModelSerializer):
    """
    辅导列表中订单序列化
    """
    id = serializers.SerializerMethodField()
    total_hour = serializers.IntegerField(source='count')
    created_at = serializers.DateTimeField(format='%Y-%m-%d %H:%M')
    interviews_hour = serializers.SerializerMethodField(help_text='已用辅导小时数')
    pay_amount = serializers.SerializerMethodField(help_text='实付金额')
    data_type = serializers.SerializerMethodField(help_text='数据类型')
    describe = serializers.SerializerMethodField(help_text='订单类型描述')
    status = serializers.SerializerMethodField(help_text='订单状态')
    coach_info = serializers.SerializerMethodField(help_text='教练信息')
    coachee_info = serializers.SerializerMethodField(help_text='被教练者信息')

    def get_id(self, obj):
        return obj.order_no

    def get_interviews_hour(self, obj):
        interviews_hour = getattr(obj, 'interviews_count', 0)  # 默认为0，如果没有interviews_count属性
        return interviews_hour

    def get_pay_amount(self, obj):

        pay_amount = Decimal(str(obj.payer_amount)) / Decimal('100')
        # pay_amount
        if pay_amount % 1 == 0:
            pay_amount = int(pay_amount)
        return pay_amount

    def get_data_type(self, obj):
        return AppInterviewListTypeEnum.order.value

    def get_status(self, obj):
        # 根据订单状态设置status值
        if obj.status == OrderStatusEnum.paid.value:
            status = 1  # 表示未完成
        elif obj.status in [OrderStatusEnum.under_refund.value, OrderStatusEnum.refunded.value]:
            status = 2  # 表示已退款
        else:
            status = None  # 不处理未支付订单
        return status

    def get_describe(self, obj):
        return obj.activity.theme if obj.activity else None  # 如果订单关联活动，描述使用活动主题

    def get_coach_info(self, obj):
        target_user_id = obj.public_attr.target_user_id
        coach = Coach.objects.filter(user_id=target_user_id, deleted=False).first()
        resume_data = Resume.objects.filter(coach_id=coach.id, deleted=False, is_customization=False) \
            .values('id', 'head_image_url').first()

        return {
            'id': coach.user_id,  # 教练用户标识
            'name': coach.personal_name if coach else None,  # 教练名字
            'head_image_url': resume_data['head_image_url'] if resume_data and resume_data[
                'head_image_url'] else coach.user.head_image_url,  # 教练头像
            'resume_id': resume_data['id'] if resume_data else None,  # 简历ID
        }

    def get_coachee_info(self, obj):
        coachee_user = obj.public_attr.user
        coachee_name = order_public.get_order_coachee_nickname(obj)
        return {
            'id': coachee_user.id,  # 被教练者标识
            'name': coachee_name,  # 被教练者名字
            'head_image_url': coachee_user.head_image_url,  # 被教练者头像
        }

    class Meta:
        model = Order
        fields = ['id', 'interviews_hour', 'pay_amount', 'status', 'total_hour',
                  'created_at', 'activity_id', 'data_type', 'describe', 'coach_info', 'coachee_info']

class AppTodoListSerializers(serializers.ModelSerializer):
    coach_info = serializers.SerializerMethodField()
    coachee_info = serializers.SerializerMethodField()
    start_time = serializers.DateTimeField(source='public_attr.start_time', format='%Y-%m-%d %H:%M')
    end_time = serializers.DateTimeField(source='public_attr.end_time', format='%Y-%m-%d %H:%M')
    status = serializers.IntegerField(source='public_attr.status')
    date = serializers.SerializerMethodField()
    status_text = serializers.SerializerMethodField()
    capacity_tag = serializers.SerializerMethodField()
    company_info = serializers.SerializerMethodField()
    record_type = serializers.IntegerField()
    data_type = serializers.SerializerMethodField(help_text='数据类型 1：辅导记录，2：教练任务，3:测评报告(该接口暂无)，4：成长目标, 5:化学面谈')
    topic = serializers.SerializerMethodField()
    meeting_info = serializers.SerializerMethodField(help_text='会议信息')

    class Meta:
        model = ProjectInterview
        fields = ('id', 'topic', 'coach_info', 'coachee_info', 'interview_number', 'date', 'times', 'status',
                  'place_category', 'status_text', 'start_time', 'end_time', 'coach_record_status',
                  'coachee_record_status', 'capacity_tag', 'place', 'company_info', 'record_type', 'data_type',
                  'is_coach_agree', 'meeting_info')

    def get_meeting_info(self, obj):
        return interview_public.get_interview_meeting_info(obj)


    def get_topic(self, obj):
        return interview_public.get_interview_topic(self.context.get('user_role'), obj)


    def get_company_info(self, obj):
        now = datetime.datetime.now()
        if (obj.place_category == BUNDLE_INTEWVIEW_TYPE_OFFLINE_GROUP and
            obj.public_attr.status !=ATTR_STATUS_INTERVIEW_CANCEL and obj.public_attr.start_time > now) \
            or (obj.place_category == BUNDLE_INTEWVIEW_TYPE_OFFLINE_GROUP and
                obj.public_attr.start_time < now < obj.public_attr.end_time and obj.public_attr.status != ATTR_STATUS_INTERVIEW_CANCEL):
            try:
                company = Company.objects.get(pk=obj.public_attr.project.company_id)
                return {'company_name': company.short, 'company_logo': company.logo}
            except Exception as e:
                LarkMessageCenter().send_other_backend_message('接口错误AppTodoListSerializers.get_company_info', 'error',
                                                               f'content:{str(e)}')
                return

    def get_capacity_tag(self, obj):
        return get_interview_tag(obj)

    def get_coachee_info(self, obj):
        coachee_name = interview_public.get_interview_coachee_nickname(obj)
        return {'name': coachee_name,}

    def get_coach_info(self, obj):
        coach_user = obj.public_attr.user
        return {
            'name': coach_user.cover_name if coach_user else None,
        }

    def get_data_type(self, obj):
        if obj.type == ProjectInterviewTypeEnum.chemical_interview:
            return DataType.chemical_interview
        elif obj.place_category == ProjectInterviewPlaceCategoryEnum.offline_group_coach:
            group_coach = obj.coach_group_module.filter(deleted=False).first()
            if group_coach:
                project_group_coach = group_coach.project_group_coach
                if project_group_coach.type == GroupCoachTypeEnum.group_tutoring:
                    return DataType.group_tutoring
        return DataType.interview

    def get_status_text(self, obj):
        """
           2、辅导状态为未开始、进行中的线上1对1辅导和线下集体辅导

            文案显示“您有一个教练辅导待开始”

            点击卡片跳转到视频通话页面（1对1）或辅导详情（集体）

            3、辅导状态为完成，且辅导记录状态为教练未完成、双方未完成的线上1对1辅导

            文案显示“您有一个辅导记录待填写”

            点击跳转到填写辅导记录页面
        """
        text = "教练辅导" if obj.type == ProjectInterviewTypeEnum.formal_interview else '化学面谈'
        if not obj.is_coach_agree:
            return {
                'status': TodoListStatusEnum.questionnaire_detail.value,
                'msg': '您有一个<客户预约>待处理', 'color': InterviewMessageColorEnum.purple.value}

        now = datetime.datetime.now()
        if obj.public_attr.status != ATTR_STATUS_INTERVIEW_CANCEL and obj.public_attr.start_time > now:
            if obj.place_category == BUNDLE_INTEWVIEW_TYPE_OFFLINE_GROUP:
                return {"status": TodoListStatusEnum.questionnaire_detail.value, "msg": "您有一个<教练辅导>待开始", "color": InterviewMessageColorEnum.blue.value}
            elif obj.type == ProjectInterviewTypeEnum.chemical_interview:
                return {"status": TodoListStatusEnum.questionnaire_detail.value, "msg": "您有一个<化学面谈>待开始", "color": InterviewMessageColorEnum.blue.value}
            else:
                days, hours, minutes = get_time_slot(obj.public_attr.start_time)
                if obj.type == ProjectInterviewTypeEnum.stakeholder_interview:
                    text = '利益相关者访谈'
                if days == 0 and hours == 0 and minutes > 0:
                    return {"status": TodoListStatusEnum.video_link.value, 'msg': f"您有一个<{text}>待开始", "color": InterviewMessageColorEnum.blue.value}
                else:
                    return {"status": TodoListStatusEnum.questionnaire_detail.value, 'msg': f"您有一个<{text}>待开始", "color": InterviewMessageColorEnum.blue.value}
        elif obj.public_attr.status != ATTR_STATUS_INTERVIEW_CANCEL and obj.public_attr.start_time < now < obj.public_attr.end_time:
            if obj.place_category == BUNDLE_INTEWVIEW_TYPE_OFFLINE_GROUP:
                return {"status": TodoListStatusEnum.questionnaire_detail.value, "msg": "您有一个<教练辅导>进行中", "color": InterviewMessageColorEnum.yellow.value}
            else:
                if obj.type == ProjectInterviewTypeEnum.stakeholder_interview:
                    text = '利益相关者访谈'
                return {"status": TodoListStatusEnum.video_link.value, 'msg': f"您有一个<{text}>进行中", "color": InterviewMessageColorEnum.yellow.value}
        elif obj.public_attr.status != ATTR_STATUS_INTERVIEW_CANCEL and obj.public_attr.end_time < now:
            text = '面谈记录' if text == '化学面谈' else text
            if obj.coachee_record_status and not obj.coach_record_status:
                if obj.record_type == InterviewRecordTypeEnum.question_and_answer:
                    return {"status": TodoListStatusEnum.write_answer_and_question_detail.value, 'msg': f'您有一个<{text}>待填写', "color": InterviewMessageColorEnum.yellow.value}
                else:
                    if obj.type == ProjectInterviewTypeEnum.stakeholder_interview:
                        text = '利益相关者访谈记录'
                    return {"status": TodoListStatusEnum.write_questionnaire_record.value, 'msg': f'您有一个<{text}>待填写', "color": InterviewMessageColorEnum.yellow.value}
            if not obj.coachee_record_status and not obj.coach_record_status:
                if obj.record_type == InterviewRecordTypeEnum.question_and_answer:
                    return {"status": TodoListStatusEnum.write_answer_and_question_detail.value, 'msg': f'您有一个<{text}>待填写', "color": InterviewMessageColorEnum.yellow.value}
                else:
                    if obj.type == ProjectInterviewTypeEnum.stakeholder_interview:
                        text = '利益相关者访谈记录'
                    return {"status": TodoListStatusEnum.write_questionnaire_record.value, 'msg': f'您有一个<{text}>待填写', "color": InterviewMessageColorEnum.yellow.value}

    def get_date(self, obj):
        try:
            weekday_map = {
                0: '周一',
                1: '周二', 
                2: '周三',
                3: '周四',
                4: '周五',
                5: '周六',
                6: '周日'
            }
            weekday = weekday_map[obj.public_attr.start_time.weekday()]
            return obj.public_attr.start_time.strftime(f'%Y-%m-%d {weekday} %H:%M') + '-' + obj.public_attr.end_time.strftime('%H:%M')
        except Exception as e:
            LarkMessageCenter().send_other_backend_message('接口错误AppTodoListSerializers.get_date', 'error',
                                                           f'content:{str(e)}')
            return

# 一对一辅导/化学面谈/利益相关访谈辅导记录序列化
class InterviewRecordQuestionAnswerEditDetailTraineeCoachSerializer(serializers.ModelSerializer):
    start_time = serializers.DateTimeField(source='public_attr.start_time', format='%Y-%m-%d %H:%M')
    end_time = serializers.DateTimeField(source='public_attr.end_time', format='%Y-%m-%d %H:%M')
    project_id = serializers.IntegerField(source='public_attr.project_id')
    status = serializers.IntegerField(source='public_attr.status')
    topic = serializers.SerializerMethodField()
    times = serializers.IntegerField(help_text='约谈时长-分钟')
    question_detail = serializers.SerializerMethodField(help_text='题目详情')
    place = serializers.CharField(help_text='辅导地点')
    isRecord = serializers.SerializerMethodField(help_text='是否填写辅导记录')
    detail_status = serializers.SerializerMethodField(help_text='详情状态')
    isAppraise = serializers.SerializerMethodField(help_text='是否评价')
    record_type = serializers.IntegerField(help_text='辅导记录类型')
    coach_info = serializers.SerializerMethodField()
    coachee_info = serializers.SerializerMethodField()
    state_describe = serializers.SerializerMethodField()
    template_id = serializers.SerializerMethodField()
    fillin_status = serializers.SerializerMethodField()
    coaching_status = serializers.SerializerMethodField()
    place_category = serializers.IntegerField(help_text='辅导类型')
    type = serializers.IntegerField(help_text='辅导类型')
    chemical_interview = serializers.SerializerMethodField(help_text='化学面谈信息')
    detail_text = serializers.SerializerMethodField(help_text='提示')
    qr_code = serializers.SerializerMethodField(help_text='提示二维码')
    stakeholder = serializers.SerializerMethodField(help_text='利益相关者访谈信息')
    remind_text = serializers.SerializerMethodField(help_text='辅导头部提示文本')
    activity = serializers.SerializerMethodField(help_text='活动信息')
    personal_activity = serializers.SerializerMethodField(help_text='教练活动信息')
    group_coach_type = serializers.SerializerMethodField(help_text='集体辅导类型')
    prompt = serializers.SerializerMethodField(help_text='提示信息')
    meeting_info = serializers.SerializerMethodField(help_text='会议详情')

    class Meta:
        model = ProjectInterview
        fields = ['id', 'start_time', 'end_time', 'topic', 'times', 'question_detail', 'place', 'isRecord',
                  'detail_status', 'isAppraise', 'record_type', 'coach_info', 'coachee_info', 'place_category',
                  'state_describe', 'project_id', 'is_coach_agree', 'close_reason', 'status', 'coaching_status',
                  'fillin_status', 'template_id', 'type', 'chemical_interview', 'detail_text', 'qr_code', 'stakeholder',
                  'remind_text', 'activity', 'group_coach_type', 'prompt', 'personal_activity', 'meeting_info']

    def get_meeting_info(self, obj):
        return interview_public.get_interview_meeting_info(obj)

    def get_prompt(self, obj):
        return interview_public.get_interview_prompt(obj, self.context.get('role'))

    def get_group_coach_type(self, obj):
        return interview_public.get_group_coach_type(obj)

    def get_activity(self, obj):
        return interview_public.get_activity_data(obj)

    def get_personal_activity(self, obj):
        return interview_public.get_personal_activity_data(obj)

    def get_remind_text(self, obj):
        return get_interview_remind_text(obj, self.context.get('role'))

    def get_stakeholder(self, obj):
        data = stakeholder_interview_public.get_stakeholder_interview_detail_data(obj)
        return data

    def get_qr_code(self, obj):
        role = int(self.context.get('role', 0))
        if role in [2, 4]:
            if obj.type == ProjectInterviewTypeEnum.chemical_interview:
                return get_project_manage_qr_code(obj.public_attr.project_id)

    def get_chemical_interview(self, obj):
        if obj.type == ProjectInterviewTypeEnum.chemical_interview:
            chemical_interview = obj.chemical_interview.filter(deleted=False).first()
            if chemical_interview:
                chemical_interview_module = chemical_interview.chemical_interview_module
                return {"duration": chemical_interview_module.duration,
                        "start_time": chemical_interview_module.start_time,
                        "end_time": chemical_interview_module.end_time}

    def get_detail_text(self, obj):
        role = int(self.context.get('role', 0))
        return detail_text(role, obj.pk)

    def get_fillin_status(self, obj):
        return obj.get_fillin_status

    def get_coaching_status(self, obj):
        return obj.get_coaching_status

    def get_topic(self, obj):
        return interview_public.get_interview_topic(self.context.get('role'), obj)

    def get_state_describe(self, obj):
        role = int(self.context.get('role', 0))
        return get_interview_state_describe(role, obj)

    def get_coachee_info(self, obj):
        return get_interview_coachee(obj.pk)

    def get_coach_info(self, obj):
        role = self.context.get('role')
        return get_interview_coach(obj.pk, role)

    def get_isAppraise(self, obj):
        if obj.record_type == 1:
            if CoachAppraise.objects.filter(interview=obj, deleted=False).exists():
                return 1
            return 0
        else:
            if ProjectInterviewRecord.objects.filter(deleted=False, interview=obj).exists():
                return 1
            return 0

    def get_isRecord(self, obj):
        if not obj.coachee_record_status:
            return False
        else:
            return True

    def get_question_detail(self, obj):
        # 根据辅导类型找到对应的模版和问题
        role = self.context.get('role', None)
        if obj.type == ProjectInterviewTypeEnum.chemical_interview:
            answers = InterviewRecordTemplateAnswer.objects.filter(interview_id=obj.pk).first()
            if answers:
                interview_template = answers.question.template
            else:
                interview_template = InterviewRecordTemplate.objects.filter(
                    type=InterviewRecordTemplateTypeEnum.chemical_interview, deleted=False).last()
        elif obj.type == ProjectInterviewTypeEnum.stakeholder_interview:
            answers = InterviewRecordTemplateAnswer.objects.filter(interview_id=obj.pk).first()
            if answers:
                interview_template = answers.question.template
            else:
                stakeholder_interview = obj.stakeholder_interview.filter(deleted=False).first()
                if stakeholder_interview:
                    interview_template = stakeholder_interview.stakeholder_interview_module.coach_template
                else:
                    cancel_stakeholder_interview = obj.cancel_stakeholder_interview.filter(deleted=False).first()
                    if not cancel_stakeholder_interview:
                        return
                    stakeholder_interview = cancel_stakeholder_interview.stakeholder_interview
                    interview_template = stakeholder_interview.stakeholder_interview_module.coach_template
        else:
            if obj.public_attr.project_id:
                total_template_type = TotalTemplateTypeEnum.project_one_to_one.value
            else:
                total_template_type = TotalTemplateTypeEnum.one_to_one_tutor.value

            # 如果辅导已经提交过记录，就返回记录对应的模板
            answers = InterviewRecordTemplateAnswer.objects.filter(interview_id=obj.pk).first()
            if answers:
                template = answers.question.template
                total_template = TotalTemplate.objects.filter(
                    (Q(coach_template=template) | Q(coachee_template=template)),
                    type=total_template_type,
                    write_role=InterviewRecordTemplateRoleEnum.coach_student.value).first()
                interview_template = total_template.coach_template if role in [1, 3] else total_template.coachee_template
            else:
                # 如果辅导没有提交过记录，就返回启用的模版
                # 优先返回项目指定的模版
                if obj.public_attr.project and obj.public_attr.project.interview_record_template:
                    total_template = obj.public_attr.project.interview_record_template
                else:
                    STATUS_ENABLE = 1
                    total_template = TotalTemplate.objects.filter(
                        write_role=InterviewRecordTemplateRoleEnum.coach_student.value,
                        type=total_template_type, status=STATUS_ENABLE
                        ).first()
                interview_template = total_template.coach_template if role in [1, 3] else total_template.coachee_template

        order = Case(*[When(pk=order, then=pos) for pos, order in enumerate(eval(interview_template.questions_order))])
        question = InterviewRecordTemplateQuestion.objects.filter(
            template_id=interview_template.pk, deleted=False).order_by(order).all()
        question = InterviewRecordTemplateQuestionSerializers(question, many=True)
        data = add_edit_data(question.data, obj.id, interview_template_id=interview_template.pk, role=role)
        return data

    def get_template_id(self, obj):
        role = self.context.get('role', None)
        if obj.public_attr.project_id:
            total_template_type = TotalTemplateTypeEnum.project_one_to_one.value
        else:
            total_template_type = TotalTemplateTypeEnum.one_to_one_tutor.value

        answers = InterviewRecordTemplateAnswer.objects.filter(interview_id=obj.pk).first()
        if answers:
            if obj.type == ProjectInterviewTypeEnum.chemical_interview:
                interview_template = answers.question.template
            elif obj.type == ProjectInterviewTypeEnum.stakeholder_interview:
                interview_template = answers.question.template
            else:
                template = answers.question.template
                total_template = TotalTemplate.objects.filter(
                    (Q(coach_template=template) | Q(coachee_template=template)),
                    type=total_template_type,
                    write_role=InterviewRecordTemplateRoleEnum.coach_student.value).first()
                interview_template = total_template.coach_template if role in [1, 3] else total_template.coachee_template
        else:
            if obj.type == ProjectInterviewTypeEnum.chemical_interview:
                interview_template = InterviewRecordTemplate.objects.filter(
                    type=InterviewRecordTemplateTypeEnum.chemical_interview, deleted=False).last()
            elif obj.type == ProjectInterviewTypeEnum.stakeholder_interview:
                stakeholder_interview = obj.stakeholder_interview.filter(deleted=False).first()
                if stakeholder_interview:
                    interview_template = stakeholder_interview.stakeholder_interview_module.coach_template
                else:
                    cancel_stakeholder_interview = obj.cancel_stakeholder_interview.filter(deleted=False).first()
                    if not cancel_stakeholder_interview:
                        return
                    stakeholder_interview = cancel_stakeholder_interview.stakeholder_interview
                    interview_template = stakeholder_interview.stakeholder_interview_module.coach_template

            else:
                if obj.public_attr.project and obj.public_attr.project.interview_record_template:
                    total_template = obj.public_attr.project.interview_record_template
                else:
                    total_template = TotalTemplate.objects.filter(
                        write_role=InterviewRecordTemplateRoleEnum.coach_student.value,
                        type=total_template_type, status=1
                        ).first()
                interview_template = total_template.coach_template if role in [1, 3] else total_template.coachee_template
        return interview_template.pk

    def get_detail_status(self, obj):
        role = int(self.context['role'])
        # 辅导记录列表的序列化固定跳到完成页面
        # 问卷模式的序列化，固定展示问卷详情
        # 根据当前用户身份，和是否填写，控制展示编辑安妮&填写按钮。
        # 定义辅导记录列表的序列化结果，固定显示在完成页面上。
        result = {
            'head': InterviewDetailHeadEnum.one_to_one_finish.value,  # 设置头部信息为一对一辅导完成
            'content': InterviewDetailContentEnum.show_questionnaire_detail.value,  # 设置内容为问卷详情
        }

        # 根据用户的角色来确定按钮的状态。
        if role in [UserRoleEnum.coach.value, UserRoleEnum.trainee_coach.value]:  # 如果用户是教练或实习教练
            if obj.coach_record_status:  # 如果教练的记录状态存在
                result['button'] = [InterviewDetailButtonEnum.edit_record.value]  # 设置按钮为编辑记录
            else:
                result['button'] = [InterviewDetailButtonEnum.write_record.value]  # 设置按钮为写记录
        else:  # 如果用户不是教练，则默认为被辅导者
            if obj.coachee_record_status:  # 如果被辅导者的记录状态存在
                result['button'] = [InterviewDetailButtonEnum.edit_record.value]  # 设置按钮为编辑记录
            else:
                result['button'] = [InterviewDetailButtonEnum.write_record.value]  # 设置按钮为写记录

        return result

# 集体辅导工作坊辅导记录序列化
class InterviewRecordQuestionAnswerEditDetailSerializer(serializers.ModelSerializer):
    start_time = serializers.DateTimeField(source='public_attr.start_time', format='%Y-%m-%d %H:%M')
    end_time = serializers.DateTimeField(source='public_attr.end_time', format='%Y-%m-%d %H:%M')
    project_id = serializers.IntegerField(source='public_attr.project_id')
    status = serializers.IntegerField(source='public_attr.status')
    topic = serializers.SerializerMethodField()
    times = serializers.IntegerField(help_text='约谈时长-分钟')
    question_detail = serializers.SerializerMethodField(help_text='题目详情')
    isAppraise = serializers.SerializerMethodField(help_text='是否评价')
    capacity_tag = serializers.SerializerMethodField(help_text='能力标签')
    place = serializers.CharField(help_text='辅导地点')
    isRecord = serializers.SerializerMethodField(help_text='是否填写辅导记录')
    detail_status = serializers.SerializerMethodField(help_text='详情状态')
    record_type = serializers.IntegerField(help_text='辅导记录类型')
    coach_info = serializers.SerializerMethodField()
    coachee_info = serializers.SerializerMethodField()
    state_describe = serializers.SerializerMethodField()
    template_id = serializers.SerializerMethodField()
    fillin_status = serializers.SerializerMethodField()
    coaching_status = serializers.SerializerMethodField()
    place_category = serializers.IntegerField(help_text='辅导类型')
    remind_text = serializers.SerializerMethodField(help_text='辅导头部提示文本')
    activity = serializers.SerializerMethodField(help_text='活动信息')
    personal_activity = serializers.SerializerMethodField(help_text='教练活动信息')
    group_coach_type = serializers.SerializerMethodField(help_text='集体辅导类型')


    class Meta:
        model = ProjectInterview
        fields = ['id', 'start_time', 'end_time', 'topic', 'times', 'question_detail', 'isAppraise', 'capacity_tag',
                  'place', 'isRecord', 'detail_status', 'record_type', 'coach_info', 'coachee_info', 'place_category',
                  'state_describe', 'project_id', 'is_coach_agree', 'close_reason', 'status', 'fillin_status',
                  'coaching_status', 'template_id', 'type', 'remind_text', 'activity', 'group_coach_type', 'personal_activity']
    # todo zkg
    def get_group_coach_type(self, obj):
        return interview_public.get_group_coach_type(obj)

    def get_activity(self, obj):
        return interview_public.get_activity_data(obj)

    def get_personal_activity(self, obj):
        return interview_public.get_personal_activity_data(obj)

    def get_remind_text(self, obj):
        return get_interview_remind_text(obj, self.context.get('role'))

    def get_fillin_status(self, obj):
        return obj.get_fillin_status

    def get_coaching_status(self, obj):
        return obj.get_coaching_status

    def get_topic(self, obj):
        return interview_public.get_interview_topic(self.context.get('role'), obj)

    def get_state_describe(self, obj):
        role = int(self.context.get('role', 0))
        return get_interview_state_describe(role, obj)

    def get_coachee_info(self, obj):
        return get_interview_coachee(obj.pk)

    def get_coach_info(self, obj):
        return get_interview_coach(obj.pk)

    def get_isRecord(self, obj):
        if obj.coach_record_status or obj.coachee_record_status:
            return 1
        return 0

    def get_isAppraise(self, obj):
        if obj.record_type == 1:
            if CoachAppraise.objects.filter(interview=obj, deleted=False).exists():
                return 1
            return 0
        else:
            if ProjectInterviewRecord.objects.filter(deleted=False, interview=obj).exists():
                return 1
            return 0


    def get_capacity_tag(self, obj):
        project_interview = ProjectInterview.objects.filter(pk=obj.id, deleted=False).first()
        # user = project_interview.public_attr.target_user
        # project = project_interview.public_attr.project
        # project_member = ProjectMember.objects.filter(user=user, project=project, deleted=False,
        #                                               role=ProjectMemberRoleEnum.coachee.value).first()
        group_coach = project_interview.coach_group_module.filter(deleted=False).first()
        # project_bundle = project_member.project_bundle.filter(deleted=False).first()
        # group_coach = project_bundle.coach_group.filter(
        #     deleted=False,
        #     theme=project_interview.topic, start_course_time=project_interview.public_attr.start_time,
        #     end_course_time=project_interview.public_attr.end_time, course_place=project_interview.place).first()
        tmp_tag_lst = PowerTag.objects.filter(group_coach=group_coach, deleted=False).values_list('tag', flat=True)
        tag_lst = [PowerTagEnum(tag).describe() for tag in tmp_tag_lst]
        return tag_lst

    def get_question_detail(self, obj):
        from wisdom_v2.views.project_interview_action import get_interview_template
        interview_template_id = get_interview_template(obj.id)
        interview_template = InterviewRecordTemplate.objects.get(pk=interview_template_id)
        order = Case(*[When(pk=order, then=pos) for pos, order in enumerate(eval(interview_template.questions_order))])
        question = InterviewRecordTemplateQuestion.objects.filter(
            template_id=interview_template_id, deleted=False).order_by(order).all()
        question = InterviewRecordTemplateQuestionSerializers(question, many=True)
        data = add_edit_data(question.data, obj.id)
        return data

    def get_template_id(self, obj):
        from wisdom_v2.views.project_interview_action import get_interview_template
        interview_template_id = get_interview_template(obj.id)
        return interview_template_id

    def get_detail_status(self, obj):
        role = int(self.context['role'])
        result = detail_status(role, obj.pk)
        return result

def add_edit_data(questions, interview_id, interview_template_id=None, role=None):
    project_interview = ProjectInterview.objects.get(pk=interview_id)
    if interview_template_id:
        interview_template = InterviewRecordTemplate.objects.get(pk=interview_template_id)
    else:
        interview_template = None
    for question in questions:
        data = get_question_answer(project_interview.id, question['id'])
        question['answer'] = data
        question['description'] = None
        question['opposite_answer'] = None
        if role:
            opposite_question_id, description = get_opposite_question(question['id'], role)
            if opposite_question_id:
                data = get_question_answer(project_interview.id, opposite_question_id, is_echo=True)
                question['opposite_answer'] = data
                question['description'] = description
    return questions


def check_answer_data(question_data, interview_id, is_single=False):
    if not question_data:
        return '当前数据不存在'

    project_interview = ProjectInterview.objects.get(pk=interview_id)

    order_lst = []
    for index, question in enumerate(question_data):
        # 填空题判断
        if question['type'] == InterviewRecordTemplateQuestionTypeEnum.blank.value:
            # 普通文本框
            if question['answer_type'] == InterviewRecordTemplateQuestionAnswerTypeEnum.normal_text.value:
                if question['answer']:
                    if 'content' not in question['answer'].keys():
                        return '问题%s缺少回答' % question['title']
                    if not question['answer']['content']:
                        return '请填写问题%s的答案' % question['title']

                    if 'id' in question['answer'].keys():
                        answer = InterviewRecordTemplateAnswer.objects.filter(pk=question['answer']['id']).first()
                        if not answer:
                            return '问题%s回答数据错误' % question['title']
                        if answer.question_id != question['id']:
                            return '问题%s回答数据与问题冲突' % question['title']
                    else:
                        answer = InterviewRecordTemplateAnswer.objects.filter(
                            interview_id=project_interview.id, public_attr=project_interview.public_attr,
                            question_id=question['id'],
                            question__type=InterviewRecordTemplateQuestionTypeEnum.blank.value,
                            question__answer_type=InterviewRecordTemplateQuestionAnswerTypeEnum.normal_text.value)
                        if answer.exists():
                            return '问题%s回答数据错误，缺少问题标识' % question['title']
                else:
                    if not is_single:
                        if question['required']:
                            order_lst.append(index + 1)

            # 能力标签
            if question['answer_type'] == InterviewRecordTemplateQuestionAnswerTypeEnum.power_tag.value:
                if question['answer']:
                    if 'content' not in question['answer'].keys():
                        return '问题%s缺少回答' % question['title']
                    if not question['answer']['content']:
                        return '请填写问题%s的答案' % question['title']

                    if 'id' in question['answer'].keys():
                        answer = InterviewRecordTemplateAnswer.objects.filter(pk=question['answer']['id']).first()
                        if not answer:
                            return '问题%s回答数据错误' % question['title']
                        if answer.question_id != question['id']:
                            return '问题%s回答数据与问题冲突' % question['title']
                    else:
                        answer = InterviewRecordTemplateAnswer.objects.filter(
                            interview_id=project_interview.id, public_attr=project_interview.public_attr,
                            question_id=question['id'],
                            question__type=InterviewRecordTemplateQuestionTypeEnum.blank.value,
                            question__answer_type=InterviewRecordTemplateQuestionAnswerTypeEnum.power_tag.value)
                        if answer.exists():
                            return '问题%s回答数据错误，缺少问题标识' % question['title']
                else:
                    if not is_single:
                        if question['required']:
                            order_lst.append(index + 1)


            # 行动计划
            if question['answer_type'] == InterviewRecordTemplateQuestionAnswerTypeEnum.action_plan.value:
                # if 'content' in question.keys() and question['content'] and 'end_date' in question.keys() \
                #         and question['end_date']:
                if question['answer']:
                    for answer_f in question['answer']:
                        if 'content' not in answer_f.keys():
                            return '问题%s缺少回答' % question['title']
                        if not answer_f['content']:
                            return '请填写问题%s的答案' % question['title']
                        if 'end_date' not in answer_f.keys():
                            return '问题%s缺少结束日期' % question['title']

                        if 'id' in answer_f.keys():
                            answer = InterviewRecordTemplateAnswer.objects.filter(pk=answer_f['id']).first()
                            if not answer:
                                return '问题%s回答数据错误' % question['title']
                            if answer.question_id != question['id']:
                                return '问题%s回答数据与问题冲突' % question['title']

                else:
                    if not is_single:
                        if question['required']:
                            order_lst.append(index + 1)

            # 习惯养成
            if question['answer_type'] == InterviewRecordTemplateQuestionAnswerTypeEnum.habit_formation.value:
                if question['answer']:
                    for answer_f in question['answer']:
                        if 'when' not in answer_f.keys() or not answer_f['when']:
                            return '问题%s缺少何时答案' % question['title']
                        if 'stop' not in answer_f.keys() or not answer_f['stop']:
                            return '问题%s缺少停止答案' % question['title']
                        if 'change' not in answer_f.keys() or not answer_f['change']:
                            return '问题%s缺少改变答案' % question['title']
                        if 'end_date' not in answer_f.keys() or not answer_f['end_date']:
                            return '问题%s缺少结束日期' % question['title']
                        if 'start_date' not in answer_f.keys() or not answer_f['start_date']:
                            return '问题%s缺少开始日期' % question['title']

                        if 'id' in answer_f.keys():
                            answer = InterviewRecordTemplateAnswer.objects.filter(pk=answer_f['id']).first()
                            if not answer:
                                return '问题%s回答数据错误' % question['title']
                            if answer.question_id != question['id']:
                                return '问题%s回答数据与问题冲突' % question['title']

                        # else:
                        #     answer = InterviewRecordTemplateAnswer.objects.filter(
                        #         interview_id=project_interview.id, public_attr=project_interview.public_attr,
                        #         question_id=question['id'], question__type=InterviewRecordTemplateQuestionTypeEnum.blank.value,
                        #         question__answer_type=InterviewRecordTemplateQuestionAnswerTypeEnum.action_plan.value)
                        #     if answer.exists():
                        #         return '问题%s回答数据错误，缺少问题标识' % question['title']

                else:
                    if not is_single:
                        if question['required']:
                            order_lst.append(index + 1)

            # 成长笔记
            if question['answer_type'] == InterviewRecordTemplateQuestionAnswerTypeEnum.growth_notes.value:
                if question['answer']:
                    if 'content' not in question['answer'].keys() or not question['answer']['content']:
                        return '问题%s缺少答案' % question['title']

                    if 'id' in question['answer'].keys():
                        answer = InterviewRecordTemplateAnswer.objects.filter(pk=question['answer']['id']).first()
                        if not answer:
                            return '问题%s回答数据错误' % question['title']
                        if answer.question_id != question['id']:
                            return '问题%s回答数据与问题冲突' % question['title']

                    else:
                        answer = InterviewRecordTemplateAnswer.objects.filter(
                            interview_id=project_interview.id, public_attr=project_interview.public_attr,
                            question_id=question['id'],
                            question__type=InterviewRecordTemplateQuestionTypeEnum.blank.value,
                            question__answer_type=InterviewRecordTemplateQuestionAnswerTypeEnum.growth_notes.value)
                        if answer.exists():
                            return '问题%s回答数据错误，缺少问题标识' % question['title']

                else:
                    if not is_single:
                        if question['required']:
                            order_lst.append(index + 1)

        # 单选
        if question['type'] == InterviewRecordTemplateQuestionTypeEnum.single.value:
            if question['answer']:
                if 'option' not in question['answer'][0].keys():
                    return '问题%s缺少选项' % question['title']
                option_lst = InterviewRecordTemplateOption.objects.filter(question_id=question['id'], deleted=False). \
                    values_list('id', flat=True)
                option_lst = list(option_lst)
                if question['answer'][0]['option'] not in option_lst:
                    return '问题%s选项错误' % question['title']
                for i in question['option']:
                    if i['id'] == question['answer'][0]['option']:
                        if i['user_defined']:
                            if 'option_custom' not in question['answer'][0].keys():
                                return '请在第%s题的文本框中输入回答' % str(index + 1)
                            if not question['answer'][0]['option_custom']:
                                return '请在第%s题的文本框中输入回答' % str(index + 1)

            else:
                if not is_single:
                    if question['required']:
                        order_lst.append(index + 1)

        # 多选
        if question['type'] == InterviewRecordTemplateQuestionTypeEnum.multiple.value:
            if question['answer']:
                option_lst = InterviewRecordTemplateOption.objects.filter(question_id=question['id'], deleted=False). \
                    values_list('id', flat=True)
                option_lst = list(option_lst)
                for option in question['answer']:
                    if 'option' not in option.keys():
                        return '问题%s缺少选项' % question['title']

                    if option['option'] not in option_lst:
                        return '问题%s选项错误' % question['title']
                    for i in question['option']:
                        if i['id'] == option['option']:
                            if i['user_defined'] and 'option_custom' not in option.keys():
                                return '请在第%s题的文本框中输入回答' % str(index + 1)
                            if i['user_defined'] and not option['option_custom']:
                                return '请在第%s题的文本框中输入回答' % str(index + 1)

            else:
                if not is_single:
                    if question['required']:
                        order_lst.append(index + 1)

        # 评分
        if question['type'] == InterviewRecordTemplateQuestionTypeEnum.rating.value:
            if question['answer']:
                if 'score' not in question['answer'].keys():
                    return '问题%s缺少分数' % question['title']
                if type(question['answer']['score']) != int:
                    return '问题%s分数类型错误' % question['title']

                if 'id' in question['answer'].keys():
                    answer = InterviewRecordTemplateAnswer.objects.filter(pk=question['answer']['id']).first()
                    if not answer:
                        return '问题%s回答数据错误' % question['title']
                    if answer.question_id != question['id']:
                        return '问题%s回答数据与问题冲突' % question['title']
                else:
                    answer = InterviewRecordTemplateAnswer.objects.filter(
                        interview_id=project_interview.id, public_attr=project_interview.public_attr,
                        question_id=question['id'], question__type=InterviewRecordTemplateQuestionTypeEnum.rating.value)
                    if answer.exists():
                        return '问题%s回答数据错误，缺少问题标识' % question['title']
            else:
                if not is_single:
                    if question['required']:
                        order_lst.append(index + 1)

    if order_lst:
        order = '、'.join(map(str, order_lst))
        return '请填写第' + order + '题'


def get_interview_template_role(interview_id):
    from wisdom_v2.views.project_interview_action import get_interview_template
    interview_template_id = get_interview_template(interview_id)
    interview_template = InterviewRecordTemplate.objects.filter(pk=interview_template_id).first()
    return interview_template.role


def detail_text(role, interview_id):
    obj = ProjectInterview.objects.get(pk=interview_id)
    if obj.type == ProjectInterviewTypeEnum.chemical_interview:  # 化学面谈
        if role == 2:
            msg, sender, external_user_id = get_project_manage_wx_user_id(obj.public_attr.project_id,
                                                                          obj.public_attr.target_user_id)
            if msg:
                return '<text class="purple-text">您已添加项目运营微信，</text>如需调整时间或确认面谈前准备，' \
                       '您识别下方二维码与运营沟通吧'
            else:
                return '<text class="purple-text">如需调整时间或确认面谈前准备，</text>' \
                       '添加项目运营微信进行一对一沟通吧'
        else:
            customer_portrait = CustomerPortrait.objects.filter(
                project=obj.public_attr.project, user=obj.public_attr.target_user,
                deleted=False, type=CustomerPortraitTypeEnum.personal).first()
            if customer_portrait:
                if customer_portrait.group_target and customer_portrait.personal_target:
                    return f'<text class="purple-text">{customer_portrait.group_target}，</text>' \
                           f'{customer_portrait.personal_target}'
            customer_portrait = CustomerPortrait.objects.filter(
                project=obj.public_attr.project, deleted=False, type=CustomerPortraitTypeEnum.group).first()
            if customer_portrait:
                if customer_portrait.group_target and customer_portrait.personal_target:
                    return f'<text class="purple-text">{customer_portrait.group_target}，</text>' \
                           f'{customer_portrait.personal_target}'

    elif obj.type == ProjectInterviewTypeEnum.stakeholder_interview:
        manage_list = obj.public_attr.project.manager_list
        project_manager_phone = settings.DEFAULT_PROJECT_MANAGER_PHONE if not manage_list else manage_list[0]['phone']

        if role == 1:  # 教练
            return "1、如果您想调整访谈时间，请务必和项目运营提前同步，确认客户是否能够调整。\n" \
                   f"2、如果在访谈开始后，访谈对象未能按时进行视频通话，可联系项目运营的电话：<span style='color:#815FFF;'>{project_manager_phone}</span>"
        else:
            return "本次访谈是通过利益相关者的视角来发现管理者的优势和待发展项，在访谈前，请回顾您和同事的合作经历，总结：\n" \
                   "1、他/她的哪些好的行为让你们工作都得到有效支持\n" \
                   "2、他/她还能改进什么行为，能让今后的业务合作有更好的效果\n" \
                   f"本次访谈将由第三方教练团队实施，在访谈中，每个人的谈话内容将被严格保密，最终结果呈现也为匿名。如果你的时间安排有变化，可联系项目运营的电话：<span style='color:#815FFF;'>{project_manager_phone}</span>"

    else:
        customer_user = WorkWechatUser.objects.filter(external_user_id__isnull=False, user_id=obj.public_attr.target_user_id, deleted=False).first()
        wx_user = WorkWechatUser.objects.filter(wx_user_id__isnull=False, user_id=obj.public_attr.user_id, deleted=False).first()
        if role in [1, 3]:  # 教练
            if obj.public_attr.target_user.phone:
                return f"客户预约的辅导时间若不方便，通过以下方式联系客户调整吧。\n手机号：<span style='color:#815FFF;'>{obj.public_attr.target_user.phone}</span>"
            else:
                return "客户预约的辅导时间若不方便，通过以下方式联系客户调整吧。"
        elif role == 2:  # 项目客户
            if wx_user:
                is_relevance, external_user_id = coach_public.work_wechat_coach_customer_info(
                    wx_user, customer_user, obj.public_attr.target_user
                )
                if is_relevance:
                    return "如需调整时间或确认辅导前准备，通过微信联系教练吧~"
                else:
                    return "如需调整时间或确认辅导前准备，添加教练微信和教练一对一沟通吧~"
            else:
                return "如需调整时间或确认辅导前准备，添加教练微信和教练一对一沟通吧~"
        else:   # 个人客户
            if wx_user:
                is_relevance, external_user_id = coach_public.work_wechat_coach_customer_info(
                    wx_user, customer_user, obj.public_attr.target_user
                )
                if obj.is_coach_agree:
                    if is_relevance:
                        return '<text class="purple-text">教练已确认预约，</text>如需调整时间或确认辅导前准备，' \
                               '通过识别下方二维码进入消息会话，联系教练吧~'
                    else:
                        return '<text class="purple-text">教练已确认预约，</text>如需调整时间或确认辅导前准备，' \
                               '添加教练微信和教练一对一沟通吧~'
                else:
                    if is_relevance:
                        return f'教练确认后您将接收到{settings.SMS_TEMPLATE_PREFIX}开头的短信通知，请注意查收～\n您已添加教练微信，识别下方二维码进入消息会话，提醒教练确认吧~'
                    else:
                        return f'教练确认后您将接收到{settings.SMS_TEMPLATE_PREFIX}开头的短信通知，请注意查收～\n您也可以长按识别二维码，添加教练微信，和教练一对一的沟通'
            else:
                if obj.is_coach_agree:
                    return '<text class="purple-text">教练已确认预约，</text>如需调整时间或确认辅导前准备，' \
                           '添加教练微信和教练一对一沟通吧~'
                else:
                    return f'教练确认后您将接收到{settings.SMS_TEMPLATE_PREFIX}开头的短信通知，请注意查收～\n您也可以长按识别二维码，添加教练微信，和教练一对一的沟通'


def detail_status(role, interview_id):
    is_coach = True if int(role) in [1, 3] else False
    obj = ProjectInterview.objects.get(pk=interview_id)
    if obj.place_category == BUNDLE_INTEWVIEW_TYPE_OFFLINE_GROUP:   # 线下集体辅导
        if obj.public_attr.status == ATTR_STATUS_INTERVIEW_CANCEL:
            result = {
                "head": InterviewDetailHeadEnum.group_coach_not_start_or_ongoing.value,
                "content": InterviewDetailContentEnum.not_show.value,
                "button": []
            }
            return result

        # 未开始
        if obj.public_attr.start_time > datetime.datetime.now() and obj.public_attr.status != ATTR_STATUS_INTERVIEW_CANCEL:
            if is_coach:
                # 教练 - 集体辅导未开始、进行中；
                result = {
                    "head": InterviewDetailHeadEnum.group_coach_finish.value,
                    "content": InterviewDetailContentEnum.not_show_detail.value,
                    "button": []
                }
                return result
            else:
                # 被教练 - 集体辅导未开始
                result = {
                    "head": InterviewDetailHeadEnum.group_coach_not_start_or_ongoing.value,
                    "content": InterviewDetailContentEnum.not_show.value,
                    "button": []
                }
                return result

        # 进行中
        elif obj.public_attr.start_time < datetime.datetime.now() < obj.public_attr.end_time \
                and obj.public_attr.status != ATTR_STATUS_INTERVIEW_CANCEL:
            if is_coach:
                # 教练 - 集体辅导未开始、进行中；
                result = {
                    "head": InterviewDetailHeadEnum.group_coach_finish.value,
                    "content": InterviewDetailContentEnum.not_show_detail.value,
                    "button": []
                }
                return result
            else:
                if obj.coachee_record_status:
                    result = {
                        "head": InterviewDetailHeadEnum.group_coach_finish.value,
                        "content": InterviewDetailContentEnum.show_questionnaire_detail.value,
                        "button": [InterviewDetailButtonEnum.edit_record.value]
                    }
                else:
                    result = {
                        "head": InterviewDetailHeadEnum.group_coach_finish.value,
                        "content": InterviewDetailContentEnum.not_show_detail.value,
                        "button": [InterviewDetailButtonEnum.write_record.value]
                    }
                return result

        # 完成
        elif obj.public_attr.status != ATTR_STATUS_INTERVIEW_CANCEL and obj.public_attr.end_time < datetime.datetime.now():
            if not obj.coachee_record_status:
                if is_coach:
                    result = {
                        "head": InterviewDetailHeadEnum.group_coach_finish.value,
                        "content": InterviewDetailContentEnum.not_show_detail.value,
                        "button": []
                    }
                    return result
                else:
                    result = {
                        "head": InterviewDetailHeadEnum.group_coach_finish.value,
                        "content": InterviewDetailContentEnum.not_show_detail.value,
                        "button": [InterviewDetailButtonEnum.write_record.value]
                    }
                    return result

            else:
                if is_coach:
                    result = {
                        "head": InterviewDetailHeadEnum.group_coach_finish.value,
                        "content": InterviewDetailContentEnum.show_questionnaire_detail.value,
                        "button": []
                    }
                    return result
                else:
                    result = {
                        "head": InterviewDetailHeadEnum.group_coach_finish.value,
                        "content": InterviewDetailContentEnum.show_questionnaire_detail.value,
                        "button": [InterviewDetailButtonEnum.edit_record.value]
                    }
                    return result

    else:  # 线上一对一
        if obj.public_attr.status == ATTR_STATUS_INTERVIEW_CANCEL:
            result = {
                "head": InterviewDetailHeadEnum.one_to_one_not_start_or_ongoing.value,
                "content": InterviewDetailContentEnum.not_show.value,
                "button": []
            }
            return result
        # 待教练确认
        if not obj.is_coach_agree:

            # 如果是教练
            if is_coach:
                result = {
                    "head": InterviewDetailHeadEnum.one_to_one_not_start_or_ongoing.value,
                    "content": InterviewDetailContentEnum.contact_detail.value,
                    "button": [InterviewDetailButtonEnum.agree_interview.value,
                               InterviewDetailButtonEnum.update_interview_time.value]
                }
            # 如果是客户
            else:
                result = {
                    "head": InterviewDetailHeadEnum.one_to_one_not_start_or_ongoing.value,
                    "content": InterviewDetailContentEnum.contact_detail.value,
                    "button": [InterviewDetailButtonEnum.cancel_interview.value,
                               InterviewDetailButtonEnum.update_interview_time.value]
                }
            return result

        # 未开始
        if obj.public_attr.start_time > datetime.datetime.now() and obj.public_attr.status != ATTR_STATUS_INTERVIEW_CANCEL:
            # days, hours, minutes = get_time_slot(obj.public_attr.start_time)

            # 线下辅导不展示进入视频按钮
            if obj.place_type == ProjectInterviewPlaceTypeEnum.offline.value:
                button = []
            else:
                button = [InterviewDetailButtonEnum.access_interview.value]

            if is_coach:
                result = {
                    "head": InterviewDetailHeadEnum.one_to_one_not_start_or_ongoing.value,
                    "content": InterviewDetailContentEnum.contact_detail.value,
                    "button": button
                }
                if is_interview_update_date_button(obj):
                    result["button"].append(InterviewDetailButtonEnum.update_interview_time.value)
                return result
            else:
                button.append(InterviewDetailButtonEnum.cancel_interview.value)
                result = {
                    "head": InterviewDetailHeadEnum.one_to_one_not_start_or_ongoing.value,
                    "content": InterviewDetailContentEnum.contact_detail.value,
                    "button": button
                }
                if is_interview_update_date_button(obj):
                    result["button"].append(InterviewDetailButtonEnum.update_interview_time.value)
                return result

        elif obj.public_attr.start_time < datetime.datetime.now() < obj.public_attr.end_time \
                and obj.public_attr.status != ATTR_STATUS_INTERVIEW_CANCEL:
            # 如果是教练
            if is_coach:
                if obj.coach_record_status:
                    result = {
                        "head": InterviewDetailHeadEnum.one_to_one_finish.value,
                        "content": InterviewDetailContentEnum.show_questionnaire_detail.value if
                        obj.record_type == InterviewRecordTypeEnum.questionnaire else InterviewDetailContentEnum.show_question_and_answer_detail.value,
                        "button": [InterviewDetailButtonEnum.edit_record.value,
                                   InterviewDetailButtonEnum.invite_customer_write.value]
                    }
                    if obj.type == ProjectInterviewTypeEnum.chemical_interview:
                        result['button'].remove(InterviewDetailButtonEnum.invite_customer_write.value)
                    elif obj.type == ProjectInterviewTypeEnum.stakeholder_interview:
                        result['button'].remove(InterviewDetailButtonEnum.invite_customer_write.value)
                else:
                    result = {
                        "head": InterviewDetailHeadEnum.one_to_one_not_start_or_ongoing.value,
                        "content": InterviewDetailContentEnum.contact_detail.value,
                        "button": [InterviewDetailButtonEnum.access_interview.value]
                    }
                    # 线下辅导不展示进入视频按钮
                    if obj.place_type == ProjectInterviewPlaceTypeEnum.offline.value:
                        result['button'].remove(InterviewDetailButtonEnum.update_interview_time.value)

                    if not obj.coachee_record_status and is_interview_update_date_button(obj):
                        result['button'].append(InterviewDetailButtonEnum.update_interview_time.value)
                return result
            else:
                if obj.coachee_record_status:
                    result = {
                        "head": InterviewDetailHeadEnum.one_to_one_finish.value,
                        "content": InterviewDetailContentEnum.show_questionnaire_detail.value if
                        obj.record_type == InterviewRecordTypeEnum.questionnaire else InterviewDetailContentEnum.show_question_and_answer_detail.value,
                        "button": [InterviewDetailButtonEnum.edit_record.value]
                    }
                    if obj.type == ProjectInterviewTypeEnum.stakeholder_interview:
                        result['content'] = InterviewDetailContentEnum.contact_detail
                        result['button'] = []
                    return result
                else:
                    result = {
                        "head": InterviewDetailHeadEnum.one_to_one_not_start_or_ongoing.value,
                        "content": InterviewDetailContentEnum.contact_detail.value,
                        "button": [InterviewDetailButtonEnum.access_interview.value]
                    }
                    # 线下辅导不展示进入视频按钮
                    if obj.place_type == ProjectInterviewPlaceTypeEnum.offline.value:
                        result['button'].remove(InterviewDetailButtonEnum.update_interview_time.value)

                    if not obj.coach_record_status and is_interview_update_date_button(obj):
                        result['button'].append(InterviewDetailButtonEnum.update_interview_time.value)
                return result

        # 完成
        elif obj.public_attr.status != ATTR_STATUS_INTERVIEW_CANCEL and obj.public_attr.end_time < datetime.datetime.now():
            if not obj.coachee_record_status and not obj.coach_record_status:  # 双方未完成
                if is_coach:
                    result = {
                        "head": InterviewDetailHeadEnum.one_to_one_finish.value,
                        "content": InterviewDetailContentEnum.not_show_detail.value,
                        "button": [InterviewDetailButtonEnum.write_record.value,
                                   InterviewDetailButtonEnum.update_interview_time.value,
                                   InterviewDetailButtonEnum.invite_customer_write.value]
                    }
                    if obj.type == ProjectInterviewTypeEnum.chemical_interview:
                        result['button'].remove(InterviewDetailButtonEnum.invite_customer_write.value)
                        if not is_interview_update_date_button(obj):
                            result['button'].remove(InterviewDetailButtonEnum.update_interview_time.value)

                    elif obj.type == ProjectInterviewTypeEnum.stakeholder_interview:
                        result['button'].remove(InterviewDetailButtonEnum.invite_customer_write.value)
                        if not is_interview_update_date_button(obj):
                            result['button'].remove(InterviewDetailButtonEnum.update_interview_time.value)
                    return result
                else:
                    if obj.type == ProjectInterviewTypeEnum.formal_interview:
                        result = {
                            "head": InterviewDetailHeadEnum.one_to_one_finish.value,
                            "content": InterviewDetailContentEnum.not_show_detail.value,
                            "button": [InterviewDetailButtonEnum.write_record.value,
                                       InterviewDetailButtonEnum.update_interview_time.value]
                        }
                        # 教练形式等于影子观察，客户不展示“填写记录”按钮
                        if obj.interview_subject == InterviewSubjectEnum.shadow_observation.value:
                            result['button'].remove(InterviewDetailButtonEnum.write_record.value)

                    elif obj.type == ProjectInterviewTypeEnum.stakeholder_interview:
                        result = {
                            "head": InterviewDetailHeadEnum.one_to_one_finish.value,
                            "content": InterviewDetailContentEnum.contact_detail.value,
                            "button": []
                        }
                        if is_interview_update_date_button(obj):
                            result['button'].append(InterviewDetailButtonEnum.update_interview_time.value)
                    else:  # 化学面谈
                        result = {
                            "head": InterviewDetailHeadEnum.one_to_one_finish.value,
                            "content": InterviewDetailContentEnum.not_show_detail.value,
                            "button": [InterviewDetailButtonEnum.write_chemical_interview.value]
                        }
                        if is_interview_update_date_button(obj):
                            result['button'].append(InterviewDetailButtonEnum.update_interview_time.value)
                    return result

            if obj.coachee_record_status and not obj.coach_record_status:  # 教练未完成
                if is_coach:
                    result = {
                        "head": InterviewDetailHeadEnum.one_to_one_finish.value,
                        "content": InterviewDetailContentEnum.not_show_detail.value,
                        "button": [InterviewDetailButtonEnum.write_record.value]
                    }
                    return result
                else:
                    result = {
                        "head": InterviewDetailHeadEnum.one_to_one_finish.value,
                        "content": InterviewDetailContentEnum.show_questionnaire_detail.value if
                        obj.record_type == InterviewRecordTypeEnum.questionnaire else InterviewDetailContentEnum.show_question_and_answer_detail.value,
                        "button": [InterviewDetailButtonEnum.edit_record.value]
                    }
                    if obj.type == ProjectInterviewTypeEnum.stakeholder_interview:
                        result['content'] = InterviewDetailContentEnum.contact_detail
                        result['button'] = [InterviewDetailButtonEnum.update_interview_time.value]
                    return result

            if not obj.coachee_record_status and obj.coach_record_status:  # 被教练者未完成
                if is_coach:
                    result = {
                        "head": InterviewDetailHeadEnum.one_to_one_finish.value,
                        "content": InterviewDetailContentEnum.show_questionnaire_detail.value if
                        obj.record_type == InterviewRecordTypeEnum.questionnaire else InterviewDetailContentEnum.show_question_and_answer_detail.value,
                        "button": [InterviewDetailButtonEnum.edit_record.value,
                                   InterviewDetailButtonEnum.invite_customer_write.value]
                    }
                    if obj.type == ProjectInterviewTypeEnum.chemical_interview:
                        result['button'].remove(InterviewDetailButtonEnum.invite_customer_write.value)
                    elif obj.type == ProjectInterviewTypeEnum.stakeholder_interview:
                        result['button'].remove(InterviewDetailButtonEnum.invite_customer_write.value)
                    return result
                else:
                    if obj.type == ProjectInterviewTypeEnum.formal_interview:
                        result = {
                            "head": InterviewDetailHeadEnum.one_to_one_finish.value,
                            "content": InterviewDetailContentEnum.not_show_detail.value,
                            "button": [InterviewDetailButtonEnum.write_record.value]
                        }
                        # 教练形式等于影子观察，客户不展示“填写记录”按钮
                        if obj.interview_subject == InterviewSubjectEnum.shadow_observation.value:
                            result['button'].remove(InterviewDetailButtonEnum.write_record.value)

                    elif obj.type == ProjectInterviewTypeEnum.stakeholder_interview:
                        result = {
                            "head": InterviewDetailHeadEnum.one_to_one_finish.value,
                            "content": InterviewDetailContentEnum.contact_detail.value,
                            "button": []
                        }
                    else:
                        result = {
                            "head": InterviewDetailHeadEnum.one_to_one_finish.value,
                            "content": InterviewDetailContentEnum.not_show_detail.value,
                            "button": [InterviewDetailButtonEnum.write_chemical_interview.value]
                        }
                    return result

            if obj.coachee_record_status and obj.coach_record_status:  # 完成
                if is_coach:
                    result = {
                        "head": InterviewDetailHeadEnum.one_to_one_finish.value,
                        "content": InterviewDetailContentEnum.show_questionnaire_detail.value if
                        obj.record_type == 1 else InterviewDetailContentEnum.show_question_and_answer_detail.value,
                        "button": [InterviewDetailButtonEnum.edit_record.value]
                    }
                    return result
                else:
                    result = {
                        "head": InterviewDetailHeadEnum.one_to_one_finish.value,
                        "content": InterviewDetailContentEnum.show_questionnaire_detail.value if
                        obj.record_type == 1 else InterviewDetailContentEnum.show_question_and_answer_detail.value,
                        "button": [InterviewDetailButtonEnum.edit_record.value]
                    }
                    return result

    return 1


def get_opposite_question(question_id, role):
    if role in [1, 3]:
        related_question = RelatedQuestion.objects.filter(coach_question_id=question_id,
                                                          template_type=TemplateTypeEnum.coach.value,
                                                          deleted=False)
        if related_question.exists():
            opposite_question_id = related_question.first().coachee_question_id
            return opposite_question_id, related_question.first().description

    else:
        related_question = RelatedQuestion.objects.filter(coachee_question_id=question_id,
                                                          template_type=TemplateTypeEnum.coachee.value,
                                                          deleted=False)
        if related_question.exists():
            opposite_question_id = related_question.first().coach_question_id
            return opposite_question_id, related_question.first().description
    return None, None


class InterviewRecordReportDetailSerializer(serializers.ModelSerializer):
    start_time = serializers.DateTimeField(source='public_attr.start_time', format='%Y-%m-%d %H:%M')
    end_time = serializers.DateTimeField(source='public_attr.end_time', format='%Y-%m-%d %H:%M')
    project_id = serializers.IntegerField(source='public_attr.project_id')
    status = serializers.IntegerField(source='public_attr.status')
    times = serializers.IntegerField(help_text='约谈时长-分钟')
    question_detail = serializers.SerializerMethodField(help_text='题目详情')
    place = serializers.CharField(help_text='辅导地点')
    isRecord = serializers.SerializerMethodField(help_text='是否填写辅导记录')
    detail_status = serializers.SerializerMethodField(help_text='详情状态')
    isAppraise = serializers.SerializerMethodField(help_text='是否评价')
    record_type = serializers.IntegerField(help_text='辅导记录类型')
    coach_info = serializers.SerializerMethodField()
    coachee_info = serializers.SerializerMethodField()
    place_category = serializers.IntegerField(help_text='辅导类型')
    state_describe = serializers.SerializerMethodField()
    topic = serializers.SerializerMethodField()
    fillin_status = serializers.SerializerMethodField()
    coaching_status = serializers.SerializerMethodField()
    real_income = serializers.SerializerMethodField(help_text='实际收入')
    pay_amount = serializers.SerializerMethodField(help_text='支付金额')
    service_charge = serializers.SerializerMethodField(help_text='平台服务费')
    tax_amount = serializers.SerializerMethodField(help_text='税费')
    discount_amount = serializers.SerializerMethodField(help_text='代金劵价格')
    original_price = serializers.SerializerMethodField(help_text='原价')
    detail_text = serializers.SerializerMethodField(help_text='提示')
    activity = serializers.SerializerMethodField(help_text='活动数据')
    personal_activity = serializers.SerializerMethodField(help_text='教练活动数据')
    group_coach_type = serializers.SerializerMethodField(help_text='集体辅导类型')
    prompt = serializers.SerializerMethodField(help_text='提示信息')

    class Meta:
        model = ProjectInterview
        fields = ['id', 'start_time', 'end_time', 'topic', 'times', 'question_detail', 'place', 'isRecord', 'detail_status',
                  'isAppraise', 'record_type', 'coach_info', 'coachee_info', 'place_category', 'state_describe',
                  'project_id', 'is_coach_agree', 'close_reason', 'status', 'fillin_status', 'coaching_status', 'type',
                  'detail_text', 'pay_amount', 'real_income', 'activity', 'group_coach_type', 'prompt', 'personal_activity',
                  'service_charge', 'discount_amount', 'original_price', 'tax_amount'
                  ]

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self._cache = {}

    # 计算或检索订单详情缓存
    def calculate_order_details(self, obj):
        if 'order_details' not in self._cache:
            if obj.order:
                raw_order_details = coach_public.calculate_coach_income_details(obj)
                # 数据库单位是分，展示时改为元
                order_details = [Decimal(str(item)) / Decimal('100').quantize(Decimal('0.00')) for item in
                                 raw_order_details]
                self._cache['order_details'] = order_details
            else:
                self._cache['order_details'] = [None, None, None, None, None]  # 默认值
        return self._cache['order_details']

    def get_prompt(self, obj):
        return interview_public.get_interview_prompt(obj, self.context.get('role'))

    def get_group_coach_type(self, obj):
        return interview_public.get_group_coach_type(obj)

    def get_activity(self, obj):
        return interview_public.get_activity_data(obj)

    def get_personal_activity(self, obj):
        return interview_public.get_personal_activity_data(obj)

    def get_service_charge(self, obj):
        if obj.order:
            mp = self.context.get('mp')
            service_scale, real_income, tax_amount, service_charge, tax_point = self.calculate_order_details(obj)
            if mp and compare_version(mp.get('version'), '2.31') < 0:
                return service_charge + tax_amount
            return service_charge
        return

    def get_discount_amount(self, obj):
        if obj.order:
            return interview_public.get_interview_to_order_discount_amount(obj)

    def get_original_price(self, obj):
        if obj.order:
            return interview_public.get_interview_to_order_original_price(obj)

    def get_real_income(self, obj):
        if obj.order:
            service_scale, real_income, tax_amount, service_charge, tax_point = self.calculate_order_details(obj)
            return real_income
        return

    def get_tax_amount(self, obj):
        service_scale, real_income, tax_amount, service_charge, tax_point = self.calculate_order_details(obj)
        return tax_amount

    def get_pay_amount(self, obj):
        if obj.order:
            return interview_public.get_interview_to_order_original_price(obj) - interview_public.get_interview_to_order_discount_amount(obj)

    def get_detail_text(self, obj):
        role = int(self.context.get('role', 0))
        return detail_text(role, obj.pk)

    def get_fillin_status(self, obj):
        return obj.get_fillin_status

    def get_coaching_status(self, obj):
        return obj.get_coaching_status

    def get_topic(self, obj):
        return interview_public.get_interview_topic(self.context.get('role'), obj)

    def get_state_describe(self, obj):
        role = int(self.context.get('role', 0))
        return get_interview_state_describe(role, obj)

    def get_coachee_info(self, obj):
        return get_interview_coachee(obj.pk)

    def get_coach_info(self, obj):
        role = self.context.get('role')
        return get_interview_coach(obj.pk, role)

    def get_isAppraise(self, obj):
        if obj.record_type == 1:
            if CoachAppraise.objects.filter(interview=obj, deleted=False).exists():
                return 1
            return 0
        else:
            if ProjectInterviewRecord.objects.filter(deleted=False, interview=obj).exists():
                return 1
            return 0

    def get_isRecord(self, obj):
        if not obj.coachee_record_status:
            return False
        else:
            return True

    def get_question_detail(self, obj):
        if obj.public_attr.project_id:
            total_template_type = TotalTemplateTypeEnum.project_one_to_one.value
        else:
            total_template_type = TotalTemplateTypeEnum.one_to_one_tutor.value

        answers = InterviewRecordTemplateAnswer.objects.filter(interview_id=obj.pk).first()
        if answers:
            template = answers.question.template
            total_template = TotalTemplate.objects.filter(
                (Q(coach_template=template) | Q(coachee_template=template)),
                type=total_template_type,
                write_role=InterviewRecordTemplateRoleEnum.coach_student.value).first()
        else:
            total_template = TotalTemplate.objects.filter(
                write_role=InterviewRecordTemplateRoleEnum.coach_student.value,
                type=total_template_type, status=1
                ).first()
        data = get_report_question(total_template.pk, obj.pk)

        return data

    def get_detail_status(self, obj):
        if 'role' in self.context:
            role = int(self.context['role'])
            result = detail_status(role, obj.pk)
            return result


def get_report_question(total_template_id, interview_id):
    total_template = TotalTemplate.objects.get(pk=total_template_id)
    total_template_report = TotalTemplateReport.objects.filter(
        total_template_id=total_template_id)
    order = Case(*[When(pk=order, then=pos) for pos, order in enumerate(eval(total_template.questions_order))])
    total_template_report = total_template_report.order_by(order)
    data = []
    for report in total_template_report:
        report_data = {'title': report.title, 'title_type': report.title_type, 'description': report.description,
                       'coach_data': [], 'coachee_data': []}
        if report.related_question_id:
            related_question = report.related_question
            if related_question.coach_question_id:
                question = InterviewRecordTemplateQuestionSerializers(related_question.coach_question)
                question = [question.data]
                question_data = add_edit_data(question, interview_id)
                report_data['coach_data'] = question_data

            if related_question.coachee_question_id:
                question = InterviewRecordTemplateQuestionSerializers(related_question.coachee_question)
                question = [question.data]
                question_data = add_edit_data(question, interview_id)
                report_data['coachee_data'] = question_data
        data.append(report_data)
    return data


def get_question_answer(obj_id, question_id, is_echo=False, is_coach_task=False):
    question = InterviewRecordTemplateQuestion.objects.get(pk=question_id)
    if not is_coach_task:
        project_interview = ProjectInterview.objects.get(pk=obj_id)
        answer = InterviewRecordTemplateAnswer.objects.filter(interview_id=project_interview.id,
                                                              public_attr=project_interview.public_attr,
                                                              question_id=question.id)
    else:
        coach_task = CoachTask.objects.get(pk=obj_id)
        answer = InterviewRecordTemplateAnswer.objects.filter(public_attr=coach_task.public_attr,
                                                              question_id=question.id)
    # 评分
    if question.type == InterviewRecordTemplateQuestionTypeEnum.rating.value:
        if answer.exists():
            data = {'id': answer.first().id, 'score': answer.first().score,
                    'rating_type': answer.first().question.rating_type}
            return data
        else:
            return {}

    # 多选
    if question.type == InterviewRecordTemplateQuestionTypeEnum.multiple.value:
        if answer.exists():
            data = []
            for ans in answer:
                if not is_echo:
                    result = {'option': ans.option_id} if not ans.option_custom else \
                        {'option': ans.option_id, 'option_custom': ans.option_custom}
                else:
                    result = {'option': ans.option_id, 'option_text': ans.option.title} if not ans.option_custom else \
                        {'option': ans.option_id, 'option_text': ans.option.title, 'option_custom': ans.option_custom}
                data.append(result)
            return data
        else:
            return []

    # 单选
    if question.type == InterviewRecordTemplateQuestionTypeEnum.single.value:
        if answer.exists():
            if not is_echo:
                data = [{'option': answer.first().option_id} if not answer.first().option_custom else
                        {'option': answer.first().option_id, 'option_custom': answer.first().option_custom}]
            else:
                data = [{'option': answer.first().option_id, 'option_text': answer.first().option.title} if not \
                            answer.first().option_custom else
                        {'option': answer.first().option_id, 'option_text': answer.first().option.title,
                         'option_custom': answer.first().option_custom}]
            return data
        else:
            return []

    if question.type == InterviewRecordTemplateQuestionTypeEnum.blank.value:
        # 成长笔记
        if question.answer_type == InterviewRecordTemplateQuestionAnswerTypeEnum.growth_notes.value:
            if answer.exists():
                relationship = answer.first().object_relationship.filter(type=ObjectTypeEnum.diary.value,
                                                                         deleted=False).first()
                if relationship:
                    diary_id = relationship.obj_id
                    diary = Diary.objects.get(pk=diary_id)
                    data = {"id": answer.first().id, "content": diary.content}
                    return data
            else:
                return {}

        # 习惯养成
        if question.answer_type == InterviewRecordTemplateQuestionAnswerTypeEnum.habit_formation.value:
            if answer.exists():
                data = []
                for ans in answer:
                    relationship = ans.object_relationship.filter(type=ObjectTypeEnum.habit.value,
                                                                  deleted=False).first()
                    if relationship:
                        habit_id = relationship.obj_id
                        habit = Habit.objects.get(pk=habit_id)
                        data.append(
                            {'id': ans.pk, 'when': habit.when, 'stop': habit.stop,
                             'change': habit.change, 'start_date': habit.start_date.strftime('%Y-%m-%d'),
                             'end_date': habit.end_date.strftime('%Y-%m-%d')})
                return data
            else:
                return []

        # 行动计划
        if question.answer_type == InterviewRecordTemplateQuestionAnswerTypeEnum.action_plan.value:
            if answer.exists():
                data = []
                for ans in answer:
                    relationship = ans.object_relationship.filter(type=ObjectTypeEnum.action_plan.value,
                                                                  deleted=False).first()
                    if relationship:
                        action_plan_id = relationship.obj_id

                        action_plan = ActionPlan.objects.get(pk=action_plan_id)
                        data.append({'id': ans.pk, 'content': action_plan.content,
                                     'end_date': action_plan.end_date.strftime('%Y-%m-%d'),
                                     'start_date': action_plan.start_date.strftime('%Y-%m-%d')})
                return data
            else:
                return []

        # 普通文本框
        if question.answer_type == InterviewRecordTemplateQuestionAnswerTypeEnum.normal_text.value:
            if answer.exists():
                data = {'id': answer.first().pk, 'content': answer.first().answer}
                return data
            else:
                return {}

        # 项目信息
        if question.answer_type == InterviewRecordTemplateQuestionAnswerTypeEnum.project_info.value:
            if answer.exists():
                relationship = answer.first().object_relationship.filter(
                    type=ObjectTypeEnum.project_note.value, deleted=False).first()
                if relationship:
                    project_note_id = relationship.obj_id
                    project_note = ProjectNote.objects.filter(pk=project_note_id).first()
                    if project_note:
                        data = {"id": answer.first().id, "content": project_note.content}
                        return data
            else:
                return {}

        # 公司信息
        if question.answer_type == InterviewRecordTemplateQuestionAnswerTypeEnum.company_info.value:
            if answer.exists():
                data = {'id': answer.first().pk, 'content': answer.first().answer}
                return data
            else:
                return {}

        # 客户信息
        if question.answer_type == InterviewRecordTemplateQuestionAnswerTypeEnum.customer_info.value:
            if answer.exists():
                data = {'id': answer.first().pk, 'content': answer.first().answer}
                return data
            else:
                return {}

        # 多条文本框
        if question.answer_type == InterviewRecordTemplateQuestionAnswerTypeEnum.multiple_text.value:
            if answer.exists():
                data = {'id': answer.first().pk, 'content': eval(answer.first().answer)}
                return data
            else:
                return {}

        # 能力标签
        if question.answer_type == InterviewRecordTemplateQuestionAnswerTypeEnum.power_tag.value:
            if answer.exists():
                data = {"id": answer.first().pk, "content": eval(answer.first().answer)}
                return data
            else:
                return {}
    return None


class AppTodoListCoachTaskViewSerializers(CoachTaskSerializer):
    coach_name = serializers.CharField(source='public_attr.user.cover_name', help_text='教练姓名')
    coachee_name = serializers.CharField(source='public_attr.target_user.cover_name', help_text='被教练者姓名')
    name = serializers.SerializerMethodField(help_text='名称')
    end_time = serializers.SerializerMethodField(help_text='利益相关者访谈结束日期')

    def get_name(self, obj):
        if obj.stakeholder_interview_module.filter(deleted=False).exists():
            return '利益相关者访谈报告'
        else:
            return '教练任务'

    def get_end_time(self, obj):
        stakeholder_interview_module = obj.stakeholder_interview_module.filter(deleted=False)
        if stakeholder_interview_module.exists():
            return stakeholder_interview_module.first().end_date.strftime('%Y-%m-%d %H:%M:%S')


def create_interview(start_time, end_time, coach_user_id, user_id, topic, place=None):
    """
    c端创建辅导
    """
    coach_user = User.objects.get(pk=coach_user_id)
    user = User.objects.get(pk=user_id)
    public_attr = PublicAttr.objects.create(
        start_time=start_time, end_time=end_time, user_id=coach_user.pk, target_user_id=user.pk,
        type=ATTR_TYPE_INTERVIEW, status=3)
    Schedule.objects.create(public_attr_id=public_attr.pk, type=ScheduleTypeEnum.interview, title=topic)
    start_time = datetime.datetime.strptime(start_time, '%Y-%m-%d %H:%M:%S')
    end_time = datetime.datetime.strptime(end_time, '%Y-%m-%d %H:%M:%S')
    times = round((end_time - start_time).seconds / 60)
    project_interview = ProjectInterview.objects.create(public_attr_id=public_attr.pk,
                                                        type=INTERVIEW_TYPE_COACHING,
                                                        is_coach_agree=False,
                                                        record_type=InterviewRecordTypeEnum.questionnaire,
                                                        place_category=1,
                                                        coachee_topic=topic,
                                                        topic=topic,
                                                        times=times)

    return project_interview

def get_interview_state_describe(role, obj):
    text = '辅导' if obj.type == ProjectInterviewTypeEnum.formal_interview else '访谈'
    if obj.public_attr.status == ATTR_STATUS_INTERVIEW_CANCEL:
        return f'{text}已取消...'
    if not obj.is_coach_agree:
        if role in [UserRoleEnum.coach, UserRoleEnum.trainee_coach]:
            return '辅导待确认...'
        else:
            return '教练确认中...'
    if obj.public_attr.start_time > datetime.datetime.now():
        return f'{text}未开始...'
    if obj.public_attr.start_time < datetime.datetime.now() < obj.public_attr.end_time:
        return f'{text}进行中...'
    return


def get_interview_remind_text(obj, role):
    # 只有教练返回描述
    if role in [UserRoleEnum.coachee, UserRoleEnum.trainee_coachee]:
        return

    # 问答模式不返回
    if obj.record_type == InterviewRecordTypeEnum.question_and_answer:
        return

    # 问卷模式，集体辅导不返回
    if obj.type == ProjectInterviewTypeEnum.formal_interview:
        coachee_name = interview_public.get_interview_coachee_nickname(obj)
        return f"正在填写与{coachee_name}的辅导记录"
    elif obj.type == ProjectInterviewTypeEnum.chemical_interview:
        return f"正在填写与{obj.public_attr.target_user.cover_name}的化学面谈记录"
    elif obj.type == ProjectInterviewTypeEnum.stakeholder_interview:
        return f"正在填写与{obj.public_attr.target_user.cover_name}的利益相关者访谈记录"
    return


def is_interview_update_date_button(obj):
    if obj.type == ProjectInterviewTypeEnum.chemical_interview:
        chemical_interview = obj.chemical_interview.filter(deleted=False).first()
        if chemical_interview:
            if chemical_interview.chemical_interview_module.end_time < datetime.datetime.now().date():
                return False

    elif obj.type == ProjectInterviewTypeEnum.stakeholder_interview:
        stakeholder_interview = obj.stakeholder_interview.filter(deleted=False).first()
        if stakeholder_interview:
            if stakeholder_interview.stakeholder_interview_module.end_date < datetime.datetime.now().date():
                return False
    return True
