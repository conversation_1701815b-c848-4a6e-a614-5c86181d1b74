from rest_framework import serializers

from wisdom_v2.enum.coach_enum import NonPlatformInterviewSourceEnum
from wisdom_v2.models_file.coach import NonPlatformInterview
from wisdom_v2.common import interview_public

class AppCoachInterviewDataSerializer(serializers.ModelSerializer):
    """
    教练辅导数据序列化
    """
    start_time = serializers.DateTimeField(help_text='开始时间', format='%Y-%m-%d %H:%M:%S')
    end_time = serializers.DateTimeField(help_text='结束时间', format='%Y-%m-%d %H:%M:%S')
    interview_time = serializers.SerializerMethodField(help_text='辅导时间')
    hour = serializers.SerializerMethodField(help_text='辅导时长')
    is_update = serializers.SerializerMethodField(help_text='是否可以编辑辅导记录信息')
    is_platform_interview = serializers.SerializerMethodField(help_text='是否是平台内辅导')
    data_type = serializers.SerializerMethodField(help_text='数据标识')
    user_count = serializers.SerializerMethodField(help_text='客户数量')
    place = serializers.SerializerMethodField(help_text='辅导地址')
    interview_type_describe = serializers.SerializerMethodField(help_text='辅导类型描述')


    def get_user_count(self, obj):
        # 默认有一位客户
        if not obj.user_count:
            return 1
        return obj.user_count

    def get_data_type(self, obj):
        return NonPlatformInterviewSourceEnum.coach_interview.value

    def get_interview_time(self, obj):
        return f"{obj.start_time.strftime('%Y.%m.%d %H:%M')} - {obj.end_time.strftime('%H:%M')}"

    def get_is_update(self, obj):
        return True

    def get_is_platform_interview(self, obj):
        return False

    def get_hour(self, obj):
        return int(obj.hour) if obj.hour.is_integer() else obj.hour
    
    def get_place(self, obj):
        # 非平台辅导暂时没有填写地址信息
        return '无'

    def get_interview_type_describe(self, obj):
        interview_types = {1: '一对一辅导', 2: '团队辅导'}
        return interview_types.get(obj.type, '')
    
    class Meta:
        model = NonPlatformInterview
        fields = ('id', 'type', 'interview_time', 'user_count', 'hour', 'start_time', 'end_time', 'topic', 'data_type',
                  'customer_name', 'customer_email', 'pay_type', 'is_update', 'is_platform_interview', 'place', 'interview_type_describe')


