import datetime
from drf_yasg import openapi
from drf_yasg.utils import swagger_auto_schema
from rest_framework.decorators import action
from django.db.models import Q

from rest_framework import serializers
from rest_framework import viewsets

from wisdom_v2.enum.user_enum import UserR<PERSON>Enum
from wisdom_v2.models import Diary, ProjectInterviewRecord, CapacityTag, PublicAttr, User, Project, SignIn

from utils.pagination import StandardResultsSetPagination
from utils.api_response import success_response, parameter_error_response
from wisdom_v2.views.constant import ROLE_COACHEE, DIARY_FROM_RECORD, ROLE_COACH, ATTR_TYPE_DIARY, DIARY_FROM_SELF


class DiarySerializer(serializers.ModelSerializer):
    capacity = serializers.SerializerMethodField()
    created_at = serializers.DateTimeField(format='%Y-%m-%d %H:%M')

    class Meta:
        model = Diary
        exclude = ('deleted', 'updated_at', 'creator_role', 'public_attr')

    def get_capacity(self, obj):
        try:
            if obj.interview:
                capacity = CapacityTag.objects.get(interview_id=obj.interview.pk, select_type=1, deleted=False)
                return capacity.title
        except CapacityTag.DoesNotExist:
            return


class DiaryViewSet(viewsets.ModelViewSet):
    queryset = Diary.objects.filter(deleted=False).order_by('-created_at')
    serializer_class = DiarySerializer

    @swagger_auto_schema(
        operation_id='app被教/教练 成长笔记列表',
        operation_summary='app被教/教练 成长笔记列表',
        manual_parameters=[
            openapi.Parameter('user_role', openapi.IN_QUERY, description='用户角色', type=openapi.TYPE_NUMBER,
                              required=True),
            openapi.Parameter('user_id', openapi.IN_QUERY, description='用户id', type=openapi.TYPE_NUMBER,
                              required=True),
            openapi.Parameter('page', openapi.IN_QUERY, description='第几页', type=openapi.TYPE_NUMBER),
            openapi.Parameter('page_size', openapi.IN_QUERY, description='每页显示条数默认15条', type=openapi.TYPE_NUMBER)
        ],
        tags=['app被教成长笔记']
    )
    def list(self, request, *args, **kwargs):
        try:
            role = int(request.headers.get('role'), 0)
            queryset = self.get_queryset().filter(public_attr__user__pk=request.query_params.get('user_id'))
            if role == UserRoleEnum.trainee_coachee:
                queryset = queryset.filter(public_attr__project__isnull=True)
            elif role == UserRoleEnum.coachee:
                queryset = queryset.filter(public_attr__project__isnull=False)
        except Exception as e:
            return parameter_error_response(str(e))
        # 分页
        paginator = StandardResultsSetPagination()
        page_list = paginator.paginate_queryset(queryset, self.request)
        serializer = self.serializer_class(page_list, many=True, context={'queryset': queryset})
        response = paginator.get_paginated_response(serializer.data)
        return success_response(response, request=request)


    @swagger_auto_schema(
        operation_id='app被教成长笔记详情',
        operation_summary='app被教成长笔记详情',
        manual_parameters=[
            openapi.Parameter('diary_id', openapi.IN_QUERY, description='成长笔记id', type=openapi.TYPE_NUMBER, required=True)
        ],
        tags=['app被教成长笔记']
    )
    @action(methods=['get'], detail=False, url_path='detail')
    def diary_detail(self, request, *args, **kwargs):
        try:
            instance = Diary.objects.get(pk=request.query_params.get('diary_id', 0), deleted=False)
        except Diary.DoesNotExist:
            return parameter_error_response('成长笔记id错误')
        serializer = self.get_serializer(instance)
        return success_response(serializer.data, request=request)


    @swagger_auto_schema(
        operation_id='app被教/教练 添加成长笔记',
        operation_summary='app被教/教练 添加成长笔记',
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'user_id': openapi.Schema(type=openapi.TYPE_NUMBER, description='用户id'),
                'user_role': openapi.Schema(type=openapi.TYPE_NUMBER, description='用户角色'),
                'project_id': openapi.Schema(type=openapi.TYPE_NUMBER, description='项目id 用户角色为签约教练、普通被教练者时必传'),
                'content': openapi.Schema(type=openapi.TYPE_STRING, description='内容'),
            }
        ),
        tags=['app被教成长笔记']
    )
    def create(self, request, *args, **kwargs):
        try:
            user_id = request.data.get('user_id')
            user_role = int(request.headers.get('role'))
            project_id = request.data.get('project_id')
            content = request.data.get('content')
            user = User.objects.get(pk=user_id, deleted=False)
        except (ValueError, TypeError):
            return parameter_error_response()
        except User.DoesNotExist:
            return parameter_error_response('用户不存在')
        if user_role in [1, 3]:  # 签约教练 个人教练
            if user_role == 1:  # 签约教练
                project = Project.objects.get(pk=project_id, deleted=False)
                public_attr = PublicAttr.objects.create(project=project, type=ATTR_TYPE_DIARY, user=user)
                Diary.objects.create(content=content, type=DIARY_FROM_SELF, creator_role=4,
                                     public_attr=public_attr)
            else:  # 个人教练
                public_attr = PublicAttr.objects.create(type=ATTR_TYPE_DIARY, user=user)
                Diary.objects.create(content=content, type=DIARY_FROM_SELF, creator_role=4,
                                     public_attr=public_attr)
        else:  # 普通被教练者 三无学员
            if user_role == 2:  # 普通被教练者
                project = Project.objects.get(pk=project_id, deleted=False)
                public_attr = PublicAttr.objects.create(project=project, type=ATTR_TYPE_DIARY, user=user)
                Diary.objects.create(content=content, type=DIARY_FROM_SELF, creator_role=6,
                                     public_attr=public_attr)
            else:  # 三无学员
                public_attr = PublicAttr.objects.create(type=ATTR_TYPE_DIARY, user=user)
                Diary.objects.create(content=content, type=DIARY_FROM_SELF, creator_role=6,
                                     public_attr=public_attr)

        return success_response(request=request)

    @swagger_auto_schema(
        operation_id='app被教修改成长笔记',
        operation_summary='app被教修改成长笔记',
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'diary_id': openapi.Schema(type=openapi.TYPE_NUMBER, description='成长笔记id'),
                'content': openapi.Schema(type=openapi.TYPE_STRING, description='内容'),
                'status': openapi.Schema(type=openapi.TYPE_NUMBER, description='1: 进行中 2:已完成 3:已取消'),
            }
        ),
        tags=['app被教成长笔记']
    )
    @action(methods=['post'], detail=False, url_path='update')
    def diary_update(self, request, *args, **kwargs):
        try:
            instance = Diary.objects.get(pk=request.data.get('diary_id'), deleted=False)
        except Diary.DoesNotExist:
            return parameter_error_response('成长笔记不存在')
        data = request.data.copy()
        serializer = self.get_serializer(instance, data=data, partial=True)
        serializer.is_valid(raise_exception=True)
        serializer.save()
        return success_response(serializer.data, request=request)

    @swagger_auto_schema(
        operation_id='app被教删除成长笔记',
        operation_summary='app被教删除成长笔记',
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'diary_id': openapi.Schema(type=openapi.TYPE_NUMBER, description='成长笔记id'),
            }
        ),
        tags=['app被教成长笔记']
    )
    @action(methods=['post'], detail=False, url_path='delete')
    def diary_delete(self, request, *args, **kwargs):
        try:
            instance = Diary.objects.get(pk=request.data.get('diary_id'), deleted=False)
        except Diary.DoesNotExist:
            return parameter_error_response('成长笔记不存在')
        instance.deleted = True
        instance.save()
        return success_response(request=request)


