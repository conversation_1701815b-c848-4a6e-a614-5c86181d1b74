import datetime
import json
import re

import numpy as np
import pendulum
from django.db import transaction
from django.db.models.functions import TruncDate

from drf_yasg import openapi
from drf_yasg.utils import swagger_auto_schema
from rest_framework.decorators import action
from django.db.models import Q, F

from rest_framework import viewsets

from wisdom_v2.enum.user_enum import UserRoleEnum
from utils.miniapp_version_judge import compare_version
from utils.utc_date_time import get_total_time, datetime_change_utc
from wisdom_v2.app_views.app_coach_schedule_actions import ScheduleSerializer
from wisdom_v2.models import Coach, Schedule, PublicAttr, User, Project, MobileSchedule
from utils.pagination import StandardResultsSetPagination
from utils.api_response import success_response, parameter_error_response
from wisdom_v2.views.constant import ATTR_TYPE_SCHEDULE, ATTR_STATUS_INTERVIEW_CANCEL, \
    ATTR_TYPE_INTERVIEW
from wisdom_v2.common import recurring_schedule_public
from wisdom_v2.enum.project_interview_enum import ProjectInterviewPlaceCategoryEnum, ProjectInterviewTypeEnum
from wisdom_v2.enum.service_content_enum import ScheduleTypeEnum, ScheduleApplyTypeEnum
from wisdom_v2.common import schedule_public
from wisdom_v2.common.recurring_schedule_public import date_in_recurring_schedule
from wisdom_v2.models_file import RecurringSchedule


class ScheduleViewSet(viewsets.ModelViewSet):
    queryset = Schedule.objects.exclude(
        public_attr__status=ATTR_STATUS_INTERVIEW_CANCEL, public_attr__type=ATTR_TYPE_INTERVIEW
    ).order_by('public_attr__start_time')
    serializer_class = ScheduleSerializer

    @swagger_auto_schema(
        operation_id='app教练日历列表',
        operation_summary='app教练日历列表',
        manual_parameters=[
            openapi.Parameter('user_id', openapi.IN_QUERY, description='用户id', type=openapi.TYPE_NUMBER,
                              required=True),
            openapi.Parameter('date_time', openapi.IN_QUERY, description='时间', type=openapi.TYPE_STRING,
                              required=True),
            openapi.Parameter('status', openapi.IN_QUERY, description='状态（1:上滑 2:下滑）', type=openapi.TYPE_NUMBER),
            openapi.Parameter('page', openapi.IN_QUERY, description='第几页', type=openapi.TYPE_NUMBER),
            openapi.Parameter('page_size', openapi.IN_QUERY, description='每页显示条数默认15条', type=openapi.TYPE_NUMBER)
        ],
        tags=['app教练日历']
    )
    def list(self, request, *args, **kwargs):
        try:
            user_id = request.query_params.get('user_id')
            date_time = request.query_params.get('date_time')
            status = int(request.query_params.get('status', 1))
            page = int(request.query_params.get('page', 1))
            page_size = int(request.query_params.get('page_size', 15))
            date_time = datetime.datetime.strptime(date_time, "%Y-%m-%d %H:%M")
            mp = json.loads(request.headers.get('mp')) if request.headers.get('mp') else None
        except Exception as e:
            return parameter_error_response('参数错误')

        # 非重复的日程
        queryset = self.get_queryset().filter(recurring_schedule__isnull=True)

        # 低于2.15.2版本不返回跨天日程
        if mp and compare_version(mp.get('version'), '2.15.2') < 0:
            queryset = queryset.annotate(
                start_date=TruncDate('public_attr__start_time'),
                end_date=TruncDate('public_attr__end_time')
            ).filter(start_date=F('end_date'))
        # 低于2.16版本不返回化学面谈和集体辅导
        if mp and compare_version(mp.get('version'), '2.16') < 0:
            queryset = queryset.exclude(
                type=ScheduleTypeEnum.interview,
                public_attr__interview_public_attr__type=ProjectInterviewTypeEnum.chemical_interview).exclude(
                type=ScheduleTypeEnum.interview,
                public_attr__interview_public_attr__place_category=ProjectInterviewPlaceCategoryEnum.offline_group_coach)
        if mp and compare_version(mp.get('version'), '2.16.2') < 0:
            queryset = queryset.exclude(
                type=ScheduleTypeEnum.interview,
                public_attr__interview_public_attr__type=ProjectInterviewTypeEnum.stakeholder_interview)

        non_recurring_schedules = []
        if status == 1:
            queryset = queryset.filter(
                public_attr__user_id=user_id,
                public_attr__end_time__gt=date_time,
                deleted=False)
            # 重复的日程计算结束时间
            recurring_schedules = RecurringSchedule.objects.filter(
                Q(end_repeat_date__isnull=True) |
                Q(end_repeat_date__gt=date_time),
                schedule__public_attr__user_id=user_id,
                schedule__deleted=False,
                deleted=False
            )
        else:
            queryset = queryset.filter(
                public_attr__user_id=user_id,
                public_attr__start_time__lt=date_time,
                deleted=False
            ).exclude(
                # 排除当天进行中的日程
                public_attr__start_time__date=date_time.date(),
                public_attr__end_time__date=date_time.date(),
                public_attr__start_time__lte=date_time,
                public_attr__end_time__gte=date_time,
            )
            # 重复的日程计算开始时间
            recurring_schedules = RecurringSchedule.objects.filter(
                schedule__public_attr__start_time__lt=date_time,
                schedule__public_attr__user_id=user_id, schedule__deleted=False, deleted=False)

        for item in queryset.all():
            # 跨天的日程进行拆分
            item = schedule_public.excerpt_schedule(item, start_time=date_time if status == 1 else None,
                                                    end_time=date_time if status == 2 else None)
            non_recurring_schedules.extend(schedule_public.split_schedule(item))

        # 重复日程进行拆分
        repeated_schedules = []
        for item in recurring_schedules:
            instances = recurring_schedule_public.generate_repeated_instances(
                item, date_time, status=status, max_instances=page_size * page)
            repeated_schedules.extend(instances)

        # 将两部分数据合并并排序
        all_schedules = non_recurring_schedules + repeated_schedules
        if status == 1:
            all_schedules.sort(key=lambda r: r.public_attr.start_time)
        else:
            all_schedules.sort(key=lambda r: r.public_attr.start_time, reverse=True)

        # 分页
        paginator = StandardResultsSetPagination()
        page_list = paginator.paginate_queryset(all_schedules, self.request)
        serializer = ScheduleSerializer(page_list, many=True)
        response = paginator.get_paginated_response(serializer.data)

        return success_response(response, request)

    @swagger_auto_schema(
        operation_id='app教练日历列表首屏',
        operation_summary='app教练日历列表',
        manual_parameters=[
            openapi.Parameter('user_id', openapi.IN_QUERY, description='用户id', type=openapi.TYPE_NUMBER,
                              required=True),
            openapi.Parameter('date_time', openapi.IN_QUERY, description='时间', type=openapi.TYPE_STRING,
                              required=True),
            openapi.Parameter('page_size', openapi.IN_QUERY, description='每页显示条数默认15条', type=openapi.TYPE_NUMBER)
        ],
        tags=['app教练日历']
    )
    @action(methods=['get'], detail=False, url_path='first_page')
    def firstpage(self, request, *args, **kwargs):
        try:
            user_id = request.query_params.get('user_id')
            date_time = request.query_params.get('date_time')
            size = int(request.query_params.get('page_size'))
            date_time = datetime.datetime.strptime(date_time, "%Y-%m-%d %H:%M")
            mp = json.loads(request.headers.get('mp')) if request.headers.get('mp') else None
        except Exception as e:
            return parameter_error_response(str(e))

        queryset = self.get_queryset().filter(recurring_schedule__isnull=True)
        # 低于2.15.2版本不返回跨天日程
        if mp and compare_version(mp.get('version'), '2.15.2') < 0:
            queryset = queryset.annotate(
                start_date=TruncDate('public_attr__start_time'),
                end_date=TruncDate('public_attr__end_time')
            ).filter(start_date=F('end_date'))
        # 低于2.16版本不返回化学面谈和集体辅导
        if mp and compare_version(mp.get('version'), '2.16') < 0:
            queryset = queryset.exclude(
                type=ScheduleTypeEnum.interview,
                public_attr__interview_public_attr__type=ProjectInterviewTypeEnum.chemical_interview).exclude(
                type=ScheduleTypeEnum.interview,
                public_attr__interview_public_attr__place_category=ProjectInterviewPlaceCategoryEnum.offline_group_coach)
        if mp and compare_version(mp.get('version'), '2.16.2') < 0:
            queryset = queryset.exclude(
                type=ScheduleTypeEnum.interview,
                public_attr__interview_public_attr__type=ProjectInterviewTypeEnum.stakeholder_interview)

        # 计算传入时间后的日程数量
        after = queryset.filter(
            public_attr__user_id=user_id,
            public_attr__end_time__gt=date_time,
            deleted=False)
        # 跨天日程切片处理
        after_schedules = []
        for after_item in after.all():
            after_item = schedule_public.excerpt_schedule(after_item, start_time=date_time)
            after_schedules.extend(schedule_public.split_schedule(after_item))

        after_recurring_schedules = RecurringSchedule.objects.filter(
            Q(end_repeat_date__isnull=True) |
            Q(end_repeat_date__gt=date_time),
            schedule__public_attr__user_id=user_id, schedule__deleted=False, deleted=False)
        # 重复日程进行拆分
        for item in after_recurring_schedules:
            instances = recurring_schedule_public.generate_repeated_instances(
                item, date_time, status=1, max_instances=size + 1)
            after_schedules.extend(instances)

        after_schedules.sort(key=lambda r: r.public_attr.start_time)
        after_next = len(after_schedules) > size
        # 构建一个唯一标识集合，用于识别已经存在的日程
        existing_identifiers = set(
            (sch.public_attr.start_time, sch.public_attr.end_time, getattr(sch, 'main_id', sch.id), sch.type) for sch in after_schedules
        )

        # 计算传入时间之前的日程
        prev = queryset.filter(
            public_attr__user_id=user_id,
            public_attr__start_time__lt=date_time,
            deleted=False
        ).exclude(
                # 排除当天进行中的日程
                public_attr__start_time__date=date_time.date(),
                public_attr__end_time__date=date_time.date(),
                public_attr__start_time__lte=date_time,
                public_attr__end_time__gte=date_time,
            )
        # 跨天日程切片处理
        prev_schedules = []
        for prev_item in prev.all():
            prev_item = schedule_public.excerpt_schedule(prev_item, end_time=date_time)
            prev_schedules.extend(schedule_public.split_schedule(prev_item))

        # 重复的日程计算开始时间
        prev_recurring_schedules = RecurringSchedule.objects.filter(
            schedule__public_attr__start_time__lt=date_time,
            schedule__public_attr__user_id=user_id, schedule__deleted=False, deleted=False)
        for item in prev_recurring_schedules:
            # 默认向下寻找
            instances = recurring_schedule_public.generate_repeated_instances(
                item, date_time, status=2, max_instances=size + 1)
            # 过滤掉已存在的实例
            unique_instances = [inst for inst in instances if (
                inst.public_attr.start_time, inst.public_attr.end_time, getattr(inst, 'main_id', inst.id), inst.type) not in existing_identifiers]
            prev_schedules.extend(unique_instances)
        prev_schedules.sort(key=lambda r: r.public_attr.start_time, reverse=True)
        prev_next = len(prev_schedules) > size

        after_data = ScheduleSerializer(after_schedules[:size], many=True).data
        prev_data = ScheduleSerializer(prev_schedules[:size], many=True).data
        response = list(reversed(prev_data)) + after_data
        return success_response({'data': response, 'after_next': after_next, 'prev_next': prev_next}, request)

    @swagger_auto_schema(
        operation_id='app添加教练日历',
        operation_summary='app添加教练日历',
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'user_id': openapi.Schema(type=openapi.TYPE_NUMBER, description='用户id'),
                'project_id': openapi.Schema(type=openapi.TYPE_NUMBER, description='项目id'),
                'title': openapi.Schema(type=openapi.TYPE_STRING, description='主题'),
                'remark': openapi.Schema(type=openapi.TYPE_STRING, description='备注'),
                'is_open_manage': openapi.Schema(type=openapi.TYPE_BOOLEAN, description='管理员是否可查看'),
                'start_time': openapi.Schema(type=openapi.TYPE_STRING, description='开始时间'),
                'end_time': openapi.Schema(type=openapi.TYPE_STRING, description='结束时间'),
                'type': openapi.Schema(type=openapi.TYPE_INTEGER, description='日程类型 1-不可预约时间段 4-可预约时间段'),
                'repeat_type': openapi.Schema(type=openapi.TYPE_INTEGER, description='重复类型 1-每日 2-每周 3-每月'),
                'end_repeat_date': openapi.Schema(type=openapi.TYPE_STRING, description='结束日期'),
                'apply_type': openapi.Schema(type=openapi.TYPE_STRING, description='日程适用类型'),
            }
        ),
        tags=['app教练日历']
    )
    def create(self, request, *args, **kwargs):
        try:
            user_id = request.data.get('user_id')
            project_id = request.data.get('project_id')
            title = request.data.get('title')
            remark = request.data.get('remark')
            is_open_manage = request.data.get('is_open_manage', True)
            is_all_day = request.data.get('is_all_day', False)
            start_time_str = request.data.get('start_time')
            end_time_str = request.data.get('end_time')
            schedule_type = request.data.get('type', 1)
            repeat_type = request.data.get('repeat_type')
            end_repeat_date = request.data.get('end_repeat_date')
            apply_type = request.data.get('apply_type')
            user = User.objects.get(pk=user_id, deleted=False)
            if project_id:
                if not Project.objects.filter(pk=project_id, deleted=False).exists():
                    return parameter_error_response('项目不存在')
        except (ValueError, TypeError):
            return parameter_error_response()
        except User.DoesNotExist:
            return parameter_error_response('用户不存在')

        if repeat_type and get_total_time(start_time_str, end_time_str).day != 0:
            return parameter_error_response('跨天日程不可以设置重复')

        # 转换时间类型，校验时间是否可用
        error, start_time, end_time = schedule_public.adjust_event_times(start_time_str, end_time_str, is_all_day)
        if error:
            return parameter_error_response(error)

        if not title:
            if schedule_type == ScheduleTypeEnum.available:
                title = '可预约日程'
            elif schedule_type == ScheduleTypeEnum.unavailable:
                title = '不可预约日程'
        elif len(title) > 50:
            return parameter_error_response('标题长度超过50限制')

        if remark and len(remark) > 800:
            return parameter_error_response('备注文本长度超过800限制')

        existing_schedule = schedule_public.detect_schedule_overlap(
            user_id, start_time, end_time, repeat_type, end_repeat_date, apply_type)
        if existing_schedule:
            start_time = existing_schedule.public_attr.start_time
            return parameter_error_response(f'与{start_time.strftime("%Y-%m-%d %H:%M")}的日程冲突')

        try:
            with transaction.atomic():
                public_attr = PublicAttr.objects.create(
                    project_id=project_id,
                    user=user,
                    type=ATTR_TYPE_SCHEDULE,
                    start_time=start_time, end_time=end_time)
                schedule = Schedule.objects.create(
                    is_all_day=is_all_day,
                    title=title, remark=remark, type=schedule_type,
                    is_open_manage=is_open_manage,
                    public_attr=public_attr,
                    apply_type=apply_type
                )

                if repeat_type:
                    RecurringSchedule.objects.create(
                        schedule=schedule, end_repeat_date=end_repeat_date,
                        repeat_type=repeat_type,
                    )

        except Exception as e:
            return parameter_error_response(str(e))
        schedule = {
            'id': schedule.pk,
            'start': public_attr.start_time,
            'end': public_attr.end_time,
            'title': schedule.title,
        }
        return success_response({'schedule': schedule}, request=request)


    @swagger_auto_schema(
        operation_id='app修改教练日历',
        operation_summary='app修改教练日历',
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'schedule_id': openapi.Schema(type=openapi.TYPE_NUMBER, description='用户id'),
                'schedule_time': openapi.Schema(type=openapi.TYPE_NUMBER, description='编辑的日程的时间'),
                'title': openapi.Schema(type=openapi.TYPE_STRING, description='主题'),
                'remark': openapi.Schema(type=openapi.TYPE_STRING, description='备注'),
                'is_open_manage': openapi.Schema(type=openapi.TYPE_BOOLEAN, description='管理员是否可查看'),
                'start_time': openapi.Schema(type=openapi.TYPE_STRING, description='开始时间'),
                'end_time': openapi.Schema(type=openapi.TYPE_STRING, description='结束时间'),
                'type': openapi.Schema(type=openapi.TYPE_INTEGER, description='日程类型 1-不可预约时间段 4-可预约时间段'),
                'repeat_type': openapi.Schema(type=openapi.TYPE_INTEGER, description='重复类型 1-每日 2-每周 3-每月'),
                'end_repeat_date': openapi.Schema(type=openapi.TYPE_STRING, description='结束日期'),
                'apply_type': openapi.Schema(type=openapi.TYPE_STRING, description='日程适用类型'),
                'update_type': openapi.Schema(type=openapi.TYPE_NUMBER,
                                           description='修改的类型 1-仅修改此日程，2-修改此日程及后续日程 3-修改所有日程'),
            }
        ),
        tags=['app教练日历']
    )
    @action(methods=['post'], detail=False, url_path='update')
    def update_coach_schedule(self, request, *args, **kwargs):
        try:
            schedule_id = request.data.get('schedule_id')
            schedule_time = request.data.get('schedule_time')
            remark = request.data.get('remark')
            is_all_day = request.data.get('is_all_day')
            start_time_str = request.data.get('start_time')
            end_time_str = request.data.get('end_time')
            repeat_type = request.data.get('repeat_type')
            end_repeat_date = request.data.get('end_repeat_date')
            update_type = request.data.get('update_type', 3)
            apply_type = request.data.get('apply_type')
            schedule = Schedule.objects.get(pk=schedule_id, deleted=False)
            user_id = request.user.id
        except (ValueError, TypeError):
            return parameter_error_response()
        except Schedule.DoesNotExist:
            return parameter_error_response('日程不存在')
        data = request.data.copy()

        if remark and len(remark) > 800:
            return parameter_error_response('备注文本长度超过800限制')

        if repeat_type and get_total_time(start_time_str, end_time_str).day != 0:
            return parameter_error_response('跨天日程不可以设置重复')

        # 转换时间类型，校验时间是否可用
        error, start_time, end_time = schedule_public.adjust_event_times(start_time_str, end_time_str, is_all_day)
        if error:
            return parameter_error_response(error)

        data['start_time'] = start_time
        data['end_time'] = end_time

        existing_schedule = schedule_public.detect_schedule_overlap(
            user_id, start_time, end_time, repeat_type, end_repeat_date, apply_type, excluded_schedule_id=schedule_id)
        if existing_schedule:
            start_time = existing_schedule.public_attr.start_time
            return parameter_error_response(f'与{start_time.strftime("%Y-%m-%d %H:%M")}的日程冲突')

        if schedule_time:
            schedule_time = datetime_change_utc(schedule_time).date()

        with transaction.atomic():
            recurring_schedule = RecurringSchedule.objects.filter(schedule=schedule, deleted=False).first()

            if recurring_schedule:

                if update_type == 1:  # 仅修改此日程

                    # 如果已有跳过日期列表，添加当前日期
                    if recurring_schedule.excluded_dates:
                        recurring_schedule.excluded_dates = recurring_schedule.excluded_dates.append(
                            schedule_time.strftime("%Y-%m-%d"))
                    else:
                        # 如果没有跳过日期列表，创建当前日期的列表
                        recurring_schedule.excluded_dates = [schedule_time.strftime("%Y-%m-%d")]
                    new_schedule = schedule_public.create_new_schedule_from_existing(
                        data, schedule, is_add_recurring_schedule=False)
                elif update_type == 2:  # 修改此日程及后续日程
                    # 如果修改的开始时间是原始日程的开始时间，删除原始日程和对应的重复日程。
                    if schedule.public_attr.start_time.date() == schedule_time:
                        schedule.deleted = True
                        recurring_schedule.deleted = True
                    else:
                        if recurring_schedule.end_repeat_date:
                            new_end_data = datetime_change_utc(recurring_schedule.end_repeat_date).subtract(
                                days=1).naive().date()
                            recurring_schedule.end_repeat_date = new_end_data
                        else:
                            new_end_data = pendulum.yesterday().naive().date()
                            recurring_schedule.end_repeat_date = new_end_data
                    new_schedule = schedule_public.create_new_schedule_from_existing(data, schedule)

                elif update_type == 3:  # 修改所有日程
                    # 创建新的日程
                    new_schedule = schedule_public.create_new_schedule_from_existing(data, schedule)
                    # 删除旧的日程
                    recurring_schedule.deleted = True
                    schedule.deleted = True
                schedule.save()
                recurring_schedule.save()

            else:
                # 排除一些自动生成的或不希望更新的字段
                excluded_fields = ['id', 'created_at', 'updated_at', 'deleted']
                fields = [field.name for field in Schedule._meta.fields if field.name not in excluded_fields]
                for field in fields:
                    if field in data:
                        setattr(schedule, field, data[field])
                schedule.save()
                new_schedule = schedule

                # 更新日程时间
                public_attr = schedule.public_attr
                public_attr.start_time = start_time
                public_attr.end_time = end_time
                public_attr.save()
                # 如果有重复类型，添加重复日程
                if repeat_type:
                    RecurringSchedule.objects.create(
                        schedule=new_schedule, end_repeat_date=end_repeat_date,
                        repeat_type=repeat_type,
                    )

        serializer = self.get_serializer(new_schedule)
        return success_response(serializer.data, request=request)

    @swagger_auto_schema(
        operation_id='app教练日历详情',
        operation_summary='app教练日历详情',
        manual_parameters=[
            openapi.Parameter('schedule_id', openapi.IN_QUERY, description='教练日历id',
                              type=openapi.TYPE_NUMBER, required=True),
            openapi.Parameter('schedule_time', openapi.IN_QUERY, description='日程日期',
                              type=openapi.TYPE_STRING, required=True)

        ],
        tags=['app教练日历']
    )
    @action(methods=['get'], detail=False, url_path='detail')
    def schedule_detail(self, request, *args, **kwargs):
        try:
            schedule_time = request.query_params.get('schedule_time')
            instance = Schedule.objects.get(pk=request.query_params.get('schedule_id'), deleted=False)
        except Schedule.DoesNotExist:
            return parameter_error_response('教练日历错误')

        # 如果查看的是重复日程，修改一下主日程时间
        if schedule_time and instance.recurring_schedule.filter(deleted=False).exists():
            schedule_time = datetime_change_utc(schedule_time)

            # 获取开始日期/结束日期
            now_start_time = datetime.datetime.combine(schedule_time.date(), instance.public_attr.start_time.time())
            now_end_time = datetime.datetime.combine(schedule_time.date(), instance.public_attr.end_time.time())

            # 修改时间（不保存）
            instance.public_attr.start_time = now_start_time
            instance.public_attr.end_time = now_end_time

        serializer = self.get_serializer(instance)
        return success_response(serializer.data, request=request)

    @swagger_auto_schema(
        operation_id='app教练日历删除',
        operation_summary='app教练日历删除',
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'schedule_id': openapi.Schema(type=openapi.TYPE_NUMBER, description='教练日历id'),
                'del_type': openapi.Schema(type=openapi.TYPE_NUMBER, description='删除类型 1-仅删除此日程，2-删除此日程及后续日程 3-删除所有日程'),
                'schedule_time': openapi.Schema(type=openapi.TYPE_NUMBER, description='删除日程的时间'),
            }
        ),
        tags=['app教练日历']
    )
    @action(methods=['post'], detail=False, url_path='delete')
    def schedule_delete(self, request, *args, **kwargs):
        try:
            del_type = request.data.get('del_type', 3)
            schedule_time = request.data.get('schedule_time')
            instance = Schedule.objects.get(pk=request.data.get('schedule_id'), deleted=False)
        except Schedule.DoesNotExist:
            return parameter_error_response('教练日历不存在')
        if schedule_time:
            schedule_time = datetime_change_utc(schedule_time).date()

        with transaction.atomic():
            recurring_schedule = RecurringSchedule.objects.filter(schedule=instance, deleted=False).first()
            if recurring_schedule:
                if del_type == 1:
                    excluded_dates = recurring_schedule.excluded_dates or []
                    excluded_dates.append(schedule_time.strftime('%Y-%m-%d'))
                    recurring_schedule.excluded_dates = excluded_dates
                elif del_type == 2:
                    recurring_schedule.end_repeat_date = schedule_time
                elif del_type == 3:
                    recurring_schedule.deleted = True
                recurring_schedule.save()

            if del_type == 3:
                instance.deleted = True
                instance.save()

            mobile_schedule = MobileSchedule.objects.filter(schedule=instance, deleted=False)
            if mobile_schedule:
                mobile_schedule_id = mobile_schedule.first().device_event_id
            else:
                mobile_schedule_id = None
        return success_response({'mobile_schedule_id': mobile_schedule_id}, request=request)

    @swagger_auto_schema(
        operation_id='app教练日历时间区间内是否有日程',
        operation_summary='app教练日历时间区间内是否有日程',
        manual_parameters=[
            openapi.Parameter('user_id', openapi.IN_QUERY, description='用户id',
                              type=openapi.TYPE_NUMBER, required=True),
            openapi.Parameter('start_date', openapi.IN_QUERY, description='开始日期',
                              type=openapi.TYPE_STRING, required=True),
            openapi.Parameter('end_date', openapi.IN_QUERY, description='结束日期',
                              type=openapi.TYPE_STRING, required=True),
        ],
        tags=['app教练日历']
    )
    @action(methods=['get'], detail=False, url_path='date_free')
    def date_free(self, request, *args, **kwargs):

        try:
            user_id = request.query_params['user_id']
            start_date = datetime.datetime.strptime(request.query_params['start_date'], '%Y-%m-%d')
            end_date = datetime.datetime.strptime(request.query_params['end_date'],
                                                  '%Y-%m-%d') + datetime.timedelta(days=1)
        except Exception as e:
            return parameter_error_response(str(e))
        if start_date > end_date:
            return parameter_error_response('起始时间大于结束时间')
        date_list = np.arange(start_date, end_date, dtype='datetime64[D]')
        res_list = []

        # 获取可能与此日期范围重叠的所有重复日程
        recurring_schedules = RecurringSchedule.objects.filter(
            Q(end_repeat_date__gte=start_date.date()) | Q(end_repeat_date__isnull=True),
            schedule__public_attr__start_time__date__lte=end_date.date(),
            schedule__public_attr__user__pk=user_id,
            deleted=False
        ).all()
        for date in date_list:
            data = {}
            date = date.astype(datetime.datetime)

            # 检查是否存在普通日程
            has_schedule = self.get_queryset().filter(
                Q(public_attr__start_time__date__lte=date, public_attr__end_time__date__gte=date),
                public_attr__user__pk=user_id,
                deleted=False
            ).exists()

            # 检查是否存在重复日程
            has_recurring_schedule = any(
                date_in_recurring_schedule(recurring, date) for recurring in recurring_schedules)
            data[date.strftime('%Y-%m-%d')] = has_schedule or has_recurring_schedule
            res_list.append(data)
        return success_response(res_list, request)

    @swagger_auto_schema(
        operation_id='具体时间在app教练日程中是否已占用的判断',
        operation_summary='具体时间在app教练日程中是否已占用的判断',
        manual_parameters=[
            openapi.Parameter('user_id', openapi.IN_QUERY, description='用户id',
                              type=openapi.TYPE_NUMBER, required=True),
            openapi.Parameter('apply_type', openapi.IN_QUERY, description='日程用途',
                              type=openapi.TYPE_NUMBER, required=True),
            openapi.Parameter('start_date', openapi.IN_QUERY, description='开始日期',
                              type=openapi.FORMAT_DATETIME, required=True),
            openapi.Parameter('end_date', openapi.IN_QUERY, description='结束日期',
                              type=openapi.FORMAT_DATETIME, required=True),
        ],
        tags=['app教练日历']
    )
    @action(methods=['get'], detail=False, url_path='date_time_free')
    def date_time_free(self, request, *args, **kwargs):
        """
        判断创建的辅导和教练的日程是否冲突, 目前管理后台创建辅导接口调用.
        """
        try:
            user_id = request.query_params.get('user_id')
            coach_id = request.query_params.get('coach_id')
            if coach_id:
                coach = Coach.objects.filter(pk=coach_id).first()
                if coach:
                    user_id = coach.user.pk
            apply_type = int(request.query_params.get('apply_type', ScheduleApplyTypeEnum.project.value))
            start_date = datetime.datetime.strptime(
                request.query_params.get('start_date'), '%Y-%m-%d %H:%M:%S')
            end_date = request.query_params.get('end_date')
            # 需要排除的辅导id
            interview_id = request.query_params.get('interview_id')
            User.objects.get(pk=user_id)
            if end_date and end_date[11:13] == '24':
                data, raw_time = str(end_date).split(' ')
                end_date = f'{data} {"00" + raw_time[2:]}'
                end_date = datetime.datetime.strptime(end_date, '%Y-%m-%d %H:%M:%S')
                end_date += datetime.timedelta(days=1)
            else:
                end_date = datetime.datetime.strptime(end_date, '%Y-%m-%d %H:%M:%S')

        except User.DoesNotExist:
            return parameter_error_response('用户不存在')
        except Exception as e:
            return parameter_error_response('请求参数错误')

        if start_date > end_date:
            return parameter_error_response('起始时间大于结束时间')

        # 获取教练可预约日程, 日程默认可预约
        time_slots = schedule_public.get_schedule_day_list(
            user_id, start_date, time_status=True, apply_type=apply_type, interview_id=interview_id)
        # 判断时间是否可用
        status = schedule_public.is_time_slot_available(time_slots, start_date, end_date)
        return success_response({'is_occupied': False if status else True})

    @swagger_auto_schema(
        operation_id='指定时间段内每天是否有可预约时间',
        operation_summary='指定时间段内每天是否有可预约时间',
        manual_parameters=[
            openapi.Parameter('coach_user_id', openapi.IN_QUERY, description='用户id',
                              type=openapi.TYPE_NUMBER, required=True),
            openapi.Parameter('apply_type', openapi.IN_QUERY, description='日程用途',
                              type=openapi.TYPE_NUMBER, required=True),
            openapi.Parameter('start_date', openapi.IN_QUERY, description='开始日期',
                              type=openapi.FORMAT_DATETIME, required=True),
            openapi.Parameter('end_date', openapi.IN_QUERY, description='结束日期',
                              type=openapi.FORMAT_DATETIME, required=True),
        ],
        tags=['app教练日历']
    )
    @action(methods=['get'], detail=False, url_path='day_schedule_time', authentication_classes=[])
    def get_day_schedule_time(self, request, *args, **kwargs):
        try:
            coach_user_id = request.query_params.get('coach_user_id')
            apply_type = request.query_params.get('apply_type')
            is_coach_add_interview = int(request.query_params.get('is_coach_add_interview', 0))
            hour_str = request.query_params.get('hour', '1')
            interview_id = request.query_params.get('interview_id')
            is_delay = request.query_params.get('is_delay', 'true').lower()
            start_date_str = request.query_params.get('start_date')
            end_date_str = request.query_params.get('end_date')
            User.objects.get(id=coach_user_id, deleted=False)
            user_role = int(request.headers.get('role', 0))
        except User.DoesNotExist:
            return parameter_error_response('用户不存在')
        except Exception as e:
            return parameter_error_response('请求参数错误')

        if re.match(r'^\d+(\.[05])?$', hour_str):
            hour = float(hour_str)
        else:
            return parameter_error_response('辅导小时数只接受0或5结尾的小数参数错误')

        try:
            start_date = pendulum.parse(start_date_str).naive().date()
            end_date = pendulum.parse(end_date_str).naive().date()
        except ValueError:
            return parameter_error_response("无效的日期格式")

        if start_date > end_date:
            return parameter_error_response("开始日期不应晚于结束日期")

        if apply_type:
            apply_type = int(apply_type)

        # 获取可用时间是默认全天不可用 教练创建辅导-默认全天可预约
        time_status = bool(is_coach_add_interview)
        # 教练请求并且有辅导 id 说明需要所有时间都可以预约
        if user_role in [UserRoleEnum.coach.value, UserRoleEnum.trainee_coach.value] and interview_id:
            time_status = True
        all_schedule_data, all_schedule_hour = schedule_public.get_schedule_time_list(
            coach_user_id, start_date, end_date, apply_type=apply_type, time_status=time_status,
            interval_time_minutes=int(hour * 60), interview_id=interview_id)
        return success_response(all_schedule_data)


class NewScheduleViewSet(viewsets.ModelViewSet):
    """
    2.30版本新增新版日程接口, 处理可预约日程和已预约辅导
    """

    # 返回没有重复日程的基础查询集
    queryset = Schedule.objects.filter(
        recurring_schedule__isnull=True,
        type__in=[ScheduleTypeEnum.available.value, ScheduleTypeEnum.interview.value, ScheduleTypeEnum.company_interview.value],
    ).exclude(
        public_attr__status=ATTR_STATUS_INTERVIEW_CANCEL,
        public_attr__type=ATTR_TYPE_INTERVIEW
    ).order_by('public_attr__start_time')
    serializer_class = ScheduleSerializer

    @swagger_auto_schema(
        operation_id='指定时间段每天是否有日程数据',
        operation_summary='指定时间段每天是否有日程数据',
        manual_parameters=[
            openapi.Parameter('user_id', openapi.IN_QUERY, description='用户id',
                              type=openapi.TYPE_NUMBER, required=True),
            openapi.Parameter('start_date', openapi.IN_QUERY, description='开始日期',
                              type=openapi.FORMAT_DATETIME, required=True),
            openapi.Parameter('end_date', openapi.IN_QUERY, description='结束日期',
                              type=openapi.FORMAT_DATETIME, required=True),
        ],
        tags=['app教练日历']
    )
    @action(methods=['get'], detail=False, url_path='period')
    def get_coach_schedule_period(self, request, *args, **kwargs):
        try:
            user_id = request.query_params.get('user_id')
            start_date_str = request.query_params['start_date']
            end_date_str = request.query_params['end_date']
            User.objects.get(pk=user_id)
        except User.DoesNotExist:
            return parameter_error_response('用户不存在')
        except Exception as e:
            return parameter_error_response('请求参数错误')

        try:
            start_date = pendulum.parse(start_date_str).naive().date()
            end_date = pendulum.parse(end_date_str).naive().date()
        except ValueError:
            return parameter_error_response("无效的日期格式")

        if start_date > end_date:
            return parameter_error_response("开始日期不应晚于结束日期")

        data = schedule_public.get_time_interval_schedule_to_list(user_id, start_date, end_date)

        return success_response(data)


    @swagger_auto_schema(
        operation_id='app教练日历列表',
        operation_summary='app教练日历列表',
        manual_parameters=[
            openapi.Parameter('user_id', openapi.IN_QUERY, description='用户id', type=openapi.TYPE_NUMBER,
                              required=True),
            openapi.Parameter('date_time', openapi.IN_QUERY, description='时间', type=openapi.TYPE_STRING,
                              required=True),
            openapi.Parameter('status', openapi.IN_QUERY, description='状态（1:上滑 2:下滑）', type=openapi.TYPE_NUMBER),
            openapi.Parameter('page', openapi.IN_QUERY, description='第几页', type=openapi.TYPE_NUMBER),
            openapi.Parameter('page_size', openapi.IN_QUERY, description='每页显示条数默认15条', type=openapi.TYPE_NUMBER)
        ],
        tags=['app教练日历']
    )
    def list(self, request, *args, **kwargs):
        try:
            user_id = request.query_params.get('user_id')
            date_time = request.query_params.get('date_time')
            status = int(request.query_params.get('status', 1))
            page = int(request.query_params.get('page', 1))
            page_size = int(request.query_params.get('page_size', 15))
            date_time = datetime.datetime.strptime(date_time, "%Y-%m-%d %H:%M")
        except Exception as e:
            return parameter_error_response('参数错误')

        # 非重复的日程
        queryset = self.get_queryset()

        non_recurring_schedules = []
        if status == 1:
            queryset = queryset.filter(
                public_attr__user_id=user_id,
                public_attr__end_time__gt=date_time,
                deleted=False)
            # 重复的日程计算结束时间
            recurring_schedules = RecurringSchedule.objects.filter(
                Q(end_repeat_date__isnull=True) |
                Q(end_repeat_date__gt=date_time),
                schedule__public_attr__user_id=user_id,
                schedule__deleted=False,
                schedule__type=ScheduleTypeEnum.available.value,
                deleted=False
            )
        else:
            queryset = queryset.filter(
                public_attr__user_id=user_id,
                public_attr__start_time__lt=date_time,
                deleted=False
            ).exclude(
                # 排除当天进行中的日程
                public_attr__start_time__date=date_time.date(),
                public_attr__end_time__date=date_time.date(),
                public_attr__start_time__lte=date_time,
                public_attr__end_time__gte=date_time,
            )
            # 重复的日程计算开始时间
            recurring_schedules = RecurringSchedule.objects.filter(
                schedule__public_attr__start_time__lt=date_time,
                schedule__public_attr__user_id=user_id, schedule__deleted=False,
                schedule__type=ScheduleTypeEnum.available.value, deleted=False)

        for item in queryset.all():
            # 跨天的日程进行拆分
            item = schedule_public.excerpt_schedule(item, start_time=date_time if status == 1 else None,
                                                    end_time=date_time if status == 2 else None)
            non_recurring_schedules.extend(schedule_public.split_schedule(item))

        # 重复日程进行拆分
        repeated_schedules = []
        for item in recurring_schedules:
            instances = recurring_schedule_public.generate_repeated_instances(
                item, date_time, status=status, max_instances=page_size * page)
            repeated_schedules.extend(instances)

        # 将两部分数据合并并排序
        all_schedules = non_recurring_schedules + repeated_schedules

        if status == 1:
            all_schedules.sort(key=lambda r: r.public_attr.start_time)
        else:
            all_schedules.sort(key=lambda r: r.public_attr.start_time, reverse=True)

        # 分页
        paginator = StandardResultsSetPagination()
        page_list = paginator.paginate_queryset(all_schedules, self.request)
        serializer = ScheduleSerializer(page_list, many=True).data

        # 数据组合
        schedules, interview_schedules = schedule_public.split_schedules(serializer)
        data = schedule_public.get_merge_schedule_to_dict(schedules, interview_schedules, status)
        response = paginator.get_paginated_response(data)

        return success_response(response, request)

    @swagger_auto_schema(
        operation_id='app教练日历列表首屏',
        operation_summary='app教练日历列表',
        manual_parameters=[
            openapi.Parameter('user_id', openapi.IN_QUERY, description='用户id', type=openapi.TYPE_NUMBER,
                              required=True),
            openapi.Parameter('date_time', openapi.IN_QUERY, description='时间', type=openapi.TYPE_STRING,
                              required=True),
            openapi.Parameter('page_size', openapi.IN_QUERY, description='每页显示条数默认15条', type=openapi.TYPE_NUMBER)
        ],
        tags=['app教练日历']
    )
    @action(methods=['get'], detail=False, url_path='first_page')
    def schedule_first_page(self, request, *args, **kwargs):
        try:
            user_id = request.query_params.get('user_id')
            date_time = request.query_params.get('date_time')
            size = int(request.query_params.get('page_size'))
            date_time = datetime.datetime.strptime(date_time, "%Y-%m-%d %H:%M")
            mp = json.loads(request.headers.get('mp')) if request.headers.get('mp') else None
        except Exception as e:
            return parameter_error_response(str(e))

        queryset = self.get_queryset()

        # 2.41版本之前不返回企业面试数据
        if mp and compare_version(mp.get('version'), '2.41') < 0:
            queryset = queryset.exclude(
                type=ScheduleTypeEnum.company_interview)
            
        # 计算传入时间后的日程数量
        after = queryset.filter(
            public_attr__user_id=user_id,
            public_attr__end_time__gt=date_time,
            deleted=False)
        # 跨天日程切片处理
        after_schedules = []
        for after_item in after.all():
            after_item = schedule_public.excerpt_schedule(after_item, start_time=date_time)
            after_schedules.extend(schedule_public.split_schedule(after_item))

        after_recurring_schedules = RecurringSchedule.objects.filter(
            Q(end_repeat_date__isnull=True) |
            Q(end_repeat_date__gt=date_time),
            schedule__public_attr__user_id=user_id, schedule__deleted=False,
            schedule__type=ScheduleTypeEnum.available.value, deleted=False)
        # 重复日程进行拆分
        for item in after_recurring_schedules:
            instances = recurring_schedule_public.generate_repeated_instances(
                item, date_time, status=1, max_instances=size + 1)
            after_schedules.extend(instances)

        after_schedules.sort(key=lambda r: r.public_attr.start_time)
        after_next = len(after_schedules) > size
        # 构建一个唯一标识集合，用于识别已经存在的日程
        existing_identifiers = set(
            (sch.public_attr.start_time, sch.public_attr.end_time, getattr(sch, 'main_id', sch.id), sch.type) for sch in after_schedules
        )

        # 计算传入时间之前的日程
        prev = queryset.filter(
            public_attr__user_id=user_id,
            public_attr__start_time__lt=date_time,
            deleted=False
        ).exclude(
                # 排除当天进行中的日程
                public_attr__start_time__date=date_time.date(),
                public_attr__end_time__date=date_time.date(),
                public_attr__start_time__lte=date_time,
                public_attr__end_time__gte=date_time,
            )
        # 跨天日程切片处理
        prev_schedules = []
        for prev_item in prev.all():
            prev_item = schedule_public.excerpt_schedule(prev_item, end_time=date_time)
            prev_schedules.extend(schedule_public.split_schedule(prev_item))

        # 重复的日程计算开始时间
        prev_recurring_schedules = RecurringSchedule.objects.filter(
            schedule__public_attr__start_time__lt=date_time,
            schedule__public_attr__user_id=user_id, schedule__deleted=False,
            schedule__type=ScheduleTypeEnum.available.value, deleted=False)
        for item in prev_recurring_schedules:
            instances = recurring_schedule_public.generate_repeated_instances(
                item, date_time, status=2, max_instances=size + 1)
            prev_schedules.extend(instances)
        prev_schedules.sort(key=lambda r: r.public_attr.start_time, reverse=True)
        prev_next = len(prev_schedules) > size
        prev_schedules = prev_schedules[:size]

        # 数据组合
        after_data = ScheduleSerializer(after_schedules[:size], many=True).data
        after_schedules, after_interview_schedules = schedule_public.split_schedules(after_data)

        # 过滤掉已存在的实例
        unique_prev_schedules = [inst for inst in prev_schedules if (
            inst.public_attr.start_time, inst.public_attr.end_time, getattr(inst, 'main_id', inst.id),
            inst.type) not in existing_identifiers]
        prev_data = ScheduleSerializer(unique_prev_schedules, many=True).data
        prev_schedules, prev_interview_schedules = schedule_public.split_schedules(prev_data)

        raw_schedules_data = [*after_schedules, *prev_schedules]
        raw_interview_data = [*after_interview_schedules, *prev_interview_schedules]
        data = schedule_public.get_merge_schedule_to_dict(raw_schedules_data, raw_interview_data)

        return success_response({'data': data, 'after_next': after_next, 'prev_next': prev_next}, request)
