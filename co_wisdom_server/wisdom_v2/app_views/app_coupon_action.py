import datetime
import string

from random import sample
from django.conf import settings
from rest_framework import serializers
from wisdom_v2.models import Coupon
from wisdom_v2.enum.pay_enum import CouponStatusEnum
from utils.third_party_payment import ThirdPartyPayment

class AppCouponSerializer(serializers.ModelSerializer):

    class Meta:
        model = Coupon
        fields = "__all__"


def get_coupon_no():
    random_no = ''.join(sample(string.ascii_letters + string.digits, 15))
    time_str = datetime.datetime.now().strftime('%Y%m%d%H%M%S')
    coupon_no = f'{settings.MCHID}{time_str}{random_no}'
    return coupon_no


def send_coupon(stock, user, type):
    """
    发放优惠券
    """
    coupon_no = get_coupon_no()
    if type == 2:  # 满减券
        try:
            is_success, message = ThirdPartyPayment(user_id=user.pk, user_name=user.true_name).send_coupon(
                stock_id=stock.stock_id, openid=user.openid, out_request_no=coupon_no,
                stock_creator_mchid=settings.MCHID
            )
            if not is_success:
                return False, message
            coupon_id = message.get('coupon_id')
            # 查询当前发放代金券详情，并存入数据库
            is_success, message = ThirdPartyPayment(user_id=user.pk, user_name=user.true_name).coupon_detail(
                coupon_id=coupon_id, openid=user.openid
            )
            if not is_success:
                return False, message
        except:
            return False, '微信支付服务错误'

        # 将优惠券信息存入库中
        if not Coupon.objects.filter(coupon_id=coupon_id).exists():
            tmp_dict = {"SENDED": 1, "USED": 2, "EXPIRED": 3}
            coupon_name = message.get('coupon_name')
            status = tmp_dict[message.get('status')]
            description = message.get('description')
            data = {"stock_id": stock.pk, "coupon_no": coupon_no, "coupon_id": coupon_id, "user_id": user.pk,
                    "coupon_name": coupon_name, "status": status}
            if description:
                data['description'] = description
            Coupon.objects.create(**data)
    elif type == 1:  # 折扣券
        data = {
            "stock_id": stock.pk,
            "coupon_no": coupon_no,
            "user_id": user.pk,
            "coupon_name": stock.stock_name,
            "status": CouponStatusEnum.SENDED,
        }
        if stock.description:
            data['description'] = stock.description
        if not Coupon.objects.filter(stock_id=stock.pk, user_id=user.pk, status=CouponStatusEnum.SENDED).exists():
            Coupon.objects.create(**data)
    return True, None