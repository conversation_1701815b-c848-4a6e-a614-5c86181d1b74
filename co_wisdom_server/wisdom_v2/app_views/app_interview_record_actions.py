from rest_framework import serializers
from ..models import ProjectInterview, ProjectInterviewRecord, ActionPlan, Diary, Habit, CapacityTag,\
    ProjectNote, ProjectInterviewOffLineRecord
from ..utils import get_interview_record_detail

from .app_coachee_habit import HabitSerializer
from .app_coachee_action_plan import ActionPlanSerializer
from wisdom_v2.enum.user_enum import UserRoleEnum


class DiarySerializer(serializers.ModelSerializer):
    class Meta:
        model = Diary
        exclude = ('created_at', 'updated_at', 'deleted', 'interview', 'public_attr')


class ProjectNoteSerializers(serializers.ModelSerializer):
    class Meta:
        model = ProjectNote
        exclude = ('created_at', 'updated_at', 'interview', 'public_attr')


class CapacityTagSerializers(serializers.ModelSerializer):
    class Meta:
        model = CapacityTag
        exclude = ('created_at', 'updated_at', 'deleted', 'interview')


class AppInterviewRecordSerializers(serializers.ModelSerializer):


    class Meta:
        model = ProjectInterviewRecord
        exclude = ('created_at', 'updated_at', 'interview', 'leader_capacity', 'discover', 'coachee_thought')


class ProjectInterviewOffLineRecordSerializers(serializers.ModelSerializer):
    action_plan = serializers.SerializerMethodField()
    change = serializers.SerializerMethodField()
    coachee_thought = serializers.SerializerMethodField()
    coachee_change = serializers.SerializerMethodField()

    class Meta:
        model = ProjectInterviewOffLineRecord
        fields = ('resistance', 'input_score', 'satisfaction_score', 'action_plan', 'change', 'coachee_thought',
                  'coachee_change', 'coach_comment', 'coach_score')


    def get_action_plan(self, obj):
        action_plan = ActionPlan.objects.filter(interview=obj.interview, creator_role=6, deleted=False,
                                                is_problem_resolve=True)
        return ActionPlanSerializer(action_plan, many=True).data

    def get_change(self, obj):
        change = ActionPlan.objects.filter(interview=obj.interview, creator_role=6, deleted=False, is_problem_resolve=False)
        return ActionPlanSerializer(change, many=True).data

    def get_coachee_thought(self, obj):
        diary_queryset = Diary.objects.filter(deleted=False, interview=obj.interview, type=2)
        if diary_queryset:
            return diary_queryset.first().content

    def get_coachee_change(self, obj):
        coachee_change_queryset = Habit.objects.filter(creator_role=6, interview=obj.interview, deleted=False)
        return HabitSerializer(coachee_change_queryset, many=True).data


class AppInterviewRecordDetailSerializers(serializers.ModelSerializer):
    # 需要返回详细信息

    topic = serializers.SerializerMethodField()
    leader_capacity = serializers.SerializerMethodField()
    discover = serializers.SerializerMethodField()
    coachee_thought = serializers.SerializerMethodField()
    coachee_change = serializers.SerializerMethodField()
    action_plan = serializers.SerializerMethodField()
    coach_change = serializers.SerializerMethodField()
    coach_action_plan = serializers.SerializerMethodField()
    coach_leader_suggest = serializers.SerializerMethodField()
    project_note = serializers.SerializerMethodField()
    diary = serializers.SerializerMethodField()

    class Meta:
        model = ProjectInterviewRecord
        exclude = ('created_at', 'updated_at')

    def get_topic(self, obj):
        return obj.interview.message_topic


    def get_discover(self, obj):
        if obj.discover:
            return obj.discover.content


    def get_coachee_thought(self, obj):
        if obj.coachee_thought:
            return obj.coachee_thought.content


    def get_leader_capacity(self, obj):
        if obj.leader_capacity:
            return obj.leader_capacity.title


    def get_coachee_change(self, obj):
        coachee_change_queryset = Habit.objects.filter(creator_role=6, interview=obj.interview, deleted=False)
        return HabitSerializer(coachee_change_queryset, many=True).data


    def get_action_plan(self, obj):
        action_plan = ActionPlan.objects.filter(interview_id=obj.interview.pk, creator_role=6, deleted=False)
        return ActionPlanSerializer(action_plan, many=True).data

    def get_coach_change(self, obj):
        coach_change_queryset = Habit.objects.filter(creator_role=4, interview=obj.interview, deleted=False)
        return HabitSerializer(coach_change_queryset, many=True).data

    def get_coach_leader_suggest(self, obj):
        capacity_tag = CapacityTag.objects.filter(interview=obj.interview, select_type=2, deleted=False)
        return CapacityTagSerializers(capacity_tag, many=True).data

    def get_coach_action_plan(self, obj):
        action_plan = ActionPlan.objects.filter(interview_id=obj.interview.pk, creator_role=4, deleted=False)
        return ActionPlanSerializer(action_plan, many=True).data

    def get_project_note(self, obj):
        note = ProjectNote.objects.filter(interview=obj.interview, deleted=False)
        return ProjectNoteSerializers(note, many=True).data

    def get_diary(self, obj):
        diary_queryset = Diary.objects.filter(deleted=False, interview=obj.interview, creator_role=4)
        return DiarySerializer(diary_queryset, many=True).data


# 2.11.1 新线上一对一辅导辅导详情的序列化，
# 目前是管理后台（项目列表-辅导记录-辅导记录详情）（辅导明细-辅导列表-辅导记录详情）使用
class ProjectInterviewRecordSerializer(serializers.ModelSerializer):
    user_info = serializers.SerializerMethodField(help_text='用户信息')
    interview_time = serializers.SerializerMethodField(help_text='辅导时间')
    detail = serializers.SerializerMethodField(help_text="辅导详情")

    def get_user_info(self, obj):
        user_info = {
            "coach_name": obj.public_attr.user.cover_name,
            "coach_image_url": obj.public_attr.user.head_image_url,
            "coachee_name": obj.public_attr.target_user.cover_name,
            "coachee_image_url": obj.public_attr.target_user.head_image_url,
        }
        return user_info

    def get_detail(self, obj):
        if not obj.coach_record_status and not obj.coachee_record_status:
            return []
        return get_interview_record_detail(obj)

    def get_interview_time(self, obj):
        return obj.public_attr.start_time.strftime('%Y-%m-%d %H:%M') + '~' + obj.public_attr.end_time.strftime('%H:%M')

    class Meta:
        model = ProjectInterview
        fields = ['id', 'user_info', 'interview_time', 'detail']
