from rest_framework.views import APIView

from drf_yasg import openapi
from drf_yasg.utils import swagger_auto_schema
from utils.api_response import success_response, parameter_error_response

from ..models import PublicAttr, ProjectInterview
from utils.rtc_utils.rtc_token import get_rtc_token


class RtcView(APIView):
    authentication_classes = []

    @swagger_auto_schema(
        operation_id='获取会议信息',
        operation_summary='获取会议信息',
        manual_parameters=[
            openapi.Parameter('interview_id', openapi.IN_QUERY, description='约谈id', type=openapi.TYPE_NUMBER),
        ],
        tags=['会议相关']
    )
    def get(self, request, *args, **kwargs):
        try:
            interview = ProjectInterview.objects.get(pk=int(request.query_params.get('interview_id', 0)))
        except ProjectInterview.DoesNotExist:
            return parameter_error_response()
        res = get_rtc_token(str(interview.public_attr_id))
        data = {
            'interview_id': interview.pk,
            'channel_name': res.get('channel_name'),
            'token': res.get('token'),
            'expired': res.get('expired'),
        }
        return success_response(data)
