import datetime
import json

import pendulum
import redis
from django.db import transaction
from django.forms import model_to_dict

from drf_yasg import openapi
from drf_yasg.utils import swagger_auto_schema
from pypinyin import lazy_pinyin
from rest_framework.decorators import action
from django.db.models import Sum, Q, Avg
from django.conf import settings

from wisdom_v2.models_file.coach import DismissedTodoItem
from utils import randomPassword
from utils import task
from utils.miniapp_version_judge import compare_version
from utils.queryset import multiple_field_distinct
from utils import validate
from utils.resource import resume_resource
from wisdom_v2.views.constant import ATTR_TYPE_COACH_TASK, MANAGE_EVALUATION, INTERVIEW_TYPE_COACHING
from wisdom_v2 import utils
from wisdom_v2.enum.chemical_interview_enum import ChemicalInterviewStatusEnum, ChemicalInterviewCoachSourceEnum
from wisdom_v2.enum.project_interview_enum import DataType
from wisdom_v2.enum.service_content_enum import Coach<PERSON><PERSON>Enum, InterviewRecordTemplateRoleEnum, NewCoachTaskTypeEnum, \
    CoachOfferStatusEnum, \
    CustomerPortraitTypeEnum, ScheduleApplyTypeEnum, ProjectCoachStatusEnum, OneToOneMatchTypeEnum, \
    PersonalReportTypeEnum
from wisdom_v2.common import coachee_public, coach_public, customer_portrait_public, project_service_public, \
    interview_public, resume_public, project_coach_public, project_member_public, coach_task_public
from wisdom_v2.models import ProjectInterview, Project, ProjectMember, Coach, User, ProjectCoach, PublicAttr, \
    ProjectInterviewRecord, CoachTask, EvaluationReport, CoachOffer, CustomerPortrait, PersonalApply, WorkWechatUser, \
    UserContract, ProjectDocs, UserTmp, Resume, PersonalReport, ProjectBundle
from .app_coach_actions import ProjectCoachSerializers, CoachResumeSerializers, get_time_slot, get_time_point, \
    UserReportCoachTaskSerializer, CoachProjectMemberSerializer, AppCoachCustomerDetailsPortraitSerializer, \
    AppCoachCustomerPortraitBaseSerializer
from rest_framework import viewsets
from wisdom_v2.enum.project_enum import ProjectStatusEnum, ProjectDocsTypeEnum
from wisdom_v2.enum.user_enum import CoachUserTypeEnum, UserRoleEnum, PersonalApplyStatusEnum, PersonalApplyTypeEnum, \
    UserTmpEnum, CoachInternToPersonalStatus

from utils.pagination import StandardResultsSetPagination
from utils.api_response import success_response, parameter_error_response
from wisdom_v2.common.coach_public import get_random_trainee_coach_list
from wisdom_v2.app_views.app_project_docs_action import AppProjectDocsListDetailsSerializer
from wisdom_v2.views.company_member_actions import id_number_check
from ..enum.company_enum import CompanyDevelopmentStageEnum, CompanyScaleEnum, CompanyIndustryEnum, CompanyAttrEnum
from ..models_file import CoachToClientNotes, CoachAssociatedCompany

data_redis = redis.Redis.from_url(settings.DATA_REDIS)


def get_time_hours(user=None, coach=None, project=None):
    if user:
        args = {'public_attr__target_user_id': user.pk}
    else:
        args = {'public_attr__user_id': coach.pk, 'public_attr__project_id': project.pk}
    times = ProjectInterview.objects.filter(
        type=INTERVIEW_TYPE_COACHING,
        public_attr__type=1, deleted=False,
        public_attr__end_time__lt=datetime.datetime.now(),
        **args,).exclude(public_attr__status=6).aggregate(times_minute=Sum('times'))
    if times['times_minute']:
        interview_hour = round(times['times_minute'] / 60, 1)
    else:
        interview_hour = 0
    return interview_hour or 0


def get_target_user_score(coach, coachee_user, score_type):
    record = ProjectInterviewRecord.objects.filter(deleted=False, interview__public_attr__target_user=coachee_user)
    all_avg_score = record.aggregate(all_avg_score=Avg(score_type))['all_avg_score']
    avg_score = record.filter(interview__public_attr__user=coach).aggregate(score_avg=Avg(score_type))['score_avg']
    return round(avg_score, 1) if avg_score else 0, round(all_avg_score, 1) if all_avg_score else 0


class ProjectCoachViewSet(viewsets.ModelViewSet):
    queryset = Coach.objects.filter(deleted=False).order_by('-created_at')
    serializer_class = ProjectCoachSerializers

    @swagger_auto_schema(
        operation_id='项目可预约教练列表',
        operation_summary='项目可预约教练列表',
        manual_parameters=[
            openapi.Parameter('project_id', openapi.IN_QUERY, description='项目id', type=openapi.TYPE_NUMBER,
                              required=True),
            openapi.Parameter('date', openapi.IN_QUERY, description='日期', type=openapi.TYPE_STRING, required=True),
            openapi.Parameter('during', openapi.IN_QUERY, description='最小时长', type=openapi.TYPE_NUMBER),
            openapi.Parameter('page', openapi.IN_QUERY, description='第几页', type=openapi.TYPE_NUMBER),
            openapi.Parameter('page_size', openapi.IN_QUERY, description='每页显示条数默认15条', type=openapi.TYPE_NUMBER)
        ],
        tags=['app教练相关']
    )
    @action(methods=['get'], detail=False, url_path='free_coach')
    def free_coach(self, request, *args, **kwargs):
        # 当前时间往后推6个小时，没有教练在这之后有时间，那就是没有教练；
        # 如果推了6个小时以后是第二天，那同样当天也依然没有教练
        # 判定教练不展示就是教练没有空闲时间
        try:
            project_id = int(request.query_params.get('project_id', 0))
            date = request.query_params.get('date', None)
            apply_type = int(request.query_params.get('apply_type', ScheduleApplyTypeEnum.project.value))
            during = int(request.query_params.get('during', 30))
            date_param = datetime.datetime.strptime(date, '%Y-%m-%d')
            user_role = int(request.headers.get('role'))

        except (TypeError, ValueError):
            return parameter_error_response('请求参数错误')

        now_time = datetime.datetime.now()
        time_obj_6h = datetime.timedelta(hours=6)
        if now_time.minute > 30:
            now_time = now_time + datetime.timedelta(hours=1)
            now_time = now_time.replace(minute=0)
        elif 30 > now_time.minute > 0:
            now_time = now_time.replace(minute=30)
        # 如果是当天时间，往后推迟6个小时， 如果是明天 就直接从8点开始算
        if datetime.datetime.today().date() == date_param.date():
            start_date = now_time
        else:
            start_date = date_param + datetime.timedelta(hours=8)
        user_id = request.user.pk
        project_member = ProjectMember.objects.filter(project_id=project_id, user_id=user_id, deleted=False).first()
        if user_role == UserRoleEnum.coachee:  # 普通被教练者
            # 如果是项目用户，获取项目教练列表，没有则为空
            if not project_member:
                return parameter_error_response()
            project_coaches = project_coach_public.get_project_user_coach(project_member)
            if project_coaches and project_coaches.exists():
                coaches = Coach.objects.filter(
                    id__in=list(project_coaches.values_list('coach_id', flat=True))).order_by('created_at')
            else:
                coaches = []

        elif user_role == 4:  # 三无学员
            interview = PublicAttr.objects.filter(project__isnull=True, target_user_id=user_id, type=1).exclude(
                status=6)
            user_ids = list(interview.values_list('user_id', flat=True))
            coaches = Coach.objects.filter(user_id__in=user_ids, deleted=False).order_by('created_at')
        else:
            coaches = Coach.objects.filter(id=-1).order_by('created_at')

        today_date = datetime.datetime.today().date()
        data = []
        for coach in coaches:
            if today_date + datetime.timedelta(days=1) == start_date.date() and start_date.hour != 8:
                continue

            time_point = get_time_point(start_date.strftime('%Y-%m-%d %H:%M:%S'), during, coach.user.pk, apply_type=apply_type)
            if not time_point:
                continue
            time_point = datetime.datetime.strptime(time_point, '%Y-%m-%d %H:%M:%S').strftime('%H:%M')

            # 查询到定制化简历
            state, resume = coach_public.get_project_coach_resume(coach.user.id, project_id)
            if not state:
                continue
            domain = resume.coach_domain if resume.coach_domain else []
            coach_info = {
                'coach_id': coach.user.pk,
                'name': coach.user.cover_name,
                'head_image_url':  resume.head_image_url if resume.head_image_url else coach.user.head_image_url,
                'domain': domain[:3],
                'appointment_time': time_point,
                'resume_id': resume.id,
            }
            data.append(coach_info)
        # 分页
        paginator = StandardResultsSetPagination()
        page_list = paginator.paginate_queryset(data, self.request)
        response = paginator.get_paginated_response(page_list)

        return success_response(response, request=request)

    @swagger_auto_schema(
        operation_id='教练简历',
        operation_summary='教练简历',
        manual_parameters=[
            openapi.Parameter('coach_user_id', openapi.IN_QUERY, description='教练id', type=openapi.TYPE_NUMBER,
                              required=True),
        ],
        tags=['app教练相关']
    )
    @action(methods=['get'], detail=False, url_path='resume')
    def resume(self, request, *args, **kwargs):
        try:
            coach_user_id = int(request.query_params.get('coach_user_id', 0))
            coach_user = User.objects.get(pk=coach_user_id)
            coach = Coach.objects.get(user=coach_user, deleted=False)
        except User.DoesNotExist:
            return parameter_error_response('用户不存在')
        except Coach.DoesNotExist:
            return parameter_error_response('教练不存在')
        serializer = CoachResumeSerializers(coach)
        return success_response(serializer.data, request=request)

    @swagger_auto_schema(
        operation_id='教练可用时间段',
        operation_summary='教练可用时间段',
        manual_parameters=[
            openapi.Parameter('coach_user_id', openapi.IN_QUERY, description='教练id', type=openapi.TYPE_NUMBER,
                              required=True),
            openapi.Parameter('project_id', openapi.IN_QUERY, description='项目id', type=openapi.TYPE_NUMBER,
                              required=True),
            openapi.Parameter('during', openapi.IN_QUERY, description='最小时间段 30', type=openapi.TYPE_NUMBER),
            openapi.Parameter('is_delay', openapi.IN_QUERY, description='是否自动延时',
                              type=openapi.TYPE_BOOLEAN, default=True),
            openapi.Parameter('date', openapi.IN_QUERY, description='日期 2021-10-21', type=openapi.TYPE_STRING,
                              required=True),

        ],
        tags=['app教练相关']
    )
    @action(methods=['get'], detail=False, url_path='timeslot', authentication_classes=[])
    def time_slot(self, request, *args, **kwargs):
        try:
            coach_user_id = int(request.query_params.get('coach_user_id', 0))
            during = int(request.query_params.get('during', 30))
            date = request.query_params.get('date', None)
            interview_id = request.query_params.get('interview_id')
            coach_user = User.objects.get(pk=coach_user_id)
            date_param = datetime.datetime.strptime(date, '%Y-%m-%d')
            is_delay = request.query_params.get('is_delay', 'true').lower()
            is_coach_add_interview = int(request.query_params.get('is_coach_add_interview', 0))
            is_coach_add_interview = bool(is_coach_add_interview)
            apply_type = request.query_params.get('apply_type')
            mp = json.loads(request.headers.get('mp')) if request.headers.get('mp') else None
            user_role = int(request.headers.get('role', 0))  # 利益相关者可以不登录，role增加默认为0
        except User.DoesNotExist:
            return parameter_error_response('用户不存在')
        except Exception:
            return parameter_error_response()

        now_time = datetime.datetime.now()
        if now_time.minute > 30:
            now_time = now_time + datetime.timedelta(hours=1)
            now_time = now_time.replace(minute=0)
        elif 30 > now_time.minute > 0:
            now_time = now_time.replace(minute=30)

        # 教练创建辅导可以自定义时间，根据is_delay判断是否需要延时
        if datetime.datetime.today().date() == date_param.date() and is_delay == 'true':
            start_date = now_time
            end_date = now_time.date() + datetime.timedelta(days=1)
        else:
            start_date = date_param
            end_date = date_param + datetime.timedelta(days=1)

        if mp and compare_version(mp.get('version'), '2.30') < 0:
            # 当前登录用户是项目教练时，判断默认可用时间
            if user_role == UserRoleEnum.coach.value:
                # 教练创建辅导&修改辅导 is_delay==false，
                # 修改辅导时会有 interview_id
                # is_delay==false同时没有interview_id = 教练创建辅导
                if is_delay == 'false' and not interview_id:
                    time_status = True
                else:
                    time_status = False
            else:
                time_status = False
        else:
            # 获取可用时间是默认全天不可用 教练创建辅导-默认全天可预约
            time_status = bool(is_coach_add_interview)

        today_date = datetime.datetime.today().date()
        # 如果当前时间剩余不到30分钟，不展示可用时间
        if today_date + datetime.timedelta(days=1) == now_time.date():
            time_slot = []
        else:
            time_slot = get_time_slot(
                coach_user, start_date, end_date, during,
                time_status=time_status, interview_id=interview_id, apply_type=apply_type)
        return success_response(time_slot, request=request)

    @swagger_auto_schema(
        operation_id='教练端客户列表',
        operation_summary='教练端客户列表',
        manual_parameters=[
            openapi.Parameter('coach_user_id', openapi.IN_QUERY, description='教练id', type=openapi.TYPE_NUMBER,
                              required=True),
            openapi.Parameter('coachee_role', openapi.IN_QUERY, description='被教练者角色 0：全部，1：企业学员 2：见习学员',
                              type=openapi.TYPE_NUMBER, required=True, default=0)
        ],
        tags=['app教练相关']
    )
    @action(methods=['get'], detail=False, url_path='coachee_list')
    def coachee_list(self, request, *args, **kwargs):
        try:
            coach_user_id = int(request.query_params.get('coach_user_id', 0))
            coachee_role = int(request.query_params.get('coachee_role', 0))
            coach_user = User.objects.get(pk=coach_user_id)
            user_role = int(request.headers.get('role'))
            page_size = request.query_params.get('page_size')
        except User.DoesNotExist:
            return parameter_error_response('用户不存在')

        # 教练客户列表在项目教练自己创建辅导时也会用上，此时客户列表只返回项目客户，当时增加coachee_role参数做判断
        if coachee_role == 1:
            # 获取企业客户时，排除已归档的。
            coachee_id_list = set(ProjectCoach.objects.filter(
                coach__user=coach_user,
                member__user_project__is_forbidden=False,
                project__isnull=False,
                project_group_coach__isnull=True,
                deleted=False).distinct().values_list('member_id', flat=True))
            coachee_user_list = User.objects.filter(id__in=coachee_id_list, deleted=False).all()
            not_sorted_res = [coach_public.coach_customer_to_dict(item, coach_user_id, UserRoleEnum.coachee.value) for item in coachee_user_list]
            res = sorted(not_sorted_res, key=lambda i: lazy_pinyin(i['name']))
        else:
            res = coach_public.get_coach_to_coachee_list(coach_user, user_role)

        paginator = StandardResultsSetPagination()
        # 旧版小程序请求接口没有分页参数，当接口没分页参数时，默认一页999
        if not page_size:
            paginator.page_size = 999
        page_list = paginator.paginate_queryset(res, self.request)
        response = paginator.get_paginated_response(page_list)
        return success_response(response, request)

    @swagger_auto_schema(
        operation_id='教练端客户详情',
        operation_summary='教练端客户详情',
        manual_parameters=[
            openapi.Parameter('user_id', openapi.IN_QUERY, description='用户id(被教id)', type=openapi.TYPE_NUMBER,
                              required=True),
            openapi.Parameter('coach_user_id', openapi.IN_QUERY, description='教练id(教练id)', type=openapi.TYPE_NUMBER,
                              required=True),

        ],
        tags=['app教练相关']
    )
    @action(methods=['get'], detail=False, url_path='coachee_detail')
    def coachee_detail(self, request, *args, **kwargs):
        try:
            user_id = int(request.query_params.get('user_id', 0))
            coach_user_id = int(request.query_params.get('coach_user_id', 0))
            user = User.objects.get(pk=user_id)
            coach_user = User.objects.get(pk=coach_user_id)
            role = int(request.headers.get('role'))
        except Exception as e:
            return parameter_error_response('请求参数错误')

        # 获取备注
        notes = coach_public.get_coach_to_client_notes(coach_user_id, user_id)
        # 获取个人用户昵称和邮箱
        personal_user = coach_public.get_to_c_coachee_info(coach_user_id, user_id)
        # 没有昵称默认使用客户混淆后的真实姓名。
        coachee_name = personal_user.nickname if personal_user and personal_user.nickname else user.cover_name

        # 教练是否和用户加了好友，加了好友才返回用户的外部联系人id
        work_wechat_coachee_user = WorkWechatUser.objects.filter(
            external_user_id__isnull=False, user=user, deleted=False).first()
        work_wechat_coach_user = WorkWechatUser.objects.filter(
            wx_user_id__isnull=False, user=coach_user, deleted=False).first()
        external_user_id = None
        if work_wechat_coach_user:
            is_relevance, tmp_external_user_id = coach_public.work_wechat_coach_customer_info(
                work_wechat_coach_user, work_wechat_coachee_user, user)
            if is_relevance:
                external_user_id = tmp_external_user_id

        project_id = None
        project_name = None
        company = None
        company_id = None
        full_project_name = None
        # 即是C端用户又是B时用户，C端用户优先级高于B端用户
        # 在没有C端用户信息的时候，才查询项目信息
        if not personal_user:
            # 获取项目信息
            project = coach_public.get_coachee_project_obj(coach_user, user)
            if project:
                project_id = project.id
                project_name = project.name
                full_project_name = project.full_name
                if project.company:
                    company = project.company.real_name
                    company_id = project.company_id

        # 计算已完成的辅导小时和剩余辅导小时
        times_str = None
        if project_id:
            pm = ProjectMember.objects.filter(user=user, project=project, deleted=False).first()
            project_bundle = pm.project_bundle.filter(deleted=False).first()
            if project_bundle:
                online = project_bundle.one_to_one_coach.filter(type=CoachTypeEnum.online.value, deleted=False)
                if online.exists():
                    completed_hours = ProjectInterview.objects.filter(
                        place_category=1, type=1, public_attr__project=project,
                        public_attr__target_user=user, deleted=False,
                        public_attr__type=1).exclude(public_attr__status=6).aggregate(used_times=Sum('times'))
                    completed_hours = completed_hours.get('used_times', 0) if completed_hours.get('used_times', 0) else 0
                    completed_hours = round(completed_hours/60, 1)

                    total_hours = online.aggregate(total_times=Sum('online_time'))
                    total_hours = total_hours.get('total_times', 0) if total_hours.get('total_times', 0) else 0
                    remaining_hours = max(0, total_hours - completed_hours)

                    # 生成文字提醒
                    completed_hours_str = str(int(completed_hours)) if completed_hours.is_integer() else str(completed_hours)
                    if remaining_hours > 0:
                        remaining_hours_str = str(int(remaining_hours)) if remaining_hours.is_integer() else str(remaining_hours)
                        times_str = f"已辅导{completed_hours_str}小时,剩余{remaining_hours_str}小时"
                    else:
                        times_str = f"已辅导{completed_hours_str}小时"
        
        if not times_str:
            times = get_time_hours(user=user)
            times_str = str(int(times)) if times.is_integer() else str(times)
            times_str = f"已辅导{times_str}小时"

        user_info = {
            'name': coachee_name,
            'project_id': project_id,
            'head_image_url': user.head_image_url,
            'times': get_time_hours(user=user),
            'times_str': times_str,
            'notes': notes,
            'full_project_name': full_project_name,
            'coachee_email': personal_user.email if personal_user else None,
            'external_user_id': external_user_id
        }

        score_data = interview_public.get_total_interview_scores(coach_user_id=coach_user.id, coachee_user_id=user.id)
        # 从 score_data 中获取各项评分
        avg_target_progress_score = score_data['target_progress_avg_score']
        all_target_progress_score = score_data['target_progress_sum_score']
        avg_harvest_score = score_data['harvest_avg_score']
        all_harvest_score = score_data['harvest_sum_score']
        avg_satisfaction_score = score_data['satisfaction_avg_score']
        all_satisfaction_score = score_data['satisfaction_sum_score']

        user_info['target_progress_score'] = {'avg_score': avg_target_progress_score, 'all_score': all_target_progress_score}
        user_info['harvest_score'] = {'avg_score': avg_harvest_score, 'all_score': all_harvest_score}
        user_info['satisfaction_score'] = {'avg_score': avg_satisfaction_score, 'all_score': all_satisfaction_score}
        if company:
            user_info['company'] = company
            user_info['company_id'] = company_id
        if project_name:
            user_info['project'] = project_name
        return success_response(user_info, request)

    @swagger_auto_schema(
        operation_id='修改客户备注信息',
        operation_summary='修改客户备注信息',
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'user_id': openapi.Schema(type=openapi.TYPE_NUMBER, description='用户id'),
                'coach_user_id': openapi.Schema(type=openapi.TYPE_STRING, description='教练用户id'),
                'notes': openapi.Schema(type=openapi.TYPE_STRING, description='备注')
            }
        ),
        tags=['小程序用户相关']
    )
    @action(methods=['post'], detail=False, url_path='notes')
    def post_coach_to_client_notes(self, request, *args, **kwargs):

        try:
            user_id = request.data.get('user_id')
            coach_user_id = request.data.get('coach_user_id')
            notes = request.data['notes']
            user = User.objects.get(pk=user_id)
            User.objects.get(pk=coach_user_id)
            coach = Coach.objects.get(user_id=coach_user_id, deleted=False)
        except User.DoesNotExist:
            return parameter_error_response('用户不存在')
        except Coach.DoesNotExist:
            return parameter_error_response('教练不存在')
        except Exception as e:
            return parameter_error_response('请求参数错误')
        obj, created = CoachToClientNotes.objects.get_or_create(coach=coach, user=user, deleted=False)
        obj.notes = notes
        obj.save()
        return success_response()


    @swagger_auto_schema(
        operation_id='教练端项目详情-项目路径图',
        operation_summary='教练端项目详情-项目路径图',
        manual_parameters=[
            openapi.Parameter('coach_user_id', openapi.IN_QUERY, description='教练用户id', type=openapi.TYPE_NUMBER,
                              required=True),
            openapi.Parameter('project_id', openapi.IN_QUERY, description='项目id', type=openapi.TYPE_NUMBER,
                              required=True)
        ],
        tags=['app教练相关']
    )
    @action(methods=['get'], detail=False, url_path='project_path_diagram')
    def project_path_diagram(self, request, *args, **kwargs):
        try:
            coach_user_id = request.query_params.get('coach_user_id')
            project_id = request.query_params.get('project_id')
            coach = Coach.objects.get(user_id=coach_user_id, deleted=False)
            project = Project.objects.get(id=project_id, deleted=False)
        except (KeyError, Coach.DoesNotExist, Project.DoesNotExist):
            return parameter_error_response()
        is_5alc = ProjectCoach.objects.filter(
            project=project, coach=coach, member__isnull=False, deleted=False,
            project_group_coach__isnull=True).exists()
        data = project_service_public.get_project_path_member(project, coach, is_5alc)
        return success_response(data)

    @swagger_auto_schema(
        operation_id='2.15.2教练端项目列表',
        operation_summary='2.15.2教练端项目列表',
        manual_parameters=[
            openapi.Parameter('coach_user_id', openapi.IN_QUERY, description='教练id', type=openapi.TYPE_NUMBER,
                              required=True)

        ],
        tags=['app教练相关']
    )
    @action(methods=['get'], detail=False, url_path='coach_project_list')
    def coach_project_list(self, request, *args, **kwargs):
        try:
            coach_user_id = int(request.query_params.get('coach_user_id', 0))
            user = User.objects.get(pk=coach_user_id)
            coach_user = Coach.objects.get(user=user, deleted=False)
            page_size = request.query_params.get('page_size')
        except User.DoesNotExist:
            return parameter_error_response('请求参数错误')
        except Coach.DoesNotExist:
            return parameter_error_response('请求参数错误')
        project_list = []
        # 2.16版本中8:项目未填写开始结束日期 此状态已无用，为了兼容先留着后续版本刹车
        # 待确认  未通过  已失效  已拒绝
        not_confirm, not_pass, expired, rejected = [], [], [], []
        # 进行中 待开始 已完成 项目未填写开始结束日期 已结束
        progress, not_start, finish, not_date, end = [], [], [], [], []
        exists_project_list = []
        coach_offers = CoachOffer.objects.filter(
            coach=coach_user, deleted=False, project_offer__deleted=False, project_offer__project__deleted=False,
            status__in=[CoachOfferStatusEnum.not_confirm],
            send_time__isnull=False
        )
        if coach_offers.exists():
            coach_offers = multiple_field_distinct(coach_offers, ['project_offer.project_id'])
            coach_offers = coach_offers.values(
                'project_offer__project_id', 'status', 'project_offer__project__name', "id",
                'project_offer__project__company__name', 'project_offer__project__company__logo',
                'project_offer__project__start_time', 'project_offer__project__end_time',
                'project_offer__project__status'
            ).order_by('-project_offer__project__created_at')
            for coach_offer in coach_offers:
                data = {"id": coach_offer['project_offer__project_id'],
                        "name": coach_offer['project_offer__project__name'],
                        "logo": coach_offer['project_offer__project__company__logo'],
                        "company_name": coach_offer['project_offer__project__company__name'],
                        "source_type": 2,
                        "offer_id": coach_offer['id']}

                # 打单中
                if coach_offer['project_offer__project__status'] == ProjectStatusEnum.not_sign:
                    pass
                # 进行中
                elif coach_offer['project_offer__project__status'] == ProjectStatusEnum.progress:
                    data['status'] = 1  # 待确认
                    if data['id'] not in exists_project_list:
                        not_confirm.append(data)
                        exists_project_list.append(data['id'])
                # 已停用
                elif coach_offer['project_offer__project__status'] == ProjectStatusEnum.deactivate:
                    pass
                # 已完成
                elif coach_offer['project_offer__project__status'] == ProjectStatusEnum.completed:
                    pass
                # 已输单
                elif coach_offer['project_offer__project__status'] == ProjectStatusEnum.lost:
                    pass
        project_coaches = ProjectCoach.objects.filter(coach=coach_user, deleted=False, project__deleted=False,
                                                      resume__isnull=False)
        if project_coaches.exists():
            project_coaches = multiple_field_distinct(project_coaches, ['project_id'])
            project_coaches = project_coaches.values(
                'project_id', 'source_type', 'project__start_time', 'project__end_time', 'project__name',
                'project__company__name', 'project__company__logo', 'project__status'
            ).order_by('-project__created_at')
            for project_coach in project_coaches:
                data = {"id": project_coach['project_id'],
                        "name": project_coach['project__name'],
                        "logo": project_coach['project__company__logo'],
                        "company_name": project_coach['project__company__name'],
                        "source_type": project_coach['source_type'],
                        "offer_id": None
                        }
                if project_coach['project__status'] == ProjectStatusEnum.not_sign:
                    # 打单中的项目，不显示
                    pass
                elif project_coach['project__status'] == ProjectStatusEnum.progress:
                    data['status'] = 2  # 进行中
                    if data['id'] not in exists_project_list:
                        progress.append(data)
                        exists_project_list.append(data['id'])
                # 已停用
                elif project_coach['project__status'] == ProjectStatusEnum.deactivate:
                    pass
                # 已完成
                elif project_coach['project__status'] == ProjectStatusEnum.completed:
                    data['status'] = 4  # 已完成
                    if data['id'] not in exists_project_list:
                        finish.append(data)
                        exists_project_list.append(data['id'])
                # 已输单
                elif project_coach['project__status'] == ProjectStatusEnum.lost:
                    pass
        project_list += not_confirm
        project_list += progress
        project_list += not_start
        project_list += finish
        project_list += end
        project_list += not_pass
        project_list += expired
        project_list += rejected
        project_list += not_date

        paginator = StandardResultsSetPagination()
        # 旧版小程序请求接口没有分页参数，当接口没分页参数时，默认一页999
        if not page_size:
            paginator.page_size = 999
        page_list = paginator.paginate_queryset(project_list, self.request)
        response = paginator.get_paginated_response(page_list)
        return success_response(response, request)

    @swagger_auto_schema(
        operation_id='教练端项目详情',
        operation_summary='教练端项目详情',
        manual_parameters=[
            openapi.Parameter('coach_user_id', openapi.IN_QUERY, description='教练id', type=openapi.TYPE_NUMBER,
                              required=True),
            openapi.Parameter('project_id', openapi.IN_QUERY, description='项目id', type=openapi.TYPE_NUMBER,
                              required=True),

        ],
        tags=['app教练相关']
    )
    @action(methods=['get'], detail=False, url_path='project_detail')
    def project_detail(self, request, *args, **kwargs):
        try:
            coach_user_id = int(request.query_params.get('coach_user_id', 0))
            project_id = int(request.query_params.get('project_id', 0))
            user = User.objects.get(pk=coach_user_id)
            project = Project.objects.get(pk=project_id)
            coach = Coach.objects.get(user=user, deleted=False)
        except User.DoesNotExist:
            return parameter_error_response('用户请求参数错误')
        except Project.DoesNotExist:
            return parameter_error_response('项目请求参数错误')
        except Coach.DoesNotExist:
            return parameter_error_response('教练请求参数错误')
        coachee_id_list = coachee_public.get_project_coach_customer_list(user.id, project.id)

        # 待开始
        if project.status == ProjectStatusEnum.not_sign:
            status = 3
        # 进行中
        elif project.status == ProjectStatusEnum.progress:
            status = 2  # 进行中
        # 已停用
        elif project.status == ProjectStatusEnum.deactivate:
            status = 9  # 已结束
        # 已完成
        elif project.status == ProjectStatusEnum.completed:
            status = 4  # 已完成
        else:
            status = None

        coach_offer = CoachOffer.objects.filter(project_offer__deleted=False,
                                                project_offer__project=project,
                                                coach=coach, status=CoachOfferStatusEnum.joined).first()
        coach_content = coach_offer.project_offer.project_msg if coach_offer else project.coach_content
        price = coach_offer.project_offer.price if coach_offer else None
        settlement_price = coach_offer.project_offer.get_settlement_price if coach_offer else None
        settlement_info = coach_offer.project_offer.get_settlement_description if coach_offer else None
        
        project_data = {'id': project.pk,
                        'name': project.name,
                        'logo': project.company.logo,
                        'company': project.company.real_name,
                        'coachee_count': len(coachee_id_list),
                        'interview_times': get_time_hours(coach=user, project=project),
                        'start_time': project.start_time.strftime('%Y-%m-%d') if project.start_time else None,
                        'end_time': project.end_time.strftime('%Y-%m-%d') if project.end_time else None,
                        'customer_demand': project.background_requirement,
                        'customer_expect': project.background_expectation,
                        'customer_info': project.background_coacheeinfo,
                        'remark': project.background_remark,
                        'promoter': project.promoter,
                        'reason': project.reason,
                        'coach_content': coach_content,
                        'status': status,
                        'price': price,
                        'settlement_price': settlement_price,
                        'settlement_info': settlement_info,
                        }
        return success_response(project_data, request)

    @swagger_auto_schema(
        operation_id='教练端项目进展',
        operation_summary='教练端项目进展',
        manual_parameters=[
            openapi.Parameter('coach_user_id', openapi.IN_QUERY, description='教练id', type=openapi.TYPE_NUMBER,
                              required=True),
            openapi.Parameter('project_id', openapi.IN_QUERY, description='项目id', type=openapi.TYPE_NUMBER,
                              required=True),
            openapi.Parameter('page', openapi.IN_QUERY, description='第几页', type=openapi.TYPE_NUMBER),
            openapi.Parameter('page_size', openapi.IN_QUERY, description='每页显示条数默认15条', type=openapi.TYPE_NUMBER)

        ],
        tags=['app教练相关']
    )
    @action(methods=['get'], detail=False, url_path='project_progress')
    def project_progress(self, request, *args, **kwargs):
        try:
            coach_user_id = int(request.query_params.get('coach_user_id', 0))
            peoject_id = int(request.query_params.get('project_id', 0))
            user = User.objects.get(pk=coach_user_id)
            project = Project.objects.get(pk=peoject_id)
        except Exception as e:
            return parameter_error_response('请求参数错误')
        coachee_id_list = coachee_public.get_project_coach_customer_list(user.id, project.id)
        coachee_data = []
        for coachee_id in coachee_id_list:
            coachee = User.objects.get(pk=coachee_id)
            coachee_data.append(
                {'id': coachee.pk,
                 'name': coachee.true_name,
                 'head_image_url': coachee.head_image_url,
                 'interview_count': interview_public.get_coachee_interview_count(coachee_id, coach_user_id),
                 })
        res = sorted(coachee_data, key=lambda i: lazy_pinyin(i['name']))
        # 分页
        paginator = StandardResultsSetPagination()
        page_list = paginator.paginate_queryset(res, self.request)
        response = paginator.get_paginated_response(page_list)
        return success_response(response, request)

    @swagger_auto_schema(
        operation_id='全部教练列表',
        operation_summary='全部教练列表',
        manual_parameters=[
            openapi.Parameter('page', openapi.IN_QUERY, description='第几页', type=openapi.TYPE_NUMBER),
            openapi.Parameter('page_size', openapi.IN_QUERY, description='每页显示条数默认15条', type=openapi.TYPE_NUMBER),
            openapi.Parameter('user_tmp_data_key', openapi.IN_QUERY, description='教练列表缓存关键字', type=openapi.TYPE_STRING)
        ],
        tags=['app教练相关']
    )
    @action(methods=['get'], detail=False, url_path='all_coach_list', authentication_classes=[])
    def all_coach_list(self, request, *args, **kwargs):
        user_role = int(request.headers.get('role', 0))
        coach_query_key = request.query_params.get('user_tmp_data_key')

        user_id = validate.get_not_token_user_id(request)
        if user_id:
            project_member = ProjectMember.objects.filter(user_id=user_id, deleted=False).first()
        else:
            project_member = None

        # 如果是项目用户，获取项目教练列表，没有则为空
        if int(user_role) == UserRoleEnum.coachee:   # 普通被教练者
            coaches = []
            if project_member:
                project_coaches = project_coach_public.get_project_user_coach(project_member)
                if project_coaches and project_coaches.exists():
                    coaches = Coach.objects.filter(
                        id__in=list(project_coaches.values_list('coach_id', flat=True))).order_by('created_at')

        else:
            # 个人用户返回个人教练列表
            # 如果有key，按照固定列表返回, 不做变动
            if coach_query_key and data_redis.get(coach_query_key):
                redis_coach_ids = json.loads(data_redis.get(coach_query_key))
                coaches, redis_coach_ids = get_random_trainee_coach_list(redis_coach_ids)
            else:
                # 如果没有key，先查询随机的教练列表
                coaches, redis_coach_ids = get_random_trainee_coach_list()

                # 根据当前版本判断是否需要固定排序
                mp = json.loads(request.headers.get('mp')) if request.headers.get('mp') else None
                if mp and compare_version(mp.get('version'), '2.22') < 0:
                    pass
                else:
                    # 如果当前用户已登陆，user_id置换user
                    if user_id:
                        user = User.objects.filter(id=user_id, deleted=False).first()
                    else:
                        user = None
                    # 查询新的教练列表
                    coaches = coach_public.to_C_coach_list(coaches, user)
                    # 获取最新的id顺序
                    redis_coach_ids = [coach.id for coach in coaches]

                # 生成缓存，记录信息。
                coach_query_key = f'{pendulum.now().to_date_string()}_{randomPassword(10)}'
                data_redis.set(
                    coach_query_key,
                    json.dumps(redis_coach_ids), ex=60 * 60 * 24 * 3)
        # 分页
        paginator = StandardResultsSetPagination()
        page_list = paginator.paginate_queryset(coaches, self.request)

        data = []
        for item in page_list:
            if project_member and int(user_role) == UserRoleEnum.coachee.value:
                # 查询到定制化简历
                state, resume = coach_public.get_project_coach_resume(item.user.id, project_member.project_id)
                if not state:
                    continue
            else:
                resume = item.resumes.filter(is_customization=False, deleted=False).first()
            domain = resume.coach_domain if resume.coach_domain else []
            data.append({
                'id': item.pk,
                'coach_user_id': item.user.pk,
                'true_name': item.user.cover_name if user_role == UserRoleEnum.coachee else resume.coach.personal_name,
                'head_image_url':  resume.head_image_url if resume.head_image_url else item.user.head_image_url,
                'domain': domain[:3],
                'price': item.display_price,
                'resume_id': resume.id,
                'city': item.city
            })
        response = paginator.get_paginated_response(data)
        # 只有个人辅导生成了随机key才返回公共参数，否则会影响老版本企业客户，在首页错误显示辅导进行中提醒
        if coach_query_key:
            return success_response(response, business={'user_tmp_data_key': coach_query_key})
        return success_response(response)

    @swagger_auto_schema(
        operation_id='全部教练列表',
        operation_summary='全部教练列表',
        manual_parameters=[
            openapi.Parameter('page', openapi.IN_QUERY, description='第几页', type=openapi.TYPE_NUMBER),
            openapi.Parameter('page_size', openapi.IN_QUERY, description='每页显示条数默认15条', type=openapi.TYPE_NUMBER),
            openapi.Parameter('project_id', openapi.IN_QUERY, description='项目id', type=openapi.TYPE_NUMBER),
            openapi.Parameter('date', openapi.IN_QUERY, description='日期', type=openapi.TYPE_NUMBER),
            openapi.Parameter('during', openapi.IN_QUERY, description='时长', type=openapi.TYPE_NUMBER)
        ],
        tags=['app教练相关']
    )
    @action(methods=['get'], detail=False, url_path='coach_list')
    def coach_list(self, request, *args, **kwargs):
        try:
            project_id = request.query_params.get('project_id')
            project = Project.objects.get(id=project_id)
            user = request.user
            project_member = ProjectMember.objects.get(user=user, project=project, deleted=False)
            date = request.query_params.get('date', None)
            during = int(request.query_params.get('during', 60))
            apply_type = request.query_params.get('apply_type', ScheduleApplyTypeEnum.project.value)
        except (Project.DoesNotExist, ProjectMember.DoesNotExist):
            return parameter_error_response()
        if date:
            date_param = datetime.datetime.strptime(date, '%Y-%m-%d')
        else:
            today_date = datetime.datetime.now().date().strftime('%Y-%m-%d')
            date_param = datetime.datetime.strptime(today_date, '%Y-%m-%d')
        start_time, end_time, duration = None, None, None
        # 1. 项目打单中进行中
        if project.status in [ProjectStatusEnum.not_sign, ProjectStatusEnum.progress]:
            chemical_interview_module = project_member.chemical_interview.filter(deleted=False).first()

            # 教练池无需处理是否匹配化学面谈。
            if project_member.coach_match_type == OneToOneMatchTypeEnum.all_project_coach.value:
                project_coaches = ProjectCoach.objects.filter(
                    project_id=project_member.project_id, deleted=False, project_group_coach__isnull=True)
                coach = Coach.objects.filter(id__in=list(project_coaches.values_list('coach_id', flat=True)))
                status = 4  # 返回匹配教练

            elif project_member.coach_match_type == OneToOneMatchTypeEnum.appoint_coach.value:
                # 配置了化学面谈
                if chemical_interview_module:
                    start_time = chemical_interview_module.start_time
                    end_time = chemical_interview_module.end_time
                    duration = chemical_interview_module.duration

                    # 已匹配教练
                    if chemical_interview_module.coaches.filter(
                            deleted=False, chemical_interview_status=ChemicalInterviewStatusEnum.selected,
                            coach__isnull=False).exists():
                        chemical_interview = chemical_interview_module.coaches.filter(
                            deleted=False, chemical_interview_status=ChemicalInterviewStatusEnum.selected,
                            coach__isnull=False)
                        status = 1  # 已匹配教练，配置了化学面谈
                        coach = Coach.objects.filter(id__in=list(chemical_interview.values_list('coach_id', flat=True)))
                    # 未匹配教练
                    else:
                        status = 2  # 未进行化学面谈推荐面谈教练，配置了化学面谈
                        if chemical_interview_module.coach_source == ChemicalInterviewCoachSourceEnum.system_random:
                            chemical_interview = chemical_interview_module.coaches.filter(
                                deleted=False, coach__isnull=False)
                        else:
                            chemical_interview = ProjectCoach.objects.filter(
                                project=project, resume__isnull=False, member__isnull=True,
                                project_group_coach__isnull=True, deleted=False)
                        coach = Coach.objects.filter(id__in=list(chemical_interview.values_list('coach_id', flat=True)))
                # 未配置化学面谈
                else:
                    project_coaches = ProjectCoach.objects.filter(member_id=user.pk, deleted=False,
                                                                  project=project_member.project,
                                                                  status=ProjectCoachStatusEnum.adopt.value,
                                                                  project_group_coach__isnull=True)
                    if project_coaches.exists():
                        status = 4  # 已匹配教练，没有配置化学面谈
                        coach = Coach.objects.filter(id__in=list(project_coaches.values_list('coach_id', flat=True)))
                    else:
                        status = 3  # 未匹配教练，没有配置化学面谈
                        coach = Coach.objects.none()
            else:
                status = 3  # 未匹配教练，没有配置化学面谈
                coach = Coach.objects.none()

        elif project.status in [ProjectStatusEnum.deactivate, ProjectStatusEnum.completed]:
            status = 5  # 5-项目已结束
            coach = Coach.objects.none()
        else:
            return parameter_error_response()

        # 计算每位教练最近可预约时间
        # 如果不是当天 就直接从8点开始算
        if datetime.datetime.today().date() != date_param.date():
            start_time = date_param + datetime.timedelta(hours=8)
        else:
            start_time = datetime.datetime.now()

        coach_list = []
        for c in coach:
            time_point = get_time_point(start_time.strftime('%Y-%m-%d %H:%M:%S'), during, c.user.pk, apply_type=apply_type)
            if not time_point:
                c.appointment_time = None
                c.hint = '当日已约满'
                coach_list.append(c)
                continue
            time_point_hm = datetime.datetime.strptime(time_point, '%Y-%m-%d %H:%M:%S').strftime('%H:%M')
            c.appointment_time = time_point_hm
            c.hint = '最近可预约时间：' + time_point_hm
            coach_list.append(c)

        # 分页
        paginator = StandardResultsSetPagination()
        page_list = paginator.paginate_queryset(coach_list, self.request)
        serializer = CoachProjectMemberSerializer(page_list, many=True, context={'project_id': project.pk,
                                                                                 'user_id': user.pk,
                                                                                 'status': status})
        response = paginator.get_paginated_response(serializer.data)
        data = dict(response.data)
        coach_list = data['results']
        data['results'] = {}
        data['results']['status'] = status
        data['results']['start_time'] = start_time
        data['results']['end_time'] = end_time
        data['results']['duration'] = duration
        data['results']['coach'] = coach_list
        response.data = data
        return success_response(response)


    @swagger_auto_schema(
        operation_id='教练客户详情-报告列表',
        operation_summary='教练客户详情-报告列表',
        manual_parameters=[
            openapi.Parameter('coachee_user_id', openapi.IN_QUERY, description='被教练者id', type=openapi.TYPE_NUMBER),
            openapi.Parameter('coach_user_id', openapi.IN_QUERY, description='教练id', type=openapi.TYPE_NUMBER),
            openapi.Parameter('page', openapi.IN_QUERY, description='第几页', type=openapi.TYPE_NUMBER, default=1),
            openapi.Parameter('page_size', openapi.IN_QUERY, description='每页显示条数', type=openapi.TYPE_NUMBER, default=15)
        ],
        tags=['app教练相关']
    )
    @action(methods=['get'], detail=False, url_path='report')
    def get_user_report(self, request, *args, **kwargs):
        try:
            coachee_user_id = request.query_params.get('coachee_user_id')
            coach_user_id = request.query_params.get('coach_user_id')
            mp = json.loads(request.headers.get('mp')) if request.headers.get('mp') else None
            project_member = ProjectMember.objects.filter(user_id=coachee_user_id, deleted=False).first()
        except Exception:
            return parameter_error_response()

        # 如果是个人教练，不返回报告数据
        if not project_member:
            return success_response([], request=request)

        change_observation_report_data = []
        evaluation_report_data = []
        project_docs_data = []

        # 找到未填写的教练任务
        raw_coach_tasks = CoachTask.objects.filter(
            coach_submit_time__isnull=True,
            public_attr__user_id=coach_user_id,
            public_attr__target_user_id=coachee_user_id,
            public_attr__type=ATTR_TYPE_COACH_TASK,
            deleted=False
        ).exclude(
            template__write_role=InterviewRecordTemplateRoleEnum.stakeholder).order_by('-hours')
        raw_coach_tasks_data = UserReportCoachTaskSerializer(raw_coach_tasks, many=True).data

        # 找到已填写的教练任务
        complete_coach_tasks = CoachTask.objects.filter(
            Q(coach_submit_time__isnull=False) | Q(type=NewCoachTaskTypeEnum.stakeholder_research,
                                                   stakeholder_submit_time__isnull=False),
            public_attr__user_id=coach_user_id,
            public_attr__target_user_id=coachee_user_id,
            public_attr__type=ATTR_TYPE_COACH_TASK,
            deleted=False
        ).order_by('-hours')
        complete_coach_tasks_data = UserReportCoachTaskSerializer(complete_coach_tasks, many=True).data

        # 低于2.37版本，不额外生成纪要，总结报告两个教练任务
        if mp and compare_version(mp.get('version'), '2.37') >= 0:
            raw_coach_tasks_data = coach_task_public.get_now_coach_tasks_data(coachee_user_id, raw_coach_tasks_data)
            complete_coach_tasks_data = coach_task_public.get_now_coach_tasks_data(coachee_user_id, complete_coach_tasks_data)
        
        # 找到填写的改变观察反馈报告
        change_observation_report_data = utils.get_change_observation_report_data(coachee_user_id)

        # 只有报告用户自己能看到教练型管理者测评报告
        self_only_code_list = [MANAGE_EVALUATION]
        evaluation_queryset = EvaluationReport.objects.filter(public_attr__user=coachee_user_id).order_by('-created_at')
        if request.user.pk != coachee_user_id:
            evaluation_queryset = evaluation_queryset.exclude(evaluation__code__in=self_only_code_list)
        if evaluation_queryset.exists():
            for item in evaluation_queryset.all():
                evaluation_report_config = item.evaluation.evaluation_report_config.filter(deleted=False).first()
                evaluation_report_data.append({
                    'image_url': item.evaluation.image_url,
                    "data_type": DataType.evaluation,
                    "describe": f'生成时间:{item.created_at.strftime("%Y/%m/%d")}',
                    'title': item.evaluation.name,
                    "details": {
                        "coachee_name": item.public_attr.user.cover_name,
                        "code": item.evaluation.code,
                        "report_id": item.id,
                        "evaluation_report_type": evaluation_report_config.type if evaluation_report_config else None,
                    }
                })

        if mp and compare_version(mp.get('version'), '2.18') >= 0:
            project_docs = ProjectDocs.objects.filter(
                project_member__user_id=coachee_user_id, file__deleted=False,
                project_docs_type=ProjectDocsTypeEnum.report, deleted=False).order_by('-file__created_at')
            project_docs = multiple_field_distinct(project_docs, ['project_member', 'file_id'])
            project_docs_data = AppProjectDocsListDetailsSerializer(project_docs, many=True).data

        paginator = StandardResultsSetPagination()
        page_list = paginator.paginate_queryset(
            [
                *raw_coach_tasks_data,
                *change_observation_report_data,
                *complete_coach_tasks_data,
                *evaluation_report_data,
                *project_docs_data
            ], self.request)
        response = paginator.get_paginated_response(page_list)
        return success_response(response, request=request)


    @swagger_auto_schema(
        operation_id='教练端-更新个人报告',
        operation_summary='更新个人报告',
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'personal_report_id': openapi.Schema(type=openapi.TYPE_NUMBER, description='个人报告id'),
                'file_url': openapi.Schema(type=openapi.TYPE_STRING, description='文件链接'),
            }
        ),
        tags=['app教练相关']
    )
    @action(methods=['post'], detail=False, url_path='stakeholder_report/update')
    def update_stakeholder_report(self, request, *args, **kwargs):

        try:
            personal_report_id = request.data['personal_report_id']
            file_url = request.data['file_url']
            personal_report = PersonalReport.objects.get(id=personal_report_id, deleted=False)
        except PersonalReport.DoesNotExist:
            return parameter_error_response('个人报告不存在')
        except Exception as e:
            return parameter_error_response()
        personal_report.pdf_url = file_url
        personal_report.save()
        if personal_report.type == PersonalReportTypeEnum.notes_report.value:
            # 新版report_url和coach_submit_time不再绑定,上传素材只更新report_url
            CoachTask.objects.filter(id=personal_report.object_id, deleted=False).update(report_url=file_url)
        return success_response()


    @swagger_auto_schema(
        operation_id='教练端-客户画像查询',
        operation_summary='客户画像查询',
        manual_parameters=[
            openapi.Parameter('project_id', openapi.IN_QUERY, description='项目id', type=openapi.TYPE_NUMBER),
            openapi.Parameter('coach_user_id', openapi.IN_QUERY, description='教练用户id', type=openapi.TYPE_NUMBER),
            openapi.Parameter('coachee_user_id', openapi.IN_QUERY, description='被教练者用户id', type=openapi.TYPE_NUMBER),
        ],
        tags=['app教练相关']
    )
    @action(methods=['get'], detail=False, url_path='customer/portrait')
    def get_customer_portrait(self, request, *args, **kwargs):
        try:
            project_id = request.query_params.get('project_id')
            coach_user_id = request.query_params.get('coach_user_id')
            coachee_user_id = request.query_params.get('coachee_user_id')
            Project.objects.get(pk=project_id, deleted=False)
            coach_user = User.objects.get(pk=coach_user_id, deleted=False)
            coachee_user = User.objects.get(pk=coachee_user_id, deleted=False)
        except Project.DoesNotExist:
            return parameter_error_response('获取项目信息错误')
        except User.DoesNotExist:
            return parameter_error_response('获取用户信息错误')
        except Exception:
            return parameter_error_response()

        # 优先查询教练对用户的画像，没有则查询项目的
        customer_portrait = CustomerPortrait.objects.filter(
            coach=coach_user, user=coachee_user, deleted=False
        ).first()
        if not customer_portrait:
            customer_portrait = CustomerPortrait.objects.filter(
                Q(type=CustomerPortraitTypeEnum.group) | Q(user=coachee_user),
                project_id=project_id,
                deleted=False).order_by('type').first()
        if customer_portrait:
            serializer = AppCoachCustomerDetailsPortraitSerializer(customer_portrait).data
            return success_response(serializer, request=request)
        else:
            return success_response()

    @swagger_auto_schema(
        operation_id='教练端-修改客户画像',
        operation_summary='修改客户画像',
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'portrait_id': openapi.Schema(type=openapi.TYPE_NUMBER, description='画像id'),
                'coach_user_id': openapi.Schema(type=openapi.TYPE_NUMBER, description='教练用户id'),
                'coachee_user_id': openapi.Schema(type=openapi.TYPE_NUMBER, description='教练用户id'),
                'customer_style': openapi.Schema(type=openapi.TYPE_STRING, description='客户个性及特点'),
                'customer_experience': openapi.Schema(type=openapi.TYPE_STRING, description='客户管理经验'),
                'city': openapi.Schema(type=openapi.TYPE_STRING, description='所在城市'),
                'challenge': openapi.Schema(type=openapi.TYPE_STRING, description='当前面临的挑战'),
                'coach_expect': openapi.Schema(type=openapi.TYPE_STRING, description='对教练认识和期待'),
                'coach_extra': openapi.Schema(type=openapi.TYPE_STRING, description='可供教练参考的其他信息'),
                'gender': openapi.Schema(type=openapi.TYPE_STRING, description='客户性别'),
            }
        ),
        tags=['app教练相关']
    )
    @action(methods=['post'], detail=False, url_path='customer/portrait/update')
    def post_customer_portrait(self, request, *args, **kwargs):

        try:
            portrait_id = request.data.get('portrait_id')
            coach_user_id = request.data.get('coach_user_id')
            coachee_user_id = request.data.get('coachee_user_id')
            if portrait_id:
                customer_portrait = CustomerPortrait.objects.get(pk=portrait_id, deleted=False)
            else:
                customer_portrait = None
            coach_user = User.objects.get(pk=coach_user_id, deleted=False)
            coachee_user = User.objects.get(pk=coachee_user_id, deleted=False)
        except CustomerPortrait.DoesNotExist:
            return parameter_error_response('获取客户简历错误')
        except User.DoesNotExist:
            return parameter_error_response('获取用户信息错误')
        except Exception as e:
            return parameter_error_response()

        error = customer_portrait_public.customer_portrait_data_check(request.data)
        if error:
            return parameter_error_response(error)

        with transaction.atomic():
            # 没有个人画像则新建
            if not customer_portrait or customer_portrait.project:
                new_customer_portrait = customer_portrait_public.create_coach_customer_portrait(
                    request.data, coach_user, coachee_user, customer_portrait)
            # 如果是个人画像则更新
            else:
                data = request.data.copy()
                serializer = AppCoachCustomerPortraitBaseSerializer(customer_portrait, data=request.data)
                serializer.is_valid(raise_exception=True)
                new_customer_portrait = serializer.update(customer_portrait, data)

        serializer = AppCoachCustomerDetailsPortraitSerializer(new_customer_portrait)
        return success_response(serializer.data)


    @swagger_auto_schema(
        operation_id='教练端-客户详细信息查询',
        operation_summary='客户详细信息查询',
        manual_parameters=[
            openapi.Parameter('project_id', openapi.IN_QUERY, description='项目id', type=openapi.TYPE_NUMBER),
            openapi.Parameter('coach_user_id', openapi.IN_QUERY, description='教练用户id', type=openapi.TYPE_NUMBER),
            openapi.Parameter('coachee_user_id', openapi.IN_QUERY, description='被教练者用户id', type=openapi.TYPE_NUMBER),
        ],
        tags=['app教练相关']
    )
    @action(methods=['get'], detail=False, url_path='customer/info')
    def get_customer_info(self, request, *args, **kwargs):
        try:
            project_id = request.query_params.get('project_id')
            coach_user_id = request.query_params.get('coach_user_id')
            coachee_user_id = request.query_params.get('coachee_user_id')

            coachee_user = User.objects.get(pk=coachee_user_id, deleted=False)
            coach_user = User.objects.get(pk=coach_user_id, deleted=False)
            Project.objects.get(pk=project_id, deleted=False)
            role = int(request.headers.get('role'), 0)
        except Project.DoesNotExist:
            return parameter_error_response('获取项目信息错误')
        except User.DoesNotExist:
            return parameter_error_response('获取用户信息错误')
        except Exception:
            return parameter_error_response()

        data = project_member_public.get_project_member_customer_info(project_id, coachee_user_id, coach_user_id)

        # 获取教练对用户的备注信息
        notes = coach_public.get_coach_to_client_notes(coach_user_id, coachee_user_id)
        # 查询教练的全部客户列表
        customer_list = coach_public.get_coach_to_coachee_list(coach_user, role)
        customer_user_id_list = [item.get('id') for item in customer_list]

        data['true_name'] = coachee_user.cover_name
        data['head_image_url'] = coachee_user.head_image_url
        data['is_coach_customer'] = bool(int(coachee_user_id) in customer_user_id_list)  # 用户是不是教练的客户
        data['notes'] = notes
        return success_response(data)

    @swagger_auto_schema(
        operation_id='教练端-修改客户信息',
        operation_summary='修改客户信息',
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'portrait_id': openapi.Schema(type=openapi.TYPE_NUMBER, description='画像id'),
                'coach_user_id': openapi.Schema(type=openapi.TYPE_NUMBER, description='教练用户id'),
                'coachee_user_id': openapi.Schema(type=openapi.TYPE_NUMBER, description='教练用户id'),
                'customer_style': openapi.Schema(type=openapi.TYPE_STRING, description='客户个性及特点'),
                'customer_experience': openapi.Schema(type=openapi.TYPE_STRING, description='客户管理经验'),
                'city': openapi.Schema(type=openapi.TYPE_STRING, description='所在城市'),
                'challenge': openapi.Schema(type=openapi.TYPE_STRING, description='当前面临的挑战'),
                'coach_expect': openapi.Schema(type=openapi.TYPE_STRING, description='对教练认识和期待'),
                'coach_extra': openapi.Schema(type=openapi.TYPE_STRING, description='可供教练参考的其他信息'),
                'gender': openapi.Schema(type=openapi.TYPE_STRING, description='客户性别'),
            }
        ),
        tags=['app教练相关']
    )
    @action(methods=['post'], detail=False, url_path='customer/info/update')
    def post_customer_info(self, request, *args, **kwargs):

        try:
            project_id = request.data.get('project_id')
            coach_user_id = request.data.get('coach_user_id')
            coachee_user_id = request.data.get('coachee_user_id')
            User.objects.get(pk=coach_user_id, deleted=False)
            User.objects.get(pk=coachee_user_id, deleted=False)
        except User.DoesNotExist:
            return parameter_error_response('获取用户信息错误')
        except Exception as e:
            return parameter_error_response()

        with transaction.atomic():
            customer_portrait, is_create = CustomerPortrait.objects.get_or_create(
                user_id=coachee_user_id, coach_id=coach_user_id, project_id__isnull=True, deleted=False)

            data = request.data.copy()
            serializer = AppCoachCustomerPortraitBaseSerializer(customer_portrait, data=request.data)
            serializer.is_valid(raise_exception=True)
            serializer.update(customer_portrait, data)

        data = project_member_public.get_project_member_customer_info(project_id, coachee_user_id, coach_user_id)
        return success_response(data)

    @swagger_auto_schema(
        operation_id='用户转个人教练签约流程查询',
        operation_summary='用户转个人教练签约流程查询',
        manual_parameters=[
            openapi.Parameter('user_id', openapi.IN_QUERY, description='用户标识', type=openapi.TYPE_STRING),
        ],
        tags=['合同相关']
    )
    @action(methods=['get'], detail=False, url_path='sign/step')
    def get_coach_contract_step(self, request, *args, **kwargs):
        try:
            user_id = request.query_params.get('user_id')
            user = User.objects.get(pk=user_id, deleted=False)
            coach = Coach.objects.get(user_id=user_id, deleted=False)
            resume = coach.resumes.filter(deleted=False, is_customization=False).first()
        except User.DoesNotExist:
            return parameter_error_response('获取用户信息错误')
        except Coach.DoesNotExist:
            return parameter_error_response('获取教练信息错误')
        except Exception as e:
            return parameter_error_response()

        if coach.coach_type not in CoachUserTypeEnum.personal_coach_value():
            return success_response({'contract_status': 6})  # 非个人教练不需要进入签约流程 无需弹窗

        # 用户申请转教练可以转见习教练/个人教练/项目教练
        # 申请转见习教练后可以见习教练申请转个人教练，这时候会出现有一个通过的user_to_coach申请和coach_type是个人，错误的进入后续流程。
        # 这里判断用户是否申请成功需要加上申请类型的coach_type，用户必须申请成为个人教练成功，才能进入后续流程。
        personal_apply = PersonalApply.objects.filter(
            coach=coach, deleted=False, status=PersonalApplyStatusEnum.passed,
            coach_type__in=CoachUserTypeEnum.personal_coach_value(),
            type=PersonalApplyTypeEnum.user_to_coach.value)
        if not personal_apply:
            return success_response({'contract_status': 6})  # 未提交申请 无需弹窗

        work_wechat_user = WorkWechatUser.objects.filter(wx_user_id__isnull=False, user_id=user_id, deleted=False).first()
        if not work_wechat_user:
            return success_response({'contract_status': 6})  # 未绑定企业微信 无需弹窗

        if not user.is_identity_check:
            return success_response({'contract_status': 1, 'resume_id': resume.id})  # 未实名认证

        if not work_wechat_user.qr_code:
            return success_response({'contract_status': 2, 'resume_id': resume.id})  # 需要绑定二维码

        user_contract = UserContract.objects.filter(
            user_id=user_id, deleted=False, personal_empower_at__isnull=False).first()
        if not user_contract:
            return success_response({'contract_status': 3, 'resume_id': resume.id})  # 需要授权

        if not resume.coach_experience and not coach.is_skip_entrant: # 用户没有选择跳过当前入住流程
            return success_response({'contract_status': 4, 'resume_id': resume.id})  # 需要填写教练经验

        return success_response({'contract_status': 5, 'resume_id': resume.id})  # 已走完签约流程


    @swagger_auto_schema(
        operation_id='见习教练入驻流程查询',
        operation_summary='见习教练入驻流程查询',
        manual_parameters=[
            openapi.Parameter('user_id', openapi.IN_QUERY, description='用户标识', type=openapi.TYPE_STRING),
        ],
        tags=['合同相关']
    )
    @action(methods=['get'], detail=False, url_path='internship_entrant')
    def get_internship_entrant(self, request, *args, **kwargs):
        try:
            user_id = request.query_params.get('user_id')
            user = User.objects.get(pk=user_id, deleted=False)
            coach = Coach.objects.get(user_id=user_id, deleted=False)
            resume = coach.resumes.filter(deleted=False, is_customization=False).first()
        except User.DoesNotExist:
            return parameter_error_response('获取用户信息错误')
        except Coach.DoesNotExist:
            return parameter_error_response('获取教练信息错误')
        except Exception as e:
            return parameter_error_response()
        if not resume:
            return parameter_error_response()
        data = {
            'contract_status': 0,  # 无需弹窗
            'resume_id': resume.id if resume else None,
            'work_wechat_user_id': None,
            'coach_type': coach.coach_type
        }
        if coach.coach_type != CoachUserTypeEnum.student.value:
            return success_response(data)  # 非见习教练不需要进入签约流程，无需弹窗
        work_wechat_user = WorkWechatUser.objects.filter(wx_user_id__isnull=False, user_id=user_id, deleted=False).first()
        if not work_wechat_user:
            return success_response(data)  # 未绑定企业微信，无需弹窗
        data['work_wechat_user_id'] = work_wechat_user.wx_user_id

        if PersonalApply.objects.filter(
                coach=coach, deleted=False, type=PersonalApplyTypeEnum.internship.value,
                status=PersonalApplyStatusEnum.passed.value).exists():
            return success_response(data)  # 已通过申请，无需弹窗

        if PersonalApply.objects.filter(
                coach=coach, deleted=False, type=PersonalApplyTypeEnum.internship.value,
                status=PersonalApplyStatusEnum.not_passed.value).exists():
            data['contract_status'] = 5  # 完成验证
            return success_response(data)  # 已有待审批申请

        if not user.is_identity_check:
            data['contract_status'] = 1  # 需要实名认证
            return success_response(data)

        if not work_wechat_user.qr_code:
            data['contract_status'] = 2  # 需要绑定二维码
            return success_response(data)

        user_contract = UserContract.objects.filter(
            user_id=user_id, deleted=False, internship_empower_at__isnull=False)
        if not user_contract.first():
            data['contract_status'] = 3  # 需要签订协议
            return success_response(data)

        # 判断是否需要完善简历
        # 判断的字段分布在resume,coach,user中，合成一个数据集
        resume_data = model_to_dict(resume)
        resume_data['email'] = coach.user.email
        resume_data['phone'] = coach.user.phone
        resume_data['personal_name'] = coach.personal_name
        resume_data['posters_text'] = coach.posters_text
        status = coach_public.coach_resume_entry_review(
            resume_data, empower_type=PersonalApplyTypeEnum.internship.value)
        if status:
            data['contract_status'] = 4  # 需要填写入驻信息
            return success_response(data)

        data['contract_status'] = 5  # 完成验证
        return success_response(data)

    @swagger_auto_schema(
        operation_id='见习教练转个人教练流程查询',
        operation_summary='见习教练转个人教练流程查询',
        manual_parameters=[
            openapi.Parameter('user_id', openapi.IN_QUERY, description='用户标识', type=openapi.TYPE_STRING),
        ],
        tags=['合同相关']
    )
    @action(methods=['get'], detail=False, url_path='internship_to_personal')
    def get_internship_to_personal_step(self, request, *args, **kwargs):
        try:
            user_id = request.query_params.get('user_id')
            coach = Coach.objects.get(user_id=user_id, deleted=False)
            resume = coach.resumes.filter(deleted=False, is_customization=False).first()
        except User.DoesNotExist:
            return parameter_error_response('获取用户信息错误')
        except Coach.DoesNotExist:
            return parameter_error_response('获取教练信息错误')
        except Exception as e:
            return parameter_error_response()
        data = {
            'contract_status': 0,  # 无需弹窗
            'resume_id': resume.id if resume else None,
            'work_wechat_user_id': None
        }

        # 项目不需要进入签约流程，无需弹窗
        if coach.coach_type in CoachUserTypeEnum.enterprise_coach_value():
            return success_response(data)
        work_wechat_user = WorkWechatUser.objects.filter(wx_user_id__isnull=False, user_id=user_id, deleted=False).first()
        if not work_wechat_user:
            return success_response(data)  # 未绑定企业微信，无需弹窗
        data['work_wechat_user_id'] = work_wechat_user.wx_user_id

        if PersonalApply.objects.filter(
                coach=coach, deleted=False, type=PersonalApplyTypeEnum.personal.value,
                status=PersonalApplyStatusEnum.passed.value
        ).exists():
            data['contract_status'] = 5
            return success_response(data)  # 已通过申请，提示申请完成

        if PersonalApply.objects.filter(
                coach=coach, deleted=False, type=PersonalApplyTypeEnum.personal.value,
                status=PersonalApplyStatusEnum.not_passed.value
        ).exists():
            data['contract_status'] = 4
            return success_response(data)  # 已有待审批申请，提示申请中

        user_contract = UserContract.objects.filter(
            user_id=user_id, deleted=False, personal_empower_at__isnull=False).first()
        if not user_contract:
            data['contract_status'] = 1  # 需要签订协议
            return success_response(data)
        review_status = coach_public.coach_resume_entry_review(
            model_to_dict(resume), empower_type=PersonalApplyTypeEnum.personal.value)
        if review_status or coach.intern_to_personal_status == CoachInternToPersonalStatus.resume.value:
            # 审核被拒绝的情况下intern_to_personal_status会变成2-resume
            # 填写简历后intern_to_personal_status会变成3-set_price
            data['contract_status'] = 2  # 需要填写简历
            return success_response(data)
        if coach.intern_to_personal_status == CoachInternToPersonalStatus.set_price.value:
            data['contract_status'] = 3  # 需要设置价格
            return success_response(data)
        data['contract_status'] = 5  # 完成验证
        return success_response(data)

    @swagger_auto_schema(
        operation_id='教练端-入驻流程确认授权',
        operation_summary='教练端-入驻流程确认授权',
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'user_id': openapi.Schema(type=openapi.TYPE_FILE, description='用户标识'),
                'empower_type': openapi.Schema(type=openapi.TYPE_FILE, description='授权类型 1-个人教练 2-见习教练'),
            }
        ),
        tags=['app教练相关']
    )
    @action(methods=['post'], detail=False, url_path='empower', authentication_classes=[])
    def post_coach_empower(self, request, *args, **kwargs):
        try:
            user_id = request.data.get('user_id')
            empower_type = request.data.get('empower_type')
            User.objects.get(pk=user_id, deleted=False)
            Coach.objects.get(user_id=user_id, deleted=False)
        except User.DoesNotExist:
            return parameter_error_response('获取用户信息错误')
        except Coach.DoesNotExist:
            return parameter_error_response('获取教练信息错误')
        except Exception as e:
            return parameter_error_response()

        user_contract, created = UserContract.objects.get_or_create(user_id=user_id, deleted=False)
        if empower_type == 1:
            user_contract.personal_empower_at = datetime.datetime.now()
        elif empower_type == 2:
            user_contract.internship_empower_at = datetime.datetime.now()
        user_contract.save()
        return success_response()

    @swagger_auto_schema(
        operation_id='教练端-见习教练入驻流程更新入驻信息',
        operation_summary='教练端-见习教练入驻流程更新入驻信息',
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'user_id': openapi.Schema(type=openapi.TYPE_FILE, description='用户标识'),
                'empower_type': openapi.Schema(type=openapi.TYPE_FILE, description='授权类型 1-个人教练 2-见习教练'),
            }
        ),
        tags=['app教练相关']
    )
    @action(methods=['post'], detail=False, url_path='entrant_info/update')
    def update_entrant_coach_data(self, request, *args, **kwargs):
        try:
            user_id = request.data.get('user_id')
            empower_type = request.data.get('empower_type')
            price = request.data.get('price')
            User.objects.get(pk=user_id, deleted=False)
            coach = Coach.objects.get(user_id=user_id, deleted=False)
            resume = coach.resumes.filter(deleted=False, is_customization=False).first()
        except User.DoesNotExist:
            return parameter_error_response('获取用户信息错误')
        except Coach.DoesNotExist:
            return parameter_error_response('获取教练信息错误')
        except Exception as e:
            return parameter_error_response()

        if 'price' in request.data.keys() and empower_type == PersonalApplyTypeEnum.personal.value:
            # 如果有价格并且是见习申请个人，跳过参数验证（价格就是唯一参数）
            pass
        else:
            # 价格修改是单独的页面，存在价格就不做参数校验。
            error_msg = coach_public.coach_resume_entry_review(request.data, empower_type, user_id=user_id)
            if error_msg:
                return parameter_error_response(error_msg)
        coach_public.coach_resume_entry_update(request.data, empower_type, coach)
        # 记录更新简历的教练信息
        resume_public.record_coach_update_resume(resume)
        # 更新教练标签
        task.update_coach_tag.delay(resume.coach_id)

        UserTmp.objects.filter(
            type=UserTmpEnum.entrant.value, data_id=resume.pk, extra_id=empower_type, user_id=user_id).delete()
        return success_response()

    @swagger_auto_schema(
        operation_id='教练端-教练跳过更新入驻信息',
        operation_summary='教练端-教练跳过更新入驻信息',
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'user_id': openapi.Schema(type=openapi.TYPE_FILE, description='用户标识'),
                'empower_type': openapi.Schema(type=openapi.TYPE_FILE, description='授权类型 1-个人教练 2-见习教练 3-用户申请转教练'),
            }
        ),
        tags=['app教练相关']
    )
    @action(methods=['post'], detail=False, url_path='skip/entrant_info')
    def skip_entrant_info(self, request, *args, **kwargs):
        try:
            user_id = request.data.get('user_id')
            empower_type = request.data.get('empower_type')
            User.objects.get(pk=user_id, deleted=False)
            coach = Coach.objects.get(user_id=user_id, deleted=False)
        except User.DoesNotExist:
            return parameter_error_response('获取用户信息错误')
        except Coach.DoesNotExist:
            return parameter_error_response('获取教练信息错误')
        except Exception as e:
            return parameter_error_response()

        # C端用户转个人教练。记录是否跳过
        if empower_type == PersonalApplyTypeEnum.user_to_coach.value:
            coach.is_skip_entrant = True
            coach.save()
        # 见习教练入驻，跳过填写=发送申请
        elif empower_type == PersonalApplyTypeEnum.internship.value:
            apply_instance = PersonalApply.objects.create(
                type=empower_type, true_name=coach.user.true_name, phone=coach.user.phone,
                email=coach.user.email, coach=coach, user_class=coach.user_class
            )
            coach.intern_to_personal_status = CoachInternToPersonalStatus.apply.value
            coach.save()
            task.push_lark_message_celery.delay([apply_instance.pk], 'add_personal_apply')
        # 见习教练转个人教练，跳过填写=发送申请
        elif empower_type == PersonalApplyTypeEnum.personal.value:
            PersonalApply.objects.create(  # 创建新的见习教练申请记录
                type=empower_type, true_name=coach.user.true_name, phone=coach.user.phone,
                email=coach.user.email, coach=coach, user_class=coach.user_class
            )

        return success_response()

    @swagger_auto_schema(
        operation_id='教练端-提交结算信息',
        operation_summary='提交结算信息',
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'front_image': openapi.Schema(type=openapi.TYPE_FILE, description='正面图片'),
                'back_image': openapi.Schema(type=openapi.TYPE_FILE, description='背面图片'),
                'coach_user_id': openapi.Schema(type=openapi.TYPE_NUMBER, description='教练用户id'),
                'id_card': openapi.Schema(type=openapi.TYPE_STRING, description='身份标识'),
                'bank_card': openapi.Schema(type=openapi.TYPE_STRING, description='银行卡标识'),
            }
        ),
        tags=['app教练相关']
    )
    @action(methods=['post'], detail=False, url_path='settlement_info')
    def post_settlement_info(self, request, *args, **kwargs):
        try:
            # 获取其他参数
            coach_user_id = request.data['coach_user_id']
            id_card = request.data['id_card']
            bank_card = request.data['bank_card']
            coach = Coach.objects.get(user_id=coach_user_id, deleted=False)
        except Coach.DoesNotExist:
            return parameter_error_response('教练信息不存在')
        except Exception as e:
            return parameter_error_response()
        if not id_number_check(id_card):
            return parameter_error_response('身份证信息不正确')
        if not str(bank_card).isdigit():
            return parameter_error_response('银行卡信息不正确，请输入数字')

        data_redis.set('coach_settlement_info_' + str(coach_user_id),  json.dumps({'id_card': id_card, 'bank_card': bank_card}), 180)
        return success_response()


    @swagger_auto_schema(
        operation_id='教练端-提交结算信息',
        operation_summary='提交结算信息',
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'front_image': openapi.Schema(type=openapi.TYPE_FILE, description='正面图片'),
                'back_image': openapi.Schema(type=openapi.TYPE_FILE, description='背面图片'),
                'coach_user_id': openapi.Schema(type=openapi.TYPE_NUMBER, description='教练用户id'),
            }
        ),
        tags=['app教练相关']
    )
    @action(methods=['post'], detail=False, url_path='settlement_image_info')
    def post_settlement_image_info(self, request, *args, **kwargs):
        try:
            front_image_file = request.FILES.get('front_image')
            back_image_file = request.FILES.get('back_image')
            coach_user_id = request.data.get('coach_user_id')
            coach = Coach.objects.get(user_id=coach_user_id, deleted=False)
        except Coach.DoesNotExist:
            return parameter_error_response('教练信息不存在')
        except Exception as e:
            return parameter_error_response()

        if not front_image_file and not back_image_file:
            return parameter_error_response('缺少图片信息')
        coach_settlement_info = data_redis.get('coach_settlement_info_' + str(coach_user_id))
        if not coach_settlement_info:
            return parameter_error_response('未获取到结算信息')

        raw_coach_settlement_info = coach_settlement_info.decode()
        coach_settlement_info = json.loads(raw_coach_settlement_info)
        id_card = coach_settlement_info.get('id_card')
        bank_card = coach_settlement_info.get('bank_card')
        front_binary_image = coach_settlement_info.get('front_binary_image')
        front_file_name = coach_settlement_info.get('front_file_name')
        back_binary_image = coach_settlement_info.get('back_binary_image')
        back_file_name = coach_settlement_info.get('back_file_name')

        if front_image_file:
            front_binary_data = front_image_file.read()
            front_binary_image = [byte for byte in front_binary_data]
            front_file_name = front_image_file.name
            front_file_name = f"{coach.user.true_name}身份证正面.{str(front_file_name).split('.')[-1]}"
            if back_binary_image:
                task.send_coach_settlement_info(
                    coach, id_card, bank_card, front_binary_image, front_file_name, back_binary_image, back_file_name)
            else:
                data_redis.set(
                    'coach_settlement_info_' + str(coach_user_id),
                    json.dumps({'id_card': id_card, 'bank_card': bank_card,
                                'front_binary_image': front_binary_image, 'front_file_name': front_file_name}), 180)
        elif back_image_file:
            back_binary_data = back_image_file.read()
            back_binary_image = [byte for byte in back_binary_data]
            back_file_name = back_image_file.name
            back_file_name = f"{coach.user.true_name}身份证背面.{str(back_file_name).split('.')[-1]}"
            if front_binary_image:
                task.send_coach_settlement_info(
                    coach, id_card, bank_card, front_binary_image, front_file_name, back_binary_image, back_file_name)
            else:
                data_redis.set(
                    'coach_settlement_info_' + str(coach_user_id),
                    json.dumps({'id_card': id_card, 'bank_card': bank_card,
                                'back_binary_image': back_binary_image, 'back_file_name': back_file_name}), 180)
        return success_response()


    @swagger_auto_schema(
        operation_id='教练端-教练推荐数据',
        operation_summary='教练推荐数据',
        manual_parameters=[
            openapi.Parameter('coach_user_id', openapi.IN_QUERY, description='教练用户标识', type=openapi.TYPE_STRING),
        ],
        tags=['app教练相关']
    )
    @action(methods=['get'], detail=False, url_path='recommend', authentication_classes=[])
    def get_coach_recommend(self, request, *args, **kwargs):
        coach_user_id = request.query_params.get('coach_user_id')
        coach = coach_public.get_bash_to_c_coach_list()

        # 教练推荐时返回不包括自己在内的，三位随机C端教练列表的教练。
        if coach_user_id:
            coach = coach.exclude(user_id=coach_user_id)
        coach_data = []
        for item in coach.order_by('?')[:3]:
            resume = Resume.objects.filter(coach_id=item.id, deleted=False, is_customization=False).first()
            domain = resume.coach_domain if resume and resume.coach_domain else []

            price = item.display_price
            # 检查payer_amount是否为整数
            if price % 1 == 0:
                price = int(price)

            coach_data.append({
                "head_image_url": resume.head_image_url if resume.head_image_url else item.user.head_image_url,
                "personal_name": item.personal_name,
                "price": price,
                "domain": domain[:3],
                "resume_id": resume.id if resume else None
            })
        resource = resume_resource

        return success_response({'coach_data': coach_data, 'resource': resource})


    @swagger_auto_schema(
        operation_id='获取教练基础信息',
        operation_summary='获取教练基础信息',
        manual_parameters=[
            openapi.Parameter('coach_user_id', openapi.IN_QUERY, description='教练用户标识', type=openapi.TYPE_STRING),
        ],
        tags=['app教练相关']
    )
    @action(methods=['get'], detail=False, url_path='base_info')
    def get_coach_base_info(self, request, *args, **kwargs):
        coach_user_id = request.query_params.get('coach_user_id')
        coach = Coach.objects.filter(user_id=coach_user_id, deleted=False).first()
        if not coach:
            return parameter_error_response('未获取到教练信息')
        resume = Resume.objects.filter(coach_id=coach.id, deleted=False, is_customization=False).first()
        if not resume:
            return parameter_error_response('未获取到教练简历信息')

        coach_data = {
            "user_id": coach_user_id,
            "coach_id": coach.id,
            "order_receiving_status": coach.order_receiving_status,
            "resume_id": resume.id
        }
        return success_response(coach_data)


    @swagger_auto_schema(
        operation_id='获取教练企业信息',
        operation_summary='获取教练企业信息',
        manual_parameters=[
            openapi.Parameter('coach_user_id', openapi.IN_QUERY, description='教练用户标识', type=openapi.TYPE_STRING),
            openapi.Parameter('company_id', openapi.IN_QUERY, description='企业标识', type=openapi.TYPE_STRING),
        ],
        tags=['app教练相关']
    )
    @action(methods=['get'], detail=False, url_path='company_info')
    def get_coach_company_info(self, request, *args, **kwargs):
        try:
            company_id = request.query_params.get('company_id')
            coach_user_id = request.query_params.get('coach_user_id')
            coach_associated_company, is_create = CoachAssociatedCompany.objects.get_or_create(
                coach_user_id=coach_user_id, company_id=company_id, deleted=False)
        except CoachAssociatedCompany.DoesNotExist:
            return parameter_error_response('教练对应企业信息不存在')
        except Exception as e:
            return parameter_error_response()

        company = coach_associated_company.company

        coach_company_data = {
            "id": str(coach_associated_company.id),
            "company_name": company.real_name,  # 企业名称
            "industry": CompanyIndustryEnum.get_display(company.industry) if company.industry else None,  # 所属行业
            "company_attr": CompanyAttrEnum.get_display(company.company_attr) if company.company_attr else None,  # 企业属性
            "scale": CompanyScaleEnum.get_display(company.scale) if company.scale else None,  # 人员规模
            "development_stage": CompanyDevelopmentStageEnum.get_display(company.development_stage) if company.development_stage else None,  # 发展阶段
            "other_background_information": coach_associated_company.other_background_information  # 其他背景信息
        }
        return success_response(coach_company_data)

    @swagger_auto_schema(
        operation_id='修改教练企业信息',
        operation_summary='修改教练企业信息',
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'coach_associated_company_id': openapi.Schema(type=openapi.TYPE_FILE, description='教练关联的企业数据标识'),
                'other_background_information': openapi.Schema(type=openapi.TYPE_FILE, description='其他背景信息'),
                'company_id': openapi.Schema(type=openapi.TYPE_FILE, description='企业标识'),
                'coach_user_id': openapi.Schema(type=openapi.TYPE_FILE, description='教练用户标识'),
            }
        ),
        tags=['app教练相关']
    )
    @action(methods=['post'], detail=False, url_path='company_info/update')
    def post_coach_company_info(self, request, *args, **kwargs):
        try:

            company_id = request.data.get('company_id')
            coach_user_id = request.data.get('coach_user_id')
            coach_associated_company_id = request.data.get('coach_associated_company_id')
            if coach_associated_company_id:
                coach_associated_company = CoachAssociatedCompany.objects.get(id=coach_associated_company_id, deleted=False)
            else:
                coach_associated_company, is_create = CoachAssociatedCompany.objects.get_or_create(
                    coach_user_id=coach_user_id, company_id=company_id, deleted=False)
        except CoachAssociatedCompany.DoesNotExist:
            return parameter_error_response('教练对应企业信息不存在')
        except Exception as e:
            return parameter_error_response()

        if 'other_background_information' in request.data.keys():
            coach_associated_company.other_background_information = request.data.get('other_background_information')
            coach_associated_company.save()
        return success_response()


    #计算指定时间段内有没有可预约的教练
    @action(methods=['get'], detail=False, url_path='coach_available')
    def coach_available(self, request, *args, **kwargs):
        try:
            project_id = request.query_params.get('project_id')
            project = Project.objects.get(id=project_id)
            user = request.user
            project_member = ProjectMember.objects.get(user=user, project=project, deleted=False)
            start_date = request.query_params.get('start_date', None)
            end_date = request.query_params.get('end_date', None)
            during = int(request.query_params.get('during', 30))
            apply_type = request.query_params.get('apply_type', ScheduleApplyTypeEnum.project.value)
            start_date_time = datetime.datetime.strptime(start_date, '%Y-%m-%d')
            end_date_time = datetime.datetime.strptime(end_date, '%Y-%m-%d')
        except (Project.DoesNotExist, ProjectMember.DoesNotExist):
            return parameter_error_response()

        if start_date_time > end_date_time:
            return parameter_error_response('开始日期不能晚于结束日期')

        # 1. 项目打单中进行中
        if project.status in [ProjectStatusEnum.not_sign, ProjectStatusEnum.progress]:
            chemical_interview_module = project_member.chemical_interview.filter(deleted=False).first()

            # 教练池无需处理是否匹配化学面谈。
            if project_member.coach_match_type == OneToOneMatchTypeEnum.all_project_coach.value:
                project_coaches = ProjectCoach.objects.filter(
                    project_id=project_member.project_id, deleted=False, project_group_coach__isnull=True)
                coach = Coach.objects.filter(id__in=list(project_coaches.values_list('coach_id', flat=True)))

            elif project_member.coach_match_type == OneToOneMatchTypeEnum.appoint_coach.value:
                # 配置了化学面谈
                if chemical_interview_module:

                    # 已匹配教练
                    if chemical_interview_module.coaches.filter(
                            deleted=False, chemical_interview_status=ChemicalInterviewStatusEnum.selected,
                            coach__isnull=False).exists():
                        chemical_interview = chemical_interview_module.coaches.filter(
                            deleted=False, chemical_interview_status=ChemicalInterviewStatusEnum.selected,
                            coach__isnull=False)
                        coach = Coach.objects.filter(id__in=list(chemical_interview.values_list('coach_id', flat=True)))
                    # 未匹配教练
                    else:
                        if chemical_interview_module.coach_source == ChemicalInterviewCoachSourceEnum.system_random:
                            chemical_interview = chemical_interview_module.coaches.filter(
                                deleted=False, coach__isnull=False)
                        else:
                            chemical_interview = ProjectCoach.objects.filter(
                                project=project, resume__isnull=False, member__isnull=True,
                                project_group_coach__isnull=True, deleted=False)
                        coach = Coach.objects.filter(id__in=list(chemical_interview.values_list('coach_id', flat=True)))
                # 未配置化学面谈
                else:
                    project_coaches = ProjectCoach.objects.filter(member_id=user.pk, deleted=False,
                                                                  project=project_member.project,
                                                                  status=ProjectCoachStatusEnum.adopt.value,
                                                                  project_group_coach__isnull=True)
                    if project_coaches.exists():
                        coach = Coach.objects.filter(id__in=list(project_coaches.values_list('coach_id', flat=True)))
                    else:
                        coach = Coach.objects.none()
            else:
                coach = Coach.objects.none()

        elif project.status in [ProjectStatusEnum.deactivate, ProjectStatusEnum.completed]:
            coach = Coach.objects.none()
        else:
            return parameter_error_response()

        date_list = {}
        current = start_date_time
        coach_exist = coach.exists()
        while current <= end_date_time:
            date_str = current.strftime('%Y-%m-%d')
            start_time = None
            # 计算每位教练最近可预约时间
            # 如果不是当天 就直接从8点开始算
            if datetime.datetime.today().date() != current.date():
                start_time = current + datetime.timedelta(hours=8)
            else:
                start_time = datetime.datetime.now()

            available = False
            if coach_exist:
                for c in coach:
                    time_point = get_time_point(start_time.strftime('%Y-%m-%d %H:%M:%S'), during, c.user.pk,
                                                apply_type=apply_type)
                    if time_point:
                        available = True
                        break
            if available:
                date_list[date_str] = 1 # 可预约
            else:
                date_list[date_str] = 2 # 不可预约
            current += datetime.timedelta(days=1)

        return success_response(data=date_list)


    @action(methods=['post'], detail=False, url_path='dismiss_todo')
    def dismiss_todo(self, request, *args, **kwargs):
        user = request.user # 获取当前登录的用户
        item_type = request.data.get('item_type') # 从请求体中获取类型
        item_identifier = request.data.get('item_identifier') # 从请求体中获取标识符

        # 1. 输入校验
        if not item_type or not item_identifier:
            return parameter_error_response('缺少必要的参数 item_type 或 item_identifier')

        # 校验 item_type 是否是合法的选项
        valid_item_types = [choice[0] for choice in DismissedTodoItem.ItemType.choices]
        if item_type not in valid_item_types:
             return parameter_error_response('无效的 item_type')

        # 2. 创建或获取记录
        try:
            # 使用 get_or_create 避免重复创建
            dismissed_item, created = DismissedTodoItem.objects.get_or_create(
                user=user,
                item_type=item_type,
                item_identifier=item_identifier
            )

            return success_response()

        except Exception as e:
            # 记录实际错误 e 到你的日志系统
            print(f"Error dismissing todo item: {e}")
            return parameter_error_response()

