import time
from rest_framework.views import APIView
from drf_yasg import openapi
from rest_framework.decorators import action
from drf_yasg.utils import swagger_auto_schema
from rest_framework.viewsets import GenericViewSet
from ..models import FeedBack, FeedBackImage, User, Evaluation, EvaluationQuestion, EvaluationOption, \
    EvaluationReport, PublicAttr, EvaluationReportScore
from rest_framework import serializers
from django.db.models import Sum, Avg
from ..views.constant import ATTR_TYPE_EVALUATION_REPORT, ATTR_TYPE_EVALUATION_ANSWER


class EvaluationOptionSerializers(serializers.ModelSerializer):
    class Meta:
        model = EvaluationOption
        fields = ('id', 'order', 'title', 'score', 'question', 'type')
