import json

from drf_yasg import openapi
from rest_framework import viewsets
from drf_yasg.utils import swagger_auto_schema
from rest_framework.decorators import action

from utils.work_wechat import WorkWechat
from utils.xiaoe_tech import XiaoeTech
from wisdom_v2.models import User
from utils.api_response import success_response, parameter_error_response
from wisdom_v2.views.user_actions import UserSerializer


class AppXiaoeTechUser(viewsets.ModelViewSet):
    queryset = User.objects.filter(deleted=False)
    serializer_class = UserSerializer

    @swagger_auto_schema(
        operation_id='获取小鹅通客户信息',
        operation_summary='获取小鹅通客户信息',
        manual_parameters=[
            openapi.Parameter('external_user_id', openapi.TYPE_STRING, description='企业微信外部联系人的userid，不是企业成员的帐号', type=openapi.TYPE_STRING),
            ],
        tags=['移动端小鹅通相关']
    )
    @action(methods=['get'], detail=False, url_path='user_details', authentication_classes=[])
    def xiaoe_tech_user_details(self, request, *args, **kwargs):
        external_user_id = request.query_params.get('external_user_id')
        if not external_user_id:
            return parameter_error_response('未获取到企业微信外部联系人的userid')

        # 置换用户微信的unionid
        wechat_unionid_state, unionid_msg = WorkWechat().get_user_unionid(external_user_id)
        if not wechat_unionid_state:
            return parameter_error_response(unionid_msg)

        # 获取小鹅通用户数据
        xiaoe_tech_user_state, user = XiaoeTech().get_user_details(unionid_msg)
        if not xiaoe_tech_user_state:
            return parameter_error_response(user)
        data = {
            'name': user.get('name'),
            'gender': user.get('gender'),
            'phone': user.get('phone'),
            'company': user.get('company'),
            'city': user.get('city'),
            'email': user.get('wx_email'),
            'job': user.get('job'),
            'orders': [],
            'forms': []
        }

        # 获取小鹅通用户订单数据
        xiaoe_tech_orders_state, orders_msg = XiaoeTech().get_user_orders(user.get('user_id'))
        if not xiaoe_tech_orders_state:
            return parameter_error_response(orders_msg)
        for item in orders_msg:
            data['orders'].append({
                'title': item.get('title'),
                'created_time': item.get('created_time'),
                'price': item.get('price'),
            })

        # 获取小鹅通用户信息收集数据
        xiaoe_tech_information_state, information_msg = XiaoeTech().get_user_information(user.get('user_id'))
        if not xiaoe_tech_information_state:
            return parameter_error_response(information_msg)
        for item in information_msg:
            data['forms'].append({
                'form_name': item.get('form_name'),
                'collect_time': item.get('collect_time'),
                'extra': json.loads(item.get('extra')),
            })
        return success_response(data)
