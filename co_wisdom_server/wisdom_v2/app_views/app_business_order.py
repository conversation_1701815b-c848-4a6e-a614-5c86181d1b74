import datetime
import json

from django.db.models import Q
from decimal import Decimal
from django.db import transaction
from rest_framework.decorators import action
from drf_yasg.utils import swagger_auto_schema
from drf_yasg import openapi
from rest_framework import viewsets

from utils.api_response import success_response, parameter_error_response
from utils.pagination import StandardResultsSetPagination
from wisdom_v2.app_views.app_business_order_actions import AppBusinessOrderSerializer, get_can_withdrawn_business_order
from wisdom_v2.common.business_settlement_public import generate_business_settlements
from wisdom_v2.enum.business_order_enum import BusinessOrderPayStatusEnum, BusinessOrderSettlementStatusEnum, \
    WorkTypeEnum, BusinessOrderWithdrawalStatusEnum
from wisdom_v2.enum.project_interview_enum import ProjectInterviewPlaceCategoryEnum, ProjectInterviewTypeEnum, \
    GroupCoachTypeEnum
from wisdom_v2.models_file import BusinessOrder, BusinessOrder2Object
from wisdom_v2.models import Coach, ProjectInterview


class AppBusinessOrderViewSet(viewsets.ModelViewSet):
    queryset = BusinessOrder.objects.filter(deleted=False).order_by('-work_start_time')
    serializer_class = AppBusinessOrderSerializer

    @swagger_auto_schema(
        operation_id='小程序端订单列表',
        operation_summary='小程序端订单列表',
        manual_parameters=[
            openapi.Parameter('coach_user_id', openapi.IN_QUERY, description='教练', type=openapi.TYPE_STRING),
            openapi.Parameter('status', openapi.IN_QUERY, description='1-可提现订单 2-已提现订单 3-排除处理中的可提现订单', type=openapi.TYPE_STRING),
            openapi.Parameter('page', openapi.IN_QUERY, description='第几页', type=openapi.TYPE_NUMBER),
            openapi.Parameter('page_size', openapi.IN_QUERY, description='每页显示条数默认15条', type=openapi.TYPE_NUMBER),
        ],
        tags=['小程序教练端订单相关']
    )
    def list(self, request, *args, **kwargs):
        try:
            coach_user_id = request.query_params['coach_user_id']
            coach = Coach.objects.get(user_id=coach_user_id, deleted=False)
            status = int(request.query_params.get('status', 1))
        except (KeyError, Coach.DoesNotExist):
            return parameter_error_response()
        business_order = self.get_queryset().filter(coach=coach)
        if status == 1:  # 可提现订单
            business_order = get_can_withdrawn_business_order(business_order)
        elif status == 2:  # 已提现订单
            business_order = business_order.filter(
                # 提现状态变为已提现订单时，还未真实提现到账，结算状态变为已结算时，才提现到账，所以需要加上结算状态
                withdrawal_status=BusinessOrderWithdrawalStatusEnum.withdrawn.value,
                settlement_status=BusinessOrderSettlementStatusEnum.settled,
                deleted=False)
        elif status == 3:  # 排除处理中的可提现订单
            # 处理中的订单会有结算id
            business_order = business_order.filter(business_settlement_id__isnull=True)
            business_order = get_can_withdrawn_business_order(business_order)

        # 分页
        paginator = StandardResultsSetPagination()
        page_list = paginator.paginate_queryset(business_order, self.request)
        serializer = self.get_serializer(page_list, many=True)
        response = paginator.get_paginated_response(serializer.data)
        return success_response(response)

    @swagger_auto_schema(
        operation_id='订单详情',
        operation_summary='订单详情',
        manual_parameters=[
            openapi.Parameter('order_id	', openapi.IN_QUERY, description='订单id', type=openapi.TYPE_STRING),
        ],
        tags=['小程序教练端订单相关']
    )
    @action(methods=['get'], detail=False, url_path='detail')
    def order_detail(self, request, *args, **kwargs):
        try:
            order_id = request.query_params['order_id']
            business_order = BusinessOrder.objects.get(id=order_id, deleted=False)
            mp = json.loads(request.headers.get('mp')) if request.headers.get('mp') else None
        except (KeyError, BusinessOrder.DoesNotExist):
            return parameter_error_response()
        data = self.serializer_class(business_order, many=False, context={'mp': mp}).data
        return success_response(data)

    @swagger_auto_schema(
        operation_id='辅导列表点击提现按钮判断',
        operation_summary='辅导列表点击提现按钮判断',
        manual_parameters=[
            openapi.Parameter('interview_id	', openapi.IN_QUERY, description='辅导id', type=openapi.TYPE_STRING),
        ],
        tags=['小程序教练端订单相关']
    )
    @action(methods=['get'], detail=False, url_path='check_order')
    def check_order(self, request, *args, **kwargs):
        try:
            interview_id = request.query_params['interview_id']
            interview = ProjectInterview.objects.get(id=interview_id, deleted=False)
        except (KeyError, ProjectInterview.DoesNotExist):
            return parameter_error_response()
        if interview.public_attr.end_time > datetime.datetime.now():
            return success_response({"is_jump": False, "msg": "辅导还未结束，暂不可提现", "order_id": None})
        elif not interview.coach_record_status and interview.place_category != \
                ProjectInterviewPlaceCategoryEnum.offline_group_coach:
            return success_response({"is_jump": False, "msg": "还未填写辅导记录，暂不可提现", "order_id": None})

        business_order2object = None
        if interview.place_category == ProjectInterviewPlaceCategoryEnum.online_one_to_one:
            type = WorkTypeEnum.one_to_one if interview.type == ProjectInterviewTypeEnum.formal_interview \
                else WorkTypeEnum.stakeholder_interview
            business_order2object = BusinessOrder2Object.objects.filter(
                object_id=interview_id, type=type, deleted=False).first()
        elif interview.place_category == ProjectInterviewPlaceCategoryEnum.offline_group_coach:
            group_coach = interview.coach_group_module.filter(deleted=False).first()
            if group_coach:
                project_group_coach = group_coach.project_group_coach
                type = WorkTypeEnum.group_coach if project_group_coach.type == GroupCoachTypeEnum.collective_tutoring \
                    else WorkTypeEnum.group_counseling
                business_order2object = BusinessOrder2Object.objects.filter(
                    object_id=project_group_coach.id, type=type, deleted=False).first()
        if business_order2object:
            business_order = business_order2object.business_order
            if all([
                business_order.pay_status in [
                    BusinessOrderPayStatusEnum.paid,
                    BusinessOrderPayStatusEnum.non_payment],
                business_order.withdrawal_status == BusinessOrderWithdrawalStatusEnum.can_withdraw.value
            ]):
                coach_actual_income = str(Decimal(str(business_order.coach_actual_income)) / Decimal('100'))
                return success_response({"is_jump": True, "msg": None, "order_id": business_order.id,
                                         "coach_actual_income": coach_actual_income})
            else:
                return success_response({"is_jump": False, "msg": "客户支付后才能提现哦～", "order_id": None})
        return success_response({"is_jump": False, "msg": "当前无法提现哦～请联系管理员", "order_id": None})

    @swagger_auto_schema(
        operation_id='小程序端教练发起提现',
        operation_summary='小程序端教练发起提现',
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'order_ids': openapi.Schema(type=openapi.TYPE_NUMBER, description='订单id列表'),
                'coach_user_id': openapi.Schema(type=openapi.TYPE_NUMBER, description='教练用户id'),
            }
        ),
        tags=['后台结算管理相关']
    )
    @action(methods=['post'], detail=False, url_path='create_settlements')
    def create_settlements(self, request, *args, **kwargs):
        try:
            order_ids = request.data['order_ids']
            business_order = BusinessOrder.objects.filter(id__in=order_ids, deleted=False)
            coach_user_id = request.data['coach_user_id']
            coach = Coach.objects.get(deleted=False, user_id=coach_user_id)
        except (KeyError, BusinessOrder.DoesNotExist, Coach.DoesNotExist):
            return parameter_error_response()
        # if not coach.is_settlement_info:
        #     return success_response({"is_success": False})
        cant_settle_order = business_order.filter(
            # 没有标记支付状态，禁止提现
            Q(pay_status=BusinessOrderPayStatusEnum.non) |
            # 提现状态是不可提现，禁止提现
            Q(withdrawal_status=BusinessOrderWithdrawalStatusEnum.non_withdrawable.value) |
            # 订单已结算，禁止提现
            Q(settlement_status=BusinessOrderSettlementStatusEnum.settled))
        if cant_settle_order.exists():
            return parameter_error_response('只有可提现订单才能发起提现')

        # 已提现订单禁止重复操作
        if business_order.filter(withdrawal_status=BusinessOrderWithdrawalStatusEnum.withdrawn.value).exists():
            return parameter_error_response('当前存在已申请提现订单，请勿重复操作')
        # 已生成结算单禁止重复操作
        if business_order.filter(business_settlement__isnull=False).exists():
            return parameter_error_response('当前存在已申请提现订单，请勿重复操作')
        with transaction.atomic():
            generate_business_settlements(coach, business_order, coach.user)
        return success_response({"is_success": True})


