from ast import literal_eval
from decimal import Decimal

from rest_framework import serializers

from utils.multiple_selection_map import get_multiple_selection_detail
from wisdom_v2.common import personal_activity_public
from wisdom_v2.enum.service_content_enum import CoachWorkingYearsEnum, CoachAuthEnum, UserInviteTypeEnum
from wisdom_v2.models import UserInviteRecord
from wisdom_v2.models_file import PersonalActivity


class AppPersonalActivityDetailSerializer(serializers.ModelSerializer):

    expire_time = serializers.DateField(help_text='开始日期', required=False, format='%Y-%m-%d')

    class Meta:
        model = PersonalActivity
        fields = ('id', 'price', 'expire_time', 'limit_count')


class AppPersonalActivityCoachShareSerializer(serializers.ModelSerializer):
    """
    个人活动详情和教练分享信息的序列化器。

    该序列化器扩展了个人活动的基本信息，包括教练的资质、工作经验和擅长领域等。
    """
    head_image_url = serializers.Char<PERSON>ield(source='resume.head_image_url', help_text='简历头像')
    share_image_url = serializers.CharField(source='resume.share_image_url', help_text='简历头像')
    true_name = serializers.CharField(source='coach.user.cover_name', help_text='真实姓名')
    price = serializers.SerializerMethodField(help_text='金额')
    coach_auth = serializers.SerializerMethodField(help_text='资质')
    all_look_count = serializers.SerializerMethodField(help_text='总查看数')
    working_years = serializers.SerializerMethodField(help_text='时长')
    coach_domain = serializers.SerializerMethodField(help_text='擅长领域')
    highest_position = serializers.SerializerMethodField(help_text='最高职位')
    invite_url = serializers.SerializerMethodField(help_text='二维码链接')
    user_look_count = serializers.SerializerMethodField(help_text='用户查看数')
    interview_pay_count = serializers.SerializerMethodField(help_text='支付次数')
    invite_code = serializers.SerializerMethodField(help_text='邀请码')
    personal_name = serializers.SerializerMethodField(help_text='对用户可见姓名')
    city = serializers.SerializerMethodField(help_text='城市')
    posters_text = serializers.SerializerMethodField(help_text='海报一句话')
    job_profile = serializers.SerializerMethodField(help_text='工作一句话')
    qualification = serializers.SerializerMethodField(help_text='教练证书')

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self._cache = {}

    # 计算或检索订单详情缓存
    def calculate_coach_obj(self, obj):
        if 'coach_obj' not in self._cache:
            self._cache['coach_obj'] = obj.coach
        return self._cache['coach_obj']

    def calculate_resume_obj(self, obj):
        if 'resume_obj' not in self._cache:
            self._cache['resume_obj'] = obj.resume
        return self._cache['resume_obj']

    def get_posters_text(self, obj):
        # 解析并获取教练的海报一句话
        if obj.posters_text:
            return obj.posters_text
        coach = self.calculate_coach_obj(obj)
        return coach.posters_text if coach else None

    def get_job_profile(self, obj):
        # 解析并获取教练的工作一句话
        if obj.job_profile:
            return obj.job_profile
        resume = self.calculate_resume_obj(obj)
        return resume.job_profile if resume else None

    def get_personal_name(self, obj):
        # 解析并获取教练的对用户可见姓名
        if obj.personal_name:
            return obj.personal_name
        coach = self.calculate_coach_obj(obj)
        return coach.personal_name if coach else None

    def get_city(self, obj):
        # 解析并获取教练的城市
        if obj.city:
            return obj.city
        coach = self.calculate_coach_obj(obj)
        return coach.city if coach else None

    def get_price(self, obj):
        return Decimal(str(obj.price)) / Decimal('100')

    def get_coach_auth(self, obj):
        # 获取教练的教练资质
        if obj.coach_auth:
            coach_auth = obj.coach_auth
        else:
            resume = self.calculate_resume_obj(obj)
            coach_auth = resume.coach_auth if resume else None
        coach_auth = CoachAuthEnum.get_display(coach_auth) \
            if coach_auth in CoachAuthEnum.get_describe_keys() else None
        return coach_auth

    def get_qualification(self, obj):
        # 获取教练的教练证书
        if obj.qualification:
            return obj.qualification
        resume = self.calculate_resume_obj(obj)
        return resume.qualification if resume else None

    def get_working_years(self, obj):
        # 获取教练的工作时长
        if obj.working_years:
            working_years = obj.working_years
        else:
            resume = self.calculate_resume_obj(obj)
            working_years = resume.working_years if resume else None
        # 获取教练的工作年限
        return f'{CoachWorkingYearsEnum.get_display(working_years)}教练经验' if working_years else None

    def get_coach_domain(self, obj):
        # 获取教练的擅长领域，最多三个
        if obj.coach_domain:
            coach_domain = obj.coach_domain
        else:
            resume = self.calculate_resume_obj(obj)
            coach_domain = resume.coach_domain if resume else None
        return coach_domain[:3] if coach_domain else []

    def get_highest_position(self, obj):
        # 解析并获取教练的最高职位
        if obj.highest_position:
            highest_position = obj.highest_position
        else:
            resume = self.calculate_resume_obj(obj)
            highest_position = resume.highest_position if resume else None
        if highest_position:
            if isinstance(highest_position, list):
                return highest_position[0]
            if isinstance(highest_position, str):
                return literal_eval(highest_position)[0]
        return None  # 如果没有最高职位信息，则返回None

    def get_invite_url(self, obj):
        # 获取教练分享的邀请链接
        return personal_activity_public.get_coach_share_invite_url(obj)

    def get_all_look_count(self, obj):
        # 获取教练分享的所有查看次数
        return personal_activity_public.get_coach_share_all_look_count(obj.coach.user)

    def get_interview_pay_count(self, obj):
        # 获取教练分享的支付次数
        return personal_activity_public.get_coach_share_pay_count(obj.coach.user, is_set=True)

    def get_user_look_count(self, obj):
        # 获取教练分享的用户查看数
        return len(personal_activity_public.get_coach_share_user_look_list(obj.coach.user))

    def get_invite_code(self, obj):
        # 根据个人活动的ID、关联的教练用户以及邀请类型为教练分享，查询用户邀请记录
        invite_record = UserInviteRecord.objects.filter(
            object_id=str(obj.pk),  # 将个人活动的主键作为对象ID
            referrer=obj.coach.user,  # 教练的用户作为推荐人
            type=UserInviteTypeEnum.coach_share.value,  # 邀请类型为教练分享
        ).first()  # 获取查询结果中的第一条记录

        # 如果查询到了邀请记录
        if invite_record:
            # 返回邀请码
            return str(invite_record.uuid)
        return

    class Meta:
        model = PersonalActivity
        fields = ('id', 'head_image_url', 'true_name', 'personal_name', 'city', 'posters_text', 'job_profile',
                  'price', 'all_look_count', 'coach_auth', 'working_years', 'coach_domain', 'highest_position',
                  'invite_url', 'user_look_count', 'interview_pay_count', 'limit_count', 'invite_code',
                  'share_image_url', 'qualification')


class AppPersonalActivityCoachUpdateResumeShareSerializer(serializers.ModelSerializer):
    """
    个人活动详情和教练分享信息的序列化器。

    该序列化器扩展了个人活动的基本信息，包括教练的资质、工作经验和擅长领域等。
    """
    coach_auth = serializers.SerializerMethodField(help_text='资质')
    working_years = serializers.SerializerMethodField(help_text='时长')
    coach_domain = serializers.SerializerMethodField(help_text='擅长领域')
    highest_position = serializers.SerializerMethodField(help_text='最高职位')
    personal_name = serializers.SerializerMethodField(help_text='对用户可见姓名')
    city = serializers.SerializerMethodField(help_text='城市')
    posters_text = serializers.SerializerMethodField(help_text='海报一句话')
    job_profile = serializers.SerializerMethodField(help_text='工作一句话')
    qualification = serializers.SerializerMethodField(help_text='教练证书')

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self._cache = {}

    # 计算或检索订单详情缓存
    def calculate_coach_obj(self, obj):
        if 'coach_obj' not in self._cache.keys():
            self._cache['coach_obj'] = obj.coach
        return self._cache['coach_obj']

    def calculate_resume_obj(self, obj):
        if 'resume_obj' not in self._cache.keys():
            self._cache['resume_obj'] = obj.resume
        return self._cache['resume_obj']

    def get_posters_text(self, obj):
        # 解析并获取教练的海报一句话
        if obj.posters_text:
            return obj.posters_text
        coach = self.calculate_coach_obj(obj)
        return coach.posters_text if coach else None

    def get_job_profile(self, obj):
        # 解析并获取教练的工作一句话
        if obj.job_profile:
            return obj.job_profile
        resume = self.calculate_resume_obj(obj)
        return resume.job_profile if resume else None

    def get_personal_name(self, obj):
        # 解析并获取教练的对用户可见姓名
        if obj.personal_name:
            return obj.personal_name
        coach = self.calculate_coach_obj(obj)
        return coach.personal_name if coach else None

    def get_city(self, obj):
        # 解析并获取教练的城市
        if obj.city:
            return obj.city
        coach = self.calculate_coach_obj(obj)
        return coach.city if coach else None

    def get_coach_auth(self, obj):
        # 获取教练的教练资质
        if obj.coach_auth:
            return obj.coach_auth
        resume = self.calculate_resume_obj(obj)
        return resume.coach_auth if resume else None

    def get_qualification(self, obj):
        # 获取教练的教练证书
        if obj.qualification:
            return obj.qualification
        resume = self.calculate_resume_obj(obj)
        return resume.qualification if resume else None

    def get_working_years(self, obj):
        # 获取教练的擅长领域，最多三个
        if obj.working_years:
            working_years = obj.working_years
        else:
            resume = self.calculate_resume_obj(obj)
            working_years = resume.working_years if resume else None
        # 获取教练的工作年限
        return working_years

    def get_coach_domain(self, obj):
        # 获取教练的擅长领域，最多三个
        if obj.coach_domain:
            coach_domain = obj.coach_domain
        else:
            resume = self.calculate_resume_obj(obj)
            coach_domain = resume.coach_domain if resume else None

        data = get_multiple_selection_detail('coach_domain', coach_domain)
        return data

    def get_highest_position(self, obj):
        # 解析并获取教练的最高职位
        if obj.highest_position:
            highest_position = obj.highest_position
        else:
            resume = self.calculate_resume_obj(obj)
            highest_position = resume.highest_position if resume else None
        data = get_multiple_selection_detail('highest_position', highest_position)

        return data  # 如果没有最高职位信息，则返回None

    class Meta:
        model = PersonalActivity
        fields = ('id', 'personal_name', 'city', 'posters_text', 'job_profile','coach_auth', 'working_years',
                  'coach_domain', 'highest_position', 'qualification')
