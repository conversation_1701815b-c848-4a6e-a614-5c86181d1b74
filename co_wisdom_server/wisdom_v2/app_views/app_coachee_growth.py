import datetime
import json

from dateutil.relativedelta import relativedelta
from django.db import transaction
from django.db.models import Q
from drf_yasg import openapi
from rest_framework import viewsets
from drf_yasg.utils import swagger_auto_schema
from rest_framework.decorators import action

from utils.api_response import parameter_error_response, success_response
from utils.miniapp_version_judge import compare_version
from utils.pagination import StandardResultsSetPagination
from wisdom_v2 import utils
from wisdom_v2.app_views.app_coachee_growth_actions import AppCoacheeGrowthGoalsViewSerializers, \
    AppCoacheeGrowthNodeCoachTaskViewSerializers, \
    AppCoacheeGrowthNodeEvaluationModuleViewSerializers
from wisdom_v2.common import stakeholder_interview_public, user_public
from wisdom_v2.enum.service_content_enum import NewCoachTaskTypeEnum
from wisdom_v2.models import GrowthGoals, PublicAttr, CoachTask, EvaluationReport, EvaluationModule, \
    EvaluationReportScore2Growth, GrowthG<PERSON><PERSON><PERSON><PERSON><PERSON>, WorkWechat<PERSON>ser, User, ProjectMember
from wisdom_v2.models_file import GrowthGoalsModule
from wisdom_v2.views.constant import ATTR_TYPE_GROWTH_GOALS, ATTR_TYPE_COACH_TASK, ATTR_TYPE_EVALUATION_REPORT
from utils.messagecenter.getui import send_work_wechat_coach_notice


class AppCoacheeGrowthGoalsViewSet(viewsets.ModelViewSet):
    queryset = GrowthGoals.objects.filter(deleted=False)
    serializer_class = AppCoacheeGrowthGoalsViewSerializers

    @swagger_auto_schema(
        operation_id='成长目标详情',
        operation_summary='成长目标详情',
        manual_parameters=[
            openapi.Parameter('growth_goals_id', openapi.IN_QUERY, description='成长目标id', type=openapi.TYPE_NUMBER),
        ],
        tags=['被教练者成长相关']
    )
    @action(methods=['get'], detail=False, url_path='detail')
    def growth_goals_detail(self, request, *args, **kwargs):

        try:
            growth_goals = GrowthGoals.objects.get(pk=request.query_params.get('growth_goals_id', 0))
        except GrowthGoals.DoesNotExist as e:
            return parameter_error_response('对应成长目标不存在')
        serializer = self.get_serializer(growth_goals)
        return success_response(serializer.data)

    @swagger_auto_schema(
        operation_id='成长目标列表',
        operation_summary='成长目标列表',
        manual_parameters=[
            openapi.Parameter(
                'coach_user_id', openapi.IN_QUERY, description='教练id', type=openapi.TYPE_STRING),
            openapi.Parameter(
                'coachee_user_id', openapi.IN_QUERY, description='被教练者id', type=openapi.TYPE_NUMBER, required=True),
            openapi.Parameter('page', openapi.IN_QUERY, description='第几页', type=openapi.TYPE_NUMBER),
            openapi.Parameter('page_size', openapi.IN_QUERY, description='每页显示条数默认15条', type=openapi.TYPE_NUMBER)
        ],
        tags=['被教练者成长相关']
    )
    def list(self, request, *args, **kwargs):
        growth_goals = self.get_queryset()
        coachee_user_id = request.query_params.get('coachee_user_id')
        coach_user_id = request.query_params.get('coach_user_id')
        if not coachee_user_id:
            return parameter_error_response('未获取到被教练者信息')

        growth_goals = growth_goals.filter(
            public_attr__user_id=coachee_user_id, public_attr__type=ATTR_TYPE_GROWTH_GOALS)
        if coach_user_id:
            growth_goals = growth_goals.filter(
                Q(public_attr__target_user=None) | Q(public_attr__target_user_id=coach_user_id)
            )

        growth_goals = growth_goals.order_by('is_finish', '-created_at')

        # 分页
        paginator = StandardResultsSetPagination()
        page_list = paginator.paginate_queryset(growth_goals, self.request)
        serializer = self.get_serializer(page_list, many=True)
        response = paginator.get_paginated_response(serializer.data)
        return success_response(response)

    @swagger_auto_schema(
        operation_id='修改成长目标信息',
        operation_summary='修改成长目标信息',
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            required=['user_id', 'growth_goals_id'],
            properties={
                'growth_goals_id': openapi.Schema(type=openapi.TYPE_NUMBER, description='成长目标id'),
                'user_id': openapi.Schema(type=openapi.TYPE_NUMBER, description='修改者的用户id',),
                'content': openapi.Schema(type=openapi.TYPE_STRING, description='内容数据'),
                'tag': openapi.Schema(type=openapi.TYPE_NUMBER, description='能力标签'),
                'progression': openapi.Schema(type=openapi.TYPE_NUMBER, description='完成度'),
                'month': openapi.Schema(type=openapi.TYPE_NUMBER, description='月数'),
                'change': openapi.Schema(type=openapi.TYPE_STRING, description='完成目标后的改变'),
                'deleted': openapi.Schema(type=openapi.TYPE_NUMBER, description='是否删除')
            }
        ),
        tags=['被教练者成长相关']
    )
    @action(methods=['post'], detail=False, url_path='update')
    def growth_goals_update(self, request, *args, **kwargs):
        try:
            growth_goals = GrowthGoals.objects.get(pk=request.data.get('growth_goals_id'))
        except GrowthGoals.DoesNotExist as e:
            return parameter_error_response('成长目标数据对象不存在')

        user_id = request.data.get('user_id')
        progression = request.data.get('progression')
        deleted = request.data.get('deleted')

        # 其他用户不可修改
        all_user_id = [growth_goals.public_attr.user_id, growth_goals.public_attr.target_user_id]
        if user_id not in all_user_id:
            return parameter_error_response("无权限修改")
        # 用户可修改教练及自己创建的成长目标、教练只能修改自己创建的成长目标
        if growth_goals.public_attr.user.id != user_id and not growth_goals.public_attr.target_user_id:
            return parameter_error_response('无法修改用户的成长目标')

        if progression:
            if growth_goals.public_attr.user.id != user_id:
                return parameter_error_response('只有被教练者可修改完成度')
            if int(progression) >= 10:
                request.data['is_finish'] = True
            else:
                request.data['is_finish'] = False

        if deleted:
            if growth_goals.public_attr.user.id != user_id and growth_goals.public_attr.target_user.id != user_id:
                return parameter_error_response('只有教练本人或学员本人可删除数据')

        data = request.data.copy()
        serializer = self.get_serializer(data=data)
        serializer.is_valid(raise_exception=True)
        growth_goals = serializer.update(growth_goals, data)
        # 学院修改教练给学院创建的成长目标  给教练企业微信通知
        if growth_goals.public_attr.target_user_id and user_id != growth_goals.public_attr.target_user_id: #
            work_wechat_user = WorkWechatUser.objects.filter(user_id=growth_goals.public_attr.target_user_id,
                                                             deleted=False).first()
            if work_wechat_user:
                user = User.objects.get(pk=user_id)
                project_member = ProjectMember.objects.filter(user_id=user_id, project__deleted=False,
                                                              deleted=False).first()
                content_item = [
                    {"key": "成长目标修改提醒", "value": "成长目标修改提醒"},
                    {"key": "客户名称", "value": user.cover_name},
                    {"key": "所属企业", "value": project_member.project.company.real_name},
                    {"key": "所属项目", "value": project_member.project.name}
                ]
                send_work_wechat_coach_notice.delay(
                    work_wechat_user.wx_user_id,
                    'growth_goal_change',
                    growth_goals_id=growth_goals.pk,
                    user_name=user.cover_name,
                    content_item=content_item,
                    user_id=user_id,
                    coachee_id=user_id,
                    coach_name=work_wechat_user.user.cover_name,
                    coach_id=work_wechat_user.user_id
                )
        serializer = self.get_serializer(growth_goals)
        return success_response(serializer.data)

    @swagger_auto_schema(
        operation_id='创建成长目标信息',
        operation_summary='创建成长目标信息',
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            required=['coach_user_id', 'content'],
            properties={
                'coach_user_id': openapi.Schema(type=openapi.TYPE_NUMBER, description='教练id'),
                'coachee_user_id': openapi.Schema(type=openapi.TYPE_NUMBER, description='被教练者id'),
                'content': openapi.Schema(type=openapi.TYPE_STRING, description='内容数据'),
                'tag': openapi.Schema(type=openapi.TYPE_NUMBER, description='能力标签'),
                'month': openapi.Schema(type=openapi.TYPE_NUMBER, description='月数'),
                'project_id': openapi.Schema(type=openapi.TYPE_NUMBER, description='项目id'),
                'change': openapi.Schema(type=openapi.TYPE_STRING, description='完成目标后的改变'),
                'evaluation_report_score_id': openapi.Schema(type=openapi.TYPE_NUMBER, description='测评报告评分表id')
            }
        ),
        tags=['被教练者成长相关']
    )
    def create(self, request, *args, **kwargs):

        coach_user_id = request.data.get('coach_user_id', None)
        coachee_user_id = request.data.get('coachee_user_id')
        content = request.data.get('content')
        tag = request.data.get('tag', None)
        month = int(request.data.get('month'))
        change = request.data.get('change')
        evaluation_report_score_id = request.data.get('evaluation_report_score_id')

        if not coachee_user_id:
            return parameter_error_response('用户id为必传参数')
        if not content:
            return parameter_error_response('未获取到具体内容')
        to_long = [1 if len(c['content']) > 200 else 0 for c in change]
        if 1 in to_long:
            return parameter_error_response('转变行为限制200字以内')

        # 读取用户项目
        project_member = user_public.get_user_to_project_member_all(coachee_user_id)
        project_id = project_member[0].project_id if project_member else None

        try:
            with transaction.atomic():
                # 开始结束时间
                start_time = datetime.datetime.now()
                end_time = start_time + relativedelta(months=month)
                public_attr = PublicAttr.objects.create(
                    user_id=coachee_user_id,
                    project_id=project_id,
                    target_user_id=coach_user_id,
                    type=ATTR_TYPE_GROWTH_GOALS,
                    start_time=start_time,
                    end_time=end_time
                )
                growth_goals = GrowthGoals.objects.create(
                    public_attr=public_attr,
                    content=content,
                    tag=tag, month=month, change=change
                )
                growth_module = GrowthGoalsModule.objects.filter(
                    project_bundle__project_member__project_id=project_id,
                    project_bundle__project_member__user_id=coachee_user_id,
                    project_bundle__project_member__deleted=False,
                    deleted=False,
                    growth_goals__isnull=True).first()
                if growth_module:
                    growth_module.growth_goals = growth_goals
                    growth_module.save()
                if type(change) == str:
                    GrowthGoalsChange.objects.create(growth_goals_id=growth_goals.pk, content=change)
                else:
                    for c in change:
                        GrowthGoalsChange.objects.create(growth_goals_id=growth_goals.pk, content=c['content'])
            serializer = self.get_serializer(growth_goals)
            if evaluation_report_score_id:
                EvaluationReportScore2Growth.objects.create(
                    evaluation_report_score_id=evaluation_report_score_id,
                    growth_goals_id=growth_goals.pk
                )
        except Exception as e:
            return parameter_error_response(str(e))

        return success_response(serializer.data)


class AppCoacheeGrowthNodeViewSet(viewsets.ModelViewSet):
    queryset = GrowthGoals.objects.filter(deleted=False)
    serializer_class = AppCoacheeGrowthGoalsViewSerializers

    @swagger_auto_schema(
        operation_id='成长节点列表',
        operation_summary='成长节点列表',
        manual_parameters=[
            openapi.Parameter(
                'user_id', openapi.IN_QUERY, description='用户id', type=openapi.TYPE_NUMBER, required=True),
            openapi.Parameter('page', openapi.IN_QUERY, description='第几页', type=openapi.TYPE_NUMBER, default=1),
            openapi.Parameter('page_size', openapi.IN_QUERY, description='每页显示条数', type=openapi.TYPE_NUMBER, default=15)
        ],
        tags=['被教练者成长相关']
    )
    def list(self, request, *args, **kwargs):
        try:
            mp = json.loads(request.headers.get('mp')) if request.headers.get('mp') else None
            user_id = request.query_params.get('user_id')
            # 找到填写的改变观察反馈报告
            change_observation_report_data = utils.get_change_observation_report_data(user_id)

            # 利益相关者访谈
            stakeholder_interview_module_data, coach_task_id = \
                stakeholder_interview_public.growth_node_get_stakeholder_interview_module_data(user_id)

            # 教练任务数据查询
            coach_tasks = CoachTask.objects.filter(
                public_attr__target_user_id=user_id,
                public_attr__type=ATTR_TYPE_COACH_TASK,
                deleted=False
            ).exclude(
                type=NewCoachTaskTypeEnum.stakeholder_research,
                stakeholder_submit_time__isnull=True).order_by('-hours')
            if coach_task_id:
                coach_tasks = coach_tasks.exclude(id=coach_task_id)
            # 教练任务序列化
            coach_tasks_serializer = AppCoacheeGrowthNodeCoachTaskViewSerializers(coach_tasks, many=True)

            # 获取所有填写的测评记录
            raw_evaluation_report = EvaluationReport.objects.filter(
                deleted=False,
                public_attr__user_id=user_id,
                public_attr__type=ATTR_TYPE_EVALUATION_REPORT
            )

            # 计算填写的测评模板出现次数
            evaluation_report = {}
            for item in raw_evaluation_report:
                if evaluation_report.get(str(item.evaluation.id)):
                    evaluation_report[str(item.evaluation.id)][1] += 1
                else:
                    evaluation_report[str(item.evaluation.id)] = [item.id, 1]

            # 所有项目绑定测评的记录
            raw_evaluation_module = EvaluationModule.objects.filter(
                project_bundle__project_member__user_id=user_id,
                deleted=False,
            ).order_by('-created_at')

            # 已完成的测评记录做标记
            evaluation_module = {}
            for item in raw_evaluation_module:
                if evaluation_report.get(str(item.evaluation.id)) and evaluation_report.get(
                        str(item.evaluation.id))[1] > 0:
                    evaluation_report[str(item.evaluation.id)][1] -= 1
                    evaluation_module[item.id] = evaluation_report[str(item.evaluation.id)][0]

            # 测评序列化
            evaluation_serializer = AppCoacheeGrowthNodeEvaluationModuleViewSerializers(
                raw_evaluation_module, many=True, context=evaluation_module)

        except Exception as e:
            return parameter_error_response(str(e))

        # 分页处理
        paginator = StandardResultsSetPagination()
        if mp and compare_version(mp.get('version'), '2.16.2') < 0:
            page_list = paginator.paginate_queryset(
                [*change_observation_report_data, *coach_tasks_serializer.data,
                 *evaluation_serializer.data], self.request)
        else:
            page_list = paginator.paginate_queryset(
                [*change_observation_report_data, *stakeholder_interview_module_data, *coach_tasks_serializer.data,
                 *evaluation_serializer.data], self.request)
        response = paginator.get_paginated_response(page_list)
        return success_response(response)


