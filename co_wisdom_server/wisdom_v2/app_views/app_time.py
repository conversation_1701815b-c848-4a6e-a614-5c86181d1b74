import time, datetime
from rest_framework.views import APIView

from drf_yasg.utils import swagger_auto_schema
from utils.api_response import success_response



class GetTime(APIView):
    authentication_classes = []

    @swagger_auto_schema(
        operation_id='获取服务器时间',
        operation_summary='获取服务器时间',
        manual_parameters=[
        ],
        tags=['app获取服务器时间']
    )
    def get(self, request, *args, **kwargs):
        return success_response({'time': int(time.time())}, request=request)

