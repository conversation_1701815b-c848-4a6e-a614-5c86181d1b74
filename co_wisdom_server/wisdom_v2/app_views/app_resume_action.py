from decimal import Decimal

import datetime
import re

from django.db.models import Sum
from rest_framework import serializers
from django.db import transaction

from utils import task
from wisdom_v2.common import coach_public
from wisdom_v2.common import resume_public
from wisdom_v2.enum.pay_enum import OrderStatusEnum
from wisdom_v2.enum.service_content_enum import Coach<PERSON>uth<PERSON><PERSON>, CoachWorkingYearsEnum
from wisdom_v2.models import ProjectInterview, Resume, User, UserTmp, Order
from utils.multiple_selection_map import get_multiple_selection_detail, validate_multiple_selection
from wisdom_v2.enum.project_interview_enum import ProjectInterviewPlaceCategoryEnum
from wisdom_v2.views.constant import ATTR_STATUS_INTERVIEW_CANCEL, INTERVIEW_TYPE_COACHING, ATTR_TYPE_ORDER
from utils.api_response import WisdomValidationError
from wisdom_v2.enum.user_enum import UserTmpEnum


class AppResumeSerializer(serializers.ModelSerializer):
    id = serializers.IntegerField(read_only=True)
    true_name = serializers.CharField(source='coach.user.cover_name', read_only=True)
    personal_name = serializers.CharField(source='coach.personal_name', read_only=True)
    birthday = serializers.DateField(source='coach.user.birthday', format='%Y-%m-%d', read_only=True)
    email = serializers.CharField(source='coach.user.email', read_only=True)
    phone = serializers.CharField(source='coach.user.phone', read_only=True)
    posters_text = serializers.CharField(source='coach.posters_text', read_only=True)
    city = serializers.CharField(source='coach.city', read_only=True)
    user_id = serializers.IntegerField(source='coach.user_id', read_only=True)
    english_name = serializers.CharField(read_only=True)
    gender = serializers.IntegerField(source='coach.user.gender', read_only=True)
    highest_degree = serializers.IntegerField(source='coach.highest_degree', read_only=True)
    school = serializers.CharField(source='coach.school', read_only=True)
    address = serializers.CharField(source='coach.address', read_only=True)
    coach_type = serializers.IntegerField(source='coach.coach_type', read_only=True)
    expected_hourly_salary = serializers.IntegerField(source='coach.expected_hourly_salary', read_only=True)
    is_share_resume = serializers.BooleanField(source='coach.is_share_resume', help_text='是否可以分享简历', read_only=True)
    language = serializers.SerializerMethodField(read_only=True)
    head_image_url = serializers.CharField(read_only=True)
    work_industry = serializers.SerializerMethodField(help_text='个人工作过的行业')
    highest_position = serializers.SerializerMethodField(help_text='曾担任的最高职位')
    enterprise_attributes = serializers.SerializerMethodField(help_text='工作过的企业属性')
    functional_module = serializers.SerializerMethodField(help_text='工作过的职能模块')
    work_experience = serializers.CharField(help_text='工作经验')
    job_profile = serializers.CharField(help_text='一句话介绍工作经验')
    coach_course = serializers.SerializerMethodField(help_text='受训经验')
    working_years = serializers.SerializerMethodField(read_only=True)
    working_years_str = serializers.SerializerMethodField(read_only=True)
    coach_auth = serializers.IntegerField(read_only=True, help_text='教练资质')
    coach_industry = serializers.SerializerMethodField(help_text='作为教练服务过的行业')
    coach_customer_level = serializers.SerializerMethodField(help_text='曾服务过的客户的最高级别')
    coach_enterprise_attributes = serializers.SerializerMethodField(help_text='曾服务过的企业属性')
    coach_domain = serializers.SerializerMethodField(help_text='擅长教练领域')
    psychological_counseling_certification = serializers.SerializerMethodField(help_text='心理咨询认证')
    one_to_one_interview_time = serializers.IntegerField(read_only=True, help_text='目前积累的1对1辅导时长')
    group_interview_time = serializers.IntegerField(read_only=True, help_text='当前积累的团队辅导时长')
    platform_one_to_one_interview_time = serializers.SerializerMethodField(help_text='平台内积累的1对1辅导时长')
    platform_group_interview_time = serializers.SerializerMethodField(help_text='平台内团队辅导时长')
    coach_experience = serializers.CharField(help_text='教练经验', read_only=True)
    coach_style = serializers.CharField(help_text='教练风格', read_only=True)
    customer_evaluate = serializers.CharField(help_text='客户反馈', read_only=True)
    qualification = serializers.ListField(help_text='教练证书', read_only=True)
    evaluation_certification = serializers.ListField(help_text='测评认证', read_only=True)
    other_certification = serializers.ListField(help_text='其它证书', read_only=True)
    order_receiving_status = serializers.BooleanField(help_text='接单状态', read_only=True, source='coach.order_receiving_status')
    price = serializers.SerializerMethodField(help_text='价格')
    not_show = serializers.SerializerMethodField(help_text='不展示的字段名称')
    customer_coach_course = serializers.SerializerMethodField(help_text='受训经验，客户/hr视角简历用')
    is_identity_check = serializers.BooleanField(help_text='是否实名', read_only=True, source='coach.user.is_identity_check')
    platform_order_receiving_status = serializers.BooleanField(help_text='平台是否允许接单', read_only=True,
                                                               source='coach.platform_order_receiving_status')
    order_remain_hours = serializers.SerializerMethodField(help_text='订单剩余时间')
    coach_course_image = serializers.SerializerMethodField(help_text='受训经验展示图片')
    coach_auth_image = serializers.SerializerMethodField(help_text='教练资质展示图片')


    class Meta:
        model = Resume
        fields = ('id', 'true_name', 'personal_name', 'birthday', 'email', 'phone', 'english_name', 'gender',
                  'highest_degree', 'school', 'address', 'language', 'head_image_url', 'work_industry',
                  'highest_position', 'enterprise_attributes', 'functional_module', 'work_experience', 'coach_course',
                  'working_years', 'coach_auth', 'coach_industry', 'coach_customer_level',
                  'coach_enterprise_attributes', 'coach_domain', 'psychological_counseling_certification',
                  'one_to_one_interview_time', 'group_interview_time', 'platform_one_to_one_interview_time',
                  'platform_group_interview_time', 'coach_experience', 'coach_style', 'customer_evaluate',
                  'qualification', 'evaluation_certification', 'posters_text', 'other_certification',
                  'order_receiving_status', 'expected_hourly_salary', 'price', 'city', 'user_id',
                  'not_show', 'customer_coach_course', 'coach_type', 'is_identity_check', 'is_share_resume',
                  'platform_order_receiving_status', 'order_remain_hours', 'job_profile', 'coach_course_image',
                  'coach_auth_image', 'share_image_url', 'working_years_str')

    def get_working_years_str(self, obj):
        return f'{CoachWorkingYearsEnum.get_display(obj.working_years)}教练经验' if obj.working_years else None

    def get_working_years(self, obj):
        # 兼容老版本小程序教练简历页面展示
        if obj.working_years and obj.working_years > 3:
            return 3
        return obj.working_years

    def get_coach_course_image(self, obj):
        if obj.coach_course:
            return coach_public.get_coach_course_to_image(obj.coach_course)

    def get_coach_auth_image(self, obj):
        if obj.coach_auth:
            image_dict = CoachAuthEnum.get_image()
            return image_dict.get(str(obj.coach_auth))

    def get_order_remain_hours(self, obj):
        coachee_user_id = self.context.get('coachee_user_id')
        if not coachee_user_id:
            return None
        orders = Order.objects.filter(public_attr__user_id=coachee_user_id, deleted=False,
                                      public_attr__target_user_id=obj.coach.user_id,
                                      public_attr__type=ATTR_TYPE_ORDER,
                                      status=OrderStatusEnum.paid).order_by('created_at')
        total_remain_hours = 0
        if orders.exists():
            for order in orders:
                interview_time = order.interview.filter(
                    deleted=False).exclude(
                    public_attr__status=ATTR_STATUS_INTERVIEW_CANCEL).aggregate(times_minute=Sum('times'))
                if interview_time['times_minute']:
                    interview_hour = round(interview_time['times_minute'] / 60, 1)
                else:
                    interview_hour = 0
                remain_hours = order.count - interview_hour
                if remain_hours > 0:
                    total_remain_hours += remain_hours
        if total_remain_hours:
            return total_remain_hours

    def get_price(self, obj):
        return obj.coach.display_price

    def get_language(self, obj):
        data = get_multiple_selection_detail('language', obj.coach_language)
        return data

    def get_work_industry(self, obj):
        data = get_multiple_selection_detail('work_industry', obj.work_industry)
        return data

    def get_highest_position(self, obj):
        data = get_multiple_selection_detail('highest_position', obj.highest_position)
        return data

    def get_enterprise_attributes(self, obj):
        data = get_multiple_selection_detail('enterprise_attributes', obj.enterprise_attributes)
        return data

    def get_functional_module(self, obj):
        data = get_multiple_selection_detail('functional_module', obj.functional_module)
        return data

    def get_coach_course(self, obj):
        data = get_multiple_selection_detail('coach_course', obj.coach_course)
        return data

    def get_customer_coach_course(self, obj):
        data = get_multiple_selection_detail('coach_course', obj.coach_course)
        other = data['content'][-1]['id']
        if other in data['value']:
            if len(data['value']) > 1:
                data['value'].remove(other)
                show_list = data['show_text'].split(',')
                show_list.remove(data['other_text'])
                data['show_text'] = ",".join(show_list)
                data['other_text'] = None
        return data

    def get_not_show(self, obj):
        not_show_list = []
        data = get_multiple_selection_detail('coach_course', obj.coach_course)
        other = data['content'][-1]['id']
        if other in data['value'] and len(data['value']) == 1:
            not_show_list.append('coach_course')
        if not obj.coach_auth:
            not_show_list.append('coach_auth')
        return not_show_list

    def get_coach_industry(self, obj):
        data = get_multiple_selection_detail('coach_industry', obj.coach_industry)
        return data

    def get_coach_customer_level(self, obj):
        data = get_multiple_selection_detail('coach_customer_level', obj.coach_customer_level)
        return data

    def get_coach_enterprise_attributes(self, obj):
        data = get_multiple_selection_detail('coach_enterprise_attributes', obj.coach_enterprise_attributes)
        return data

    def get_coach_domain(self, obj):
        data = get_multiple_selection_detail('coach_domain', obj.coach_domain)
        return data

    def get_psychological_counseling_certification(self, obj):
        data = get_multiple_selection_detail('psychological_counseling_certification',
                                             obj.psychological_counseling_certification)
        return data

    def get_platform_one_to_one_interview_time(self, obj):
        times = ProjectInterview.objects.filter(
            type=INTERVIEW_TYPE_COACHING,
            place_category=ProjectInterviewPlaceCategoryEnum.online_one_to_one.value,
            public_attr__type=1, deleted=False,
            public_attr__end_time__lt=datetime.datetime.now(),
            public_attr__user_id=obj.coach.user_id).exclude(
            public_attr__status=ATTR_STATUS_INTERVIEW_CANCEL).aggregate(times_minute=Sum('times'))
        if times['times_minute']:
            interview_hour = round(times['times_minute'] / 60, 1)
        else:
            interview_hour = 0
        return interview_hour or 0

    def get_platform_group_interview_time(self, obj):
        times = ProjectInterview.objects.filter(
            type=INTERVIEW_TYPE_COACHING,
            place_category=ProjectInterviewPlaceCategoryEnum.offline_group_coach.value,
            public_attr__type=1, deleted=False,
            public_attr__end_time__lt=datetime.datetime.now(),
            public_attr__user_id=obj.coach.user_id).exclude(
            public_attr__status=ATTR_STATUS_INTERVIEW_CANCEL).aggregate(times_minute=Sum('times'))
        if times['times_minute']:
            interview_hour = round(times['times_minute'] / 60, 1)
        else:
            interview_hour = 0
        return interview_hour or 0


class AppResumeUpdateSerializer(serializers.Serializer):
    id = serializers.IntegerField(write_only=True, required=True)
    # user
    english_name = serializers.CharField(write_only=True, required=False, allow_null=True, allow_blank=True)
    birthday = serializers.CharField(write_only=True, required=False, allow_null=True, allow_blank=True)
    email = serializers.CharField(write_only=True, required=False)
    phone = serializers.CharField(write_only=True, required=False)
    gender = serializers.IntegerField(write_only=True, required=False, allow_null=True)
    head_image_url = serializers.CharField(write_only=True, required=False, allow_null=True, allow_blank=True)
    # coach
    highest_degree = serializers.IntegerField(write_only=True, required=False, allow_null=True)
    school = serializers.CharField(write_only=True, required=False, allow_null=True, allow_blank=True)
    language = serializers.ListField(write_only=True, required=False, allow_null=True)
    address = serializers.CharField(write_only=True, required=False, allow_null=True, allow_blank=True)
    posters_text = serializers.CharField(write_only=True, required=False, allow_null=True, allow_blank=True)
    is_share_resume = serializers.BooleanField(write_only=True, required=False, allow_null=True, help_text='是否可以分享简历（是否可以查看）')
    city = serializers.CharField(write_only=True, required=False, allow_null=True, allow_blank=True)
    price = serializers.FloatField(write_only=True, required=False, allow_null=True)
    order_receiving_status = serializers.BooleanField(write_only=True, required=False, allow_null=True)
    personal_name = serializers.CharField(write_only=True, required=False, allow_null=True, allow_blank=True)
    # resume
    work_industry = serializers.ListField(write_only=True, help_text='个人工作过的行业', required=False, allow_null=True)
    highest_position = serializers.ListField(write_only=True, help_text='曾担任的最高职位', required=False, allow_null=True)
    enterprise_attributes = serializers.ListField(write_only=True, help_text='工作过的企业属性', required=False, allow_null=True)
    functional_module = serializers.ListField(write_only=True, help_text='工作过的职能模块', required=False, allow_null=True)
    work_experience = serializers.CharField(write_only=True, help_text='工作经验', required=False, allow_null=True,
                                            allow_blank=True)
    job_profile = serializers.CharField(write_only=True, help_text='一句话介绍工作经验', required=False, allow_null=True,
                                            allow_blank=True)
    coach_course = serializers.ListField(write_only=True, help_text='受训经验', required=False, allow_null=True)
    working_years = serializers.IntegerField(write_only=True, help_text='工作年限', required=False, allow_null=True)
    coach_auth = serializers.IntegerField(write_only=True, help_text='教练资质', required=False, allow_null=True)
    coach_industry = serializers.ListField(write_only=True, required=False, help_text='作为教练服务过的行业', allow_null=True)
    coach_customer_level = serializers.ListField(write_only=True, required=False, help_text='曾服务过的客户的最高级别',
                                                 allow_null=True)
    coach_enterprise_attributes = serializers.ListField(write_only=True, required=False, help_text='曾服务过的企业属性',
                                                        allow_null=True)
    coach_domain = serializers.ListField(write_only=True, required=False, help_text='擅长教练领域', allow_null=True)
    psychological_counseling_certification = serializers.ListField(write_only=True, required=False,
                                                                   help_text='心理咨询认证', allow_null=True)
    one_to_one_interview_time = serializers.FloatField(write_only=True, required=False, help_text='目前积累的1对1辅导时长',
                                                       allow_null=True)
    group_interview_time = serializers.FloatField(write_only=True, required=False, help_text='当前积累的团队辅导时长',
                                                  allow_null=True)
    coach_experience = serializers.CharField(write_only=True, required=False, help_text='教练经验', allow_null=True,
                                             allow_blank=True)
    coach_style = serializers.CharField(write_only=True, required=False, help_text='教练风格', allow_null=True,
                                        allow_blank=True)
    customer_evaluate = serializers.CharField(write_only=True, required=False, help_text='客户反馈', allow_null=True,
                                              allow_blank=True)
    coach_record = serializers.ListField(write_only=True, required=False, help_text='教练履历', allow_null=True)
    qualification = serializers.ListField(write_only=True, required=False, help_text='教练证书', allow_null=True)
    evaluation_certification = serializers.ListField(write_only=True, required=False, help_text='测评认证',
                                                     allow_null=True)
    other_certification = serializers.ListField(write_only=True, required=False, help_text='其它证书', allow_null=True)


    def edit_app_resume_msg(self, validated_data):
        resume = Resume.objects.get(pk=validated_data.pop('id'))
        user = resume.coach.user
        if 'email' in validated_data.keys():
            if ' ' in validated_data.get('email') and 'email':
                raise WisdomValidationError('邮箱中存在空格，请修改后再添加。')
            if validated_data['email'] != user.email and User.objects.filter(email=validated_data['email'],
                                                                             deleted=False).exists():
                raise WisdomValidationError('当前邮箱已存在')
        if 'phone' in validated_data.keys():
            if validated_data['phone'] != user.phone and User.objects.filter(phone=validated_data['phone'],
                                                                             deleted=False).exists():
                raise WisdomValidationError('当前手机号已存在')
            result, msg = validate_phone(validated_data['phone'])
            if not result:
                raise WisdomValidationError(msg)

        with transaction.atomic():
            validated_data = update_resume_coach(validated_data, resume.coach)
            if 'language' in validated_data.keys():
                validated_data['coach_language'] = validated_data.pop('language')
            validated_data = update_resume_user(validated_data, user)
            Resume.objects.filter(pk=resume.pk).update(**validated_data)
            UserTmp.objects.filter(type=UserTmpEnum.resume, data_id=resume.pk).delete()

        resume = Resume.objects.filter(pk=resume.pk).first()
        if 'head_image_url' in validated_data.keys():
            task.update_coach_resume_share_url.delay(resume.pk, resume.head_image_url)  # 更新小程序教练简历分享的头像

        # 记录更新简历的教练信息
        resume_public.record_coach_update_resume(resume)
        # 更新教练标签
        task.update_coach_tag.delay(resume.coach_id)
        data = AppResumeSerializer(resume).data
        return data

    def validate(self, attrs):
        attrs = validate_multiple_selection(attrs)
        return attrs


def update_resume_coach(validated_data, coach):
    if 'personal_name' in validated_data.keys():
        coach.personal_name = validated_data['personal_name']
        validated_data.pop('personal_name')
    if 'highest_degree' in validated_data.keys():
        coach.highest_degree = validated_data['highest_degree']
        validated_data.pop('highest_degree')
    if 'school' in validated_data.keys():
        coach.school = validated_data['school']
        validated_data.pop('school')
    if 'address' in validated_data.keys():
        coach.address = validated_data['address']
        validated_data.pop('address')
    if 'order_receiving_status' in validated_data.keys():
        coach.order_receiving_status = validated_data['order_receiving_status']
        validated_data.pop('order_receiving_status')
    if 'posters_text' in validated_data.keys():
        coach.posters_text = validated_data['posters_text']
        validated_data.pop('posters_text')
    if 'city' in validated_data.keys():
        coach.city = validated_data['city']
        validated_data.pop('city')
    if 'price' in validated_data.keys():
        price = validated_data['price']
        # 如果是修改为空，则更新成Null，如果是数字，则乘以100转化成分，保留整数
        coach.price = None if price is None else int(Decimal(str(price)) * Decimal('100').quantize(Decimal('0')))
        validated_data.pop('price')
    if 'is_share_resume' in validated_data.keys():
        coach.is_share_resume = validated_data['is_share_resume']
        validated_data.pop('is_share_resume')
    coach.save()
    return validated_data


def update_resume_user(validated_data, user):
    if 'true_name' in validated_data.keys():
        if not user.is_identity_check:
            user.true_name = validated_data['true_name']
        validated_data.pop('true_name')
    if 'birthday' in validated_data.keys():
        user.birthday = validated_data['birthday']
        validated_data.pop('birthday')
    if 'email' in validated_data.keys():
        user.email = validated_data['email']
        validated_data.pop('email')
    if 'phone' in validated_data.keys():
        user.phone = validated_data['phone']
        validated_data.pop('phone')
    if 'gender' in validated_data.keys():
        user.gender = validated_data['gender']
        validated_data.pop('gender')
    if 'english_name' in validated_data.keys():
        user.english_name = validated_data['english_name']
    # if 'head_image_url' in validated_data.keys():
    #     user.head_image_url = validated_data['head_image_url']
    if 'user_id' in validated_data.keys():
        validated_data.pop('user_id')
    user.save()

    return validated_data


def validate_phone(phone):
    if len(phone) > 11:
        return False, '手机号最多11位'
    elif not phone.isdigit():
        return False, '手机号应为数字'
    elif not re.match(r"^1[3456789][0-9]{9}$", phone):
        return False, '手机号格式错误'
    return True, phone
