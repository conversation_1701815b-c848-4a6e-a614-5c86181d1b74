from drf_yasg import openapi
from drf_yasg.utils import swagger_auto_schema

from rest_framework.views import APIView

from wisdom_v2.models import User
from utils.api_response import success_response, parameter_error_response
from utils.wechat_oauth import WeChatOauth
from wisdom_v2.models_file.visitor_records import VisitorRecords


class WeChatAuth(APIView):
    @swagger_auto_schema(
        operation_id='app小程序授权接口',
        operation_summary='app小程序授权接口',
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'user_id': openapi.Schema(type=openapi.TYPE_NUMBER, description='user_id'),
                'code': openapi.Schema(type=openapi.TYPE_STRING, description='code'),
            }
        ),
        tags=['app小程序授权相关']
    )
    def post(self, request, *args, **kwargs):
        try:
            user_id = request.data.get('user_id')
            code = request.data.get('code')
            user = User.objects.get(pk=user_id)
        except User.DoesNotExist:
            return parameter_error_response('用户ID错误')
        #  使用code获取openid
        if code:
            response = WeChatOauth().jscode2session(code)
            if response.get('err'):
                return parameter_error_response('wechat jscode2session error')
            openid = response.get('openid')
            # unionid = response.get('unionid')
            if openid:
                user.openid = openid
                user.save()
            return success_response()
        else:
            return parameter_error_response('缺少授权参数')


class WeChatVisitorRecords(APIView):
    authentication_classes = []

    @swagger_auto_schema(
        operation_id='微信游客信息记录',
        operation_summary='微信游客信息记录',
        manual_parameters=[
            openapi.Parameter('code', openapi.IN_QUERY, description='授权凭证', type=openapi.TYPE_STRING),
        ],
        tags=['app小程序授权相关']
    )
    def post(self, request, *args, **kwargs):
        code = request.data.get('code')

        if not code:
            return parameter_error_response('缺少授权参数')

        #  使用code获取openid
        response = WeChatOauth().jscode2session(code)
        if response.get('err'):
            return parameter_error_response('获取授权信息失败')

        openid = response.get('openid')
        if not openid:
            return parameter_error_response('获取授权信息失败')

        is_new = True
        if User.objects.filter(openid=openid, deleted=False).exists():
            is_new = False
        elif VisitorRecords.objects.filter(openid=openid, deleted=False).exists():
            is_new = False
        else:
            VisitorRecords.objects.create(openid=openid)

        return success_response({'is_new': is_new, 'openid': openid})

