from decimal import Decimal
from rest_framework import serializers

from utils import utc_date_time
from utils.miniapp_version_judge import compare_version
from wisdom_v2.common import business_order_public
from wisdom_v2.enum.business_order_enum import BusinessOrderTypeEnum, \
    BusinessOrderPayStatusEnum, BusinessOrderSettlementStatusEnum, BusinessOrderDurationTypeEnum, \
    BusinessOrderWithdrawalStatusEnum, BusinessOrderDataTypeEnum
from wisdom_v2.enum.service_content_enum import ActivityTypeEnum
from wisdom_v2.models_file import BusinessOrder
from wisdom_v2.models_file.business_order import BusinessOrderExtraCost


class AppBusinessOrderSerializer(serializers.ModelSerializer):
    id = serializers.CharField(help_text='id')
    type = serializers.IntegerField(help_text='工作类型')
    work_type = serializers.IntegerField(help_text='工作形式')
    company_name = serializers.SerializerMethodField(help_text='企业名称')
    project_name = serializers.SerializerMethodField(help_text='项目名称')
    member_name = serializers.SerializerMethodField(help_text='客户名称')
    order_date = serializers.SerializerMethodField(help_text='辅导/开课时间')
    class_name = serializers.SerializerMethodField(help_text='班级名称')
    coach_actual_income = serializers.SerializerMethodField(help_text='教练实际收益')
    order_settlement_status = serializers.SerializerMethodField(help_text='订单状态')
    duration = serializers.SerializerMethodField(help_text='时长')
    coach_price = serializers.SerializerMethodField(help_text='教练单价')
    duration_type = serializers.IntegerField(help_text='结算时长类型')
    tax_amount = serializers.SerializerMethodField(help_text='税费')
    apply_settlement_time = serializers.SerializerMethodField(help_text='结算申请时间')
    platform_service_amount = serializers.SerializerMethodField(help_text='平台服务费')
    member_paid = serializers.SerializerMethodField(help_text='客户实付')
    extra_cost = serializers.SerializerMethodField(help_text='额外费用')
    total_extra_cost = serializers.SerializerMethodField(help_text='额外费用总金额')
    is_welfare_donations = serializers.SerializerMethodField(help_text='是否义捐')

    class Meta:
        model = BusinessOrder
        fields = ('id', 'type', 'work_type', 'company_name', 'member_name', 'class_name', 'order_date',
                  'coach_actual_income', 'order_settlement_status', 'project_name', 'duration', 'coach_price',
                  'duration_type', 'tax_amount', 'apply_settlement_time', 'platform_service_amount', 'member_paid',
                  'extra_cost', 'total_extra_cost', 'is_welfare_donations')

    def get_is_welfare_donations(self, obj):
        # 默认有收入的公益教练活动收入属于义捐,
        activity = business_order_public.get_business_order_activity(obj)
        if activity and activity.type == ActivityTypeEnum.public_welfare_coach.value and obj.member_paid:
            return True
        return False

    def get_total_extra_cost(self, obj):
        return business_order_public.get_all_business_order_extra_cost(obj)

    def get_extra_cost(self, obj):
        extra_cost = BusinessOrderExtraCost.objects.filter(business_order=obj, deleted=False).all()

        data = {}
        for item in extra_cost:
            if data.get(item.describe):
                data[item.describe].append({
                    "marking_date": item.marking_date.strftime('%Y-%m-%d'),
                    "cost": item.actual_cost,
                })
            else:
                data[item.describe] = [{
                    "marking_date": item.marking_date.strftime('%Y-%m-%d'),
                    "cost": item.actual_cost,
                }]
        return [{"name": k, "content": v} for k, v in data.items()]

    def get_member_name(self, obj):
        return business_order_public.get_business_order_member_name(obj)

    def get_member_paid(self, obj):
        if obj.type == BusinessOrderTypeEnum.personal:
            return str(Decimal(str(obj.member_paid)) / Decimal('100'))

    def get_platform_service_amount(self, obj):
        if obj.type == BusinessOrderTypeEnum.personal:
            mp = self.context.get('mp')
            if mp and compare_version(mp.get('version'), '2.31') < 0:
                return str(Decimal(str(obj.platform_service_amount)) / Decimal('100') +
                           Decimal(str(obj.tax_amount)) / Decimal('100'))
            else:
                return str(Decimal(str(obj.platform_service_amount)) / Decimal('100'))

    def get_apply_settlement_time(self, obj):
        if obj.business_settlement:
            return obj.business_settlement.created_at.strftime('%Y-%m-%d %H:%M:%S')

    def get_tax_amount(self, obj):
        if obj.tax_amount is not None:
            return str(Decimal(str(obj.tax_amount)) / Decimal('100'))

    def get_coach_price(self, obj):
        amount = str(Decimal(obj.coach_price) / Decimal('100'))
        return amount

    def get_duration(self, obj):
        duration = int(obj.duration) if isinstance(obj.duration, float) and obj.duration.is_integer() else obj.duration
        if obj.duration_type == BusinessOrderDurationTypeEnum.hour:
            return f"{duration}小时"
        return f"{duration}天"

    def get_project_name(self, obj):
        if obj.type == BusinessOrderTypeEnum.enterprise:
            if obj.project:
                return obj.project.name
            elif obj.extra_info and 'project_name' in obj.extra_info:
                return obj.extra_info['project_name']

    def get_company_name(self, obj):
        if obj.type == BusinessOrderTypeEnum.enterprise:
            if obj.project:
                return obj.project.company.real_name
            elif obj.extra_info and 'company_name' in obj.extra_info:
                return obj.extra_info['company_name']

    def get_order_date(self, obj):
        if obj.extra_info and 'work_time' in obj.extra_info:
            return obj.extra_info['work_time']
        else:
            return utc_date_time.get_auto_cal_datetime_str(obj.work_start_time, obj.work_end_time)

    def get_class_name(self, obj):
        if obj.type == BusinessOrderTypeEnum.public_course:
            public_course_coach = business_order_public.get_business_order_to_public_course(obj)
            if public_course_coach:
                return public_course_coach.class_name
            elif obj.extra_info and 'project_name' in obj.extra_info:
                return obj.extra_info['project_name']

    def get_coach_actual_income(self, obj):
        return str(Decimal(str(obj.coach_actual_income)) / Decimal('100'))

    def get_order_settlement_status(self, obj):
        if obj.settlement_status == BusinessOrderSettlementStatusEnum.settled:
            return 3  # 已到账
        elif obj.business_settlement_id:
            return 1  # 处理中 已经提交提现申请的订单
        elif obj.pay_status in [BusinessOrderPayStatusEnum.paid, BusinessOrderPayStatusEnum.non_payment]:
            return 2  # 未提现 提现状态是可提现，结算状态为未结算


def get_can_withdrawn_business_order(business_order):
    """
    查询可提现订单列表
    """

    business_order = business_order.filter(
        # 未结算的订单，表示这些订单尚未完成结算处理
        settlement_status=BusinessOrderSettlementStatusEnum.unsettled,
        deleted=False,  # 筛选出未被删除的订单
        # 筛选出可提现和已提现状态的订单
        withdrawal_status__in=[
            BusinessOrderWithdrawalStatusEnum.can_withdraw.value,
            BusinessOrderWithdrawalStatusEnum.withdrawn.value
        ],
        # 筛选出已支付和未支付无风险的订单
        pay_status__in=[
            BusinessOrderPayStatusEnum.paid,
            BusinessOrderPayStatusEnum.non_payment.value
        ]
    )

    return business_order
