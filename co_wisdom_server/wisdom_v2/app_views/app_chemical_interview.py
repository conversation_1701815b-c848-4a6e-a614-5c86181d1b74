import datetime

from drf_yasg import openapi
from rest_framework import viewsets
from drf_yasg.utils import swagger_auto_schema
from rest_framework.decorators import action
from django.db import transaction

from utils.api_response import parameter_error_response, success_response
from utils.send_account_email import get_project_manage_qr_code
from utils.task import chemical_interview_feedback_notification
from wisdom_v2.enum.chemical_interview_enum import ChemicalInterviewStatusEnum, ChemicalInterviewCoachSourceEnum

from wisdom_v2.models_file import ChemicalInterview2Coach, ChemicalInterviewModule
from wisdom_v2.models import ProjectMember, Coach, ProjectInterview, User, Project, WorkWechatUser, ProjectCoach
from wisdom_v2.app_views.app_chemical_interview_action import AppChemicalInterview2CoachSerializer
from utils.messagecenter.getui import send_work_wechat_coach_notice
from wisdom_v2.common import project_coach_public


class AppChemicalInterview2CoachViewSet(viewsets.ModelViewSet):
    queryset = ChemicalInterview2Coach.objects.filter(deleted=False)
    serializer_class = AppChemicalInterview2CoachSerializer

    @swagger_auto_schema(
        operation_id='小程序化学面谈详情',
        operation_summary='小程序化学面谈详情',
        manual_parameters=[
            openapi.Parameter(
                'chemical_interview_id', openapi.IN_QUERY, description='化学面谈id',
                type=openapi.TYPE_NUMBER,required=True),
            openapi.Parameter(
                'interview_id', openapi.IN_QUERY, description='辅导id', type=openapi.TYPE_NUMBER, required=True)
        ],
        tags=['小程序化学面谈相关']
    )
    @action(methods=['get'], detail=False, url_path='detail')
    def chemical_interview_detail(self, request, *args, **kwargs):
        try:
            interview_id = request.query_params.get('interview_id')
            chemical_interview_id = request.query_params.get('chemical_interview_id')
            if interview_id:
                project_interview = ProjectInterview.objects.get(id=interview_id)
                chemical_interview = project_interview.chemical_interview.filter(deleted=False).first()
            else:
                chemical_interview = ChemicalInterview2Coach.objects.get(id=chemical_interview_id)
        except (ProjectInterview.DoesNotExist, ChemicalInterview2Coach.DoesNotExist):
            return parameter_error_response()
        serializer = self.get_serializer(chemical_interview)
        return success_response(serializer.data)

    @swagger_auto_schema(
        operation_id='修改小程序化学面谈',
        operation_summary='修改小程序化学面谈',
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'id': openapi.Schema(type=openapi.TYPE_NUMBER, description='化学面谈id'),
                'chemical_interview_status': openapi.Schema(type=openapi.TYPE_NUMBER, description='面谈状态'),
                'coach_aspect': openapi.Schema(type=openapi.TYPE_NUMBER, description='面谈状态'),
                'coach_impression': openapi.Schema(type=openapi.TYPE_NUMBER, description='面谈状态'),
            }
        ),
        tags=['小程序化学面谈相关']
    )
    @action(methods=['post'], detail=False, url_path='update')
    def update_chemical_interview(self, request, *args, **kwargs):
        try:
            chemical_interview_id = request.data['id']
            chemical_interview_status = request.data['chemical_interview_status']
            coach_aspect = request.data.get('coach_aspect')
            coach_impression = request.data.get('coach_impression')
            chemical_interview = ChemicalInterview2Coach.objects.get(pk=chemical_interview_id)
        except (KeyError, ChemicalInterview2Coach.DoesNotExist):
            return parameter_error_response()
        with transaction.atomic():
            if chemical_interview_status == ChemicalInterviewStatusEnum.selected:
                exists_obj = ChemicalInterview2Coach.objects.filter(
                        chemical_interview_module=chemical_interview.chemical_interview_module,
                        chemical_interview_status=ChemicalInterviewStatusEnum.selected, deleted=False
                ).exclude(id=chemical_interview_id).first()
                if exists_obj:
                    return parameter_error_response(f'无效操作，已选择了其他教练')

            chemical_interview.chemical_interview_status = chemical_interview_status
            if coach_aspect:
                coach_aspect = ','.join(coach_aspect)
                chemical_interview.coach_aspect = coach_aspect
            if coach_impression:
                chemical_interview.coach_impression = coach_impression
            chemical_interview.save()
            interview = chemical_interview.interview
            interview.coachee_record_status = True
            interview.save()
            if chemical_interview_status == ChemicalInterviewStatusEnum.selected:
                project_coach_public.user_add_coach(chemical_interview.interview.public_attr.target_user_id,
                                                    chemical_interview.interview.public_attr.project_id,
                                                    chemical_interview.coach.id,
                                                    chemical_interview.coach.user_id)
        # 后续通知
        chemical_interview_feedback_notification.delay(chemical_interview_id)

        serializer = self.get_serializer(chemical_interview)
        return success_response(serializer.data)

    @swagger_auto_schema(
        operation_id='小程序化学面谈可否预约',
        operation_summary='小程序化学面谈可否预约',
        manual_parameters=[
            openapi.Parameter(
                'coach_id', openapi.IN_QUERY, description='教练id',
                type=openapi.TYPE_NUMBER, required=True),
            openapi.Parameter(
                'project_id', openapi.IN_QUERY, description='辅导id', type=openapi.TYPE_NUMBER, required=True),
            openapi.Parameter(
                'user_id', openapi.IN_QUERY, description='用户id',
                type=openapi.TYPE_NUMBER, required=True),
        ],
        tags=['小程序化学面谈相关']
    )
    @action(methods=['get'], detail=False, url_path='is_add')
    def is_add(self, request, *args, **kwargs):
        try:
            coach_id = request.query_params['coach_id']
            project_id = request.query_params['project_id']
            user_id = request.query_params['user_id']
            user = User.objects.get(pk=user_id)
            project = Project.objects.get(pk=project_id)
            coach = Coach.objects.get(pk=coach_id)
            project_member = ProjectMember.objects.get(user=user, project=project, deleted=False)
            chemical_interview_module = project_member.chemical_interview.filter(deleted=False).first()
        except (KeyError, User.DoesNotExist, Project.DoesNotExist, Coach.DoesNotExist, ProjectMember.DoesNotExist,
                ChemicalInterviewModule.DoesNotExist):
            return parameter_error_response()
        if not chemical_interview_module:
            return parameter_error_response()
        chemical_interview = chemical_interview_module.coaches.filter(deleted=False)
        not_feedback = chemical_interview.filter(chemical_interview_status=ChemicalInterviewStatusEnum.not_feedback,
                                                 interview__isnull=False)

        data = {
                "is_add": True,
                "msg": None,
                "project_manage_qrcode": None,
                "chemical_interview_id": None,
                "is_progress": False,
            }

        if not chemical_interview and chemical_interview_module == ChemicalInterviewCoachSourceEnum.system_random:
            data = {
                "is_add": False,
                "msg": None,
                "project_manage_qrcode": None,
                "interview_id": None,
                "is_progress": False,
            }
        elif chemical_interview_module and chemical_interview_module.end_time < datetime.datetime.now().date():
            project_manage_qrcode = get_project_manage_qr_code(project_id)
            data = {
                "is_add": False,
                "msg": "化学面谈已结束，如未选定教练，请识别下方二维码联系项目运营处理",
                "project_manage_qrcode": project_manage_qrcode if project_manage_qrcode
                else 'http://open.work.weixin.qq.com/wwopen/userQRCode?vcode=vc8b96af1abdd77067',
                "interview_id": None,
                "is_progress": False,
            }

        elif not_feedback.exists():
            now = datetime.datetime.now()
            not_feedback = not_feedback.first()
            # 未开始是开始时间大与当前时间
            # 进行中是开始时间笑与当前时间小于结束时间
            if not_feedback.interview.public_attr.start_time >= now or \
                not_feedback.interview.public_attr.start_time <= now <= not_feedback.interview.public_attr.end_time:  # 未开始进行中
                data = {
                    "is_add": False,
                    "msg": f"请先完成和{not_feedback.interview.public_attr.user.cover_name}教练的面谈，再预约其他教练",
                    "project_manage_qrcode": None,
                    "is_progress": True,
                    "interview_id": not_feedback.interview_id
                }
            # else:
            #     data = {
            #         "is_add": False,
            #         "msg": "请先反馈上一位教练的选择结果，再预约下一次化学面谈",
            #         "project_manage_qrcode": None,
            #         "interview_id": not_feedback.interview_id,
            #         "is_progress": False,
            #     }
        elif chemical_interview_module.max_interview_number <= chemical_interview.filter(interview__isnull=False).count():
            project_manage_qrcode = get_project_manage_qr_code(project_id)
            data = {
                    "is_add": False,
                    "msg": "您的化学面谈次数已用尽，如未选定教练，请识别下方二维码联系项目运营处理",
                    "project_manage_qrcode": project_manage_qrcode if project_manage_qrcode
                    else 'http://open.work.weixin.qq.com/wwopen/userQRCode?vcode=vc8b96af1abdd77067',
                    "interview_id": None,
                    "is_progress": False,
                }

        return success_response(data)