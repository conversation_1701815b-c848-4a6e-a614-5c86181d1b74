import datetime
from decimal import Decimal

from django.conf import settings
from django.db.models import Sum
from drf_yasg import openapi

from rest_framework.decorators import action
from drf_yasg.utils import swagger_auto_schema
from django.db import transaction

from utils import task
from utils.aliyun_sls_log import AliyunSlsLogLayout
from utils.messagecenter import getui
from wisdom_v2.app_views import app_order_action
from wisdom_v2.common import interview_public, personal_user_public, order_public, schedule_public
from wisdom_v2.enum.pay_enum import OrderStatusEnum, CouponStatusEnum, RefundStatusEnum
from wisdom_v2.enum.service_content_enum import ScheduleApplyTypeEnum
from wisdom_v2.models import Coupon, User, Order, Coach, RefundOrder, ProjectInterview, WorkWechatUser
from rest_framework import viewsets
from utils.messagecenter.center import push_v2_message
from utils.messagecenter.getui import push_to_single

from utils.api_response import success_response, parameter_error_response
from wisdom_v2.app_views.app_order_action import AppOrderSerializer, get_pending_order_detail, \
    third_party_payment, remove_order_data, create_order_msg, get_verify_order_detail, get_order_coach_info, \
    create_order_info, remove_order, create_refund_order
from rest_framework.response import Response
from rest_framework import status
from utils.third_party_payment import ThirdPartyPayment
from utils.feishu_robot import push_wx_error_message
from utils.task import send_interview_confirmed_or_cancel_message, push_lark_message_celery
from wisdom_v2.models_file import Activity, ActivityInterview, PersonalActivity, PersonalActivityInterview, Poster
from wisdom_v2.views.constant import ATTR_STATUS_INTERVIEW_CANCEL


class OrderViewSet(viewsets.ModelViewSet):
    queryset = Order.objects.filter(deleted=False)
    serializer_class = AppOrderSerializer

    @swagger_auto_schema(
        operation_id='待付款订单详情',
        operation_summary='待付款订单详情',
        manual_parameters=[
            openapi.Parameter(
                'type', openapi.IN_QUERY, description='订单类型 1-预约教练订单 2-公益教练订单0元', type=openapi.TYPE_NUMBER, required=False,
            ),
            openapi.Parameter(
                'user_id', openapi.IN_QUERY, description='用户id', type=openapi.TYPE_NUMBER, required=True),
            openapi.Parameter(
                'coach_user_id', openapi.IN_QUERY, description='教练用户id', type=openapi.TYPE_NUMBER, required=True),
            openapi.Parameter(
                'activity_id', openapi.IN_QUERY, description='活动标识', type=openapi.TYPE_NUMBER, required=False,
            ),
        ],
        tags=['订单相关']
    )
    @action(methods=['get'], detail=False, url_path='pending_order_detail')
    def pending_order_detail(self, request, *args, **kwargs):
        try:
            user = User.objects.get(pk=request.query_params.get('user_id'))
            coach = Coach.objects.get(user_id=request.query_params.get('coach_user_id'), deleted=False)
            order_type = int(request.query_params.get('type', 1))
            activity_id = request.query_params.get('activity_id')
            if activity_id:
                activity = Activity.objects.get(pk=activity_id, deleted=False)
            else:
                activity = None
        except Activity.DoesNotExist:
            return parameter_error_response('活动不存在')
        except:
            return parameter_error_response()
        # 查询订单详情信息
        if order_type and order_type == 2:
            data = get_pending_order_detail(user, coach, 'fellow_interview')
        else:
            data = get_pending_order_detail(user, coach, 'coach_interview', activity)
        if not data:
            return parameter_error_response('订单类型错误')
        return success_response(data)

    @swagger_auto_schema(
        operation_id='支付下单接口',
        operation_summary='支付下单接口',
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'type': openapi.Schema(type=openapi.TYPE_NUMBER, description='订单类型 1-预约教练订单 2-公益教练订单0元'),
                'interview': openapi.Schema(
                    type=openapi.TYPE_OBJECT, description='辅导', properties={
                        'topic': openapi.Schema(type=openapi.TYPE_STRING, description='辅导议题'),
                        'coach_user_id': openapi.Schema(type=openapi.TYPE_NUMBER, description='教练用户id'),
                        'user_id': openapi.Schema(type=openapi.TYPE_NUMBER, description='用户id'),
                        'start_time': openapi.Schema(type=openapi.TYPE_STRING, description='开始时间 2022-12-12 10:00:00'),
                        'end_time': openapi.Schema(type=openapi.TYPE_STRING, description='结束时间 2022-12-12 10:00:00'),
                    }),
                'coupon_id': openapi.Schema(type=openapi.TYPE_STRING, description='优惠券id'),
                'coupon_no': openapi.Schema(type=openapi.TYPE_STRING, description='优惠券发放凭证'),
                'channel': openapi.Schema(type=openapi.TYPE_NUMBER, description='1-微信支付'),
                'activity_id': openapi.Schema(type=openapi.TYPE_NUMBER, description='活动标识'),
            }
        ),
        tags=['订单相关']
    )
    def create(self, request, *args, **kwargs):
        # 老版本支付接口，先选择辅导时间，再支付，所以这里是订单和辅导一起创建
        try:
            type = int(request.data.get('type', 1))
            channel = request.data.get('channel', 1)
            activity_id = request.data.get('activity_id')
            if activity_id:
                activity = Activity.objects.get(pk=activity_id)
            else:
                activity = None
        except Activity.DoesNotExist:
            return parameter_error_response('活动不存在')
        except:
            return parameter_error_response()

        if activity:
            limit_count = activity.limit_count
            user_count = ActivityInterview.objects.filter(
                activity=activity, deleted=False,
                interview__public_attr__target_user=request.data.get('interview', {}).get('user_id')
            ).exclude(interview__public_attr__status=ATTR_STATUS_INTERVIEW_CANCEL).count()
            if user_count >= limit_count:
                return parameter_error_response('您的报名已达上限~')

        data = request.data.copy()
        # 1.创建辅导，订单，商品信息
        is_success, order, interview, msg = create_order_msg(data, type=type, activity=activity)
        if not is_success:
            return parameter_error_response(msg)
        if order.status == OrderStatusEnum.paid:
            send_interview_confirmed_or_cancel_message.delay(interview.pk, 'interview_confirmed')
            push_lark_message_celery.delay([interview.pk], 'success_pay')
            payment_data = {
                'interview_id': interview.pk,
                'order_status': order.status
            }
            # 订单支付给教练发送通知
            work_wechat_user = WorkWechatUser.objects.filter(
                wx_user_id__isnull=False, user_id=order.public_attr.target_user, deleted=False).first()
            if work_wechat_user:
                coach_user = order.public_attr.target_user
                getui.send_work_wechat_coach_notice.delay(
                    work_wechat_user.wx_user_id,
                    'to_c_order_pay_notice',
                    coach_name=coach_user.cover_name,
                    coach_id=coach_user.id,
                    pay_money=str(Decimal(str(order.payer_amount)) / Decimal('100')),
                )
            return success_response(payment_data)
        # 获取优惠信息
        coach = Coach.objects.get(user_id=interview.public_attr.user_id, deleted=False)
        coupon_data = get_pending_order_detail(order.public_attr.user, coach, 'coach_interview')
        coupon_no = coupon_data.get('coupon_no')
        # 2.调用第三方支付
        is_success, payment_data = third_party_payment(channel=channel, order=order, coupon_no=coupon_no)
        if not is_success:
            remove_order_data(order, interview)
            return parameter_error_response(f'支付失败:{payment_data}')
        payment_data['interview_id'] = interview.pk
        payment_data['order_status'] = order.status,
        return success_response(payment_data)

    @swagger_auto_schema(
        operation_id='支付回调接口',
        operation_summary='支付回调接口',
        tags=['订单相关']
    )
    @action(methods=['post'], detail=False, url_path='wechat_pay_call_back', authentication_classes=[])
    def wechat_pay_call_back(self, request, *args, **kwargs):
        headers = {}
        headers['Wechatpay-Signature'] = request.META.get('HTTP_WECHATPAY_SIGNATURE')
        headers['Wechatpay-Timestamp'] = request.META.get('HTTP_WECHATPAY_TIMESTAMP')
        headers['Wechatpay-Nonce'] = request.META.get('HTTP_WECHATPAY_NONCE')
        headers['Wechatpay-Serial'] = request.META.get('HTTP_WECHATPAY_SERIAL')

        # 微信签名探测通知，会故意提供错误的签名验证商户是否正确在验签，
        # 错误通知的签名开始会是“WECHATPAY/SIGNTEST/”字符串，以此进行排除
        if headers.get('Wechatpay-Signature', [])[:19] == 'WECHATPAY/SIGNTEST/':
            return Response({'code': 'FAIL', 'message': '失败'}, status=status.HTTP_400_BAD_REQUEST)

        is_success, result = ThirdPartyPayment(channel=1).call_back(headers, request.body, 1)
        if is_success:
            try:
                with transaction.atomic():  # try
                    resource = result.get('resource')
                    out_trade_no = resource.get('out_trade_no')
                    order = Order.objects.get(order_no=out_trade_no)
                    order.status = OrderStatusEnum.paid
                    transaction_id = resource.get('transaction_id')
                    order.transaction_id = transaction_id
                    amount = resource.get('amount')
                    order.payer_amount = amount.get('payer_total')
                    attach = resource.get('attach')
                    promotion_detail = resource.get('promotion_detail', [])
                    discount_amount = 0
                    for promotion_item in promotion_detail:
                        stock_id = promotion_item.get('stock_id')
                        coupon_id = promotion_item.get('coupon_id')
                        merchant_contribute = promotion_item.get('merchant_contribute', 0)
                        wechatpay_contribute = promotion_item.get('wechatpay_contribute', 0)
                        other_contribute = promotion_item.get('other_contribute', 0)
                        if merchant_contribute:
                            if attach:
                                coupon = Coupon.objects.get(coupon_no=attach)
                                if coupon.stock.stock_id == stock_id:
                                    order.coupon_no = attach
                                    coupon.status = CouponStatusEnum.USED
                                    coupon.coupon_id = coupon_id
                                    coupon.save()
                            discount_amount += merchant_contribute
                        if wechatpay_contribute:
                            order.payer_amount += wechatpay_contribute
                        if other_contribute:
                            order.payer_amount += other_contribute
                    order.discount_amount = discount_amount
                    success_time = resource.get('success_time')
                    success_time = success_time.replace('+08:00', '')
                    success_time = success_time.replace('T', ' ')
                    order.success_time = success_time
                    order.payment_info = resource
                    order.save()
                    # 给教练发送预约确认提醒
                    interview = ProjectInterview.objects.filter(order=order).first()
                    if interview:
                        send_interview_confirmed_or_cancel_message.delay(interview.pk, 'interview_confirmed')
                        push_lark_message_celery.delay([interview.pk], 'success_pay')

                    # 订单支付给教练发送通知
                    work_wechat_user = WorkWechatUser.objects.filter(
                        wx_user_id__isnull=False, user_id=order.public_attr.target_user, deleted=False).first()
                    if work_wechat_user:
                        coach_user = order.public_attr.target_user
                        getui.send_work_wechat_coach_notice.delay(
                            work_wechat_user.wx_user_id,
                            'to_c_order_pay_notice',
                            coach_name=coach_user.cover_name,
                            coach_id=coach_user.id,
                            pay_money=str(Decimal(str(order.payer_amount)) / Decimal('100')),
                        )
                    user = order.public_attr.user
                    params = {
                        'user_id': user.id,
                        'user_name': user.true_name,
                        'content': {'headers': headers, 'data': request.body, 'result': result},
                        'message': '微信支付回调成功'}
                    AliyunSlsLogLayout().send_third_api_log(**params)
                return Response({'code': 'SUCCESS', 'message': '成功'}, status=status.HTTP_200_OK)
            except Exception as e:
                push_wx_error_message(
                    name='微信支付-支付回调参数处理失败', level='error', content={'result': result, 'error': str(e)})
                return Response({'code': 'FAIL', 'message': '失败'}, status=status.HTTP_400_BAD_REQUEST)
        else:
            push_wx_error_message(
                name='微信支付-支付回调解密失败', level='warning', content={'headers': request.META, 'result': result})
            params = {
                'url': "wechat_pay_call_back",
                'body': {'headers': headers, 'data': request.body, 'result': result},
                'message': '微信支付回调失败'
            }
            AliyunSlsLogLayout().send_business_api_log(**params)
            return Response({'code': 'FAIL', 'message': '失败'}, status=status.HTTP_400_BAD_REQUEST)

    @swagger_auto_schema(
        operation_id='退款回调接口',
        operation_summary='退款回调接口',
        tags=['订单相关']
    )
    @action(methods=['post'], detail=False, url_path='wechat_refunds_call_back', authentication_classes=[])
    def wechat_refunds_call_back(self, request, *args, **kwargs):
        headers = {}
        headers['Wechatpay-Signature'] = request.META.get('HTTP_WECHATPAY_SIGNATURE')
        headers['Wechatpay-Timestamp'] = request.META.get('HTTP_WECHATPAY_TIMESTAMP')
        headers['Wechatpay-Nonce'] = request.META.get('HTTP_WECHATPAY_NONCE')
        headers['Wechatpay-Serial'] = request.META.get('HTTP_WECHATPAY_SERIAL')

        # 微信签名探测通知，会故意提供错误的签名验证商户是否正确在验签，
        # 错误通知的签名开始会是“WECHATPAY/SIGNTEST/”字符串，以此进行排除
        if headers.get('Wechatpay-Signature', [])[:19] == 'WECHATPAY/SIGNTEST/':
            return Response({'code': 'FAIL', 'message': '失败'}, status=status.HTTP_400_BAD_REQUEST)

        is_success, result = ThirdPartyPayment(channel=1).call_back(headers, request.body, 2)
        if is_success:
            try:
                with transaction.atomic():
                    resource = result.get('resource')
                    out_trade_no = resource.get('out_trade_no')
                    order = Order.objects.get(order_no=out_trade_no)
                    order.status = OrderStatusEnum.refunded
                    refund_no = resource.get('out_refund_no')
                    refund_order = RefundOrder.objects.get(refund_no=refund_no)
                    refund_order.status = RefundStatusEnum.SUCCESS
                    refund_order.refund_id = resource.get('refund_id')
                    refund_order.refund_amount = resource.get('amount').get('payer_refund')
                    success_time = resource.get('success_time')
                    success_time = success_time.replace('+08:00', '')
                    success_time = success_time.replace('T', ' ')
                    refund_order.success_time = success_time
                    refund_order.user_received_account = resource.get('user_received_account')
                    refund_order.save()
                    order.save()
                    # 查询是否使用优惠券，如果使用了返回优惠券
                    coupon = Coupon.objects.filter(coupon_no=order.coupon_no).first()
                    if coupon:
                        coupon.status = CouponStatusEnum.SENDED
                        coupon.save()
                    interview = ProjectInterview.objects.filter(order=order).first()
                    remove_order_data(order, interview, is_delete=False)
                    if interview:
                        send_interview_confirmed_or_cancel_message.delay(interview.pk, 'interview_cancel')

                    # 订单退款给教练发送通知
                    work_wechat_user = WorkWechatUser.objects.filter(
                        wx_user_id__isnull=False, user_id=order.public_attr.target_user, deleted=False).first()
                    if work_wechat_user:
                        coach_user = order.public_attr.target_user
                        getui.send_work_wechat_coach_notice.delay(
                            work_wechat_user.wx_user_id,
                            'to_c_order_refund_notice',
                            coach_name=coach_user.cover_name,
                            coach_id=coach_user.id,
                            refund_money=str(Decimal(str(refund_order.refund_amount)) / Decimal('100')),
                        )

                    user = order.public_attr.user
                    params = {
                        'user_id': user.id,
                        'user_name': user.true_name,
                        'content': {'headers': headers, 'data': request.body, 'result': result},
                        'message': '微信退款回调成功'}
                    AliyunSlsLogLayout().send_third_api_log(**params)
                return Response({'code': 'SUCCESS', 'message': '成功'}, status=status.HTTP_200_OK)
            except Exception as e:
                push_wx_error_message(
                    name='微信支付-退款回调参数处理失败', level='error', content={'result': result, 'error': str(e)})
                return Response({'code': 'FAIL', 'message': '失败'}, status=status.HTTP_400_BAD_REQUEST)
        else:
            push_wx_error_message(name='微信支付-退款回调解密失败', level='warning', content={'headers': request.META, 'result': result})
            params = {
                'url': "wechat_pay_call_back",
                'body': {'headers': headers, 'data': request.body, 'result': result},
                'message': '微信退款回调失败'
            }
            AliyunSlsLogLayout().send_business_api_log(**params)
            return Response({'code': 'FAIL', 'message': '失败'}, status=status.HTTP_400_BAD_REQUEST)


    @swagger_auto_schema(
        operation_id='拉起支付后未支付且关闭支付页面后取消订单接口',
        operation_summary='拉起支付后未支付且关闭支付页面后取消订单接口',
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'type': openapi.Schema(type=openapi.TYPE_NUMBER, description='订单类型 1-教练辅导订单'),
                'object_id': openapi.Schema(type=openapi.TYPE_NUMBER, description='对应对象id type为1时是interview_id')
            }
        ),
        tags=['订单相关']
    )
    @action(methods=['post'], detail=False, url_path='close_order')
    def close_order(self, request, *args, **kwargs):
        try:
            type = int(request.data.get('type'))
            object_id = request.data.get('object_id')
            if type == 1:
                interview = ProjectInterview.objects.get(pk=object_id)
        except:
            return parameter_error_response()
        if type == 1:  # 取消辅导订单
            order = interview.order
            if order.status == OrderStatusEnum.pending_pay:
                # 清除订单数据
                remove_order_data(order, interview)
        return success_response()

    @swagger_auto_schema(
        operation_id='确认订单详情',
        operation_summary='确认订单详情',
        manual_parameters=[
            openapi.Parameter(
                'type', openapi.IN_QUERY, description='订单类型 1-预约教练订单 2-公益教练订单0元', type=openapi.TYPE_NUMBER,
                required=False,
            ),
            openapi.Parameter(
                'user_id', openapi.IN_QUERY, description='用户id', type=openapi.TYPE_NUMBER, required=True),
            openapi.Parameter(
                'coach_user_id', openapi.IN_QUERY, description='教练用户id', type=openapi.TYPE_NUMBER, required=True),
            openapi.Parameter(
                'activity_id', openapi.IN_QUERY, description='活动标识', type=openapi.TYPE_NUMBER, required=False,
            ),
            openapi.Parameter(
                'personal_activity_id', openapi.IN_QUERY, description='个人活动标识', type=openapi.TYPE_NUMBER, required=False,
            ),
            openapi.Parameter(
                'hours', openapi.IN_QUERY, description='时长', type=openapi.TYPE_NUMBER, required=True),
        ],
        tags=['订单相关']
    )
    @action(methods=['get'], detail=False, url_path='verify_order_detail')
    def verify_order_detail(self, request, *args, **kwargs):
        try:
            user = User.objects.get(pk=request.query_params.get('user_id'))
            coach = Coach.objects.get(user_id=request.query_params.get('coach_user_id'), deleted=False)
            activity_id = request.query_params.get('activity_id')
            personal_activity_id = request.query_params.get('personal_activity_id')
            hours = int(request.query_params.get('hours', 1))
            if activity_id:
                activity = Activity.objects.get(pk=activity_id, deleted=False)
            else:
                activity = None
            if personal_activity_id:
                personal_activity = PersonalActivity.objects.get(pk=personal_activity_id, deleted=False)
                coach.price = personal_activity.price
            else:
                personal_activity = None

        except Activity.DoesNotExist:
            return parameter_error_response('活动不存在')
        except PersonalActivity.DoesNotExist:
            return parameter_error_response('个人活动不存在')
        except:
            return parameter_error_response()
        # 查询订单详情信息
        # if activity and hours != 1:
        #     return parameter_error_response('公益教练只可购买1小时')
        data = get_verify_order_detail(
            user, coach, 'coach_interview', hours, activity=activity, personal_activity=personal_activity)
        if not data:
            return parameter_error_response('订单类型错误')
        coach_info = get_order_coach_info(coach, activity_id=activity_id)
        data['coach_info'] = coach_info
        # 获取用户未完成的订单
        available_order = order_public.get_user_available_order(coachee_user_id=user.id, coach_user_id=coach.user.id)
        data['coachee_info'] = {"is_available_order": available_order.exists()}
        data['image_url'] = settings.DEFAULT_TO_C_MANAGER_QRCODE
        return success_response(data)

    @swagger_auto_schema(
        operation_id='支付下单接口',
        operation_summary='支付下单接口',
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'type': openapi.Schema(type=openapi.TYPE_NUMBER, description='订单类型 1-预约教练订单 2-公益教练订单0元'),
                'coach_user_id': openapi.Schema(type=openapi.TYPE_NUMBER, description='教练ID'),
                "coachee_user_id": openapi.Schema(type=openapi.TYPE_NUMBER, description='受教者ID'),
                'coupon_id': openapi.Schema(type=openapi.TYPE_STRING, description='优惠券id'),
                'coupon_no': openapi.Schema(type=openapi.TYPE_STRING, description='优惠券发放凭证'),
                'channel': openapi.Schema(type=openapi.TYPE_NUMBER, description='1-微信支付'),
                'activity_id': openapi.Schema(type=openapi.TYPE_NUMBER, description='活动标识'),
                'personal_activity_id': openapi.Schema(type=openapi.TYPE_NUMBER, description='个人活动标识'),
                'hours': openapi.Schema(type=openapi.TYPE_NUMBER, description='时长'),
            }
        ),
        tags=['订单相关']
    )
    @action(methods=['post'], detail=False, url_path='pay')
    def order_pay(self, request, *args, **kwargs):
        try:
            type = int(request.data.get('type', 1))
            channel = request.data.get('channel', 1)
            activity_id = request.data.get('activity_id')
            personal_activity_id = request.data.get('personal_activity_id')
            hours = int(request.data.get('hours', 1))
            coach_user_id = request.data.get('coach_user_id')
            coach = Coach.objects.get(user_id=coach_user_id, deleted=False)
            coachee_user_id = request.data.get('coachee_user_id')
            invite_code = request.data.get('invite_code')
            user = User.objects.get(pk=coachee_user_id, deleted=False)
            if activity_id:
                activity = Activity.objects.get(pk=activity_id, deleted=False)
            else:
                activity = None
            if personal_activity_id:
                personal_activity = PersonalActivity.objects.get(pk=personal_activity_id, deleted=False)
                # 如果有个人活动参数，则忽略活动参数
                activity = None
            else:
                personal_activity = None

            # 获取海报信息
            poster_id = request.data.get('poster_id')
            poster = Poster.objects.filter(auto_id=poster_id, deleted=False).first()

        except Activity.DoesNotExist:
            return parameter_error_response('活动不存在')
        except PersonalActivity.DoesNotExist:
            return parameter_error_response('个人活动不存在')
        except Coach.DoesNotExist:
            return parameter_error_response('教练不存在')
        except User.DoesNotExist:
            return parameter_error_response('用户不存在')
        except:
            return parameter_error_response()

        if activity:
            is_residue_activity_count = order_public.activity_anticipation_allowed(coachee_user_id, activity=activity)
            if not is_residue_activity_count:
                return parameter_error_response('您的报名已达上限~')
        if personal_activity:
            is_residue_personal_activity_count = order_public.personal_activity_anticipation_allowed(
                coachee_user_id, personal_activity, hours)
            if not is_residue_personal_activity_count:
                return parameter_error_response('超过海报最大预约次数~')

        data = request.data.copy()
        # 1.创建辅导，订单，商品信息
        is_success, order, msg = create_order_info(
            data, type=type, hours=hours, activity=activity, personal_activity=personal_activity, poster=poster)
        if not is_success:
            return parameter_error_response(msg)
        # 如果订单价格是0元，这里不用支付，订单状态直接是已支付
        if order.status == OrderStatusEnum.paid:
            payment_data = {
                'order_id': order.pk,
                'order_status': order.status
            }
            # 订单支付给教练发送通知
            work_wechat_user = WorkWechatUser.objects.filter(
                wx_user_id__isnull=False, user_id=order.public_attr.target_user, deleted=False).first()
            if work_wechat_user:
                coach_user = order.public_attr.target_user
                getui.send_work_wechat_coach_notice.delay(
                    work_wechat_user.wx_user_id,
                    'to_c_order_pay_notice',
                    coach_name=coach_user.cover_name,
                    coach_id=coach_user.id,
                    pay_money=str(Decimal(str(order.payer_amount)) / Decimal('100')),
                )
            if invite_code:
                channel1, channel2, channel3 = personal_user_public.invite_code_displace_channel(invite_code)
                payment_data['channel1'] = channel1
                payment_data['channel2'] = channel2
                payment_data['channel3'] = channel3
            return success_response(payment_data)
        # 获取优惠信息
        coupon_data = get_verify_order_detail(
            order.public_attr.user, coach, 'coach_interview', hours,
            activity=activity, personal_activity=personal_activity)
        coupon_no = coupon_data.get('coupon_no')
        # 2.调用第三方支付
        is_success, payment_data = third_party_payment(channel=channel, order=order, coupon_no=coupon_no)
        if not is_success:
            remove_order(order)
            return parameter_error_response(f'支付失败:{payment_data}')
        payment_data['order_id'] = order.pk
        payment_data['order_status'] = order.status,
        if invite_code:
            channel1, channel2, channel3 = personal_user_public.invite_code_displace_channel(invite_code)
            payment_data['channel1'] = channel1
            payment_data['channel2'] = channel2
            payment_data['channel3'] = channel3
        return success_response(payment_data)

    @swagger_auto_schema(
        operation_id='预约辅导页面教练头像、姓名、价格',
        operation_summary='预约辅导页面教练头像、姓名、价格',
        manual_parameters=[
            openapi.Parameter(
                'coach_user_id', openapi.IN_QUERY, description='教练用户id', type=openapi.TYPE_NUMBER, required=True)
        ],
        tags=['订单相关']
    )
    @action(methods=['get'], detail=False, url_path='coach_info')
    def coach_info(self, request, *args, **kwargs):
        try:
            coach_user_id = request.query_params['coach_user_id']
            coach = Coach.objects.get(user_id=coach_user_id, deleted=False)
            user = request.user
            personal_activity_id = request.query_params.get('personal_activity_id')
            activity_id = request.query_params.get('activity_id')
            order_id = request.query_params.get('order_id')
            if personal_activity_id:
                personal_activity = PersonalActivity.objects.get(pk=personal_activity_id, deleted=False)
                coach.price = personal_activity.price
            if activity_id:
                activity = Activity.objects.get(pk=str(activity_id), deleted=False)
                coach.price = activity.price
        except (KeyError, Coach.DoesNotExist, PersonalActivity.DoesNotExist, Activity.DoesNotExist):
            return parameter_error_response()
        coach_info = get_order_coach_info(coach, user=user, activity_id=activity_id, order_id=order_id)

        # 获取活动时长，用户在活动中只能预约指定小时数
        if activity_id:
            activity = Activity.objects.get(pk=str(activity_id), deleted=False)
            coach_info['activity_hours'] = activity.hours
        return success_response(coach_info)

    @swagger_auto_schema(
        operation_id='订单详情（轮询查看订单支付状态用-新',
        operation_summary='订单详情（轮询查看订单支付状态用-新',
        manual_parameters=[
            openapi.Parameter(
                'order_id', openapi.IN_QUERY, description='订单id', type=openapi.TYPE_STRING, required=True)
        ],
        tags=['订单相关']
    )
    @action(methods=['get'], detail=False, url_path='detail')
    def order_detail(self, request, *args, **kwargs):
        try:
            order_id = request.query_params['order_id']
            order = Order.objects.get(pk=order_id, deleted=False)
        except (KeyError, Order.DoesNotExist):
            return parameter_error_response()
        data = {"id": order.pk, "status": order.status}
        return success_response(data)


    @swagger_auto_schema(
        operation_id='预约辅导接口',
        operation_summary='预约辅导接口',
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'coach_user_id': openapi.Schema(type=openapi.TYPE_NUMBER, description='教练ID'),
                "coachee_user_id": openapi.Schema(type=openapi.TYPE_NUMBER, description='受教者ID'),
                'order_id': openapi.Schema(type=openapi.TYPE_STRING, description='订单id'),
                'start_time': openapi.Schema(type=openapi.TYPE_STRING, description='开始时间'),
                'end_time': openapi.Schema(type=openapi.TYPE_NUMBER, description='结束时间'),
                'topic': openapi.Schema(type=openapi.TYPE_NUMBER, description='辅导议题'),
            }
        ),
        tags=['订单相关']
    )
    @action(methods=['post'], detail=False, url_path='interview')
    def add_interview(self, request, *args, **kwargs):
        try:
            coach_user_id = request.data['coach_user_id']
            coachee_user_id = request.data['coachee_user_id']
            coach = Coach.objects.get(user_id=coach_user_id, deleted=False)
            user = User.objects.get(id=coachee_user_id, deleted=False)
            start_time = request.data['start_time']
            end_time = request.data['end_time']
            topic = request.data['topic']
            order_id = request.data.get('order_id', None)
        except (KeyError, Coach.DoesNotExist, User.DoesNotExist, PersonalActivity.DoesNotExist, Poster):
            return parameter_error_response()
        start_time = start_time + ':00'
        end_time = end_time + ':00'
        date1 = datetime.datetime.strptime(start_time, "%Y-%m-%d %H:%M:%S")
        date2 = datetime.datetime.strptime(end_time, "%Y-%m-%d %H:%M:%S")
        # 检查辅导结束时间是否早于当前时间，如果早于当前时间无法下单（不能预约当前时间之前的辅导）
        if date2 < datetime.datetime.now():
            return parameter_error_response('无法预约当前时间之前的教练辅导')
        hours = (date2 - date1).seconds / 3600
        if order_id:
            order = Order.objects.filter(pk=order_id, deleted=False, status=OrderStatusEnum.paid).first()
            if not order:
                return parameter_error_response('订单不存在')
            interview_time = order.interview.filter(
                deleted=False).exclude(
                public_attr__status=ATTR_STATUS_INTERVIEW_CANCEL).aggregate(times_minute=Sum('times'))
            if interview_time['times_minute']:
                interview_hour = round(interview_time['times_minute'] / 60, 1)
            else:
                interview_hour = 0
            remain_hours = order.count - interview_hour
            if remain_hours < hours:
                return parameter_error_response('剩余时长不足')
        else:
            orders = Order.objects.filter(public_attr__user=user, public_attr__target_user=coach.user, deleted=False,
                                          status=OrderStatusEnum.paid).order_by('created_at')
            if orders.exists():
                remain_hours = 0
                order = None
                for tmp_order in orders:
                    interview_time = tmp_order.interview.filter(
                        deleted=False).exclude(
                        public_attr__status=ATTR_STATUS_INTERVIEW_CANCEL).aggregate(times_minute=Sum('times'))
                    if interview_time['times_minute']:
                        interview_hour = round(interview_time['times_minute'] / 60, 1)
                    else:
                        interview_hour = 0
                    remain_hours = tmp_order.count - interview_hour
                    if remain_hours > 0:
                        order = tmp_order
                        break
                if not order:
                    return parameter_error_response('未找到订单')
                if remain_hours < hours:
                    return parameter_error_response('剩余时长不足')
            else:
                return parameter_error_response('未找到订单')
            
        # 判断时间是否有效活动
        apply_type = ScheduleApplyTypeEnum.activity.value if order.activity else ScheduleApplyTypeEnum.personal.value
        time_slots = schedule_public.get_schedule_day_list(coach_user_id, date1, apply_type=apply_type)
        status = schedule_public.is_time_slot_available(time_slots, date1, date2)
        if not status:
            return parameter_error_response('无法预约当前时间的教练辅导')
        
        with transaction.atomic():
            interview = order_public.create_interview(start_time, end_time, coach.user_id, user.pk, topic)
            interview.order = order
            interview.save()
            if order.activity:
                ActivityInterview.objects.create(
                    activity=order.activity, interview=interview)
            if order.personal_activity:
                PersonalActivityInterview.objects.create(
                    personal_activity=order.personal_activity, interview=interview, poster_id=order.poster_id)

        send_interview_confirmed_or_cancel_message.delay(interview.pk, 'interview_confirmed')
        push_lark_message_celery.delay([interview.pk], 'success_pay')
        return success_response({"interview_id": interview.pk})

    @swagger_auto_schema(
        operation_id='退款接口',
        operation_summary='退款接口',
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'interview_id': openapi.Schema(type=openapi.TYPE_NUMBER, description='辅导ID'),
                'reason': openapi.Schema(type=openapi.TYPE_STRING, description='退款原因')
            }
        ),
        tags=['订单相关']
    )
    @action(methods=['post'], detail=False, url_path='refund')
    def refund_order(self, request, *args, **kwargs):
        try:
            interview_id = request.data['interview_id']
            interview = ProjectInterview.objects.get(pk=interview_id, deleted=False)
            reason = request.data['reason']
        except (KeyError, ProjectInterview.DoesNotExist):
            return parameter_error_response()
        if not interview.order:
            return parameter_error_response()
        last_time = interview.public_attr.start_time - datetime.timedelta(hours=4)
        if datetime.datetime.now() > last_time:
            return parameter_error_response('当前距离辅导开始不足4小时，无法退款')
        order = interview.order
        if order.activity:
            return parameter_error_response('公益教练无法退款')
        if order.count != 1:
            return parameter_error_response('暂不支持部分退款')
        is_success, msg = create_refund_order(order, reason, refund_type='wechat')
        if not is_success:
            return parameter_error_response('取消辅导失败')
        interview_public.cancel_interview(interview, reason)
        interview_time = interview.public_attr.start_time.strftime('%Y-%m-%d %H:%M') + '-' + \
                         interview.public_attr.end_time.strftime('%H:%M')
        push_v2_message.delay(interview.public_attr.user, 'coach_interview_cancel',
                              param={'target_user': interview.public_attr.target_user.cover_name,
                                     'interview_time': interview_time}, project_id=interview.public_attr.project_id)
        # push_to_single.delay(interview.public_attr.user.user_cid, title='%s,你和%s的教练辅导已取消' % (
        #     interview.public_attr.user.cover_name, interview.public_attr.target_user.cover_name), body='查看取消详情')
        return success_response('您的订单已发起退款，将在1-5个工作日退回您的原支付方式')

    @swagger_auto_schema(
        operation_id='c端用户可预约时长',
        operation_summary='用户可预约时长',
        manual_parameters=[
            openapi.Parameter(
                'coachee_user_id', openapi.IN_QUERY, description='教练用户id', type=openapi.TYPE_NUMBER, required=True)
        ],
        tags=['订单相关']
    )
    @action(methods=['get'], detail=False, url_path='remain_time')
    def remain_time(self, request, *args, **kwargs):
        try:
            coachee_user_id = request.query_params['coachee_user_id']
            user = User.objects.get(id=coachee_user_id, deleted=False)
        except (KeyError, User.DoesNotExist):
            return parameter_error_response()
        data, coach_user_ids = [], []
        total_hours = 0
        orders = Order.objects.filter(
            public_attr__user=user, deleted=False, status=OrderStatusEnum.paid)
        for order in orders:
            interview_time = order.interview.filter(
                deleted=False).exclude(
                public_attr__status=ATTR_STATUS_INTERVIEW_CANCEL).aggregate(times_minute=Sum('times'))
            if interview_time['times_minute']:
                interview_hour = round(interview_time['times_minute'] / 60, 1)
            else:
                interview_hour = 0
            remain_hours = order.count - interview_hour
            if remain_hours > 0:
                coach = Coach.objects.filter(user=order.public_attr.target_user, deleted=False).first()
                if coach:
                    coach_info = get_order_coach_info(coach)
                    coach_info['coach_user_id'] = coach.user_id
                    coach_info['hours'] = remain_hours
                    total_hours += remain_hours
                    if coach.user_id not in coach_user_ids:
                        data.append(coach_info)
                        coach_user_ids.append(coach.user_id)
                    else:
                        for coach_info in data:
                            if coach_info['coach_user_id'] == coach.user_id:
                                coach_info['hours'] += remain_hours
        return success_response({"total_hours": total_hours, "coaches": data})


    @swagger_auto_schema(
        operation_id='退款接口',
        operation_summary='退款接口',
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'order_no': openapi.Schema(type=openapi.TYPE_STRING, description='订单号'),
            }
        ),
        tags=['订单相关']
    )
    @action(methods=['post'], detail=False, url_path='order_no/refund')
    def order_no_refund_order(self, request, *args, **kwargs):
        try:
            order_no = request.data['order_no']
            order = Order.objects.get(order_no=order_no, deleted=False)
        except (KeyError, Order.DoesNotExist):
            return parameter_error_response('订单不存在')

        err_msg = order_public.refund_order(order, '用户退款')
        if err_msg:
            return parameter_error_response(err_msg)

        params = {
            'url': request.get_full_path(),
            'token': request.META.get('HTTP_AUTHORIZATION'),
            'user_id': request.user.pk,
            'true_name': request.user.cover_name,
            'body': request.body.decode('utf8') if request.body.decode('utf8') else {},
            'params': request.GET if request.GET else {},
            'message': '订单退款'
        }
        task.send_business_api_sls_log.delay(params)
        return success_response('您的订单已发起退款，将在1-5个工作日退回您的原支付方式')