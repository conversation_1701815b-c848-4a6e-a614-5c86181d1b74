import json
import time
from datetime import datetime
from decimal import Decimal

import pendulum
import redis
from django.db import transaction

from drf_yasg import openapi
from rest_framework import viewsets
from drf_yasg.utils import swagger_auto_schema
from rest_framework.decorators import action
from django.conf import settings

from utils import validate, utc_date_time
from utils.miniapp_version_judge import compare_version
from wisdom_v2.common import activity_coach_public, schedule_public, personal_user_public
from wisdom_v2.enum.service_content_enum import UserInviteTypeEnum, CoachAuthEnum, ScheduleApplyTypeEnum, \
    ActivityCoachStatusEnum
from wisdom_v2.enum.user_enum import BrowseRecordObjectEnum
from wisdom_v2.models import Resume, UserInviteRecord, User
from wisdom_v2.app_views.app_resume_action import AppResumeSerializer, AppResumeUpdateSerializer
from utils.api_response import success_response, parameter_error_response
from wisdom_v2.models_file import PersonalActivity, PersonalActivityFlow, Poster, ActivityCoach
from wisdom_v2.models_file.coach import BrowseRecord

data_redis = redis.Redis.from_url(settings.DATA_REDIS)


class AppResumeViewSet(viewsets.ModelViewSet):
    queryset = Resume.objects.filter(deleted=False).order_by('-created_at')
    serializer_class = AppResumeSerializer

    @swagger_auto_schema(
        operation_id='简历详情',
        operation_summary='简历详情',
        manual_parameters=[
            openapi.Parameter('resume_id', openapi.IN_QUERY, description='简历id', type=openapi.TYPE_NUMBER),
            openapi.Parameter('invite_code', openapi.IN_QUERY, description='邀请码', type=openapi.TYPE_STRING),
            openapi.Parameter('poster_code', openapi.IN_QUERY, description='海报标识', type=openapi.TYPE_STRING),
            openapi.Parameter('user_id', openapi.IN_QUERY, description='教练用户id', type=openapi.TYPE_NUMBER),
        ],
        tags=['教练简历相关']
    )
    @action(methods=['get'], detail=False, url_path='detail', authentication_classes=[])
    def resume_detail(self, request, *args, **kwargs):
        try:
            user_id = request.query_params.get('user_id')
            resume_id = request.query_params.get('resume_id')
            coachee_user_id = request.query_params.get('coachee_user_id', None)
            invite_code = request.query_params.get('invite_code')
            poster_id = request.query_params.get('poster_id')
            if resume_id:
                resume = Resume.objects.get(pk=resume_id, deleted=False)
            else:
                resume = Resume.objects.filter(coach__user_id=user_id, deleted=False,
                                               coach__deleted=False).order_by('created_at').first()
                name = resume.name
            mp = json.loads(request.headers.get('mp')) if request.headers.get('mp') else None
        except Resume.DoesNotExist:
            return parameter_error_response('简历不存在')
        except Exception:
            return parameter_error_response()
        token_user_id = validate.get_not_token_user_id(request)
        if token_user_id:
            data_redis.set(f'app_resume_detail_{token_user_id}', resume.id, ex=1209600)
            data_redis.set(f'app_resume_detail_count_{resume.coach_id}__{time.time()}', 1, ex=1209600)
        serializer = self.get_serializer(resume, context={'coachee_user_id': coachee_user_id}).data
        activity_id = None
        personal_activity_id = None
        price = None
        activity_data = None

        # 如果存在邀请码
        if invite_code:
            # 根据邀请码和未删除的条件查询用户邀请记录
            invite = UserInviteRecord.objects.filter(pk=invite_code, deleted=False).first()
            # 如果找到了邀请记录
            if invite:
                # 使用数据库事务确保数据的一致性
                with transaction.atomic():
                    # 如果邀请类型是活动类型
                    if invite.type == UserInviteTypeEnum.activity.value:
                        # 如果没有邀请人，则认为是活动邀请
                        if invite.referrer:
                            # 邀请人是教练
                            if ActivityCoach.objects.filter(
                                    activity_id=invite.object_id, deleted=False,
                                    status=ActivityCoachStatusEnum.joined.value,
                                    coach__user_id=invite.referrer_id).exists():
                                # 邀请人是教练，并且通过活动校验，则将受邀人添加到活动教练中
                                activity_coach = activity_coach_public.invite_to_activity_coach_data(invite, resume)
                                if activity_coach:
                                    # 更新活动教练的查询数据
                                    activity_coach_public.update_activity_coach_query_data(
                                        activity_coach, token_user_id)
                                    # 获取活动ID
                                    activity_id = str(activity_coach.activity_id)
                            # 邀请人不是教练，返回活动信息
                            else:
                                activity_id = invite.object_id
                        else:
                            activity_id = invite.object_id

                        if activity_id:
                            activity_data = activity_coach_public.get_coach_activity_data(activity_id, resume.coach, coachee_user_id)
                            if activity_data:
                                price = activity_data.get('price')

                    # 如果邀请类型是教练分享
                    elif invite.type == UserInviteTypeEnum.coach_share.value:
                        # 查询未过期且未删除的个人活动
                        # 去掉个人海报过期逻辑
                        personal_activity = PersonalActivity.objects.filter(
                            pk=invite.object_id, deleted=False).first()
                        if personal_activity:
                            # 更新个人活动教练的查询数据
                            activity_coach_public.update_activity_coach_query_data(personal_activity, token_user_id)
                            # 如果存在海报代码，则创建个人活动额外数据
                            if poster_id:
                                poster = Poster.objects.filter(auto_id=poster_id, deleted=False).first()
                                PersonalActivityFlow.objects.create(
                                    personal_activity=personal_activity, poster=poster, user_id=token_user_id)
                            # 获取个人活动ID
                            personal_activity_id = str(personal_activity.id)
                            # 教练分享的活动可以自定义金额
                            price = Decimal(str(personal_activity.price)) / Decimal('100')

        serializer['activity_id'] = activity_id
        serializer['personal_activity_id'] = personal_activity_id
        serializer['activity_data'] = activity_data  # 简历详情页面要返回活动具体信息，提供给前段做弹窗判断
        # 旧版本小程序没有5选择，会报错，需要处理
        if mp and compare_version(mp.get('version'), '2.23') < 0 and serializer['coach_auth'] == CoachAuthEnum.PREPARE_ICF_ACC.value:
            serializer['coach_auth'] = 0  # 2.23版本前小程序中coach_auth=0等于“无”，后续不用处理，在此处兼容返回0

        schedule_describe = None  # 默认没有教练日程描述
        if activity_data:
            start_date = activity_data.get('interview_start_date')  # 活动开始日期
            end_date = activity_data.get('interview_end_date')  # 活动开始日期
            # 如果活动开始时间小于当前时间，则开始时间设置为当前时间
            if start_date < datetime.now().date():
                start_date = datetime.now().date()

            # 如果活动结束时间小于当前时间，则设置默认时间为当前时间+两周
            if end_date < datetime.now().date():
                start_date = pendulum.now().naive().date()
                end_date = pendulum.now().add(weeks=2).naive().date()

                # 当前时间大于结束时间，活动已结束增加提示，移除活动信息
                schedule_describe = '公益教练时段已约满，您可付费预约'
                serializer['activity_id'] = None
                serializer['activity_data'] = None

        else:
            start_date = pendulum.now().naive().date()
            end_date = pendulum.now().add(weeks=2).naive().date()

        if serializer['activity_id']:
            apply_type = ScheduleApplyTypeEnum.activity.value
        else:
            apply_type = ScheduleApplyTypeEnum.personal.value

        all_schedule_data, all_schedule_hour = schedule_public.get_schedule_time_list(
            resume.coach.user_id, start_date, end_date, apply_type=apply_type)

        # 有活动信息
        if activity_data:
            # 活动没有可预约时间，修改日程描述，置空活动id和活动数据
            if all_schedule_hour == 0:
                schedule_describe = '公益教练时段已约满，您可付费预约'
                serializer['activity_id'] = None
                serializer['activity_data'] = None
            # 活动还有可预约时间，修改教练金额为活动金额
            else:
                # 公益活动也可以自定义金额， 所以一旦有活动信息导致的价格变化，需要更新教练简历页面展示的支付金额
                # 活动金额可以是0 这里需要判断是不是None
                if price is not None:
                    serializer['price'] = price

        serializer['schedule_data'] = {
            'describe': schedule_describe,  # 教练日程描述
            'content': all_schedule_data,  # 教练日程数据
            'hour': all_schedule_hour}  # 教练日程总可约时长

        serializer['manager_qrcode'] = settings.DEFAULT_TO_C_MANAGER_QRCODE
        if invite_code:
            channel1, channel2, channel3 = personal_user_public.invite_code_displace_channel(invite_code)
            serializer['channel1'] = channel1
            serializer['channel2'] = channel2
            serializer['channel3'] = channel3

        # 非本人本人查看，简历查看次数增加
        if token_user_id != resume.coach.user_id:
            # 更新浏览次数
            resuem_browse_record, is_create = BrowseRecord.objects.get_or_create(
                user_id=resume.coach.user_id, object_type=BrowseRecordObjectEnum.resume.value, object_id=resume.id, deleted=False)
            resuem_browse_record.total_views += 1
            resuem_browse_record.save()
            date = pendulum.now().to_date_string()
            redis_key = f"browse_record_user:{resume.coach.user_id}:resume:{resume.id}:date:{date}"
            # 如果已经有值-自增。
            if data_redis.get(redis_key):
                data_redis.incr(redis_key)
            else:
                # 设置过期时间为30天后的0点0分0秒
                timeout = utc_date_time.get_total_time(
                    pendulum.now().to_datetime_string(), pendulum.now().add(days=30).to_date_string()).seconds
                data_redis.set(redis_key, 1, ex=timeout)

        return success_response(serializer)

    @swagger_auto_schema(
        operation_id='修改简历信息',
        operation_summary='修改简历信息',
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'type': openapi.Schema(type=openapi.TYPE_NUMBER, description='教练类型 1-签约教练 2-个人教练'),
                'user_id': openapi.Schema(type=openapi.TYPE_NUMBER, description='教练用户id'),
                'name': openapi.Schema(type=openapi.TYPE_STRING, description='用户名'),
                'email': openapi.Schema(type=openapi.TYPE_STRING, description='邮箱'),
                'phone': openapi.Schema(type=openapi.TYPE_STRING, description='手机号'),
                'gender': openapi.Schema(type=openapi.TYPE_NUMBER, description='性别'),
                'english_name': openapi.Schema(type=openapi.TYPE_STRING, description='英文名'),
                'personal_name': openapi.Schema(type=openapi.TYPE_STRING, description='个人客户可见名字'),
                'head_image_url': openapi.Schema(type=openapi.TYPE_STRING, description='头像链接'),
                'birthday': openapi.Schema(type=openapi.TYPE_STRING, description='生日'),
                'user_class': openapi.Schema(
                    type=openapi.TYPE_STRING, description='班次'),
                'extra_time': openapi.Schema(
                    type=openapi.TYPE_NUMBER, description='平台外一对一辅导时常'),
                'working_years': openapi.Schema(
                    type=openapi.TYPE_NUMBER, description='工作年限 | 1：一年以上，2：三年以上，3：五年以上'),
                'language': openapi.Schema(
                    type=openapi.TYPE_NUMBER, description='语言｜ 1：中文，2：英语，3：法语，4：日语'),
                'work_experience': openapi.Schema(type=openapi.TYPE_STRING, description='工作经历'),
                'brief': openapi.Schema(type=openapi.TYPE_STRING, description='教练简介'),
                'customer_evaluate': openapi.Schema(type=openapi.TYPE_STRING, description='案例及客户评价'),
                'style': openapi.Schema(type=openapi.TYPE_STRING, description='教练风格'),
                'qualification': openapi.Schema(type=openapi.TYPE_ARRAY, description='资质证书', items=openapi.Schema(
                    type=openapi.TYPE_OBJECT, properties={
                        "describe": openapi.Schema(type=openapi.TYPE_STRING, description='描述'),
                        "image_url": openapi.Schema(type=openapi.TYPE_STRING, description='图片链接'),
                    })),
                'industry': openapi.Schema(type=openapi.TYPE_STRING, description='教练过的行业'),
                'domain': openapi.Schema(type=openapi.TYPE_STRING, description='教练领域'),
                'coach_auth': openapi.Schema(
                    type=openapi.TYPE_STRING, description='教练认证 ｜ 1：ICF-ACC, 2：ICF-PCC, 3：ICF-MCC, 4：准ICF-PCC, 5：个人教练'),
            }
        ),
        tags=['教练简历相关']
    )
    @action(methods=['post'], detail=False, url_path='update', serializer_class=AppResumeUpdateSerializer)
    def update_resume(self, request, *args, **kwargs):
        try:
            resume_id = request.data.get('id')
            resume = Resume.objects.get(pk=resume_id)
        except:
            return parameter_error_response()
        data = request.data
        serializer = self.get_serializer(data=data)
        serializer.is_valid(raise_exception=True)
        validated_data = serializer.validated_data
        return success_response(serializer.edit_app_resume_msg(validated_data))
