import datetime
import logging
import redis
import requests
import hashlib
import time

from django.conf import settings
from django.db import transaction
from drf_yasg import openapi
from rest_framework import viewsets
from drf_yasg.utils import swagger_auto_schema
from rest_framework.decorators import action
from django.shortcuts import HttpResponse

from utils import wx_biz_msg_crypt, task
from utils.work_wechat import WorkWechat
from wisdom_v2 import utils
from wisdom_v2.app_views.work_wechat_action import WorkWechatUserSerializers
from wisdom_v2.common import user_public, coachee_public
from wisdom_v2.models import WorkWechatUser, ProjectMember, UserBackend, TraineeCoachInviteUser, Coach, User
from utils.api_response import success_response, parameter_error_response
from wisdom_v2.enum.user_backend_enum import UserBackendTypeEnum
from wisdom_v2.enum.project_member_enum import ProjectMemberRoleEnum
from utils.authentication import get_token
from wisdom_v2.enum.user_enum import UserRoleEnum
from wisdom_v2.views.user_actions import UserSerializer

error_logging = logging.getLogger('api_action')
third_party_redis = redis.Redis.from_url(settings.THIRD_PARTY_DATA_REDIS)


class WorkWechatViewSet(viewsets.ModelViewSet):
    queryset = WorkWechatUser.objects.filter(deleted=False)
    serializer_class = WorkWechatUserSerializers

    @swagger_auto_schema(
        operation_id='企业微信获取项目成员信息',
        operation_summary='企业微信获取项目成员信息',
        manual_parameters=[
            openapi.Parameter('work_wechat_user_id',
                              openapi.IN_QUERY, description='企业微信用户id', type=openapi.TYPE_STRING),
            ],
        tags=['企业微信相关']
    )
    @action(methods=['get'], detail=False, url_path='user')
    def get_work_wechat_user(self, request, *args, **kwargs):
        try:
            work_wechat_user_id = request.query_params.get('work_wechat_user_id')
            wx_user = self.queryset.filter(
                wx_user_id=work_wechat_user_id,
                user__isnull=False
            )
            if wx_user.exists():
                serializer = self.get_serializer(wx_user.first())
                return success_response(serializer.data)
            else:
                return success_response()
        except Exception as e:
            return parameter_error_response(str(e))

    @swagger_auto_schema(
        operation_id='企业微信获取session_key',
        operation_summary='企业微信获取session_key',
        manual_parameters=[
            openapi.Parameter('code', openapi.TYPE_STRING, description='前端置换的用户信息凭证', type=openapi.TYPE_STRING),
            ],
        tags=['企业微信相关']
    )
    @action(methods=['get'], detail=False, url_path='session_key', authentication_classes=[])
    def session_key(self, request, *args, **kwargs):
        try:
            code = request.query_params['code']
        except:
            return parameter_error_response()
        is_success, data = WorkWechat().get_code2session(code)
        if not is_success:
            return parameter_error_response(data)
        return success_response(data)

    @swagger_auto_schema(
        operation_id='企业微信外部联系人id置换客户id',
        operation_summary='企业微信外部联系人id置换客户id',
        manual_parameters=[
            openapi.Parameter('external_user_id', openapi.TYPE_STRING, description='用户的外部联系人id', type=openapi.TYPE_STRING),
            openapi.Parameter('coach_user_id', openapi.TYPE_STRING, description='教练的用户id', type=openapi.TYPE_STRING),
            ],
        tags=['企业微信相关']
    )
    @action(methods=['get'], detail=False, url_path='coachee')
    def get_coachee_info(self, request, *args, **kwargs):
        try:
            external_user_id = request.query_params.get('external_user_id')
            coach_user_id = request.query_params.get('coach_user_id')
            coachee_wx_user = WorkWechatUser.objects.filter(
                external_user_id=external_user_id, user_id__isnull=False, deleted=False).first()
            User.objects.get(id=coach_user_id, deleted=False)
            coach = Coach.objects.get(user_id=coach_user_id, deleted=False)
        except (User.DoesNotExist, Coach.DoesNotExist):
            return parameter_error_response('教练信息错误')
        except Exception as e:
            return parameter_error_response(str(e))
        if not coachee_wx_user:
            return parameter_error_response('企业微信对应用户信息不存在')

        customer_list = coachee_public.get_coach_customer_list(coach.user_id)
        if coachee_wx_user.user_id not in customer_list:
            coachee_user_id = None
        else:
            coachee_user_id = coachee_wx_user.user_id
        return success_response({'coachee_user_id': coachee_user_id})


    @swagger_auto_schema(
        operation_id='企业微信成员绑定授权二维码',
        operation_summary='企业微信成员绑定授权二维码',
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'work_wechat_user_id': openapi.Schema(type=openapi.TYPE_STRING, description='企业微信用户id'),
                'qr_code': openapi.Schema(type=openapi.TYPE_STRING, description='二维码链接')
            }
        ),
        tags=['企业微信相关']
    )
    @action(methods=['post'], detail=False, url_path='qr_code')
    def post_department_user_qr_code(self, request, *args, **kwargs):
        try:
            work_wechat_user_id = request.data.get('work_wechat_user_id')
            qr_code = request.data.get('qr_code')

            wx_user = self.queryset.filter(
                wx_user_id=work_wechat_user_id,
                user__isnull=False
            ).first()
            if wx_user:
                wx_user.qr_code = qr_code
                wx_user.save()
                # 是否是教练
                is_jump_miniapp = Coach.objects.filter(user_id=wx_user.user.id, deleted=False).exists()
                return success_response({'is_jump_miniapp': is_jump_miniapp, 'is_sign': True})
            else:
                return parameter_error_response('当前企微账号未绑定教练或员工账号')
        except Exception as e:
            return parameter_error_response(str(e))

    @swagger_auto_schema(
        operation_id='企业微信部门成员列表',
        operation_summary='企业微信部门成员列表',
        manual_parameters=[
            openapi.Parameter('is_enterprise_coach', openapi.IN_QUERY,
                              description='是否是企业教练用户列表', type=openapi.TYPE_BOOLEAN)
            ],
        tags=['企业微信相关']
    )
    @action(methods=['get'], detail=False, url_path='department')
    def get_department_users(self, request, *args, **kwargs):
        try:
            is_enterprise_coach = request.query_params.get('is_enterprise_coach', 'true').lower()
        except:
            return parameter_error_response()

        if is_enterprise_coach not in ['true', 'false']:
            return parameter_error_response('is_enterprise_coach参数格式错误')

        if is_enterprise_coach == 'true':
            state, content = utils.get_work_wechat_department_users(include=[settings.WORK_WECHAT_COACH_DEPARTMENT_ID])
        else:
            state, content = utils.get_work_wechat_department_users(
                exclude=[settings.WORK_WECHAT_COACH_DEPARTMENT_ID, settings.WORK_WECHAT_EXTERNAL_DEPARTMENT_ID])
        if not state:
            return parameter_error_response(content)
        return success_response(content)

    @swagger_auto_schema(
        methods=['get', 'post'],
        operation_id='企业微信回调',
        operation_summary='企业微信回调',
        tags=['企业微信相关']
    )
    @action(methods=['get', 'post'], detail=False, url_path='callback', authentication_classes=[])
    def work_wechat_callback(self, request, *args, **kwargs):
        try:

            # 获取企业微信加解密类
            wxcpt = wx_biz_msg_crypt.WXBizMsgCrypt(
                settings.WORK_WECHAT_CALLBACK_TOKEN, settings.WORK_WECHAT_CALLBACK_KEY, settings.WORK_WECHAT_CORPID)

            # 获取企业微信回调通知的基础数据
            msg_signature = request.query_params.get('msg_signature')
            time_stamp = request.query_params.get('timestamp')
            nonce = request.query_params.get('nonce')
            is_work_wechat_callback = request.query_params.get('is_work_wechat_callback', 'true')

            # 请求项目其他回调链接时的参数拼接
            domain, params = request.get_full_path_info().split('?')
            domain = request.META.get('HTTP_HOST')

            # 根据不同请求方式处理数据
            if request.method == 'GET':
                # get请求主要用于域名验证

                # 如果是企业微信回调，则分发给其他服务器项目
                if is_work_wechat_callback == 'true':
                    try:
                        params += '&is_work_wechat_callback=false'  # 防止项目间循环回调
                        for item in settings.WORK_WECHAT_CALLBACK_URL:
                            if domain not in item:
                                url = '{}?{}'.format(item, params)  # 请求参数拼接
                                requests.get(url, data=request.body)
                    except Exception as e:
                        error_logging.info({'error': str(e), 'url': request.get_full_path()})

                # 企业微信回调的数据解析处理
                echo_str = request.query_params.get('echostr')
                ret, echo_str = wxcpt.VerifyURL(msg_signature, time_stamp, nonce, echo_str)
                if not echo_str:
                    return parameter_error_response(ret)
                echo_str = echo_str.decode('utf8')

                return HttpResponse(echo_str)
            else:
                # 企业微信回调的数据解析处理
                ret, msg = wxcpt.DecryptMsg(request.body, msg_signature, time_stamp, nonce)
                if not msg:
                    return parameter_error_response(ret)
                task.process_work_wechat_callback.delay(request.body, msg, domain, params, is_work_wechat_callback)

                return HttpResponse('SUCCESS')

        except Exception as e:
            error_logging.info({'error': str(e),
                                'url': request.get_full_path(),
                                'data': request.body})
            return parameter_error_response(str(e))

    @swagger_auto_schema(
        operation_id='企业微信客户信息维护工具-绑定用户',
        operation_summary='企业微信客户信息维护工具-绑定用户',
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'external_user_id': openapi.Schema(type=openapi.TYPE_STRING, description='企业微信客户（外部联系人）ID'),
                'project_member_id': openapi.Schema(type=openapi.TYPE_NUMBER, description='客户对应的被教练者ID')
            }
        ),
        tags=['企业微信相关']
    )
    @action(methods=['post'], detail=False, url_path='customer_information_maintenance')
    def customer_information_maintenance(self, request, *args, **kwargs):
        try:
            external_user_id = request.data['external_user_id']
            project_member_id = request.data['project_member_id']
            project_member = ProjectMember.objects.get(pk=project_member_id)
            user = project_member.user
            manager_user_id = request.user.pk
        except:
            return parameter_error_response()

        # 检查当前项目运营能否绑定当前用户
        project_ids = list(UserBackend.objects.filter(
            user_id=manager_user_id, role__name='项目运营', user_type=UserBackendTypeEnum.admin.value,
            project__isnull=False, deleted=False).values_list('project_id', flat=True))
        if project_member.project_id not in project_ids:
            return parameter_error_response('当前项目运营只能绑定其关联项目下的用户')

        # 置换用户unionid
        state, unionid = WorkWechat().get_user_unionid(external_user_id)
        if not state:
            return parameter_error_response(unionid)

        # 判断当前客户是否绑定unionid
        if user.unionid:
            return parameter_error_response('当前客户已绑定微信')

        with transaction.atomic():
            exists_project_member = ProjectMember.objects.filter(deleted=False, user__unionid=unionid).first()
            # 判断当前unionid是否绑定其它用户
            if exists_project_member:  # 已绑定其它用户，将其它用户unionid置空
                exists_user = exists_project_member.user
                exists_user.unionid = None
                exists_user.save()
            if user.work_wechat_user.filter(deleted=False).exists():
                work_wechat_user = user.work_wechat_user.filter(deleted=False).first()
                work_wechat_user.external_user_id = external_user_id
                work_wechat_user.save()
            else:
                work_wechat_user = WorkWechatUser.objects.create(external_user_id=external_user_id, user=user)
            user.unionid = unionid
            user.save()
        return success_response()

    @swagger_auto_schema(
        operation_id='企业微信客户信息维护工具-检查当前用户',
        operation_summary='企业微信客户信息维护工具-检查当前用户',
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'external_user_id': openapi.Schema(type=openapi.TYPE_STRING, description='企业微信客户（外部联系人）ID'),
            }
        ),
        tags=['企业微信相关']
    )
    @action(methods=['post'], detail=False, url_path='check_bound_user')
    def check_bound_user(self, request, *args, **kwargs):
        try:
            external_user_id = request.data['external_user_id']
        except:
            return parameter_error_response()

        # 置换用户unionid
        state, unionid = WorkWechat().get_user_unionid(external_user_id)
        if not state:
            return parameter_error_response(unionid)

        # 个人教练
        # if TraineeCoach.objects.filter(user__unionid=unionid).exists():
        if Coach.objects.filter(user__unionid=unionid, deleted=False).exists():
            coach = Coach.objects.filter(user__unionid=unionid, deleted=False).first()
            return success_response({'user_type': '个人教练', 'name': coach.user.cover_name,
                                     'company': '', 'project': '', 'company_id': '', 'project_id': '',
                                     'project_member_id': ''})
        # 三无学员
        elif TraineeCoachInviteUser.objects.filter(user__unionid=unionid).exists():
            trainee_coach_inviteuser = TraineeCoachInviteUser.objects.filter(user__unionid=unionid).first()
            return success_response({'user_type': '见习学员', 'name': trainee_coach_inviteuser.user.cover_name,
                                     'company': '', 'project': '', 'company_id': '', 'project_id': '',
                                     'project_member_id': ''})

        # 被教练者
        elif ProjectMember.objects.filter(user__unionid=unionid, role=ProjectMemberRoleEnum.coachee.value, deleted=False).exists():
            project_member = ProjectMember.objects.filter(user__unionid=unionid, deleted=False,
                                                          role=ProjectMemberRoleEnum.coachee.value).first()
            return success_response({'user_type': '项目客户', 'name': project_member.user.cover_name,
                                     'company': project_member.project.company.real_name,
                                     'project': project_member.project.name,
                                     'company_id': project_member.project.company.pk,
                                     'project_id': project_member.project.pk,
                                     'project_member_id': project_member.pk})
        # 签约教练
        elif Coach.objects.filter(user__unionid=unionid).exists():
            coach = Coach.objects.filter(user__unionid=unionid).first()
            return success_response({'user_type': '签约教练', 'name': coach.user.cover_name,
                                     'company': '',
                                     'project': '', 'company_id': '', 'project_id': '',
                                     'project_member_id': ''})
        else:
            return success_response()

    @swagger_auto_schema(
        operation_id='企业微信登录',
        operation_summary='企业微信登录',
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'user_id': openapi.Schema(type=openapi.TYPE_STRING, description='企业微信成员用户ID'),
            }
        ),
        tags=['企业微信相关']
    )
    @action(methods=['post'], detail=False, url_path='work_wechat_login', authentication_classes=[])
    def work_wechat_login(self, request, *args, **kwargs):
        try:
            user_id = request.data['user_id']
        except:
            return parameter_error_response()
        work_wechat_user = WorkWechatUser.objects.filter(wx_user_id=user_id, deleted=False).first()
        if not work_wechat_user:
            return parameter_error_response('您未绑定群智账号，请先绑定后登录')
        if not work_wechat_user.user_id:
            return parameter_error_response('未查询到当前用户')
        user = work_wechat_user.user
        token = get_token(user, is_backend=True)
        user_data = UserSerializer(user).data

        # 企业微信拦截被教练者登录
        user_role = user_public.del_specified_user_permissions(
            user_data.get('role'), [UserRoleEnum.trainee_coachee, UserRoleEnum.coachee])
        user_data['role'] = user_role

        user.last_active = datetime.datetime.now()
        user.save()

        # 如果没有绑定二维码，异步更新
        if not work_wechat_user.qr_code:
            task.update_work_wechat_user_info.delay(user_id)

        return success_response({'token': token, 'user': user_data})


    @swagger_auto_schema(
        operation_id='获取企业微信JSSDK配置',
        operation_summary='获取企业微信JSSDK配置',
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'external_user_id': openapi.Schema(type=openapi.TYPE_STRING, description='企业微信客户（外部联系人）ID'),
            }
        ),
        tags=['企业微信相关']
    )
    @action(methods=['post'], detail=False, url_path='get_agent_config', authentication_classes=[])
    def get_agent_config(self, request, *args, **kwargs):
        try:
            agentid = request.data['agentid']
            url = request.data['url']
        except:
            return parameter_error_response()

        state, response = WorkWechat().get_agent_config(agentid=agentid)
        if not state:
            return parameter_error_response(response)

        corp_state, corp_response = WorkWechat().get_corp_config()
        if not corp_state:
            return parameter_error_response(corp_response)
        
        timestamp = int(time.time())
        # jsapi_ticket=LpTl7WSgID1m7UF736oFAA==&noncestr=123&timestamp=1705921397&url=https://assistant.qzcoach.com
        signature_text = f'jsapi_ticket={response["ticket"]}&noncestr=123&timestamp={timestamp}&url={url}'
        sha1_hash = hashlib.sha1()
        # 更新哈希对象以包含要进行哈希的数据
        sha1_hash.update(signature_text.encode('utf-8'))
        # 获取十六进制表示的SHA1哈希值
        signature = sha1_hash.hexdigest()

        corp_signature_text = f'jsapi_ticket={corp_response["ticket"]}&noncestr=123&timestamp={timestamp}&url={url}'
        corp_sha1_hash = hashlib.sha1()
        # 更新哈希对象以包含要进行哈希的数据
        corp_sha1_hash.update(corp_signature_text.encode('utf-8'))
        # 获取十六进制表示的SHA1哈希值
        corp_signature = corp_sha1_hash.hexdigest()


        data = {
            'corpid': settings.WORK_WECHAT_CORPID,
            'agentid': agentid,
            'timestamp': timestamp,
            'nonceStr': '123',
            'signature': signature,
            'corp_signature': corp_signature,
        }

        return success_response(data)