import pendulum
import requests
from drf_yasg import openapi
from rest_framework import viewsets
from drf_yasg.utils import swagger_auto_schema
from rest_framework.decorators import action

from utils import aliyun, randomPassword
from utils.authentication import get_token
from utils.wechat_oauth import WeChatOauth
from wisdom_v2.models import User, ProjectInterview, TraineeCoachInviteUser, Coach
from utils.api_response import success_response, parameter_error_response
from wisdom_v2.views.project_interview_action import ProjectInterviewListSerializer
from wisdom_v2.views.user_actions import UserSerializer


class AppTraineeCoacheeViewSet(viewsets.ModelViewSet):
    queryset = User.objects.filter(deleted=False)
    serializer_class = UserSerializer

    @swagger_auto_schema(
        operation_id='见习学员手机号授权登录',
        operation_summary='见习学员手机号授权登录',
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'code': openapi.Schema(type=openapi.TYPE_STRING, description='请求用户登录的code'),
                'phone_code': openapi.Schema(type=openapi.TYPE_STRING, description='请求用户手机号的code'),
                'interview_id': openapi.Schema(type=openapi.TYPE_STRING, description='链接对应的辅导id'),
            }),
        tags=['移动端见习学员相关']
    )
    @action(methods=['post'], detail=False, url_path='login', authentication_classes=[])
    def app_trainee_coachee_login(self, request, *args, **kwargs):
        code = request.data.get('code')
        phone_code = request.data.get('phone_code')
        interview_id = request.data.get('interview_id')

        # 获取用户基础信息
        response = WeChatOauth().jscode2session(code)
        if response.get('err'):
            return success_response(response.get('err'))
        unionid = response.get('unionid')
        open_id = response.get('openid')

        # 获取用户手机号
        phone_response = WeChatOauth().get_phone_number(phone_code)
        if phone_response.get('err'):
            return success_response(phone_response.get('err'))
        phone = phone_response.get('phone_info').get('phoneNumber')

        # 判断用户是否已存在
        user = self.get_queryset().filter(phone=phone).first()
        interview = ProjectInterview.objects.filter(pk=interview_id, deleted=False).first()
        if not user:
            if interview.public_attr.target_user.unionid:
                return success_response({"is_invite": 1})
            else:
                user = interview.public_attr.target_user
            user.name = phone
        try:
            user.phone = phone
            user.unionid = unionid
            user.openid = open_id
            user.save()
        except Exception:
            return parameter_error_response('修改用户数据失败')

        coach = Coach.objects.filter(user_id=user.id, deleted=False).first()
        # coach.user_coach为项目教练外键查询
        if coach and not coach.user_coach.filter(deleted=False).first():
            return parameter_error_response('您账号暂未获取到登录权限，请先获取后登录')
        token = get_token(user, is_backend=True)
        return success_response({'token': token, 'user': UserSerializer(user).data})

    @swagger_auto_schema(
        operation_id='见习学员微信授权数据更新',
        operation_summary='见习学员微信授权数据更新',
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'name': openapi.Schema(type=openapi.TYPE_STRING, description='微信昵称'),
                'image_url': openapi.Schema(type=openapi.TYPE_STRING, description='微信头像'),
            }),
        tags=['移动端见习学员相关']
    )
    def update(self, request, *args, **kwargs):
        user = self.get_object()
        name = request.data.get('name')

        # 判断是否是系统自动生成的用户名
        if str(user.true_name)[:2] == '用户' and len(str(user.true_name)) == 6:
            user.true_name = name
        if not user.head_image_url:
            image_url = request.data.get('image_url')
            raw_image_data = requests.get(image_url)

            # 存入阿里云指定文件
            image_name = 'user/{}/{}.jpg'.format(
                pendulum.now().format('YYYY/MM/DD/HH/mm'),
                randomPassword(length=5)
            )
            aliyun.AliYun('cwcoach').send_file(
                image_name, raw_image_data.content, headers={'Content-Type': 'image/jpeg'})
            url = aliyun.AliYun('cwcoach').oss_url(image_name, headers={'Content-Type': 'image/jpeg'})
            user.head_image_url = str(url).split('?')[0]
        user.save()
        serializer = self.get_serializer(user)
        return success_response(serializer.data)

    @swagger_auto_schema(
        operation_id='见习学员辅导记录绑定',
        operation_summary='见习学员辅导记录绑定',
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'interview_id': openapi.Schema(type=openapi.TYPE_STRING, description='链接对应的辅导id'),
            }),
        tags=['移动端见习学员相关']
    )
    @action(methods=['post'], detail=True, url_path='interview')
    def app_trainee_coachee_interview(self, request, *args, **kwargs):
        user = self.get_object()

        interview_id = request.data.get('interview_id')
        self.serializer_class = ProjectInterviewListSerializer
        interview = ProjectInterview.objects.filter(pk=interview_id, deleted=False).first()
        serializer = self.get_serializer(interview)

        if not user.unionid:
            return parameter_error_response('未同步微信登录信息')

        if interview.public_attr.target_user.unionid:
            if interview.public_attr.target_user != user:
                return success_response({"is_invite": 1})
        else:
            old_user = interview.public_attr.target_user
            User.objects.filter(pk=old_user.id).delete()
            public_attr = interview.public_attr
            public_attr.target_user = user
            public_attr.save()
            TraineeCoachInviteUser.objects.filter(
                interview=interview,
            ).update(user=user)
        return success_response(serializer.data)
