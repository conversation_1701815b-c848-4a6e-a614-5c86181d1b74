from django.db import transaction
from drf_yasg import openapi
from rest_framework.decorators import action
from drf_yasg.utils import swagger_auto_schema

from wisdom_v2.common import user_public, action_plan_public
from wisdom_v2.enum.user_enum import UserRoleEnum
from wisdom_v2.models import Article, User, PublicAttr, Habit, Diary, ActionPlan, ArticleUserAction, Project
from rest_framework import viewsets

from utils.api_response import success_response, parameter_error_response
from wisdom_v2.views.article_actions import ArticleSerializers
from wisdom_v2.views.constant import ATTR_TYPE_HABIT, ROLE_COACHEE, ATTR_TYPE_DIARY, DIARY_FROM_SELF, ROLE_COACH, \
    ATTR_TYPE_ACTIONPLAN


class AppArticleViewSet(viewsets.ModelViewSet):
    queryset = Article.objects.filter(deleted=False).order_by('-top_at', '-created_at')
    serializer_class = ArticleSerializers

    @swagger_auto_schema(
        operation_id='创建文章对应的用户行为',
        operation_summary='创建文章对应的用户行为',
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'user_id': openapi.Schema(type=openapi.TYPE_NUMBER, description='用户id'),
                'project_id': openapi.Schema(type=openapi.TYPE_NUMBER, description='项目id'),
                'user_role': openapi.Schema(type=openapi.TYPE_NUMBER, description='用户身份:1/3教练，2/4为被教练者'),
                'action_plan': openapi.Schema(type=openapi.TYPE_ARRAY, items=openapi.Schema(
                    type=openapi.TYPE_OBJECT, description='行动计划', properties={
                        'id': openapi.Schema(type=openapi.TYPE_NUMBER, description='行动计划id(可选)'),
                        'content': openapi.Schema(type=openapi.TYPE_STRING, description='内容'),
                        'end_date': openapi.Schema(type=openapi.TYPE_STRING, description='结束日期',
                                                   format=openapi.FORMAT_DATE),
                    })),
                'habit': openapi.Schema(type=openapi.TYPE_ARRAY, items=openapi.Schema(
                    type=openapi.TYPE_OBJECT, description='习惯养成', properties={
                        'id': openapi.Schema(type=openapi.TYPE_NUMBER, description='习惯养成id(可选)'),
                        'stop': openapi.Schema(type=openapi.TYPE_STRING, description='当...'),
                        'when': openapi.Schema(type=openapi.TYPE_STRING, description='停止...'),
                        'change': openapi.Schema(type=openapi.TYPE_STRING, description='转变...'),
                        'start_date': openapi.Schema(type=openapi.TYPE_STRING, description='开始日期',
                                                     format=openapi.FORMAT_DATE),
                        'end_date': openapi.Schema(type=openapi.FORMAT_DATE, description='结束日期',
                                                   format=openapi.FORMAT_DATE),
                    })),
                'diary': openapi.Schema(
                    type=openapi.TYPE_OBJECT, description='成长笔记', properties={
                        'id': openapi.Schema(type=openapi.TYPE_NUMBER, description='成长笔记id'),
                        'content': openapi.Schema(type=openapi.TYPE_STRING, description='内容'),
                    }),
            }
        ),
        tags=['文章相关']
    )
    @action(methods=['post'], detail=True, url_path='action')
    def article_action_create(self, request, *args, **kwargs):

        try:
            article = self.get_object()
            # 获取当前用户数据
            user = User.objects.filter(pk=request.data.get('user_id')).first()
            if not user:
                return parameter_error_response('用户不存在')
            user_role = int(request.data.get('user_role', 0))
            if not user_role or user_role not in UserRoleEnum.get_describe_keys():
                return parameter_error_response('未知的用户权限')

            # 读取用户项目
            project_member = user_public.get_user_to_project_member_all(user.id)
            project_id = project_member[0].project_id if project_member else None

            creator_role = ROLE_COACH if user_role in [UserRoleEnum.coach, UserRoleEnum.trainee_coach] else ROLE_COACHEE
            raw_diary = request.data.get('diary')
            raw_habit = request.data.get('habit')
            raw_action_plan = request.data.get('action_plan')
        except Project.DoesNotExist:
            return parameter_error_response()
        except Exception as e:
            return parameter_error_response(str(e))

        # 增加事务控制
        try:
            with transaction.atomic():
                if raw_habit:

                    # article_user_public_attr是ArticleUserAction表的public_attr反向查询字段
                    # 确认是同一文章下的数据
                    habits = list(Habit.objects.filter(
                        public_attr__article_user_public_attr__article=article,
                        public_attr__user=user,
                        deleted=False
                    ).values_list('id', flat=True))
                    for item in raw_habit:
                        if item.get('id'):
                            habit = Habit.objects.get(pk=item.get('id'))
                            habit.when = item.get('when')
                            habit.stop = item.get('stop')
                            habit.change = item.get('change')
                            habit.start_date = item.get('start_date')
                            habit.end_date = item.get('end_date')
                            habit.save()
                            habits.remove(habit.id)
                        else:
                            public_attr = PublicAttr.objects.create(
                                user=user, type=ATTR_TYPE_HABIT, project_id=project_id)
                            Habit.objects.create(
                                public_attr=public_attr,
                                when=item.get('when'),
                                stop=item.get('stop'),
                                change=item.get('change'),
                                start_date=item.get('start_date'),
                                end_date=item.get('end_date'),
                                creator_role=creator_role)
                            ArticleUserAction.objects.create(public_attr=public_attr, article=article)
                    Habit.objects.filter(pk__in=habits).update(deleted=True)

                #  目前行动计划只对应一个，不做列表处理
                if raw_diary:
                    # diarys = list(Diary.objects.filter(
                    #     public_attr__article_user_public_attr__article=article,
                    #     public_attr__user=user,
                    #     deleted=False
                    # ).values_list('id', flat=True))
                    # for item in raw_diary:

                    content = raw_diary.get('content')
                    if raw_diary.get('id'):
                        diary = Diary.objects.filter(pk=raw_diary.get('id')).first()
                        diary.content = content
                        diary.save()
                        # diarys.remove(diary.id)
                    else:
                        Diary.objects.filter(
                                public_attr__article_user_public_attr__article=article,
                                public_attr__user=user,
                                deleted=False
                            ).update(deleted=True)
                        public_attr = PublicAttr.objects.create(
                            type=ATTR_TYPE_DIARY, user=user, project_id=project_id)
                        Diary.objects.create(
                            content=content, type=DIARY_FROM_SELF, creator_role=creator_role,
                            public_attr=public_attr)
                        ArticleUserAction.objects.create(public_attr=public_attr, article=article)
                    # Diary.objects.filter(pk__in=diarys).update(deleted=True)
                if raw_action_plan:
                    action_plans = list(ActionPlan.objects.filter(
                        public_attr__article_user_public_attr__article=article,
                        public_attr__user=user,
                        deleted=False
                    ).values_list('id', flat=True))
                    for item in raw_action_plan:
                        start_date, error = action_plan_public.get_default_start_date(item)
                        if error:
                            return parameter_error_response(error)
                        if item.get('id'):
                            action_plan = ActionPlan.objects.get(pk=item.get('id'))
                            action_plan.content = item.get('content')
                            action_plan.end_date = item.get('end_date')
                            action_plan.start_date = start_date
                            action_plan.save()
                            action_plans.remove(action_plan.id)
                        else:
                            public_attr = PublicAttr.objects.create(
                                user=user, type=ATTR_TYPE_ACTIONPLAN, project_id=project_id)
                            ActionPlan.objects.create(
                                content=item.get('content'), end_date=item.get('end_date'), creator_role=creator_role,
                                public_attr=public_attr, start_date=start_date)
                            ArticleUserAction.objects.create(public_attr=public_attr, article=article)
                    ActionPlan.objects.filter(pk__in=action_plans).update(deleted=True)
        except Exception as e:
            return parameter_error_response(str(e))
        serializer = self.serializer_class(article, context={"user": user})
        return success_response(serializer.data)
