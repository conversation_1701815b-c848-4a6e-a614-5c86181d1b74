import json

from django.db.models import Q
from drf_yasg import openapi
from drf_yasg.utils import swagger_auto_schema
from rest_framework import viewsets, serializers

from utils.api_response import success_response, parameter_error_response
from utils.miniapp_version_judge import compare_version
from utils.pagination import StandardResultsSetPagination
from wisdom_v2.enum.user_enum import Coach<PERSON><PERSON><PERSON>ype<PERSON><PERSON>, PosterTypeEnum
from wisdom_v2.models import Coach
from wisdom_v2.models_file import Poster


class AppPosterDetailSerializer(serializers.ModelSerializer):

    id = serializers.IntegerField(source='auto_id', help_text='id')
    """
    海报序列化
    """
    class Meta:
        model = Poster
        fields = ('id', 'name')


class AppPosterViewSet(viewsets.ModelViewSet):
    queryset = Poster.objects.filter(deleted=False).order_by('-created_at')
    serializer_class = AppPosterDetailSerializer

    @swagger_auto_schema(
        operation_id='海报列表',
        operation_summary='海报列表',
        manual_parameters=[
            openapi.Parameter('page', openapi.IN_QUERY, description='第几页', type=openapi.TYPE_NUMBER),
            openapi.Parameter('page_size', openapi.IN_QUERY, description='每页显示条数默认15条', type=openapi.TYPE_NUMBER),
        ],
        tags=['海报相关']
    )
    def list(self, request, *args, **kwargs):
        mp = json.loads(request.headers.get('mp')) if request.headers.get('mp') else None

        # 分页
        paginator = StandardResultsSetPagination()

        queryset = self.get_queryset()
        user = request.user
        coach = Coach.objects.filter(user_id=user.id, deleted=False).first()
        if not coach:
            return parameter_error_response('未获取到教练信息')

        if mp and compare_version(mp.get('version'), '2.30') < 0:
            # 如果是旧版本，只查找type为null的数据
            queryset = queryset.filter(type__isnull=True)
        else:
            # 针对不同的教练类型构建不同的查询
            if coach.coach_type == CoachUserTypeEnum.student.value:
                queryset = queryset.filter(
                    Q(type__isnull=True) | Q(type=PosterTypeEnum.internship_project.value)
                )
            else:
                # 其他类型的教练只查找coach_type为null的数据
                queryset = queryset.filter(type__isnull=True)

        if mp and compare_version(mp.get('version'), '2.31.3') < 0:
            # 如果是低于2.31.1旧版本，只查找auto_id小于5的数据
            queryset = queryset.filter(auto_id__lte=5)

        page_list = paginator.paginate_queryset(queryset, self.request)
        serializer = self.get_serializer(page_list, many=True)
        response = paginator.get_paginated_response(serializer.data)
        return success_response(response)
