from rest_framework import serializers
from django.db import transaction

from co_wisdom_server.base import NOT_SELECT_IMAGE_URL
from wisdom_v2.enum.project_interview_enum import DataType
from wisdom_v2.models import ProjectDocs


class AppProjectDocsListListSerializer(serializers.ModelSerializer):
    """
        小程序项目文件列表序列化
    """
    id = serializers.IntegerField(help_text='id')
    file_path = serializers.CharField(source='file.file_path', help_text='文件路径')
    title = serializers.SerializerMethodField(help_text='报告名称')
    data_type = serializers.SerializerMethodField(help_text='数据类型')
    created_at = serializers.SerializerMethodField(help_text='创建时间')

    class Meta:
        model = ProjectDocs
        fields = ['id', 'title', 'created_at', 'data_type', 'file_path']

    def get_title(self, obj):
        return str(obj.file.file_name).split('.')[0]

    def get_data_type(self, obj):
        return DataType.project_docs.value

    def get_created_at(self, obj):
        return obj.file.created_at.date()


class AppProjectDocsListDetailsSerializer(serializers.ModelSerializer):
    """
        小程序项目文档详情序列化
    """
    title = serializers.SerializerMethodField(help_text='报告名称')
    describe = serializers.SerializerMethodField(help_text='描述')
    image_url = serializers.SerializerMethodField(help_text='模板图片')
    details = serializers.SerializerMethodField(help_text='详情')
    data_type = serializers.SerializerMethodField(help_text='数据类型')

    class Meta:
        model = ProjectDocs
        fields = ['data_type', 'describe', 'image_url', 'details', 'title']

    def get_title(self, obj):
        return str(obj.file.file_name).split('.')[0]

    def get_data_type(self, obj):
        return DataType.project_docs.value

    def get_image_url(self, obj):
        return NOT_SELECT_IMAGE_URL

    def get_describe(self, obj):
        return f'上传时间：{obj.file.created_at.strftime("%Y-%m-%d")}'

    def get_details(self, obj):

        project_member_name = None
        if obj.project_member:
            project_member_name = obj.project_member.user.name
        elif obj.project_interested:
            project_member_name = obj.project_interested.master.name
        stakeholder_name = None
        if obj.project_interested:
            stakeholder_name = obj.project_interested.interested.cover_name
        data = {
            "coachee_name": project_member_name,
            "stakeholder_name": stakeholder_name,
            "id": obj.id,
            "file_path": obj.file.file_path,
            }
        return data