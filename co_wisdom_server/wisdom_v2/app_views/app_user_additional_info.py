import json

from drf_yasg import openapi
from rest_framework import viewsets
from drf_yasg.utils import swagger_auto_schema
from rest_framework.decorators import action

from utils import user_data_collection_constant
from utils.user_data_collection_constant import lbi_add_user_data_sub
from wisdom_v2.common import user_public, project_public
from wisdom_v2.enum.message_type_enum import LarkMessageTypeEnum
from wisdom_v2.models import User, EvaluationModule
from utils.api_response import success_response, parameter_error_response

from rest_framework import serializers
from utils import task
from wisdom_v2.models import UserAdditionalInfo
from wisdom_v2.views.constant import LBI_EVALUATION


class UserAdditionalInfoViewSerializers(serializers.ModelSerializer):
    class Meta:
        model = UserAdditionalInfo
        exclude = ('updated_at', 'created_at')


class UserAdditionalInfoViewSet(viewsets.ModelViewSet):
    queryset = UserAdditionalInfo.objects
    serializer_class = UserAdditionalInfoViewSerializers

    @swagger_auto_schema(
        operation_id='查看开屏用户信息收集题目',
        operation_summary='查看开屏幕用户信息收集题目',
        manual_parameters=[],
        tags=['开屏用户信息收集']
    )
    @action(methods=['get'], detail=False, url_path='topic')
    def get_user_data_collection_topic(self, request, *args, **kwargs):
        mp = json.loads(request.headers.get('mp')) if request.headers.get('mp') else None

        user = request.user

        is_lbi_user = EvaluationModule.objects.filter(
            project_bundle__project_member__user_id=user.id,
            evaluation__code=LBI_EVALUATION,
            deleted=False
        ).exists()

        is_user_info = UserAdditionalInfo.objects.filter(
            user_id=user.id
        ).exists()

        return success_response(user_data_collection_constant.get_project_user_data_collection_topic(
            is_lbi_user, is_user_info, mp))

    @swagger_auto_schema(
        operation_id='创建（更新）开屏用户信息收集',
        operation_summary='创建（更新）开屏用户信息收集',
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'user_id': openapi.Schema(type=openapi.TYPE_STRING, description='用户id'),
                "user_data": openapi.Schema(
                    type=openapi.TYPE_OBJECT, description='用户数据', properties={
                        'sub': openapi.Schema(type=openapi.TYPE_STRING, description='问题序号'),
                        'count': openapi.Schema(type=openapi.TYPE_STRING, description='选项描述'),
                    })
            },
            tags=['开屏用户信息收集'],
        ))
    def create(self, request, *args, **kwargs):
        try:
            user_id = request.data['user_id']
            user = User.objects.get(id=user_id)
            user_data = request.data['user_data']
        except (Exception, User.DoesNotExist) as e:
            return parameter_error_response(str(e))

        data = {'user_id': user.id}

        # 循环参数，获取数据对应下标的字段
        all_sub = []
        for item in user_data:
            sub = str(item.get('sub'))
            if lbi_add_user_data_sub.get(sub):
                # lbi_add_user_data_sub：问题对应的字段名列表
                data[lbi_add_user_data_sub.get(sub)] = item.get('count')
            all_sub.append(sub)

        # 用户是否提交过开屏信息
        user_info = UserAdditionalInfo.objects.filter(user_id=user.id)

        # 已经提交过开屏信息，则更新。
        if user_info.exists():
            # 已有数据进行追加操作，all_info是列表
            user_info_obj = user_info.first()
            if user_info_obj.all_info:
                data['all_info'] = user_info_obj.all_info + user_data
            else:
                data['all_info'] = user_data
            user_info.update(**data)
        else:
            data['all_info'] = user_data
            # 没有提交过则新建
            user_info = UserAdditionalInfo.objects.create(**data)

        # 更新项目标签
        task.update_project_member_tag_task.delay(user_id)

        # 数据返回
        serializer = self.get_serializer(user_info)
        return success_response(serializer.data)