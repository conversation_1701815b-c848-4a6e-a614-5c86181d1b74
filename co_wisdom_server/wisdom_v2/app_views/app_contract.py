from drf_yasg import openapi
from rest_framework import viewsets
from drf_yasg.utils import swagger_auto_schema
from rest_framework.decorators import action

from static.contract_text import contract_text
from wisdom_v2.models import UserContract
from utils.api_response import success_response, parameter_error_response
from wisdom_v2.views.contract import ContractViewSetSerializers


class AppContractViewSet(viewsets.ModelViewSet):
    queryset = UserContract.objects.filter(deleted=False)
    serializer_class = ContractViewSetSerializers

    @swagger_auto_schema(
        operation_id='用户合同查询',
        operation_summary='用户合同查询',
        manual_parameters=[
            openapi.Parameter('user_id', openapi.IN_QUERY, description='用户标识', type=openapi.TYPE_STRING),
        ],
        tags=['合同相关']
    )
    @action(methods=['get'], detail=False, url_path='detail')
    def get_contract_detail(self, request, *args, **kwargs):
        try:
            user_id = request.query_params.get('user_id')
            user_contract = self.queryset.get(user_id=user_id)
        except UserContract.DoesNotExist:
            return success_response({}, business={'contract_text': contract_text})
        except Exception as e:
            return parameter_error_response(str(e))
        serializer = self.get_serializer(user_contract)
        return success_response(serializer.data, business={'contract_text': contract_text})

    @swagger_auto_schema(
        operation_id='创建用户合同签约记录',
        operation_summary='创建用户合同签约记录',
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'user_id': openapi.Schema(type=openapi.TYPE_NUMBER, description='用户标识'),
                'sign_url': openapi.Schema(type=openapi.TYPE_STRING, description='签约合同链接'),
            }
        ),
        tags=['合同相关']
    )
    def create(self, request, *args, **kwargs):
        return success_response(super(AppContractViewSet, self).create(request, *args, **kwargs).data)
