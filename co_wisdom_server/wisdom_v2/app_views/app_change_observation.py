import pendulum
from drf_yasg import openapi
from rest_framework import viewsets
from drf_yasg.utils import swagger_auto_schema
from rest_framework.decorators import action
from django.db import transaction

from utils.api_response import parameter_error_response, success_response
from utils.pagination import StandardResultsSetPagination
from wisdom_v2.app_views.app_change_observation_action import ChangeObservationSerializer, \
    AppChangeObservationInfoSerializer
from wisdom_v2.common import change_observation_public
from wisdom_v2.enum.message_type_enum import LarkMessageTypeEnum
from wisdom_v2.models import ProjectInterested, ChangeObservation, User, MultipleAssociationRelation, \
    ChangeObservationAnswer, PublicAttr, PersonalReport
from wisdom_v2.enum.service_content_enum import MultipleAssociationRelationTypeEnum, PersonalReportTypeEnum
from wisdom_v2.views.constant import ATTR_TYPE_CHANGE_OBSERVATION
from utils.task import generate_change_observation_report, send_lark_business_message


class ChangeObservationViewSet(viewsets.ModelViewSet):
    queryset = ChangeObservation.objects.filter(deleted=False)
    serializer_class = ChangeObservationSerializer

    @swagger_auto_schema(
        operation_id='小程序改变观察反馈详情',
        operation_summary='小程序改变观察反馈详情',
        manual_parameters=[
            openapi.Parameter(
                'change_observation_id', openapi.IN_QUERY, description='被教练者id', type=openapi.TYPE_NUMBER, required=True),
        ],
        tags=['小程序改变观察反馈相关']
    )
    @action(methods=['get'], detail=False, url_path='detail', authentication_classes=[])
    def change_observation_detail(self, request, *args, **kwargs):
        try:
            change_observation_id = request.query_params.get('change_observation_id')
            change_observation = ChangeObservation.objects.get(pk=change_observation_id)
        except ChangeObservation.DoesNotExist:
            return parameter_error_response('未获取到改变观察信息')
        serializer = self.get_serializer(change_observation)
        return success_response(serializer.data)

    @swagger_auto_schema(
        operation_id='利益相关者填写改变观察反馈',
        operation_summary='利益相关者填写改变观察反馈',
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'remind_type': openapi.Schema(type=openapi.TYPE_NUMBER, description='提醒方式')
            }
        ),
        tags=['小程序改变观察反馈相关']
    )
    @action(methods=['post'], detail=False, url_path='write', authentication_classes=[])
    def write_change_observation(self, request, *args, **kwargs):
        try:
            change_observation_id = request.data.get('id')
            growth_goals = request.data['growth_goals']
            change_choice = request.data['change_choice']
            user_id = request.data.get('user_id')
            change_observation = ChangeObservation.objects.get(pk=change_observation_id)
            user = User.objects.get(pk=user_id)
        except ChangeObservation.DoesNotExist:
            return parameter_error_response()
        except Exception:
            return parameter_error_response()
        if change_observation.deleted:
            return parameter_error_response('当前改变观察已移除')
        project_interested_ids = list(MultipleAssociationRelation.objects.filter(
            main_id=change_observation_id, deleted=False,
            type=MultipleAssociationRelationTypeEnum.stakeholder_change_observation).values_list('secondary_id',
                                                                                                 flat=True))
        user_ids = ProjectInterested.objects.filter(id__in=project_interested_ids).values_list('interested_id',
                                                                                               flat=True)
        if user.pk not in user_ids:
            return parameter_error_response(f'您不是{change_observation.project_member.user.cover_name}的利益相关者')

        if ChangeObservationAnswer.objects.filter(change_observation=change_observation,
                                                  public_attr__user=user).exists():
            return parameter_error_response('您当前已填写,请勿重复提交')

        with transaction.atomic():
            data = {'growth_goals': growth_goals, 'change_choice': change_choice}
            public_attr = PublicAttr.objects.create(user=user, target_user=change_observation.project_member.user,
                                                    type=ATTR_TYPE_CHANGE_OBSERVATION)
            ChangeObservationAnswer.objects.create(change_observation=change_observation, public_attr=public_attr,
                                                   content=data)
        # 如果达到需求人数则生成报告
        if ChangeObservationAnswer.objects.filter(
                change_observation=change_observation).count() == change_observation.max_stakeholders_count:
            # 生成报告
            generate_change_observation_report.delay(change_observation.pk)
        elif change_observation.stakeholders_write_end_date:
            # 如果超过了截止时间，并且已经有报告生成,就去更新报告：
            if (pendulum.today().date() > change_observation.stakeholders_write_end_date and
                    PersonalReport.objects.filter(
                        type=PersonalReportTypeEnum.change_observation_report.value,
                        object_id=str(change_observation.pk),
                        user=change_observation.project_member.user, deleted=False).exists()):
                # 更新报告
                generate_change_observation_report.delay(change_observation.pk)

        return success_response()


    @swagger_auto_schema(
        operation_id='小程序改变观察反馈配置详情',
        operation_summary='小程序改变观察反馈配置详情',
        manual_parameters=[
            openapi.Parameter(
                'change_observation_id', openapi.IN_QUERY, description='被教练者id', type=openapi.TYPE_NUMBER, required=True),
        ],
        tags=['小程序改变观察反馈相关']
    )
    @action(methods=['get'], detail=False, url_path='info')
    def get_change_observation_info(self, request, *args, **kwargs):
        try:
            change_observation_id = request.query_params.get('change_observation_id')
            change_observation = ChangeObservation.objects.get(pk=change_observation_id)
        except:
            return parameter_error_response('未获取到改变观察信息')
        serializer = AppChangeObservationInfoSerializer(change_observation)
        return success_response(serializer.data)

    @swagger_auto_schema(
        operation_id='改变观察反馈修改利益相关者',
        operation_summary='改变观察反馈修改利益相关者',
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'id': openapi.Schema(type=openapi.TYPE_NUMBER, description='改变观察反馈id'),
                'stakeholder': openapi.Schema(type=openapi.TYPE_NUMBER, description='选中的利益相关者id列表')
            }
        ),
        tags=['小程序改变观察反馈相关']
    )
    @action(methods=['post'], detail=False, url_path='update')
    def update_change_observation_stakeholder(self, request, *args, **kwargs):
        try:
            stakeholder = request.data.get('stakeholder')
            change_observation_id = request.data.get('id')
            change_observation = ChangeObservation.objects.get(pk=change_observation_id)
        except ChangeObservation.DoesNotExist:
            return parameter_error_response()

        if change_observation.max_stakeholders_count > len(stakeholder):
            return parameter_error_response(f"本次改变观察反馈需要邀请{change_observation.max_stakeholders_count}位同事，"
                                        f"您还要再选择{change_observation.max_stakeholders_count - len(stakeholder)}位参与同事")

        if change_observation.max_stakeholders_count < len(stakeholder):
            return parameter_error_response(f"本次访谈仅需要邀请{change_observation.max_stakeholders_count}位同事参与")
        err, deleted_list, add_list = change_observation_public.check_change_observation_update_stakeholder(stakeholder, change_observation)
        if err:
            return parameter_error_response(err)
        with transaction.atomic():
            change_observation_public.update_change_observation_stakeholders(add_list, deleted_list, change_observation)

        params = {'project_name': f'{change_observation.project_member.project.company.real_name}-{change_observation.project_member.project.name}',
                  'user_name': change_observation.project_member.user.cover_name}
        send_lark_business_message.delay(LarkMessageTypeEnum.change_observation_personnel_selection.value, params)
        serializer = AppChangeObservationInfoSerializer(change_observation)
        return success_response(serializer.data)

    @swagger_auto_schema(
        operation_id='小程序客户发送邀请列表',
        operation_summary='小程序客户发送邀请列表',
        manual_parameters=[
            openapi.Parameter(
                'id', openapi.IN_QUERY, description='利益相关者访谈配置id',
                type=openapi.TYPE_NUMBER, required=True),
        ],
        tags=['小程序改变观察反馈相关']
    )
    @action(methods=['get'], detail=False, url_path='invite_stakeholders')
    def get_change_observation_invite_stakeholders(self, request, *args, **kwargs):
        try:
            change_observation_id = request.query_params.get('id')
            change_observation = ChangeObservation.objects.get(pk=change_observation_id)
        except ChangeObservation.DoesNotExist:
            return parameter_error_response()
        relation = MultipleAssociationRelation.objects.filter(
            main_id=str(change_observation.pk), type=MultipleAssociationRelationTypeEnum.stakeholder_change_observation.value,
            deleted=False)
        request.GET._mutable = True
        self.request.query_params['page_size'] = relation.count()
        # 分页
        paginator = StandardResultsSetPagination()
        page_list = paginator.paginate_queryset(relation, self.request)
        serializer, err = change_observation_public.get_invite_stakeholders(page_list)
        if err:
            return parameter_error_response(err)
        response = paginator.get_paginated_response(serializer)
        return success_response(response)

    @swagger_auto_schema(
        operation_id='利益相关者查看改变观察反馈详情',
        operation_summary='利益相关者查看改变观察反馈详情',
        manual_parameters=[
            openapi.Parameter(
                'stakeholder_relation_id', openapi.IN_QUERY, description='改变观察反馈利益相关者关联id',
                type=openapi.TYPE_NUMBER, required=True),
        ],
        tags=['小程序改变观察反馈相关']
    )
    @action(methods=['get'], detail=False, url_path='stakeholder_detail', authentication_classes=[])
    def get_change_observation_stakeholder_detail(self, request, *args, **kwargs):
        try:
            stakeholder_relation_id = request.query_params.get('stakeholder_relation_id')
            relation = MultipleAssociationRelation.objects.get(pk=stakeholder_relation_id)
            change_observation = ChangeObservation.objects.get(pk=relation.main_id, deleted=False)
            project_interested = ProjectInterested.objects.get(pk=relation.secondary_id, deleted=False)
        except (MultipleAssociationRelation.DoesNotExist, ProjectInterested.DoesNotExist, ChangeObservation.DoesNotExist):
            return parameter_error_response()

        if relation.deleted:
            status = 3  # 已失效
        elif ChangeObservationAnswer.objects.filter(
                change_observation=change_observation, public_attr__user_id=project_interested.interested_id).exists():
            status = 2  # 已填写
        else:
            status = 1  # 可填写
        data = {
            "id": change_observation.pk,
            "end_date": change_observation.stakeholders_write_end_date.strftime('%Y.%m.%d') if change_observation.stakeholders_write_end_date else '',
            "status": status,
            "stakeholder_user_id": project_interested.interested_id,
            "coachee_user_id": project_interested.master_id,
            "coachee_true_name": project_interested.master.cover_name
        }
        return success_response(data)


