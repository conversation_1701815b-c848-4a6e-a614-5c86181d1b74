import random

from drf_yasg import openapi
from rest_framework.decorators import action
from django.db import transaction
from rest_framework.viewsets import GenericViewSet
from drf_yasg.utils import swagger_auto_schema

from utils.messagecenter.getui import send_work_wechat_coach_notice
from wisdom_v2.app_views.app_coachee_action_plan import ActionPlanSerializer
from wisdom_v2.app_views.app_coachee_habit import HabitSerializer
from wisdom_v2.app_views.app_interview_offline_record import save_public_attr
from wisdom_v2.common import action_plan_public, interview_public
from wisdom_v2.models import ProjectInterview, ProjectInterviewRecord, ActionPlan, ProjectNote, Diary, \
    CapacityTag, Habit, LearnArticle, Article, WorkWechatUser
from wisdom_v2.app_views.app_interview_record_actions import AppInterviewRecordSerializers, AppInterviewRecordDetailSerializers
from utils.messagecenter.center import send_scoring_risk_alert_email
from utils.api_response import success_response, parameter_error_response
from wisdom_v2.views.constant import ATTR_TYPE_DIARY, DIARY_FROM_RECORD, R<PERSON>E_COACHEE, ATTR_TYPE_HABIT, \
    ATTR_TYPE_ACTIONPLAN, ROLE_COACH, ATTR_TYPE_LEARN, ATTR_TYPE_PROJECTNOTE, TAG_SELECT_TYPE_SINGLE, TAG_SOURCE_SYSTEM, \
    TAG_SOURCE_CUSTOM, TAG_SELECT_TYPE_MULTI
from utils.task import send_interview_view_message
from wisdom_v2.enum.user_enum import UserRoleEnum


def send_scoring_risk_alert_email_pre(harvest_score, satisfaction_score, interview_id, target_progress=None):

    describe = '投入度打{}分、满意度打{}分'.format(
        harvest_score,
        satisfaction_score,
    )
    if target_progress:
        if target_progress < 7 or harvest_score < 7 or satisfaction_score < 7:
            describe = '有效度打{}分、'.format(target_progress) + describe
            send_scoring_risk_alert_email.delay(
                describe=describe,
                interview_id=interview_id,
                message_type='scoring_risk_alert')
    else:
        if harvest_score < 7 or satisfaction_score < 7:
            send_scoring_risk_alert_email.delay(
                describe=describe,
                interview_id=interview_id,
                message_type='scoring_risk_alert')


# 添加接口，先用interview查找记录，如果有记录存在则直接覆盖属性
# 编辑接口，通过id查找记录，对于关联字段要处理增，删，改三种情况

class AppInterviewRecordViewSet(GenericViewSet):
    queryset = ProjectInterviewRecord.objects.all().order_by('-created_at')
    serializer_class = AppInterviewRecordSerializers
    # reflection change action_plan 需要支持批量添加
    @swagger_auto_schema(
        operation_id='app创建约谈记录信息',
        operation_summary='app创建约谈记录信息',
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'interview_id': openapi.Schema(type=openapi.TYPE_NUMBER, description='约谈id'),
                'topic': openapi.Schema(type=openapi.TYPE_STRING, description='约谈议题'),
                'coach_target': openapi.Schema(type=openapi.TYPE_STRING, description='教练辅导目标信息'),
                'leader_capacity': openapi.Schema(type=openapi.TYPE_ARRAY, description='教练填写领导力',
                                                    items=openapi.Schema(type=openapi.TYPE_OBJECT)),
                'observation': openapi.Schema(type=openapi.TYPE_STRING, description='教练填写观察'),
                'coach_change': openapi.Schema(type=openapi.TYPE_ARRAY, description='教练填写行为转变 A',
                                               items=openapi.Schema(type=openapi.TYPE_OBJECT)),
                'coach_action_plan': openapi.Schema(type=openapi.TYPE_ARRAY, description='教练填写行为任务 C',
                                                    items=openapi.Schema(type=openapi.TYPE_OBJECT)),
                'coach_leader_suggest': openapi.Schema(type=openapi.TYPE_ARRAY, description='教练填写学习能力项 B',
                                                    items=openapi.Schema(type=openapi.TYPE_OBJECT)),
                'note': openapi.Schema(type=openapi.TYPE_STRING, description='教练填写项目笔记'),
                'diary': openapi.Schema(type=openapi.TYPE_STRING, description='教练填写成长日记'),

                'discover': openapi.Schema(type=openapi.TYPE_STRING, description='被教填写发现'),
                'coachee_thought': openapi.Schema(type=openapi.TYPE_STRING, description='被教填写新的思考'),
                'coachee_change': openapi.Schema(type=openapi.TYPE_ARRAY, description='被教填写行为转变 A',
                                                 items=openapi.Schema(type=openapi.TYPE_OBJECT)),
                'action_plan':  openapi.Schema(type=openapi.TYPE_ARRAY, description='被教填写行为任务 C',
                                                 items=openapi.Schema(type=openapi.TYPE_OBJECT)),
                'target_progress': openapi.Schema(type=openapi.TYPE_NUMBER, description='被教填写发展目标评分'),
                'harvest_score': openapi.Schema(type=openapi.TYPE_NUMBER, description='被教填写投入度评分'),
                'satisfaction_score': openapi.Schema(type=openapi.TYPE_NUMBER, description='被教填写满意度评分'),
            }
        ),
        tags=['app约谈记录相关']
    )
    @action(methods=['post'], detail=False, url_path='add')
    def interview_record_add(self, request, *args, **kwargs):
        try:
            interview_id = int(request.data.get('interview_id', 0))
            topic = request.data.get('topic', None)

            # 教练填写
            leader_capacity = request.data.get('leader_capacity', [])
            observation = request.data.get('observation', None)
            coach_target = request.data.get('coach_target', None)
            note = request.data.get('note', None)
            diary = request.data.get('diary', None)
            coach_change = request.data.get('coach_change', [])
            coach_action_plan = request.data.get('coach_action_plan', [])
            coach_leader_suggest = request.data.get('coach_leader_suggest', [])

            # 被教填写
            coachee_change = request.data.get('coachee_change', [])
            action_plan = request.data.get('action_plan', [])
            discover = request.data.get('discover', None)
            coachee_thought = request.data.get('coachee_thought', None)
            target_progress = int(request.data.get('target_progress', 0))
            harvest_score = int(request.data.get('harvest_score', 0))
            satisfaction_score = int(request.data.get('satisfaction_score', 0))
            interview = ProjectInterview.objects.get(pk=interview_id)
            role = int(request.headers.get('role'))
        except Exception as e:
            print(1, e)
            return parameter_error_response(str(e))

        # user_role = get_user_role(request.user.pk, interview.public_attr.project_id)
        # if user_role not in [4, 6]:
        #     return parameter_error_response('当前用户身份错误')

        if (interview.coach_record_status and role in [1, 3]) or \
                (interview.coachee_record_status and role in [UserRoleEnum.coachee.value, UserRoleEnum.trainee_coachee.value]):
            return parameter_error_response('约谈记录已存在，请勿重复提交')

        try:
            with transaction.atomic():
                record = ProjectInterviewRecord.objects.filter(deleted=False, interview_id=interview_id).first()
                if record:
                    serializer = AppInterviewRecordSerializers(record, data=request.data, partial=True)
                    serializer.is_valid(raise_exception=True)
                    coach_record_obj = serializer.save()
                else:
                    save_serializer = AppInterviewRecordSerializers
                    serializer = save_serializer(data=request.data)
                    serializer.is_valid(raise_exception=True)
                    coach_record_obj = serializer.save()
                    coach_record_obj.interview = interview
                    coach_record_obj.save()

                # 教练修改是coach_target，客户修改议题是topic
                if topic:
                    interview.coachee_topic = topic
                    interview.topic = topic
                if coach_target:
                    interview.topic = coach_target
                    interview.coachee_topic = coach_target

                # 被教成长笔记 客户问题Q2-1
                if discover:
                    public_attr = save_public_attr(project=interview.public_attr.project,
                                                   user=interview.public_attr.target_user, save_type=ATTR_TYPE_DIARY)
                    discover_diary = Diary.objects.create(content=discover, type=DIARY_FROM_RECORD, public_attr=public_attr,
                                                          interview=interview, creator_role=ROLE_COACHEE)
                    coach_record_obj.discover = discover_diary
                # 被教成长笔记 客户问题Q2-2
                if coachee_thought:
                    public_attr = save_public_attr(project=interview.public_attr.project,
                                                   user=interview.public_attr.target_user, save_type=ATTR_TYPE_DIARY)
                    thought_diary = Diary.objects.create(content=coachee_thought, type=DIARY_FROM_RECORD, public_attr=public_attr,
                                                         interview=interview, creator_role=ROLE_COACHEE)
                    coach_record_obj.coachee_thought = thought_diary

                # 被教习惯养成 客户问题Q3-1/Q3-2
                if coachee_change:
                    for coachee_item in coachee_change:
                        public_attr = save_public_attr(project=interview.public_attr.project,
                                                       user=interview.public_attr.target_user,
                                                       save_type=ATTR_TYPE_HABIT)
                        Habit.objects.create(interview=interview, public_attr=public_attr, when=coachee_item['when'],
                                             stop=coachee_item['stop'],  change=coachee_item['change'],
                                             start_date=coachee_item['start_date'],
                                             end_date=coachee_item['end_date'],
                                             creator_role=ROLE_COACHEE)
                # 被教行动计划 客户问题Q4-1/Q4-2
                if action_plan:
                    for coachee_plan in action_plan:
                        public_attr = save_public_attr(project=interview.public_attr.project,
                                                   user=interview.public_attr.target_user, save_type=ATTR_TYPE_ACTIONPLAN)

                        start_date, error = action_plan_public.get_default_start_date(coachee_plan)
                        if error:
                            return parameter_error_response(error)
                        ActionPlan.objects.create(interview=interview, public_attr=public_attr,
                                                  content=coachee_plan['content'],
                                                  end_date=coachee_plan['end_date'],
                                                  start_date=start_date,
                                                  creator_role=ROLE_COACHEE)

                # 教练领导力单选 教练Q3
                if leader_capacity:
                    for capacity in leader_capacity:
                        capacity_query = CapacityTag.objects.filter(tag_type=1, title=capacity['capacity'], deleted=False,
                                                                    interview__isnull=True)

                        if capacity_query:
                            capacity_tag = capacity_query.first()
                        else:
                            capacity_tag = CapacityTag.objects.create(interview=interview, title=capacity['capacity'],
                                                                      tag_type=2, select_type=1)
                        coach_record_obj.leader_capacity = capacity_tag

                # 习惯养成 教练Q4-1/Q4-2
                if coach_change:
                    for coach_item in coach_change:
                        public_attr = save_public_attr(project=interview.public_attr.project,
                                                   user=interview.public_attr.target_user,
                                                   target_user=interview.public_attr.user, save_type=ATTR_TYPE_HABIT)
                        Habit.objects.create(interview=interview, public_attr=public_attr, when=coach_item['when'],
                                             stop=coach_item['stop'], change=coach_item['change'],
                                             start_date=coach_item['start_date'],
                                             end_date=coach_item['end_date'],
                                             creator_role=ROLE_COACH)
                # 行动计划 教练Q5-1/Q5-2
                if coach_action_plan:
                    for coach_plan in coach_action_plan:
                        public_attr = save_public_attr(project=interview.public_attr.project,
                                                   user=interview.public_attr.target_user,
                                                   target_user=interview.public_attr.user, save_type=ATTR_TYPE_ACTIONPLAN)
                        start_date, error = action_plan_public.get_default_start_date(coach_plan)
                        if error:
                            return parameter_error_response(error)
                        ActionPlan.objects.create(interview=interview, public_attr=public_attr,
                                                  content=coach_plan['content'],
                                                  end_date=coach_plan['end_date'],
                                                  start_date=start_date,
                                                  creator_role=ROLE_COACH)

                # 拓展学习多选标签 教练Q6
                if coach_leader_suggest:
                    state, msg = add_article_tag(coach_leader_suggest, interview)
                    if not state:
                        return parameter_error_response(msg)
                # 项目笔记 教练Q7
                if note:
                    public_attr = save_public_attr(project=interview.public_attr.project,
                                                   user=interview.public_attr.user, save_type=ATTR_TYPE_PROJECTNOTE)
                    ProjectNote.objects.create(interview=interview, public_attr=public_attr, content=note)
                # 成长笔记 教练Q8
                if diary:
                    # Diary.objects.create(public_attr=interview.public_attr, content=diary, creator_role=4)
                    public_attr = save_public_attr(project=interview.public_attr.project,
                                                   user=interview.public_attr.user, save_type=ATTR_TYPE_DIARY)
                    Diary.objects.create(content=diary, type=DIARY_FROM_RECORD, public_attr=public_attr,
                                         interview=interview, creator_role=ROLE_COACH)
                # 保存填写状态
                if not interview.coach_record_status and all([coach_target]):
                    interview_public.fill_record(interview, is_coach=True)
                    if int(request.headers.get('role')) == 1:
                        send_interview_view_message.delay(interview.pk)

                # 被教练者完成任意一个即为已填写
                if not interview.coachee_record_status and (discover or coachee_thought):
                    interview_public.fill_record(interview, is_coach=False)
                    if int(request.headers.get('role')) == 2:
                        # 教练通知
                        work_wechat_user = WorkWechatUser.objects.filter(
                            wx_user_id__isnull=False,
                            user_id=interview.public_attr.user.id,
                            deleted=False
                        ).first()
                        if work_wechat_user:
                            company = interview.public_attr.project.company
                            company_name = company.real_name
                            date = '{}-{}'.format(
                                interview.public_attr.start_time.strftime('%Y-%m-%d %H:%M'),
                                interview.public_attr.end_time.strftime('%H:%M'))
                            send_work_wechat_coach_notice.delay(
                                work_wechat_user.wx_user_id,
                                'coachee_record',
                                company=company_name,
                                coachee_name=interview.public_attr.target_user.cover_name,
                                interview_id=interview.id,
                                date=date,
                                coach_id=interview.public_attr.user.id,
                                project_id=interview.public_attr.project_id,
                                project_name=interview.public_attr.project.name,
                                coachee_id=interview.public_attr.target_user.id,
                                coach_name=interview.public_attr.user.cover_name,
                                content_item=[
                                    {
                                        "key": "客户名称", "value": interview.public_attr.target_user.cover_name
                                    },
                                    {
                                        "key": "所属企业", "value": company_name
                                    },
                                    {
                                        "key": "所属项目", "value": interview.public_attr.project.name
                                    },
                                    {
                                        "key": "辅导时间", "value": date,
                                    },
                                ])
                interview.save()
                coach_record_obj.save()

                # 打分过低则发送邮件
                # 教练填写不发送邮件
                if harvest_score or satisfaction_score or target_progress:
                    send_scoring_risk_alert_email_pre(
                        harvest_score, satisfaction_score, interview_id, target_progress=target_progress)

                return success_response(request=request)
        except Exception as e:
            return parameter_error_response(str(e))


    @swagger_auto_schema(
        operation_id='app约谈记录详情',
        operation_summary='app约谈记录详情',
        manual_parameters=[
            openapi.Parameter('interview_id', openapi.IN_QUERY, description='约谈id', type=openapi.TYPE_NUMBER, required=True)
        ],
        tags=['app约谈记录相关']
    )
    @action(methods=['get'], detail=False, url_path='detail')
    def interview_record_detail(self, request, *args, **kwargs):
        interview_id = request.query_params.get('interview_id', 0)
        instance = ProjectInterviewRecord.objects.filter(deleted=False, interview_id=interview_id).order_by('-updated_at').first()
        user_role = int(request.headers.get('role', 0))
        serializer = AppInterviewRecordDetailSerializers(instance, context={'user_role': user_role})
        data = serializer.data
        interview = ProjectInterview.objects.get(id=interview_id)
        data['coach_name'] = interview.public_attr.user.cover_name if interview.public_attr.user_id else ''
        data['coachee_name'] = interview.public_attr.target_user.cover_name
        data['coachee_user_id'] = interview.public_attr.target_user_id
        data['date'] = interview.public_attr.start_time.strftime('%Y-%m-%d %H:%M') + '-' + \
                       interview.public_attr.end_time.strftime('%H:%M')
        if not instance:
            data['topic'] = interview.message_topic

        return success_response(data, request=request)


    @swagger_auto_schema(
        operation_id='app修改约谈记录信息',
        operation_summary='app修改约谈记录信息',
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'interview_id': openapi.Schema(type=openapi.TYPE_NUMBER, description='约谈id'),
                'topic': openapi.Schema(type=openapi.TYPE_STRING, description='约谈议题'),
                'coach_target': openapi.Schema(type=openapi.TYPE_STRING, description='教练辅导目标信息'),
                'leader_capacity': openapi.Schema(type=openapi.TYPE_ARRAY, description='教练填写领导力',
                                                    items=openapi.Schema(type=openapi.TYPE_OBJECT)),
                'observation': openapi.Schema(type=openapi.TYPE_STRING, description='教练填写观察'),
                'coach_change': openapi.Schema(type=openapi.TYPE_ARRAY, description='教练填写行为转变 A',
                                               items=openapi.Schema(type=openapi.TYPE_OBJECT)),
                'coach_action_plan': openapi.Schema(type=openapi.TYPE_ARRAY, description='教练填写行为任务 C',
                                                    items=openapi.Schema(type=openapi.TYPE_OBJECT)),
                'coach_leader_suggest': openapi.Schema(type=openapi.TYPE_ARRAY, description='教练填写学习能力项 B',
                                                    items=openapi.Schema(type=openapi.TYPE_OBJECT)),
                'note': openapi.Schema(type=openapi.TYPE_STRING, description='教练填写项目笔记'),
                'diary': openapi.Schema(type=openapi.TYPE_STRING, description='教练填写成长日记'),

                'discover': openapi.Schema(type=openapi.TYPE_STRING, description='被教填写发现'),
                'coachee_thought': openapi.Schema(type=openapi.TYPE_STRING, description='被教填写新的思考'),
                'coachee_change': openapi.Schema(type=openapi.TYPE_ARRAY, description='被教填写行为转变 A',
                                                 items=openapi.Schema(type=openapi.TYPE_OBJECT)),
                'action_plan':  openapi.Schema(type=openapi.TYPE_ARRAY, description='被教填写行为任务 C',
                                                 items=openapi.Schema(type=openapi.TYPE_OBJECT)),
                'target_progress': openapi.Schema(type=openapi.TYPE_NUMBER, description='被教填写发展目标评分'),
                'harvest_score': openapi.Schema(type=openapi.TYPE_NUMBER, description='被教填写投入度评分'),
                'satisfaction_score': openapi.Schema(type=openapi.TYPE_NUMBER, description='被教填写满意度评分'),
            }
        ),
        tags=['app约谈记录相关']
    )
    @action(methods=['post'], detail=False, url_path='update')
    def interview_record_update(self, request, *args, **kwargs):
        try:
            interview_id = int(request.data.get('interview_id', 0))
            topic = request.data.get('topic', None)
            # 教练填写
            leader_capacity = request.data.get('leader_capacity', [])
            observation = request.data.get('observation', None)
            coach_target = request.data.get('coach_target', None)
            note = request.data.get('project_note', None)
            diary = request.data.get('diary', None)
            coach_change = request.data.get('coach_change', [])
            coach_action_plan = request.data.get('coach_action_plan', [])
            coach_leader_suggest = request.data.get('coach_leader_suggest', [])

            # 被教填写
            coachee_change = request.data.get('coachee_change', [])
            action_plan = request.data.get('action_plan', [])
            discover = request.data.get('discover', None)
            coachee_thought = request.data.get('coachee_thought', None)
            target_progress = int(request.data.get('target_progress', 0))
            harvest_score = int(request.data.get('harvest_score', 0))
            satisfaction_score = int(request.data.get('satisfaction_score', 0))
            interview = ProjectInterview.objects.get(pk=interview_id)
            instance = ProjectInterviewRecord.objects.get(interview=interview)
        except Exception as e:
            return parameter_error_response()

        with transaction.atomic():
            data = request.data.copy()
            update_serializer = AppInterviewRecordSerializers
            serializer = update_serializer(instance, data=data)
            serializer.is_valid(raise_exception=True)
            instance = serializer.save()
            # 保存议题 客户问题Q1

            # 教练修改是coach_target，客户修改议题是topic
            if topic:
                interview.coachee_topic = topic
            if coach_target:
                interview.topic = coach_target

            # 被教成长笔记 客户问题Q2-1
            if discover:
                if instance.discover:
                    instance.discover.content = discover
                    instance.discover.save()
                else:
                    public_attr = save_public_attr(project=interview.public_attr.project,
                                                   user=interview.public_attr.target_user, save_type=ATTR_TYPE_DIARY)
                    discover_diary = Diary.objects.create(content=discover, type=DIARY_FROM_RECORD,
                                                          public_attr=public_attr,
                                                          interview=interview, creator_role=ROLE_COACHEE)
                    instance.discover = discover_diary
            # 被教成长笔记 客户问题Q2-2
            if coachee_thought:
                if instance.coachee_thought:
                    instance.coachee_thought.content = coachee_thought
                    instance.coachee_thought.save()
                else:
                    public_attr = save_public_attr(project=interview.public_attr.project,
                                                   user=interview.public_attr.target_user, save_type=ATTR_TYPE_DIARY)
                    thought_diary = Diary.objects.create(content=coachee_thought, type=DIARY_FROM_RECORD,
                                                         public_attr=public_attr,
                                                         interview=interview, creator_role=ROLE_COACHEE)
                    instance.coachee_thought = thought_diary

            # 被教习惯养成 客户问题Q3-1/Q3-2
            if coachee_change:
                exist_change = Habit.objects.filter(interview=interview, creator_role=ROLE_COACHEE, deleted=False,
                                                    public_attr__user=interview.public_attr.target_user)
                change_list = list(exist_change.values_list('id', flat=True))
                for coachee_item in coachee_change:
                    if coachee_item.get('id'):
                        change = exist_change.get(pk=coachee_item.get('id'))
                        serializer_change = HabitSerializer(change, data=coachee_item)
                        serializer_change.is_valid(raise_exception=True)
                        serializer_change.save()
                        # 从现有数组删除对象，剩余的是需要删除的
                        change_list.remove(change.id)
                    else:
                    #     新的内容
                        public_attr = save_public_attr(project=interview.public_attr.project,
                                                       user=interview.public_attr.target_user,
                                                       save_type=ATTR_TYPE_HABIT)
                        Habit.objects.create(interview=interview, public_attr=public_attr, when=coachee_item['when'],
                                             stop=coachee_item['stop'], change=coachee_item['change'],
                                             start_date=coachee_item['start_date'],
                                             end_date=coachee_item['end_date'],
                                             creator_role=ROLE_COACHEE)
                #   没有上传的id需要删除
                delete_list = exist_change.filter(pk__in=change_list)
                for item in delete_list:
                    item.deleted = True
                    item.save()
            elif 'coachee_change' in data.keys() and not data['coachee_change']:
                exist_change = Habit.objects.filter(interview=interview, creator_role=ROLE_COACHEE, deleted=False,
                                                    public_attr__user=interview.public_attr.target_user)
                if exist_change.exists():
                    exist_change.update(deleted=True)
            else:
                pass

            # 被教行动计划 客户问题Q4-1/Q4-2
            if action_plan:
                exist_list = ActionPlan.objects.filter(interview=interview, creator_role=ROLE_COACHEE, deleted=False,
                                                       public_attr__user=interview.public_attr.target_user)
                id_list = list(exist_list.values_list('id', flat=True))
                for coachee_plan in action_plan:
                    if coachee_plan.get('id'):
                        exist_obj = exist_list.get(pk=coachee_plan.get('id'))
                        serializer_obj = ActionPlanSerializer(exist_obj, data=coachee_plan)
                        serializer_obj.is_valid(raise_exception=True)
                        serializer_obj.save()
                        # 从现有数组删除对象，剩余的是需要删除的
                        id_list.remove(exist_obj.id)
                    else:
                        #     新的内容
                        public_attr = save_public_attr(project=interview.public_attr.project,
                                                       user=interview.public_attr.target_user,
                                                       save_type=ATTR_TYPE_ACTIONPLAN)
                        start_date, error = action_plan_public.get_default_start_date(coachee_plan)
                        if error:
                            return parameter_error_response(error)
                        ActionPlan.objects.create(interview=interview, public_attr=public_attr,
                                                  content=coachee_plan['content'],
                                                  end_date=coachee_plan['end_date'],
                                                  start_date=start_date,
                                                  creator_role=ROLE_COACHEE)
                #   没有上传的id需要删除
                delete_list = exist_list.filter(pk__in=id_list)
                for item in delete_list:
                    item.deleted = True
                    item.save()
            elif 'action_plan' in data.keys() and not data['action_plan']:
                exist_list = ActionPlan.objects.filter(interview=interview, creator_role=ROLE_COACHEE,
                                                       deleted=False,
                                                       public_attr__user=interview.public_attr.target_user)
                if exist_list.exists():
                    exist_list.update(deleted=True)
            else:
                pass

            # 教练领导力单选 教练Q3
            if leader_capacity:
                exist_tag = CapacityTag.objects.filter(
                    interview=interview,
                    select_type=TAG_SELECT_TYPE_SINGLE,
                    deleted=False
                ).first()
                if exist_tag:
                    exist_tag.deleted = True
                    exist_tag.save()
                for capacity in leader_capacity:
                    tag_type = TAG_SOURCE_SYSTEM if capacity['is_default'] else TAG_SOURCE_CUSTOM
                    capacity = CapacityTag.objects.create(interview=interview, title=capacity['capacity'],
                                                          tag_type=tag_type, select_type=TAG_SELECT_TYPE_SINGLE)
                    instance.leader_capacity = capacity
            elif 'leader_capacity' in data.keys() and not data['leader_capacity']:  # 删除
                exist_tag = CapacityTag.objects.filter(
                    interview=interview,
                    select_type=TAG_SELECT_TYPE_SINGLE,
                    deleted=False
                ).first()
                if exist_tag:
                    exist_tag.deleted = True
                    exist_tag.save()
                instance.leader_capacity = None
                instance.save()
            else:
                pass

            # 习惯养成 教练Q4-1/Q4-2
            if coach_change:
                exist_change = Habit.objects.filter(interview=interview, creator_role=ROLE_COACH, deleted=False,
                                                    public_attr__user=interview.public_attr.target_user)
                change_list = list(exist_change.values_list('id', flat=True))
                for coach_item in coach_change:
                    if coach_item.get('id'):
                        change = exist_change.get(pk=coach_item.get('id'))
                        serializer_change = HabitSerializer(change, data=coach_item)
                        serializer_change.is_valid(raise_exception=True)
                        serializer_change.save()
                        # 从现有数组删除对象，剩余的是需要删除的
                        change_list.remove(change.id)
                    else:
                        #     新的内容
                        public_attr = save_public_attr(project=interview.public_attr.project,
                                                       user=interview.public_attr.target_user,
                                                       save_type=ATTR_TYPE_HABIT)
                        Habit.objects.create(interview=interview, public_attr=public_attr,
                                             when=coach_item['when'],
                                             stop=coach_item['stop'], change=coach_item['change'],
                                             start_date=coach_item['start_date'],
                                             end_date=coach_item['end_date'],
                                             creator_role=ROLE_COACH)
                #   没有上传的id需要删除
                delete_list = exist_change.filter(pk__in=change_list)
                for item in delete_list:
                    item.deleted = True
                    item.save()
            elif 'coach_change' in data.keys() and not data['coach_change']:
                exist_change = Habit.objects.filter(interview=interview, creator_role=ROLE_COACH, deleted=False,
                                                    public_attr__user=interview.public_attr.target_user)
                if exist_change.exists():
                    exist_change.update(deleted=True)
            else:
                pass
            # 行动计划 教练Q5-1/Q5-2
            if coach_action_plan:
                exist_list = ActionPlan.objects.filter(interview=interview, creator_role=ROLE_COACH, deleted=False,
                                                       public_attr__user=interview.public_attr.target_user)
                id_list = list(exist_list.values_list('id', flat=True))
                for coach_plan in coach_action_plan:
                    if coach_plan.get('id'):
                        exist_obj = exist_list.get(pk=coach_plan.get('id'))
                        serializer_obj = ActionPlanSerializer(exist_obj, data=coach_plan)
                        serializer_obj.is_valid(raise_exception=True)
                        serializer_obj.save()
                        # 从现有数组删除对象，剩余的是需要删除的
                        id_list.remove(exist_obj.id)
                    else:
                        # 新的内容
                        public_attr = save_public_attr(project=interview.public_attr.project,
                                                       user=interview.public_attr.target_user,
                                                       save_type=ATTR_TYPE_ACTIONPLAN)
                        start_date, error = action_plan_public.get_default_start_date(coach_plan)
                        if error:
                            return parameter_error_response(error)
                        ActionPlan.objects.create(interview=interview, public_attr=public_attr,
                                                  content=coach_plan['content'],
                                                  end_date=coach_plan['end_date'],
                                                  start_date=start_date,
                                                  creator_role=ROLE_COACH)
                #   没有上传的id需要删除
                delete_list = exist_list.filter(pk__in=id_list)
                for item in delete_list:
                    item.deleted = True
                    item.save()
            elif 'coach_action_plan' in data.keys() and not data['coach_action_plan']:
                exist_list = ActionPlan.objects.filter(interview=interview, creator_role=ROLE_COACH, deleted=False,
                                                       public_attr__user=interview.public_attr.target_user)
                if exist_list.exists():
                    exist_list.update(deleted=True)
            else:
                pass

            # 拓展学习多选标签 教练Q6
            if coach_leader_suggest:

                # 存在自定义主题，需要写入CapacityTag表
                # 清除之前绑定的数据
                exist_tags = CapacityTag.objects.filter(interview=interview, select_type=TAG_SELECT_TYPE_MULTI)
                for tag in exist_tags:
                    tag.deleted = True
                    tag.save()
                state, msg = add_article_tag(coach_leader_suggest, interview)
                if not state:
                    return parameter_error_response(msg)

            # 项目笔记
            if note:
                interview_public.create_or_update_interview_project_note(interview, note)

            # 成长笔记
            if diary:
                interview_public.create_or_update_interview_diary(interview, diary)

            instance.save()
            interview.save()
            # 教练填写不发送邮件
            if harvest_score or satisfaction_score or target_progress:
                send_scoring_risk_alert_email_pre(
                    instance.harvest_score,
                    instance.satisfaction_score,
                    interview_id,
                    target_progress=instance.target_progress
                )
            return success_response(request=request)

    @swagger_auto_schema(
        operation_id='app修改问卷模式约谈记录信息',
        operation_summary='app修改问卷模式约谈记录信息',
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'interview_id': openapi.Schema(type=openapi.TYPE_NUMBER, description='约谈id'),
                'project_note': openapi.Schema(type=openapi.TYPE_STRING, description='教练填写项目笔记'),
                'diary': openapi.Schema(type=openapi.TYPE_STRING, description='教练填写成长日记')
            }
        ),
        tags=['app约谈记录相关']
    )
    @action(methods=['post'], detail=False, url_path='questionnaire/update')
    def questionnaire_interview_record_update(self, request, *args, **kwargs):
        try:
            interview_id = request.data.get('interview_id')
            project_note = request.data.get('project_note')
            diary = request.data.get('diary')
            interview = ProjectInterview.objects.get(id=interview_id, deleted=False)
        except ProjectInterview.DoesNotExist:
            return parameter_error_response('辅导不存在')
        except Exception as e:
            return parameter_error_response()

        # 项目笔记
        if project_note:
            interview_public.create_or_update_interview_project_note(interview, project_note)

        # 成长笔记
        if diary:
            interview_public.create_or_update_interview_diary(interview, diary)
        return success_response(request=request)


def add_article_tag(coach_leader_suggest, interview):
    for tag in coach_leader_suggest:
        # CapacityTag创建绑定主题记录
        tag_type = TAG_SOURCE_SYSTEM if tag.get('is_default') else TAG_SOURCE_CUSTOM
        capacity = CapacityTag.objects.create(
            interview=interview, title=tag.get('capacity'),
            tag_type=tag_type, select_type=TAG_SELECT_TYPE_MULTI)
        # 如果不是自定义主题，则绑定推送文章
        if tag.get('is_default'):
            article_queryset = Article.objects.filter(
                tag=tag.get('tag'),
                deleted=False)
            # 该主题下有文章才进行绑定
            if article_queryset:
                # 随机绑定某篇文章
                last_index = random.randint(0, article_queryset.count() - 1)
                article = article_queryset[last_index]
                learn_article = LearnArticle.objects.filter(
                    article=article, deleted=False,
                    public_attr__user=interview.public_attr.target_user).first()

                # 随机到已绑定的文章则跳过
                if not learn_article:
                    public_attr = save_public_attr(
                        project=interview.public_attr.project,
                        user=interview.public_attr.target_user,
                        target_user=interview.public_attr.user,
                        save_type=ATTR_TYPE_LEARN)
                    LearnArticle.objects.create(
                        public_attr=public_attr, capacity_tag=capacity, article=article)
        return True, None
