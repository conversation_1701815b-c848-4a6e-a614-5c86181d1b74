import time, datetime
from rest_framework.views import APIView

from drf_yasg.utils import swagger_auto_schema
from utils.api_response import success_response
from utils.sts  import getStsToken



class GetSts(APIView):

    @swagger_auto_schema(
        operation_id='获取sts token',
        operation_summary='获取sts token',
        manual_parameters=[
        ],
        tags=['app获取sts token']
    )
    def get(self, request, *args, **kwargs):
        token = getStsToken()
        return success_response({'token': token}, request=request)

