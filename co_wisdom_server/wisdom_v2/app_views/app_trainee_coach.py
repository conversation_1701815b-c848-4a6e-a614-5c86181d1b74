import datetime
import json

import pendulum
from drf_yasg import openapi
from django.db import transaction
from rest_framework import viewsets
from drf_yasg.utils import swagger_auto_schema
from rest_framework.decorators import action

from utils import utc_date_time, validate, task
from utils.miniapp_version_judge import compare_version
from utils.utc_date_time import get_total_time, datetime_change_utc
from wisdom_v2.common import schedule_public, interview_public
from wisdom_v2.common.interview_public import check_member_interview_times
from wisdom_v2.enum.project_interview_enum import InterviewRecordTypeEnum, ProjectInterviewPlaceTypeEnum, \
    InterviewSubjectEnum
from wisdom_v2.models import TraineeCoach, User, PublicAttr, Schedule, ProjectInterview, \
    TraineeCoachInviteUser, ProjectMember, ProjectCoach, WorkWechatUser
from utils.api_response import success_response, parameter_error_response
from wisdom_v2.views.constant import ATTR_TYPE_INTERVIEW, INTERVIEW_TYPE_COACHING
from wisdom_v2.views.project_interview_action import ProjectInterviewListSerializer
from wisdom_v2.views.trainee_coach_actions import TraineeCoachBaseSerializers
from utils.task import send_coach_add_interview_message
from wisdom_v2.enum.service_content_enum import CoachTypeEnum, ScheduleTypeEnum, ScheduleApplyTypeEnum
from wisdom_v2.app_views.app_interview_actions import time_hour



class AppTraineeCoachBaseViewSet(viewsets.ModelViewSet):
    queryset = User.objects.filter(deleted=False)
    serializer_class = TraineeCoachBaseSerializers

    @swagger_auto_schema(
        operation_id='个人教练详情',
        operation_summary='个人教练详情',
        tags=['移动端个人教练相关']
    )
    def retrieve(self, request, *args, **kwargs):
        user = self.get_object()
        trainee_coach = TraineeCoach.objects.filter(
            user=user
        ).first()

        serializer = self.get_serializer(trainee_coach)
        return success_response(serializer.data)

    @swagger_auto_schema(
        operation_id='修改个人教练信息',
        operation_summary='修改个人教练信息',
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'name': openapi.Schema(type=openapi.TYPE_STRING, description='用户名'),
                'email': openapi.Schema(type=openapi.TYPE_STRING, description='邮箱'),
                'phone': openapi.Schema(type=openapi.TYPE_STRING, description='手机号'),
                'head_image_url': openapi.Schema(type=openapi.TYPE_STRING, description='头像链接'),

                'user_class': openapi.Schema(
                    type=openapi.TYPE_STRING, description='班次'),
                'extra_time': openapi.Schema(
                    type=openapi.TYPE_NUMBER, description='平台外一对一辅导时常'),
                'working_years': openapi.Schema(
                    type=openapi.TYPE_NUMBER, description='工作年限 | 1：一年以上，2：三年以上，3：五年以上'),
                'language': openapi.Schema(
                    type=openapi.TYPE_NUMBER, description='语言｜ 1：中文，2：英语，3：法语，4：日语'),
                'work_experience': openapi.Schema(type=openapi.TYPE_STRING, description='工作经历'),
                'brief': openapi.Schema(type=openapi.TYPE_STRING, description='教练简介'),
                'customer_evaluate': openapi.Schema(type=openapi.TYPE_STRING, description='案例及客户评价'),
                'style': openapi.Schema(type=openapi.TYPE_STRING, description='教练风格'),
                'qualification': openapi.Schema(type=openapi.TYPE_ARRAY, description='资质证书', items=openapi.Schema(
                    type=openapi.TYPE_OBJECT, properties={
                        "describe": openapi.Schema(type=openapi.TYPE_STRING, description='描述'),
                        "image_url": openapi.Schema(type=openapi.TYPE_STRING, description='图片链接'),
                    })),
                'industry': openapi.Schema(type=openapi.TYPE_STRING, description='教练过的行业'),
                'domain': openapi.Schema(type=openapi.TYPE_STRING, description='教练领域'),
                'coach_auth': openapi.Schema(
                    type=openapi.TYPE_STRING, description='教练认证 ｜ 1：ICF-ACC, 2：ICF-PCC, 3：ICF-MCC, 4：准ICF-PCC, 5：个人教练'),
            }
        ),
        tags=['移动端个人教练相关']
    )
    def update(self, request, *args, **kwargs):
        data = request.data.copy()
        user = self.get_object()
        instance = TraineeCoach.objects.filter(
            user=user
        ).first()

        email = request.data.get('email')
        if email:
            if not validate.validate_email(email):
                return parameter_error_response('邮箱格式错误')
            elif len(email) > 50:
                return parameter_error_response('邮箱最多50个字')
            elif User.objects.filter(email=email, deleted=False).exists():
                return parameter_error_response('该邮箱已注册')
            user.email = email
        user.head_image_url = request.data.get('head_image_url', user.head_image_url)
        User.save(user)

        serializer = self.get_serializer(instance, data=data, partial=True)
        serializer.is_valid(raise_exception=True)
        serializer.save()
        return success_response(serializer.data)

    @swagger_auto_schema(
        operation_id='个人教练创建辅导记录',
        operation_summary='个人教练创建辅导记录',
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'user_name': openapi.Schema(type=openapi.TYPE_NUMBER, description='用户真实姓名'),
                'topic': openapi.Schema(type=openapi.TYPE_STRING, description='约谈议题'),
                'user_id': openapi.Schema(type=openapi.TYPE_STRING, description='被教练者id'),
                'start_time': openapi.Schema(type=openapi.TYPE_STRING, description='约谈开始时间'),
                'end_time': openapi.Schema(type=openapi.TYPE_STRING, description='约谈结束时间'),
            }
        ),
        tags=['移动端个人教练相关']
    )
    @action(methods=['post'], detail=True, url_path='interview')
    def trainee_coach_interview_create(self, request, *args, **kwargs):

        try:
            coach_user = self.get_object()
            user_name = request.data['user_name']
            user_id = request.data.get('user_id')
            topic = request.data['topic']
            start_time = request.data['start_time']
            end_time = request.data['end_time']
            status = int(request.data.get('status', 3))
            place_category = int(request.data.get('place_category', 1))
            is_time_validate = request.data.get('is_time_validate')
            mp = json.loads(request.headers.get('mp')) if request.headers.get('mp') else None

            place_type = request.data.get('place_type', ProjectInterviewPlaceTypeEnum.online.value)
            place = request.data.get('place', '腾讯会议')
            interview_subject = request.data.get('interview_subject', InterviewSubjectEnum.regular.value)
        except Exception:
            return parameter_error_response()

        try:
            # with transaction.atomic():
            project = None
            # 是否直接绑定用户
            if user_id:
                user = User.objects.filter(pk=user_id).first()
                project_coach = ProjectCoach.objects.filter(
                    coach__user=coach_user,
                    project__isnull=False,
                    member_id=user_id, project_group_coach__isnull=True,
                    deleted=False).first()
                if project_coach:
                    project = project_coach.project
            else:
                return parameter_error_response('目前仅支持预约项目客户')

            # 获取时长
            date = get_total_time(start_time, end_time)

            # 检测时间是否冲突，教练辅导时间冲突需要弹窗提示
            if is_time_validate:
                conflict_interview = interview_public.coach_interview_time_validate(
                    coach_user.id, start_time, end_time)
                # 如果存在冲突的日程，返回描述信息，弹窗提示
                if conflict_interview:
                    time_str = (f'{conflict_interview.public_attr.start_time.strftime("%Y年%m月%d日%H:%M")}-'
                                f'{conflict_interview.public_attr.end_time.strftime("%H:%M")}')
                    return success_response({
                        'is_interview_conflict': True,
                        'msg': f'新的日程与{time_str}的日程有冲突，确认要创建吗？'})

            # 创建辅导关系并在教练日历创建日程
            public_data = {
                "start_time": start_time, "end_time" : end_time, "user_id": coach_user.pk, "target_user_id": user_id,
                "type": ATTR_TYPE_INTERVIEW, "status": status
            }

            if mp and compare_version(mp.get('version'), '2.32') >= 0:
                record_type = InterviewRecordTypeEnum.questionnaire.value
            else:
                record_type = InterviewRecordTypeEnum.question_and_answer.value
            if project:
                public_data['project_id'] = project.pk
                # 添加预约辅导次数及时间限制
                project_member = ProjectMember.objects.filter(user_id=user_id, project=project, deleted=False,
                                                              is_forbidden=False).first()
                if not project_member:
                    return parameter_error_response('当前被教练者未配置项目')

                project_bundle = project_member.project_bundle.filter(deleted=False).first()
                if not project_bundle:
                    return parameter_error_response('当前被教练者未配置服务内容')
                if not project_bundle.one_to_one_coach.filter(deleted=False).exists():
                    return parameter_error_response('当前被教练者未配置1对1辅导模块')
                flag, times = time_hour(start_time, end_time, user_id, project.pk)

                if place_category == 1:
                    if not project_bundle.one_to_one_coach.filter(deleted=False,
                                                                  type=CoachTypeEnum.online.value).exists():
                        return parameter_error_response('当前被教练者服务内容不包含线上1对1辅导')

                    result, available_time = check_member_interview_times(project_member, times)
                    if not result:
                        return parameter_error_response('当前被教练者目前没有可用的教练辅导时长')
            else:
                return parameter_error_response('目前仅支持预约项目客户')

            with transaction.atomic():
                public_attr = PublicAttr.objects.create(**public_data)
                Schedule.objects.create(public_attr_id=public_attr.pk, type=ScheduleTypeEnum.interview, title=topic)
                project_interview = ProjectInterview.objects.create(
                    place_type=place_type, place=place, interview_subject=interview_subject,
                    public_attr_id=public_attr.pk,
                    type=INTERVIEW_TYPE_COACHING,
                    place_category=place_category,
                    record_type=record_type,
                    times=date.minute)
                if topic:
                    project_interview.topic = topic
                project_interview.save()

                # 线上辅导创建会议
                if place_type == ProjectInterviewPlaceTypeEnum.online.value:
                    if mp and compare_version(mp.get('version'), '2.33') >= 0:
                        # 教练通知
                        work_wechat_user = WorkWechatUser.objects.filter(
                            wx_user_id__isnull=False,
                            user_id=public_attr.user_id,
                            deleted=False).first()
                        if work_wechat_user:
                            # 创建会议信息
                            start_date_time = datetime.datetime.strptime(start_time, '%Y-%m-%d %H:%M')
                            end_date_time = datetime.datetime.strptime(end_time, '%Y-%m-%d %H:%M')
                            time_delta = end_date_time - start_date_time
                            task.create_project_interview_meeting.delay(
                                project_interview.id, work_wechat_user.wx_user_id, '教练辅导',
                                int(start_date_time.timestamp()), time_delta.seconds)

                self.serializer_class = ProjectInterviewListSerializer
                serializer = self.get_serializer(project_interview)
                # 给被教练者客户通过企业微信发送提醒
                if not project:
                    # 非企业客户创建邀请记录
                    TraineeCoachInviteUser.objects.create(
                        user=user,
                        trainee_coach=coach_user,
                        interview=project_interview,
                    )
                else:

                    start_time = utc_date_time.datetime_change_utc(start_time)
                    if start_time > pendulum.now():
                        send_coach_add_interview_message.apply_async(kwargs=dict(
                            interview_id=project_interview.pk, project_id=project.pk), countdown=3, expires=120)
                return success_response(serializer.data)
        except Exception as e:
            return parameter_error_response(str(e))
