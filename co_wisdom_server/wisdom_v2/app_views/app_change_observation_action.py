from datetime import datetime

from rest_framework import serializers

from wisdom_v2.enum.service_content_enum import MultipleAssociationRelationTypeEnum, PersonalReportTypeEnum
from wisdom_v2.models import ChangeObservation, GrowthGoals2ChangeObservation, GrowthGoalsChange, ProjectInterested, \
    CompanyMember, MultipleAssociationRelation, ChangeObservationAnswer, PersonalReport


class ChangeObservationSerializer(serializers.ModelSerializer):
    id = serializers.IntegerField(help_text='改变观察反馈id')
    name = serializers.CharField(help_text='改变观察反馈名称')
    remind_text = serializers.SerializerMethodField(help_text='改变观察反馈文案')
    growth_goals = serializers.SerializerMethodField(help_text='成长目标')

    class Meta:
        model = ChangeObservation
        fields = ['id', 'name', 'remind_text', 'growth_goals']

    def get_remind_text(self, obj):
        return f'正在给{obj.project_member.user.cover_name}填写反馈...'

    def get_growth_goals(self, obj):
        # page_number = ['①', '②', '③', '④', '⑤', '⑥', '⑦', '⑧', '⑨', '⑩', '⑪', '⑫', '⑬', '⑭', '⑮', '⑯',
        #                '⑰', '⑱', '⑲', '⑳', '㉑', '㉒', '㉓', '㉔', '㉕', '㉖', '㉗', '㉘', '㉙', '㉚', '㉛', '㉜',
        #                '㉝', '㉞', '㉟', '㊱', '㊲', '㊳', '㊴', '㊵', '㊶', '㊷', '㊸', '㊹', '㊺', '㊻', '㊼', '㊽',
        #                '㊾', '㊿']
        growth_goals2change_observations = GrowthGoals2ChangeObservation.objects.filter(
            change_observation=obj, deleted=False)
        if not growth_goals2change_observations.exists():
            return []
        results = []
        for index, growth_goals2change_observation in enumerate(list(growth_goals2change_observations)):
            data = {}
            data['id'] = growth_goals2change_observation.growth_goals_id
            data['content'] = growth_goals2change_observation.growth_goals.content
            data['title'] = f'改变目标{index + 1}'
            change_contents = list(GrowthGoalsChange.objects.filter(
                id__in=growth_goals2change_observation.change).values_list('content', flat=True))
            data['change'] = [{'id': growth_goals2change_observation.change[c_index],
                               'content': content} for c_index, content in enumerate(change_contents)]
            results.append(data)
        return results


class AppChangeObservationInfoSerializer(serializers.ModelSerializer):
    """
        小程序获取改变观察反馈基础配置信息
    """
    invite_end_time = serializers.DateTimeField(help_text='客户邀请的截止时间', format='%Y-%m-%d %H:%M')
    stakeholder = serializers.SerializerMethodField(help_text='利益相关者数据')
    status = serializers.SerializerMethodField(help_text='状态')
    personal_report_id = serializers.SerializerMethodField(help_text='报告id')

    class Meta:
        model = ChangeObservation
        fields = ['id', 'name', 'max_stakeholders_count', 'stakeholders_write_end_date', 'stakeholder',
                  'invite_end_time','status', 'personal_report_id']

    def get_status(self, obj):
        personal_report = PersonalReport.objects.filter(
            type=PersonalReportTypeEnum.change_observation_report.value,
            object_id=str(obj.pk), user=obj.project_member.user, deleted=False).first()
        if personal_report:
            return 3  # 有报告，查看报告详情
        elif obj.stakeholders_write_end_date and datetime.now().date() > obj.stakeholders_write_end_date:  # 已经过期
            return 2  # 没有报告，超过时间返回填写中
        else:
            return 1  # 可编辑

    def get_personal_report_id(self, obj):
        personal_report = PersonalReport.objects.filter(
            type=PersonalReportTypeEnum.change_observation_report.value,
            object_id=str(obj.pk), user=obj.project_member.user, deleted=False).first()
        if personal_report:
            return personal_report.pk
        return

    def get_stakeholder(self, obj):
        project_member = obj.project_member
        stakeholders = ProjectInterested.objects.filter(
            master_id=project_member.user_id, project_id=project_member.project_id,
            deleted=False).order_by('-created_at').all()
        stakeholders_data = []
        for item in stakeholders:
            company_member = CompanyMember.objects.filter(
                company=project_member.project.company,
                user=item.interested,  deleted=False).first()
            position = company_member.position if company_member else None

            is_set = MultipleAssociationRelation.objects.filter(
                deleted=False, type=MultipleAssociationRelationTypeEnum.stakeholder_change_observation.value,
                main_id=str(obj.pk), secondary_id=str(item.pk)).exists()

            # 是否已经填写改变观察反馈
            is_answer = ChangeObservationAnswer.objects.filter(
                change_observation=obj, public_attr__user_id=item.interested_id).exists()

            stakeholders_data.append({
                'id': item.pk,
                'true_name': item.interested.cover_name,
                'relation': item.relation,
                'position': position,
                'is_set': is_set,  # 是否已配置改变观察反馈
                'is_cancel': False if is_answer else True,  # 如已经填写,将不可取消
            })
        return stakeholders_data


class AppChangeObservationInviteSerializer(serializers.ModelSerializer):
    """
        小程序获取改变观察反馈基础配置信息
    """
    stakeholders_write_end_date = serializers.DateField(
        help_text='利益相关者填写的截止时间', format='%Y-%m-%d')
    invite_end_time = serializers.DateTimeField(help_text='客户邀请的截止时间', format='%Y-%m-%d %H:%M:%S')
    stakeholder = serializers.SerializerMethodField(help_text='利益相关者数据')
    status = serializers.SerializerMethodField(help_text='状态')
    personal_report_id = serializers.SerializerMethodField(help_text='报告id')

    class Meta:
        model = ChangeObservation
        fields = ['id', 'name', 'max_stakeholders_count', 'stakeholders_write_end_date', 'stakeholder',
                  'invite_end_time', 'status', 'personal_report_id']

