import datetime

from drf_yasg import openapi
from rest_framework import viewsets
from drf_yasg.utils import swagger_auto_schema
from rest_framework.decorators import action

from utils.api_response import parameter_error_response, success_response
from utils.pagination import StandardResultsSetPagination
from wisdom_v2.models import ProjectMember, MultipleAssociationRelation

from wisdom_v2.models_file import StakeholderInterviewModule, StakeholderInterview
from wisdom_v2.app_views.app_stakeholder_interview_action import AppStakeholderInterviewModuleSerializer, \
    AppStakeholderInterviewModuleUpdateSerializer, member_add_stakeholder, AppStakeholderInterviewSerializer, \
    AppStakeholderInterviewDetailSerializer


class AppStakeHolderInterviewModuleViewSet(viewsets.ModelViewSet):
    queryset = StakeholderInterviewModule.objects.filter(deleted=False)
    serializer_class = AppStakeholderInterviewModuleSerializer

    @swagger_auto_schema(
        operation_id='小程序利益相关者访谈详情',
        operation_summary='小程序利益相关者访谈详情',
        manual_parameters=[
            openapi.Parameter(
                'stakeholder_interview_module_id', openapi.IN_QUERY, description='利益相关者访谈配置id',
                type=openapi.TYPE_NUMBER, required=True)
        ],
        tags=['小程序利益相关者访谈相关']
    )
    @action(methods=['get'], detail=False, url_path='detail')
    def stakeholder_interview_detail(self, request, *args, **kwargs):
        try:
            stakeholder_interview_module_id = request.query_params['stakeholder_interview_module_id']
            stakeholder_interview_module = StakeholderInterviewModule.objects.get(id=stakeholder_interview_module_id,
                                                                                  deleted=False)
        except (KeyError, StakeholderInterviewModule.DoesNotExist):
            return parameter_error_response()
        serializer = self.get_serializer(stakeholder_interview_module)
        return success_response(serializer.data)

    @swagger_auto_schema(
        operation_id='修改小程序利益相关者访谈',
        operation_summary='修改小程序利益相关者访谈',
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'id': openapi.Schema(type=openapi.TYPE_NUMBER, description='利益相关者访谈配置id'),
                'stakeholder': openapi.Schema(type=openapi.TYPE_NUMBER, description='客户选中的利益相关者id列表'),
            }
        ),
        tags=['小程序利益相关者访谈相关']
    )
    @action(methods=['post'], detail=False, url_path='update',
            serializer_class=AppStakeholderInterviewModuleUpdateSerializer)
    def update_stakeholder_interview_module(self, request, *args, **kwargs):
        try:
            stakeholder_interview_module_id = request.data['id']
            stakeholder_interview_module = StakeholderInterviewModule.objects.get(
                pk=stakeholder_interview_module_id, deleted=False)
        except (KeyError, StakeholderInterviewModule.DoesNotExist):
            return parameter_error_response()
        data = request.data.copy()
        serializer = self.get_serializer(data=data)
        serializer.is_valid(raise_exception=True)
        stakeholder_interview_module = serializer.update(stakeholder_interview_module, data)
        serializer = AppStakeholderInterviewModuleSerializer(stakeholder_interview_module)
        return success_response(serializer.data)

    @swagger_auto_schema(
        operation_id='客户添加利益相关者',
        operation_summary='客户添加利益相关者',
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'user_id': openapi.Schema(type=openapi.TYPE_NUMBER, description='被教练者用户id'),
                'true_name': openapi.Schema(type=openapi.TYPE_NUMBER, description='姓名'),
                'phone': openapi.Schema(type=openapi.TYPE_NUMBER, description='姓名'),
                'email': openapi.Schema(type=openapi.TYPE_NUMBER, description='姓名'),
                'position': openapi.Schema(type=openapi.TYPE_NUMBER, description='姓名'),
                'relation': openapi.Schema(type=openapi.TYPE_NUMBER, description='姓名'),
            }
        ),
        tags=['小程序利益相关者访谈相关']
    )
    @action(methods=['post'], detail=False, url_path='add_stakeholder')
    def add_stakeholder(self, request, *args, **kwargs):
        try:
            user_id = request.data['user_id']
            true_name = request.data['true_name']
            phone = request.data['phone']
            email = request.data['email']
            position = request.data['position']
            relation = request.data['relation']
            project_member = ProjectMember.objects.filter(user_id=user_id, deleted=False).first()
        except (KeyError, ProjectMember.DoesNotExist):
            return parameter_error_response()

        err, project_interested = member_add_stakeholder(true_name, phone, email, position,relation, project_member)
        if err:
            return parameter_error_response(err)
        # 只需要基础id数据，前端用来做标识
        data = {
            'id': project_interested.pk,
            'relation': project_interested.relation,
        }
        # 历史数据格式影响，返回特定格式
        return success_response({"stakeholder": [data]})


class AppStakeHolderInterviewViewSet(viewsets.ModelViewSet):
    queryset = StakeholderInterview.objects.filter(deleted=False).order_by('-created_at')
    serializer_class = AppStakeholderInterviewSerializer

    @swagger_auto_schema(
        operation_id='小程序客户发送邀请列表',
        operation_summary='小程序客户发送邀请列表',
        manual_parameters=[
            openapi.Parameter(
                'stakeholder_interview_module_id', openapi.IN_QUERY, description='利益相关者访谈配置id',
                type=openapi.TYPE_NUMBER, required=True),
        ],
        tags=['小程序利益相关者访谈相关']
    )
    def list(self, request, *args, **kwargs):
        try:
            stakeholder_interview_module_id = request.query_params['stakeholder_interview_module_id']
            stakeholder_interview_module = StakeholderInterviewModule.objects.get(pk=stakeholder_interview_module_id,
                                                                                  deleted=False)
        except (KeyError, StakeholderInterviewModule.DoesNotExist):
            return parameter_error_response()
        stakeholder_interview = self.get_queryset().filter(stakeholder_interview_module=stakeholder_interview_module)
        request.GET._mutable = True
        self.request.query_params['page_size'] = stakeholder_interview.count()
        # 分页
        paginator = StandardResultsSetPagination()
        page_list = paginator.paginate_queryset(stakeholder_interview, self.request)
        serializer = self.get_serializer(page_list, many=True)
        response = paginator.get_paginated_response(serializer.data)
        return success_response(response)

    @swagger_auto_schema(
        operation_id='客户给利益相关者发送邀请记录',
        operation_summary='客户给利益相关者发送邀请记录',
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'stakeholder_interview_id': openapi.Schema(type=openapi.TYPE_NUMBER, description='利益相关者访谈id'),
            }
        ),
        tags=['小程序利益相关者访谈相关']
    )
    @action(methods=['post'], detail=False, url_path='send_message')
    def send_message(self, request, *args, **kwargs):
        try:
            stakeholder_interview_id = request.data.get('stakeholder_interview_id')
            if stakeholder_interview_id:
                stakeholder_interview = StakeholderInterview.objects.get(id=stakeholder_interview_id, deleted=False)
            else:
                stakeholder_interview = None
            change_observation_relation_id = request.data.get('change_observation_relation_id')
            if change_observation_relation_id:
                change_observation_relation = MultipleAssociationRelation.objects.get(
                    pk=change_observation_relation_id, deleted=False)
            else:
                change_observation_relation = None

        except (KeyError, StakeholderInterview.DoesNotExist, MultipleAssociationRelation.DoesNotExist):
            return parameter_error_response()
        if stakeholder_interview:
            stakeholder_interview.is_send_message = True
            stakeholder_interview.save()

        if change_observation_relation:
            change_observation_relation.send_wechat_invite_at = datetime.datetime.now().date()
            change_observation_relation.save()
        return success_response()

    @swagger_auto_schema(
        operation_id='利益相关者点击邀请链接跳转页',
        operation_summary='利益相关者点击邀请链接跳转页',
        manual_parameters=[
            openapi.Parameter(
                'stakeholder_interview_id', openapi.IN_QUERY, description='利益相关者访谈id',
                type=openapi.TYPE_NUMBER, required=True)
        ],
        tags=['小程序利益相关者访谈相关']
    )
    @action(methods=['get'], detail=False, url_path='detail', authentication_classes=[],
            serializer_class=AppStakeholderInterviewDetailSerializer)
    def stakeholder_interview_detail(self, request, *args, **kwargs):
        try:
            stakeholder_interview_id = request.query_params['stakeholder_interview_id']
            stakeholder_interview = StakeholderInterview.objects.get(id=stakeholder_interview_id)
        except (KeyError, StakeholderInterview.DoesNotExist):
            return parameter_error_response()
        serializer = self.get_serializer(stakeholder_interview)
        return success_response(serializer.data)