from rest_framework import serializers

from wisdom_v2.common.coach_public import get_project_coach_resume
from wisdom_v2.models_file import ChemicalInterview2Coach
from wisdom_v2.models import WorkWechatUser

class AppChemicalInterview2CoachSerializer(serializers.ModelSerializer):
    id = serializers.Char<PERSON>ield(read_only=True)
    coachee_name = serializers.CharField(source='interview.public_attr.target_user.cover_name', read_only=True)
    coach_name = serializers.CharField(source='interview.public_attr.user.cover_name', read_only=True)
    interview_time = serializers.SerializerMethodField(read_only=True)
    chemical_interview_status = serializers.IntegerField(help_text='状态')
    coach_aspect = serializers.SerializerMethodField(help_text='教练特点')
    coach_impression = serializers.CharField(help_text='教练反馈')
    coach_count = serializers.SerializerMethodField(help_text='剩余教练人数')
    head_image_url = serializers.SerializerMethod<PERSON>ield(help_text='教练头像')
    wework_qr_code = serializers.SerializerMethodField(help_text='教练的企业微信二维码')

    class Meta:
        model = ChemicalInterview2Coach
        fields = ('id', 'coachee_name', 'coach_name', 'interview_time', 'chemical_interview_status',
                  'coach_aspect', 'coach_impression', 'coach_count', 'head_image_url', 'wework_qr_code')

    def get_head_image_url(self, obj):
        state, resume = get_project_coach_resume(obj.coach.user_id, obj.chemical_interview_module.project_member.project_id)
        if state:
            return resume.head_image_url if resume.head_image_url else obj.coach.user.head_image_url
        else:
            return obj.coach.user.head_image_url

    def get_wework_qr_code(self, obj):
        work_wechat_user = WorkWechatUser.objects.filter(user_id=obj.coach.user_id, deleted=False).first()
        if work_wechat_user and work_wechat_user.qr_code:
            return work_wechat_user.qr_code
        return None

    def get_coach_count(self, obj):
        chemical_interview_module = obj.chemical_interview_module
        max_interview_number = chemical_interview_module.max_interview_number
        exists_interview_count = chemical_interview_module.coaches.filter(deleted=False,
                                                                          interview__isnull=False).count()
        return max_interview_number - exists_interview_count


    def get_interview_time(self, obj):
        return f"{obj.interview.public_attr.start_time.strftime('%Y.%m.%d %H:%M')}-{obj.interview.public_attr.end_time.strftime('%H:%M')}"

    def get_coach_aspect(self, obj):
        base = [{"name": "教练经验", "isChecked": False}, {"name": "行业背景", "isChecked": False},
                {"name": "管理经验", "isChecked": False}, {"name": "业务背景", "isChecked": False},
                {"name": "年龄", "isChecked": False}, {"name": "性别", "isChecked": False},
                {"name": "个性特点", "isChecked": False}]
        if obj.coach_aspect:
            coach_aspect = obj.coach_aspect.split(',')
            for c in coach_aspect:
                for item in base:
                    if c == item['name']:
                        item['isChecked'] = True
        return base