import datetime

from django.db import transaction
from rest_framework import serializers
from drf_yasg import openapi
from rest_framework import viewsets
from drf_yasg.utils import swagger_auto_schema
from rest_framework.decorators import action

from django.conf import settings

from utils.authentication import get_token
from utils.feishu_robot import push_wx_error_message
from utils import validate
from utils.wechat_oauth import WeChatOauth
from wisdom_v2.common import personal_user_public
from wisdom_v2.enum.service_content_enum import UserInviteTypeEnum, PersonaSourceEnum, CoachAuthEnum, \
    CoachWorkingYearsEnum, ActivityCoachStatusEnum
from wisdom_v2.enum.user_enum import BrowseRecordObjectEnum
from wisdom_v2.models import User, PersonalUser, UserInviteRecord, Resume, ProjectInterview
from utils.api_response import parameter_error_response
from wisdom_v2.models import UserTmp
from utils.api_response import success_response
from wisdom_v2.models_file import ActivityCoach
from wisdom_v2.models_file.coach import BrowseRecord
from wisdom_v2.views.user_actions import UserSerializer


class AppUserTmpViewSerializers(serializers.ModelSerializer):
    class Meta:
        model = UserTmp
        exclude = ('updated_at', 'created_at')


class AppUserViewSet(viewsets.ModelViewSet):
    queryset = User.objects
    serializer_class = UserSerializer

    @swagger_auto_schema(
        operation_id='个人用户微信授权登录',
        operation_summary='个人用户微信授权登录',
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'code': openapi.Schema(type=openapi.TYPE_NUMBER, description='微信返回code'),
                'invite_code': openapi.Schema(type=openapi.TYPE_NUMBER, description='邀请码'),
                'phone_code': openapi.Schema(type=openapi.TYPE_NUMBER, description='小程序获取手机号微信返回code')
            }
        ),
        tags=['小程序用户相关']
    )
    @action(methods=['post'], detail=False, url_path='personal/wechat_login', authentication_classes=[])
    def personal_wechat_login(self, request, *args, **kwargs):
        try:
            code = request.data['code']
            invite_code = request.data.get('invite_code')
            phone_code = request.data['phone_code']
        except:
            return parameter_error_response()
        access_token_response = WeChatOauth().jscode2session(code)
        if access_token_response.get('err'):
            return success_response(access_token_response.get('err'))
        unionid = access_token_response.get('unionid')
        openid = access_token_response.get('openid')

        # 通过unionid判断用户是否已经绑定过个人用户表
        persona_user = PersonalUser.objects.filter(user__unionid=unionid, user__deleted=False, deleted=False).first()

        # 是否添加新用户
        is_add_user = False
        # 邀请人信息
        channel1 = None
        channel2 = None
        channel3 = None

        if persona_user:
            user = persona_user.user
        else:
            # 通过unionid和openid查询小程序用户是否存在
            user = User.objects.filter(unionid=unionid, deleted=False).first()
            if not user:
                # 没有的情况下再手机号查询用户信息
                phone_response = WeChatOauth().get_phone_number(phone_code)
                if phone_response.get('err'):
                    return success_response(phone_response.get('err'))
                phone = phone_response.get('phone_info').get('phoneNumber')
                user = User.objects.filter(phone=phone, deleted=False).first()

                # 一个手机号可以注册多个微信号，不同微信号的unionid一致
                # 在手机号查询到用户，用户已有unionid，但是用户的unionid和当前查询出来的unionid不一致，我们认为是多微信号注册的情况，需要拦截。
                if user and user.unionid and unionid != user.unionid:
                    push_wx_error_message(
                        name='个人用户微信授权登录失败', level='error',
                        content=f'手机号:{phone}，已绑定用户:{user.id}，unionid:{unionid}, openid:{openid}，invite_code:{invite_code}')
                    return parameter_error_response('登录失败，请使用该手机号绑定的另一微信账号登录')
                try:
                    with transaction.atomic():
                        # 手机号查询到则更新unionid和openid
                        if user:
                            user.unionid = unionid
                            user.openid = openid
                            user.save()
                        else:
                            # 没查到则创建用户
                            if User.objects.filter(name=phone, deleted=False).exists():
                                push_wx_error_message(
                                    name='个人用户微信授权登录失败', level='error',
                                    content=f'用户名:{phone}出现重复')
                                return parameter_error_response('该账号信息错误，请使用其他账号登录')
                            user = User.objects.create(
                                unionid=unionid, openid=openid, phone=phone, name=phone, true_name=phone)
                            is_add_user = True
                except Exception as e:
                    push_wx_error_message(
                        name='个人用户微信授权登录失败', level='error',
                        content=f'error{str(e)}, unionid:{unionid}')
                    return parameter_error_response('该账号信息错误，请使用其他账号登录')
            # 绑定个人用户表
            personal_user_data = {
                'user': user,
                'email': user.email,
                'source': PersonaSourceEnum.natural_flow
            }
            if invite_code:
                # 是否有邀请记录
                invite = UserInviteRecord.objects.filter(
                    pk=invite_code, deleted=False).first()
                if not invite:
                    return parameter_error_response('邀请码信息错误')
                personal_user_data['invite'] = invite

                # 邀请记录类型判断来源
                if invite.type == UserInviteTypeEnum.coach_resume:
                    if invite.object_id == str(invite.referrer_id):
                        personal_user_data['source'] = PersonaSourceEnum.coach_recommend
                    elif invite.object_id != str(invite.referrer_id):
                        personal_user_data['source'] = PersonaSourceEnum.coachee_recommend
                elif invite.type == UserInviteTypeEnum.interview:
                    personal_user_data['source'] = PersonaSourceEnum.coach_invitation
                elif invite.type == UserInviteTypeEnum.activity.value:
                    # 类型是活动，有邀请人，
                    if invite.referrer_id:
                        # 邀请人是免邀请或已加入的教练，用户来源=教练邀请
                        if ActivityCoach.objects.filter(
                                status__in=[ActivityCoachStatusEnum.joined.value,
                                            ActivityCoachStatusEnum.not_invitation.value],
                                coach__user_id=invite.referrer_id,
                                activity_id=invite.object_id, deleted=False).exists():
                            personal_user_data['source'] = PersonaSourceEnum.coach_invitation
                        # 邀请人不是教练，活动运营邀请，用户来源=运营活动
                        else:
                            personal_user_data['source'] = PersonaSourceEnum.activity
                    else:
                        personal_user_data['source'] = PersonaSourceEnum.activity
                elif invite.type == UserInviteTypeEnum.coach_share.value:
                    personal_user_data['source'] = PersonaSourceEnum.coach_recommend
                elif invite.type == UserInviteTypeEnum.wechat_official_account.value:
                    personal_user_data['source'] = PersonaSourceEnum.wechat_official_account.value  # 微信公众号
                elif invite.type == UserInviteTypeEnum.wechat_official_account_article.value:
                    personal_user_data['source'] = PersonaSourceEnum.wechat_official_account_article.value  # 微信公众号文章
                elif invite.type == UserInviteTypeEnum.video_live.value:
                    personal_user_data['source'] = PersonaSourceEnum.video_live.value  # 视频号直播
                elif invite.type == UserInviteTypeEnum.sms.value:
                    personal_user_data['source'] = PersonaSourceEnum.sms.value  # 短信链接

                # 如果是新增用户，返回渠道信息
                if is_add_user:
                    channel1, channel2, channel3 = personal_user_public.invite_code_displace_channel(invite_code)
            PersonalUser.objects.create(**personal_user_data)
        token = get_token(user, is_backend=True)

        # 个人用户user_role置空
        user_data = UserSerializer(user).data
        user_data['user_role'] = []

        user.openid = openid
        user.last_active = datetime.datetime.now()
        user.save()

        return success_response({
            'token': token, 'user': user_data, 'is_add_user': is_add_user,
            'channel1': channel1, 'channel2': channel2, 'channel3': channel3})

    @swagger_auto_schema(
        operation_id='查看用户填写临时信息',
        operation_summary='查看用户填写临时信息',
        manual_parameters=[
            openapi.Parameter('data_id', openapi.IN_QUERY, description='数据id', type=openapi.TYPE_NUMBER),
            openapi.Parameter('extra_id', openapi.IN_QUERY, description='数据额外标识id', type=openapi.TYPE_NUMBER),
            openapi.Parameter('user_id', openapi.IN_QUERY, description='用户id', type=openapi.TYPE_NUMBER),
            openapi.Parameter('type', openapi.IN_QUERY, description='数据类型 | 1:辅导记录问卷 2:教练任务 3:测评信息 4:小程序-简历信息',
                              type=openapi.TYPE_NUMBER),
        ],
        tags=['小程序用户相关']
    )
    @action(methods=['get'], detail=False, url_path='tmp/details', authentication_classes=[])
    def get_user_tmp(self, request, *args, **kwargs):

        self.serializer_class = AppUserTmpViewSerializers
        user_id = request.query_params.get('user_id')
        extra_id = request.query_params.get('extra_id')

        token_user_id = validate.get_not_token_user_id(request)
        user_id = user_id if user_id else token_user_id
        user_tmp_data = UserTmp.objects.filter(
            data_id=request.query_params.get('data_id'),
            user_id=user_id,
            type=request.query_params.get('type')
        )
        if extra_id:
            user_tmp_data = user_tmp_data.filter(extra_id=extra_id)

        serializer = self.get_serializer(user_tmp_data.first())
        return success_response(serializer.data)

    @swagger_auto_schema(
        operation_id='创建（更新）用户填写临时信息',
        operation_summary='创建（更新）用户填写临时信息',
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'content': openapi.Schema(type=openapi.TYPE_STRING, description='临时数据'),
                'data_id': openapi.Schema(type=openapi.TYPE_STRING, description='数据id'),
                'user_id': openapi.Schema(type=openapi.TYPE_STRING, description='用户id'),
                'extra_id': openapi.Schema(type=openapi.TYPE_STRING, description='数据额外标识id'),
                'type': openapi.Schema(type=openapi.TYPE_STRING, description='数据类型 | 1:辅导记录问卷')}
        ),
        tags=['小程序用户相关']
    )
    @action(methods=['post'], detail=False, url_path='tmp/record', authentication_classes=[])
    def add_user_tmp(self, request, *args, **kwargs):

        self.serializer_class = AppUserTmpViewSerializers
        user_id = request.data.get('user_id')
        extra_id = request.data.get('extra_id')
        token_user_id = validate.get_not_token_user_id(request)

        user_id = user_id if user_id else token_user_id

        user_tmp_data = UserTmp.objects.filter(
            data_id=request.data.get('data_id'), user_id=user_id,
            type=request.data.get('type')
        )
        if extra_id:
            user_tmp_data.filter(extra_id=extra_id)
        user_tmp_data = user_tmp_data.first()
        if not user_tmp_data:
            user_tmp_data = UserTmp(
                data_id=request.data.get('data_id'),
                extra_id=extra_id,
                user_id=user_id,
                type=request.data.get('type')
            )
        user_tmp_data.content = request.data.get('content')
        user_tmp_data.save()
        serializer = self.get_serializer(user_tmp_data)
        return success_response(serializer.data)

    @swagger_auto_schema(
        operation_id='获取用户分享二维码',
        operation_summary='获取用户分享二维码',
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'object_id': openapi.Schema(type=openapi.TYPE_STRING, description='对象id type=1时传入教练用户id type=2时传入辅导记录id  type=3时传入活动id'),
                'referrer_id': openapi.Schema(type=openapi.TYPE_STRING, description='推荐人用户id'),
                'type': openapi.Schema(type=openapi.TYPE_STRING, description='邀请类型 | 1:教练简历 2-辅导邀请 3-活动邀请')}
        ),
        tags=['小程序用户相关']
    )
    @action(methods=['post'], detail=False, url_path='invite/add')
    def post_user_invite_add(self, request, *args, **kwargs):
        try:
            object_id = request.data.get('object_id')
            referrer_id = request.data.get('user_id')
            invite_type = int(request.data.get('type'))
        except Exception as e:
            return parameter_error_response(str(e))
        if not object_id:
            return parameter_error_response('未获取到对象id')
        if not invite_type:
            return parameter_error_response('未获取到分享类型')
        if invite_type == UserInviteTypeEnum.coach_resume:
            referrer_user = User.objects.get(pk=referrer_id, deleted=False)
            resume = Resume.objects.filter(coach__user_id=object_id, deleted=False, is_customization=False).first()
            if not resume:
                return parameter_error_response('未获取到教练主简历信息')
            if resume.coach.price is None:
                return parameter_error_response('请在我的-教练简历中设置单小时收费')

            invite_record = UserInviteRecord.objects.create(
                type=invite_type, object_id=object_id,
                referrer=referrer_user, deleted=False
            )

            url = f'{settings.SITE_URL}landing/?p=/pages/user/myResume&invite_code={invite_record.uuid}&resume_id={resume.id}'
            coach_auth = CoachAuthEnum.get_display(resume.coach_auth) \
                if resume.coach_auth in CoachAuthEnum.get_describe_keys() else None
            working_years = f'{CoachWorkingYearsEnum.get_display(resume.working_years)}教练经验' if resume.working_years else None

            data = {
                'head_image_url': resume.head_image_url,
                'true_name': resume.coach.personal_name,
                'city': resume.coach.city,
                'coach_auth': coach_auth,
                'working_years': working_years,
                'coach_domain': resume.coach_domain[:3] if resume.coach_domain else [],
                'posters_text': resume.coach.posters_text,
                'invite_url': url,
                'invite_code': invite_record.uuid,
                'resume_id': resume.id,
            }
            if referrer_id != object_id:
                resuem_browse_record, is_create = BrowseRecord.objects.get_or_create(
                    user_id=object_id, object_type=BrowseRecordObjectEnum.resume.value, object_id=resume.id, deleted=False)
                resuem_browse_record.total_shares += 1
                resuem_browse_record.save()

            return success_response(data)
        elif invite_type == UserInviteTypeEnum.interview:
            try:
                interview = ProjectInterview.objects.get(pk=object_id)
                if referrer_id:
                    referrer_user = User.objects.get(pk=referrer_id, deleted=False)
                else:
                    referrer_user = interview.public_attr.user
            except Exception as e:
                return parameter_error_response(str(e))
            invite_record = UserInviteRecord.objects.create(
                type=invite_type, object_id=object_id,
                referrer=referrer_user, deleted=False
            )
            data = {
                'invite_code': invite_record.uuid,
            }
            return success_response(data)

        elif invite_type == UserInviteTypeEnum.activity:
            resume = Resume.objects.filter(coach__user_id=referrer_id, deleted=False, is_customization=False).first()
            if not resume:
                return parameter_error_response('未获取到教练主简历信息')
            invite_record = UserInviteRecord.objects.create(
                type=invite_type, object_id=object_id,
                referrer_id=referrer_id, deleted=False
            )
            url = f'{settings.SITE_URL}landing/?p=/pages/user/myResume&invite_code={invite_record.uuid}&resume_id={resume.id}'
            coach_auth = CoachAuthEnum.get_display(resume.coach_auth) \
                if resume.coach_auth in CoachAuthEnum.get_describe_keys() else None
            working_years = f'{CoachWorkingYearsEnum.get_display(resume.working_years)}教练经验' if resume.working_years else None

            data = {
                'head_image_url': resume.head_image_url,
                'true_name': resume.coach.personal_name,
                'city': resume.coach.city,
                'coach_auth': coach_auth,
                'working_years': working_years,
                'coach_domain': resume.coach_domain[:3] if resume.coach_domain else [],
                'posters_text': resume.coach.posters_text,
                'invite_url': url,
                'invite_code': invite_record.uuid,
                'resume_id': resume.id,
            }
            return success_response(data)

        else:
            return parameter_error_response('暂不支持其他邀请类型')

    @swagger_auto_schema(
        operation_id='修改个人用户信息',
        operation_summary='修改个人用户信息',
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'user_id': openapi.Schema(type=openapi.TYPE_NUMBER, description='用户id'),
                'nickname': openapi.Schema(type=openapi.TYPE_STRING, description='昵称'),
                'email': openapi.Schema(type=openapi.TYPE_STRING, description='邮箱')
            }
        ),
        tags=['小程序用户相关']
    )
    @action(methods=['post'], detail=False, url_path='personal/update')
    def update_personal_user_data(self, request, *args, **kwargs):
        try:
            user_id = request.data['user_id']
            nickname = request.data.get('nickname')
            email = request.data.get('email')
            personal_user = PersonalUser.objects.get(user_id=user_id, deleted=False)
        except PersonalUser.DoesNotExist:
            return parameter_error_response('用户不存在')
        except Exception as e:
            return parameter_error_response()

        if email and not validate.validate_email(email):
            return parameter_error_response('邮箱格式错误')
        if email:
            personal_user.email = email
        if nickname:
            personal_user.nickname = nickname
        personal_user.save()
        return success_response()

    @swagger_auto_schema(
        operation_id='绑定手机号',
        operation_summary='绑定手机号',
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'phone_code': openapi.Schema(type=openapi.TYPE_STRING, description='手机号验证码'),
                'user_id': openapi.Schema(type=openapi.TYPE_NUMBER, description='用户id')
            }
        ),
        tags=['小程序用户相关']
    )
    @action(methods=['post'], detail=False, authentication_classes=[])
    def bind_phone(self, request, *args, **kwargs):
        try:
            phone_code = request.data['phone_code']
            user_id = request.data['user_id']
            user = User.objects.get(pk=user_id, deleted=False)
        except User.DoesNotExist:
            return parameter_error_response('用户不存在')
        except Exception as e:
            return parameter_error_response(str(e))
        phone_response = WeChatOauth().get_phone_number(phone_code)
        if phone_response.get('err'):
            return success_response(phone_response.get('err'))
        phone = phone_response.get('phone_info').get('phoneNumber')

        user.phone = phone
        user.save()

        return success_response()
