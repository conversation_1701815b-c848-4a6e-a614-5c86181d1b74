import json

from drf_yasg import openapi
from drf_yasg.utils import swagger_auto_schema
from rest_framework.decorators import action
from rest_framework import viewsets

from utils.api_response import success_response, parameter_error_response
from utils.miniapp_version_judge import compare_version
from wisdom_v2.app_views.app_activity_actions import AppActivityDetailSerializer, AppActivityCoachDetailSerializer
from wisdom_v2.enum.service_content_enum import CoachAuthEnum
from wisdom_v2.models_file import Activity, ActivityCoach


class AppActivityViewSet(viewsets.ModelViewSet):
    queryset = Activity.objects.filter()
    serializer_class = AppActivityDetailSerializer

    @swagger_auto_schema(
        operation_id='获取活动详情',
        operation_summary='获取活动详情',
        manual_parameters=[
            openapi.Parameter('activity_id', openapi.IN_QUERY, description='活动标识', type=openapi.TYPE_NUMBER,
                              required=True),
        ],
        tags=['活动运营']
    )
    @action(methods=['get'], detail=False, url_path='details', authentication_classes=[])
    def get_activity_detail(self, request, *args, **kwargs):
        try:
            activity_id = request.query_params.get('activity_id')
            activity = Activity.objects.get(id=activity_id)
            mp = json.loads(request.headers.get('mp')) if request.headers.get('mp') else None
        except Activity.DoesNotExist:
            return parameter_error_response('活动不存在')
        except Exception as e:
            return parameter_error_response()
        serializer = AppActivityDetailSerializer(activity).data

        if mp and compare_version(mp.get('version'), '2.23') < 0:
            for item in serializer.get('coach_content'):
                # 旧版本小程序没有5选项，会报错，需要处理
                if item['coach_auth'] == CoachAuthEnum.PREPARE_ICF_ACC.value:
                    item['coach_auth'] = 0  # 2.23版本前小程序中coach_auth=0等于“无”，后续不用处理，在此处兼容返回0
        return success_response(serializer)

    @swagger_auto_schema(
        operation_id='获取活动教练详情',
        operation_summary='获取活动教练详情',
        manual_parameters=[
            openapi.Parameter('activity_coach_id', openapi.IN_QUERY, description='活动教练标识', type=openapi.TYPE_NUMBER, required=True),
        ],
        tags=['活动运营']
    )
    @action(methods=['get'], detail=False, url_path='coach/details')
    def get_activity_coach_detail(self, request, *args, **kwargs):
        try:
            activity_coach_id = request.query_params.get('activity_coach_id')
            activity_coach = ActivityCoach.objects.get(id=activity_coach_id)
        except ActivityCoach.DoesNotExist:
            return parameter_error_response('活动教练不存在')
        except Exception as e:
            return parameter_error_response()
        serializer = AppActivityCoachDetailSerializer(activity_coach)
        return success_response(serializer.data)


    @swagger_auto_schema(
        operation_id='修改活动教练详情',
        operation_summary='修改活动教练详情',
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'activity_coach_id': openapi.Schema(type=openapi.TYPE_STRING, description='教练活动标识'),
                'status': openapi.Schema(type=openapi.TYPE_NUMBER, description='教练活动状态'),
                'image_url': openapi.Schema(type=openapi.TYPE_STRING, description='教练海报链接'),
            }),
        tags=['活动运营']
    )
    @action(methods=['post'], detail=False, url_path='coach/update')
    def update_activity_coach_detail(self, request, *args, **kwargs):
        try:
            activity_coach_id = request.data.get('activity_coach_id')
            status = request.data.get('status')
            image_url = request.data.get('image_url')
            activity_coach = ActivityCoach.objects.get(id=activity_coach_id)
        except ActivityCoach.DoesNotExist:
            return parameter_error_response('未获取到活动信息')
        except Exception as e:
            return parameter_error_response()
        if status:
            activity_coach.status = status
        if image_url:
            activity_coach.image_url = image_url
        activity_coach.save()
        serializer = AppActivityCoachDetailSerializer(activity_coach)
        return success_response(serializer.data)