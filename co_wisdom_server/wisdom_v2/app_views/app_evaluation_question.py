from drf_yasg import openapi
from rest_framework.decorators import action
from drf_yasg.utils import swagger_auto_schema
from rest_framework.viewsets import GenericViewSet

from utils import validate
from utils.api_response import success_response, parameter_error_response

from .app_evaluation_option import EvaluationOptionSerializers
from ..models import Evaluation, EvaluationQuestion, EvaluationOption, PublicAttr, User, EvaluationModule
from rest_framework import serializers

from ..views.constant import MANAGE_EVALUATION, LBI_EVALUATION, ATTR_TYPE_EVALUATION_ANSWER


class EvaluationQuestionSerializers(serializers.ModelSerializer):
    option = serializers.SerializerMethodField()

    class Meta:
        model = EvaluationQuestion
        fields = ('id', 'order', 'title', 'multi', 'evaluation', 'option')

    def get_option(self, obj):
        option_list = EvaluationOption.objects.filter(question=obj, deleted=False).order_by('order')
        return EvaluationOptionSerializers(option_list, many=True).data


class EvaluationQuestionViewSet(GenericViewSet):
    queryset = EvaluationQuestion.objects.filter(deleted=False)
    serializer_class = EvaluationQuestionSerializers

    @swagger_auto_schema(
        operation_id='测评问题',
        operation_summary='测评问题',
        manual_parameters=[
            openapi.Parameter('evaluation_id', openapi.IN_QUERY, description='测评id', type=openapi.TYPE_NUMBER,
                              required=True)
        ],
        tags=['app测评']
    )
    @action(methods=['get'], detail=False, url_path='list', authentication_classes=[])
    def question_list(self, request):
        try:
            evaluation_id = int(request.query_params.get('evaluation_id', 0))
            project_id = request.query_params.get('project_id', 0)
            evaluation = Evaluation.objects.get(pk=evaluation_id)
            token_user_id = validate.get_not_token_user_id(request)
            user_id = token_user_id if token_user_id else request.query_params.get('user_id')
            master_user_id = request.query_params.get('master_user_id')

            User.objects.get(pk=user_id)

        except Evaluation.DoesNotExist:
            return parameter_error_response('测评id错误')
        except Exception as e:
            return parameter_error_response(str(e))

        evaluation_module = EvaluationModule.objects.filter(
            evaluation_id=evaluation_id,
            project_bundle__project_member__user_id=master_user_id if master_user_id else user_id,
            deleted=False
        ).first()
        if not evaluation_module:
            return parameter_error_response('该用户暂未配置测评信息')

        if evaluation.code == MANAGE_EVALUATION:
            public_attr = PublicAttr.objects.filter(
                user_id=user_id,
                target_user__isnull=True,
                type=ATTR_TYPE_EVALUATION_ANSWER)
        elif evaluation.code == LBI_EVALUATION:
            public_attr = PublicAttr.objects.filter(
                user_id=user_id,
                target_user=master_user_id if master_user_id else user_id,
                type=ATTR_TYPE_EVALUATION_ANSWER)
        else:
            return parameter_error_response('测评编号错误')
        if project_id:
            public_attr = public_attr.filter(
                project_id=project_id
            )
        if public_attr.exists():
            return success_response({'is_fill_out_question': True})

        question_list = self.get_queryset().filter(evaluation=evaluation).order_by('order')
        response = self.serializer_class(question_list, many=True).data

        return success_response(response, request=request)
