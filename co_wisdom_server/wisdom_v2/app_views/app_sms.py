import time
import json
import redis
import random
import datetime

from drf_yasg import openapi
from rest_framework.views import APIView
from drf_yasg.utils import swagger_auto_schema

from utils.messagecenter.sms import SendSMS
from utils.message.email import message_send_email_base
from utils.api_response import success_response, parameter_error_response
from wisdom_v2.models import User
code_redis = redis.Redis.from_url('redis://127.0.0.1:6379/3')


def check_phone(phone):
    values = code_redis.get('code_' + phone)

    if not values:
        # 第一次请求
        ex_time = int(time.mktime((datetime.datetime.now().date() + datetime.timedelta(days=1)).timetuple())) - int(
            time.time())
        sms_code = str(random.randint(100000, 1000000))
        values = {'sms_code': sms_code,
                  'send_phone': phone,
                  'last_time': int(time.time()),
                  'send_time': int(time.time()),
                  'sms_count': 1}
        code_redis.set('code_' + str(phone), json.dumps(values), ex=ex_time)
        return False, sms_code
    else:
        values_dict = json.loads(values)
        last_time = datetime.datetime.fromtimestamp(values_dict.get('last_time')) + datetime.timedelta(minutes=1)
        if values_dict.get('sms_count') >= 10:
            return True, '今日验证码已达上限，请明日再试'
        if last_time > datetime.datetime.now():
            return True, '操作太频繁啦，请稍后重试'
        sms_code = str(random.randint(100000, 1000000))
        values_dict['sms_code'] = sms_code
        values_dict['last_time'] = int(time.time())
        values_dict['send_time'] = int(time.time())
        values_dict['sms_count'] += 1
        ex_time = int(time.mktime((datetime.datetime.now().date() + datetime.timedelta(days=1)).timetuple())) - int(
            time.time())
        code_redis.set('code_' + str(phone), json.dumps(values_dict), ex=ex_time)
        return False, sms_code


def check_and_generate_email_code(email):
    """
    检查是否允许向指定邮箱发送新的验证码，并在允许的情况下生成验证码。

    参数:
    email (str): 需要检查并发送验证码的邮箱地址。

    返回:
    tuple: 包含一个布尔值和一个字符串的元组。
           布尔值指示是否限制发送（True表示限制）。
           字符串要么是错误消息，要么是生成的验证码。
    """

    # Redis存储的基础键
    base_redis_key = 'code_' + email
    # 从Redis列表获取所有与该邮箱相关的验证码键
    all_keys = code_redis.keys(f'{base_redis_key}_*')

    # 如果已有验证码数量大于等于3，则禁止发送新验证码
    if len(all_keys) >= 3:
        return True, '操作太频繁啦，请稍后重试'

    # 生成新的验证码，并保存到Redis中，设置有效期为5分钟
    email_code = str(random.randint(100000, 1000000))
    code_redis.set(f'{base_redis_key}_{email_code}', '1', ex=60*5)
    return False, email_code


class SmsCode(APIView):
    authentication_classes = []

    @swagger_auto_schema(
        operation_id='获取验证码',
        operation_summary='获取验证码',
        manual_parameters=[
            openapi.Parameter('phone', openapi.IN_QUERY, description='手机号', type=openapi.TYPE_STRING)
        ],
        tags=['用户相关']
    )
    def get(self, request):
        try:
            phone = str(request.query_params.get('phone'))
        except (TypeError, ValueError):
            return parameter_error_response()

        # 判定用户在本系统存在,才会发送验证码
        try:
            user = User.objects.get(phone=phone, deleted=False)
        except User.DoesNotExist:
            return parameter_error_response('该手机号不存在，请重新输入')
        flag, res = check_phone(phone)
        if flag:
            return parameter_error_response(res)

        SendSMS().send_base_sms(phone, sms_str='验证码：%s，有效期5分钟，此验证码用于修改登录密码，请勿告知他人' % res)
        return success_response()


class EmailCode(APIView):
    authentication_classes = []

    @swagger_auto_schema(
        operation_id='获取验证码',
        operation_summary='获取验证码',
        manual_parameters=[
            openapi.Parameter('email', openapi.IN_QUERY, description='邮箱', type=openapi.TYPE_STRING)
        ],
        tags=['用户相关']
    )
    def get(self, request):
        try:
            email = request.query_params.get('email')
            user = User.objects.get(email=email, deleted=False)
        except (TypeError, ValueError, User.DoesNotExist):
            return parameter_error_response('该用户不存在，请重新输入')
        flag, res = check_and_generate_email_code(email)
        if flag:
            return parameter_error_response(res)
        param = {'user_name': user.cover_name, 'code': res}
        message_send_email_base.delay('change_user_password', param, [email], receiver_ids=user.id)
        return success_response()


class VerifyCode(APIView):
    authentication_classes = []

    @swagger_auto_schema(
        operation_id='验证验证码是否正确',
        operation_summary='验证验证码是否正确',
        manual_parameters=[
            openapi.Parameter('code', openapi.IN_QUERY, description='验证码', type=openapi.TYPE_NUMBER),
            openapi.Parameter('phone', openapi.IN_QUERY, description='手机号', type=openapi.TYPE_STRING),
            openapi.Parameter('email', openapi.IN_QUERY, description='邮箱', type=openapi.TYPE_STRING)
        ],
        tags=['用户相关']
    )
    def get(self, request):
        try:
            code = int(request.query_params.get('code', None))
            phone = request.query_params.get('phone')
            email = request.query_params.get('email')
        except (TypeError, ValueError):
            return parameter_error_response()

        if phone:
            values = code_redis.get('code_' + str(phone))

            if not values:
                return parameter_error_response('请先获取验证码')

            values_dict = json.loads(values)
            ex_time = datetime.datetime.fromtimestamp(values_dict.get('send_time')) + datetime.timedelta(minutes=5)

            if ex_time < datetime.datetime.now():
                return parameter_error_response('验证码已失效，请重新获取')
            if code != int(values_dict['sms_code']):
                return parameter_error_response('验证码错误，请输入最新发送的验证码')
        elif email:
            email_redis_key = 'code_' + email + '_' + str(code)
            if not code_redis.get(email_redis_key):
                return parameter_error_response('验证码错误，请重新输入')
        else:
            return parameter_error_response()
        return success_response('验证成功')
