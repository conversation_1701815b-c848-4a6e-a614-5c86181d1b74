import redis
import datetime
import random
import logging

from wisdom_v2.common import user_public, coachee_public
from wisdom_v2.enum.user_enum import User<PERSON><PERSON>Enum
from wisdom_v2.views.constant import BIZ_CODE_USER_DISABLE_LOGIN

_logging = logging.getLogger('others')

from django.db import transaction
from drf_yasg import openapi
from rest_framework import viewsets
from rest_framework.views import APIView
from drf_yasg.utils import swagger_auto_schema
from wisdom_v2.models import User, Project, ProjectInterview
from wisdom_v2.views.user_actions import UserSerializer
from rest_framework.decorators import action
from utils.api_response import success_response, parameter_error_response, biz_error_response
from utils.wechat_oauth import WeChatOauth, AppWechatOauth
from utils import validate, redis_public
from utils.authentication import get_token
from wisdom_v2.enum.project_interview_enum import ProjectInterviewPlaceCategoryEnum, GroupCoachTypeEnum
from wisdom_v2.views.company_member_actions import id_number_check
from utils.two_factor_certification import TwoFactorCertification

WeChat_Register_URL = 'redis://127.0.0.1:6379/5'
Two_Factor_Certification_URL = 'redis://127.0.0.1:6379/6'
email_verification_code_redis = redis.Redis.from_url(WeChat_Register_URL)
two_factor_certification_redis = redis.Redis.from_url(Two_Factor_Certification_URL)


class WechatRegisterViewSet(viewsets.ModelViewSet):
    authentication_classes = []
    queryset = User.objects.filter(deleted=False).order_by('-created_at')
    serializer_class = UserSerializer


    @swagger_auto_schema(
        operation_id='小程序/app登录',
        operation_summary='小程序/app登录',
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'code': openapi.Schema(type=openapi.TYPE_NUMBER, description='微信返回code'),
                'source': openapi.Schema(type=openapi.TYPE_NUMBER, description='1-小程序， 2-app'),
                'phone_code': openapi.Schema(type=openapi.TYPE_NUMBER, description='小程序获取手机号微信返回code，'
                                                                                   '小程序登录必传，app端不传')
            }
        ),
        tags=['微信扫码注册相关']
    )
    @action(methods=['post'], detail=False, url_path='wechat_login')
    def wechat_login(self, request, *args, **kwargs):
        try:
            code = request.data['code']
            source = request.data['source']
            if source == 1:
                phone_code = request.data['phone_code']
        except:
            return parameter_error_response()
        if source == 1:
            response = WeChatOauth().jscode2session(code)
        elif source == 2:
            access_token_response = AppWechatOauth().get_access_token(code)
            if access_token_response.get('err'):
                return success_response(access_token_response.get('err'))
            access_token = access_token_response.get('access_token')
            openid = access_token_response.get('openid')
            response = AppWechatOauth().get_userinfo(access_token, openid)
        else:
            return parameter_error_response()
        if response.get('err'):
            return success_response(response.get('err'))
        unionid = response.get('unionid')
        user = User.objects.filter(unionid=unionid, deleted=False).first()
        if not user:
            if source == 1:  # 小程序获取手机号 个人教练首次登录
                phone_response = WeChatOauth().get_phone_number(phone_code)
                if phone_response.get('err'):
                    return success_response(phone_response.get('err'))
                phone = phone_response.get('phone_info').get('phoneNumber')
                user = User.objects.filter(phone=phone, deleted=False).first()
                if user:
                    openid = response.get('openid')
                    user.unionid = unionid
                    user.openid = openid
                    user.save()
                else:
                    return success_response({'status': 1, 'msg': '当前用户不存在'})

            else:  # app无法获取手机号直接返回用户不存在
                return success_response({'status': 1, 'msg': '当前用户不存在'})

        # B端用户项目是已输单地禁止登录。
        status = coachee_public.user_is_login_allowed(user.id)
        if not status:
            return biz_error_response(
                BIZ_CODE_USER_DISABLE_LOGIN,
                '您的企业已关闭该服务。如果您仍需要教练服务，可以通过个人版小程序预约更多教练。'
            )
        token = get_token(user, is_backend=True)
        user_data = UserSerializer(user).data
        user_role = user_public.del_specified_user_permissions(
            user_data.get('role'), [UserRoleEnum.trainee_coachee.value])
        if not user_role:
            return success_response({'status': 1, 'msg': '当前用户无登录权限'})
        user_data['role'] = user_role

        # 更新登录时间
        user.last_active = datetime.datetime.now()
        user.save()
        return success_response({'token': token, 'user': user_data})


class TwoFactorCertificationViews(APIView):

    @swagger_auto_schema(
        operation_id='二要素认证接口',
        operation_summary='二要素认证接口',
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'idcard': openapi.Schema(type=openapi.TYPE_NUMBER, description='用户身份证号'),
                'name': openapi.Schema(type=openapi.TYPE_STRING, description='用户姓名'),
                'user_id': openapi.Schema(type=openapi.TYPE_NUMBER, description='用户id')
            }
        ),
        tags=['微信小程序接口']
    )
    def post(self, request):
        try:
            idcard = request.data['idcard']
            name = request.data['name']
            user_id = request.data['user_id']
            user = User.objects.get(pk=user_id)
        except:
            return parameter_error_response()
        data = {"is_certification": None, "message": None}

        count = two_factor_certification_redis.get(user_id)
        if count:
            count = int(count.decode())
            if count >= 5:
                data['is_certification'], data['message'] = False, '实名认证次数已达上限，请明日再试'
                return success_response(data)
        else:
            count = 0

        if not id_number_check(idcard):
            data['is_certification'], data['message'] = False, '身份证错误'
            return success_response(data)

        body = {"idcard": idcard, "name": name}
        _logging.info('Request Start 二要素认证信息 user_id: {0}, idcard: {1}, name: {2}'.format(user_id, idcard, name))
        is_certification, msg = TwoFactorCertification().get_result(body=body)
        _logging.info('Request Success 二要素认证信息 user_id: {0}, idcard: {1}, name: {2}'.format(user_id, idcard, name))

        if not is_certification:
            data['is_certification'], data['message'] = False, '认证失败，姓名和身份证号不匹配'
            redis_public.save_today_end_redis(
                two_factor_certification_redis, user_id, count + 1)

            return success_response(data)
        redis_public.save_today_end_redis(
            two_factor_certification_redis, user_id, count + 1)
        user.true_name = name
        user.is_identity_check = True
        user.save()

        return success_response()
