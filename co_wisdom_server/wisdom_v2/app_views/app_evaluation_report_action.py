import copy
import math

from django.db.models import Avg, Sum

from ..models import EvaluationOption, PublicAttr, EvaluationReport, EvaluationReportScore
from ..views.constant import ATTR_TYPE_EVALUATION_REPORT

TOPIC_MAP = [[1, 4, 7, 8, 17],  # 'understand'
             [2, 3, 5, 6, 10],  # 'target':
             [9, 11, 18],  # 'enlighten':
             [12, 14, 15],  # 'action':
             [13, 16, 19, 20]  # 'progress':
             ]
ALL_SCORE_MAP = {
    'score_20_40': {
        'analysis': '''
        <p>教练能力处于你管理工具箱中的&ldquo;未知区&ldquo;。</p>
<p>这类管理者没有明显的教练型领导者特点，常常聚焦于解决当下的问题，而忽视这个过程中团队的成长和成就需求。可能会面临的挑战是：团队成员的自主担责不够理想，自己事无巨细都需要花时间参与决策，为了达成目标要付出很多精力和时间。</p>
        ''',
    },
    'score_41_60': {
        'analysis': '''
        <p>教练能力处于你管理工具箱中的&ldquo;潜能区&ldquo;。</p>
<p>你身上偶尔会展现出的符合教练型管理者特点的行为，并由此带来和团队成员之间更有效的管理互动。可能面临的挑战是，有效的互动行为可能是无意识做出的，缺乏进行高效管理对话的思路或能力。尤其身处压力或繁忙状态时，难以让自己的教练潜能在关键时刻发挥影响力。</p>
        '''
    },
    'score_61_80': {
        'analysis': '''
        <p>教练能力处于你管理工具箱中&rdquo;常用区&rdquo;</p>
        <p>你能够在管理中带入“人”的视角来推动目标达成，并可以在管理中开始有意识地调整自己的行为。可能面临的挑战是，对有效管理对话的关键能力掌握程度有深有浅，由于这些短板或盲区，面对复杂情境或有挑战的员工时，达不到理想的管理效果。</p>
        '''
    },
    'score_81_100': {
        'analysis': '''
        <p>教练能力处于你管理工具箱中的&rdquo;宝藏区&ldquo;</p>
<p>你可以自如地展现出教练型管理者的特点，能够兼顾业务目标的达成和团队的成长。可能的发展机遇是，将行为和能力沉淀为管理理念和可复制的对话方法，成为教练型管理者的先行者和倡导者，在更大的组织范围内协助教练文化的建立，发挥更大的影响力。</p>
        '''
    }
}


def get_avg_score(options, public_attr):
    score = (EvaluationOption.objects.filter(
        question__order__in=options,
        evaluation_answer_option__public_attr=public_attr,
        deleted=False
    ).aggregate(avg_score=Avg('score'))['avg_score'])

    return int(score * 20) if score else 0


def get_management_analyze(item, public_attr):
    score_list = EvaluationOption.objects.filter(
        question__order__in=item,
        evaluation_answer_option__public_attr=public_attr,
        deleted=False
    ).order_by('-score').values('question__order', 'score')
    gt_60, equal_60, lt_60 = [], [], []
    for score in score_list:
        if score['score'] > 3:
            gt_60.append(score)
        elif score == 3:
            equal_60.append(score)
        else:
            lt_60.append(score)
    advantage, advance, to_advance = {}, {}, {}
    # 优势
    if gt_60:
        if len(gt_60) == len(score_list):  # 当该领域所有题目的得分都>3分时
            advantage['text'] = '你在这个领域做的非常好，继续保持你在'
        else:
            advantage['text'] = '你在这个领域的优势是'
        top_score = gt_60[0]['score']
        advantage['parse_document'] = [PARSE_DOCUMENT[doc['question__order']] for doc in gt_60
                                       if doc['score'] == top_score]

    # 进步
    if not gt_60:  # 当该领域所有题目的得分都<=3分时
        advance['text'] = '你在该领域的优势不明显。你需要继续进步的是'
    else:
        advance['text'] = '你需要继续进步的是'
    if (lt_60 and equal_60) or (lt_60 and not equal_60):
        lower_score = lt_60[-1]['score']
        advance['parse_document'] = [PARSE_DOCUMENT[doc['question__order']] for doc in lt_60 if
                                     doc['score'] == lower_score]
    elif equal_60 and not lt_60:
        lower_score = equal_60[-1]['score']
        advance['parse_document'] = [PARSE_DOCUMENT[doc['question__order']] for doc in equal_60 if
                                     doc['score'] == lower_score]

    # 待进步
    if equal_60 or lt_60:
        to_advance['text'] = '将这个发展项设为成长目标，以便教练为您提供针对性的辅导。'
        doc_equal_60 = [PARSE_DOCUMENT[doc['question__order']] for doc in equal_60]
        doc_lt_60 = [PARSE_DOCUMENT[doc['question__order']] for doc in lt_60]
        to_advance['parse_document'] = doc_equal_60 + doc_lt_60

    str_advantage, str_advance, str_to_advance = None, None, None
    if 'parse_document' in advantage:
        text = advantage['text'] if advantage['text'] == '你在这个领域做的非常好，继续保持你在' else advantage['text'] + '：'
        str_advantage = text + '、'.join(advantage['parse_document']) + '的优势'
    if 'parse_document' in advance:
        str_advance = advance['text'] + "：" + '、'.join(advance['parse_document'])
    if 'parse_document' in to_advance:
        str_to_advance = to_advance['text']

    return str_advantage, str_advance, str_to_advance, to_advance.get('parse_document', None)


def get_score_average(item, public_attr):
    all_score = len(item) * 5
    score = EvaluationOption.objects.filter(
        question__order__in=item,
        evaluation_answer_option__public_attr=public_attr
    ).aggregate(score=Sum('score'))['score']
    score_average = score / all_score
    if score_average < 0.2:
        return 1
    elif score_average < 0.4:
        return 2
    elif score_average < 0.6:
        return 3
    elif score_average < 0.8:
        return 4
    else:
        return 5


def get_management_score_text(public_attr):
    score_list = {}
    for key, order in POWER_TAG_MAP.items():
        score = EvaluationOption.objects.filter(
            question__order__in=order,
            evaluation_answer_option__public_attr=public_attr
        ).aggregate(avg_score=Avg('score'))['avg_score']
        score_list[key] = round(score, 1)

    tmp_list = copy.deepcopy(score_list)
    raw_tag_list = sorted(tmp_list.items(), key=lambda i: i[1], reverse=False)

    tag_list = []
    count = {}
    for k, v in raw_tag_list:
        if count.get(v):
            tag_list.append(k)
        else:
            if len(count) == 3:
                break
            else:
                count[v] = True
                tag_list.append(k)

    text = "提升" + '、'.join(tag_list) + '的能力可以帮助你实现高质量、有成效管理对话。'
    return list(score_list.values()), text, tag_list


def coach_prepare(evaluation, public_attr):
    all_score = EvaluationOption.objects.filter(evaluation_answer_option__public_attr=public_attr
                                                ).aggregate(all_score=Sum('score'))['all_score']

    report_public_attr = PublicAttr.objects.create(project_id=public_attr.project_id, user_id=public_attr.user_id,
                                                   type=ATTR_TYPE_EVALUATION_REPORT)
    if 20 <= all_score <= 40:
        analysis = ALL_SCORE_MAP['score_20_40']['analysis']
    elif 41 <= all_score <= 60:
        analysis = ALL_SCORE_MAP['score_41_60']['analysis']
    elif 61 <= all_score <= 80:
        analysis = ALL_SCORE_MAP['score_61_80']['analysis']
    elif 81 <= all_score <= 100:
        analysis = ALL_SCORE_MAP['score_81_100']['analysis']
    else:
        analysis = None

    # 计算雷达图教练冷力分析各项参数
    score_list, management_score_text, power_tag = get_management_score_text(public_attr)
    build_trust_score, promote_action_score, consensus_target_score, tutor_feedback_score, inspired_question_score,\
    active_listen_score = score_list

    report = EvaluationReport.objects.create(public_attr=report_public_attr, score=all_score,
                                             evaluation_id=evaluation.id,
                                             analysis=analysis, build_trust_score=build_trust_score,
                                             promote_action_score=promote_action_score,
                                             consensus_target_score=consensus_target_score,
                                             tutor_feedback_score=tutor_feedback_score,
                                             inspired_question_score=inspired_question_score,
                                             active_listen_score=active_listen_score,
                                             management_score_text=management_score_text,
                                             power_tag=power_tag)

    for index, item in enumerate(TOPIC_MAP):
        avg_score = get_avg_score(item, public_attr)
        # 计算管理行为分析各项参数
        advantage, advance, to_advance, support_growth_target = get_management_analyze(item, public_attr)
        score_average = get_score_average(item, public_attr)
        if support_growth_target:
            support_growth_target = [{"content": i} for i in support_growth_target]
        else:
            support_growth_target = []
        EvaluationReportScore.objects.create(report=report, score=avg_score, section=index, advantage=advantage,
                                             advance=advance, to_advance=to_advance, score_average=score_average,
                                             support_growth_target=support_growth_target)

    return report.id


PARSE_DOCUMENT = [
    '索引取值占位',
    '能够觉察自身状态',
    '清楚自己的谈话目的',
    '澄清谈话目标和成果',
    '有意识地建立信任',
    '谈话围绕目标展开',
    '促成对目标的真正共识',
    '感知情绪变化',
    '创造真实、坦诚的沟通氛围',
    '提供独立思考和决策的机会',
    '开放地聆听',
    '创造多元视角',
    '关注目标如何落地',
    '真实、及时地反馈',
    '把思考具化到行动',
    '共识如何衡量“做到了”',
    '给下属提供支持和辅导',
    '对正向的意图给予肯定',
    '预判风险、预设方案',
    '引导对本质问题的思考',
    '平衡发展人和解决事'
]

POWER_TAG_MAP = {
    "建立信任": [1, 4, 8],
    "促进行动": [12, 14, 18],
    "共识目标": [2, 3, 5, 6, 15],
    "辅导反馈": [13, 16, 20],
    "启发提问": [9, 11, 19],
    "积极聆听": [7, 10, 17]
}


# 定义一个函数，根据给定的评价分数列表，生成直方图的数据
def get_histogram_data(evaluation_scores):
    # 将评价分数列表按照分数进行排序
    all_score = evaluation_scores.order_by('score')
    # 如果评价分数列表的数量小于2，则返回一个列表，其中包含一个分数区间和对应的计数为1的元素
    if all_score.count() < 2:
        return [str(all_score.first().score - 0.5) + '-' + str(all_score.first().score + 0.5)], [1]
        # 获取最大的分数，向上取整，保留一位小数
    max_score = math.ceil(round(all_score.last().score / 20, 1))
    # 获取最小的分数，向下取整，保留一位小数
    min_score = math.floor(round(all_score.first().score / 20, 1))

    # 注意5分制和百分制切换
    # 计算分数的间隔，保留一位小数
    interval = round((max_score - min_score) / 5, 1)
    # 初始化一个空的分数区间列表
    score_interval_list = []
    # 循环生成5个分数区间
    for i in range(5):
        # 计算当前分数的起始和结束值，保留一位小数，并添加到列表中
        start = round(min_score + i * interval, 1)
        end = round(min_score + (i + 1) * interval, 1)
        tmp_list = [start, end]
        score_interval_list.append(tmp_list)
    # 初始化一个空的y数据列表
    y_data = []
    # 循环获取每个分数区间的计数，并添加到y数据列表中
    for i in range(4):
        count = all_score.filter(score__gte=score_interval_list[i][0] * 20,
                                 score__lt=score_interval_list[i][1] * 20).count()
        y_data.append(count)
    # 获取最后一个分数区间的计数，并添加到y数据列表中
    last_count = all_score.filter(score__gte=score_interval_list[4][0] * 20,
                                  score__lte=score_interval_list[4][1] * 20).count()
    y_data.append(last_count)
    # 将分数区间列表中的每个区间转换为字符串，并添加到x数据列表中
    x_data = [str(i[0]) + '-' + str(i[1]) for i in score_interval_list]
    # 返回x数据和y数据
    return x_data, y_data


def get_pie_chart(evaluation_reports, text):
    reports = evaluation_reports.order_by(text)
    if reports.count() < 2:
        return [{'title': str(round(getattr(reports.first(), text) - 0.5, 1)) + '-' + str(round(getattr(reports.first(), text) + 0.5, 1)),
                'value': 1, 'percentage': '100%'}]
    max_score = math.ceil(getattr(reports.last(), text))
    min_score = math.floor(getattr(reports.first(), text))
    interval = round((max_score - min_score) / 5, 1)

    score_interval_list = []
    for i in range(5):
        start = round(min_score + i * interval, 1)
        end = round(min_score + (i + 1) * interval, 1)
        tmp_list = [start, end]
        score_interval_list.append(tmp_list)

    data = []
    for index, l in enumerate(score_interval_list):
        if text == 'build_trust_score':
            if index < len(score_interval_list)-1:
                count = reports.filter(build_trust_score__gte=l[0], build_trust_score__lt=l[1]).count()
            else:
                count = reports.filter(build_trust_score__gte=l[0], build_trust_score__lte=l[1]).count()
            title = str(l[0]) + '-' + str(l[1])
            percentage = str(round(count / reports.count() * 100, 2)) + '%' if count != 0 else '0%'
            dic = {}
            dic['title'] = title
            dic['value'] = count
            dic['percentage'] = percentage
            data.append(dic)
        elif text == 'promote_action_score':
            if index < len(score_interval_list)-1:
                count = reports.filter(promote_action_score__gte=l[0], promote_action_score__lt=l[1]).count()
            else:
                count = reports.filter(promote_action_score__gte=l[0], promote_action_score__lte=l[1]).count()
            title = str(l[0]) + '-' + str(l[1])
            percentage = str(round(count / reports.count() * 100, 2)) + '%' if count != 0 else '0%'
            dic = {}
            dic['title'] = title
            dic['value'] = count
            dic['percentage'] = percentage
            data.append(dic)
        elif text == 'active_listen_score':
            if index < len(score_interval_list)-1:
                count = reports.filter(active_listen_score__gte=l[0], active_listen_score__lt=l[1]).count()
            else:
                count = reports.filter(active_listen_score__gte=l[0], active_listen_score__lte=l[1]).count()
            title = str(l[0]) + '-' + str(l[1])
            percentage = str(round(count / reports.count() * 100, 2)) + '%' if count != 0 else '0%'
            dic = {}
            dic['title'] = title
            dic['value'] = count
            dic['percentage'] = percentage
            data.append(dic)
        elif text == 'consensus_target_score':
            if index < len(score_interval_list) - 1:
                count = reports.filter(consensus_target_score__gte=l[0], consensus_target_score__lt=l[1]).count()
            else:
                count = reports.filter(consensus_target_score__gte=l[0], consensus_target_score__lte=l[1]).count()
            title = str(l[0]) + '-' + str(l[1])
            percentage = str(round(count / reports.count() * 100, 2)) + '%' if count != 0 else '0%'
            dic = {}
            dic['title'] = title
            dic['value'] = count
            dic['percentage'] = percentage
            data.append(dic)
        elif text == 'inspired_question_score':
            if index < len(score_interval_list) - 1:
                count = reports.filter(inspired_question_score__gte=l[0], inspired_question_score__lt=l[1]).count()
            else:
                count = reports.filter(inspired_question_score__gte=l[0], inspired_question_score__lte=l[1]).count()
            title = str(l[0]) + '-' + str(l[1])
            percentage = str(round(count / reports.count() * 100, 2)) + '%' if count != 0 else '0%'
            dic = {}
            dic['title'] = title
            dic['value'] = count
            dic['percentage'] = percentage
            data.append(dic)
        else:
            if index < len(score_interval_list) - 1:
                count = reports.filter(tutor_feedback_score__gte=l[0], tutor_feedback_score__lt=l[1]).count()
            else:
                count = reports.filter(tutor_feedback_score__gte=l[0], tutor_feedback_score__lte=l[1]).count()
            title = str(l[0]) + '-' + str(l[1])
            percentage = str(round(count / reports.count() * 100, 2)) + '%' if count != 0 else '0%'
            dic = {}
            dic['title'] = title
            dic['value'] = count
            dic['percentage'] = percentage
            data.append(dic)

    return data


QUESTION_TEXT = {
    1: '能够觉察自身状态。',
    2: '清楚自己的谈话目的。',
    3: '澄清谈话目标和成果。',
    4: '有意识地建立信任。',
    5: '谈话围绕目标展开。',
    6: '促成对目标的真正共识。',
    7: '感知情绪变化。',
    8: '创造真实、坦诚的沟通氛围。',
    9: '提供独立思考和决策的机会。',
    10: '开放地聆听。',
    11: '创造多元视角。',
    12: '关注目标如何落地。',
    13: '真实、及时地反馈。',
    14: '把思考具化到行动。',
    15: '共识如何衡量“做到了”。',
    16: '给下属提供支持和辅导。',
    17: '对正向的意图给予肯定。',
    18: '预判风险、预设方案。',
    19: '引导对本质问题的思考。',
    20: '平衡发展人和解决事。',
}

