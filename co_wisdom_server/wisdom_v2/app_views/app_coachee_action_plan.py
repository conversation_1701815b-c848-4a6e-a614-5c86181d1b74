from drf_yasg import openapi
from drf_yasg.utils import swagger_auto_schema
from rest_framework.decorators import action

from rest_framework import serializers
from rest_framework import viewsets

from wisdom_v2.common import user_public, action_plan_public
from wisdom_v2.models import ActionPlan, CapacityTag, PublicAttr, User, Project, Habit

from utils.pagination import StandardResultsSetPagination
from utils.api_response import success_response, parameter_error_response
from wisdom_v2.views.constant import ATTR_TYPE_ACTIONPLAN, ROLE_COACHEE
from wisdom_v2.app_views.app_coachee_habit import HabitSerializer
from wisdom_v2.enum.user_enum import UserRoleEnum


class ActionPlanSerializer(serializers.ModelSerializer):
    capacity = serializers.SerializerMethodField()
    created_at = serializers.DateTimeField(format='%Y-%m-%d %H:%M', read_only=True)
    tag_type = serializers.SerializerMethodField()


    class Meta:
        model = ActionPlan
        exclude = ('updated_at', 'creator_role', 'public_attr', 'deleted')

    def get_capacity(self, obj):
        try:
            if obj.interview:
                capacity = CapacityTag.objects.get(interview_id=obj.interview.pk, select_type=1, deleted=False)
                return capacity.title
        except CapacityTag.DoesNotExist:
            return

    def get_tag_type(self, obj):
        return 2


class ActionPlanViewSet(viewsets.ModelViewSet):
    queryset = ActionPlan.objects.filter(deleted=False).order_by('-end_date')
    serializer_class = ActionPlanSerializer

    @swagger_auto_schema(
        operation_id='app被教行动计划列表',
        operation_summary='app被教行动计划列表',
        manual_parameters=[
            openapi.Parameter('user_id', openapi.IN_QUERY, description='用户id', type=openapi.TYPE_NUMBER,
                              required=True),
            openapi.Parameter('page', openapi.IN_QUERY, description='第几页', type=openapi.TYPE_NUMBER),
            openapi.Parameter('page_size', openapi.IN_QUERY, description='每页显示条数默认15条', type=openapi.TYPE_NUMBER)
        ],
        tags=['app被教行动计划']
    )
    def list(self, request, *args, **kwargs):
        try:
            user_id = request.query_params.get('user_id')
            role = int(request.headers.get('role'), 0)
            queryset = self.get_queryset().filter(public_attr__user__pk=user_id)
            if role == UserRoleEnum.trainee_coachee:
                queryset = queryset.filter(public_attr__project__isnull=True)
            elif role == UserRoleEnum.coachee:
                queryset = queryset.filter(public_attr__project__isnull=False)

        except Exception as e:
            return parameter_error_response(str(e))

        # 分页
        paginator = StandardResultsSetPagination()
        page_list = paginator.paginate_queryset(queryset, self.request)
        serializer = self.serializer_class(page_list, many=True, context={'queryset': queryset})
        response = paginator.get_paginated_response(serializer.data)
        return success_response(response, request)

    @swagger_auto_schema(
        operation_id='app被教添加行动计划',
        operation_summary='app被教添加行动计划',
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'user_id': openapi.Schema(type=openapi.TYPE_NUMBER, description='用户id'),
                'project_id': openapi.Schema(type=openapi.TYPE_NUMBER, description='项目id 普通被教练者必传'),
                'content': openapi.Schema(type=openapi.TYPE_STRING, description='内容'),
                'end_date': openapi.Schema(type=openapi.TYPE_STRING, description='结束日期'),
            }
        ),
        tags=['app被教行动计划']
    )
    def create(self, request, *args, **kwargs):
        try:
            user_id = request.data.get('user_id')
            content = request.data.get('content')
            end_date = request.data.get('end_date')
            user = User.objects.get(pk=user_id, deleted=False)
            user_role = int(request.headers.get('role'))
        except (ValueError, TypeError):
            return parameter_error_response()
        except User.DoesNotExist:
            return parameter_error_response('用户不存在')

        start_date, error = action_plan_public.get_default_start_date(request.data)

        if user_role == 2:  # 普通被教练者
            # 读取用户项目
            project_member = user_public.get_user_to_project_member_all(user_id)
            project_id = project_member[0].project_id if project_member else None

            project = Project.objects.get(pk=project_id, deleted=False)
            public_attr = PublicAttr.objects.create(project=project, user=user, type=ATTR_TYPE_ACTIONPLAN)
        elif user_role == 4:  # 三无学员
            public_attr = PublicAttr.objects.create(user=user, type=ATTR_TYPE_ACTIONPLAN)
        else:
            return parameter_error_response('未知用户角色')
        ActionPlan.objects.create(
            content=content, end_date=end_date, start_date=start_date,
            creator_role=ROLE_COACHEE, public_attr=public_attr)
        return success_response(request=request)


    @swagger_auto_schema(
        operation_id='app被教行动计划详情',
        operation_summary='app被教行动计划详情',
        manual_parameters=[
            openapi.Parameter('action_plan_id', openapi.IN_QUERY, description='行动计划id',
                              type=openapi.TYPE_NUMBER, required=True)
        ],
        tags=['app被教行动计划']
    )
    @action(methods=['get'], detail=False, url_path='detail')
    def action_plan_detail(self, request, *args, **kwargs):
        try:
            instance = ActionPlan.objects.get(pk=request.query_params.get('action_plan_id', 0), deleted=False)
        except ActionPlan.DoesNotExist:
            return parameter_error_response('行动计划id错误')
        serializer = self.get_serializer(instance)
        return success_response(serializer.data, request=request)

    @swagger_auto_schema(
        operation_id='app被教修改行动计划',
        operation_summary='app被教修改行动计划',
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'action_plan_id': openapi.Schema(type=openapi.TYPE_NUMBER, description='行动计划id'),
                'content': openapi.Schema(type=openapi.TYPE_STRING, description='内容'),
                'status': openapi.Schema(type=openapi.TYPE_NUMBER, description='1: 进行中 2:已完成 3:已取消'),
            }
        ),
        tags=['app被教行动计划']
    )
    @action(methods=['post'], detail=False, url_path='update')
    def action_plan_update(self, request, *args, **kwargs):
        try:
            instance = ActionPlan.objects.get(pk=request.data.get('action_plan_id'), deleted=False)
        except ActionPlan.DoesNotExist:
            return parameter_error_response('行动计划不存在')
        data = request.data.copy()
        serializer = self.get_serializer(instance, data=data, partial=True)
        serializer.is_valid(raise_exception=True)
        serializer.save()
        return success_response(serializer.data, request=request)


    @swagger_auto_schema(
        operation_id='app被教删除行动计划',
        operation_summary='app被教删除行动计划',
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'action_plan_id': openapi.Schema(type=openapi.TYPE_NUMBER, description='行动计划id'),
            }
        ),
        tags=['app被教行动计划']
    )
    @action(methods=['post'], detail=False, url_path='delete')
    def action_plan_delete(self, request, *args, **kwargs):
        try:
            instance = ActionPlan.objects.get(pk=request.data.get('action_plan_id'), deleted=False)
        except ActionPlan.DoesNotExist:
            return parameter_error_response('行动计划不存在')
        instance.deleted = True
        instance.save()
        return success_response(request=request)


    @swagger_auto_schema(
        operation_id='app被教行动计划-行为转变列表',
        operation_summary='app被教行动计划-行为转变列表',
        manual_parameters=[
            openapi.Parameter('user_id', openapi.IN_QUERY, description='用户id', type=openapi.TYPE_NUMBER,
                              required=True),
            openapi.Parameter('page_size', openapi.IN_QUERY, description='每页显示条数默认15条', type=openapi.TYPE_NUMBER)
        ],
        tags=['app被教行动计划']
    )
    @action(methods=['get'], detail=False, url_path='action_plan_habit_list')
    def action_plan_habit_list(self, request, *args, **kwargs):
        try:
            user_id = request.query_params.get('user_id')
            user = User.objects.get(pk=user_id, deleted=False)
            role = int(request.headers.get('role'))
        except (ValueError, TypeError):
            return parameter_error_response()
        action_plan = self.get_queryset().filter(public_attr__user__pk=user.pk).order_by('-created_at')
        habit = Habit.objects.filter(public_attr__user__pk=user.pk, deleted=False).order_by('-created_at')

        # 数据查询
        if role == UserRoleEnum.trainee_coachee:
            action_plan = action_plan.filter(public_attr__project_id__isnull=True)
            habit = habit.filter(public_attr__project__isnull=True)
        elif role == UserRoleEnum.coachee:
            action_plan = action_plan.filter(public_attr__project_id__isnull=False)
            habit = habit.filter(public_attr__project__isnull=False)

        # 行动计划
        action_plan_data = self.serializer_class(action_plan, many=True).data
        for action in action_plan_data:
            action['data_type'] = 1
        # 行为转变
        habit_data = HabitSerializer(habit, many=True).data
        for habit in habit_data:
            habit['data_type'] = 2
        paginator = StandardResultsSetPagination()
        page_list = paginator.paginate_queryset([*action_plan_data, *habit_data], self.request)
        response = paginator.get_paginated_response(page_list)
        return success_response(response, request=request)
