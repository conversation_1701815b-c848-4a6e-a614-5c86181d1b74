from rest_framework import serializers

from wisdom_v2.views.article_actions import ArticleSerializers
from wisdom_v2.models import SlideShow


class AppSlideShowSerializers(serializers.ModelSerializer):
    id = serializers.IntegerField(read_only=True)
    image = serializers.SerializerMethodField(help_text='图片链接')
    title = serializers.CharField(help_text='标题')
    url = serializers.CharField(help_text='跳转链接')
    url_type = serializers.IntegerField(help_text='跳转类型')
    suitable_object = serializers.IntegerField(help_text='适用对象')
    article = serializers.SerializerMethodField(help_text='文章')
    theme = serializers.SerializerMethodField(help_text='主题')

    class Meta:
        model = SlideShow
        fields = ('id', 'image', 'title', 'url', 'url_type', 'suitable_object', 'article', 'theme')

    def get_image(self, obj):
        return obj.image_url

    def get_article(self, obj):
        if obj.url_type == 1 and obj.article_id:  # 文章
            article_data = ArticleSerializers(obj.article).data
            return article_data

    def get_theme(self, obj):
        if obj.url_type == 2 and obj.theme:  # 主题
            return {"id": obj.theme.id, "name": obj.theme.name}