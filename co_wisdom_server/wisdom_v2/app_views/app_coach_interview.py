import datetime

from drf_yasg import openapi
from drf_yasg.utils import swagger_auto_schema
from pypinyin import lazy_pinyin
from rest_framework.decorators import action
from rest_framework import viewsets

from utils.api_response import success_response, parameter_error_response
from utils.pagination import StandardResultsSetPagination
from utils import utc_date_time, validate
from wisdom_v2.common import coach_interview_public
from wisdom_v2.enum.business_order_enum import WorkTypeEnum
from wisdom_v2.enum.coach_enum import NonPlatformInterviewTypeEnum, NonPlatformInterviewPayTypeEnum, \
    NonPlatformInterviewSourceEnum
from wisdom_v2.enum.project_interview_enum import ProjectInterviewTypeEnum, ProjectInterviewPlaceCategoryEnum
from wisdom_v2.models import User, Coach, ProjectInterview

from wisdom_v2.app_views.app_coach_interview_actions import AppCoachInterviewDataSerializer
from wisdom_v2.models_file import PublicCoursesCoach
from wisdom_v2.models_file.coach import NonPlatformInterview
from wisdom_v2.views.constant import ATTR_STATUS_INTERVIEW_CANCEL


class AppCoachInterviewDataViewSet(viewsets.ModelViewSet):
    queryset = NonPlatformInterview.objects.filter(deleted=False)
    serializer_class = AppCoachInterviewDataSerializer

    @swagger_auto_schema(
        operation_id='获取教练辅导列表数据',
        operation_summary='获取教练辅导列表数据',
        manual_parameters=[
            openapi.Parameter('start_date', openapi.IN_QUERY, description='开始时间', type=openapi.TYPE_STRING,
                              required=True),
            openapi.Parameter('end_date', openapi.IN_QUERY, description='结束时间', type=openapi.TYPE_STRING,
                              required=True),
            openapi.Parameter('pay_type', openapi.IN_QUERY, description='支付类型', type=openapi.TYPE_NUMBER,
                              required=True),
            openapi.Parameter('interview_type', openapi.IN_QUERY, description='辅导类型', type=openapi.TYPE_NUMBER,
                              required=True),
            openapi.Parameter('customer_name', openapi.IN_QUERY, description='客户名称', type=openapi.TYPE_STRING,
                              required=True),
            openapi.Parameter('coach_user_id', openapi.IN_QUERY, description='教练id标识', type=openapi.TYPE_NUMBER,
                              required=True),
            openapi.Parameter('page', openapi.IN_QUERY, description='页数', type=openapi.TYPE_NUMBER,
                              required=True),
            openapi.Parameter('page_size', openapi.IN_QUERY, description='页码', type=openapi.TYPE_NUMBER,
                              required=True)
        ],
        tags=['教练辅导数据']
    )
    def list(self, request, *args, **kwargs):
        try:
            coach_user_id = request.query_params.get('coach_user_id')
            User.objects.get(id=coach_user_id, deleted=False)
            Coach.objects.get(user_id=coach_user_id, deleted=False)

        except (User.DoesNotExist, Coach.DoesNotExist):
            return parameter_error_response('教练不存在')
        except Exception as e:
            return parameter_error_response(str(e))

        data = {
            'interview_hour': 0,
            'interview_data': [],
        }
        coach_interview_queryset, project_interview_queryset, public_courses_coach_queryset = coach_interview_public.get_coach_interview_list(
            request.query_params)

        # 教练创建辅导数据
        coach_interview_data = []
        if coach_interview_queryset:
            coach_interview_data = AppCoachInterviewDataSerializer(coach_interview_queryset, many=True).data

        # 平台内辅导数据
        all_project_interview_data = []
        if project_interview_queryset:
            for item in project_interview_queryset.all():
                project_interview_data = coach_interview_public.coach_project_interview_to_dict(item)
                all_project_interview_data.append(project_interview_data)

        # 公开课辅导数据
        all_public_courses_coach_data = []
        # 获取公开课数据
        if public_courses_coach_queryset:
            for item in public_courses_coach_queryset.all():
                public_courses_coach_data = coach_interview_public.public_courses_coach_to_dict(item)
                all_public_courses_coach_data.append(public_courses_coach_data)

        # 获取数据总时长
        all_interview_hour = 0
        for item in [*coach_interview_data, *all_project_interview_data, *all_public_courses_coach_data]:
            all_interview_hour += item.get('hour')

        # 只保留一位小数
        all_interview_hour = round(all_interview_hour, 1)
        # 如果总小时数是浮点数但实际为整数，转换为整数类型
        if isinstance(all_interview_hour, float):
            all_interview_hour = int(all_interview_hour) if all_interview_hour.is_integer() else all_interview_hour
        data['interview_hour'] = all_interview_hour

        # 数据拼接
        all_data = [*coach_interview_data, *all_project_interview_data, *all_public_courses_coach_data]
        # 数据排序
        sorted_data = sorted(all_data, key=lambda x: x['start_time'], reverse=True)

        # 分页
        paginator = StandardResultsSetPagination()
        page_list = paginator.paginate_queryset(sorted_data, self.request)
        data['interview_data'] = page_list
        response = paginator.get_paginated_response(data)
        return success_response(response)

    @swagger_auto_schema(
        operation_summary='获取教练辅导数据详情',
        manual_parameters=[
            openapi.Parameter('id', openapi.IN_QUERY, description='数据标识id', type=openapi.TYPE_NUMBER,
                              required=True),
        ],
        tags=['教练辅导数据']
    )
    @action(methods=['get'], detail=False, url_path='detail')
    def get_coach_interview_data_detail(self, request, *args, **kwargs):
        try:
            interview_id = request.query_params.get('id')
            data_type = request.query_params.get('data_type')
        except Exception as e:
            return parameter_error_response()
        if not data_type:
            return parameter_error_response('未获取到辅导类型')

        if int(data_type) == NonPlatformInterviewSourceEnum.interview.value:
            project_interview = ProjectInterview.objects.get(id=interview_id, deleted=False)
            interview_data = coach_interview_public.coach_project_interview_to_dict(project_interview)

        elif int(data_type) == NonPlatformInterviewSourceEnum.coach_interview.value:
            coach_interview = self.queryset.filter(pk=interview_id).first()
            if not coach_interview:
                return parameter_error_response('数据不存在')
            interview_data = AppCoachInterviewDataSerializer(coach_interview).data

        elif int(data_type) == NonPlatformInterviewSourceEnum.public_courses_coach.value:
            public_courses_coach = PublicCoursesCoach.objects.get(id=interview_id, deleted=False)
            interview_data = coach_interview_public.public_courses_coach_to_dict(public_courses_coach)

        else:
            return parameter_error_response('数据类型错误')
        return success_response(interview_data)

    @swagger_auto_schema(
        operation_id='创建教练辅导数据',
        operation_summary='创建教练辅导数据',
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'coach_user_id': openapi.Schema(type=openapi.TYPE_NUMBER, description='用户id'),
            }),
        tags=['教练辅导数据']
    )
    @action(methods=['post'], detail=False, url_path='create')
    def create_coach_interview_data(self, request, *args, **kwargs):
        try:
            raw_start_time = request.data.get('start_time')
            raw_end_time = request.data.get('end_time')
            coach_user_id = request.data.get('coach_user_id')
            user_count = request.data.get('user_count')
            customer_email = request.data.get('customer_email')
            User.objects.get(id=coach_user_id)
            Coach.objects.get(user_id=coach_user_id)
        except (User.DoesNotExist, Coach.DoesNotExist):
            return parameter_error_response('教练不存在')

        if not validate.validate_email(customer_email):
            return parameter_error_response('邮箱格式错误')

        if user_count and not isinstance(user_count, int):
            return parameter_error_response('辅导人数必须为整数')

        if not raw_start_time or not raw_end_time:
            return parameter_error_response('开始时间或者结束时间不能为空')

        data = request.data
        start_time = utc_date_time.datetime_change_utc(raw_start_time)
        end_time = utc_date_time.datetime_change_utc(raw_end_time)
        if start_time >= end_time:
            return parameter_error_response('结束时间必须大于开始时间')

        data['hour'] = round((end_time - start_time).total_hours(), 1)
        data['start_time'] = start_time.naive()
        data['end_time'] = end_time.naive()

        instance = NonPlatformInterview.objects.create(**data)
        interview_data = AppCoachInterviewDataSerializer(instance).data
        return success_response(interview_data)

    @swagger_auto_schema(
        operation_id='更新教练辅导数据',
        operation_summary='更新教练辅导数据',
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'coach_user_id': openapi.Schema(type=openapi.TYPE_NUMBER, description='用户id'),
            }),
        tags=['教练辅导数据']
        )
    @action(methods=['post'], detail=False, url_path='update')
    def update_coach_interview_data(self, request, *args, **kwargs):
        interview_id = request.data.get('id')
        user_count = request.data.get('user_count')
        customer_email = request.data.get('customer_email')
        coach_interview = self.queryset.filter(pk=interview_id).first()
        if not coach_interview:
            return parameter_error_response('数据不存在')

        # 如果deleted有值就则删除教练辅导
        if request.data.get('deleted'):
            coach_interview.deleted = True
            coach_interview.save()
            return success_response()

        if not validate.validate_email(customer_email):
            return parameter_error_response('邮箱格式错误')

        if user_count and not isinstance(user_count, int):
            return parameter_error_response('用户数量必须为整数')

        # 计算小时
        raw_start_time = request.data.get('start_time', coach_interview.start_time)
        raw_end_time = request.data.get('end_time', coach_interview.end_time)
        start_time = utc_date_time.datetime_change_utc(raw_start_time)
        end_time = utc_date_time.datetime_change_utc(raw_end_time)

        if start_time >= end_time:
            return parameter_error_response('结束时间必须大于开始时间')

        data = request.data
        data['hour'] = round((end_time - start_time).total_hours(), 1)
        data['start_time'] = start_time.naive()
        data['end_time'] = end_time.naive()

        fields = [
            'topic', 'pay_type', 'type', 'start_time', 'end_time',
            'customer_name', 'customer_email', 'user_count', 'hour'
        ]
        for field in fields:
            if field in request.data:
                setattr(coach_interview, field, request.data[field])
        coach_interview.save()

        interview_data = AppCoachInterviewDataSerializer(coach_interview).data
        return success_response(interview_data)

    @swagger_auto_schema(
        operation_summary='获取教练辅导客户信息',
        manual_parameters=[
            openapi.Parameter('coach_user_id', openapi.IN_QUERY, description='教练用户标识', type=openapi.TYPE_NUMBER,
                              required=True),
        ],
        tags=['教练辅导数据']
    )
    @action(methods=['get'], detail=False, url_path='customer')
    def get_coach_interview_data_customer(self, request):
        # 从请求中获取教练用户标识
        coach_user_id = request.query_params.get('coach_user_id')
        # 从请求中获取客户类型，默认为1-全部
        customer_type = request.query_params.get('type',)  # 全部(默认), 1-个人客户, 2-项目客户
        if not coach_user_id:
            return parameter_error_response('请输入教练用户标识')

        customer_data = coach_interview_public.get_coach_interview_customer(
            coach_user_id, customer_type)

        # 数据需要按照customer_name的首字母排序
        sorted_data = sorted(customer_data, key=lambda i: lazy_pinyin(i['customer_name']))

        paginator = StandardResultsSetPagination()
        page_list = paginator.paginate_queryset(sorted_data, self.request)
        response = paginator.get_paginated_response(page_list)
        return success_response(response)

    @swagger_auto_schema(
        operation_summary='获取教练辅导基础信息',
        manual_parameters=[
            openapi.Parameter('coach_user_id', openapi.IN_QUERY, description='教练用户标识', type=openapi.TYPE_NUMBER,
                              required=True),
        ],
        tags=['教练辅导数据']
    )
    @action(methods=['get'], detail=False, url_path='info')
    def get_coach_interview_base_data(self, request):

        coach_user_id = request.query_params.get('coach_user_id')

        # 筛选出符合条件的平台内辅导信息
        project_interview_queryset = ProjectInterview.objects.filter(
            is_coach_agree=True, deleted=False,
            type=ProjectInterviewTypeEnum.formal_interview.value,
            public_attr__user_id=coach_user_id,
            public_attr__end_time__lt=datetime.datetime.now()
        ).exclude(public_attr__status=ATTR_STATUS_INTERVIEW_CANCEL)

        # 教练自己录入的信息
        coach_interview_queryset = self.queryset.filter(coach_user_id=coach_user_id)

        # 公开课的信息
        public_courses_coach_queryset = PublicCoursesCoach.objects.filter(
            coach__user_id=coach_user_id, deleted=False, type__in=[
                WorkTypeEnum.one_to_one.value, WorkTypeEnum.group_coach.value, WorkTypeEnum.group_counseling.value])

        # 获取全部辅导总时长
        all_interview_hour = coach_interview_public.get_coach_interview_hour(
            coach_interview_queryset=coach_interview_queryset,
            project_interview_queryset=project_interview_queryset,
            public_courses_coach_queryset=public_courses_coach_queryset
        )

        # 全部辅导小时数
        all_hour = all_interview_hour.get('all_hour')

        # 付费辅导小时数
        # 平台内付费
        pay_project_interview_queryset = project_interview_queryset.exclude(
            order__payer_amount=0)  # 排除实付金额等于0的辅导
        # 教练创建的付费
        pay_coach_interview_queryset = coach_interview_queryset.filter(pay_type=NonPlatformInterviewPayTypeEnum.pay.value)
        # 公开课只有付费
        pay_interview_hour = coach_interview_public.get_coach_interview_hour(
            coach_interview_queryset=pay_coach_interview_queryset,
            project_interview_queryset=pay_project_interview_queryset,
            public_courses_coach_queryset=public_courses_coach_queryset
        )
        pay_all_hour = pay_interview_hour.get('all_hour')

        # 免费辅导小时数
        # 平台内免费
        free_project_interview_queryset = project_interview_queryset.filter(
            order__payer_amount=0)  # 实付金额等于0的辅导
        # 教练创建免费
        free_coach_interview_queryset = coach_interview_queryset.filter(pay_type=NonPlatformInterviewPayTypeEnum.free.value)
        # 公开课只有付费
        free_interview_hour = coach_interview_public.get_coach_interview_hour(
            coach_interview_queryset=free_coach_interview_queryset,
            project_interview_queryset=free_project_interview_queryset
        )
        free_all_hour = free_interview_hour.get('all_hour')

        # 一对一辅导小时数
        # 平台个人辅导
        personal_interview_queryset = project_interview_queryset.filter(
            place_category=ProjectInterviewPlaceCategoryEnum.online_one_to_one.value
        )
        # 教练创建的个人辅导
        personal_coach_interview_queryset = coach_interview_queryset.filter(type=NonPlatformInterviewTypeEnum.personal.value)
        # 公开课个人辅导
        personal_public_courses_coach_queryset = public_courses_coach_queryset.filter(
            type__in=[WorkTypeEnum.one_to_one.value]
        )
        personal_interview_hour = coach_interview_public.get_coach_interview_hour(
            coach_interview_queryset=personal_coach_interview_queryset,
            project_interview_queryset=personal_interview_queryset,
            public_courses_coach_queryset=personal_public_courses_coach_queryset,
        )
        personal_all_hour = personal_interview_hour.get('all_hour')

        # 团队辅导小时数
        # 平台集体辅导
        group_interview_queryset = project_interview_queryset.filter(
            place_category=ProjectInterviewPlaceCategoryEnum.offline_group_coach.value
        )
        # 教练创建的团队辅导
        group_coach_interview_queryset = coach_interview_queryset.filter(type=NonPlatformInterviewTypeEnum.group.value)

        # 公开课团队辅导
        group_public_courses_coach_queryset = public_courses_coach_queryset.filter(
            type__in=[WorkTypeEnum.group_coach.value, WorkTypeEnum.group_counseling.value]
        )
        group_interview_hour = coach_interview_public.get_coach_interview_hour(
            coach_interview_queryset=group_coach_interview_queryset,
            project_interview_queryset=group_interview_queryset,
            public_courses_coach_queryset=group_public_courses_coach_queryset,
        )
        group_all_hour = group_interview_hour.get('all_hour')

        # 客户数量
        customer_count = len(coach_interview_public.get_coach_interview_customer(
            coach_user_id, None))  # 全部

        data = {
            'interview_hour': all_hour,
            'customer_count': customer_count,
            'paid_interview_hour': pay_all_hour,
            'free_interview_hour': free_all_hour,
            'one_to_one_interview_hour': personal_all_hour,
            'team_interview_hour': group_all_hour
        }

        return success_response(data)
