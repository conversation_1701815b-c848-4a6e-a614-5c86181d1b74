from rest_framework import serializers

from wisdom_v2.common import activity_public
from wisdom_v2.common import activity_interview_public
from wisdom_v2.enum.service_content_enum import UserInviteTypeEnum
from wisdom_v2.models import UserInviteRecord
from wisdom_v2.models_file import Activity, ActivityCoach


class AppActivityDetailSerializer(serializers.ModelSerializer):
    """
    活动详情序列化
    """

    start_date = serializers.DateField(help_text='开始日期', required=False, format='%Y-%m-%d')
    end_date = serializers.DateField(help_text='结束日期', required=False, format='%Y-%m-%d')
    interview_start_date = serializers.DateField(help_text='可预约辅导开始日期', required=False, format='%Y-%m-%d')
    interview_end_date = serializers.DateField(help_text='可预约辅导结束日期', required=False, format='%Y-%m-%d')
    coach_content = serializers.SerializerMethodField(help_text='教练数据')
    status = serializers.SerializerMethodField(help_text='活动状态')
    invite_code = serializers.SerializerMethodField(help_text='邀请码')
    price = serializers.SerializerMethodField(help_text='活动金额')

    def get_price(self, obj):
        return obj.display_price

    def get_status(self, obj):
        return activity_public.get_status_display(obj)

    def get_coach_content(self, obj):
        return activity_public.get_coach_content(obj, is_user_query=True)

    def get_invite_code(self, obj):
        # 返回活动邀请，邀请人是空
        invite_code = UserInviteRecord.objects.filter(
            type=UserInviteTypeEnum.activity.value, object_id=str(obj.id), deleted=False, referrer__isnull=True).first()
        return str(invite_code.uuid) if invite_code else None

    class Meta:
        model = Activity
        fields = ('id', 'theme', 'type', 'start_date', 'end_date', 'brief', 'model_image_url', 'guide_image_url',
                  'poster_image_url', 'limit_count', 'coach_content', 'status', 'invite_code', 'rule', 'bg_color',
                  'head_image_url', 'notes', 'price', 'interview_start_date', 'interview_end_date')


class AppActivityCoachDetailSerializer(serializers.ModelSerializer):
    """
    活动教练详情序列化
    """
    coach_user_id = serializers.CharField(source='coach.user_id', help_text='教练用户id')
    coach_name = serializers.CharField(source='coach.user.cover_name', help_text='教练姓名')
    coach_type = serializers.CharField(source='coach.coach_type', help_text='教练类型')
    look_count = serializers.IntegerField(source='look_all_count',help_text='总浏览数')
    user_look_count = serializers.SerializerMethodField(help_text='小程序用户查看数')
    interview_user_count = serializers.SerializerMethodField(help_text='辅导次数')
    activity_id = serializers.SerializerMethodField(help_text='活动id')
    activity_theme = serializers.CharField(source='activity.theme', help_text='活动标题')
    activity_brief = serializers.CharField(source='activity.brief', help_text='活动简介')
    activity_start_date = serializers.DateField(
        source='activity.start_date', help_text='活动开始日期', required=False, format='%Y-%m-%d')
    activity_end_date = serializers.DateField(
        source='activity.end_date', help_text='活动结束日期', required=False, format='%Y-%m-%d')
    activity_interview_start_date = serializers.DateField(
        source='activity.interview_start_date', help_text='活动可预约辅导开始日期', required=False, format='%Y-%m-%d')
    activity_interview_end_date = serializers.DateField(
        source='activity.interview_end_dat', help_text='活动可预约辅导结束日期', required=False, format='%Y-%m-%d')
    activity_price = serializers.SerializerMethodField(help_text='活动金额')

    def get_activity_price(self, obj):
        return obj.activity.display_price

    def get_activity_id(self, obj):
        return str(obj.activity.id)

    def get_user_look_count(self, obj):
        if obj.look_all_user:
            return len(obj.look_all_user)
        return 0

    def get_interview_user_count(self, obj):
        return activity_interview_public.get_pay_user_count(
            obj.activity, [obj.coach.user_id], is_set=True)

    class Meta:
        model = ActivityCoach
        fields = ('id', 'coach_id', 'resume_id', 'status', 'image_url', 'activity_id', 'coach_user_id',
                  'look_count', 'user_look_count', 'interview_user_count', 'deleted', 'coach_name', 'coach_type',
                  'activity_theme', 'activity_brief', 'activity_start_date', 'activity_end_date', 'activity_price',
                  'activity_interview_start_date', 'activity_interview_end_date')
