import json
import pickle

import jieba
import redis

from django.db.models import Q
from django.conf import settings
from drf_yasg import openapi
from rest_framework.decorators import action
from drf_yasg.utils import swagger_auto_schema
from rest_framework import viewsets

from utils import validate
from utils.api_response import success_response, parameter_error_response
from utils.miniapp_version_judge import compare_version

from wisdom_v2.common import coach_public, theme_public
from wisdom_v2.enum.service_content_enum import CoachWorkingYearsEnum, CoachAuthEnum
from wisdom_v2.models import Theme, Article
from wisdom_v2.app_views.app_theme_actions import AppThemeSerializer
from wisdom_v2.models_file import NonPlatformArticles

data_redis = redis.Redis.from_url(settings.DATA_REDIS)


class AppThemeViewSet(viewsets.ModelViewSet):
    queryset = Theme.objects.filter(deleted=False).order_by('-created_at')
    serializer_class = AppThemeSerializer

    @swagger_auto_schema(
        operation_id='小程序主题详情',
        operation_summary='小程序主题详情',
        manual_parameters=[
            openapi.Parameter('theme_id	', openapi.IN_QUERY, description='名称', type=openapi.TYPE_STRING),
        ],
        tags=['小程序主题']
    )
    @action(methods=['get'], detail=False, url_path='detail', authentication_classes=[])
    def theme_detail(self, request, *args, **kwargs):
        try:
            theme_id = request.query_params.get('theme_id')
            theme = Theme.objects.get(id=theme_id)
        except Exception as e:
            return parameter_error_response()
        user_id = validate.get_not_token_user_id(request)
        if user_id:
            data_redis.set(f"quick_link__{user_id}", str(theme.id), ex=1209600)
        serializer = self.get_serializer(theme)
        return success_response(serializer.data)

    @swagger_auto_schema(
        operation_id='小程序端搜索文章、教练',
        operation_summary='小程序端搜索文章、教练',
        manual_parameters=[
            openapi.Parameter('content', openapi.IN_QUERY, description='搜索内容', type=openapi.TYPE_STRING),
        ],
        tags=['小程序主题']
    )
    @action(methods=['get'], detail=False, url_path='select', authentication_classes=[])
    def select_article_coach(self, request, *args, **kwargs):
        content = request.query_params.get('content')
        mp = json.loads(request.headers.get('mp')) if request.headers.get('mp') else None
        user_id = validate.get_not_token_user_id(request)
        if not content:
            return parameter_error_response()
        theme_ids = Theme.objects.filter(name__contains=content, deleted=False).values_list('id', flat=True)
        if user_id:
            data_redis.set(f"theme__{user_id}", pickle.dumps([str(theme_id) for theme_id in theme_ids]), ex=1209600)

        # 构建基于内容搜索的分词列表，一次构造，多处使用
        seg = [item for item in jieba.cut(content) if item != ' ']
        # 文章查询
        articles_content_query = Q()
        # 教练查询
        coaches_content_query = Q()
        # 平台外文章查询
        non_platform_articles_content_query = Q()
        for item in seg:
            # 文章内容，标题，简介
            articles_content_query |= Q(content__icontains=item) | Q(title__icontains=item) | Q(brief__icontains=item)
            # 教练数据查询

            coaches_content_query |= (Q(personal_name__icontains=item) |  # 对个人用户可见姓名
                                      Q(user__true_name__icontains=item) |  # 用户名
                                      Q(city__icontains=item) |  # 城市
                                      Q(resumes__coach_language__icontains=item) |  # 语言
                                      Q(resumes__english_name__icontains=item) |  # 英文名
                                      Q(resumes__work_experience__icontains=item) |  # 工作经历
                                      Q(resumes__job_profile__icontains=item) |  # 一句话介绍工作经历
                                      Q(resumes__customer_evaluate__icontains=item) |  # 客户评价
                                      Q(resumes__coach_domain__icontains=item) |  # 教练领域
                                      Q(resumes__coach_industry__icontains=item) |  # 教练过的行业
                                      Q(resumes__coach_experience__icontains=item) |  # 教练经验
                                      Q(resumes__coach_style__icontains=item))  # 教练风格
            working_years_value = CoachWorkingYearsEnum.get_value_from_description(item)
            if working_years_value:
                coaches_content_query |= Q(resumes__working_years=working_years_value)   # 工作年限
            coach_auth_value = CoachAuthEnum.get_value_from_description(item)
            if coach_auth_value:
                coaches_content_query |= Q(resumes__coach_auth=coach_auth_value)  # 教练资质

            # 平台外文章查询
            non_platform_articles_content_query |= (Q(content__icontains=item) |  # 内容
                                                    Q(title__icontains=item) |  # 标题
                                                    Q(brief__icontains=item) |  # 简介
                                                    Q(tag__icontains=item))  # 标签

        # 合并主题ID和内容搜索的查询
        all_articles = Article.objects.filter(
            Q(article_theme_relation__theme_id__in=theme_ids, article_theme_relation__deleted=False) |
            articles_content_query, deleted=False, enabled=True
        ).distinct().order_by('-article_theme_relation__weight', '-article_theme_relation__created_at')

        # 基于版本，控制教练查询和是否查询公众号文章
        if mp and compare_version(mp.get('version'), '2.31') < 0:
            base_coaches = coach_public.get_bash_to_c_coach_list().prefetch_related('resumes')
            non_platform_articles = []
        else:
            base_coaches = coach_public.get_to_c_search_coach_list().prefetch_related('resumes')
            non_platform_articles = NonPlatformArticles.objects.filter(
                non_platform_articles_content_query, deleted=False).distinct().order_by('-created_at')
        # 使用主题ID和内容搜索教练
        all_coaches = base_coaches.filter(
            Q(coach_article_relation__theme_id__in=theme_ids, coach_article_relation__deleted=False) |
            coaches_content_query,
            resumes__deleted=False, resumes__is_customization=False
        ).distinct().order_by('-coach_article_relation__weight', '-coach_article_relation__created_at')

        # 文章和教练数据格式化为输出
        article_list, coach_list = theme_public.format_articles_and_coaches(
            all_articles, all_coaches, non_platform_articles)

        return success_response({"article": article_list, "coach": coach_list})
