from django.conf.urls import url
from django.urls import include

from rest_framework.routers import Default<PERSON><PERSON><PERSON>

from .views import coach, interview, action_plan, question, user, company, company_member, update, project, article, \
    project_member, evaluation, interview_record_template, slide_show, interview_question, interview_answer, \
    project_coach, project_interview, content_operation_article, content_operation_base, content_operation_key, \
    content_operation_tag, content_operation_browsing_history, content_operation_project, project_docs, role, \
    user_backend, trainee_coach, coach_task, resume, total_template, health_check, tasks, growth_goals, \
    project_interested, change_observation, personal_report, customer_portrait, user_notice_record, personal_user, \
    quick_link, contract, personal_apply, project_offer, chemical_interview, stakeholder_interview, project_service, \
    project_member_service, schedule, activity, public_courses, business_order, business_settlement, theme, \
    public_courses_coach, project_settlement, company_interview, coze, platform_notification
from wisdom_v2.app_views import app_interview, app_project_member, app_rtc, app_interview_record, app_coach, \
    app_time, app_sts, app_my, app_feedback, app_upload_image, wechat_auth, app_coachee_diary, app_coachee_action_plan, \
    app_coachee_habit, app_coachee_action_list, app_coachee_learn_article, app_coach_schedule, app_project_note, \
    app_evaluation_question, app_evaluation_answer, app_evaluation, app_evaluation_report, app_interview_offline_record, \
    app_sms, app_sync_calendar, app_wechat_register, app_trainee_coach, app_trainee_coachee, \
    app_coachee_growth, app_xiaoe_tech_user, work_wechat, app_article, app_user_additional_info, app_change_observation, \
    app_resume, app_order, app_coupon, app_user, app_quick_link, app_contract, app_coach_offer, app_chemical_interview, \
    app_stakeholder_interview, app_activity, app_business_order, app_theme, app_slide_show, app_personal_activity, \
    app_poster, app_coach_interview

router = DefaultRouter()

router.register(r'app/coach/calendar', app_coach_schedule.ScheduleViewSet, basename="app教练日历")
router.register(r'app/coach/schedule', app_coach_schedule.NewScheduleViewSet, basename="只计算可预约时长版教练日程")
router.register(r'interview', interview.InterviewViewSet, basename="约谈")
router.register(r'action_plan', action_plan.ActionPlanViewSet, basename="行动计划")
router.register(r'question', question.QuestionViewSet, basename="问题")
router.register(r'user', user.UserViewSet, basename="用户")
router.register(r'tasks', tasks.TasksViewSet, basename="异步任务")
router.register(r'company', company.CompanyViewSet, basename="企业")
router.register(r'company_member', company_member.CompanyMemberViewSet, basename="企业人员")
router.register(r'project', project.ProjectViewSet, basename="项目")
router.register(r'project_member', project_member.ProjectMemberViewSet, basename="项目成员")
router.register(r'project_coach', project_coach.FiveALCProjectCoachViewSet, basename="教练管理")
router.register(r'project_interview', project_interview.BackendProjectInterviewViewSet, basename="辅导管理")
router.register(r'project/customer/portrait', customer_portrait.CustomerPortraitBaseViewSet, basename="项目下的客户画像")
router.register(r'project_service', project_service.ProjectServiceViewSet, basename="项目服务")
router.register(r'project_member_service', project_member_service.ProjectMemberServiceViewSet, basename="项目服务")
router.register(r'article', article.ArticleViewSet, basename="文章")
router.register(r'slide_show', slide_show.SlideShowViewSet, basename="轮播图")
router.register(r'content_operation_article', content_operation_article.ContentOperationArticleViewSet, basename="内容展示(文章)")
router.register(r'content_operation_browsing_history', content_operation_browsing_history.ContentOperationBrowsingHistoryViewSet, basename="内容展示(浏览记录)")
router.register(r'content_operation_base', content_operation_base.ContentOperationBaseViewSet, basename="内容展示(基础数据)")
router.register(r'content_operation_key', content_operation_key.ContentOperationKeyViewSet, basename="内容展示(关键字)")
router.register(r'content_operation_project', content_operation_project.ContentProjectViewSet, basename="内容展示(关键字)")
router.register(r'content_operation_tag', content_operation_tag.ContentOperationTagViewSet, basename="内容展示(能力标签)")
router.register(r'evaluation', evaluation.EvaluationViewSet, basename="测评")
router.register(r'evaluation_module', evaluation.ProjectMemberEvaluationViewSet, basename="测评管理")
router.register(r'project_docs', project_docs.ProjectDocsViewSet, basename="项目报告管理")
router.register(r'project_evaluation_report', project_docs.ProjectEvaluationReportViewSet, basename="项目测评报告管理")
router.register(r'role', role.RoleViewSet, basename="角色管理")
router.register(r'user_backend', user_backend.UserBackendViewSet, basename="后台用户管理")
router.register(r'interview_record_template', interview_record_template.InterViewRecordTemplateViewSet, basename="辅导记录模版")
router.register(r'interview_question', interview_question.InterViewRecordTemplateQuestionViewSet, basename="记录辅导模版问题")
router.register(r'interview_answer', interview_answer.InterviewRecordTemplateAnswerViewSet, basename="辅导记录回答")
router.register(r'coach', coach.CoachViewSet, basename="教练")
router.register(r'trainee_coaches', trainee_coach.TraineeCoachViewSet, basename="个人教练（组）")
router.register(r'trainee_coach', trainee_coach.TraineeCoachBaseViewSet, basename="个人教练")
router.register(r'trainee_coaches_client', trainee_coach.TraineeCoachClientViewSet, basename="个人教练客户评论")
router.register(r'trainee_coaches/interview_record', trainee_coach.TraineeCoachInterviewViewSet,
                basename="个人教练辅导记录")
router.register(r'admin_growth', growth_goals.AdminGrowthGoalsViewSet, basename="后台成长目标")
router.register(r'project_interested', project_interested.AdminProjectInterestedViewSet, basename="后台利益相关者")
router.register(r'change_observation', change_observation.AdminChangeObservationViewSet, basename="后台列表改变观察反馈")
router.register(r'user_notice_record', user_notice_record.UserNoticeRecordBaseViewSet, basename="用户通知记录")
router.register(r'personal_report', personal_report.PersonalReportViewSet, basename="后台个人报告")
router.register(r'personal/user', personal_user.PersonalUserViewSet, basename="后台个人用户")
router.register(r'app/change_observation', app_change_observation.ChangeObservationViewSet, basename="app 改变观察反馈")
router.register(r'app/article', app_article.AppArticleViewSet, basename="app 文章")
router.register(r'coach_task', coach_task.CoachTaskViewSet, basename="教练任务")
router.register(r'resume', resume.ResumeViewSet, basename="教练简历")
router.register(r'total_template', total_template.TotalTemplateViewSet, basename="总模版")
router.register(r'total_template_report', total_template.TotalTemplateReportViewSet, basename="总模版报告")
router.register(r'quick_link', quick_link.QuickLinkViewSet, basename="金刚区")
router.register(r'contract', contract.ContractViewSet, basename="合同")
router.register(r'personal_apply', personal_apply.PersonalApplyViewSet, basename="学员申请")
router.register(r'project_offer', project_offer.ProjectOfferViewSet, basename="项目邀请")
router.register(r'chemical_interview_module', chemical_interview.ChemicalInterviewModuleViewSet, basename='化学面谈配置')
router.register(r'chemical_interview', chemical_interview.ChemicalInterview2CoachViewSet, basename="化学面谈")
router.register(r'stakeholder_interview_module', stakeholder_interview.StakeholderInterviewModuleViewSet,
                basename="利益相关者访谈")
router.register(r'schedule', schedule.AdminScheduleViewSet, basename="日程")
router.register(r'activity', activity.AdminActivityViewSet, basename="活动")
router.register(r'project_settlement', project_settlement.AdminProjectSettlementViewSet, basename="项目结算")
router.register(r'public_courses', public_courses.AdminPublicCoursesViewSet, basename="公开课")
router.register(r'public_courses_coach', public_courses_coach.AdminPublicCoursesCoachViewSet, basename="教练公开课")
router.register(r'business_order', business_order.BusinessOrderViewSet, basename="订单")
router.register(r'settlements', business_settlement.BusinessSettlementViewSet, basename="结算单")
router.register(r'theme', theme.ThemeViewSet, basename="主题")
router.register(r'company_interview', company_interview.CompanyInterviewViewSet, basename="企业面试")
router.register(r'platform_notification', platform_notification.PlatformNotificationViewSet, basename="平台通知管理")


router.register(r'app/interview', app_interview.AppInterviewViewSet, basename="app 约谈")
router.register(r'app/interview_template', app_interview.AppInterviewRecordTemplateViewSet, basename="app 约谈相关")
router.register(r'app/interview/record', app_interview_record.AppInterviewRecordViewSet, basename="app 约谈记录")
router.register(r'app/project/member', app_project_member.AppProjectMemberViewSet, basename="app 用户")
router.register(r'app/user', app_user.AppUserViewSet, basename="app 用户信息")
router.register(r'app/coach', app_coach.ProjectCoachViewSet, basename="app 项目教练")
router.register(r'app/coach_interview_data', app_coach_interview.AppCoachInterviewDataViewSet, basename="app 教练辅导数据")
router.register(r'app/trainee_coaches', app_trainee_coach.AppTraineeCoachBaseViewSet, basename="app 个人教练详情")
router.register(r'app/trainee_coachee', app_trainee_coachee.AppTraineeCoacheeViewSet, basename="app 见习学员相关")
router.register(r'my', app_my.AppMyViewSet, basename="app 教练/被教")
router.register(r'app/coachee/diary', app_coachee_diary.DiaryViewSet, basename="app被教成长笔记")
router.register(r'app/coachee/action_plan', app_coachee_action_plan.ActionPlanViewSet, basename="app被教行动计划")
router.register(r'app/coachee/habit', app_coachee_habit.HabitViewSet, basename="app被教习惯养成")
router.register(r'app/coachee/growth', app_coachee_growth.AppCoacheeGrowthGoalsViewSet, basename="app被教练者成长目标")
router.register(r'app/coachee/growth_node', app_coachee_growth.AppCoacheeGrowthNodeViewSet, basename="app被教练者成长节点")
router.register(r'app/coachee/learn_article', app_coachee_learn_article.LearnArticleViewSet, basename="app被教拓展学习")
router.register(r'app/project_note', app_project_note.ProjectNoteViewSet, basename="app项目笔记")
router.register(r'app/evaluation_question', app_evaluation_question.EvaluationQuestionViewSet, basename="app测评问题")
router.register(r'app/evaluation_answer', app_evaluation_answer.EvaluationAnswerViewSet, basename="app测评结果")
router.register(r'app/evaluation', app_evaluation.EvaluationViewSet, basename="app测评")
router.register(r'app/user_additional_info', app_user_additional_info.UserAdditionalInfoViewSet, basename="app测评")
router.register(r'app/evaluation_report', app_evaluation_report.EvaluationReportViewSet, basename="app测评")
router.register(r'app/offline/interview/record', app_interview_offline_record.AppOfflineInterviewRecordViewSet,
                basename="app 线下辅导约谈记录")
router.register(r'app/wechat_register', app_wechat_register.WechatRegisterViewSet, basename="微信扫码注册")
router.register(r'app/xiaoe_tech', app_xiaoe_tech_user.AppXiaoeTechUser, basename="小鹅通数据详情")
router.register(r'app/work_wechat', work_wechat.WorkWechatViewSet, basename="企业微信相关")
router.register(r'app/resume', app_resume.AppResumeViewSet, basename="app简历相关")
router.register(r'app/order', app_order.OrderViewSet, basename="小程序订单相关")
router.register(r'app/coupon', app_coupon.CouponViewSet, basename="小程序优惠券相关")
router.register(r'app/quick_link', app_quick_link.APPQuickLinkViewSet, basename="小程序金刚区列表")
router.register(r'app/contract', app_contract.AppContractViewSet, basename="小程序合同相关")
router.register(r'app/coach_offer', app_coach_offer.AppCoachOfferViewSet, basename="小程序offer相关")
router.register(r'app/chemical_interview', app_chemical_interview.AppChemicalInterview2CoachViewSet,
                basename="小程序化学面谈相关")
router.register(r'app/stakeholder_interview_module', app_stakeholder_interview.AppStakeHolderInterviewModuleViewSet,
                basename="小程序利益相关者访谈配置相关")
router.register(r'app/stakeholder_interview', app_stakeholder_interview.AppStakeHolderInterviewViewSet,
                basename="小程序利益相关者访谈相关")
router.register(r'app/activity', app_activity.AppActivityViewSet, basename="活动")
router.register(r'app/business_order', app_business_order.AppBusinessOrderViewSet, basename="小程序教练端订单")
router.register(r'app/theme', app_theme.AppThemeViewSet, basename="小程序端主题相关")
router.register(r'app/slide_show', app_slide_show.AppSlideShowViewSet, basename="小程序端轮播图相关")
router.register(r'app/personal_activity', app_personal_activity.AppPersonalActivityViewSet, basename="小程序端个人海报相关")
router.register(r'app/poster', app_poster.AppPosterViewSet, basename="小程序端轮播图相关")


urlpatterns = [
    url(r'^update$', update.UpdateModel.as_view(), name='更新'),
    url(r'^time$', app_time.GetTime.as_view(), name='获取服务器时间'),
    url(r'^sts$', app_sts.GetSts.as_view(), name='获取sts token'),
    url(r'^upload/file$', app_upload_image.UploadImage.as_view(), name='上传图片'),
    url(r'^chat/token$', app_rtc.RtcView.as_view(), name='获取rtc token'),
    url(r'^wechat_auth$',  wechat_auth.WeChatAuth.as_view(), name='获取rtc token'),
    url(r'^wechat_visitor_records$',  wechat_auth.WeChatVisitorRecords.as_view(), name='获取用户openid'),
    url(r'^app/feedback/add$', app_feedback.FeedBackView.as_view(), name='添加用户反馈'),
    url(r'^app/coachee/action/list$',  app_coachee_action_list.CoacheeActionListView.as_view(), name='app 被教行动清单'),
    url(r'^sms/code$', app_sms.SmsCode.as_view(), name='手机号获取验证码'),
    url(r'^email/code$', app_sms.EmailCode.as_view(), name='邮箱获取验证码'),
    url(r'^verify/code$', app_sms.VerifyCode.as_view(), name='验证验证码'),
    url(r'^coach/calendar/sync$', app_sync_calendar.SyncCalendar.as_view(), name='日程同步'),
    url(r'^coach/calendar/device_event_update$', app_sync_calendar.DeviceUpdate.as_view(), name='日程id同步'),
    url(r'^app/two_factor_certification$', app_wechat_register.TwoFactorCertificationViews.as_view(), name='二要素认证'),
    url(r'^app/health_check$', health_check.HealthCheckView.as_view(), name='服务健康检查'),
    url(r'^coze$', coze.CozeView.as_view(), name='coze'),
    url('^', include(router.urls))

]
