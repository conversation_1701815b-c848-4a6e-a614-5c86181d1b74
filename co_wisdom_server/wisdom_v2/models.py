import datetime
from decimal import Decimal

from django.contrib.auth.hashers import make_password, check_password
from django.db import models
from django.db.models import Sum, Q, When, Case
import uuid

from utils import validation_and_data_helpers
from utils.queryset import multiple_field_distinct
from wisdom_v2.enum.business_order_enum import WorkTypeEnum, BusinessOrderDurationTypeEnum
from wisdom_v2.views import constant
from wisdom_v2.views.constant import MANAGE_EVALUATION, INTERVIEW_TYPE_COACHING, ATTR_STATUS_INTERVIEW_CANCEL
from wisdom_v2.enum.company_enum import CompanyScaleEnum, CompanyIndustryEnum, CompanyAttrEnum, CompanyBusinessScaleEnum, CompanyDevelopmentStageEnum
from wisdom_v2.enum.user_enum import AreaCodeEnum, UserTmpEnum, EducationBackgroundEnum, CoachUserTypeEnum, \
    HourlySalaryEnum, UserRoleEnum, PersonalApplyStatusEnum, PersonalApplyTypeEnum, CoachInternToPersonalStatus
from wisdom_v2.enum.user_backend_enum import User<PERSON><PERSON>endTypeEnum
from wisdom_v2.enum.service_content_enum import \
    InterviewRecordTemplateTypeEnum, InterviewRecordTemplateRoleEnum, \
    InterviewRecordTemplateQuestionAnswerTypeEnum, InterviewRecordTemplateQuestionTypeEnum, \
    ContentOperationBaseInputChannelEnum, ContentOperationBaseKeyChannelEnum, ArticleTopicEnum, \
    InterviewRecordTemplateQuestionRatingTypeEnum, UserBehaviorDataTypeEnum, \
    EvaluationWriteRoleEnum, MultipleAssociationRelationTypeEnum, PersonalReportTypeEnum, \
    NewCoachTaskTypeEnum, CustomerPortraitTypeEnum, PortraitInterviewTypeEnum, NoticeChannelTypeEnum, \
    NoticeTemplateTypeEnum, ProjectCoachStatusEnum, PersonaSourceEnum, QuickLinkStatusEnum, UserInviteTypeEnum, \
    ScheduleTypeEnum, CoachTypeEnum, PowerTagEnum, DataTypeEnum, CoachAuthEnum, \
    CoachOfferStatusEnum, ProjectOfferStatusEnum, CoachSourceTypeEnum, AssociationTypeEnum, SuitableObjectEnum, \
    ChangeObservationInviteTypeEnum, ScheduleApplyTypeEnum, OneToOneMatchTypeEnum
from wisdom_v2.enum.project_interview_enum import ObjectTypeEnum, ProjectInterviewPlaceCategoryEnum, \
    InterviewRecordTypeEnum, TotalTemplateTypeEnum, TitleEnum, TemplateTypeEnum, ProjectInterviewRecordCompleteEnum, \
    ProjectInterviewTypeEnum, GroupCoachTypeEnum, ProjectInterviewPlaceTypeEnum, InterviewSubjectEnum

from wisdom_v2.enum.company_member_enum import ManageRoleEnum
from wisdom_v2.enum.project_enum import ProjectDocsTypeEnum, ProjectOfferWorkTypeEnum, ProjectStatusEnum, \
    ProjectEvaluationReportTypeEnum, ProjectInitiatorEnum, ProjectTypeEnum
from wisdom_v2.enum.project_member_enum import ProjectMemberRoleEnum
from wisdom_v2.enum.pay_enum import OrderStatusEnum, CommodityTypeEnum, SettlementChannelEnum, CommodityStatusEnum, \
    RefundStatusEnum, StockStatusEnum, StockTypesEnum, CouponStatusEnum, StockBusinessTypeEnum


class BaseModel(models.Model):
    created_at = models.DateTimeField('创建时间', auto_now_add=True)
    updated_at = models.DateTimeField('更新时间', auto_now=True, null=True)

    class Meta:
        abstract = True
        db_table = 'v2_base_model'
        verbose_name = '基础表'
        verbose_name_plural = verbose_name


class SubscriptionUser(models.Model):
    openid = models.CharField('微信服务号openid', max_length=130, null=True, unique=True)
    unionid = models.CharField('用户unionid', max_length=150, blank=True, null=True)
    subscribe = models.BooleanField('用户是否关注服务号', default=False)

    class Meta:
        db_table = 'v2_subscription_user'
        verbose_name = '公众号用户'
        verbose_name_plural = verbose_name


class GrowthGoals(BaseModel):

    public_attr = models.ForeignKey('PublicAttr', related_name='growth_goals_public_attr', on_delete=models.DO_NOTHING,
                                    db_constraint=False, null=True)
    content = models.TextField(null=True, help_text='成长目标的内容')
    tag = models.SmallIntegerField(null=True, choices=PowerTagEnum.choices(), default=PowerTagEnum.default(),
                                   help_text='能力标签')
    month = models.IntegerField(null=True, default=1, help_text='达成目标需要月数')
    change = models.TextField(null=True, default=None, help_text='达成目标后发生的改变')
    progression = models.IntegerField(null=True, default=0, help_text='完成度')
    is_finish = models.BooleanField(null=True, default=False, help_text='完成状态')
    deleted = models.BooleanField('是否删除', default=False)

    class Meta:
        db_table = 'v2_growth_goals'
        verbose_name = '用户成长目标'
        verbose_name_plural = verbose_name


class GrowthGoalsChange(BaseModel):
    growth_goals = models.ForeignKey('GrowthGoals', related_name='growth_goals_change', on_delete=models.DO_NOTHING,
                                     db_constraint=False, null=True)
    content = models.CharField(null=True, max_length=200, help_text='达成目标后发生的改变')
    deleted = models.BooleanField('是否删除', default=False)

    class Meta:
        db_table = 'v2_growth_goals_change'
        verbose_name = '用户成长目标改变表'
        verbose_name_plural = verbose_name


class User(models.Model):
    name = models.CharField(null=True, max_length=50, unique=True, help_text='用户名')
    true_name = models.CharField(null=True, max_length=50, help_text='用户真实姓名')
    english_name = models.CharField(null=True, max_length=32, help_text='用户英文名')
    password = models.CharField(null=True, max_length=250, help_text='用户密码')
    phone = models.CharField(max_length=60, null=True, unique=True, help_text='手机号码')
    area_code = models.IntegerField(choices=AreaCodeEnum.choices(),
                                    default=AreaCodeEnum.default(), help_text='区号')
    gender = models.IntegerField(null=True, help_text='性别 1: 男 2:女')
    email = models.CharField(null=True, max_length=50, unique=True, help_text='邮箱')
    head_image_url = models.CharField(max_length=200, null=True, help_text='头像')
    age = models.IntegerField(null=True, help_text='年龄')
    birthday = models.DateField(null=True, help_text='生日')
    last_active = models.DateTimeField(null=True, help_text='最后登录时间')

    openid = models.CharField(max_length=130, null=True, help_text='微信小程序openid')
    unionid = models.CharField(max_length=150, blank=True, null=True, help_text='微信小程序unionid')
    msg_openid = models.CharField(max_length=150, blank=True, null=True, help_text='微信服务号openid')

    user_cid = models.CharField(max_length=150, blank=True, null=True, help_text='app推送cid')

    message_id = models.CharField(max_length=200, null=True, help_text='im ID')
    message_pwd = models.CharField(max_length=200, null=True, help_text='im 密码')
    send_email = models.BooleanField(default=True, help_text='是否发送邮件')
    send_msg = models.BooleanField(default=True, help_text='是否信息')
    user_type = models.IntegerField(default=1,
                                    help_text='1:普通用户 2:网站管理员 3:项目管理员（只有网站管理员/项目管理员才能新建项目）')

    is_send_login_msg = models.BooleanField(default=False, help_text='是否发送过登录提醒通知')
    is_identity_check = models.BooleanField(default=False, help_text='是否实名认证')
    deleted = models.BooleanField('是否删除', default=False)
    created_at = models.DateTimeField('创建时间', auto_now_add=True, null=True)
    updated_at = models.DateTimeField('更新时间', auto_now=True, null=True)


    class Meta:
        db_table = 'v2_user'
        verbose_name = '用户表'
        verbose_name_plural = verbose_name

    def save(self, *args, **kwargs):
        # 如果 name 存在，则去除两端空格
        if self.name:
            self.name = self.name.strip()

        # 如果 email 存在，则去除两端空格
        if self.email:
            self.email = self.email.strip()

        super(User, self).save(*args, **kwargs)

    @property
    def cover_name(self):
        if self.true_name and self.phone == self.true_name:
            return validation_and_data_helpers.mask_phone_number(self.true_name)
        return self.true_name if self.true_name else self.name

    # 获取用户全部辅导次数
    def get_all_interview_count(self, coachee_user_id=None, coach_user_id=None, place_category=None):
        interview = ProjectInterview.objects.filter(type=INTERVIEW_TYPE_COACHING).exclude(deleted=True)
        if coachee_user_id:
            interview = interview.filter(public_attr__target_user_id=coachee_user_id)
        if coach_user_id:
            interview = interview.filter(public_attr__user_id=coach_user_id)
        if place_category:
            interview = interview.filter(place_category=place_category)
        return interview.count()

    # 获取小程序用户辅导记录
    def get_miniapp_interview(self, role=None, coachee_user_id=None, is_sort=True):
        # role 用户身份
        # coachee_user_id 教练查询指定用户辅导记录

        """
            状态描述 —— 是否删除 —— attr辅导状态（3-进行中，6-取消）
            B端正常辅导 —— False —— 3
            B端取消辅导 —— False —— 6
            C端正常支付的辅导 —— False —— 3
            C端退款的辅导 —— False —— 6
            C端未支付的辅导 —— True —— 3
        """
        # 排除C端未支付的辅导记录
        queryset = ProjectInterview.objects.filter(
            deleted=False).exclude(order__status=OrderStatusEnum.pending_pay)
        queryset = queryset.exclude(place_category=ProjectInterviewPlaceCategoryEnum.offline_group_coach,
                                    public_attr__status=ATTR_STATUS_INTERVIEW_CANCEL)

        # 根据用户身份信息先进行基础查询
        if role == UserRoleEnum.coach:
            if coachee_user_id:
                queryset = queryset.filter(public_attr__target_user_id=coachee_user_id)
            queryset = queryset.filter(public_attr__user_id=self.id)
        elif role == UserRoleEnum.trainee_coach:
            if coachee_user_id:
                queryset = queryset.filter(public_attr__target_user_id=coachee_user_id)
            queryset = queryset.filter(
                public_attr__project__isnull=True,
                public_attr__user_id=self.id)
        elif role == UserRoleEnum.coachee:
            queryset = queryset.filter(
                public_attr__project__isnull=False,
                public_attr__target_user_id=self.id)
        elif role == UserRoleEnum.trainee_coachee:
            queryset = queryset.filter(
                public_attr__project__isnull=True,
                public_attr__target_user_id=self.id)
        # 排序查询
        if is_sort:
            # 首先查询未确定的
            to_coach_agree = list(queryset.filter(is_coach_agree=False).exclude(
                public_attr__status=ATTR_STATUS_INTERVIEW_CANCEL).values_list('id', flat=True))
            # 查询进行中/未开始的
            not_completed = queryset.filter(
                is_coach_agree=True,
                public_attr__end_time__gte=datetime.datetime.now()
            ).exclude(public_attr__status=ATTR_STATUS_INTERVIEW_CANCEL).order_by(
                'public_attr__start_time')
            if role == UserRoleEnum.coach:
                not_completed = multiple_field_distinct(not_completed, ['topic', 'times', 'place', 'place_category',
                                                              'public_attr.start_time', 'public_attr.end_time'])
            not_completed = list(not_completed.values_list('id', flat=True))

            # 查询已结束/已取消
            completed_or_cancel= list(queryset.filter(
                Q(public_attr__status=ATTR_STATUS_INTERVIEW_CANCEL) | Q(
                    ~Q(public_attr__status=ATTR_STATUS_INTERVIEW_CANCEL),
                    is_coach_agree=True,
                    public_attr__end_time__lt=datetime.datetime.now()
                    )
                ).order_by('-public_attr__start_time').values_list('id', flat=True))

            queryset_sort_data = [*to_coach_agree, *not_completed, *completed_or_cancel]

            order = Case(*[When(pk=order, then=pos) for pos, order in enumerate(queryset_sort_data)])
            queryset = queryset.filter(id__in=queryset_sort_data).order_by(order)
            return queryset

        return queryset

    def set_password(self, raw_password):
        self.password = make_password(raw_password)
        self._password = raw_password

    def check_password(self, raw_password):
        def setter(raw_password):
            self.set_password(raw_password)
            self._password = None
            self.save(update_fields=["password"])

        return check_password(raw_password, self.password, setter)


class PersonalUser(BaseModel):
    user = models.ForeignKey(
        'User', related_name='personal_user',
        on_delete=models.DO_NOTHING,
        db_constraint=False, null=True, help_text='个人用户id')
    invite = models.ForeignKey(
        'UserInviteRecord', related_name='personal_user_invite_record',
        on_delete=models.DO_NOTHING,
        db_constraint=False, null=True, help_text='关联的用户id')
    source = models.IntegerField(
        choices=PersonaSourceEnum.choices(), default=PersonaSourceEnum.default())
    nickname = models.CharField(help_text='昵称', max_length=150, null=True, default=None)
    email = models.CharField(help_text='邮箱', max_length=50, null=True, default=None)
    deleted = models.BooleanField('是否删除', default=False)


    class Meta:
        db_table = 'v2_personal_user'
        verbose_name = '个人用户信息表'
        verbose_name_plural = verbose_name


class UserInviteRecord(BaseModel):
    uuid = models.UUIDField(default=uuid.uuid4, primary_key=True, help_text="UUID")
    referrer = models.ForeignKey(
        'User', related_name='invite_record_referrer_user',
        on_delete=models.DO_NOTHING,
        db_constraint=False, null=True, help_text='邀请的用户id')
    type = models.IntegerField(
        choices=UserInviteTypeEnum.choices(), default=UserInviteTypeEnum.default(), null=True)
    object_id = models.TextField(null=True, help_text='邀请类型对象id')
    deleted = models.BooleanField('是否删除', default=False)

    class Meta:
        db_table = 'v2_user_invite_record'
        verbose_name = '邀请记录表'
        verbose_name_plural = verbose_name


class UserBehaviorData(BaseModel):

    type = models.IntegerField(
        choices=UserBehaviorDataTypeEnum.choices(),
        default=UserBehaviorDataTypeEnum.default(),
        null=True, help_text='数据类型 | 1：首页关键字搜索 2：首页接口错误数据')
    user = models.ForeignKey('User', related_name='user_behavior_data',
                             on_delete=models.DO_NOTHING, db_constraint=False, null=True)
    content = models.JSONField(null=True, help_text='搜索数据')

    class Meta:
        db_table = 'v2_user_behavior_data'
        verbose_name = '用户行为数据记录'
        verbose_name_plural = verbose_name


class Company(models.Model):
    short = models.CharField(null=True, blank=True, max_length=50, help_text='公司简称')
    name = models.CharField(null=True, blank=True, max_length=50, help_text='公司名称')
    logo = models.CharField(max_length=250, null=True, blank=True, help_text='公司logo')
    brief = models.CharField(max_length=250, null=True, blank=True, help_text='公司简介')
    company_attr = models.IntegerField(choices=CompanyAttrEnum.choices(),
                                       default=CompanyAttrEnum.default(), help_text='公司属性')
    industry = models.IntegerField(choices=CompanyIndustryEnum.choices(),
                                   default=CompanyIndustryEnum.default(), help_text='公司行业')
    scale = models.IntegerField(choices=CompanyScaleEnum.choices(),
                                default=CompanyScaleEnum.default(), help_text='公司规模')
    video = models.CharField(max_length=250, null=True, blank=True, help_text='视频介绍')
    website = models.CharField(max_length=250, null=True, blank=True, help_text='公司官网')
    email = models.CharField(max_length=250, null=True, blank=True, help_text='公司邮箱(后缀)')
    logo_remark = models.CharField(max_length=250, null=True, blank=True, help_text='logo备注')
    # 新增的字段
    development_stage = models.IntegerField(choices=CompanyDevelopmentStageEnum.choices(), null=True, default=None, help_text="发展阶段")
    business_scale = models.IntegerField(choices=CompanyBusinessScaleEnum.choices(), null=True, default=None, help_text="业务规模")

    deleted = models.BooleanField('是否删除', default=False)
    created_at = models.DateTimeField('创建时间', auto_now_add=True, null=True)
    updated_at = models.DateTimeField('更新时间', auto_now=True, null=True)

    class Meta:
        db_table = 'v2_company'
        verbose_name = '企业表'
        verbose_name_plural = verbose_name

    @property
    def real_name(self):
        return self.short if self.short else self.name


class CompanyMember(models.Model):
    user = models.ForeignKey('User', related_name='user_company', on_delete=models.DO_NOTHING, db_constraint=False,
                             null=True, help_text='用户')
    company = models.ForeignKey('Company', related_name='company_user', on_delete=models.DO_NOTHING,
                                db_constraint=False, null=True, help_text='公司')

    # manage_role = models.IntegerField(blank=True, null=True,
    #                                   help_text='管理角色 1:一线经理 2:部门经理 3:事业部经理 4:事业部总经理 5:集团高管 6:首席执行官')
    manage_role = models.IntegerField(choices=ManageRoleEnum.choices(), default=None, null=True,
                                      help_text='管理角色 1:一线经理 2:部门经理 3:事业部经理 4:事业部总经理 5:集团高管 6:首席执行官')
    employee = models.IntegerField(blank=True, null=True, help_text='直系下属人数')
    position = models.CharField(blank=True, null=True, max_length=255, help_text='职位')
    department = models.CharField(blank=True, null=True, max_length=255, help_text='部门（事业部）')
    line_department = models.CharField(blank=True, null=True, max_length=255, help_text='部门')
    id_number = models.CharField(blank=True, null=True, max_length=50, help_text='身份证号')
    job_number = models.CharField(blank=True, null=True, max_length=50, help_text='工号')
    remark = models.CharField(blank=True, null=True, max_length=255, help_text='管理员备注')
    is_forbidden = models.BooleanField(default=False, help_text='是否归档')
    work_year = models.IntegerField(blank=True, null=True, help_text='工作年限')
    job_year = models.IntegerField(blank=True, null=True, help_text='本岗年限')
    company_year = models.IntegerField(blank=True, null=True, help_text='公司年限')

    created_at = models.DateTimeField('创建时间', auto_now_add=True, null=True)
    updated_at = models.DateTimeField('更新时间', auto_now=True, null=True)
    deleted = models.BooleanField('是否删除', default=False)

    class Meta:
        db_table = 'v2_company_member'
        verbose_name = '企业人员信息'
        verbose_name_plural = verbose_name
        unique_together = ('user', 'company')


class CompanyManage(models.Model):
    company_member = models.ForeignKey('CompanyMember', related_name='company_manage', on_delete=models.DO_NOTHING,
                                       db_constraint=False, null=True, help_text='企业用户')

    class Meta:
        db_table = 'v2_company_manage'
        verbose_name = '企业管理员'
        verbose_name_plural = verbose_name


class User2Project(models.Model):
    project = models.ForeignKey('Project', related_name='project_manager', on_delete=models.DO_NOTHING,
                                db_constraint=False, null=False, help_text='项目')
    user = models.ForeignKey('User', related_name='user_manager', on_delete=models.DO_NOTHING,
                             db_constraint=False, null=True, help_text='项目顾问')
    deleted = models.BooleanField('是否删除', default=False)
    created_at = models.DateTimeField('创建时间', auto_now_add=True, null=True)
    updated_at = models.DateTimeField('更新时间', auto_now=True, null=True)

    class Meta:
        db_table = 'v2_user2project'
        verbose_name = '项目顾问关系表'
        verbose_name_plural = verbose_name


class Project(models.Model):
    company = models.ForeignKey('Company', related_name='company_project', on_delete=models.DO_NOTHING,
                                db_constraint=False, null=True, help_text='项目所属公司')
    name = models.CharField(null=True, blank=True, max_length=50, help_text='项目名称')
    start_time = models.DateField(null=True, help_text='项目开始时间 已开始时间和结束时间来判定项目状态')
    end_time = models.DateField(null=True, help_text='项目结束时间')
    is_sign = models.BooleanField(default=True, help_text='是否签约')
    type = models.IntegerField(choices=ProjectTypeEnum.choices(), null=True, default=None,
                               help_text='区分项目类型 1:5ALC 2:MOP 3:无1对1辅导')
    status = models.IntegerField(choices=ProjectStatusEnum.choices(), default=ProjectStatusEnum.default(),
                                 null=True, help_text='项目状态 1:打单中 2:进行中 3:已停用 4:已完成 5:已输单')
    all_times = models.FloatField(default=0, help_text='线上一对一辅导总时长（小时）')
    offline_time = models.FloatField(default=0, help_text='线下一对一辅导总时长（只有5ALC有该字段）')
    background_requirement = models.TextField(null=True, help_text='项目背景-客户需求')
    background_expectation = models.TextField(null=True, help_text='项目背景-对教练的期望')
    background_coacheeinfo = models.TextField(null=True, help_text='项目背景-被教练者信息')
    background_remark = models.TextField(null=True, help_text='项目背景-备注')
    deleted = models.BooleanField('是否删除', default=False)
    created_at = models.DateTimeField('创建时间', auto_now_add=True, null=True)
    updated_at = models.DateTimeField('更新时间', auto_now=True, null=True)
    is_enable = models.BooleanField(default=True, help_text='是否启用 1：启动，0：停用')
    history_data = models.JSONField(null=True, default=None, help_text='逻辑删除数据')

    max_cost = models.IntegerField(null=True, default=None, help_text='最大费用')
    min_cost = models.IntegerField(null=True, default=None, help_text='最小费用')
    cost = models.TextField(null=True, default=None, help_text='项目费用')
    
    required_coach_count = models.IntegerField(null=True, default=None, help_text='教练数量')
    required_coachee_count = models.IntegerField(null=True, default=None, help_text='被教练着数量')
    promoter = models.TextField(null=True, default=None, help_text='由谁发起')
    initiator = models.IntegerField(choices=ProjectInitiatorEnum.choices(), null=True, default=None, help_text='发起方')
    reason = models.TextField(null=True, default=None, help_text='为什么发起')
    coach_content = models.TextField(null=True, default=None, help_text='包含的教练工作')
    providing_coach_list_time = models.DateField(null=True, default=None, help_text='最晚推送教练时间')
    is_action_plan_to_report = models.BooleanField(default=False, help_text='行动计划是否写入教练报告')
    is_show_path_diagram_hour = models.BooleanField(default=False, help_text='用户路径图是否显示辅导时长')
    interview_record_template = models.ForeignKey('TotalTemplate', related_name='interview_record_template_project', db_constraint=False,
                                        null=True, on_delete=models.DO_NOTHING, help_text='一对一辅导记录模板')
    is_collect_info = models.BooleanField(default=True, help_text='是否收集信息')
    internal_reference_info = models.TextField(null=True, blank=True, help_text='内部参考信息')
    
    class Meta:
        db_table = 'v2_project'
        verbose_name = '项目表'
        verbose_name_plural = verbose_name

    @property
    def full_name(self):
        project_name = f'{self.company.real_name}-{self.name}'
        return project_name

    @property
    def manager_list(self):
        managers = self.project_user_backend.filter(deleted=False, role__name='项目运营', project_id=self.id).order_by('-created_at') \
            .values_list('id', 'user__true_name', 'user__id', 'user__email', 'user__phone')
        result = []
        if managers:
            for manager in managers:
                data = {'id': manager[0], 'true_name': manager[1], 'user_id': manager[2], 'email': manager[3],
                        'phone': manager[4]}
                result.append(data)
        return result

    @property
    def account_manager(self):
        account_manager = self.project_user_backend.filter(
            deleted=False,
            role__name='客户顾问',
            project_id=self.id).first()
        if account_manager:
            data = {'id': account_manager.id, 'true_name': account_manager.user.cover_name, 'user_id': account_manager.user.id}
            return data
        return

    @property
    def admin_list(self):
        admin = self.project_user_backend.filter(
            deleted=False,
            role__name='企业管理员',
            project_id=self.id).order_by('-created_at') \
            .values_list('id', 'user__true_name', 'user__id', 'user__email', 'user__name')
        result = []
        if admin:
            for item in admin:
                data = {'id': item[0], 'true_name': item[1], 'user_id': item[2], 'email': item[3], 'name': item[4]}
                result.append(data)
        return result

    @property
    def project_progress(self):

        # 项目辅导时长统一使用all_times
        used_times = ProjectInterview.objects.filter(coach_record_status=True, public_attr__project=self, deleted=False,
                                                     public_attr__type=1).aggregate(used_times=Sum('times'))
        used_hours = used_times.get('used_times', 0) if used_times.get('used_times', 0) else 0
        used_hours = round(used_hours/60, 1)
        all_hours = self.all_times if self.all_times else 0
        return '%g小时/%g小时' % (used_hours, all_hours)

    @property
    def project_member_count(self):
        return self.project_member.exclude(deleted=True).count()

    @property
    def coach_count(self):
        project_coach_count = list(ProjectCoach.objects.filter(
            project=self, deleted=False, member__isnull=True, project_group_coach__isnull=True).values_list('coach_id', flat=True))
        return str(len(project_coach_count)) if project_coach_count else '--'

    @property
    def project_interview_count(self):
        count = ProjectInterview.objects.filter(public_attr__project=self, public_attr__type=1, deleted=False).\
            exclude(public_attr__status=6).count()
        return str(count) if count else '--'

    @property
    def project_evaluation_count(self):
        if not EvaluationModule.objects.filter(deleted=False, project_bundle__deleted=False,
                                               project_bundle__project=self).exists():
            return '--'
        else:
            evaluation_modules = EvaluationModule.objects.filter(deleted=False, project_bundle__deleted=False,
                                                                 project_bundle__project=self)
            count = evaluation_modules.count()
            for e_module in evaluation_modules:
                if e_module.evaluation.role == EvaluationWriteRoleEnum.coachee_stakeholder.value:
                    stakeholders = e_module.project_bundle.project_member.stakeholder
                    count += len(stakeholders)

            return str(count)

    @property
    def project_report(self):
        count = ProjectDocs.objects.filter(project=self, project_docs_type=ProjectDocsTypeEnum.report.value,
                                           file__deleted=False, deleted=False).count()

        coach_task_count = CoachTask.objects.filter(
            (Q(coach_submit_time__isnull=False, type=NewCoachTaskTypeEnum.default_task) |
             Q(coachee_submit_time__isnull=False, type=NewCoachTaskTypeEnum.default_task)) |
            Q(report_url__isnull=False, type=NewCoachTaskTypeEnum.stakeholder_research),
            project_bundle__project=self, deleted=False).count()
        count += coach_task_count

        evaluation_report_count = EvaluationReport.objects.filter(deleted=False, public_attr__project=self).exclude(
            evaluation__code=MANAGE_EVALUATION).count()
        count += evaluation_report_count

        # 个人报告不返回未完成的“利益相关者访谈纪要”
        # 个人报告不返回未完成的“利益相关者访谈总结报告”
        personal_report_count = PersonalReport.objects.filter(project=self, deleted=False).exclude(
            type=PersonalReportTypeEnum.notes_report.value, pdf_url__isnull=True).exclude(
            type=PersonalReportTypeEnum.summary_report.value, pdf_url__isnull=True).count()
        count += personal_report_count
        project_evaluation_report_count = ProjectEvaluationReport.objects.filter(project=self).count()
        count += project_evaluation_report_count
        return str(count)

    @property
    def project_material(self):
        files = self.project_docs.filter(project_docs_type=ProjectDocsTypeEnum.material.value). \
            order_by('-file__created_at').values_list('file__creator_id', 'file__file_type',
                                                      'file__file_name', 'file__file_path',
                                                      'file__created_at', 'file__id')
        if files:
            lst = []
            for file in files:
                lst.append({"user_id": file[0], "file_type": file[1], "file_name": file[2],
                            "file_path": file[3], "created_at": file[4], "file_id": file[5]})
            return lst
        else:
            return []
        
    @property
    def project_manager_feishu_at_info(self):
        manage_list = self.manager_list
        at_info = '' # 项目运营@信息
        if manage_list:
            user_backend_id = manage_list[0]['user_id']
            # id 转飞书 user_id
            backend_feishu_id_mapping = {
                '31681': 'a26f924a',
                '1357': '78ag3f2c',
                '31680': '79a98764'
            }
            feishu_user_id = backend_feishu_id_mapping.get(str(user_backend_id))
            if feishu_user_id:
                at_info = f'<at user_id="{feishu_user_id}">@</at>'
        return at_info


class ProjectMember(models.Model):
    user = models.ForeignKey('User', related_name='user_project', on_delete=models.DO_NOTHING, db_constraint=False,
                             null=True)
    project = models.ForeignKey('Project', related_name='project_member', on_delete=models.DO_NOTHING,
                                db_constraint=False, null=True)
    # role = models.IntegerField(null=True, help_text='用户角色: 4: 教练 5:企业管理员 6:被教练者 7:利益相关者 ')
    role = models.IntegerField(choices=ProjectMemberRoleEnum.choices(), default=ProjectMemberRoleEnum.default())
    help_text = '区分项目类型 1:5ALC 2:MOP 3:无1对1辅导'
    coachee_first_msg_status = models.BooleanField(default=False, help_text='教练前准备邮件是否发送--发送客户')
    week_interview_count = models.IntegerField(default=-1, help_text='每周辅导次数上限 -1:不做次数限制【1-5】次')
    one_interview_time = models.IntegerField(null=True, help_text='单次辅导时长上限（分钟）')
    all_interview_time = models.IntegerField(default=-1, help_text='该员工总辅导时长上限（分钟 -1:不做限制）')
    is_forbidden = models.BooleanField(default=False, help_text='是否归档')
    is_send_email = models.BooleanField(default=False, help_text='是否发送账号开通邮件')

    deleted = models.BooleanField('是否删除', default=False)
    created_at = models.DateTimeField('创建时间', auto_now_add=True, null=True)
    updated_at = models.DateTimeField('更新时间', auto_now=True, null=True)

    class Meta:
        db_table = 'v2_project_member'
        verbose_name = '项目所有角色人员信息（除教练角色）'
        verbose_name_plural = verbose_name
        unique_together = ('user', 'project', 'role')

    @property
    def is_stage(self):
        from wisdom_v2.models_file import ServiceStage
        stage = ServiceStage.objects.filter(project_member_id=self.id, deleted=False).first()
        return stage.is_stage if stage else None

    @property
    def company_id(self):
        return self.project.company_id

    @property
    def company_member_id(self):
        company_member = CompanyMember.objects.filter(company=self.project.company, user=self.user).first()
        return company_member.id

    @property
    def coach_match_type(self):
        one_to_one_coach = OneToOneCoach.objects.filter(project_bundle__project_member_id=self.id, deleted=False).first()
        return one_to_one_coach.coach_match_type if one_to_one_coach else None

    @property
    def coach_name(self):
        project_coach = ProjectCoach.objects.filter(
            member_id=self.user_id, project_id=self.project_id, deleted=False).first()
        if project_coach:
            return project_coach.coach.user.cover_name
        return ''

    @property
    def coach_id(self):
        # 返回教练id
        project_coach = ProjectCoach.objects.filter(member_id=self.user_id, project_id=self.project_id,
                                                    deleted=False).first()
        if project_coach:
                return project_coach.coach.id
        return None


    @property
    def stakeholder(self):
        project_interested = ProjectInterested.objects.filter(master_id=self.user_id, project_id=self.project_id,
                                                              deleted=False).order_by('-id').\
            values('interested__name', 'id', 'interested__true_name', 'interested__phone', 'relation', 'deleted',
                   'interested_id')
        stakeholder = []
        if project_interested:
            for info in project_interested:
                company_member = CompanyMember.objects.filter(company=self.project.company, user_id=info['interested_id']).first()
                company_member_id = company_member.id if company_member else None
                data = {"id": info['id'], "name": info['interested__name'], 'company_member_id': company_member_id,
                        "true_name": info['interested__true_name'] if info['interested__true_name'] else '',
                        "phone": info['interested__phone'] if info['interested__phone'] else '',
                        "relation": info['relation'], "deleted": info['deleted'], 'user_id': info['interested_id']}
                stakeholder.append(data)
        return stakeholder

    @property
    def position(self):
        result = self.project.company.company_user.filter(user_id=self.user_id, user__deleted=False).values('position')
        if result:
            if result[0].get('position'):
                return result[0].get('position')
            else:
                return ' '
        return ' '

    @property
    def department(self):
        result = self.project.company.company_user.filter(user_id=self.user_id, user__deleted=False).values(
            'department')
        if result:
            if result[0].get('department'):
                return result[0].get('department')
            else:
                return ' '
        return ' '

    @property
    def online_progress(self):
        project_bundle = self.project_bundle.filter(deleted=False).first()
        if project_bundle:
            online = project_bundle.one_to_one_coach.filter(type=CoachTypeEnum.online.value, deleted=False)
            if online.exists():
                used_times = ProjectInterview.objects.filter(
                    place_category=1, type=1, public_attr__project=self.project,
                    public_attr__target_user=self.user, deleted=False,
                    public_attr__type=1).exclude(public_attr__status=6).aggregate(used_times=Sum('times'))
                used_times = used_times.get('used_times', 0) if used_times.get('used_times', 0) else 0
                used_times = round(used_times/60, 1)

                # 新版服务配置不用区分项目类型，online_time会记录辅导时长
                all_times = online.aggregate(used_times=Sum('online_time'))
                all_times = all_times.get('used_times', 0) if all_times.get('used_times', 0) else 0
                all_times_str = str(all_times) + '小时'
                return '%s/%s' % (used_times, all_times_str)
        return '--'

    @property
    def offline_progress(self):
        project_bundle = self.project_bundle.filter(deleted=False).first()
        if project_bundle:
            offline = project_bundle.one_to_one_coach.filter(type=CoachTypeEnum.offline.value, deleted=False).first()
            if offline:
                used_times = ProjectInterview.objects.filter(
                    place_category=ProjectInterviewPlaceCategoryEnum.offline_one_to_one.value, deleted=False,
                    public_attr__project=self.project, public_attr__target_user=self.user, public_attr__status=5,
                    public_attr__type=1, type=1).aggregate(used_times=Sum('times'))
                used_times = used_times.get('used_times', 0) if used_times.get('used_times', 0) else 0
                used_times = round(used_times/60, 1)
                all_times = str(offline.offline_available_time) + '小时'
                return '%s/%s' % (used_times, all_times)
        return '--'

    @property
    def coach_target(self):
        # todo 在教练任务模块中配置了ILDP教练目标时，才有该字段
        # 暂时返回str
        return ' '

    @property
    def service_content(self):
        # todo 服务内容待后续服务相关暂时返回''
        return ' '


class ProjectInterested(models.Model):
    interested = models.ForeignKey('User', related_name='interested_user', on_delete=models.DO_NOTHING,
                                   db_constraint=False, null=True, help_text='利益相关者（7）')
    master = models.ForeignKey('User', related_name='master_user', on_delete=models.DO_NOTHING,
                               db_constraint=False, null=True, help_text='利益相关对象（6）')
    project = models.ForeignKey('Project', related_name='project_member_user', on_delete=models.DO_NOTHING,
                                db_constraint=False, null=True)
    relation = models.CharField(max_length=50, null=True, help_text='上下级关系 1:上级 2：平级 3:下级')
    concert_years = models.IntegerField(null=True, help_text='合作年限')

    deleted = models.BooleanField('是否删除', default=False)
    created_at = models.DateTimeField('创建时间', auto_now_add=True, null=True)
    updated_at = models.DateTimeField('更新时间', auto_now=True, null=True)

    class Meta:
        db_table = 'v2_project_interested'
        verbose_name = '项目利益相关者'
        verbose_name_plural = verbose_name


class ProjectCoach(models.Model):
    coach = models.ForeignKey('Coach', related_name='user_coach', on_delete=models.DO_NOTHING,
                              db_constraint=False, null=True, help_text='教练')
    member = models.ForeignKey('User', related_name='user_member', on_delete=models.DO_NOTHING,
                               db_constraint=False, null=True, help_text='被教练者')
    project = models.ForeignKey('Project', related_name='project_user', on_delete=models.DO_NOTHING,
                                db_constraint=False, null=True)
    project_group_coach = models.ForeignKey('ProjectGroupCoach', null=True, related_name='project_coach',
                                            on_delete=models.DO_NOTHING, db_constraint=False)
    resume = models.JSONField(null=True, help_text='教练简历表的id列表')
    show_resume = models.ForeignKey('Resume', related_name='project_coach', on_delete=models.DO_NOTHING,
                              db_constraint=False, null=True, help_text='展示的简历id')
    matched_type = models.IntegerField(null=True, help_text='匹配类型 1：企业面试 2：化学面谈 ')
    status = models.IntegerField(
        null=True, default=ProjectCoachStatusEnum.default(), help_text='匹配状态 1: 通过 2: 待定 3:不合适')
    reason = models.CharField(null=True, max_length=255, help_text='通过')
    no_reason = models.CharField(null=True, max_length=255, help_text='不通过')
    deleted = models.BooleanField('是否删除', default=False)
    source_type = models.IntegerField(choices=CoachSourceTypeEnum.choices(), default=CoachSourceTypeEnum.default(),
                                      null=True, help_text='加入来源')
    created_at = models.DateTimeField('创建时间', auto_now_add=True, null=True)
    updated_at = models.DateTimeField('更新时间', auto_now=True, null=True)

    class Meta:
        db_table = 'v2_project_coach'
        verbose_name = '项目教练和被教练者'
        verbose_name_plural = verbose_name


class Coach(models.Model):
    user = models.ForeignKey('User', related_name='coach_user', on_delete=models.DO_NOTHING, db_constraint=False,
                             null=True)
    working_years = models.CharField(max_length=30, null=True, help_text='工作年限 ')
    # language = models.CharField(max_length=30, null=True, help_text='语言')
    brief = models.TextField(null=True, help_text='教练简介')
    work_experience = models.TextField(null=True, help_text='工作经历')
    domain = models.CharField(max_length=255, null=True, help_text='教练领域')
    customer_evaluate = models.TextField(null=True, help_text='客户评价')
    style = models.CharField(max_length=255, null=True, help_text='教练风格')
    industry = models.CharField(max_length=255, null=True, help_text='教练过的行业')
    qualification = models.CharField(max_length=100, null=True, help_text='资质')
    explain = models.TextField(null=True, help_text='选拔教练说明')

    specialty = models.CharField(max_length=255, null=True, help_text='专业')
    coach_auth = models.CharField(max_length=255, null=True, help_text='教练认证')
    coach_experience = models.TextField(null=True, help_text='教练经验')
    coach_background_experience = models.TextField(null=True, help_text='教练企业背景经验')
    degree = models.CharField(max_length=100, null=True, help_text='学位')
    coach_type = models.IntegerField(choices=CoachUserTypeEnum.choices(),
                                     default=None, help_text='教练类型', null=True)
    expected_hourly_salary = models.IntegerField(choices=HourlySalaryEnum.choices(),
                                                 default=None, null=True, help_text='期望最低时薪')
    address = models.CharField(max_length=255, null=True, help_text='收件地址')
    school = models.CharField(max_length=50, null=True, default=None, help_text='毕业学校')
    highest_degree = models.IntegerField(choices=EducationBackgroundEnum.choices(),
                                         default=None, help_text='最高学历', null=True)
    order_receiving_status = models.BooleanField('接单状态', default=True)
    # language = models.JSONField(null=True, default=None, help_text='语言')
    user_class = models.CharField(max_length=50, null=True, help_text='班次')
    posters_text = models.TextField(null=True, default=None, help_text='个人海报中的文本信息')
    city = models.TextField(null=True, default=None, help_text='所在城市')
    price = models.IntegerField(null=True, default=None, help_text='辅导价格，单位：分')
    platform_order_receiving_status = models.BooleanField('平台是否允许接单', default=False)
    personal_name = models.CharField(max_length=255, null=True, help_text='个人客户可见姓名')
    is_settlement_info = models.BooleanField('是否有结算信息', default=False)
    is_share_resume = models.BooleanField('是否可以分享简历（用户是否可以查看）', default=True)
    intern_to_personal_status = models.IntegerField(
        choices=CoachInternToPersonalStatus.choices(),default=None, help_text='见习教练转个人教练所在的阶段', null=True)
    is_skip_entrant = models.BooleanField('是否跳过入驻流程', default=False)
    platform_fee_rate = models.IntegerField(default=None, null=True, help_text='平台抽成比例，如果为空代表按照规则计算')

    deleted = models.BooleanField('是否删除', default=False)
    created_at = models.DateTimeField('创建时间', auto_now_add=True, null=True)
    updated_at = models.DateTimeField('更新时间', auto_now=True, null=True)


    @property
    def default_time_status(self):
        if self.coach_type in CoachUserTypeEnum.enterprise_coach_value():
            return True
        else:
            return True

    @property
    def display_price(self):
        # 如果是None或者0，直接返回。
        # 如果是其他数字，需要除以100，转换成元。
        if self.price:
            return Decimal(str(self.price)) / Decimal('100')
        return self.price

    class Meta:
        db_table = 'v2_coach'
        verbose_name = '教练者'
        verbose_name_plural = verbose_name


class TraineeCoach(BaseModel):
    user = models.ForeignKey('User', related_name='trainee_coach_user', on_delete=models.DO_NOTHING, db_constraint=False,
                             null=True)
    user_class = models.CharField(max_length=50, null=True, help_text='班次')
    extra_time = models.IntegerField(null=True, default=0, help_text='平台外一对一辅导时常')
    working_years = models.IntegerField(
        null=True, help_text='工作年限 | 1：一年以上，2：三年以上，3：五年以上')
    language = models.CharField(max_length=100, null=True, help_text='语言｜ 1：中文，2：英语，3：法语，4：日语')
    brief = models.TextField(null=True, help_text='教练简介')
    work_experience = models.TextField(null=True, help_text='工作经历')
    customer_evaluate = models.TextField(null=True, help_text='客户评价')
    style = models.TextField(null=True, help_text='教练风格')
    qualification = models.JSONField(null=True, default=dict, help_text='资质')
    industry = models.CharField(max_length=100, null=True, help_text='教练过的行业')
    domain = models.CharField(max_length=100, null=True, help_text='教练领域')
    coach_auth = models.IntegerField(
        null=True, help_text='教练认证 ｜ 1：ICF-ACC, 2：ICF-PCC, 3：ICF-MCC, 4：准ICF-PCC, 5：个人教练')
    resume = models.ForeignKey('Resume', related_name='trainee_coach', on_delete=models.DO_NOTHING, db_constraint=False,
                               null=True)
    deleted = models.BooleanField('是否删除', default=False)

    class Meta:
        db_table = 'v2_trainee_coach'
        verbose_name = '见习教练者'
        verbose_name_plural = verbose_name


class Resume(BaseModel):
    working_years = models.IntegerField(
        null=True, help_text='工作年限 | 1：一年以上，2：三年以上，3：五年以上，4：八年以上，5：十年以上')
    language = models.CharField(max_length=100, null=True, help_text='语言｜ 1：中文，2：英语，3：法语，4：日语')
    brief = models.TextField(null=True, help_text='教练简介')
    work_experience = models.TextField(null=True, help_text='工作经历')
    job_profile = models.TextField(null=True, default=None, blank=True, help_text='一句话介绍工作经历')
    customer_evaluate = models.TextField(null=True, help_text='客户评价')
    style = models.TextField(null=True, help_text='教练风格')
    qualification = models.JSONField(null=True, default=None, help_text='教练证书')
    industry = models.CharField(max_length=100, null=True, help_text='教练过的行业')
    domain = models.CharField(max_length=100, null=True, help_text='教练领域')
    coach_auth = models.IntegerField(
        choices=CoachAuthEnum.choices(), default=None,
        null=True, help_text='教练认证 ｜ 1：ICF-ACC, 2：ICF-PCC, 3：ICF-MCC, 4：通过PCC口试, 5：通过ACC口试')
    extra_time = models.IntegerField(null=True, default=0, help_text='平台外一对一辅导时常')
    deleted = models.BooleanField('是否删除', default=False)

    coach = models.ForeignKey('Coach', related_name='resumes', on_delete=models.DO_NOTHING, db_constraint=False,
                              null=True, default=None)

    tmp_coach_id = models.IntegerField(null=True, default=None)
    name = models.CharField(max_length=50, null=True, default=None, help_text='简历名称')
    creator = models.ForeignKey('User', related_name='resume_creator_id', on_delete=models.DO_NOTHING,
                                db_constraint=False, null=True, default=None)

    coach_language = models.JSONField(null=True, default=None, help_text='语言')
    english_name = models.CharField(null=True, max_length=32, help_text='用户英文名')
    head_image_url = models.CharField(max_length=200, null=True, help_text='头像')
    share_image_url = models.CharField(max_length=200, null=True, help_text='用户分享图')
    is_customization = models.BooleanField('是否是定制化简历', default=False)
    # 工作经验
    work_industry = models.JSONField(null=True, default=None, help_text='工作过的行业')
    highest_position = models.JSONField(null=True, default=None, help_text='曾担任过的最高职位')
    enterprise_attributes = models.JSONField(null=True, default=None, help_text='工作过的企业属性')
    # work_experience
    functional_module = models.JSONField(null=True, default=None, help_text='工作过的职能模块')

    # 教练经历
    coach_course = models.JSONField(null=True, default=None, help_text='受训经验')
    # working_years
    # coach_auth
    coach_industry = models.JSONField(null=True, default=None, help_text='教练过的行业')  # 刷industry字段数据
    coach_customer_level = models.JSONField(null=True, default=None, help_text='曾服务过客户的最高级别')
    coach_enterprise_attributes = models.JSONField(null=True, default=None, help_text='曾服务过的企业属性')
    coach_domain = models.JSONField(null=True, default=None, help_text='擅长的教练领域')  # 刷domain字段数据
    psychological_counseling_certification = models.JSONField(null=True, default=None, help_text='心理咨询认证')
    one_to_one_interview_time = models.FloatField(null=True, default=None, help_text='当前积累的1对1教练辅导时长')
    group_interview_time = models.FloatField(null=True, default=None, help_text='当前积累的团队教练辅导时长')
    coach_experience = models.TextField(null=True, default=None, help_text='教练经验')  # 刷brief字段
    coach_style = models.TextField(null=True, help_text='教练风格') # 刷style字段数据
    # customer_evaluate
    coach_record = models.JSONField(null=True, default=None, help_text='教练履历')
    # qualification
    evaluation_certification = models.JSONField(null=True, default=None, help_text='测评认证')
    other_certification = models.JSONField(null=True, default=None, help_text='测评认证')


    class Meta:
        db_table = 'v2_resume'
        verbose_name = '教练简历表'
        verbose_name_plural = verbose_name


class TraineeCoachInviteUser(BaseModel):
    user = models.ForeignKey(
        'User', related_name='trainee_coach_invited_user', on_delete=models.DO_NOTHING, db_constraint=False,
        null=True)
    trainee_coach = models.ForeignKey(
        'User', related_name='trainee_coach_invite', on_delete=models.DO_NOTHING, db_constraint=False,
        null=True)
    interview = models.ForeignKey(
        'ProjectInterview', related_name='trainee_coach_interview', on_delete=models.DO_NOTHING,
        db_constraint=False, null=True)
    user_type = models.IntegerField(null=True, help_text='用户类型 ｜ 1：企业用户，2：分享邀请用户')

    class Meta:
        db_table = 'v2_trainee_coach_invite_user'
        verbose_name = '个人教练者邀请辅导记录'
        verbose_name_plural = verbose_name


class PublicAttr(models.Model):
    uuid = models.UUIDField(default=uuid.uuid4, primary_key=True, help_text="UUID")
    project = models.ForeignKey('Project', related_name='project_schedule', on_delete=models.DO_NOTHING,
                                db_constraint=False, null=True)
    # 关联user 是因为只要是网站用户都能做约谈
    user = models.ForeignKey('User', related_name='interested_project', on_delete=models.DO_NOTHING,
                             db_constraint=False, null=True, help_text='对应用户')
    target_user = models.ForeignKey('User', related_name='target_user_project', on_delete=models.DO_NOTHING,
                                    db_constraint=False, null=True, help_text='其他关联用户')
    # 暂时3 6 状态可用   3：已预约（默认状态）4：待记录（辅导结束后挂断更新/过结束时间） 5：已结束（双方都填写完记录，暂时未用到） 6：取消
    status = models.IntegerField(null=True, help_text='约谈状态')
    result = models.TextField(null=True, help_text='结果（存报告）')
    start_time = models.DateTimeField(null=True, help_text='开始时间', db_index=True)
    end_time = models.DateTimeField(null=True, help_text='结束时间', db_index=True)
    # 约谈/约谈记录：user: 教练 target_user：约谈对象
    # 日程：user: 教练

    # 测评：user: 教练、用户、利益相关、管理员
    # LBI测评中利益相关者的target_user为测评用户id user与target_user一致为自评，user与target_user不一致为他评

    # 成长目标：user: 用户 target_user 教练
    # 教练任务：user: 教练 target_user 用户
    # 利益相关者填写教练任务：target_user为任务用户id，user为利益相关者id
    type = models.IntegerField(
        null=True, help_text='1: 约谈 2: 日程 3:成长笔记 4:习惯养成 5: 行动计划 6:项目笔记'
                             ' 7:拓展学习 8:测评结果 9:测评报告 10:成长目标 11:教练任务 12:改变观察 13:利益相关者填写教练任务 14:订单')
    created_at = models.DateTimeField('创建时间', auto_now_add=True, null=True)
    updated_at = models.DateTimeField('更新时间', auto_now=True, null=True)

    class Meta:
        db_table = 'v2_public_attr'
        verbose_name = '测评 日程 约谈公共表'
        verbose_name_plural = verbose_name


class EventType(models.Model):
    name = models.CharField(null=True, blank=True, max_length=50, help_text='名称： 测评 约谈 项目')
    code = models.CharField(null=True, blank=True, max_length=50, help_text='r1: LBI r2:约谈记录问题')
    remark = models.CharField(null=True, blank=True, max_length=50, help_text='备注')

    deleted = models.BooleanField('是否删除', default=False)
    created_at = models.DateTimeField('创建时间', auto_now_add=True, null=True)
    updated_at = models.DateTimeField('更新时间', auto_now=True, null=True)

    class Meta:
        db_table = 'v2_event_type'
        verbose_name = '测评类型 约谈类型 项目类型'
        verbose_name_plural = verbose_name


class ProjectInterview(models.Model):
    public_attr = models.ForeignKey('PublicAttr', related_name='interview_public_attr', on_delete=models.DO_NOTHING,
                                    db_constraint=False, null=True)
    topic = models.TextField(null=True, help_text='约谈议题')
    coachee_topic = models.TextField(null=True, help_text='学员约谈议题')
    place_category = models.IntegerField(choices=ProjectInterviewPlaceCategoryEnum.choices(),
                                         default=ProjectInterviewPlaceCategoryEnum.default(), help_text='辅导类型')
    place = models.TextField(null=True, help_text='其它平台或线下地址')
    place_type = models.IntegerField(choices=ProjectInterviewPlaceTypeEnum.choices(),
                                     default=ProjectInterviewPlaceTypeEnum.default(), null=True,
                                     help_text='1: 在线 2: 线下')

    type = models.SmallIntegerField(choices=ProjectInterviewTypeEnum.choices(),
                                    default=ProjectInterviewTypeEnum.default(), null=True,
                                    help_text='1: 正式约谈 2: 企业面试 3:化学面试 4:利益相关者访谈')
    interview_subject = models.SmallIntegerField(
        choices=InterviewSubjectEnum.choices(), default=InterviewSubjectEnum.default())
    close_reason = models.CharField(max_length=250, null=True, help_text='结束原因')
    times = models.CharField(null=True, max_length=255, help_text='约谈时长 分钟')
    interview_number = models.IntegerField(null=True, help_text='第几次约谈')
    coach_record_status = models.BooleanField(null=True, help_text='教练是否填写辅导记录', default=False)
    coachee_record_status = models.BooleanField(null=True, help_text='被教是否填写辅导记录', default=False)
    record_type = models.IntegerField(choices=InterviewRecordTypeEnum.choices(),
                                      default=InterviewRecordTypeEnum.default(), help_text='辅导记录类型')
    deleted = models.BooleanField('是否删除', default=False)
    is_settlement = models.BooleanField('是否结算', default=False)
    is_coach_agree = models.BooleanField('教练是否同意', default=True)  # C端辅导创建为False
    order = models.ForeignKey('Order', related_name='interview', on_delete=models.DO_NOTHING,
                              db_constraint=False, null=True, default=None)
    created_at = models.DateTimeField('创建时间', auto_now_add=True, null=True)
    updated_at = models.DateTimeField('更新时间', auto_now=True, null=True)


    @property
    def message_topic(self):
        default_topic = '教练辅导'
        if self.type == ProjectInterviewTypeEnum.chemical_interview:
            default_topic = '进行初步的沟通'
        # 优先显示客户填写的议题
        if self.coachee_topic and self.coachee_topic != default_topic:
            message_topic = self.coachee_topic
        else:
            message_topic = self.topic if self.topic else default_topic
        return message_topic

    @property
    # 获取填写状态
    def get_fillin_status(self):
        # 都未填写
        if not self.coach_record_status and not self.coachee_record_status:
            return ProjectInterviewRecordCompleteEnum.all_unfinished.value
        # 教练未填写
        elif not self.coach_record_status and self.coachee_record_status:
            return ProjectInterviewRecordCompleteEnum.coach_unfinished.value
        # 被教未填写
        elif self.coach_record_status and not self.coachee_record_status:
            return ProjectInterviewRecordCompleteEnum.member_unfinished.value
        # 都填写
        elif self.coach_record_status and self.coachee_record_status:
            return ProjectInterviewRecordCompleteEnum.finished.value
        return

    @property
    # 获取辅导状态
    def get_coaching_status(self):
        date = datetime.datetime.now()
        # 辅导已取消
        if self.public_attr.status == ATTR_STATUS_INTERVIEW_CANCEL:
            return constant.ADMIN_INTERVIEW_CANCEL
        # 教练未同意
        elif not self.is_coach_agree:
            return constant.ADMIN_COACH_AGREE
        # 结束时间小于当前时间-未开始
        elif self.public_attr.start_time < date:
            return constant.ADMIN_INTERVIEW_NOT_START
        # 开始时间小于等于当前时间，结束时间大于等于当前时间-进行中
        elif self.public_attr.start_time <= date <= self.public_attr.end_time:
            return constant.ADMIN_INTERVIEW_ONGOING
        # 结束时间小于当前时间-已完成
        elif self.public_attr.end_time < date:
            return constant.ADMIN_INTERVIEW_END
        return

    class Meta:
        db_table = 'v2_project_interview'
        verbose_name = '项目约谈'
        verbose_name_plural = verbose_name


class ProjectInterviewRecord(models.Model):
    interview = models.ForeignKey('ProjectInterview', related_name='interview_record', on_delete=models.DO_NOTHING,
                                  db_constraint=False, null=True)
    # 教练填写
    leader_capacity = models.ForeignKey('CapacityTag', related_name='record_capacity', on_delete=models.DO_NOTHING,
                                        db_constraint=False, null=True, help_text='能力标签-教练Q3')
    observation = models.TextField(null=True, help_text='客户模式的观察-Q2')
    coach_target = models.CharField(max_length=500, null=True, help_text='教练填写的辅导目标信息-教练Q1')
    discover = models.ForeignKey('Diary', null=True, related_name='discover_record', on_delete=models.DO_NOTHING,
                                 db_constraint=False, help_text='收获 成长笔记Q2-1')
    coachee_thought = models.ForeignKey('Diary', null=True, related_name='thought_record',
                                        on_delete=models.DO_NOTHING, db_constraint=False, help_text=' 成长笔记Q2-2')
    target_progress = models.IntegerField(null=True, help_text='用户发展目标评分（多大程度被解决，效果度评分）')
    harvest_score = models.IntegerField(null=True, help_text='投入度评分')

    satisfaction_score = models.IntegerField(null=True, help_text='满意度评分')
    coach_score = models.IntegerField(null=True, help_text='被教给教练的评价分')
    coach_comment = models.CharField(max_length=500, null=True, help_text='被教给教练的评价')
    created_at = models.DateTimeField('创建时间', auto_now_add=True, null=True)
    updated_at = models.DateTimeField('更新时间', auto_now=True, null=True)
    deleted = models.BooleanField('是否删除', default=False)

    class Meta:
        db_table = 'v2_interview_record'
        verbose_name = '约谈记录'
        verbose_name_plural = verbose_name


class ProjectInterviewOffLineRecord(models.Model):
    interview = models.ForeignKey('ProjectInterview', related_name='interview_offline_record',
                                  on_delete=models.DO_NOTHING,
                                  db_constraint=False, null=True)
    # 只有客户填写
    resistance = models.CharField(max_length=500, null=True, help_text='阻力')
    # action_plan = models.ForeignKey('ActionPlan', null=True, related_name='action_plan_record', on_delete=models.DO_NOTHING,
    #                                 db_constraint=False, help_text='解决阻力/行动计划')
    # change = models.ForeignKey('ActionPlan', null=True, related_name='change_record', on_delete=models.DO_NOTHING,
    #                            db_constraint=False, help_text='行为转变/行动计划')

    input_score = models.IntegerField(null=True, help_text='投入度评分')
    satisfaction_score = models.IntegerField(null=True, help_text='满意度评分')
    coach_score = models.IntegerField(null=True, help_text='被教给教练的评价分')
    coach_comment = models.CharField(max_length=500, null=True, help_text='被教给教练的评价')

    created_at = models.DateTimeField('创建时间', auto_now_add=True, null=True)
    updated_at = models.DateTimeField('更新时间', auto_now=True, null=True)
    deleted = models.BooleanField('是否删除', default=False)

    class Meta:
        db_table = 'v2_interview_offline_record'
        verbose_name = '约谈记录'
        verbose_name_plural = verbose_name


class Schedule(models.Model):
    public_attr = models.ForeignKey('PublicAttr', related_name='schedule_public_attr', on_delete=models.DO_NOTHING,
                                    db_constraint=False, null=True)
    title = models.TextField(null=True, help_text='日程标题')
    type = models.IntegerField(null=True, choices=ScheduleTypeEnum.choices(), help_text='1:日程 2:约谈 3:忙碌(线上平台) 4:可预约')
    apply_type = models.JSONField(
        null=True, choices=ScheduleApplyTypeEnum.choices(),
        help_text='应用类型 默认全部 1:个人辅导 2:企业项目 3:教练活动')
    platform = models.IntegerField(default=1, null=True, help_text='区分线上平台和移动端 1:移动端 2:线上平台 3:手机日历')
    schedule_id = models.IntegerField(null=True, help_text='如果是线上平台，这个id就是线上平台的schedule_id，否则为空')
    # device_event_id = models.CharField(max_length=255, null=True, help_text='手机上的日程id')
    # device_id = models.CharField(max_length=255, null=True, help_text='设备id')

    remark = models.TextField(null=True, help_text='备注')
    is_open_manage = models.BooleanField(default=True, null=True, help_text='管理员是否可查看主题和备注')
    is_all_day = models.BooleanField(default=False, help_text='是否是全天日程')

    deleted = models.BooleanField('是否删除', default=False)
    created_at = models.DateTimeField('创建时间', auto_now_add=True, null=True)
    updated_at = models.DateTimeField('更新时间', auto_now=True, null=True)

    class Meta:
        db_table = 'v2_schedule'
        verbose_name = '日程'
        verbose_name_plural = verbose_name


class MobileSchedule(models.Model):
    schedule = models.ForeignKey('Schedule', related_name='mobile_schedule', on_delete=models.DO_NOTHING,
                             db_constraint=False, null=True)
    title = models.CharField(max_length=255, null=True, help_text='日程标题')
    device_id = models.CharField(max_length=255, null=True, help_text='设备id')
    device_event_id = models.CharField(max_length=255, null=True, help_text='手机上的日程id')

    deleted = models.BooleanField('是否删除', default=False)
    created_at = models.DateTimeField('创建时间', auto_now_add=True, null=True)
    updated_at = models.DateTimeField('更新时间', auto_now=True, null=True)

    class Meta:
        db_table = 'v2_mobile_schedule'
        verbose_name = '手机日程'
        verbose_name_plural = verbose_name


class ProjectExam(models.Model):
    public_attr = models.ForeignKey('PublicAttr', related_name='exam_public_attr', on_delete=models.DO_NOTHING,
                                    db_constraint=False, null=True)
    event_type = models.ForeignKey('EventType', related_name='event_type', on_delete=models.DO_NOTHING,
                                   db_constraint=False, null=True)

    deleted = models.BooleanField('是否删除', default=False)
    created_at = models.DateTimeField('创建时间', auto_now_add=True, null=True)
    updated_at = models.DateTimeField('更新时间', auto_now=True, null=True)

    class Meta:
        db_table = 'v2_project_exam'
        verbose_name = '测评邀请'
        verbose_name_plural = verbose_name


class ActionPlan(models.Model):
    interview = models.ForeignKey('ProjectInterview', related_name='interview_action_plan', on_delete=models.DO_NOTHING,
                                  db_constraint=False, null=True)
    public_attr = models.ForeignKey('PublicAttr', related_name='action_plan_public_attr', on_delete=models.DO_NOTHING,
                                    db_constraint=False, null=True)
    coach_task = models.ForeignKey('CoachTask', related_name='coach_task_action_plan', on_delete=models.DO_NOTHING,
                                   db_constraint=False, null=True)
    content = models.TextField(null=True, help_text='标题')
    count = models.IntegerField(null=True, help_text='签到人数')
    status = models.SmallIntegerField(default=1, help_text='1: 进行中 2:已完成 3:已取消')
    end_date = models.DateField(null=True, help_text='结束日期')
    start_date = models.DateField(null=True, help_text='开始日期')
    # type = models.CharField(max_length=30, null=True, help_text='A:打卡 B:阅读 C:任务')
    creator_role = models.IntegerField(null=True, help_text='创建人角色 4:教练 6:被教练者')
    is_problem_resolve = models.BooleanField('是否是解决阻力的计划', default=False)

    deleted = models.BooleanField('是否删除', default=False)
    created_at = models.DateTimeField('创建时间', auto_now_add=True, null=True)
    updated_at = models.DateTimeField('更新时间', auto_now=True, null=True)

    class Meta:
        db_table = 'v2_action_plan'
        verbose_name = '行动计划'
        verbose_name_plural = verbose_name


class SignIn(models.Model):
    habit = models.ForeignKey('Habit', related_name='habit_sign_in', on_delete=models.DO_NOTHING,
                              db_constraint=False, null=True)
    continue_sign_count = models.IntegerField(default=0, help_text='连续签到次数')
    last_continue_sign_count = models.IntegerField(default=0, help_text='上次连续签到次数')
    sign_date = models.DateField(auto_now=True, null=True)

    created_at = models.DateTimeField('创建时间', auto_now_add=True, null=True)
    updated_at = models.DateTimeField('更新时间', auto_now=True, null=True)

    class Meta:
        db_table = 'v2_sign_in'
        verbose_name = '打卡'
        verbose_name_plural = verbose_name


class Habit(models.Model):
    interview = models.ForeignKey('ProjectInterview', related_name='interview_habit', on_delete=models.DO_NOTHING,
                                  db_constraint=False, null=True)
    public_attr = models.ForeignKey('PublicAttr', related_name='habit_public_attr', on_delete=models.DO_NOTHING,
                                    db_constraint=False, null=True)
    coach_task = models.ForeignKey('CoachTask', related_name='coach_task_habit', on_delete=models.DO_NOTHING,
                                   db_constraint=False, null=True)
    # 当...会停止...转变为... 字段分开保存
    when = models.TextField(null=True, help_text='行为转变 当...')
    stop = models.TextField(null=True, help_text='行为转变 会停止...')
    change = models.TextField(null=True, help_text='行为转变 转变为...')
    creator_role = models.IntegerField(null=True, help_text='创建人角色 4:教练 6:被教练者')
    # sign_in = models.BooleanField(default=False, help_text='是否打卡')
    status = models.SmallIntegerField(default=1, help_text='1: 进行中 2:已完成 3:已取消')
    start_date = models.DateField(null=True, help_text='开始日期')
    end_date = models.DateField(null=True, help_text='结束日期')

    deleted = models.BooleanField('是否删除', default=False)
    created_at = models.DateTimeField('创建时间', auto_now_add=True, null=True)
    updated_at = models.DateTimeField('更新时间', auto_now=True, null=True)

    class Meta:
        db_table = 'v2_habit'
        verbose_name = '习惯养成'
        verbose_name_plural = verbose_name


class Diary(models.Model):
    interview = models.ForeignKey('ProjectInterview', related_name='interview_diary', on_delete=models.DO_NOTHING,
                                  db_constraint=False, null=True)
    public_attr = models.ForeignKey('PublicAttr', related_name='diary_public_attr', on_delete=models.DO_NOTHING,
                                    db_constraint=False, null=True)
    coach_task = models.ForeignKey('CoachTask', related_name='coach_task_diary', on_delete=models.DO_NOTHING,
                                   db_constraint=False, null=True)
    status = models.SmallIntegerField(default=1, help_text='1: 进行中 2:已完成 3:已取消')
    content = models.TextField(null=True, help_text='内容')
    type = models.SmallIntegerField(default=1, help_text='1:用户正常填写 2:通过约谈记录问题填写')
    creator_role = models.IntegerField(null=True, help_text='创建人角色 4:教练 6:被教练者')

    deleted = models.BooleanField('是否删除', default=False)
    created_at = models.DateTimeField('创建时间', auto_now_add=True, null=True)
    updated_at = models.DateTimeField('更新时间', auto_now=True, null=True)

    class Meta:
        db_table = 'v2_diary'
        verbose_name = '成长笔记'
        verbose_name_plural = verbose_name


class ProjectNote(models.Model):
    interview = models.ForeignKey('ProjectInterview', related_name='interview_note', on_delete=models.DO_NOTHING,
                                  db_constraint=False, null=True)
    public_attr = models.ForeignKey('PublicAttr', related_name='project_note_public_attr', on_delete=models.DO_NOTHING,
                                    db_constraint=False, null=True)
    content = models.TextField(null=True, help_text='内容（教练约谈完成后填写的项目笔记')
    status = models.SmallIntegerField(default=1, help_text='1: 进行中 2:已完成 3:已取消')

    deleted = models.BooleanField('是否删除', default=False)
    created_at = models.DateTimeField('创建时间', auto_now_add=True, null=True)
    updated_at = models.DateTimeField('更新时间', auto_now=True, null=True)

    class Meta:
        db_table = 'v2_project_note'
        verbose_name = '项目笔记'
        verbose_name_plural = verbose_name


class UserInput(models.Model):
    public_attr = models.ForeignKey('PublicAttr', related_name='user_input_public_attr', on_delete=models.DO_NOTHING,
                                    db_constraint=False, null=True)
    text = models.TextField(null=True)
    created_at = models.DateTimeField('创建时间', auto_now_add=True, null=True)
    updated_at = models.DateTimeField('更新时间', auto_now=True, null=True)

    class Meta:
        db_table = 'v2_user_input'
        verbose_name = '用户填写'
        verbose_name_plural = verbose_name


class Docs(models.Model):
    creator = models.ForeignKey('User', related_name='docs_creator', on_delete=models.DO_NOTHING,
                                db_constraint=False, null=True)
    file_name = models.CharField(max_length=1024, null=True, help_text='上传文件名字')
    file_type = models.CharField(max_length=100, null=True, help_text='上传文件类型')
    file_path = models.CharField(max_length=1024, null=True, help_text='上传文件路径')

    deleted = models.BooleanField('是否删除', default=False)
    created_at = models.DateTimeField('创建时间', auto_now_add=True, null=True)
    updated_at = models.DateTimeField('更新时间', auto_now=True, null=True)

    class Meta:
        db_table = 'v2_docs'
        verbose_name = '项目报告表(上传)'
        verbose_name_plural = verbose_name


class ProjectDocs(models.Model):
    project = models.ForeignKey('Project', related_name='project_docs', on_delete=models.DO_NOTHING,
                                db_constraint=False, null=True)
    file = models.ForeignKey('Docs', related_name='docs_file', on_delete=models.DO_NOTHING,
                             db_constraint=False, null=True)
    project_docs_type = models.IntegerField(choices=ProjectDocsTypeEnum.choices(),
                                            default=ProjectDocsTypeEnum.default(),
                                            help_text='项目文件类型（1：项目资料 2：项目报告）')
    project_member = models.ForeignKey('ProjectMember', related_name='project_docs', on_delete=models.DO_NOTHING,
                                       db_constraint=False, null=True)
    project_interested = models.ForeignKey('ProjectInterested', related_name='project_interested',
                                           on_delete=models.DO_NOTHING, db_constraint=False, null=True)
    deleted = models.BooleanField('是否删除', default=False)

    class Meta:
        db_table = 'v2_project_docs'
        verbose_name = '项目报告关系'
        verbose_name_plural = verbose_name


class Question(models.Model):
    event = models.ForeignKey('EventType', related_name='event_question', on_delete=models.DO_NOTHING,
                              db_constraint=False, null=True)
    sort = models.IntegerField(null=True, help_text='问题排序')
    title = models.CharField(max_length=250, null=True, help_text='问题描述')
    hint_text = models.CharField(max_length=250, null=True, help_text='提示文案')
    option = models.TextField(null=True, help_text='选项')
    max_score = models.TextField(null=True, help_text='最大分数')
    subs = models.TextField(null=True, help_text='复合类型')
    editable = models.BooleanField(default=True, help_text='是否可编辑')
    option_title = models.CharField(max_length=255, null=True, help_text='选项提示文案')
    require = models.BooleanField(default=True, help_text='是否必填')
    answer_type = models.SmallIntegerField(null=True, help_text='回答类型 1: 单选 2: 多选 3: 文本 4：多项 5：复合 6：单选+富文本 7：下拉+单选')
    hint_info = models.TextField(null=True, help_text='复合问题')
    tag = models.SmallIntegerField(null=True, help_text='1：LDP 满意度 教练投入 2：约谈记录')
    deleted = models.BooleanField('是否删除', default=False)
    created_at = models.DateTimeField('创建时间', auto_now_add=True, null=True)
    updated_at = models.DateTimeField('更新时间', auto_now=True, null=True)

    class Meta:
        db_table = 'v2_event_question'
        verbose_name = '问题'
        verbose_name_plural = verbose_name


class UserAnswer(models.Model):
    creator = models.ForeignKey('User', related_name='answer_creator', on_delete=models.DO_NOTHING,
                                db_constraint=False, null=True)
    public_attr = models.ForeignKey('PublicAttr', related_name='answer_public_attr', on_delete=models.DO_NOTHING,
                                    db_constraint=False, null=True)
    question = models.ForeignKey('Question', related_name='event_question', on_delete=models.DO_NOTHING,
                                 db_constraint=False, null=True)
    answer = models.TextField(null=True, help_text='用户回答')
    order = models.SmallIntegerField(null=True, help_text='多个答案的时候记录，用来匹配答案关系')
    created_at = models.DateTimeField('创建时间', auto_now_add=True, null=True)
    updated_at = models.DateTimeField('更新时间', auto_now=True, null=True)

    class Meta:
        db_table = 'v2_project_event_answer'
        verbose_name = '用户回答'
        verbose_name_plural = verbose_name


class Log(models.Model):
    url = models.TextField('请求url', null=True)
    request_body = models.TextField('请求body', null=True)
    request_content_length = models.CharField('请求content_length', null=True, max_length=10)
    user_agent = models.TextField('请求user_agent', null=True)
    token = models.TextField('请求token', null=True)
    user_id = models.IntegerField('请求用户id', null=True)
    user_name = models.CharField('请求用户名', null=True, max_length=50)
    response_body = models.TextField('返回body', null=True)
    response_code = models.CharField('返回code', max_length=10, null=True)
    response_content_length = models.IntegerField('返回content_length', null=True)
    spend_time = models.IntegerField('请求花费时间', default=0)

    deleted = models.BooleanField('是否删除', default=False)
    created_at = models.DateTimeField('创建时间', auto_now_add=True)
    updated_at = models.DateTimeField('更新时间', auto_now=True, null=True)

    class Meta:
        db_table = 'v2_log'
        verbose_name = '日志'
        verbose_name_plural = verbose_name


class MessageTemplates(models.Model):
    code = models.CharField(max_length=100, help_text='唯一标识')
    title = models.CharField(max_length=100, help_text='标题')
    send_mail = models.BooleanField(default=1, help_text='0: 不发 1: 发')
    send_sms = models.BooleanField(default=1, help_text='0: 不发 1: 发')
    send_inner_message = models.BooleanField(default=1, help_text='0: 不发 1: 发')
    send_wechat = models.BooleanField(default=1, help_text='0: 不发 1: 发')
    description = models.TextField(null=True, help_text='描述')
    email_subject = models.TextField(null=True, help_text='邮件标题')
    email_body = models.TextField(null=True, help_text='邮件内容')
    inner_message_subject = models.TextField(null=True, help_text='站内信标题')
    inner_message_body = models.TextField(null=True, help_text='站内信内容')
    sms_body = models.TextField(null=True, help_text='短信内容')
    wechat_template_id = models.CharField(null=True, max_length=255, help_text='微信模版id')
    wechat_body = models.CharField(null=True, max_length=255, help_text='微信模版内容')

    deleted = models.BooleanField('是否删除', default=False)
    created_at = models.DateTimeField('创建时间', auto_now_add=True)
    updated_at = models.DateTimeField('更新时间', auto_now=True, null=True)

    class Meta:
        db_table = 'v2_message_templates'
        verbose_name = '模版消息'
        verbose_name_plural = verbose_name


class ProjectPermission(models.Model):
    project = models.ForeignKey('Project', related_name='project_permission', on_delete=models.DO_NOTHING,
                                db_constraint=False, null=True)
    type = models.IntegerField(null=True, help_text='企业/化学面谈。。。')
    global_limit = models.IntegerField(null=True, help_text='全局限制')
    single_limit = models.IntegerField(null=True, help_text='单个限制')
    start_time = models.DateTimeField(null=True, help_text='开始时间')
    end_time = models.DateTimeField(null=True, help_text='结束时间')
    deleted = models.BooleanField('是否删除', default=False)
    created_at = models.DateTimeField('创建时间', auto_now_add=True)
    updated_at = models.DateTimeField('更新时间', auto_now=True, null=True)

    class Meta:
        db_table = 'v2_project_permission'
        verbose_name = '项目权限'
        verbose_name_plural = verbose_name


class FeedBackImage(models.Model):
    url = models.CharField(max_length=500, null=True, help_text='反馈图片')
    feedback = models.ForeignKey('FeedBack', related_name='feedback_image', on_delete=models.DO_NOTHING,
                                 db_constraint=False, null=True)

    deleted = models.BooleanField('是否删除', default=False)
    created_at = models.DateTimeField('创建时间', auto_now_add=True)
    updated_at = models.DateTimeField('更新时间', auto_now=True, null=True)

    class Meta:
        db_table = 'v2_feedback_image'
        verbose_name = '反馈意见图片'
        verbose_name_plural = verbose_name


class FeedBack(models.Model):
    creator_user = models.ForeignKey('User', related_name='feedback_user', on_delete=models.DO_NOTHING,
                                     db_constraint=False, null=True)
    content = models.TextField(null=True, help_text='反馈内容')

    deleted = models.BooleanField('是否删除', default=False)
    created_at = models.DateTimeField('创建时间', auto_now_add=True)
    updated_at = models.DateTimeField('更新时间', auto_now=True, null=True)

    class Meta:
        db_table = 'v2_feedback'
        verbose_name = '反馈'
        verbose_name_plural = verbose_name


class SlideShow(models.Model):
    article = models.ForeignKey('Article', related_name='slide_show_article', on_delete=models.DO_NOTHING,
                                null=True, db_constraint=False)
    title = models.CharField(max_length=255, null=True, help_text='轮播图上文字')
    image_url = models.CharField(max_length=500, null=True, help_text='图片url')
    url = models.CharField(max_length=500, null=True, help_text='跳转url')
    url_type = models.SmallIntegerField(null=True, help_text='链接类型 | 1:文章 2:主题 3:外部链接 4:预约辅导 5:无跳转 6:小程序内部链接')
    order = models.IntegerField(null=True, help_text='排序')
    suitable_object = models.IntegerField(help_text='适用对象', choices=SuitableObjectEnum.choices(),
                                          default=SuitableObjectEnum.default())
    theme = models.ForeignKey('Theme', related_name='slide_show_theme', on_delete=models.DO_NOTHING,
                              null=True, db_constraint=False, default=None)
    enabled = models.BooleanField('启用', default=False)
    deleted = models.BooleanField('是否删除', default=False)
    created_at = models.DateTimeField('创建时间', auto_now_add=True)
    updated_at = models.DateTimeField('更新时间', auto_now=True, null=True)

    class Meta:
        db_table = 'v2_slideshow'
        verbose_name = '轮播图'
        verbose_name_plural = verbose_name


class CapacityTag(models.Model):
    interview = models.ForeignKey('ProjectInterview', related_name='interview_capacity_tag',
                                  on_delete=models.DO_NOTHING,
                                  db_constraint=False, null=True)
    title = models.TextField(null=True, help_text='能力标签')
    tag_type = models.SmallIntegerField(default=2, help_text='标签类型1:系统内置 2:教练/被教新增')
    select_type = models.SmallIntegerField(default=1, help_text='1:单选（教练Q3 成才笔记、计划清单、习惯养成） 2:多选（被教Q6-拓展学习标签）')

    deleted = models.BooleanField('是否删除', default=False)
    created_at = models.DateTimeField('创建时间', auto_now_add=True)
    updated_at = models.DateTimeField('更新时间', auto_now=True, null=True)

    class Meta:
        db_table = 'v2_capacity_tag'
        verbose_name = '能力标签'
        verbose_name_plural = verbose_name


class Article(models.Model):
    capacity_tag = models.ForeignKey('CapacityTag', related_name='article_capacity_tag', on_delete=models.DO_NOTHING,
                                     null=True, db_constraint=False)
    title = models.CharField(max_length=255, null=True, help_text='文章标题')
    brief = models.TextField(null=True, help_text='文章简介')
    content = models.TextField(null=True, help_text='文字内容')
    image_url = models.CharField(max_length=500, null=True, help_text='文章图片')
    order = models.IntegerField(null=True, help_text='排序')
    category = models.SmallIntegerField(default=1, help_text='文章类型 | 1:经验, 2:关于教练, 3:工具')
    topic = models.SmallIntegerField(
        choices=ArticleTopicEnum.choices(),
        null=True,
        help_text='文章主题 | 1:辅导下属, 2:向上管理, 3:横向协作,'
                  ' 4:领导风格, 5:决策授权, 6:课前学习, 7:释放压力')
    tag = models.SmallIntegerField(null=True, help_text='能力标签')
    top_at = models.DateTimeField(null=True, help_text='置顶时间')
    # topic_top_at = models.DateTimeField(null=True, help_text='主题置顶时间')
    duration = models.IntegerField(null=True, help_text='学习时长', default=0)

    enabled = models.BooleanField('启用', default=True)
    deleted = models.BooleanField('是否删除', default=False)
    created_at = models.DateTimeField('创建时间', auto_now_add=True)
    updated_at = models.DateTimeField('更新时间', auto_now=True, null=True)

    class Meta:
        db_table = 'v2_article'
        verbose_name = '文章'
        verbose_name_plural = verbose_name

    @property
    def get_is_top(self):
        return bool(self.top_at)

    @property
    def get_is_topic_top(self):
        return bool(self.topic_top_at)

    @property
    def get_duration(self):
        import re
        raw_content = str(self.content) + str(self.title)
        word = ''
        video = 0
        for item in re.findall('<[^>]+?>([^<]+?)(?=<)', raw_content.replace('\n', '')):
            word += item
        for item in re.findall('https:\/\/.+?_([^.]+)\.mp4', raw_content):
            video += int(item)
        return int(len(word) / 300 + video / 60 + 1)


class ArticleUserAction(models.Model):

    article = models.ForeignKey(
        'Article', related_name='article_user_action', on_delete=models.DO_NOTHING,
        null=True, db_constraint=False)
    public_attr = models.ForeignKey(
        'PublicAttr', related_name='article_user_public_attr', on_delete=models.DO_NOTHING,
        db_constraint=False, null=True)

    class Meta:
        db_table = 'v2_article_user_action'
        verbose_name = '文章关联用户行为记录'
        verbose_name_plural = verbose_name


class LearnArticle(models.Model):
    public_attr = models.ForeignKey('PublicAttr', related_name='learn_plan_public_attr', on_delete=models.DO_NOTHING,
                                    db_constraint=False, null=True)
    capacity_tag = models.ForeignKey('CapacityTag', related_name='learn_article_capacity_tag',
                                     on_delete=models.DO_NOTHING,
                                     null=True, db_constraint=False)
    article = models.ForeignKey('Article', related_name='learn_article', on_delete=models.DO_NOTHING,
                                null=True, db_constraint=False)
    source = models.SmallIntegerField(default=0, help_text='0: 教练推荐； 1:项目设置')
    status = models.SmallIntegerField(default=1, help_text='1: 进行中 2:已完成 3:已取消')

    deleted = models.BooleanField('是否删除', default=False)
    created_at = models.DateTimeField('创建时间', auto_now_add=True)
    updated_at = models.DateTimeField('更新时间', auto_now=True, null=True)

    class Meta:
        db_table = 'v2_learn_article'
        verbose_name = '教练选择的标签推荐文章/拓展学习'
        verbose_name_plural = verbose_name


class SMSToken(models.Model):
    token = models.CharField(max_length=6, null=False, help_text='随机数')
    purpose = models.SmallIntegerField(null=False, help_text='用途。1：重置密码；2：登录')
    mobile = models.CharField(max_length=20, null=False, help_text='手机号码')
    expired = models.DateTimeField(null=False, help_text='有效期')

    created_at = models.DateTimeField('创建时间', auto_now_add=True)
    updated_at = models.DateTimeField('更新时间', auto_now=True, null=True)

    class Meta:
        db_table = 'v2_sms_token'
        verbose_name = '短信验证码'
        verbose_name_plural = verbose_name


class Evaluation(models.Model):
    name = models.CharField(max_length=50, help_text='名称')
    brief = models.CharField(max_length=200, help_text='简要介绍')
    about = models.TextField(null=True, help_text='测评报告通用说明')
    image_url = models.CharField(max_length=500, null=True, help_text='配图')
    code = models.SmallIntegerField(null=True, help_text='测评编号')

    # role 角色
    role = models.IntegerField(choices=EvaluationWriteRoleEnum.choices(), null=True,
                               default=EvaluationWriteRoleEnum.default(),
                               help_text='填写角色 1-被教练者 2-被教练者，利益相关者')

    deleted = models.BooleanField('是否删除', default=False)
    created_at = models.DateTimeField('创建时间', auto_now_add=True, null=True)
    updated_at = models.DateTimeField('更新时间', auto_now=True, null=True)

    class Meta:
        db_table = 'v2_evaluation'
        verbose_name = '测评元类型'
        verbose_name_plural = verbose_name


class EvaluationQuestion(models.Model):
    evaluation = models.ForeignKey('Evaluation', related_name='evaluation_question', on_delete=models.DO_NOTHING,
                                   db_constraint=False)
    order = models.SmallIntegerField(null=True, help_text='问题排序')
    title = models.CharField(max_length=250, help_text='问题描述')
    multi = models.SmallIntegerField(default=1, help_text='0：不限选择数量；1：单选；2-n：制定数量多选')

    deleted = models.BooleanField('是否删除', default=False)
    created_at = models.DateTimeField('创建时间', auto_now_add=True, null=True)
    updated_at = models.DateTimeField('更新时间', auto_now=True, null=True)

    class Meta:
        db_table = 'v2_evaluation_question'
        verbose_name = '测评问题'
        verbose_name_plural = verbose_name


class EvaluationOption(models.Model):
    question = models.ForeignKey('EvaluationQuestion', related_name='question_option', on_delete=models.DO_NOTHING,
                                 db_constraint=False)
    order = models.SmallIntegerField(null=True, help_text='选项排序')
    score = models.SmallIntegerField(null=True, help_text='选项分数')
    title = models.CharField(null=True, max_length=250, help_text='问题描述')
    type = models.SmallIntegerField(default=0, help_text='0：选择题；1：输入题')

    deleted = models.BooleanField('是否删除', default=False)
    created_at = models.DateTimeField('创建时间', auto_now_add=True, null=True)
    updated_at = models.DateTimeField('更新时间', auto_now=True, null=True)

    class Meta:
        db_table = 'v2_evaluation_option'
        verbose_name = '测评问题选项'
        verbose_name_plural = verbose_name


class EvaluationAnswer(models.Model):
    public_attr = models.ForeignKey('PublicAttr', related_name='evaluation_answer_public_attr',
                                    on_delete=models.DO_NOTHING,
                                    db_constraint=False, null=True)
    option = models.ForeignKey('EvaluationOption', related_name='evaluation_answer_option',
                               on_delete=models.DO_NOTHING,
                               db_constraint=False, help_text='选择题代表被选择，输入题答案记录在answer字段')
    answer = models.TextField(null=True, help_text='输入类型问题的回答记录在这里')

    created_at = models.DateTimeField('创建时间', auto_now_add=True, null=True)
    updated_at = models.DateTimeField('更新时间', auto_now=True, null=True)

    class Meta:
        db_table = 'v2_evaluation_answer'
        verbose_name = '测评回答'
        verbose_name_plural = verbose_name


class EvaluationReport(models.Model):
    public_attr = models.ForeignKey('PublicAttr', related_name='evaluation_report_public_attr',
                                    on_delete=models.DO_NOTHING,
                                    db_constraint=False, null=True)
    evaluation = models.ForeignKey('Evaluation', related_name='evaluation_report', on_delete=models.DO_NOTHING,
                                   db_constraint=False)
    score = models.SmallIntegerField(null=True, help_text='测评得分')
    analysis = models.TextField(null=True, help_text='解析文案')
    tips = models.TextField(null=True, help_text='成长贴士文案')
    build_trust_score = models.FloatField(null=True, default=None, help_text='建立信任得分')
    promote_action_score = models.FloatField(null=True, default=None, help_text='促进行动得分')
    consensus_target_score = models.FloatField(null=True, default=None, help_text='共识目标得分')
    tutor_feedback_score = models.FloatField(null=True, default=None, help_text='辅导反馈得分')
    inspired_question_score = models.FloatField(null=True, default=None, help_text='启发提问得分')
    active_listen_score = models.FloatField(null=True, default=None, help_text='积极聆听得分')
    management_score_text = models.CharField(max_length=256, default=None, null=True, help_text='教练能力分析文案')
    power_tag = models.JSONField(null=True, default=None, help_text='待提升能力标签')
    lbi_analysis = models.JSONField(null=True, default=None, help_text='LBI测评分析结果')
    pdf_url = models.TextField(null=True, default=None, help_text='报告转换的pdf文件链接')
    company_manager_pdf_url = models.TextField(null=True, default=None, help_text='报告转换的pdf文件链接(后台企业管理查看lbi个人报告url)')

    created_at = models.DateTimeField('创建时间', auto_now_add=True, null=True)
    updated_at = models.DateTimeField('更新时间', auto_now=True, null=True)
    deleted = models.BooleanField('是否删除', default=False)

    class Meta:
        db_table = 'v2_evaluation_report'
        verbose_name = '测评回答'
        verbose_name_plural = verbose_name

    @property
    def get_report_type(self):
        evaluation_report_config = self.evaluation.evaluation_report_config.filter(deleted=False).first()
        return evaluation_report_config.type if evaluation_report_config else None


class EvaluationReportScore(models.Model):
    SECTION = (
        (0, '理解人'),
        (1, '共识目标'),
        (2, '启发人'),
        (3, '坚定行动'),
        (4, '发展人')
    )
    report = models.ForeignKey('EvaluationReport', related_name='evaluation_report_score', on_delete=models.DO_NOTHING,
                               db_constraint=False)
    score = models.SmallIntegerField(null=True, help_text='测评得分')
    section = models.SmallIntegerField(choices=SECTION, null=True, help_text='对应测评报告中的具体项目；如：理解人，共识目标')

    created_at = models.DateTimeField('创建时间', auto_now_add=True, null=True)
    updated_at = models.DateTimeField('更新时间', auto_now=True, null=True)
    advantage = models.CharField(null=True, default=None, max_length=256, help_text='优势')
    advance = models.CharField(null=True, default=None, max_length=256, help_text='进步')
    to_advance = models.CharField(null=True, default=None, max_length=256, help_text='待进步')
    score_average = models.SmallIntegerField(null=True, default=None, help_text='得分率')
    support_growth_target = models.JSONField(null=True, default=None, help_text='原始成长目标')
    deleted = models.BooleanField('是否删除', default=False)

    class Meta:
        db_table = 'v2_evaluation_report_score'
        verbose_name = '测评回答'
        verbose_name_plural = verbose_name


class ProjectBundle(models.Model):
    project = models.ForeignKey('Project', related_name='project_bundle', on_delete=models.DO_NOTHING,
                                db_constraint=False)
    project_member = models.ForeignKey('ProjectMember', related_name='project_bundle', on_delete=models.DO_NOTHING,
                                       db_constraint=False, null=True)
    # interview_type = models.SmallIntegerField(null=True, help_text='0: 线上1v1；1：线上集体辅导；2：线下1v1；3：线下集体辅导；'
    #                                                                '4：企业面谈；5：化学面谈')
    # evaluation = models.ForeignKey('Evaluation', related_name='bundle_evaluation', on_delete=models.DO_NOTHING,
    #                                db_constraint=False, null=True)
    # article = models.ForeignKey('Article', related_name='bundle_article', on_delete=models.DO_NOTHING,
    #                             null=True, db_constraint=False)

    times = models.SmallIntegerField(null=True, help_text='人均次数限制')
    suppose_date = models.DateTimeField(null=True, help_text='建议完成时间')

    deleted = models.BooleanField('是否删除', default=False)
    created_at = models.DateTimeField('创建时间', auto_now_add=True)
    updated_at = models.DateTimeField('更新时间', auto_now=True, null=True)

    class Meta:
        db_table = 'v2_project_bundle'
        verbose_name = '项目功能'
        verbose_name_plural = verbose_name


class InterviewRecordTemplate(models.Model):
    name = models.CharField(max_length=50, null=False, help_text='名称')
    type = models.IntegerField(
        choices=InterviewRecordTemplateTypeEnum.choices(),
        default=InterviewRecordTemplateTypeEnum.default(), help_text='教练形式 | 1：线下集体辅导 2-见习一对一 3-教练任务 '
                                                                     '4-化学面谈 5-利益相关者访谈')
    role = models.IntegerField(
        choices=InterviewRecordTemplateRoleEnum.choices(),
        default=InterviewRecordTemplateRoleEnum.default(), null=False, help_text='填写记录的角色 | 1：教练；2：被教练者')
    questions_order = models.TextField('问题排序列表', null=True, default='[]')
    preface = models.TextField('前言', null=True, default=None)
    modify = models.BooleanField(default=True, help_text='是否可修改')
    available = models.BooleanField(default=False, help_text='是否上架')

    deleted = models.BooleanField('是否删除', default=False)
    created_at = models.DateTimeField('创建时间', auto_now_add=True, null=True)
    updated_at = models.DateTimeField('更新时间', auto_now=True, null=True)

    class Meta:
        db_table = 'v2_interview_record_template'
        verbose_name = '记录模版元类型'
        verbose_name_plural = verbose_name


class InterviewRecordTemplateQuestion(models.Model):
    template = models.ForeignKey(
        'InterviewRecordTemplate',
        related_name='interview_record_template_question',
        on_delete=models.DO_NOTHING,
        db_constraint=False)
    type = models.IntegerField(
        choices=InterviewRecordTemplateQuestionTypeEnum.choices(),
        default=InterviewRecordTemplateQuestionTypeEnum.default(),
        help_text='问题类型 | 1:填空，2:单选，3:多选，4:评分')
    answer_type = models.IntegerField(
        null=True,
        choices=InterviewRecordTemplateQuestionAnswerTypeEnum.choices(),
        default=InterviewRecordTemplateQuestionAnswerTypeEnum.default(),
        help_text='回答格式类型 ｜1:普通文本框, 2:行动计划, 3:习惯养成, 4:成长笔记, 5：能力标签，6:多条回答文本框')
    rating_type = models.IntegerField(
        null=True,
        choices=InterviewRecordTemplateQuestionRatingTypeEnum.choices(),
        default=InterviewRecordTemplateQuestionRatingTypeEnum.default(),
        help_text='评分格式类型 ｜ 1:有效度，2:投入度，3:满意度')
    title = models.CharField(max_length=100, help_text='问题')
    title_describe = models.TextField(default=None, null=True, help_text='问题描述')
    answer_tips = models.TextField(default=None, null=True, help_text='问题提示')
    sub_tips = models.TextField(default=None, null=True, help_text='多条文本提示')
    required = models.BooleanField(default=False, help_text='是否必须填写')

    deleted = models.BooleanField('是否删除', default=False)
    created_at = models.DateTimeField('创建时间', auto_now_add=True, null=True)
    updated_at = models.DateTimeField('更新时间', auto_now=True, null=True)

    class Meta:
        db_table = 'v2_interview_record_template_question'
        verbose_name = '记录模版问题'
        verbose_name_plural = verbose_name


class InterviewRecordTemplateOption(models.Model):
    question = models.ForeignKey('InterviewRecordTemplateQuestion',
                                 related_name='interview_record_template_option',
                                 on_delete=models.DO_NOTHING,
                                 db_constraint=False)
    order = models.SmallIntegerField(null=True, help_text='选项排序')
    title = models.CharField(null=True, max_length=250, help_text='问题描述')
    user_defined = models.BooleanField('是否允许填写自定义内容', default=False)

    deleted = models.BooleanField('是否删除', default=False)
    created_at = models.DateTimeField('创建时间', auto_now_add=True, null=True)
    updated_at = models.DateTimeField('更新时间', auto_now=True, null=True)

    class Meta:
        db_table = 'v2_interview_record_template_option'
        verbose_name = '记录模版问题选项'
        verbose_name_plural = verbose_name


class InterviewRecordTemplateAnswer(models.Model):
    interview = models.ForeignKey('ProjectInterview', related_name='interview_record_answer',
                                  on_delete=models.DO_NOTHING,
                                  db_constraint=False, null=True)
    public_attr = models.ForeignKey('PublicAttr', related_name='interview_record_answer_public_attr',
                                    on_delete=models.DO_NOTHING,
                                    db_constraint=False, null=True)
    option = models.ForeignKey('InterviewRecordTemplateOption', related_name='interview_record_template_answer_option',
                               on_delete=models.DO_NOTHING, null=True,
                               db_constraint=False, help_text='选择题代表被选择，输入题答案记录在answer字段')
    question = models.ForeignKey('InterviewRecordTemplateQuestion',
                                 related_name='interview_record_template_answer_question',
                                 on_delete=models.DO_NOTHING,
                                 db_constraint=False, help_text='填空和评价没有option，只有question')
    answer = models.TextField(null=True, help_text='填空类型问题的回答')
    option_custom = models.TextField(null=True, help_text='单选多选类型问题的自定义内容')
    score = models.SmallIntegerField(null=True, help_text='打分类型问题的分数')

    created_at = models.DateTimeField('创建时间', auto_now_add=True, null=True)
    updated_at = models.DateTimeField('更新时间', auto_now=True, null=True)
    deleted = models.BooleanField('是否删除', default=False)


    class Meta:
        db_table = 'v2_interview_record_answer'
        verbose_name = '辅导记录回答'
        verbose_name_plural = verbose_name


class OneToOneCoach(BaseModel):
    project_bundle = models.ForeignKey('ProjectBundle', related_name='one_to_one_coach', on_delete=models.DO_NOTHING,
                                       db_constraint=False)
    type = models.IntegerField(choices=CoachTypeEnum.choices(), default=CoachTypeEnum.default(),
                               help_text='1-线上一对一, 2-线下一对一')
    online_available_time = models.FloatField(null=True, blank=True, help_text='线上一对一辅导可用时长')
    online_is_stakeholder_interview = models.BooleanField(default=True, help_text='是否包含利益相关者访谈, 1是， 0否')
    online_is_setting_guidance_suggestion = models.BooleanField(default=True, help_text='线上是否设置辅导建议, 1是， 0否')

    offline_available_time = models.FloatField(null=True, blank=True, help_text='线下一对一辅导可用时长')
    offline_is_setting_guidance_suggestion = models.BooleanField(default=True, help_text='线下是否设置辅导建议, 1是， 0否')
    online_coaching_ceiling = models.IntegerField(null=True, blank=True, help_text='每人每周辅导次数上限')
    online_office_coaching_ceiling = models.FloatField(null=True, blank=True, help_text='每人单次辅导时间上限')
    online_available_coaching_duration = models.IntegerField(null=True, blank=True, help_text='每人可用总辅导时长，0-无限制，1-设置时长')
    online_time = models.FloatField(null=True, blank=True, help_text='mop线上一对一辅导可用时长')
    suggested_interval = models.IntegerField(default=0, null=True, blank=True, help_text='建议间隔时间/周')
    suggested_duration = models.FloatField(default=0, null=True, blank=True, help_text='建议时长/小时')
    suggested_start_date = models.DateField(default=None, null=True, blank=True, help_text='建议开始时间')
    coach_match_type = models.IntegerField(
        choices=OneToOneMatchTypeEnum.choices(), null=True, default=None,help_text='1-指定教练, 2-教练池')
    deleted = models.BooleanField('是否删除', default=False)

    class Meta:
        db_table = 'v2_one_to_one_coach'
        verbose_name = '一对一辅导模块'
        verbose_name_plural = verbose_name


class GuidanceSuggestion(BaseModel):
    one_to_one_coach = models.ForeignKey('OneToOneCoach', related_name='guidance_suggestion',
                                         on_delete=models.DO_NOTHING, db_constraint=False)
    start_time = models.DateField(null=True, help_text='开始时间')
    end_time = models.DateField(null=True, help_text='结束时间')
    hours = models.FloatField(null=True, help_text='小时数')
    deleted = models.BooleanField('是否删除', default=False)

    class Meta:
        db_table = 'v2_guidance_suggestion'
        verbose_name = '辅导建议'
        verbose_name_plural = verbose_name


class GroupCoach(BaseModel):
    project_bundle = models.ForeignKey('ProjectBundle', related_name='coach_group',
                                       on_delete=models.DO_NOTHING, db_constraint=False)
    project_group_coach = models.ForeignKey('ProjectGroupCoach', null=True, related_name='coach_group_module',
                                            on_delete=models.DO_NOTHING, db_constraint=False)
    interview = models.ForeignKey('ProjectInterview', null=True, related_name='coach_group_module',
                                  on_delete=models.DO_NOTHING, db_constraint=False)
    theme = models.CharField(null=True, max_length=256, help_text='辅导主题')
    start_course_time = models.DateTimeField(null=True, help_text='上课时间')
    end_course_time = models.DateTimeField(null=True, help_text='下课时间')
    course_place = models.CharField(null=True, max_length=256, help_text='上课地点')
    inter_view_record_template = models.ForeignKey('InterviewRecordTemplate', related_name='coach_group',
                                                   on_delete=models.DO_NOTHING, db_constraint=False)

    deleted = models.BooleanField('是否删除', default=False)

    class Meta:
        db_table = 'v2_group_coach'
        verbose_name = '集体辅导模块'
        verbose_name_plural = verbose_name


class PowerTag(BaseModel):
    group_coach = models.ForeignKey('GroupCoach', null=True, related_name='power_tag', on_delete=models.DO_NOTHING,
                                    db_constraint=False)
    article = models.ForeignKey('Article', related_name='article_power_tag', on_delete=models.DO_NOTHING,
                                null=True, db_constraint=False)
    coach_task = models.ForeignKey('CoachTask', related_name='coach_task_power_tag', on_delete=models.DO_NOTHING,
                                   null=True, db_constraint=False)
    concrete_behavior = models.CharField(max_length=256, null=True, help_text='具体行为（ildp教练目标时可设置）')
    tag = models.IntegerField(choices=PowerTagEnum.choices(), default=PowerTagEnum.default(), help_text='能力标签')
    deleted = models.BooleanField('是否删除', default=False)

    class Meta:
        db_table = 'v2_power_tag'
        verbose_name = '能力标签'
        verbose_name_plural = verbose_name


class CoachTask(BaseModel):
    project_bundle = models.ForeignKey('ProjectBundle', related_name='coach_task',
                                       on_delete=models.DO_NOTHING, db_constraint=False)
    hours = models.FloatField(null=True, help_text='小时数')
    deleted = models.BooleanField('是否删除', default=False)
    template = models.ForeignKey("TotalTemplate", related_name='coach_task', on_delete=models.DO_NOTHING,
                                 null=True,
                                 db_constraint=False)
    public_attr = models.ForeignKey('PublicAttr', related_name='coach_task_attr', on_delete=models.DO_NOTHING,
                                    db_constraint=False, null=True)
    coach_submit_time = models.DateTimeField(null=True, help_text='教练提交任务报告时间')
    coachee_submit_time = models.DateTimeField(null=True, help_text='被教练提交任务报告时间')
    stakeholder_submit_time = models.DateTimeField(null=True, help_text='全部利益相关者完成时间间')
    type = models.IntegerField(
        choices=NewCoachTaskTypeEnum.choices(),
        default=NewCoachTaskTypeEnum.default(), null=True, help_text='任务类型')
    report_url = models.TextField(null=True, help_text='报告文件链接')

    class Meta:
        db_table = 'v2_coach_task'
        verbose_name = '教练任务模块'
        verbose_name_plural = verbose_name


class EvaluationModule(BaseModel):
    project_bundle = models.ForeignKey('ProjectBundle', related_name='evaluation_module',
                                       on_delete=models.DO_NOTHING, db_constraint=False)
    evaluation = models.ForeignKey('Evaluation', related_name='evaluation_module',
                                   on_delete=models.DO_NOTHING, db_constraint=False, null=True)
    start_time = models.DateField(null=True, help_text='开始日期')
    end_time = models.DateField(null=True, help_text='截止日期')
    send_email = models.BooleanField(default=False, help_text='今日是否发送邮件')
    deleted = models.BooleanField('是否删除', default=False)
    is_submit = models.BooleanField('是否提交', default=False)
    is_open_screen = models.BooleanField(help_text='是否开屏测评', default=False)
    submit_time = models.DateTimeField(null=True, default=None, help_text='测评提交时间')

    class Meta:
        db_table = 'v2_evaluation_module'
        verbose_name = '测评模块'
        verbose_name_plural = verbose_name


class DataViewPermission(BaseModel):
    type = models.IntegerField(choices=DataTypeEnum.choices(), default=DataTypeEnum.default(), help_text='数据类型')
    data_id = models.IntegerField(null=True, help_text='数据对象id')
    role = models.CharField(max_length=1024, null=True,
                            help_text="角色 存储list样式str，包含各角色值 1-被教练者 2-企业管理员 \
                                       3-一对一辅导教练 4-项目主管/顾问 5-集体辅导教练")
    deleted = models.BooleanField('是否删除', default=False)

    class Meta:
        db_table = 'v2_data_view_permission'
        verbose_name = '数据查看权限表'
        verbose_name_plural = verbose_name


class ArticleModule(BaseModel):
    project_bundle = models.ForeignKey('ProjectBundle', related_name='article_module',
                                       on_delete=models.DO_NOTHING, db_constraint=False)
    article = models.ForeignKey('Article', related_name='article_module', on_delete=models.DO_NOTHING,
                                null=True, db_constraint=False)
    is_complete = models.BooleanField('是否完成', default=False)
    start_time = models.DateField(null=True, help_text='开始日期')
    end_time = models.DateField(null=True, help_text='截止日期')
    deleted = models.BooleanField('是否删除', default=False)

    class Meta:
        db_table = 'v2_article_module'
        verbose_name = '文章模块'
        verbose_name_plural = verbose_name


class ProjectGroupCoach(BaseModel):
    project = models.ForeignKey('Project', null=True, related_name='project_group_coach', on_delete=models.DO_NOTHING,
                                db_constraint=False)
    type = models.IntegerField(choices=GroupCoachTypeEnum.choices(), default=GroupCoachTypeEnum.default(),
                               help_text='1-集体辅导, 2-小组辅导')
    start_time = models.DateTimeField(null=True, help_text='辅导开始时间')
    end_time = models.DateTimeField(null=True, help_text='辅导结束时间')
    theme = models.CharField(max_length=256, null=True, help_text='辅导主题')
    place = models.CharField(max_length=256, null=True, help_text='教练地点')
    deleted = models.BooleanField('是否删除', default=False)

    class Meta:
        db_table = 'v2_project_group_coach'
        verbose_name = '项目线下集体辅导'
        verbose_name_plural = verbose_name


class QuestionObjectRelationship(BaseModel):
    type = models.IntegerField(choices=ObjectTypeEnum.choices(), default=ObjectTypeEnum.default(), help_text='对象类型')
    obj_id = models.IntegerField(null=True, help_text='数据对象id')
    answer = models.ForeignKey('InterviewRecordTemplateAnswer', related_name='object_relationship',
                               on_delete=models.DO_NOTHING, db_constraint=False)
    deleted = models.BooleanField('是否删除', default=False)

    class Meta:
        db_table = 'v2_answer_object_relationship'
        verbose_name = '答案-对象关系表'
        verbose_name_plural = verbose_name


class ContentOperationBase(BaseModel):

    date = models.DateField(null=True, help_text='数据对应日期')
    android_user = models.SmallIntegerField(null=True, default=0, help_text='安卓活跃用户数')
    ios_user = models.SmallIntegerField(null=True, default=0, help_text='ios活跃用户数')
    applets_user = models.SmallIntegerField(null=True, default=0, help_text='小程序活跃用户数')
    android_use_time = models.JSONField(null=True, default=dict, help_text='安卓活跃用户使用时长中位数（秒）')
    ios_use_time = models.JSONField(null=True, default=dict, help_text='ios活跃用户使用时长中位数（秒）')
    applets_use_time = models.JSONField(null=True, default=dict, help_text='小程序活跃用户使用时长中位数（秒）')

    class Meta:
        db_table = 'v2_content_operation_base'
        verbose_name = '内容展示基础数据'
        verbose_name_plural = verbose_name


class ContentOperationArticle(BaseModel):

    article = models.ForeignKey('Article', related_name='content_operation_article',
                                on_delete=models.DO_NOTHING, null=True, db_constraint=False)
    read_volume = models.SmallIntegerField(default=0, help_text='阅读量')
    median_browsing_time = models.SmallIntegerField(null=True, default=0, help_text='浏览中位数时长(秒)')

    class Meta:
        db_table = 'v2_content_operation_article'
        verbose_name = '内容展示文章数据'
        verbose_name_plural = verbose_name


class ContentOperationBrowsingHistory(BaseModel):

    project = models.ForeignKey('Project', related_name='content_operation_browsing_project', on_delete=models.DO_NOTHING,
                                db_constraint=False, null=True, help_text='项目')
    article = models.ForeignKey('ContentOperationArticle', related_name='content_operation_browsing_article',
                                on_delete=models.DO_NOTHING, null=True, db_constraint=False, help_text='内容管理文章数据')
    user = models.ForeignKey('User', related_name='content_operation_user', on_delete=models.DO_NOTHING, db_constraint=False,
                             null=True, help_text='用户')
    browse_date = models.DateField(help_text='浏览日期')
    browse_time = models.SmallIntegerField(null=True, default=0, help_text='浏览时长(秒)')

    class Meta:
        db_table = 'v2_content_operation_browsing_history'
        verbose_name = '内容展示记录'
        verbose_name_plural = verbose_name


class ContentOperationKeywords(BaseModel):
    project = models.ForeignKey('Project', related_name='content_operation_key_project', on_delete=models.DO_NOTHING,
                                db_constraint=False, null=True, help_text='项目')
    keywords = models.TextField(null=False, help_text='关键字')
    count = models.SmallIntegerField(null=False, default=0, help_text='次数')
    date = models.DateField(null=True, help_text='数据对应时间')
    channel = models.IntegerField(
        choices=ContentOperationBaseInputChannelEnum.choices(),
        default=ContentOperationBaseInputChannelEnum.default(), help_text='关键词来源 | 1:搜索，2:辅导记录')

    class Meta:
        db_table = 'v2_content_operation_key'
        verbose_name = '内容展示关键字记录'
        verbose_name_plural = verbose_name


class ContentOperationPowerTag(BaseModel):

    project = models.ForeignKey('Project', related_name='content_operation_tag_project', on_delete=models.DO_NOTHING,
                                db_constraint=False, null=True, help_text='项目')
    tag = models.IntegerField(
        choices=PowerTagEnum.choices(), default=PowerTagEnum.default(), null=False, help_text='能力标签')
    count = models.SmallIntegerField(help_text='次数', null=False, default=0)
    date = models.DateField(null=True, help_text='数据对应时间')
    channel = models.IntegerField(
        choices=ContentOperationBaseKeyChannelEnum.choices(),
        default=ContentOperationBaseKeyChannelEnum.default(), help_text='关键词来源 | 1:文章，2:辅导记录')

    class Meta:
        db_table = 'v2_content_operation_power_tag'
        verbose_name = '内容展示能力标签记录'
        verbose_name_plural = verbose_name


class Permission(BaseModel):
    name = models.CharField(
        max_length=64, help_text='权限名称，最多64个字符', null=True)
    parent = models.ForeignKey(
        'self', null=True, default=None, on_delete=models.CASCADE, name='parent',
        related_name='submenus')
    text = models.CharField(
        max_length=128, null=True, help_text='权限展示名称，最多128个字符')
    is_menus = models.BooleanField(help_text='是否为菜单', default=True,)
    is_element = models.BooleanField(help_text='是否为元素', default=False)
    order = models.IntegerField(default=1, help_text='排序')
    deleted = models.BooleanField('是否删除', default=False)

    class Meta:
        db_table = 'v2_permission'
        verbose_name = '权限表'
        verbose_name_plural = verbose_name

    @classmethod
    def add_permission(cls, permission_dict):
        for p_1_index, permission_info in enumerate(permission_dict):
            p_1 = cls.add_permission_menus(
                name=permission_info['name'], text=permission_info['text'], order=p_1_index + 1
            )
            submenus_list = permission_info.get('submenus', [])
            for p_2_index, submenus in enumerate(submenus_list):
                p_2 = p_1.add_permission_page(
                    name=submenus['name'], text=submenus['text'], order=p_2_index + 1)
                elements_list = submenus.get('elements', [])
                for p_3_index, elements in enumerate(elements_list):
                    p_2.add_permission_element(
                        name=elements['name'], text=elements['text'], order=p_3_index + 1)

    @classmethod
    def add_permission_menus(cls, name, text, order=1):
        """添加第一层菜单权限"""
        permission, created = cls.objects.get_or_create(name=name, deleted=False)
        permission.is_element = False
        permission.is_menus = True
        permission.order = order
        permission.text = text
        permission.save()
        return permission


    def add_permission_page(self, name, text, order=1):
        """添加第二层页面权限"""
        permission, created = Permission.objects.get_or_create(name=name, deleted=False)
        permission.parent_id = self.id
        permission.is_element = False
        permission.text = text
        permission.order = order
        permission.is_menus = True
        permission.save()
        return permission

    def add_permission_element(self, name, text, order=1):
        """添加第三层按钮等元素权限"""
        permission, created = Permission.objects.get_or_create(name=name, deleted=False)
        permission.parent_id = self.id
        permission.is_element = True
        permission.text = text
        permission.order = order
        permission.is_menus = False
        permission.save()
        return permission


class Role(BaseModel):
    name = models.CharField(max_length=64, help_text='权限名称，最多64个字符', null=True)
    deleted = models.BooleanField('是否删除', default=False)

    class Meta:
        db_table = 'v2_role'
        verbose_name = '角色表'
        verbose_name_plural = verbose_name


class Role2Permission(BaseModel):
    role = models.ForeignKey('Role', related_name='role_2_permission', on_delete=models.DO_NOTHING, db_constraint=False)
    permission = models.ForeignKey('Permission', related_name='permission_2_role', on_delete=models.DO_NOTHING,
                                   db_constraint=False)
    deleted = models.BooleanField('是否删除', default=False)

    class Meta:
        db_table = 'v2_role_2_permission'
        verbose_name = '权限角色关系表'
        verbose_name_plural = verbose_name


class UserBackend(BaseModel):
    user = models.ForeignKey('User', related_name='user_backend', on_delete=models.DO_NOTHING, db_constraint=False,
                             null=True, help_text='用户')
    role = models.ForeignKey('Role', related_name='role_2_user', on_delete=models.DO_NOTHING, db_constraint=False,
                             null=True)
    user_type = models.IntegerField(choices=UserBackendTypeEnum.choices(), default=UserBackendTypeEnum.default(),
                                    help_text='用户类型')
    company = models.ForeignKey('Company', related_name='company_user_backend', on_delete=models.DO_NOTHING,
                                db_constraint=False, null=True, help_text='公司')
    project = models.ForeignKey('Project', related_name='project_user_backend', on_delete=models.DO_NOTHING,
                                db_constraint=False, null=True)
    is_send_email = models.BooleanField(default=False, help_text='是否发送账号开通邮件')
    deleted = models.BooleanField('是否删除', default=False)

    class Meta:
        db_table = 'v2_user_backend'
        verbose_name = '后台用户表'
        verbose_name_plural = verbose_name


class StakeholderInterviewTmp(BaseModel):
    project_member_user = models.ForeignKey('User', related_name='stakeholder_interview_member',
                                            on_delete=models.DO_NOTHING, db_constraint=False, null=True,
                                            help_text='被教练者用户')
    interested_user = models.ForeignKey('User', related_name='stakeholder_interview_interested',
                                        on_delete=models.DO_NOTHING, db_constraint=False, null=True,
                                        help_text='利益相关者用户')
    coach_user = models.ForeignKey('User', related_name='stakeholder_interview_coach',
                                   on_delete=models.DO_NOTHING, db_constraint=False, null=True,
                                   help_text='利益相关者用户')
    project_interview = models.ForeignKey('ProjectInterview', related_name='stakeholder_interview_tmp',
                                          on_delete=models.DO_NOTHING, db_constraint=False, null=True)

    class Meta:
        db_table = 'v2_stakeholder_interview_tmp'
        verbose_name = '利益相关者访谈相关用户信息表'
        verbose_name_plural = verbose_name


class CoachAppraise(BaseModel):
    interview = models.ForeignKey('ProjectInterview', related_name='coach_appraise',
                                  on_delete=models.DO_NOTHING, db_constraint=False, null=True)
    score = models.IntegerField(null=True, default=0, help_text='被教练者给教练的评分')
    appraise = models.TextField(null=True, help_text='被教练者给教练的评价')
    deleted = models.BooleanField('是否删除', default=False)

    class Meta:
        db_table = 'v2_coach_appraise'
        verbose_name = '被教练者对教练评价表'
        verbose_name_plural = verbose_name


class UserTmp(BaseModel):
    data_id = models.IntegerField(null=True, help_text='数据对应id')
    # type=3 存储测评数据时extra_id为测评项目用户id user为当前填写用户
    extra_id = models.IntegerField(null=True, help_text='数据额外标识id')
    type = models.IntegerField(
        choices=UserTmpEnum.choices(), default=UserTmpEnum.default(),
        null=True, help_text='数据类型 | 1:辅导记录问卷 2:教练任务 3:测评信息 4:教练简历信息 5:教练入驻信息'
    )
    user = models.ForeignKey('User', related_name='interview_tmp_user',
                             on_delete=models.DO_NOTHING, db_constraint=False, null=True)
    content = models.JSONField(null=True, default=dict, help_text='临时数据')

    class Meta:
        db_table = 'v2_user_tmp'
        verbose_name = '用户临时数据记录'
        verbose_name_plural = verbose_name


class TotalTemplate(BaseModel):
    title = models.CharField(max_length=100, help_text='模版标题')
    write_role = models.IntegerField(
        choices=InterviewRecordTemplateRoleEnum.choices(),
        default=InterviewRecordTemplateRoleEnum.default(), null=False,
        help_text='填写记录的角色 | 1：教练；2：被教练者; 3: 教练及被教练者 4：利益相关者')
    status = models.SmallIntegerField(default=0, help_text='0: 未启用 1:已启用')
    coach_template = models.ForeignKey(
        'InterviewRecordTemplate',
        related_name='total_template_coach',
        on_delete=models.DO_NOTHING,
        db_constraint=False, null=True, default=None)
    coachee_template = models.ForeignKey(
        'InterviewRecordTemplate',
        related_name='total_template_coachee',
        on_delete=models.DO_NOTHING,
        db_constraint=False, null=True, default=None)
    # 利益相关者问卷
    stakeholder_template = models.ForeignKey(
        'InterviewRecordTemplate',
        related_name='total_template_stakeholder',
        on_delete=models.DO_NOTHING,
        db_constraint=False, default=None, null=True)
    image_url = models.TextField('展示图片链接', null=True, default=None)
    type = models.IntegerField(
        choices=TotalTemplateTypeEnum.choices(),
        default=TotalTemplateTypeEnum.default(), null=False,
        help_text='总辅导模版类型 | 1：一对一辅导；2：教练任务;')
    questions_order = models.TextField('问题排序列表', null=True, default='[]')
    deleted = models.BooleanField('是否删除', default=False)

    class Meta:
        db_table = 'v2_total_template'
        verbose_name = '总辅导模版表'
        verbose_name_plural = verbose_name


class RelatedQuestion(BaseModel):
    coach_question = models.ForeignKey('InterviewRecordTemplateQuestion',
                                       related_name='coach_related_question',
                                       on_delete=models.DO_NOTHING,
                                       null=True,
                                       db_constraint=False, help_text='教练问卷被关联问题')
    coachee_question = models.ForeignKey('InterviewRecordTemplateQuestion',
                                         related_name='coachee_related_question',
                                         on_delete=models.DO_NOTHING,
                                         null=True,
                                         db_constraint=False, help_text='被教练者问卷被关联问题')
    template_type = models.IntegerField(
        choices=TemplateTypeEnum.choices(),
        default=TemplateTypeEnum.default(), null=False,
        help_text='关联模版问卷类型 | 1：教练问卷；2：被教练者问卷；3：报告')
    description = models.TextField(null=True, help_text='说明')
    deleted = models.BooleanField('是否删除', default=False)

    class Meta:
        db_table = 'v2_related_question'
        verbose_name = '关联问题表'
        verbose_name_plural = verbose_name


class TotalTemplateReport(BaseModel):
    title = models.TextField(null=True, help_text='标题')
    title_type = models.IntegerField(
        choices=TitleEnum.choices(),
        default=TitleEnum.default(), null=False,
        help_text='标题类型 | 1：一级标题；2：二级标题')
    description = models.TextField(null=True, help_text='说明')
    related_question = models.ForeignKey('RelatedQuestion',
                                         related_name='total_template_report',
                                         on_delete=models.DO_NOTHING,
                                         db_constraint=False, help_text='关联问题', null=True)
    total_template = models.ForeignKey('TotalTemplate',
                                       related_name='total_template_report',
                                       on_delete=models.DO_NOTHING,
                                       db_constraint=False, help_text='总模版')

    class Meta:
        db_table = 'v2_total_template_report'
        verbose_name = '总模版报告表'
        verbose_name_plural = verbose_name


class WorkWechatUser(BaseModel):
    user = models.ForeignKey('User', related_name='work_wechat_user', on_delete=models.DO_NOTHING, db_constraint=False,
                             null=True, help_text='用户')
    wx_user_id = models.TextField(help_text='企业微信成员用户ID', null=True, default=None)
    external_user_id = models.TextField(help_text='外部联系人ID（客户）', null=True, default=None)
    qr_code = models.TextField(help_text='用户授权二维码链接', null=True, default=None)
    department_id = models.JSONField(
        null=True, help_text='部门id: 1:教练，2:项目顾问', default=None)
    deleted = models.BooleanField('是否删除', default=False)

    class Meta:
        db_table = 'v2_work_wechat_user'
        verbose_name = '企业微信用户信息'
        verbose_name_plural = verbose_name


class EvaluationReportScore2Growth(BaseModel):
    evaluation_report_score = models.ForeignKey('EvaluationReportScore',
                                                related_name='evaluation_report_score_growth_related',
                                                db_constraint=False, null=True, on_delete=models.DO_NOTHING,
                                                help_text='测评报告得分表')
    growth_goals = models.ForeignKey('GrowthGoals', related_name='growth_evaluation_report_score_related',
                                     db_constraint=False, null=True, on_delete=models.DO_NOTHING,
                                     help_text='成长目标表')

    class Meta:
        db_table = 'v2_evaluation_report_score_2_growth'
        verbose_name = '成长目标与测评报告评分关联表'
        verbose_name_plural = verbose_name


class UserAdditionalInfo(BaseModel):
    user = models.ForeignKey('User', related_name='user_additional_info', on_delete=models.DO_NOTHING, db_constraint=False,
                             null=True, help_text='用户')

    profession = models.TextField(help_text='职业', null=True, default=None)
    position = models.TextField(help_text='职位', null=True, default=None)
    often = models.TextField(help_text='管理经验时常', null=True, default=None)
    personnel = models.TextField(help_text='领导的团队规模', null=True, default=None)
    gender = models.TextField(help_text='性别', null=True, default=None)
    working_years = models.TextField(help_text='当前公司工作年限', null=True, default=None)
    education = models.TextField(help_text='学历', null=True, default=None)
    challenge = models.JSONField(help_text='想解决的挑战', null=True, default=None)
    all_info = models.JSONField(help_text='全部信息记录', null=True, default=None)

    class Meta:
        db_table = 'v2_user_additional_info'
        verbose_name = '客户附加信息表'
        verbose_name_plural = verbose_name


class ProjectEvaluationReport(BaseModel):
    project = models.ForeignKey('Project', related_name='project_evaluation_report', on_delete=models.DO_NOTHING,
                                db_constraint=False, null=True)
    lbi_analysis = models.JSONField(null=True, default=None, help_text='LBI测评分析结果')
    is_push_manager = models.BooleanField('是否推送企业管理员', default=False)
    pdf_url = models.TextField(null=True, default=None, help_text='报告转换的pdf文件链接')
    type = models.IntegerField(choices=ProjectEvaluationReportTypeEnum.choices(),
                               default=ProjectEvaluationReportTypeEnum.default(), null=False, help_text='项目报告类型')
    deleted = models.BooleanField('是否删除', default=False)

    class Meta:
        db_table = 'v2_project_evaluation_report'
        verbose_name = '项目测评报告表'
        verbose_name_plural = verbose_name


class ChangeObservation(BaseModel):
    remind_type = models.JSONField(null=True, default=None, help_text='通知类型 1-企业微信 2-短信 3-邮件')
    name = models.CharField(max_length=100, help_text='改变观察反馈名称')
    write_condition = models.FloatField(null=True, help_text='填写条件')
    project_member = models.ForeignKey('ProjectMember', related_name='change_observation', on_delete=models.DO_NOTHING,
                                       db_constraint=False, null=True)
    is_send = models.BooleanField('是否发送', default=False)
    is_complete = models.BooleanField('是否完成', default=False)
    deleted = models.BooleanField('是否删除', default=False)
    max_stakeholders_count = models.IntegerField(null=True, default=None, help_text='能配置的最大利益相关者人数')
    invite_type = models.IntegerField(
        choices=ChangeObservationInviteTypeEnum.choices(),
        default=ChangeObservationInviteTypeEnum.default(), null=True, help_text='邀请类型')
    stakeholders_write_end_date = models.DateField(null=True, default=None, help_text='利益相关者填写截止时间')
    invite_end_time = models.DateTimeField(null=True, default=None, help_text='客户邀请展示的截止时间(只用来展示，不作具体限制)')

    class Meta:
        db_table = 'v2_change_observation'
        verbose_name = '改变观察反馈表'
        verbose_name_plural = verbose_name


class ChangeObservationAnswer(BaseModel):
    change_observation = models.ForeignKey('ChangeObservation', related_name='change_observation_answers',
                                           on_delete=models.DO_NOTHING, db_constraint=False, null=True)
    public_attr = models.ForeignKey('PublicAttr', related_name='change_observation_answers_public_attr',
                                    on_delete=models.DO_NOTHING, db_constraint=False, null=True)
    content = models.JSONField(null=True, default=None, help_text='衡量指标信息')

    class Meta:
        db_table = 'v2_change_observation_answer'
        verbose_name = '改变观察反馈回答表'
        verbose_name_plural = verbose_name


class GrowthGoals2ChangeObservation(BaseModel):
    change_observation = models.ForeignKey('ChangeObservation',
                                           related_name='change_observation_growth_related',
                                           db_constraint=False, null=True, on_delete=models.DO_NOTHING,
                                           help_text='改变观察反馈表')
    growth_goals = models.ForeignKey('GrowthGoals', related_name='growth_change_observation_related',
                                     db_constraint=False, null=True, on_delete=models.DO_NOTHING,
                                     help_text='成长目标表')
    change = models.JSONField(null=True, default=None, help_text='衡量指标信息')

    deleted = models.BooleanField('是否删除', default=False)

    class Meta:
        db_table = 'v2_growth_goals2change_observation'
        verbose_name = '改变观察反馈成长目标关系表'
        verbose_name_plural = verbose_name


class MultipleAssociationRelation(BaseModel):
    main_id = models.IntegerField(null=True, help_text='主对象id')
    secondary_id = models.IntegerField(null=True, help_text='副对象id')
    type = models.IntegerField(choices=MultipleAssociationRelationTypeEnum.choices(),
                               default=MultipleAssociationRelationTypeEnum.default(), null=False, help_text='关系类型')
    public_attr = models.ForeignKey(
        'PublicAttr', related_name='multiple_association_public_attr', on_delete=models.DO_NOTHING,
        db_constraint=False, null=True,  default=None, help_text='利益相关者调研使用，利益相关者填写前当前字段为空，'
                                                                 '填写后保存利益相关者填写的answer的public_attr， '
                                                                 '用于查询利益相关者是否填写，填写内容，填写关系等')
    deleted = models.BooleanField('是否删除', default=False)
    is_send_sms_notice = models.BooleanField('是否发送短信通知', default=False)
    is_send_email_notice = models.BooleanField('是否发送邮件通知', default=False)
    send_wechat_invite_at = models.DateTimeField('发送微信邀请时间', default=None, null=True)

    @classmethod
    def get_object(cls):
        # todo zkg
        return 1

    class Meta:
        db_table = 'v2_multiple_association_relation'
        verbose_name = '多类型对象关系表'
        verbose_name_plural = verbose_name


class PersonalReport(BaseModel):
    name = models.CharField(max_length=50, help_text='报告名称')
    content = models.JSONField(null=True, default=None, help_text='报告内容')
    type = models.IntegerField(choices=PersonalReportTypeEnum.choices(),
                               default=PersonalReportTypeEnum.default(), null=False, help_text='报告类型')
    object_id = models.IntegerField(null=True, help_text='报告类型对象id')
    user = models.ForeignKey('User', related_name='user_personal_report', on_delete=models.DO_NOTHING,
                             db_constraint=False,
                             null=True, help_text='用户')
    pdf_url = models.TextField(null=True, default=None, help_text='报告转换的pdf文件链接')
    project = models.ForeignKey('Project', related_name='personal_report', on_delete=models.DO_NOTHING,
                                db_constraint=False, null=True)
    deleted = models.BooleanField('是否删除', default=False)

    class Meta:
        db_table = 'v2_personal_report'
        verbose_name = '个人报告表'
        verbose_name_plural = verbose_name


class CustomerPortrait(BaseModel):
    project = models.ForeignKey(
        'Project', related_name='project_customer_portrait',
        on_delete=models.DO_NOTHING,
        db_constraint=False, null=True,  help_text='对应项目id')
    coach = models.ForeignKey(
        'User', related_name='coach_user_customer_portrait', on_delete=models.DO_NOTHING,
        db_constraint=False, null=True, help_text='对应教练id')
    user = models.ForeignKey(
        'User', related_name='user_customer_portrait', on_delete=models.DO_NOTHING,
        db_constraint=False, null=True, help_text='对应用户id')
    customer_name = models.TextField(null=True, default=None, help_text='客户姓名', blank=True)
    customer_position = models.TextField(null=True, default=None, help_text='客户职位', blank=True)
    customer_age = models.TextField(null=True, default=None, help_text='客户年龄', blank=True)
    customer_style = models.TextField(null=True, default=None, help_text='客户风格及特点', blank=True)
    customer_experience = models.TextField(null=True, default=None, help_text='客户管理经验', blank=True)
    gender = models.IntegerField(null=True, default=None, help_text='性别 1: 男 2:女')
    type = models.IntegerField(
        choices=CustomerPortraitTypeEnum.choices(),
        default=CustomerPortraitTypeEnum.default(), null=False, help_text='客户类型 1-个人画像 2-群体画像')
    city = models.TextField(null=True, default=None, help_text='所在城市', blank=True)
    group_features = models.TextField(null=True, default=None, help_text='群体特点', blank=True)
    challenge = models.TextField(null=True, default=None, help_text='当前面临的挑战', blank=True)
    coach_expect = models.TextField(null=True, default=None, help_text='对教练认识和期待', blank=True)
    language = models.JSONField(null=True, default=None, help_text='语言')
    coach_experience = models.TextField(null=True, default=None, help_text='教练经验', blank=True)
    interview_type = models.IntegerField(
        choices=PortraitInterviewTypeEnum.choices(),
        default=PortraitInterviewTypeEnum.default(), null=True, help_text='教练类型 1-线上 2-线下 3-线上+线下')
    coach_ability = models.TextField(null=True, default=None, help_text='教练能力', blank=True)
    coach_style = models.TextField(null=True, default=None, help_text='教练风格及特点', blank=True)
    coach_extra = models.TextField(null=True, default=None, help_text='可供教练参考的其他信息', blank=True)
    group_target = models.TextField(null=True, default=None, help_text='组织目标', blank=True)
    personal_target = models.TextField(null=True, default=None, help_text='教练目的', blank=True)
    stakeholder_feedback_and_expectations = models.TextField(null=True, default=None, help_text='利益相关方的反馈和期待信息', blank=True)
    sensitive_info = models.TextField(null=True, default=None, help_text='敏感信息', blank=True)
    expected_interview_start_time = models.DateField(null=True, help_text='预约化学面谈开始时间')
    expected_interview_end_time = models.DateField(null=True, help_text='预约化学面谈结束时间')
    deleted = models.BooleanField('是否删除', default=False)

    class Meta:
        db_table = 'v2_customer_portrait'
        verbose_name = '客户画像'
        verbose_name_plural = verbose_name


class UserNoticeRecord(BaseModel):
    uuid = models.UUIDField(default=uuid.uuid4, primary_key=True, help_text="UUID")
    project = models.ForeignKey(
        'Project', related_name='project_user_notification_record',
        on_delete=models.DO_NOTHING,
        db_constraint=False, null=True,  help_text='通知对应的项目id')
    user = models.ForeignKey(
        'User', related_name='user_notification_record',
        on_delete=models.DO_NOTHING,
        db_constraint=False, null=True, help_text='创建发送通知的用户id')
    type = models.IntegerField(
        choices=NoticeTemplateTypeEnum.choices(), default=NoticeTemplateTypeEnum.default(),
        null=True, help_text='数据类型 |  1-账号开通通知 2-改变观察问卷 3-利益相关者调研问卷 4-教练简历匹配通知'
    )
    channel = models.IntegerField(
        choices=NoticeChannelTypeEnum.choices(), default=NoticeChannelTypeEnum.default(),
        null=True, help_text='发送渠道类型 | 1-企业微信 2-短信 3-邮件'
    )
    content = models.JSONField(null=True, default=dict, help_text='发送通知的数据')
    feedback = models.JSONField(null=True, default=None, help_text='反馈')
    extra = models.TextField(null=True, help_text='额外数据')
    is_now_modify = models.BooleanField(default=False, null=True, help_text='是否有新的修改')
    deleted = models.BooleanField('是否删除', default=False)

    class Meta:
        db_table = 'v2_user_notice_record'
        verbose_name = '用户通知记录'
        verbose_name_plural = verbose_name


class Order(BaseModel):
    order_no = models.UUIDField(default=uuid.uuid4, primary_key=True, help_text="UUID")
    payer_amount = models.PositiveIntegerField(help_text='实付金额，单位：分', default=0)
    total_amount = models.PositiveIntegerField(help_text='订单总金额，单位：分', default=0)
    discount_amount = models.PositiveIntegerField(help_text='优惠金额，单位：分', default=0)
    coupon_no = models.CharField(max_length=256, help_text='用户代金券发放凭据号', default=None, null=True)
    status = models.IntegerField(choices=OrderStatusEnum.choices(), default=OrderStatusEnum.default(),
                                 null=True, help_text='订单状态 | 1-待支付 2-已支付 3-待退款 4-已退款')
    transaction_id = models.TextField(null=True, default=None, help_text='第三方支付平台订单号')
    success_time = models.DateTimeField(null=True, default=None, help_text='支付完成时间')
    channel = models.IntegerField(choices=SettlementChannelEnum.choices(), default=SettlementChannelEnum.default(),
                                  null=True, help_text='渠道 | 1-微信')
    public_attr = models.ForeignKey('PublicAttr', related_name='orders_public_attr', on_delete=models.DO_NOTHING,
                                    db_constraint=False, null=True)
    count = models.IntegerField(default=1, help_text='购买数量')
    activity = models.ForeignKey(
        'Activity', related_name='activity_order',
        on_delete=models.DO_NOTHING, db_constraint=False, default=None, null=True)
    personal_activity = models.ForeignKey(
        'PersonalActivity', related_name='personal_activity_order',
        on_delete=models.DO_NOTHING, db_constraint=False, default=None, null=True)
    poster = models.ForeignKey('Poster', related_name='poster_order', on_delete=models.DO_NOTHING,
                               db_constraint=False, null=True, help_text='海报标识')
    payment_info = models.JSONField(null=True, default=None, help_text='支付信息')
    deleted = models.BooleanField('是否删除', default=False)

    class Meta:
        db_table = 'v2_order'
        verbose_name = '订单表'
        verbose_name_plural = verbose_name


class Commodity(BaseModel):
    type = models.IntegerField(choices=CommodityTypeEnum.choices(), default=CommodityTypeEnum.default(),
                               null=True, help_text='商品类型 | 1-教练辅导')
    price = models.PositiveIntegerField(help_text='价格，单位：分', default=0)
    commodity_no = models.UUIDField(default=uuid.uuid4, primary_key=True, help_text="UUID")
    total_count = models.IntegerField(null=True, help_text='总数量')
    available_count = models.IntegerField(null=True, help_text='可用数量')
    status = models.IntegerField(choices=CommodityStatusEnum.choices(), default=CommodityStatusEnum.default(),
                                 null=True, help_text='状态 | 1-上架 2-下架')
    desc = models.TextField(null=True, help_text='商品描述')
    name = models.CharField(max_length=256, default=None, null=True, help_text='商品名称')
    object_id = models.IntegerField(null=True, help_text='商品关联对象id')
    deleted = models.BooleanField('是否删除', default=False)

    class Meta:
        db_table = 'v2_commodity'
        verbose_name = '商品表'
        verbose_name_plural = verbose_name


class Order2Commodity(BaseModel):
    commodity = models.ForeignKey('Commodity', related_name='commodity_orders', on_delete=models.DO_NOTHING,
                                  db_constraint=False, null=True)
    order = models.ForeignKey('Order', related_name='order_commodities', on_delete=models.DO_NOTHING,
                              db_constraint=False, null=True)
    deleted = models.BooleanField('是否删除', default=False)

    class Meta:
        db_table = 'v2_order2commodity'
        verbose_name = '订单-商品关系表'
        verbose_name_plural = verbose_name


class RefundOrder(BaseModel):
    order = models.ForeignKey('Order', related_name='refunds', on_delete=models.DO_NOTHING,
                              db_constraint=False, null=True)
    refund_no = models.UUIDField(default=uuid.uuid4, primary_key=True, help_text="UUID")
    reason = models.TextField(null=True, help_text='退款原因')
    refund_amount = models.PositiveIntegerField(help_text='退款金额，单位：分', default=0)
    refund_id = models.TextField(null=True, default=None, help_text='第三方支付平台退款单号')
    status = models.IntegerField(choices=RefundStatusEnum.choices(), default=RefundStatusEnum.default(),
                                 null=True, help_text='退款状态 | 1-退款处理中 2-退款成功 3-退款关闭 4-退款异常')
    success_time = models.DateTimeField(null=True, default=None, help_text='退款成功时间')
    user_received_account = models.TextField(null=True, help_text='退款入账账户')
    channel = models.TextField(null=True, help_text='退款渠道 ORIGINAL：原路退款    BALANCE：退回到余额    '
                                                    'OTHER_BALANCE：原账户异常退到其他余额账户 '
                                                    'OTHER_BANKCARD：原银行卡异常退到其他银行卡')
    deleted = models.BooleanField('是否删除', default=False)

    class Meta:
        db_table = 'v2_refund_order'
        verbose_name = '退款单表'
        verbose_name_plural = verbose_name


class Stock(BaseModel):
    stock_id = models.CharField(max_length=256, default=None, null=True, help_text='批次代金券id')
    stock_name = models.TextField(null=True, help_text='批次代金券名称')
    create_time = models.DateTimeField(null=True, default=None, help_text='创建时间')
    status = models.IntegerField(choices=StockStatusEnum.choices(), default=StockStatusEnum.default(),
                                 null=True, help_text='批次状态')
    description = models.TextField(null=True, help_text='使用说明')
    max_coupons = models.IntegerField(null=True, help_text='最大发券数')
    distributed_coupons = models.IntegerField(null=True, help_text='已发券数量')
    max_amount = models.PositiveIntegerField(help_text='总预算', default=0)
    max_amount_by_day = models.PositiveIntegerField(help_text='单天发放上限金额', default=0)
    coupon_amount = models.PositiveIntegerField(help_text='优惠面额', default=0)
    transaction_minimum = models.PositiveIntegerField(help_text='优惠门槛金额', default=0)
    available_begin_time = models.DateTimeField(null=True, default=None, help_text='可用开始时间')
    available_end_time = models.DateTimeField(null=True, default=None, help_text='可用结束时间')
    stock_type = models.IntegerField(choices=StockTypesEnum.choices(), default=StockTypesEnum.default(),
                                     null=True, help_text='批次类型')
    type = models.IntegerField(choices=StockBusinessTypeEnum.choices(), default=StockBusinessTypeEnum.default(),
                               null=True, help_text='代金券业务类型 1-新人礼')
    discount = models.PositiveIntegerField(null=True, help_text='折扣 例:7折-70')
    goods_tag = models.CharField(max_length=256, default=None, null=True, help_text='订单优惠标记')
    deleted = models.BooleanField('是否删除', default=False)

    class Meta:
        db_table = 'v2_stock'
        verbose_name = '批次代金券表'
        verbose_name_plural = verbose_name


class Coupon(BaseModel):
    stock = models.ForeignKey('Stock', related_name='coupons', on_delete=models.DO_NOTHING,
                              db_constraint=False, null=True)
    coupon_no = models.CharField(max_length=256, default=None, null=True, help_text='用户代金券发放凭据号')
    coupon_id = models.CharField(max_length=256, default=None, null=True, help_text='第三方代金券id')
    user = models.ForeignKey('User', related_name='coupons', on_delete=models.DO_NOTHING,
                             db_constraint=False, null=True, help_text='代金券用户')
    coupon_name = models.TextField(null=True, help_text='代金券名称')
    status = models.IntegerField(choices=CouponStatusEnum.choices(), default=CouponStatusEnum.default(),
                                 null=True, help_text='代金券状态')
    description = models.TextField(null=True, help_text='使用说明')
    deleted = models.BooleanField('是否删除', default=False)

    class Meta:
        db_table = 'v2_coupon'
        verbose_name = '代金券表'
        verbose_name_plural = verbose_name


class QuickLink(BaseModel):
    name = models.CharField(max_length=256, default=None, null=True, help_text='名称')
    image_url = models.TextField(null=True, help_text='图片链接')
    page_url = models.CharField(max_length=256, default=None, null=True, help_text='指定跳转页面')
    display_position = models.IntegerField(null=True, help_text='排序')
    creator = models.ForeignKey('User', related_name='quick_link', on_delete=models.DO_NOTHING,
                                db_constraint=False, null=True)
    status = models.IntegerField(choices=QuickLinkStatusEnum.choices(), default=QuickLinkStatusEnum.default(),
                                 null=True, help_text='状态 1-上架 2-下架')
    article = models.ForeignKey('Article', related_name='quick_link_article', on_delete=models.DO_NOTHING,
                                null=True, db_constraint=False)
    page_type = models.SmallIntegerField(null=True, help_text='链接类型 | 1:视野拓展 2:关于教练 3:预约辅导')
    page_text = models.CharField(max_length=256, default=None, null=True, help_text='指定页面文案')
    suitable_object = models.IntegerField(help_text='适用对象',  choices=SuitableObjectEnum.choices(),
                                          default=SuitableObjectEnum.default())
    association_type = models.IntegerField(choices=AssociationTypeEnum.choices(), default=None, null=True,
                                           help_text='关联类型')
    theme = models.ForeignKey('Theme', related_name='quick_link_theme', on_delete=models.DO_NOTHING,
                              null=True, db_constraint=False)
    deleted = models.BooleanField('是否删除', default=False)

    class Meta:
        db_table = 'v2_quick_link'
        verbose_name = '金刚区导航表'
        verbose_name_plural = verbose_name


class UserContract(BaseModel):
    id = models.UUIDField(default=uuid.uuid4, primary_key=True, help_text="UUID")
    user = models.ForeignKey(
        'User', related_name='user_contract', on_delete=models.DO_NOTHING,
        db_constraint=False, null=True, help_text='对应用户id')
    sign_url = models.TextField(null=True, help_text='签约链接', default=None)
    contract_url = models.TextField(null=True, help_text='合同链接', default=None)
    internship_empower_at = models.DateTimeField(null=True, help_text='见习教练授权协议时间', default=None)
    personal_empower_at = models.DateTimeField(null=True, help_text='个人教练授权协议时间', default=None)
    deleted = models.BooleanField('是否删除', default=False)

    class Meta:
        db_table = 'v2_user_contract'
        verbose_name = '用户合同记录表'
        verbose_name_plural = verbose_name


class PersonalApply(BaseModel):
    true_name = models.CharField(null=True, max_length=50, help_text='用户真实姓名')
    phone = models.CharField(max_length=60, null=True, help_text='手机号码')
    email = models.CharField(null=True, max_length=50, help_text='邮箱')
    user_class = models.CharField(max_length=50, null=True, help_text='班次')
    coach_auth = models.IntegerField(
        choices=CoachAuthEnum.choices(), default=None,
        null=True, help_text='教练认证 ｜ 1：ICF-ACC, 2：ICF-PCC, 3：ICF-MCC, 4：通过PCC口试, 5：通过ACC口试')
    coach_type = models.IntegerField(choices=CoachUserTypeEnum.choices(),
                                     default=None, help_text='申请通过的教练类型', null=True)
    qualification = models.JSONField(null=True, default=None, help_text='教练证书')
    coach = models.ForeignKey('Coach', related_name='personal_apply', on_delete=models.DO_NOTHING,
                              db_constraint=False, null=True, help_text='教练')
    status = models.IntegerField(choices=PersonalApplyStatusEnum.choices(), default=PersonalApplyStatusEnum.default(),
                                 null=True, help_text='审核状态')
    type = models.IntegerField(choices=PersonalApplyTypeEnum.choices(), default=PersonalApplyTypeEnum.default(),
                               help_text='申请类型')
    auditor = models.ForeignKey('User', related_name='personal_apply_auditor', on_delete=models.DO_NOTHING,
                                db_constraint=False, null=True, help_text='处理人')
    rejection_reason = models.TextField(null=True, help_text='拒绝原因', default=None)
    deleted = models.BooleanField('是否删除', default=False)

    class Meta:
        db_table = 'v2_personal_apply'
        verbose_name = '学员申请表'
        verbose_name_plural = verbose_name


class ProjectOffer(BaseModel):
    id = models.UUIDField(default=uuid.uuid4, primary_key=True, help_text="UUID")
    offer_name = models.CharField(max_length=256, null=True, help_text='offer名称')
    project = models.ForeignKey('Project', related_name='offers', on_delete=models.DO_NOTHING,
                                db_constraint=False, null=True)
    confirm_time = models.DateTimeField(null=True, default=None, help_text='offer确认截止时间')
    send_time = models.DateTimeField(null=True, default=None, help_text='发送时间')
    price = models.TextField(help_text='教练费用', null=True, default=None)
    project_msg = models.TextField(null=True, help_text='项目描述', default=None)
    status = models.IntegerField(choices=ProjectOfferStatusEnum.choices(), default=ProjectOfferStatusEnum.default(),
                                 null=True, help_text='加入状态')
    customer_count = models.IntegerField(help_text='服务客户数量', null=True)
    deleted = models.BooleanField('是否删除', default=False)


    @property
    def get_settlement_price(self):
        offer_price = self.offer_price.filter(deleted=False).all()
        data = []
        for item in offer_price:
            data.append({
                'work_type': item.work_type,
                'time_type': item.time_type,
                'place_type': item.place_type,
                'price': Decimal(str(item.price)) / Decimal('100'),
            })
        return data

    @property
    def get_settlement_description(self):
        settlement_prices = self.get_settlement_price
        settlement_info = []
        for item in settlement_prices:
            work_type = ProjectOfferWorkTypeEnum(item['work_type']).describe()
            time_unit = ['', '元/小时', '元/天'][item['time_type']]
            place_type = '线上' if item['place_type'] == 1 else '线下'
            price = item['price']
            
            info = f"{work_type}（{place_type}）：{price}{time_unit} "
            settlement_info.append(info)
        
        return '\n'.join(settlement_info)


    class Meta:
        db_table = 'v2_project_offer'
        verbose_name = '项目offer表'
        verbose_name_plural = verbose_name


class ProjectOfferPrice(BaseModel):
    id = models.UUIDField(default=uuid.uuid4, primary_key=True, help_text="UUID")
    project_offer = models.ForeignKey(
        'ProjectOffer', related_name='offer_price', on_delete=models.DO_NOTHING, db_constraint=False, null=True)
    work_type = models.IntegerField(
        choices=WorkTypeEnum.choices(), default=WorkTypeEnum.default(), null=True, help_text='工作类型')
    time_type = models.IntegerField(
        choices=BusinessOrderDurationTypeEnum.choices(), default=BusinessOrderDurationTypeEnum.default(), null=True, help_text='时间间隔类型')
    place_type = models.IntegerField(choices=ProjectInterviewPlaceTypeEnum.choices(),
                                     default=None, null=True, help_text='1: 线上 2: 线下')
    price = models.IntegerField(null=True, default=0, help_text='辅导价格，单位：分')
    deleted = models.BooleanField('是否删除', default=False)

    class Meta:
        db_table = 'v2_project_offer_price'
        verbose_name = '项目offer表'
        verbose_name_plural = verbose_name


class CoachOffer(BaseModel):
    id = models.UUIDField(default=uuid.uuid4, primary_key=True, help_text="UUID")
    project_offer = models.ForeignKey('ProjectOffer', related_name='coach_offers', on_delete=models.DO_NOTHING,
                                      db_constraint=False, null=True)
    coach = models.ForeignKey('Coach', related_name='offers', on_delete=models.DO_NOTHING,
                              db_constraint=False, null=True)
    status = models.IntegerField(choices=CoachOfferStatusEnum.choices(), default=CoachOfferStatusEnum.default(),
                                 null=True, help_text='加入状态')
    max_customer_count = models.IntegerField(help_text='最多服务客户数量', null=True)
    refuse_reason = models.TextField(help_text='拒绝原因')
    send_time = models.DateTimeField(null=True, default=None, help_text='发送时间')
    confirm_time = models.DateTimeField(null=True, default=None, help_text='确认时间')
    deleted = models.BooleanField('是否删除', default=False)


    class Meta:
        db_table = 'v2_coach_offer'
        verbose_name = '教练offer表'
        verbose_name_plural = verbose_name


class Theme(BaseModel):
    id = models.UUIDField(default=uuid.uuid4, primary_key=True, help_text="UUID")
    name = models.CharField(max_length=255, help_text='主题名称')
    brief = models.TextField(null=True, help_text='主题简介')
    image_url = models.CharField(max_length=255, help_text='主题图片')
    deleted = models.BooleanField('是否删除', default=False)


    class Meta:
        db_table = 'v2_home_theme'
        verbose_name = '首页主题表'
        verbose_name_plural = verbose_name


class ArticleThemeRelation(BaseModel):
    id = models.UUIDField(default=uuid.uuid4, primary_key=True, help_text="UUID")
    article = models.ForeignKey('Article', related_name='article_theme_relation', on_delete=models.DO_NOTHING,
                                null=True, db_constraint=False)
    theme = models.ForeignKey('Theme', related_name='theme_article_relation', on_delete=models.DO_NOTHING,
                              null=True, db_constraint=False)
    weight = models.IntegerField(default=1, help_text='权重')
    coach = models.ForeignKey('Coach', related_name='coach_article_relation', on_delete=models.DO_NOTHING,
                              null=True, db_constraint=False)

    deleted = models.BooleanField('是否删除', default=False)

    class Meta:
        db_table = 'v2_article_theme_relation'
        verbose_name = '文章主题关联表'
        verbose_name_plural = verbose_name