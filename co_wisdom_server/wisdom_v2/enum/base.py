from collections import OrderedDict
from enum import IntEnum, Enum


class StrEnum(str, Enum):
    __describe__ = OrderedDict()

    @classmethod
    def choices(cls):
        return cls.__describe__.items()

    @classmethod
    def choice_dict(cls):
        return cls.__describe__


class EnumClass(Enum):
    @classmethod
    def choices(cls) -> list:
        members = cls.__members__
        return [
            (
                key,
                members[key].value,
            )
            for key in members
        ]

    @classmethod
    def displays(cls) -> list:
        members = cls.__members__
        return [members[key].value for key in members]


class _ChoiceClass(IntEnum):
    __describe__ = OrderedDict()

    @classmethod
    def choices(cls):
        return list(cls.__describe__.items())

    @classmethod
    def choice_dict(cls):
        return cls.__describe__

    @classmethod
    def describe_to_str(cls) -> str:
        return str(dict(cls.__describe__))

    @classmethod
    def get_display(cls, value) -> str:
        """ 获得对应的中文显示 """
        return cls.__describe__.get(int(value), '')

    @classmethod
    def get_describe_keys(cls):
        return list(cls.__describe__.keys())