from enum import unique
from wisdom_v2.enum.base import _ChoiceClass, OrderedDict


# 区号
@unique
class ManageRoleEnum(_ChoiceClass):
    # 1: 一线经理
    # 2: 部门经理
    # 3: 事业部经理
    # 4: 事业部总经理
    # 5: 集团高管
    # 6: 首席执行官
    # '
    line_manager = 1
    department_manager = 2
    business_manager = 3
    general_business_manager = 4
    executives = 5
    ceo = 6

    __describe__ = OrderedDict((
        (line_manager, '一线经理'),
        (department_manager, '部门经理'),
        (business_manager, '事业部经理'),
        (general_business_manager, '事业部总经理'),
        (executives, '集团高管'),
        (ceo, '首席执行官'),
    ))

    @classmethod
    def default(cls):
        return cls.line_manager

    def describe(self):
        return self.__describe__[self.value]