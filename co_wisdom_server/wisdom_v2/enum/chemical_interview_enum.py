from enum import unique
from wisdom_v2.enum.base import _ChoiceClass, OrderedDict

@unique
class ChemicalInterviewCoachSourceEnum(_ChoiceClass):
    system_random = 1
    auto_select = 2

    __describe__ = OrderedDict((
        (system_random, '系统随机'),
        (auto_select, '自主选择'),
    ))

    @classmethod
    def default(cls):
        return cls.system_random

    def describe(self):
        return self.__describe__[self.value]


@unique
class ChemicalInterviewStatusEnum(_ChoiceClass):
    unselected = 1
    selected = 2
    not_feedback = 3
    undetermined = 4

    __describe__ = OrderedDict((
        (unselected, '未选择'),
        (selected, '已选择'),
        (not_feedback, '未反馈'),
        (undetermined, '待定'),
    ))

    @classmethod
    def default(cls):
        return cls.not_feedback

    def describe(self):
        return self.__describe__[self.value]
