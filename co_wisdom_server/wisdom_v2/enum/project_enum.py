from enum import unique
from wisdom_v2.enum.base import _ChoiceClass, OrderedDict


# 项目类型
@unique
class ProjectTypeEnum(_ChoiceClass):
    five_alc = 1
    mop = 2
    no_one_to_one = 3

    __describe__ = OrderedDict((
        (five_alc, '5ALC'),
        (mop, 'MOP'),
        (no_one_to_one, '无1对1辅导'),
    ))

    @classmethod
    def default(cls):
        return cls.five_alc

    def describe(self):
        return self.__describe__[self.value]


class ProjectInitiatorEnum(_ChoiceClass):
    hr_head = 1
    business_hrbp = 2
    direct_superior = 3
    client = 4
    chairman_ceo = 5

    __describe__ = OrderedDict((
        (hr_head, 'HR一号位或企业大学负责人'),
        (business_hrbp, '业务的HRBP'),
        (direct_superior, '客户的直接上级'),
        (client, '客户本人'),
        (chairman_ceo, '董事长/CEO'),
    ))

    @classmethod
    def default(cls):
        return cls.client

    def describe(self):
        return self.__describe__[self.value]


# 项目文件类型
class ProjectDocsTypeEnum(_ChoiceClass):
    material = 1
    report = 2

    __describe__ = OrderedDict((
        (material, '项目资料'),
        (report, '项目报告'),
    ))

    @classmethod
    def default(cls):
        return cls.report

    def describe(self):
        return self.__describe__[self.value]


# 项目状态
class ProjectStatusEnum(_ChoiceClass):
    not_sign = 1
    progress = 2
    deactivate = 3
    completed = 4
    lost = 5
    __describe__ = OrderedDict((
        (not_sign, '打单中'),
        (progress, '进行中'),
        (deactivate, '已停用'),
        (completed, '已完成'),
        (lost, '已输单'),
    ))

    @classmethod
    def default(cls):
        return cls.not_sign

    def describe(self):
        return self.__describe__[self.value]

    @classmethod
    def admin_data_viewing(cls):
        return [cls.progress, cls.deactivate, cls.completed]


class SendDelayEmailEnum(_ChoiceClass):
    middle = 1
    high = 2

    __describe__ = OrderedDict((
        (middle, '中级'),
        (high, '高级'),
    ))

    def describe(self):
        return self.__describe__[self.value]


class ProjectEvaluationReportTypeEnum(_ChoiceClass):
    LBI_EVALUATION = 1
    MANAGE_EVALUATION = 2

    __describe__ = OrderedDict((
        (LBI_EVALUATION, 'LBI项目测评报告'),
        (MANAGE_EVALUATION, '教练型管理者测评报告'),
    ))

    @classmethod
    def default(cls):
        return cls.LBI_EVALUATION

    def describe(self):
        return self.__describe__[self.value]


class ProjectOfferWorkTypeEnum(_ChoiceClass):
    one_to_one = 1
    stakeholder_interview = 2
    workshop = 3
    group_coaching = 4
    multi_party_meeting = 9
    shadow_observation = 10

    __describe__ = OrderedDict((
        (one_to_one, '一对一辅导'),
        (stakeholder_interview, '利益相关者访谈'),
        (workshop, '工作坊'),
        (group_coaching, '小组辅导'),
        (multi_party_meeting, '多方会谈'),
        (shadow_observation, '影子观察'),
    ))

    @classmethod
    def default(cls):
        return cls.one_to_one

    def describe(self):
        return self.__describe__[self.value]

