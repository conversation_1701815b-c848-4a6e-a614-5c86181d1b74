from enum import unique
from wisdom_v2.enum.base import _ChoiceClass, OrderedDict


# 用户角色
@unique
class ProjectMemberRoleEnum(_ChoiceClass):
    coach = 4
    company_manager = 5
    coachee = 6
    stakeholder = 7

    __describe__ = OrderedDict((
        (coach, '教练'),
        (company_manager, '企业管理员'),
        (coachee, '被教练者'),
        (stakeholder, '利益相关者'),
    ))

    @classmethod
    def default(cls):
        return cls.coachee

    def describe(self):
        return self.__describe__[self.value]


# 利益相关者关系
class StakeHolderRelationEnum(_ChoiceClass):
    superior = 1
    peers = 2
    junior = 3

    __describe__ = OrderedDict((
        (superior, '上级'),
        (peers, '平级'),
        (junior, '下级'),
    ))

    @classmethod
    def default(cls):
        return cls.superior

    def describe(self):
        return self.__describe__[self.value]