from enum import unique
from wisdom_v2.enum.base import _ChoiceClass, OrderedDict

@unique
class OrderStatusEnum(_ChoiceClass):
    pending_pay = 1
    paid = 2
    under_refund = 3
    refunded = 4

    __describe__ = OrderedDict((
        (pending_pay, '待支付'),
        (paid, '已支付'),
        (under_refund, '待退款'),
        (refunded, '已退款'),
    ))

    @classmethod
    def default(cls):
        return cls.pending_pay

    def describe(self):
        return self.__describe__[self.value]


@unique
class CommodityTypeEnum(_ChoiceClass):
    coaching = 1

    __describe__ = OrderedDict((
        (coaching, '教练辅导'),
    ))

    @classmethod
    def default(cls):
        return cls.coaching

    def describe(self):
        return self.__describe__[self.value]

@unique
class SettlementChannelEnum(_ChoiceClass):
    wechat = 1

    __describe__ = OrderedDict((
        (wechat, '微信支付'),
    ))

    @classmethod
    def default(cls):
        return cls.wechat

    def describe(self):
        return self.__describe__[self.value]


@unique
class CommodityStatusEnum(_ChoiceClass):
    shelf = 1
    off_shelf = 2

    __describe__ = OrderedDict((
        (shelf, '上架'),
        (off_shelf, '下架'),
    ))

    @classmethod
    def default(cls):
        return cls.shelf

    def describe(self):
        return self.__describe__[self.value]


@unique
class RefundStatusEnum(_ChoiceClass):
    PROCESSING = 1
    SUCCESS = 2
    CLOSED = 3
    ABNORMAL = 4
    FAIL = 5

    __describe__ = OrderedDict((
        (PROCESSING, '退款处理中'),
        (SUCCESS, '退款成功'),
        (CLOSED, '退款关闭'),
        (ABNORMAL, '退款异常'),
        (FAIL, '退款失败'),
    ))

    @classmethod
    def default(cls):
        return cls.PROCESSING

    def describe(self):
        return self.__describe__[self.value]


@unique
class StockStatusEnum(_ChoiceClass):
    unactivated = 1
    audit = 2
    running = 3
    stoped = 4
    paused = 5

    __describe__ = OrderedDict((
        (unactivated, '未激活'),
        (audit, '审核中'),
        (running, '运行中'),
        (stoped, '已停止'),
        (paused, '暂停发放'),
    ))

    @classmethod
    def default(cls):
        return cls.unactivated

    def describe(self):
        return self.__describe__[self.value]


@unique
class StockTypesEnum(_ChoiceClass):
    NORMAL = 1
    DISCOUNT = 2
    OTHER = 3
    CUT = 4

    __describe__ = OrderedDict((
        (NORMAL, '代金券批次'),
        (DISCOUNT, '折扣'),
        (OTHER, '其他'),
        (CUT, '立减'),
    ))

    @classmethod
    def default(cls):
        return cls.NORMAL

    def describe(self):
        return self.__describe__[self.value]


@unique
class CouponStatusEnum(_ChoiceClass):
    SENDED = 1
    USED = 2
    EXPIRED = 3

    __describe__ = OrderedDict((
        (SENDED, '可用'),
        (USED, '已实扣'),
        (EXPIRED, '已过期'),
    ))

    @classmethod
    def default(cls):
        return cls.SENDED

    def describe(self):
        return self.__describe__[self.value]


@unique
class StockBusinessTypeEnum(_ChoiceClass):
    new_gift = 1

    __describe__ = OrderedDict((
        (new_gift, '新人礼'),
    ))

    @classmethod
    def default(cls):
        return cls.new_gift

    def describe(self):
        return self.__describe__[self.value]




