from enum import unique
from wisdom_v2.enum.base import _ChoiceClass, OrderedDict


# 企业属性
@unique
class CompanyAttrEnum(_ChoiceClass):
    state_center = 1
    joint_venture = 2
    foreign = 3
    private = 4
    entrepreneurship = 5
    other = 6

    __describe__ = OrderedDict((
        (state_center, '央企/国企'),
        (joint_venture, '中外合资'),
        (foreign, '外资企业'),
        (private, '民营企业'),
        (entrepreneurship, '创业团队'),
        (other, '其他'),
    ))

    @classmethod
    def default(cls):
        return cls.other

    def describe(self):
        return self.__describe__[self.value]

    @classmethod
    def is_create_tag(cls):
        return [cls.state_center, cls.joint_venture, cls.foreign, cls.private]


class CompanyDevelopmentStageEnum(_ChoiceClass):
    startup = 1
    high_growth = 2
    mature_stable = 3
    mature_sluggish = 4
    transformation = 5

    __describe__ = OrderedDict((
        (startup, '初创期'),
        (high_growth, '高速成长期'),
        (mature_stable, '成熟期-增长稳定'),
        (mature_sluggish, '成熟期-增长乏力'),
        (transformation, '转型期'),
    ))

    @classmethod
    def default(cls):
        return cls.transformation

    def describe(self):
        return self.__describe__[self.value]


class CompanyBusinessScaleEnum(_ChoiceClass):
    million = 1
    ten_million = 2
    hundred_million = 3
    billion = 4
    ten_billion = 5
    hundred_billion = 6
    trillion = 7

    __describe__ = OrderedDict((
        (million, '百万'),
        (ten_million, '千万'),
        (hundred_million, '亿'),
        (billion, '十亿'),
        (ten_billion, '百亿'),
        (hundred_billion, '千亿'),
        (trillion, '万亿'),
    ))

    @classmethod
    def default(cls):
        return cls.trillion

    def describe(self):
        return self.__describe__[self.value]


# 人员规模
@unique
class CompanyScaleEnum(_ChoiceClass):
    under_20_people = 1
    between_20_50_people = 2
    between_50_100_people = 3
    between_100_200_people = 4
    between_200_500_people = 5
    between_500_1000_people = 6
    between_1000_10000_people = 7
    other = 8
    above_10000_people = 9

    __describe__ = OrderedDict((
        (under_20_people, '20以下'),
        (between_20_50_people, '20-50人'),
        (between_50_100_people, '50-100人'),
        (between_100_200_people, '100-200人'),
        (between_200_500_people, '200-500人'),
        (between_500_1000_people, '500-1000人'),
        (between_1000_10000_people, '1000-10000人'),
        (above_10000_people, '10000人以上'),
        (other, '其他'),
    ))

    @classmethod
    def default(cls):
        return cls.other

    def describe(self):
        return self.__describe__[self.value]

    @classmethod
    def under_100(cls):
        return [cls.under_20_people, cls.between_20_50_people, cls.between_50_100_people]

    @classmethod
    def under_1000(cls):
        return [cls.under_20_people, cls.between_20_50_people, cls.between_50_100_people,
                cls.between_100_200_people, cls.between_200_500_people, cls.between_500_1000_people]

    @classmethod
    def under_10000(cls):
        return [cls.under_20_people, cls.between_20_50_people, cls.between_50_100_people,
                cls.between_100_200_people, cls.between_200_500_people, cls.between_500_1000_people,
                cls.between_1000_10000_people]


# 公司行业
@unique
class CompanyIndustryEnum(_ChoiceClass):
    internet_ai = 1
    electronics_communication_semiconductor = 2
    services = 3
    consumer_goods_retail_wholesale = 4
    real_estate_construction = 5
    education_training = 6
    advertising_media_culture_sports = 7
    manufacturing = 8
    professional_services = 9
    pharmaceutical_medical = 10
    automotive = 11
    transportation_logistics = 12
    energy_chemical_environmental = 13
    financial = 14
    agriculture_environment = 15
    government_non_profit_other = 16
    other = 17

    __describe__ = OrderedDict((
        (internet_ai, '互联网/AI'),
        (electronics_communication_semiconductor, '电子/通信/半导体'),
        (services, '服务业'),
        (consumer_goods_retail_wholesale, '消费品/零售/批发'),
        (real_estate_construction, '房地产/建筑'),
        (education_training, '教育培训'),
        (advertising_media_culture_sports, '广告/传媒/文化/体育'),
        (manufacturing, '制造业'),
        (professional_services, '专业服务'),
        (pharmaceutical_medical, '制药/医疗'),
        (automotive, '汽车'),
        (transportation_logistics, '交通运输/物流'),
        (energy_chemical_environmental, '能源/化工/环保'),
        (financial, '金融'),
        (agriculture_environment, '农业/环保'),
        (government_non_profit_other, '政府/非盈利机构/其它'),
        (other, '其他'),
    ))

    @classmethod
    def default(cls):
        return cls.government_non_profit_other

    def describe(self):
        return self.__describe__[self.value]