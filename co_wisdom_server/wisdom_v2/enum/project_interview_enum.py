from enum import unique
from wisdom_v2.enum.base import _ChoiceClass, OrderedDict


# 企业属性
@unique
class ProjectInterviewRecordCompleteEnum(_ChoiceClass):
    all_unfinished = 1
    coach_unfinished = 2
    member_unfinished = 3
    finished = 4

    __describe__ = OrderedDict((
        (all_unfinished, '双方未完成'),
        (coach_unfinished, '教练未完成'),
        (member_unfinished, '被教练者未完成'),
        (finished, '完成'),
    ))

    @classmethod
    def default(cls):
        return cls.all_unfinished

    def describe(self):
        return self.__describe__[self.value]


# 对象类型
@unique
class ObjectTypeEnum(_ChoiceClass):
    diary = 1
    habit = 2
    action_plan = 3
    project_note = 4

    __describe__ = OrderedDict((
        (diary, '成长笔记-思考反思'),
        (habit, '习惯养成-行为转变'),
        (action_plan, '行动计划-行动任务'),
        (project_note, '项目信息-项目笔记'),
    ))

    @classmethod
    def default(cls):
        return cls.diary

    def describe(self):
        return self.__describe__[self.value]

# 辅导类型
@unique
class ProjectInterviewPlaceCategoryEnum(_ChoiceClass):
    online_one_to_one = 1
    others_online = 2
    offline_group_coach = 3
    offline_one_to_one = 4

    __describe__ = OrderedDict((
        (online_one_to_one, '线上一对一'),
        (others_online, '其他线上平台'),
        (offline_group_coach, '线下集体辅导'),
        (offline_one_to_one, '线下一对一'),
    ))

    @classmethod
    def default(cls):
        return cls.online_one_to_one

    def describe(self):
        return self.__describe__[self.value]


class InterviewSubjectEnum(_ChoiceClass):
    regular = 0
    goal_oriented = 1
    multi_party = 2
    phase_review = 3
    summary = 4
    one_to_many = 5
    shadow_observation = 6

    __describe__ = OrderedDict((
        (regular, '一对一辅导'),
        (goal_oriented, '目标约谈'),
        (multi_party, '多方会谈'),
        (phase_review, '阶段回顾'),
        (summary, '总结约谈'),
        (one_to_many, '一对多约谈'),
        (shadow_observation, '影子观察'),
    ))

    @classmethod
    def default(cls):
        return cls.regular

    def describe(self):
        return self.__describe__[self]


@unique
class ProjectInterviewPlaceTypeEnum(_ChoiceClass):
    online = 1
    offline = 2

    __describe__ = OrderedDict((
        (online, '线上'),
        (offline, '线下'),
    ))

    @classmethod
    def default(cls):
        return cls.online

    def describe(self):
        return self.__describe__[self.value]

# 辅导类型
@unique
class ProjectInterviewTypeEnum(_ChoiceClass):
    formal_interview = 1
    enterprise_interview = 2
    chemical_interview = 3
    stakeholder_interview = 4

    __describe__ = OrderedDict((
        (formal_interview, '正式约谈'),
        (enterprise_interview, '企业面试'),
        (chemical_interview, '化学面谈'),
        (stakeholder_interview, '利益相关者访谈'),
    ))

    @classmethod
    def default(cls):
        return cls.formal_interview

    def describe(self):
        return self.__describe__[self.value]


# 小程序/app端详情状态
@unique
class AppInterviewDetailStatusEnum(_ChoiceClass):
    coach_group_coach_not_start_ongoing = 1
    coach_group_coach_finish_record_unfinish = 2
    coach_group_coach_finish_record_finfish = 3
    coachee_group_coach_not_start = 4
    coachee_group_coach_ongoing_or_finish_record_unfinish = 5
    coachee_group_coach_finish_record_finish = 6
    coach_one_to_one_not_start = 7
    coachee_one_to_one_not_start = 8
    coach_one_to_one_ongoing = 9
    coachee_one_to_one_ongoing = 10
    coach_one_to_one_finfsh_record_unfinish = 11
    coachee_one_to_one_finish_record_unfinish_or_coachee_record_unfinish = 12
    coach_one_to_one_finish_record_coach_unfinish = 13
    coachee_one_to_one_finish_record_coach_unfinish = 14
    coach_one_to_one_finish_record_coachee_unfinish = 15
    coach_one_to_one_finish_record_finish = 16
    coachee_one_to_one_finish_record_finish = 17

    __describe__ = OrderedDict((
        (coach_group_coach_not_start_ongoing, '教练-集体辅导未开始、进行中'),
        (coach_group_coach_finish_record_unfinish, '教练-集体辅导完成、辅导记录被教练者未完成'),
        (coach_group_coach_finish_record_finfish, '教练-集体辅导完成，辅导记录完成'),
        (coachee_group_coach_not_start, '被教练-集体辅导未开始'),
        (coachee_group_coach_ongoing_or_finish_record_unfinish, '被教练-集体辅导进行中；集体辅导完成、辅导记录被教练者未完成'),
        (coachee_group_coach_finish_record_finish, '被教练-集体辅导完成，辅导记录完成'),
        (coach_one_to_one_not_start, '教练-一对一辅导未开始'),
        (coachee_one_to_one_not_start, '被教练-一对一辅导未开始'),
        (coach_one_to_one_ongoing, '教练-一对一辅导进行中'),
        (coachee_one_to_one_not_start, '被教练-一对一辅导进行中'),
        (coach_one_to_one_finfsh_record_unfinish, '教练-一对一辅导完成，辅导记录双方未完成'),
        (coachee_one_to_one_finish_record_unfinish_or_coachee_record_unfinish,
         '被教练-一对一辅导完成，辅导记录双方未完成；一对一辅导完成，辅导记录被教练者未完成'),
        (coach_one_to_one_finish_record_coach_unfinish, '教练-一对一辅导完成，辅导记录教练未完成'),
        (coachee_one_to_one_finish_record_coach_unfinish, '被教练-一对一辅导完成，辅导记录教练未完成'),
        (coach_one_to_one_finish_record_coachee_unfinish, '教练-一对一辅导完成，辅导记录被教练者未完成'),
        (coach_one_to_one_finish_record_finish, '教练-一对一辅导完成，辅导记录完成'),
        (coachee_one_to_one_finish_record_finish, '被教练-一对一辅导完成，辅导记录完成'),

    ))

    @classmethod
    def default(cls):
        return cls.coach_group_coach_not_start_ongoing

    def describe(self):
        return self.__describe__[self.value]


# 辅导列表状态按钮跳转页面
@unique
class InterviewListStatusEnum(_ChoiceClass):
    video_link = 1
    write_answer_and_question_record = 2
    questionnaire_detail = 3
    answer_and_question_detail = 4
    write_questionnaire_record = 5
    wait_agree_questionnaire_detail = 6
    completed_questionnaire_detail = 7
    completed_answer_and_question_detail = 8
    chemical_interview_write_feedback = 9
    chemical_interview_feedback_detail = 10

    __describe__ = OrderedDict((
        (video_link, '跳转到视频通话页面'),
        (write_answer_and_question_record, '跳转到填写问答形式辅导记录页面'),
        (questionnaire_detail, '跳转到问卷形式详情页面'),
        (answer_and_question_detail, '跳转到问答形式详情页面'),
        (write_questionnaire_record, '跳转到填写问卷形式辅导记录页面'),
        (wait_agree_questionnaire_detail, '跳转到待同意问卷形式详情页面'),
        (completed_questionnaire_detail, '已完成辅导跳转到问卷形式详情页面'),
        (completed_answer_and_question_detail, '已完成辅导跳转到问答形式详情页面'),
        (chemical_interview_write_feedback, '跳转填写反馈页面'),
        (chemical_interview_feedback_detail, '跳转查看反馈页面'),
    ))

    @classmethod
    def default(cls):
        return cls.questionnaire_detail

    def describe(self):
        return self.__describe__[self.value]


# 待办事项列表状态按钮跳转页面
@unique
class TodoListStatusEnum(_ChoiceClass):
    video_link = 1
    questionnaire_detail = 2
    write_questionnaire_record = 3
    write_answer_and_question_detail = 4

    __describe__ = OrderedDict((
        (video_link, '跳转到视频通话页面'),
        (questionnaire_detail, '跳转到问卷形式辅导详情页面'),
        (write_questionnaire_record, '跳转到填写问卷形式辅导记录页面'),
        (write_answer_and_question_detail, '跳转到填写问答形式辅导记录页面'),
    ))

    @classmethod
    def default(cls):
        return cls.video_link

    def describe(self):
        return self.__describe__[self.value]


# 辅导记录类型枚举
@unique
class InterviewRecordTypeEnum(_ChoiceClass):
    questionnaire = 1
    question_and_answer = 2

    __describe__ = OrderedDict((
        (questionnaire, '问卷'),
        (question_and_answer, '问答'),
    ))

    @classmethod
    def default(cls):
        return cls.question_and_answer

    def describe(self):
        return self.__describe__[self.value]


# 辅导列表提示文案颜色枚举
@unique
class InterviewMessageColorEnum(_ChoiceClass):
    yellow = 1
    red = 2
    blue = 3
    grey = 4
    purple = 5

    __describe__ = OrderedDict((
        (yellow, '黄'),
        (red, '红'),
        (blue, '蓝'),
        (grey, '灰'),
        (purple, '紫'),
    ))

    @classmethod
    def default(cls):
        return cls.grey

    def describe(self):
        return self.__describe__[self.value]


# 辅导详情头部展示形式枚举
@unique
class InterviewDetailHeadEnum(_ChoiceClass):
    one_to_one_not_start_or_ongoing = 1
    one_to_one_finish = 2
    group_coach_not_start_or_ongoing = 3
    group_coach_finish = 4

    __describe__ = OrderedDict((
        (one_to_one_not_start_or_ongoing, '线上一对一未开始、进行中'),
        (one_to_one_finish, '线上一对一完成'),
        (group_coach_not_start_or_ongoing, '集体辅导未开始、进行中'),
        (group_coach_finish, '集体辅导完成'),
    ))

    @classmethod
    def default(cls):
        return cls.one_to_one_not_start_or_ongoing

    def describe(self):
        return self.__describe__[self.value]


# 辅导详情内容展示形式枚举
@unique
class InterviewDetailContentEnum(_ChoiceClass):
    not_show = 1
    not_show_detail = 2
    show_question_and_answer_detail = 3
    show_questionnaire_detail = 4
    contact_detail = 5

    __describe__ = OrderedDict((
        (not_show, '无需展示'),
        (not_show_detail, '不展示详情'),
        (show_question_and_answer_detail, '展示问答详情'),
        (show_questionnaire_detail, '展示问卷详情'),
        (contact_detail, '展示联络方式详情'),
    ))

    @classmethod
    def default(cls):
        return cls.not_show

    def describe(self):
        return self.__describe__[self.value]


# 辅导详情按钮展示枚举
class InterviewDetailButtonEnum(_ChoiceClass):
    invite_customer_write = 1
    write_record = 2
    edit_record = 3
    edit_topic = 4
    agree_interview = 5
    cancel_interview = 6
    access_interview = 7
    write_chemical_interview = 8
    update_interview_time = 9
    interview_record = 10

    __describe__ = OrderedDict((
        (invite_customer_write, '邀请客户填写按钮'),
        (write_record, '填写记录按钮'),
        (edit_record, '编辑记录按钮'),
        (edit_topic, '编辑议题按钮'),
        (agree_interview, '同意辅导邀请'),
        (cancel_interview, '取消辅导'),
        (access_interview, '进入辅导'),
        (write_chemical_interview, '填写化学面谈'),
        (update_interview_time, '修改辅导时间'),
        (interview_record, '辅导记录'),
    ))

    @classmethod
    def default(cls):
        return cls.invite_customer_write

    def describe(self):
        return self.__describe__[self.value]


# 总模版类型枚举
class TotalTemplateTypeEnum(_ChoiceClass):
    one_to_one_tutor = 1
    coach_task = 2
    project_one_to_one = 3

    __describe__ = OrderedDict((
        (one_to_one_tutor, '一对一辅导'),
        (coach_task, '教练任务'),
        (project_one_to_one, '项目一对一辅导'),
    ))

    @classmethod
    def default(cls):
        return cls.one_to_one_tutor

    def describe(self):
        return self.__describe__[self.value]


# 关联问题类型枚举
class RelatedQuestionEnum(_ChoiceClass):
    report = 1
    question = 2

    __describe__ = OrderedDict((
        (report, '报告'),
        (question, '普通问题'),
    ))

    @classmethod
    def default(cls):
        return cls.report

    def describe(self):
        return self.__describe__[self.value]

# 标题类型枚举
class TitleEnum(_ChoiceClass):
    level_one = 1
    level_two = 2

    __describe__ = OrderedDict((
        (level_one, '一级标题'),
        (level_two, '二级标题'),
    ))

    @classmethod
    def default(cls):
        return cls.level_one

    def describe(self):
        return self.__describe__[self.value]


# 关联模版问卷类型枚举
class TemplateTypeEnum(_ChoiceClass):
    coach = 1
    coachee = 2
    report = 3

    __describe__ = OrderedDict((
        (coach, '教练问卷'),
        (coachee, '被教练者问卷'),
        (report, '报告'),
    ))

    @classmethod
    def default(cls):
        return cls.report

    def describe(self):
        return self.__describe__[self.value]


# 教练首页待办及用户成长节点及利益相关者待反馈列表数据类型
class DataType(_ChoiceClass):
    interview = 1
    coach_tasks = 2
    evaluation = 3
    growth_goals = 4
    change_observation = 5
    chemical_interview = 6
    stakeholder_interview = 7
    article = 8
    group_coach = 9
    project_docs = 10
    group_tutoring = 11
    personal_report = 12

    __describe__ = OrderedDict((
        (interview, '辅导记录'),
        (coach_tasks, '教练任务'),
        (evaluation, '测评文件'),
        (growth_goals, '成长目标'),
        (change_observation, '改变观察反馈'),
        (chemical_interview, '化学面谈'),
        (stakeholder_interview, '利益相关者访谈'),
        (article, '文章'),
        (group_coach, '集体辅导'),
        (project_docs, '项目报告'),
        (group_tutoring, '小组辅导'),
        (personal_report, '个人报告'),
    ))

    @classmethod
    def default(cls):
        return cls.interview

    def describe(self):
        return self.__describe__[self.value]


class GroupCoachTypeEnum(_ChoiceClass):
    collective_tutoring = 1
    group_tutoring = 2

    __describe__ = OrderedDict((
        (collective_tutoring, '工作坊'),
        (group_tutoring, '小组辅导'),
    ))

    @classmethod
    def default(cls):
        return cls.collective_tutoring

    def describe(self):
        return self.__describe__[self.value]


class AppInterviewListTypeEnum(_ChoiceClass):
    """
    辅导列表返回的数据类型
    """
    interview = 1
    order = 2

    __describe__ = OrderedDict((
        (interview, '辅导'),
        (order, '订单'),
    ))

    @classmethod
    def default(cls):
        return cls.interview

    def describe(self):
        return self.__describe__[self.value]


class InterviewMeetingChannelTypeEnum(_ChoiceClass):
    """
    辅导列表返回的数据类型
    """
    tencent_meeting = 1

    __describe__ = OrderedDict((
        (tencent_meeting, '腾讯会议'),
    ))

    @classmethod
    def default(cls):
        return cls.tencent_meeting

    def describe(self):
        return self.__describe__[self.value]