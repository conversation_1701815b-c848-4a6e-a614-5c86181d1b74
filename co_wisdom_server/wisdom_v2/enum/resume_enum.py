from enum import unique
from wisdom_v2.enum.base import _ChoiceClass, OrderedDict


@unique
class ResumeCoachCourseTypeEnum(_ChoiceClass):
    cito = 1
    stc_team = 2
    psychodynamic = 3
    leadership = 4

    __describe__ = OrderedDict((
        (cito, 'CITO教练认证'),
        (stc_team, 'STC团队教练认证'),
        (psychodynamic, '心理动力学高管教练认证'),
        (leadership, '5A领导力教练认证'),
    ))

    @classmethod
    def default(cls):
        return cls.cito

    def describe(self):
        return self.__describe__[self.value]

    @classmethod
    def get_image(cls):
        return {
            cls.__describe__[cls.cito]: 'https://static.qzcoach.com/app/view_background/resumeMedal/coach_certification_cito_v2.png',
            cls.__describe__[cls.stc_team]: 'https://static.qzcoach.com/app/view_background/resumeMedal/coach_certification_stc_v2.png',
            cls.__describe__[cls.psychodynamic]: 'https://static.qzcoach.com/app/view_background/resumeMedal/coach_certification_psychology_v2.png',
            cls.__describe__[cls.leadership]: 'https://static.qzcoach.com/app/view_background/resumeMedal/coach_certification_5a_v2.png',
        }


    @classmethod
    def get_resume_tag_name(cls, value):
        data = {
            cls.__describe__[cls.cito]: 'CITO课程',
            cls.__describe__[cls.stc_team]: 'STC课程',
            cls.__describe__[cls.psychodynamic]: '心理动力学高管教练课程',
        }
        return data.get(value)



