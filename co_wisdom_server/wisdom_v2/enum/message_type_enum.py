from enum import unique
from wisdom_v2.enum.base import _ChoiceClass, OrderedDict


# 后台用户类型
@unique
class LarkMessageTypeEnum(_ChoiceClass):
    server_error = 1
    business_error = 2
    celery_health_check = 3
    others_backend_error = 4
    chemical_interview_start = 5
    chemical_interview_select_coach = 6
    chemical_interview_unselect_coach = 7
    chemical_interview_fail = 8
    chemical_interview_customer_appointment = 13
    chemical_interview_coach_schedule = 9
    stakeholder_interview_coach_schedule = 10
    stakeholder_interview_start = 11
    coach_task_report = 12
    chemical_interview_reservation_count = 14
    change_observation_personal_report = 15
    coach_update_resume = 16
    project_end_date_check = 17
    change_observation_personnel_selection = 18
    stakeholder_interview_personnel_selection = 19
    user_info_collect = 20
    coach_settlement_create = 21
    project_interview_add = 22
    app_feedback = 23

    __describe__ = OrderedDict((
        (server_error, '服务器500错误'),
        (business_error, '业务400错误'),
        (celery_health_check, 'celery健康监测'),
        (others_backend_error, '其它后端提醒（企微,邮件,报告....）'),
        (chemical_interview_start, '化学面谈开始'),
        (chemical_interview_select_coach, '化学面谈选定教练'),
        (chemical_interview_unselect_coach, '化学面谈未选定教练'),
        (chemical_interview_fail, '化学面谈教练匹配失败提醒'),
        (chemical_interview_customer_appointment, '化学面谈客户预约情况'),
        (chemical_interview_coach_schedule, '化学面谈教练可预约时间'),
        (stakeholder_interview_coach_schedule, '利益相关者教练可预约时间'),
        (stakeholder_interview_start, '利益相关者访谈预约提醒'),
        (coach_task_report, '教练任务报告生成'),
        (chemical_interview_reservation_count, '化学面谈教练被预约情况'),
        (change_observation_personal_report, '改变观察反馈报告生成情况通知'),
        (coach_update_resume, '教练更新简历通知'),
        (project_end_date_check, '项目结束时间临期通知'),
        (change_observation_personnel_selection, '改变观察选人员选定通知'),
        (stakeholder_interview_personnel_selection, '利益相关者访谈人员选定通知'),
        (user_info_collect, '项目用户提交信息后通知'),
        (coach_settlement_create, '教练提现生成通知'),
        (project_interview_add, '项目辅导预约通知'),
        (app_feedback, 'App反馈通知')
    ))

    @classmethod
    def default(cls):
        return cls.server_error

    def describe(self):
        return self.__describe__[self.value]

    @classmethod
    def chemical_interview_choice(cls):
        return [cls.chemical_interview_start, cls.chemical_interview_select_coach, cls.chemical_interview_reservation_count,
                cls.chemical_interview_customer_appointment, cls.chemical_interview_coach_schedule,
                cls.chemical_interview_unselect_coach, cls.chemical_interview_fail]

    @classmethod
    def stakeholder_interview_choice(cls):
        return [cls.stakeholder_interview_coach_schedule, cls.stakeholder_interview_start,
                cls.coach_task_report, cls.stakeholder_interview_personnel_selection]
    
    @classmethod
    def coach_choice(cls):
        return [cls.coach_update_resume, cls.coach_settlement_create]


