from enum import unique
from wisdom_v2.enum.base import _ChoiceClass, OrderedDict


# 区号
@unique
class AreaCodeEnum(_ChoiceClass):
    china = 1
    hongkong = 2
    macau = 3
    taiwan = 4
    korea = 5
    japan = 6
    america_canada = 7
    uk = 8
    singapore = 9

    __describe__ = OrderedDict((
        (china, '中国大陆 +86'),
        (hongkong, '中国香港 +852'),
        (macau, '中国澳门 +853'),
        (taiwan, '中国台湾 +886'),
        (korea, '韩国 +82'),
        (japan, '日本 +81'),
        (america_canada, '美国/加拿大 +1'),
        (uk, '英国 +44'),
        (singapore, '新加坡 +65'),
    ))

    @classmethod
    def default(cls):
        return cls.china

    def describe(self):
        return self.__describe__[self.value]


area_code_dic = {
    'code_scope': [1, 2, 3, 4, 5, 6, 7, 8, 9],
    '86': 1,
    '852': 2,
    '853': 3,
    '886': 4,
    '82': 5,
    '81': 6,
    '1': 7,
    '44': 8,
    '65': 9
}


# 用户角色
@unique
class UserRoleEnum(_ChoiceClass):
    coach = 1
    coachee = 2
    trainee_coach = 3
    trainee_coachee = 4

    __describe__ = OrderedDict((
        (coach, '签约教练'),
        (coachee, '普通被教练者'),
        (trainee_coach, '个人教练'),
        (trainee_coachee, '三无学员'),
    ))

    @classmethod
    def default(cls):
        return cls.coach

    def describe(self):
        return self.__describe__[self.value]


# 用户临时填写数据
@unique
class UserTmpEnum(_ChoiceClass):
    interview = 1
    coach_tasks = 2
    evaluation = 3
    resume = 4
    entrant = 5

    __describe__ = OrderedDict((
        (interview, '辅导记录问卷'),
        (coach_tasks, '教练任务'),
        (evaluation, '测评信息'),
        (resume, '小程序-简历信息'),
        (entrant, '小程序-教练入驻信息'),
    ))

    @classmethod
    def default(cls):
        return cls.interview

    def describe(self):
        return self.__describe__[self.value]


@unique
class EducationBackgroundEnum(_ChoiceClass):
    junior_college = 1
    undergraduate = 2
    master = 3
    doctor = 4

    __describe__ = OrderedDict((
        (junior_college, '专科'),
        (undergraduate, '本科'),
        (master, '硕士'),
        (doctor, '博士'),
    ))

    @classmethod
    def default(cls):
        return cls.undergraduate

    def describe(self):
        return self.__describe__[self.value]


@unique
class CoachUserTypeEnum(_ChoiceClass):
    student = 1
    trainee_coach = 2
    junior_coach = 3
    intermediate_coach = 4
    senior_coach = 5
    expert_coach = 6
    intermediate_trainee_coach = 7
    senior_trainee_coach = 8


    __describe__ = OrderedDict((
        (student, '见习教练'),
        (trainee_coach, '初级个人教练'),
        (junior_coach, '初级企业教练'),
        (intermediate_coach, '中级企业教练'),
        (senior_coach, '高级企业教练'),
        (expert_coach, '资深企业教练'),
        (intermediate_trainee_coach, '中级个人教练'),
        (senior_trainee_coach, '高级个人教练'),
    ))

    @classmethod
    def default(cls):
        return cls.junior_coach

    def describe(self):
        return self.__describe__[self.value]

    @classmethod
    def trainee_coach_value(cls):
        return [cls.student, cls.trainee_coach, cls.intermediate_trainee_coach, cls.senior_trainee_coach]

    @classmethod
    def personal_coach_value(cls):
        return [cls.trainee_coach, cls.intermediate_trainee_coach, cls.senior_trainee_coach]

    @classmethod
    def formal_coach(cls):
        return [cls.trainee_coach, cls.junior_coach, cls.intermediate_trainee_coach, cls.intermediate_coach,
                cls.senior_trainee_coach, cls.senior_coach, cls.expert_coach]

    @classmethod
    def enterprise_coach_value(cls):
        return [cls.junior_coach, cls.intermediate_coach, cls.senior_coach, cls.expert_coach]


@unique
class CoachInternToPersonalStatus(_ChoiceClass):
    contract = 1
    resume = 2
    set_price = 3
    apply = 4
    complete = 5

    __describe__ = OrderedDict((
        (contract, '协议签约'),
        (resume, '修改简历'),
        (set_price, '修改金额'),
        (apply, '申请中'),
        (complete, '申通过')
    ))

    @classmethod
    def default(cls):
        return cls.contract

    def describe(self):
        return self.__describe__[self.value]


@unique
class HourlySalaryEnum(_ChoiceClass):
    one_hundred = 1
    two_hundred = 2
    three_hundred = 3
    four_hundred = 4
    five_hundred = 5
    six_hundred = 6
    seven_hundred = 7
    eight_hundred = 8
    nine_hundred = 9
    thousand = 10


    __describe__ = OrderedDict((
        (one_hundred, '100元/小时'),
        (two_hundred, '200元/小时'),
        (three_hundred, '300元/小时'),
        (four_hundred, '400元/小时'),
        (five_hundred, '500元/小时'),
        (six_hundred, '600元/小时'),
        (seven_hundred, '700元/小时'),
        (eight_hundred, '800元/小时'),
        (nine_hundred, '900元/小时'),
        (thousand, '1000元/小时'),
    ))

    @classmethod
    def default(cls):
        return cls.seven_hundred

    def describe(self):
        return self.__describe__[self.value]


@unique
class PersonalApplyStatusEnum(_ChoiceClass):
    not_passed = 1
    passed = 2
    rejected = 3

    __describe__ = OrderedDict((
        (not_passed, '未通过'),
        (passed, '已通过'),
        (rejected, '已拒绝'),
    ))

    @classmethod
    def default(cls):
        return cls.not_passed

    def describe(self):
        return self.__describe__[self.value]


class PersonalApplyTypeEnum(_ChoiceClass):
    personal = 1
    internship = 2
    user_to_coach = 3

    __describe__ = OrderedDict((
        (personal, '个人教练'),
        (internship, '见习教练'),
        (user_to_coach, '用户申请转教练'),
    ))

    @classmethod
    def default(cls):
        return cls.personal

    def describe(self):
        return self.__describe__[self.value]


@unique
class PosterTypeEnum(_ChoiceClass):
    internship_project = 1

    __describe__ = OrderedDict((
        (internship_project, '实习项目'),
    ))

    @classmethod
    def default(cls):
        return cls.internship_project

    def describe(self):
        return self.__describe__[self.value]

@unique
class BrowseRecordObjectEnum(_ChoiceClass):
    resume = 1

    __describe__ = OrderedDict((
        (resume, '教练简历'),
    ))

    @classmethod
    def default(cls):
        return cls.resume

    def describe(self):
        return self.__describe__[self.value]


class GenderTypeEnum(_ChoiceClass):
    male = 1
    female = 2

    __describe__ = OrderedDict((
        (male, '男'),
        (female, '女'),
    ))

    @classmethod
    def default(cls):
        return cls.male

    def describe(self):
        return self.__describe__[self.value]

