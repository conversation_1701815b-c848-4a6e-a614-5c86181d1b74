from enum import unique
from wisdom_v2.enum.base import _ChoiceClass, OrderedDict

@unique
class BusinessOrderTypeEnum(_ChoiceClass):
    enterprise = 1
    personal = 2
    public_course = 3

    __describe__ = OrderedDict((
        (enterprise, '企业项目'),
        (personal, '个人'),
        (public_course, '公开课'),
    ))

    @classmethod
    def default(cls):
        return cls.enterprise

    def describe(self):
        return self.__describe__[self.value]


@unique
class BusinessOrderPayStatusEnum(_ChoiceClass):
    paid = 1
    non_payment = 2
    non = 3

    __describe__ = OrderedDict((
        (paid, '已支付'),
        (non_payment, '未支付无风险'),
        (non, '--'),
    ))

    @classmethod
    def default(cls):
        return cls.non

    def describe(self):
        return self.__describe__[self.value]

@unique
class WorkTypeEnum(_ChoiceClass):
    """工作类型"""
    one_to_one = 1
    stakeholder_interview = 2
    group_coach = 3
    group_counseling = 4
    teaching = 5
    evaluation = 6
    case_customization = 7
    paper_review = 8
    multi_party = 9
    shadow_observation = 10

    __describe__ = OrderedDict((
        (one_to_one, '一对一辅导'),
        (stakeholder_interview, '利益相关者访谈'),
        (group_coach, '工作坊'),
        (group_counseling, '小组辅导'),
        (teaching, '授课'),
        (evaluation, '口试'),
        (case_customization, '案例定制'),
        (paper_review, '论文评审'),
        (multi_party, '多方会谈'),
        (shadow_observation, '影子观察'),

    ))

    @classmethod
    def default(cls):
        return cls.one_to_one

    def describe(self):
        return self.__describe__[self.value]

    @classmethod
    def interview_data(cls):
        return [cls.one_to_one, cls.stakeholder_interview, cls.group_coach, cls.group_counseling,
                cls.multi_party, cls.shadow_observation]


@unique
class BusinessOrderPayStatusEnum(_ChoiceClass):
    paid = 1
    non_payment = 2
    non = 3

    __describe__ = OrderedDict((
        (paid, '已支付'),
        (non_payment, '未支付无风险'),
        (non, '--'),
    ))

    @classmethod
    def default(cls):
        return cls.non

    def describe(self):
        return self.__describe__[self.value]


@unique
class BusinessOrderSettlementStatusEnum(_ChoiceClass):
    settled = 1
    unsettled = 2

    __describe__ = OrderedDict((
        (settled, '已结算'),
        (unsettled, '未结算'),
    ))

    @classmethod
    def default(cls):
        return cls.unsettled

    def describe(self):
        return self.__describe__[self.value]


@unique
class BusinessOrderWithdrawalStatusEnum(_ChoiceClass):
    non_withdrawable = 1
    can_withdraw = 2
    withdrawn = 3

    __describe__ = OrderedDict((
        (non_withdrawable, '不可提现'),
        (can_withdraw, '可提现'),
        (withdrawn, '已提现'),
    ))

    @classmethod
    def default(cls):
        return cls.non_withdrawable

    def describe(self):
        return self.__describe__[self.value]


@unique
class BusinessOrderDurationTypeEnum(_ChoiceClass):
    hour = 1
    day = 2

    __describe__ = OrderedDict((
        (hour, '小时'),
        (day, '天'),
    ))

    @classmethod
    def default(cls):
        return cls.hour

    def describe(self):
        return self.__describe__[self.value]


@unique
class SettlementUserTypeEnum(_ChoiceClass):
    enterprise = 1
    personal = 2
    others = 3

    __describe__ = OrderedDict((
        (enterprise, '企业用户'),
        (personal, '个人用户'),
        (others, '--'),
    ))

    @classmethod
    def default(cls):
        return cls.enterprise

    def describe(self):
        return self.__describe__[self.value]


class BusinessOrderDataTypeEnum(_ChoiceClass):
    """结算订单数据来源"""
    interview = 1
    group_coach = 2
    public_course = 3
    project_settlement = 4

    __describe__ = OrderedDict((
        (interview, '辅导'),
        (group_coach, '集体辅导'),
        (public_course, '公开课'),
        (project_settlement, '项目结算'),
    ))

    @classmethod
    def default(cls):
        return cls.interview

    def describe(self):
        return self.__describe__[self.value]
