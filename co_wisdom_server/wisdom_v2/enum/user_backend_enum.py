from enum import unique
from wisdom_v2.enum.base import _ChoiceClass, OrderedDict


# 后台用户类型
@unique
class UserBackendTypeEnum(_ChoiceClass):
    admin = 1
    company_manager = 2

    __describe__ = OrderedDict((
        (admin, '后台用户'),
        (company_manager, '企业管理员'),
    ))

    @classmethod
    def default(cls):
        return cls.admin

    def describe(self):
        return self.__describe__[self.value]