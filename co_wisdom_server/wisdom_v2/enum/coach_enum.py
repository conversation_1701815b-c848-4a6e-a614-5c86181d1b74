from enum import unique
from wisdom_v2.enum.base import _ChoiceClass, OrderedDict


# 企业属性
@unique
class NonPlatformInterviewTypeEnum(_ChoiceClass):
    personal = 1
    group = 2

    __describe__ = OrderedDict((
        (personal, "一对一辅导"),
        (group, '团队辅导'),
    ))

    @classmethod
    def default(cls):
        return cls.personal

    def describe(self):
        return self.__describe__[self.value]


class NonPlatformInterviewPayTypeEnum(_ChoiceClass):
    pay = 1
    free = 2

    __describe__ = OrderedDict((
        (pay, '付费辅导'),
        (free, '免费辅导'),
    ))

    @classmethod
    def default(cls):
        return cls.pay

    def describe(self):
        return self.__describe__[self.value]


class NonPlatformInterviewSourceEnum(_ChoiceClass):
    interview = 1
    coach_interview = 2
    public_courses_coach = 3

    __describe__ = OrderedDict((
        (interview, '平台内辅导'),
        (coach_interview, '教练创建的辅导'),
        (public_courses_coach, '公开课'),
    ))

    @classmethod
    def default(cls):
        return cls.interview

    def describe(self):
        return self.__describe__[self.value]


@unique
class NonPlatformArticlesTypeEnum(_ChoiceClass):
    wechat_official_account = 1

    __describe__ = OrderedDict((
        (wechat_official_account, "微信公众号"),
    ))

    @classmethod
    def default(cls):
        return cls.wechat_official_account

    def describe(self):
        return self.__describe__[self.value]

@unique
class NonPlatformArticlesCategoryEnum(_ChoiceClass):
    executive_coach = 1
    team_coach = 2
    personal_coach = 3
    coaching_leadership = 4

    __describe__ = OrderedDict((
        (executive_coach, "高管教练"),
        (team_coach, "团队教练"),
        (personal_coach, "个人教练"),
        (coaching_leadership, "教练型领导力"),
    ))

    @classmethod
    def default(cls):
        return cls.executive_coach

    def describe(self):
        return self.__describe__[self.value]
