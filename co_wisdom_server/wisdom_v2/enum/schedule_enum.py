from enum import unique
from wisdom_v2.enum.base import _ChoiceClass, OrderedDict

@unique
class RecurringScheduleRepeatTypeEnum(_ChoiceClass):
    daily = 1
    weekly = 2
    monthly = 3

    __describe__ = OrderedDict((
        (daily, '每天重复'),
        (weekly, '每周重复'),
        (monthly, '每月重复')
    ))

    @classmethod
    def default(cls):
        return cls.daily

    def describe(self):
        return self.__describe__[self.value]
