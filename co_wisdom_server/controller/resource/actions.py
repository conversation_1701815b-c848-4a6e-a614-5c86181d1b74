from controller.dictionary.actions import get_dictionary, get_dictionary_list_value
from data import models
from utils import null_or_blank


# 获取默认的学习任务（帮助指南）
def get_default_help(num=5):
    res_list = models.AppResources.objects.filter(Enable=1, IsTop=1, IsShow__isnull=False)
    if num > 0:
        res_list = res_list[:num]
    return res_list


def get_resources_info(resid):
    resource = models.AppResources.objects.filter(ResId=resid).first()
    if resource:
        return resource
    return models.AppResources()


def get_category_name(categoryid, cate1, cate2, cate3, isparent=False):
    if categoryid > 0:
        cinfo = models.AppRescategories.objects.filter(CategoryId=categoryid).first()
        if cinfo:
            if isparent and cinfo.ParentId > 0:
                cinfo = models.AppRescategories.objects.filter(CategoryId=cinfo.ParentId).first()
                return cinfo.Name
            else:
                return cinfo.Name
    if not null_or_blank(cate1):
        if isparent:
            dic = get_dictionary('ability')
            if dic:
                return dic.first().dic_name
        val = get_dictionary_list_value('ability', cate1)
        return val
    if not null_or_blank(cate2):
        if isparent:
            dic = get_dictionary('behaviour')
            if dic:
                return dic.first().dic_name
        val = get_dictionary_list_value('behaviour', cate2)
        return val
    if not null_or_blank(cate3):
        if isparent:
            dic = get_dictionary('personality')
            if dic:
                return dic.first().dic_name
        val = get_dictionary_list_value('personality', cate3)
        return val
    return ''
