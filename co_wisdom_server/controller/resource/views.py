from django.db.models import Q
from rest_framework.decorators import action
from rest_framework.response import Response

from controller.resource.actions import get_category_name
from data import models, serializers, extension
from utils import response, int_arg, null_or_blank
from utils.queryset import randomSet


class ResourceViewSet(extension.ResponseViewSet):
    queryset = models.AppResources.objects.all()
    serializer_class = serializers.AppResourcesSerializer

    @action(methods=['post'], detail=False)
    def add(self, request):
        main_data = request.data.get('mainData')
        CategoryId = main_data.get('CategoryId', '').split('_')
        if len(CategoryId) == 2:
            # 领导力/领导行为/领导品格 ability behaviour personality
            sub = CategoryId[0]
            main_data['CategoryId'] = sub
        res = self.add_row(main_data)
        return Response(
            res.raw()
        )

    @action(methods=['post'], detail=False, url_path='update')
    def update_new(self, request):
        main_data = request.data.get('mainData')
        CategoryId = main_data.get('CategoryId', '').split('_')
        if len(CategoryId) == 2:
            # 领导力/领导行为/领导品格 ability behaviour personality
            sub = CategoryId[0]
            main_data['CategoryId'] = sub
        res = self.update_row(main_data)
        return Response(
            res.raw()
        )

    @action(methods=['get'], detail=False)
    def GetRandList(self, request):
        category = request.query_params.get('category')
        cateid = request.query_params.get('cateid')
        probjectid = int_arg(request.query_params.get('probjectid'))
        interviewid = int_arg(request.query_params.get('interviewid'))
        num = int_arg(request.query_params.get('num'), 3)
        query = None
        if probjectid > 0 and interviewid > 0:
            minfo = models.AppProbjectsatisfaction.objects.filter(ProbjectId=probjectid, interviewId=interviewid,
                                                                  User_Id=request.user.pk).first()
            if minfo:
                query = models.AppResources.objects.filter(Enable=1, IsShow=True)
                if not null_or_blank(minfo.A2) and not null_or_blank(minfo.A3):
                    a2 = minfo.A2.split(',')
                    a3 = minfo.A3.split(',')
                    cateids = []
                    if a3 is list:
                        for item in a3:
                            if not item.isnumeric():
                                cateids.append(0)
                            else:
                                cateids.append(item)
                    cateids = set(cateids)
                    query = models.AppResources.objects.filter(Q(Cate2__in=a2) | Q(CategoryId__in=cateids), Enable=1,
                                                               IsShow=True)
                elif not null_or_blank(minfo.A2):
                    a2 = minfo.A2.split(',')
                    query = models.AppResources.objects.filter(Q(Cate2__in=a2), Enable=1, IsShow=True)
                elif not null_or_blank(minfo.A3):
                    a3 = minfo.A3.split(',')
                    cateids = []
                    if a3 is list:
                        for item in a3:
                            if not item.isnumeric():
                                cateids.append(0)
                            else:
                                cateids.append(item)
                    cateids = set(cateids)
                    query = models.AppResources.objects.filter(Q(CategoryId__in=cateids), Enable=1, IsShow=True)
        elif category == 'behaviour':
            query = models.AppResources.objects.filter(Enable=1, IsShow=True).exclude(Cate2='')
            if not null_or_blank(cateid):
                query = models.AppResources.objects.filter(Cate2=cateid, Enable=1,
                                                           IsShow=True)
        elif category == 'personality':
            query = models.AppResources.objects.filter(Enable=1, IsShow=True).exclude(Cate3='')
            if not null_or_blank(cateid):
                query = models.AppResources.objects.filter(Cate3=cateid, Enable=1,
                                                           IsShow=True)
        else:
            query = models.AppResources.objects.filter(Enable=1, IsShow=True)
            if not null_or_blank(cateid):
                query = models.AppResources.objects.filter(CategoryId=cateid, Enable=1,
                                                           IsShow=True)
        data = ''
        if query:
            query = randomSet(query)
            data = query.values('CategoryId', 'Cate1', 'Cate2', 'Cate3', 'ResId', 'Tags', 'Title', 'Description',
                                'CreateDate', 'ViewCount', 'ImgPath', 'Video')
        for item in data:
            item['CateParentName'] = get_category_name(item.get('CategoryId'), item.get('Cate1'), item.get('Cate2'),
                                                       item.get('Cate3'), True)
            item['CateName'] = get_category_name(item.get('CategoryId'), item.get('Cate1'), item.get('Cate2'),
                                                 item.get('Cate3'))
        return Response(
            response.Content().ok(None, data).raw()
        )
