import datetime
import json

from rest_framework.response import Response
from rest_framework.views import APIView

from controller.modeltemplate.actions import get_model_template_info
from controller.project.actions import get_project_info
from data import models
from utils import int_arg, response, value_or_default


class getpagedata(APIView):
    def post(self, request):
        from django.db import connection
        from collections import namedtuple

        def namedtuplefetchall(cursor):
            "Return all rows from a cursor as a namedtuple"
            desc = cursor.description
            nt_result = namedtuple('Result', [col[0] for col in desc])
            return [nt_result(*row) for row in cursor.fetchall()]

        where_desc = " WHERE 1=1 "
        for where in json.loads(request.data["wheres"]):
            if where['name'] == 'ProbjectId':
                value = where['value']
                where_desc += ' AND vmodel.ProbjectId = ' + str(value)
            if where['name'] == 'UserId':
                value = where['value']
                where_desc += ' AND vmodel.UserId = ' + str(value)
            if where['name'] == 'status':
                value = where['value']
                where_desc += ' AND vmodel.status > ' + str(value)

        # role
        if request.user.Role_Id != 1:
            role = models.SysUser.objects.filter(User_Id=request.user.pk).first().Role_Id
            where_desc += " AND rolesid LIKE '%" + str(role) + "%'"

        sql = "SELECT * FROM (SELECT Id AS ExId, ProbjectId, UserId, CreateDate, reportjson, status, pici, title, ModelCode, ReportCode, rolesid, 2 AS typeid, 0 AS ReportId, OtherPdf FROM app_modelreportreport UNION ALL SELECT id AS ExId, ProbjectId, Userid AS UserId, CreateTime AS CreateDate, ( SELECT reportjson FROM app_evalutionreport WHERE EvaluationId = app_evaluationresult.EvaluationId AND ProbjectId = app_evaluationresult.ProbjectId AND Userid = app_evaluationresult.Userid LIMIT 1) AS reportjson, '1' AS status, pici, ( SELECT EvalName FROM app_evaluation WHERE EvaluationId = app_evaluationresult.EvaluationId LIMIT 1) AS title, 'r210' AS ModelCode, 'r210' AS ReportCode, '3,4,6' AS rolesid, 1 AS typeid, ( SELECT Id FROM app_evalutionreport WHERE EvaluationId = app_evaluationresult.EvaluationId AND ProbjectId = app_evaluationresult.ProbjectId AND Userid = app_evaluationresult.Userid LIMIT 1) AS ReportId, ( SELECT OtherPdf FROM app_evalutionreport WHERE EvaluationId = app_evaluationresult.EvaluationId AND ProbjectId = app_evaluationresult.ProbjectId AND Userid = app_evaluationresult.Userid LIMIT 1) AS OtherPdf FROM app_evaluationresult WHERE tindex = 1 AND Interested_Id = 0) vmodel" + where_desc
        res_list = []
        with connection.cursor() as cursor:
            cursor.execute(sql)
            res_list = namedtuplefetchall(cursor)
        extra_list = []
        rows = []
        if len(res_list) > 0:
            for idx, item in enumerate(res_list):
                create_date = item.CreateDate.strftime('%Y-%m-%d %H:%M')
                if item.typeid == 1:
                    report = models.AppEvalutionreport.objects.filter(ProbjectId=item.ProbjectId,
                                                                      Userid=item.UserId).order_by('-CreateDate')
                    if report.first():
                        create_date = report.first().CreateDate.strftime('%Y-%m-%d %H:%M')
                row = {
                    'Id': str(idx + 1),
                    'ExId': item.ExId,
                    'ProbjectId': item.ProbjectId,
                    'ReportId': item.ReportId,
                    'UserId': item.UserId,
                    'CreateDate': create_date,
                    'reportjson': item.reportjson,
                    'status': item.status,
                    'pici': item.pici,
                    'title': item.title,
                    'ModelCode': item.ModelCode,
                    'ReportCode': item.ReportCode,
                    'rolesid': item.rolesid,
                    'typeid': item.typeid,
                    'OtherPdf': item.OtherPdf,
                }
                dic = {}
                dic["eId"] = str(idx+1)
                dic["ProName"] = get_project_info(item.ProbjectId).Name
                user = models.SysUser.objects.filter(User_Id=item.UserId).first()
                if user:
                    dic["UserName"] = user.UserName
                    dic["UserTrueName"] = user.UserTrueName
                    dic['UserRole'] = user.RoleName
                extra_list.append(dic)
                rows.append(row)

        total = len(rows)
        return Response({
            "status": 0,
            'msg': '',
            "rows": rows,
            "extra": extra_list,
            "total": total,
            'summary': None
        })
