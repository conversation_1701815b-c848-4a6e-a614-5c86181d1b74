import datetime

from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.viewsets import ModelViewSet, ViewSet, GenericViewSet

from controller.dictionary.actions import get_dictionary
from data import models, serializers, extension
from data.user.models import User
from utils import response, blank_to_none, int_arg, value_or_default, aesencrypt, randomPassword, float_arg, \
    null_or_blank
import json
from django.db.models import Avg, Count, Min, Sum, F


def get_dic_cate(dic_no):
    dic = get_dictionary(dic_no)
    if dic:
        sub_dic_list = models.SysDictionaryList.objects.filter(dictionary__dic_no=dic_no, enable=1).order_by('order_no').annotate(CategoryId=F('dic_value'), Name=F('dic_name'))
        return {
            'CategoryId': dic_no,
            'Name': dic.first().dic_name,
            'children': sub_dic_list.values('CategoryId', 'Name')
        }
    return None
