import json

from rest_framework.decorators import action
from rest_framework.response import Response

from controller.resourcecategory.actions import get_dic_cate
from data import models, serializers, extension
from utils import response, int_arg
from utils.model import add_option


class ResourceCategoryViewSet(extension.ResponseViewSet):
    queryset = models.AppRescategories.objects.all()
    serializer_class = serializers.AppRescategoriesSerializer

    @action(methods=['post'], detail=False)
    def add(self, request):
        main_data = request.data.get('mainData')
        ParentId = int_arg(main_data.get('ParentId'))
        main_data['ParentId'] = ParentId
        res = self.add_row(main_data)
        return Response(
            res.raw()
        )

    @action(methods=['post'], detail=False, url_path='update')
    def update_new(self, request):
        main_data = request.data.get('mainData')
        ParentId = int_arg(main_data.get('ParentId'))
        main_data['ParentId'] = ParentId
        res = self.update_row(main_data)
        return Response(
            res.raw()
        )

    @action(methods=['post'], detail=False)
    def getpagedata(self, request):
        for where in json.loads(request.data["wheres"]):
            if where['name'] == 'ParentId':
                parentid = int_arg(where['value'])
                ids = models.AppRescategories.objects.filter(ParentId=parentid).values_list('CategoryId', flat=True)
                add_option(request.data, 'CategoryId', ids, 'checkbox')
            else:
                add_option(request.data, where['name'], where['value'], where['displayType'])
        t = self.get_page_rows(request.data)
        if t.count() > 0:
            extra = []
            for item in t.all():
                dic = {}
                dic["eCategoryId"] = item.CategoryId
                # 二级类
                child = models.AppRescategories.objects.filter(ParentId=item.CategoryId, IsShow=True)
                if child.count() > 0:
                    dic['children'] = self.serializer_class(child.all(), many=True).data
                extra.append(dic)
            t.extra_list = extra
        return Response(
            self.page_data(t)
        )

    @action(methods=['get'], detail=False)
    def GetCategory(self, request):
        typeid = int_arg(request.query_params.get('typeid'), 1)
        cates = models.AppRescategories.objects.filter(ParentId=0, IsShow=True).order_by('-DisplaySequence')
        res_list = []
        if typeid == 0:
            behaviour = get_dic_cate('behaviour')
            if behaviour:
                res_list.append(behaviour)
        for item in cates.all():
            dic = {}
            dic["CategoryId"] = item.CategoryId
            dic["Name"] = item.Name
            # 二级类
            child = models.AppRescategories.objects.filter(ParentId=item.CategoryId, IsShow=True)
            if child.count() > 0:
                dic['children'] = child.values_list('CategoryId', 'Name')
            else:
                dic['children'] = []
            res_list.append(dic)
        return Response(
            response.Content().ok('SUC', res_list).raw()
        )
