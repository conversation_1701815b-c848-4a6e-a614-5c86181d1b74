from rest_framework.decorators import action
from rest_framework.response import Response

from controller.coach.actions import get_coach_info
from controller.member.actions import get_member_info
from data import models, serializers, extension
from utils import int_arg


class CoachRateViewSet(extension.ResponseViewSet):
    queryset = models.AppCoachrate.objects.all()
    serializer_class = serializers.AppCoachrateSerializer

    @action(methods=['post'], detail=False)
    def add(self, request):
        main_data = request.data.get('mainData')
        uid = request.user.pk
        main_data['UserId'] = uid
        res = self.add_row(main_data)
        return Response(
            res.raw()
        )

    @action(methods=['post'], detail=False, url_path='update')
    def update_new(self, request):
        self.filter_blank_for_nonstringarg(request)
        main_data = request.data.get('mainData')
        res = self.update_row(main_data)
        return Response(
            res.lower_raw()
        )

    @action(methods=['post'], detail=False)
    def getpagedata(self, request):
        t = self.get_page_rows(request.data)
        if t.count() > 0:
            extra = []
            for item in t.all():
                dic = {}
                dic["eId"] = item.Id
                uinfo = get_member_info(int_arg(item.UserId, 0))
                if uinfo:
                    dic["img"] = uinfo.Photograph
                    dic["truename"] = uinfo.TrueName
                comodel = get_coach_info(item.CoachId)
                if comodel:
                    dic["coimg"] = comodel.Photograph
                    dic["cotruename"] = comodel.TrueName
                extra.append(dic)
            t.extra_list = extra
        return Response(
            self.page_data(t)
        )
