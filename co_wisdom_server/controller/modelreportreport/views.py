import json

from rest_framework.decorators import action
from rest_framework.response import Response

from controller.modeltemplate.actions import get_model_template_info
from controller.project.actions import get_project_info
from data import models, serializers, extension
from utils import response, value_or_default
from utils.model import add_option
from utils.user import get_user_ids


class ModelReportReportViewSet(extension.ResponseViewSet):
    queryset = models.AppModelreportreport.objects.all()
    serializer_class = serializers.AppModelreportreportSerializer

    @action(methods=['post'], detail=False)
    def getpagedata(self, request):
        for where in json.loads(request.data["wheres"]):
            if where['name'] == 'UserName':
                ids = get_user_ids(where['value'])
                if len(ids) > 0:
                    add_option(request.data, 'UserId', ids, 'checkbox')
            elif where['name'] == 'ModelName':
                ids = models.AppModeltemplate.objects.filter(Enable=1, ModelName__icontains=where['value']).values_list(
                    'ModelReportId', flat=True)
                if len(ids) > 0:
                    add_option(request.data, 'ModelReportId', ids, 'checkbox')
            elif where['name'] == 'ProName':
                ids = models.AppProbject.objects.filter(Enable=1, Name__icontains=where['value']).values_list(
                    'Probject_Id', flat=True)
                if len(ids) > 0:
                    add_option(request.data, 'ProbjectId', ids, 'checkbox')
            else:
                add_option(request.data, where['name'], where['value'], where['displayType'])
        if request.user.Role_Id != 1:
            roleid = models.SysUser.objects.filter(User_Id=request.user.pk).first().Role_Id
            add_option(request.data, 'rolesid', roleid, 'like')
        t = self.get_page_rows(request.data)
        if t.count() > 0:
            extra = []
            for item in t.all():
                dic = {}
                dic["eId"] = item.Id
                modelinfo = get_model_template_info(value_or_default(item.ModelReportId, 0))
                dic["ModelName"] = modelinfo.ModelName
                dic["ModelCode"] = modelinfo.PageCode
                dic["ProName"] = get_project_info(item.ProbjectId).Name
                username = models.SysUser.objects.filter(User_Id=item.UserId).first().UserName
                dic["UserName"] = username
                extra.append(dic)
            t.extra_list = extra
        return Response(
            self.page_data(t)
        )

    @action(methods=['post'], detail=False)
    def upload(self, request):
        file_obj = request.FILES.get('fileInput')
        res = self.upload_file(file_obj, ['.jpg', '.png', '.pdf'])
        return Response(
            res.raw()
        )

    @action(methods=['get'], detail=False)
    def getreportmodel(self, request):
        pici = request.query_params.get('pici')
        minfo = models.AppModelreportreport.objects.filter(pici=pici)
        if not minfo.exists():
            return Response(
                response.Content().error('报告还未生成，请稍后查看').raw()
            )
        dic = minfo.values('title', 'pici', 'reportjson', 'status', 'rolesid', 'ModelCode')[0]
        return Response(
            response.Content().ok('SUC', dic).raw()
        )

    @action(methods=['get'], detail=False)
    def getreportrelated(self, request):
        username = request.query_params.get('username')
        pid = request.query_params.get('pid')
        member_list = models.AppMember.objects.filter(TrueName=username)
        member = None
        for item in member_list.all():
            if models.AppProbjectrelation.objects.filter(ProbjectId=pid, UserId=item.User_Id).exists():
                member = item
        if not member:
            return Response(
                response.Content().error().raw()
            )
        data = []
        interested_list = models.AppMemberinterested.objects.filter(ProbjectId=pid,
                                                                    MasterMember_Id=member.User_Id).values_list(
            'Member_Id', flat=True)
        if interested_list:
            data = models.AppMember.objects.filter(User_Id__in=interested_list).values_list('TrueName', flat=True)
        return Response(
            response.Content().ok('', data).raw()
        )
