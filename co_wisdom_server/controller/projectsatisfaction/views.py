import datetime
import random
from django.conf import settings

from django.db.models import <PERSON><PERSON>, <PERSON>, Q, Count
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.viewsets import ModelViewSet, ViewSet, GenericViewSet

from controller.CacheTmpResult.actions import del_key
from controller.coach.actions import get_coach_info
from controller.dictionary.actions import get_dictionary_list_value
from controller.ildp.actions import get_my_project_objectives
from controller.member.actions import get_member_info
from controller.modelreportresult.actions import analysR413
from controller.project.actions import get_start_project_num_by_company, get_complete_project_num_by_company, \
    get_sign_project_num_by_company, get_project_info, get_project_info_by_user
from controller.projectinterview.actions import get_interview_times, update_times, get_student_times
from controller.projectreport.views import get_two_number
from data import models, serializers, extension
from utils import response, blank_to_none, null_or_blank, int_arg, value_or_default, enter_to_br
import json
from utils.messagecenter.center import push_message
from utils.messagecenter.actions import get_pici_link, subject_type, get_interview_end_to_coach_link,\
    get_interview_end_to_student_link, get_interview_end_to_coach_ok_link, get_interview_end_ok_to_student_link,\
    get_r411_link_to_student, get_r411_link_to_coach, get_r411_link_to_padmin, get_r411_link_to_cadmin
from utils.business import new_pici
from utils.model import add_option
from utils.user import get_user_ids
from controller.member.actions import get_company_name
from controller.projectinterview.project_interview_constant import TYPE_MAP


def save_report(result, title, rolesid, reportcode='', status=1, tindex=1):
    mreport = models.AppModelreportreport()
    mreport.ProbjectId = result.ProbjectId
    mreport.ModelReportId = result.ModelReportId
    mreport.UserId = result.UserId
    mreport.reportjson = result.Answer
    mreport.status = status
    mreport.pici = result.pici
    mreport.title = title
    mreport.rolesid = rolesid
    mreport.ModelCode = result.ModelCode
    mreport.ReportCode = reportcode
    mreport.CoachId = result.DataId
    mreport.tindex = tindex
    mreport.CreateDate = datetime.datetime.now().replace(microsecond=0)
    mreport.save()
    return mreport.Id


def create_r411(satisfaction_list):
    stuinfo = satisfaction_list.filter(userType=0).first()
    if not stuinfo:
        stuinfo = models.AppProbjectsatisfaction()
    coinfo = satisfaction_list.filter(userType=1).first()
    if not coinfo:
        coinfo = models.AppProbjectsatisfaction()
    mb = ''
    prinfo = get_my_project_objectives(stuinfo.ProbjectId, value_or_default(stuinfo.User_Id, 0))
    if prinfo.status:
        mb = prinfo.data.split('|||')[0]
    a1 = {
        'student': stuinfo.A2,
        'coach': coinfo.subject
    }
    a2 = {
        'student': stuinfo.A3
    }
    a3 = {
        'student': stuinfo.A1
    }
    a4 = {
        'student': stuinfo.A4,
        'coach': coinfo.modelobser
    }
    a5 = {
        'student': stuinfo.A5,
        'coach': coinfo.A1
    }
    a6 = {
        'student': stuinfo.A6,
        'coach': coinfo.A4
    }
    a7 = {
        'student': stuinfo.A7,
        'coach': coinfo.A2
    }
    # 已完成测评和报告 已完成的教练小时数 已完成的行动计划次数 已完成的学习任务次数 计划中剩余的教练小时数 规划中的测评和报告
    # 已完成测评和报告，只算被教练者的测评数量
    evnums = models.AppProbjectexamrelationuser.objects.filter(
        Q(interested_id=stuinfo.User_Id) | Q(UserId=stuinfo.User_Id),
        ProbjectId=stuinfo.ProbjectId,
        Status__gte=1).exclude(Q(ExId=18) |
                               Q(PageCode='r62')
                               ).values('ExId').annotate(Count('ExId')).count()
    # 已完成教练小时数 ok
    times = get_interview_times(stuinfo.User_Id, stuinfo.ProbjectId)
    # 已完成的行动计划
    acplan = models.AppProbjectlearningaction.objects.filter(ProbjectId=stuinfo.ProbjectId, User_Id=stuinfo.User_Id,
                                                             IsCompateActionPlan=1).count()
    # 已完成的学习任务
    learningplan = models.AppProbjectlearningaction.objects.filter(ProbjectId=stuinfo.ProbjectId,
                                                                   User_Id=stuinfo.User_Id,
                                                                   IsCompateLearningPlan=1).count()
    pinfo = get_project_info(stuinfo.ProbjectId)
    # 用户总小时数的取值是约谈设置中每人约谈时长--bug356
    total_times = get_student_times(pinfo.pk)
    # 计划中剩余小时数
    sytimes = total_times - times
    if isinstance(sytimes, float):
        if str(sytimes).split('.')[1] == 0:
            sytimes = int(sytimes)
    # 总报告 = 测评列表中添加的报告（只算被教练者测评数量，企业管理员满意度调查不算） + 个人阶段回顾 + 个人总结
    exam = models.AppProbjectexam.objects.filter(ProbjectId=stuinfo.ProbjectId).exclude(Q(ExId=18) |
                                                                                        Q(PageCode='r62'))
    # 阶段回顾
    interview_stage = models.AppProbjectinterview.objects.filter(User_Id=stuinfo.User_Id,
                                                                 ProbjectId=stuinfo.ProbjectId,
                                                                 interviewType=0, interviewsubject=3)
    # 个人总结
    interview_summary = models.AppProbjectinterview.objects.filter(User_Id=stuinfo.User_Id,
                                                                   ProbjectId=stuinfo.ProbjectId,
                                                                   interviewType=0, interviewsubject=4)
    total_count = exam.count() + interview_stage.count() + interview_summary.count()
    # 规划中的测评和报告
    waitev_num = total_count - evnums - interview_stage.filter(Satisfaction__isnull=False,
                                                               Times__isnull=False
                                                               ).count()
    uinfo = models.SysUser.objects.filter(User_Id=stuinfo.User_Id).first()
    cinfo = models.SysUser.objects.filter(User_Id=coinfo.User_Id).first()
    company_name = get_company_name(stuinfo.User_Id)
    data = {
        'reportdate': datetime.datetime.now().strftime('%Y-%m-%d'),
        'truename': uinfo.UserTrueName,
        'coachname': cinfo.UserTrueName,
        'mb': mb,
        'a1': a1,
        'a2': a2,
        'a3': a3,
        'a4': a4,
        'a5': a5,
        'a6': a6,
        'a7': a7,
        'evnums': evnums,
        'times': times,
        'acplan': acplan,
        'learningplan': learningplan,
        'sytimes': sytimes,
        'waitevnums': int(waitev_num),
        'company_name': company_name
    }
    pici = new_pici()
    result = models.AppModelreportresult()
    result.ProbjectId = stuinfo.ProbjectId
    result.ModelReportId = 10
    result.UserId = value_or_default(stuinfo.User_Id, 0)
    result.pici = pici
    result.ModelCode = "r410"
    result.DataId = value_or_default(coinfo.User_Id, 0)
    result.Answer = json.dumps(data, ensure_ascii=False, default=str)
    id = save_report(result, "阶段性回顾总结 综合报告 ", "3,4,5,6", "r410")
    return id

def create_r54(satisfaction_list):
    stuinfo = satisfaction_list.filter(userType=0).first()
    if not stuinfo:
        stuinfo = models.AppProbjectsatisfaction()
    coinfo = satisfaction_list.filter(userType=1).first()
    if not coinfo:
        coinfo = models.AppProbjectsatisfaction()
    pici = new_pici()
    a1 = {
        'student': stuinfo.subject,
        'coach': coinfo.subject
    }
    a2 = {
        'student': stuinfo.modelobser,
        'coach': ''
    }
    a3 = {
        'student': stuinfo.A1,
        'coach': coinfo.A1
    }
    a4 = {
        'student': stuinfo.A2,
        'coach': coinfo.A2
    }
    a5 = {
        'student': stuinfo.A3,
        'coach': coinfo.A3
    }
    a6 = {
        'student': stuinfo.A4,
        'coach': ''
    }
    result = models.AppModelreportresult()
    result.ProbjectId = stuinfo.ProbjectId
    result.ModelReportId = 12
    result.UserId = value_or_default(stuinfo.User_Id, 0)
    result.pici = pici
    result.ModelCode = "r54"
    result.DataId = value_or_default(coinfo.User_Id, 0)
    evnums = models.AppProbjectexamrelationuser.objects.filter(ProbjectId=result.ProbjectId, UserId=result.UserId,
                                                               Status__gt=1).count()
    times = get_interview_times(result.UserId, result.ProbjectId)
    acplan = models.AppProbjectlearningaction.objects.filter(ProbjectId=result.ProbjectId, User_Id=result.UserId,
                                                             IsCompateActionPlan=1).count()
    learningplan = models.AppProbjectlearningaction.objects.filter(ProbjectId=result.ProbjectId,
                                                                   User_Id=result.UserId,
                                                                   IsCompateLearningPlan=1).count()
    # 投入度
    listI = models.AppProbjectinterview.objects.filter(ProbjectId=result.ProbjectId, status=1, interviewType=0,
                                                       Satisfaction__gt=0, User_Id=result.UserId)
    nowInterview = 1
    if listI.exists():
        interview_max = listI.aggregate(Max('nowInterview')).get('nowInterview__max', 0)
        if interview_max > 0:
            nowInterview = interview_max
    jltitle = []
    czdata = []
    trdata = []
    jldata = []
    for i in range(1, nowInterview + 1):
        jltitle.append('第' + str(i) + '次')
        jllist = listI.filter(nowInterview=i)
        val = 0
        if jllist.count() > 0:
            val = get_two_number(jllist.aggregate(Sum('Satisfaction')).get('Satisfaction__sum') / jllist.count())
        jldata.append(val)
        val2 = 0
        if jllist.count() > 0:
            val2 = get_two_number(
                jllist.aggregate(Sum('growupSatisfied')).get('growupSatisfied__sum') / jllist.count())
        czdata.append(val2)
        val3 = 0
        if jllist.count() > 0:
            val3 = get_two_number(
                jllist.aggregate(Sum('inputSatisfied')).get('inputSatisfied__sum') / jllist.count())
        trdata.append(val3)
    listcz = {
        'title': jltitle,
        'data': czdata
    }
    listtr = {
        'title': jltitle,
        'data': trdata
    }
    listjl = {
        'title': jltitle,
        'data': jldata
    }

    cz = 1
    if listI.count() > 0:
        cz = get_two_number(listI.aggregate(Sum('growupSatisfied')).get('growupSatisfied__sum') / listI.count())
    else:
        cz = get_two_number(listI.aggregate(Sum('growupSatisfied')).get('growupSatisfied__sum'))
    tr = 1
    if listI.count() > 0:
        tr = get_two_number(listI.aggregate(Sum('inputSatisfied')).get('inputSatisfied__sum') / listI.count())
    else:
        tr = get_two_number(listI.aggregate(Sum('inputSatisfied')).get('inputSatisfied__sum'))
    jl = 1
    if listI.count() > 0:
        jl = get_two_number(listI.aggregate(Sum('Satisfaction')).get('Satisfaction__sum') / listI.count())
    else:
        jl = get_two_number(listI.aggregate(Sum('Satisfaction')).get('Satisfaction__sum'))
    uinfo = models.SysUser.objects.filter(User_Id=result.UserId).first()
    cinfo = models.SysUser.objects.filter(User_Id=result.DataId).first()
    projectname = get_project_info(result.ProbjectId).Name
    data = {
        'reportdate': datetime.datetime.now().strftime('%Y-%m-%d'),
        'truename': uinfo.UserTrueName,
        'coachname': cinfo.UserTrueName,
        'a1': a1,
        'a2': a2,
        'a3': a3,
        'a4': a4,
        'a5': a5,
        'a6': a6,
        'evnums': evnums,
        'times': times,
        'acplan': acplan,
        'learningplan': learningplan,
        'cz': cz,
        'czlist': listcz,
        'tr': tr,
        'trlist': listtr,
        'jl': jl,
        'jllist': listjl,
        'projectname': projectname
    }
    result.Answer = json.dumps(data, ensure_ascii=False, default=str)
    id = save_report(result, "最后一次约谈后 客户总结报告", "3,4,5,6", "r55")
    # analysR413(result.ProbjectId, result.pici, 1)


class ProjectSatisfactionViewSet(extension.ResponseViewSet):
    queryset = models.AppProbjectsatisfaction.objects.all()
    serializer_class = serializers.AppProbjectsatisfactionSerializer

    @action(methods=['post'], detail=False)
    def getpagedata(self, request):
        t = self.get_page_rows(request.data)
        if t.count() > 0:
            extra = []
            for item in t.all():
                dic = {}
                dic["eId"] = item.Id
                # 教练信息 姓名 头像 群智教练级别 认证 /职位 最近登陆时间
                comodel = get_coach_info(item.CoachId)
                if comodel and comodel.CoachId > 0:
                    dic["img"] = comodel.Photograph
                    dic["level"] = get_dictionary_list_value("coachlevel", comodel.CoachLevel)
                    dic["truename"] = comodel.TrueName
                    dic["jlzz"] = comodel.jlzz
                    dic["jlzw"] = comodel.jlzw
                dic["ProName"] = get_project_info(item.ProbjectId).Name
                extra.append(dic)
            t.extra_list = extra
        return Response(
            self.page_data(t)
        )

    @action(methods=['post'], detail=False)
    def AddNew(self, request):
        """ 常规约谈调用接口 """
        main_data = request.data.get('mainData')
        diary = main_data.get('diary')
        KeyName = main_data.get('KeyName')
        if null_or_blank(KeyName):
            return Response(
                response.Content().error('非法的约谈类型记录').raw()
            )
        arrkey = KeyName.split('_')
        main_data['ModelCode'] = arrkey[1]
        usertype = int_arg(main_data.get('userType'))
        interviewId = int_arg(main_data.get('interviewId'))
        User_Id = int_arg(main_data.get('User_Id'))
        ProbjectId = int_arg(main_data.get('ProbjectId'))
        A8 = main_data.get('A8')
        plan_list = main_data.get('plan_list', [])
        if not A8:
            A8 = ''
            main_data['A8'] = ''
        interview = models.AppProbjectinterview.objects.filter(pk=interviewId)
        if interview and interview.first().interviewsubject not in TYPE_MAP.keys():
            return Response(
                response.Content().error('约谈类型错误').raw()
            )
        if models.AppProbjectsatisfaction.objects.filter(User_Id=User_Id, interviewId=interviewId,
                                                         ProbjectId=ProbjectId).exists():

            return Response(
                response.Content().error('请勿重复提交').raw()
            )
        res = self.add_row(main_data)
        if not res.status:
            return Response(
                response.Content().error('生成报告出错').raw()
            )

        del_key(KeyName)
        # 更新约谈状态及能力时间
        uinfo = res.row
        # interviewsubject type 区分pagecode
        pinfo = models.AppProbjectinterview.objects.filter(Id=uinfo.interviewId, User_Id=uinfo.User_Id,
                                                           ProbjectId=uinfo.ProbjectId).first()
        if usertype == 1:
            pinfo = models.AppProbjectinterview.objects.filter(Id=uinfo.interviewId, Coach_Id=uinfo.User_Id,
                                                               ProbjectId=uinfo.ProbjectId).first()
        if pinfo:
            current_user = models.SysUser.objects.filter(User_Id=request.user.pk).first()
            roleid = current_user.Role_Id
            if roleid == 6:
                # 被教练者填写常规约谈,通知已填写的教练
                coach = models.SysUser.objects.get(pk=pinfo.Coach_Id)
                student = models.SysUser.objects.get(pk=pinfo.User_Id)
                company = models.AppCompany.objects.get(Company_Id=models.AppProbjectrelation.objects.filter(
                    ProbjectId=pinfo.ProbjectId, UserId=pinfo.User_Id).first().CompanyId)
                interview_ildp_to_coach_ok = 'interview_ildp_to_coach_ok'
                interview_ildp_to_coach = 'interview_ildp_to_coach'
                pici = get_pici_link(pinfo.Coach_Id, pinfo.interviewsubject, pinfo.ProbjectId, pinfo.pk)

                dic = {
                    'studentname': student.UserTrueName,
                    'interview_date': pinfo.EndTime.strftime('%Y-%m-%d'),
                    'company': company.CompanyName,
                    'path_link': get_interview_end_to_coach_link(pinfo.interviewsubject, pinfo.ProbjectId, pici, student.pk),
                }
                if pinfo.Times:
                    dic['path_link'] = get_interview_end_to_coach_ok_link(pinfo.interviewsubject, pinfo.ProbjectId, pici, student.pk)
                    push_message.delay(coach, interview_ildp_to_coach_ok, dic, pinfo.ProbjectId)
                # 被教练者填写目标约谈,通知未填写的教练
                else:
                    push_message.delay(coach, interview_ildp_to_coach, dic, pinfo.ProbjectId)
            elif roleid == 4:
                # 教练者填写目标约谈,通知已填写的被教练者
                coach = models.SysUser.objects.get(pk=pinfo.Coach_Id)
                student = models.SysUser.objects.get(pk=pinfo.User_Id)
                company = models.AppCompany.objects.get(Company_Id=models.AppProbjectrelation.objects.filter(
                    ProbjectId=pinfo.ProbjectId, UserId=pinfo.User_Id).first().CompanyId)
                interview_ildp_to_student_ok = 'interview_ildp_to_student_ok'
                interview_ildp_to_student = 'interview_ildp_to_student'
                pici = get_pici_link(pinfo.User_Id, pinfo.interviewsubject, pinfo.ProbjectId, pinfo.pk)

                dic = {
                    'coach': coach.UserTrueName,
                    'interview_date': pinfo.EndTime.strftime('%Y-%m-%d'),
                    'company': company.CompanyName,
                    'path_link': get_interview_end_to_student_link()
                }
                if pinfo.Satisfaction:
                    dic['path_link'] = get_interview_end_ok_to_student_link(pinfo.interviewsubject, pinfo.ProbjectId, pici, coach.pk)
                    push_message.delay(student, interview_ildp_to_student_ok, dic, pinfo.ProbjectId)
                # 教练者填写目标约谈,通知未填写的被教练者
                else:
                    push_message.delay(student, interview_ildp_to_student, dic, pinfo.ProbjectId)
            if usertype == 0:
                pinfo.Satisfaction = uinfo.CocahRank
                pinfo.growupSatisfied = uinfo.GrowthRank
                pinfo.inputSatisfied = uinfo.InvestmentRank
                pinfo.save()
                # 更新行动计划
                # print(type(plan_list), plan_list)
                if not null_or_blank(uinfo.actionplan) and plan_list:
                    for plan in plan_list:
                        m = models.AppProbjectlearningaction(Title=plan, ActionPlan=plan,
                                                             ProbjectId=uinfo.ProbjectId,
                                                             User_Id=value_or_default(uinfo.User_Id, 0),
                                                             interviewId=uinfo.interviewId)
                        m.CreateDate = datetime.datetime.now()
                        m.Creator = request.user.UserName
                        m.save()
                # if not null_or_blank(uinfo.actionplan):
                #     arr = uinfo.actionplan.split('\n')
                #     for item in arr:
                #         if not null_or_blank(item):
                #             m = models.AppProbjectlearningaction(Title=item, ActionPlan=item,
                #                                                  ProbjectId=uinfo.ProbjectId,
                #                                                  User_Id=value_or_default(uinfo.User_Id, 0),
                #                                                  interviewId=uinfo.interviewId)
                #             m.CreateDate = datetime.datetime.now()
                #             m.Creator = request.user.UserName
                #             m.save()
            else:
                update_times(pinfo)
                if not null_or_blank(uinfo.A8):
                    arrmb = uinfo.A8.split('|||')
                    if len(arrmb) > 0:
                        for item in arrmb:
                            mb = item.split('@@')
                            if len(mb) >= 2:
                                info = models.AppProbjectupability()
                                info.ProbjectId = uinfo.ProbjectId
                                info.User_Id = pinfo.User_Id
                                info_times = 0
                                if mb[1].isnumeric():
                                    info_times = int(mb[1])
                                info.Times = info_times
                                info.UpAbility = "b-" + mb[0]
                                info.save()
                if not null_or_blank(diary):
                    diaryinfo = models.AppGrowthdiary()
                    diaryinfo.User_Id = 0
                    diaryinfo.Coach_Id = value_or_default(uinfo.User_Id, 0)
                    diaryinfo.ProbjectId = uinfo.ProbjectId
                    diaryinfo.isPublic = 0
                    diaryinfo.Remark = diary
                    diaryinfo.Title = diary[:100]
                    diaryinfo.CreateDate = datetime.datetime.now()
                    diaryinfo.Creator = request.user.UserName
                    diaryinfo.save()
        if 'r411' in KeyName or 'r54' in KeyName:
            res_list = models.AppProbjectsatisfaction.objects.filter(ProbjectId=uinfo.ProbjectId,
                                                                     interviewId=uinfo.interviewId)
            if res_list.count() == 2:
                if 'r411' in KeyName:
                    report_id = create_r411(res_list)
                    try:
                        # 发送个人阶段回顾报告通知
                        # 发送被教练者
                        title = '阶段性回顾总结 综合报告'

                        student = models.SysUser.objects.get(pk=res_list.filter(userType=0).first().User_Id)
                        coach = models.SysUser.objects.get(pk=res_list.filter(userType=1).first().User_Id)
                        dic = {
                            'title': title,
                            'evaluation_name': title,
                            'studentname': student.UserTrueName,
                            'path_link': '-'
                        }

                        pici = get_pici_link(student.pk, 3, uinfo.ProbjectId, interviewId)
                        if student:
                            dic['path_link'] = get_r411_link_to_student(uinfo.ProbjectId, pici, coach.pk)
                            push_message.delay(student, 'end_report_to_manage', dic, uinfo.ProbjectId)
                        if coach:
                            dic['path_link'] = get_r411_link_to_coach(uinfo.ProbjectId, pici, student.pk)

                            push_message.delay(coach, 'end_report_to_manage', dic, uinfo.ProbjectId)
                        project = models.AppProbject.objects.get(pk=ProbjectId)

                        project_manage_user = models.SysUser.objects.get(pk=project.Relation_UserId)
                        report_pici = models.AppModelreportreport.objects.get(pk=report_id).pici
                        if project_manage_user:
                            dic['path_link'] = get_r411_link_to_padmin(uinfo.ProbjectId, report_pici)
                            push_message.delay(project_manage_user, 'exam_end_to_manage', dic, uinfo.ProbjectId)

                        # 企业管理员可能有多个 IsManger project_id
                        user_ids = models.AppProbjectrelation.objects.filter(ProbjectId=project.pk,
                                                                             IsManger=True).values_list('UserId',
                                                                                                        flat=True)
                        company_user = models.SysUser.objects.filter(pk__in=user_ids)
                        if company_user.exists:
                            for user in company_user:
                                dic['path_link'] = get_r411_link_to_cadmin(uinfo.ProbjectId, report_pici)

                                push_message.delay(user, 'exam_end_to_manage', dic, uinfo.ProbjectId)

                    except Exception as e:
                        print(e)
                if 'r54' in KeyName:
                    create_r54(res_list)
        return Response(
            res.raw()
        )

    @action(methods=['get'], detail=False)
    def GetProbjectSatisfaction(self, request):
        uid = int_arg(request.query_params.get('uid'))
        probjectid = int_arg(request.query_params.get('probjectid'))
        interviewId = int_arg(request.query_params.get('interviewId'))
        res_list = models.AppProbjectsatisfaction.objects.filter(ProbjectId=probjectid, interviewId=interviewId)
        roleid = 0
        request_user = models.SysUser.objects.filter(User_Id=request.user.pk).first()
        if request_user:
            roleid = request_user.Role_Id

        if res_list.filter(User_Id=uid).count() == 0 and roleid not in [3, 5]:
            return Response(
                response.Content().error('请先完成约谈记录').raw()
            )
        title = "常规约谈"
        truename = ''
        coachname = ''
        interviewinfo = models.AppProbjectinterview.objects.filter(Id=interviewId).first()
        if interviewinfo:
            title = interviewinfo.Title
        _sublist = []
        _modlist = []
        _actlist = []

        stuinfo = res_list.filter(userType=0).first()
        if stuinfo:
            uinfo = get_member_info(stuinfo.User_Id)
            truename = uinfo.TrueName
            _subdic = {
                'usertype': 0,
                'satisfactionid': stuinfo.Id,
                'userimg': uinfo.Photograph,
                'username': uinfo.TrueName,
                'content': enter_to_br(stuinfo.subject)
            }
            _sublist.append(_subdic)
            _moddic = {
                'usertype': 0,
                'satisfactionid': stuinfo.Id,
                'userimg': uinfo.Photograph,
                'username': uinfo.TrueName,
                'content': enter_to_br(stuinfo.modelobser)
            }
            _modlist.append(_moddic)
            _actdic = {
                'usertype': 0,
                'satisfactionid': stuinfo.Id,
                'userimg': uinfo.Photograph,
                'username': uinfo.TrueName,
                'content': enter_to_br(stuinfo.actionplan)
            }
            _actlist.append(_actdic)
        else:
            stuinfo = models.AppProbjectsatisfaction()
        coinfo = res_list.filter(userType=1).first()
        if coinfo:
            uinfo = get_coach_info(coinfo.User_Id)
            coachname = uinfo.TrueName
            _subdic = {
                'usertype': 1,
                'satisfactionid': coinfo.Id,
                'userimg': uinfo.Photograph,
                'username': uinfo.TrueName,
                'content': enter_to_br(coinfo.subject)
            }
            _sublist.append(_subdic)
            _moddic = {
                'usertype': 1,
                'satisfactionid': coinfo.Id,
                'userimg': uinfo.Photograph,
                'username': uinfo.TrueName,
                'content': enter_to_br(coinfo.modelobser)
            }
            _modlist.append(_moddic)
            _actdic = {
                'usertype': 1,
                'satisfactionid': coinfo.Id,
                'userimg': uinfo.Photograph,
                'username': uinfo.TrueName,
                'content': enter_to_br(coinfo.actionplan)
            }
            _actlist.append(_actdic)
        else:
            coinfo = models.AppProbjectsatisfaction()
        # 兼容A2 times 聚集》A8, A4其他
        a8 = coinfo.A8
        a4 = coinfo.A4
        otherdic = {}
        if not null_or_blank(a4):
            arr = a4.split('@@')
            if len(arr) == 2 and not null_or_blank(arr[0]) and not null_or_blank(arr[1]):
                otherdic['other'] = arr[0]
                otherdic['othertime'] = round(int(arr[1]) / 60, 1)
            else:
                otherdic['other'] = arr[0]
        _planlist = []
        if not null_or_blank(a8):
            arr = a8.split('|||')
            for item in arr:
                if not null_or_blank(item):
                    arrx = item.split('@@')
                    behaviour = get_dictionary_list_value('behaviour', arrx[0])
                    _tims = round(int(arrx[1]) / 60, 1)
                    _planlist.append({
                        'value': behaviour,
                        'time': _tims
                    })
        elif not null_or_blank(coinfo.A2):
            behaviour = get_dictionary_list_value('behaviour', coinfo.A2)
            _tims = round(coinfo.times / 60, 1)
            _planlist.append({
                'value': behaviour,
                'time': _tims
            })
        date = {
            'title': title,
            'truename': truename,
            'coachname': coachname,
            'cocahrank': stuinfo.CocahRank,
            'growthrank': stuinfo.GrowthRank,
            'investmentrank': stuinfo.InvestmentRank,
            'devplan': _planlist,
            'otherplan': otherdic,
            'A5': enter_to_br(coinfo.A5),
            'subject': _sublist,
            'modelobser': _modlist,
            'actionplan': _actlist,
            'CreateDate': stuinfo.CreateDate,
            'CreateDate2': coinfo.CreateDate
        }
        return Response(
            response.Content().ok('SUC', date).raw()
        )

    @action(methods=['get'], detail=False)
    def GetProbjectThirdReport(self, request):
        uid = int_arg(request.query_params.get('uid'))
        probjectid = int_arg(request.query_params.get('probjectid'))
        interviewId = int_arg(request.query_params.get('interviewId'))
        res_list = models.AppProbjectsatisfaction.objects.filter(ProbjectId=probjectid, interviewId=interviewId)
        roleid = 0
        request_user = models.SysUser.objects.filter(User_Id=request.user.pk).first()
        if request_user:
            roleid = request_user.Role_Id

        if res_list.filter(User_Id=uid).count() == 0 and roleid not in [3, 5]:
            return Response(
                response.Content().error('请先完成约谈记录').raw()
            )
        title = "三方约谈记录"
        truename = ''
        coachname = ''
        interviewinfo = models.AppProbjectinterview.objects.filter(Id=interviewId).first()
        if interviewinfo:
            title = interviewinfo.Title
        _sublist = []
        _modlist = []
        _actlist = []
        _planlist = []
        stuinfo = res_list.filter(userType=0).first()
        if stuinfo:
            uinfo = get_member_info(stuinfo.User_Id)
            truename = uinfo.TrueName
            _plandic = {
                'usertype': 0,
                'satisfactionid': stuinfo.Id,
                'userimg': uinfo.Photograph,
                'username': uinfo.TrueName,
            }
            if not null_or_blank(stuinfo.A8):
                _dev = []
                arr = stuinfo.A8.split('|||')
                for item in arr:
                    if not null_or_blank(item):
                        arrx = item.split('@@')
                        behaviour = get_dictionary_list_value('behaviour', arrx[0])
                        _dev.append({
                            'value': behaviour,
                            'mb': arrx[1],
                            'usertype': 0
                        })
                _plandic['content'] = _dev
            else:
                _plandic['content'] = None
            _planlist.append(_plandic)
            _moddic = {
                'usertype': 0,
                'satisfactionid': stuinfo.Id,
                'userimg': uinfo.Photograph,
                'username': uinfo.TrueName,
                'content': enter_to_br(stuinfo.modelobser)
            }
            _modlist.append(_moddic)
            _actdic = {
                'usertype': 0,
                'satisfactionid': stuinfo.Id,
                'userimg': uinfo.Photograph,
                'username': uinfo.TrueName,
                'content': enter_to_br(stuinfo.actionplan)
            }
            _actlist.append(_actdic)
        else:
            stuinfo = models.AppProbjectsatisfaction()
        coinfo = res_list.filter(userType=1).first()
        if coinfo:
            uinfo = get_coach_info(coinfo.User_Id)
            coachname = uinfo.TrueName
            _plandic = {
                'usertype': 1,
                'satisfactionid': coinfo.Id,
                'userimg': uinfo.Photograph,
                'username': uinfo.TrueName,
                'content': enter_to_br(coinfo.subject)
            }
            if not null_or_blank(coinfo.A8):
                _dev = []
                arr = coinfo.A8.split('|||')
                for item in arr:
                    if not null_or_blank(item):
                        arrx = item.split('@@')
                        behaviour = get_dictionary_list_value('behaviour', arrx[0])
                        _dev.append({
                            'value': behaviour,
                            'mb': arrx[1],
                            'usertype': 1
                        })
                _plandic['content'] = _dev
            else:
                _plandic['content'] = None
            _planlist.append(_plandic)
            _moddic = {
                'usertype': 1,
                'satisfactionid': coinfo.Id,
                'userimg': uinfo.Photograph,
                'username': uinfo.TrueName,
                'content': enter_to_br(coinfo.modelobser)
            }
            _modlist.append(_moddic)
            _actdic = {
                'usertype': 1,
                'satisfactionid': coinfo.Id,
                'userimg': uinfo.Photograph,
                'username': uinfo.TrueName,
                'content': enter_to_br(coinfo.actionplan)
            }
            _actlist.append(_actdic)
        else:
            coinfo = models.AppProbjectsatisfaction()
        date = {
            'title': title,
            'truename': truename,
            'coachname': coachname,
            'cocahrank': stuinfo.CocahRank,
            'growthrank': stuinfo.GrowthRank,
            'investmentrank': stuinfo.InvestmentRank,
            'devplan': _planlist,
            'A5': enter_to_br(coinfo.A5),
            'subject': _sublist,
            'modelobser': _modlist,
            'actionplan': _actlist,
            'CreateDate': stuinfo.CreateDate,
            'CreateDate2': coinfo.CreateDate
        }
        return Response(
            response.Content().ok('SUC', date).raw()
        )

    @action(methods=['get'], detail=False)
    def GetProbjectR411Report(self, request):
        interviewId = int_arg(request.query_params.get('interviewid'))
        res_list = models.AppProbjectsatisfaction.objects.filter(interviewId=interviewId)

        request_user = models.SysUser.objects.filter(User_Id=request.user.pk).first()
        if request_user:
            roleid = request_user.Role_Id
        coinfo = res_list.filter(userType=1).first()
        stuinfo = res_list.filter(userType=0).first()
        # 按照提交日期判断题目版本
        if (coinfo and coinfo.CreateDate < datetime.datetime.fromtimestamp(1646496000))\
                or (stuinfo and stuinfo.CreateDate < datetime.datetime.fromtimestamp(1646496000)):
            title = "阶段回顾"
            truename = ''
            coachname = ''
            interviewinfo = models.AppProbjectinterview.objects.filter(Id=interviewId).first()
            if interviewinfo:
                title = interviewinfo.Title
            _sublist = []
            _modlist = []
            _actlist = []
            _a4list = []
            _a1list = []
            _a2list = []

            # type= 0：被教练者  1：教练
            # 被教练者
            if stuinfo:
                uinfo = get_member_info(stuinfo.User_Id)
                truename = uinfo.TrueName
                _subdic = {
                    'usertype': 0,
                    'username': uinfo.TrueName,
                    'content': enter_to_br(stuinfo.subject)
                }
                _sublist.append(_subdic)
                _moddic = {
                    'usertype': 0,
                    'username': uinfo.TrueName,
                    'content': enter_to_br(stuinfo.modelobser)
                }
                _modlist.append(_moddic)
                _actdic = {
                    'usertype': 0,
                    'username': uinfo.TrueName,
                    'content': enter_to_br(stuinfo.actionplan)
                }
                _actlist.append(_actdic)
                _a4dic = {
                    'usertype': 0,
                    'username': uinfo.TrueName,
                    'content': enter_to_br(stuinfo.A4)
                }
                _a4list.append(_a4dic)
                _a1dic = {
                    'usertype': 0,
                    'username': uinfo.TrueName,
                    'content': enter_to_br(stuinfo.A1)
                }
                _a1list.append(_a1dic)
                _a2dic = {
                    'usertype': 0,
                    'username': uinfo.TrueName,
                    'content': enter_to_br(stuinfo.A2)
                }
                _a2list.append(_a2dic)
            else:
                stuinfo = models.AppProbjectsatisfaction()

            # 教练
            if coinfo:
                uinfo = get_coach_info(coinfo.User_Id)
                coachname = uinfo.TrueName
                _subdic = {
                    'usertype': 1,
                    'username': uinfo.TrueName,
                    'content': enter_to_br(coinfo.subject)
                }
                _sublist.append(_subdic)
                _moddic = {
                    'usertype': 1,
                    'username': uinfo.TrueName,
                    'content': enter_to_br(coinfo.modelobser)
                }
                _modlist.append(_moddic)
                _actdic = {
                    'usertype': 1,
                    'username': uinfo.TrueName,
                    'content': enter_to_br(coinfo.actionplan)
                }
                _actlist.append(_actdic)
                _a4dic = {
                    'usertype': 1,
                    'username': uinfo.TrueName,
                    'content': enter_to_br(coinfo.A4)
                }
                _a4list.append(_a4dic)
                _a1dic = {
                    'usertype': 1,
                    'username': uinfo.TrueName,
                    'content': enter_to_br(coinfo.A1)
                }
                _a1list.append(_a1dic)
                _a2dic = {
                    'usertype': 1,
                    'username': uinfo.TrueName,
                    'content': enter_to_br(coinfo.A2)
                }
                _a2list.append(_a2dic)
            else:
                coinfo = models.AppProbjectsatisfaction()
            date = {
                'title': title,
                'coachee_name': truename,
                'coach_name': coachname,
                'interview_date': datetime.datetime.strftime(interviewinfo.StartTime, '%Y-%m-%d %H:%M') + '-' +
                                  datetime.datetime.strftime(interviewinfo.EndTime, '%H:%M'),
                'report_date': datetime.datetime.strftime(stuinfo.CreateDate,
                                                          '%Y-%m-%d %H:%M') if stuinfo.CreateDate else datetime.datetime.strftime(
                    coinfo.CreateDate, '%Y-%m-%d %H:%M'),
                'cocahrank': stuinfo.CocahRank,
                'growthrank': stuinfo.GrowthRank,
                'investmentrank': stuinfo.InvestmentRank,
                'a1': _a1list,
                'a2': _a2list,
                'a4': _a4list,
                'a5': enter_to_br(coinfo.A5),
                'subject': _sublist,
                'modelobser': _modlist,
                'actionplan': _actlist,
                'CreateDate': stuinfo.CreateDate,
                'CreateDate2': coinfo.CreateDate
            }
        else:
            title = "阶段回顾"
            truename = ''
            coachname = ''
            interviewinfo = models.AppProbjectinterview.objects.filter(Id=interviewId).first()
            if interviewinfo:
                title = interviewinfo.Title
            _sublist = []
            _modlist = []
            _actlist = []
            _a1list = []
            _a2list = []
            _a3list = []
            _a4list = []
            _a5list = []
            _a6list = []
            _a7list = []

            # type= 0：被教练者  1：教练
            # 被教练者
            if stuinfo:
                uinfo = get_member_info(stuinfo.User_Id)
                truename = uinfo.TrueName
                _actdic = {
                    'usertype': 0,
                    'username': uinfo.TrueName,
                    'content': enter_to_br(stuinfo.actionplan)
                }
                _actlist.append(_actdic)
                _a4dic = {
                    'usertype': 0,
                    'username': uinfo.TrueName,
                    'content': enter_to_br(stuinfo.A4)
                }
                _a4list.append(_a4dic)
                _a1dic = {
                    'usertype': 0,
                    'username': uinfo.TrueName,
                    'content': enter_to_br(stuinfo.A2)
                }
                _a1list.append(_a1dic)
                _a2dic = {
                    'usertype': 0,
                    'username': uinfo.TrueName,
                    'content': enter_to_br(stuinfo.A3)
                }
                _a2list.append(_a2dic)
                _a3dic = {
                    'usertype': 0,
                    'username': uinfo.TrueName,
                    'content': enter_to_br(stuinfo.A1)
                }
                _a3list.append(_a3dic)
                _a5dic = {
                    'usertype': 0,
                    'username': uinfo.TrueName,
                    'content': enter_to_br(stuinfo.A5)
                }
                _a5list.append(_a5dic)
                _a6dic = {
                    'usertype': 0,
                    'username': uinfo.TrueName,
                    'content': enter_to_br(stuinfo.A6)
                }
                _a6list.append(_a6dic)
                _a7dic = {
                    'usertype': 0,
                    'username': uinfo.TrueName,
                    'content': enter_to_br(stuinfo.A7)
                }
                _a7list.append(_a7dic)
            else:
                stuinfo = models.AppProbjectsatisfaction()

            # 教练
            if coinfo:
                uinfo = get_coach_info(coinfo.User_Id)
                coachname = uinfo.TrueName
                _actdic = {
                    'usertype': 1,
                    'username': uinfo.TrueName,
                    'content': enter_to_br(coinfo.actionplan)
                }
                _actlist.append(_actdic)
                _a1dic = {
                    'usertype': 1,
                    'username': uinfo.TrueName,
                    'content': enter_to_br(coinfo.subject)
                }
                _a1list.append(_a1dic)
                _a4dic = {
                    'usertype': 1,
                    'username': uinfo.TrueName,
                    'content': enter_to_br(coinfo.modelobser)
                }
                _a4list.append(_a4dic)
                _a5dic = {
                    'usertype': 1,
                    'username': uinfo.TrueName,
                    'content': enter_to_br(coinfo.A1)
                }
                _a5list.append(_a5dic)
                _a6dic = {
                    'usertype': 1,
                    'username': uinfo.TrueName,
                    'content': enter_to_br(coinfo.A4)
                }
                _a6list.append(_a6dic)
                _a7dic = {
                    'usertype': 1,
                    'username': uinfo.TrueName,
                    'content': enter_to_br(coinfo.A2)
                }
                _a7list.append(_a7dic)
            else:
                coinfo = models.AppProbjectsatisfaction()
            date = {
                'title': title,
                'coachee_name': truename,
                'coach_name': coachname,
                'interview_date': datetime.datetime.strftime(interviewinfo.StartTime, '%Y-%m-%d %H:%M') + '-' +
                                  datetime.datetime.strftime(interviewinfo.EndTime, '%H:%M'),
                'report_date': datetime.datetime.strftime(stuinfo.CreateDate,
                                                          '%Y-%m-%d %H:%M') if stuinfo.CreateDate else datetime.datetime.strftime(coinfo.CreateDate, '%Y-%m-%d %H:%M'),
                'cocahrank': stuinfo.CocahRank,
                'growthrank': stuinfo.GrowthRank,
                'investmentrank': stuinfo.InvestmentRank,
                'a1': _a1list,
                'a2': _a2list,
                'a3': _a3list,
                'a4': _a4list,
                'a5': _a5list,
                'a6': _a6list,
                'a7': _a7list,
                'coach_remark': enter_to_br(coinfo.A5),
                'subject': _sublist,
                'modelobser': _modlist,
                'actionplan': _actlist,
                'CreateDate': stuinfo.CreateDate,
                'CreateDate2': coinfo.CreateDate
            }
        return Response(
            response.Content().ok('SUC', date).raw()
        )

    @action(methods=['get'], detail=False)
    def GetProbjectR54Report(self, request):
        uid = int_arg(request.query_params.get('uid'))
        probjectid = int_arg(request.query_params.get('probjectid'))
        interviewId = int_arg(request.query_params.get('interviewId'))
        res_list = models.AppProbjectsatisfaction.objects.filter(ProbjectId=probjectid, interviewId=interviewId)
        roleid = 0
        request_user = models.SysUser.objects.filter(User_Id=request.user.pk).first()
        if request_user:
            roleid = request_user.Role_Id

        if res_list.filter(User_Id=uid).count() == 0 and roleid != 3:
            return Response(
                response.Content().error('请先完成约谈记录').raw()
            )
        title = "总结约谈总结"
        truename = ''
        coachname = ''
        interviewinfo = models.AppProbjectinterview.objects.filter(Id=interviewId).first()
        if interviewinfo:
            title = interviewinfo.Title
        _sublist = []
        _actlist = []
        _a1list = []
        _a2list = []
        _a3list = []
        stuinfo = res_list.filter(userType=0).first()
        if stuinfo:
            uinfo = get_member_info(stuinfo.User_Id)
            truename = uinfo.TrueName
            _subdic = {
                'usertype': 0,
                'satisfactionid': stuinfo.Id,
                'userimg': uinfo.Photograph,
                'username': uinfo.TrueName,
                'content': enter_to_br(stuinfo.subject)
            }
            _sublist.append(_subdic)
            _actdic = {
                'usertype': 0,
                'satisfactionid': stuinfo.Id,
                'userimg': uinfo.Photograph,
                'username': uinfo.TrueName,
                'content': enter_to_br(stuinfo.actionplan)
            }
            _actlist.append(_actdic)
            _a1dic = {
                'usertype': 0,
                'satisfactionid': stuinfo.Id,
                'userimg': uinfo.Photograph,
                'username': uinfo.TrueName,
                'content': enter_to_br(stuinfo.A1)
            }
            _a1list.append(_a1dic)
            _a2dic = {
                'usertype': 0,
                'satisfactionid': stuinfo.Id,
                'userimg': uinfo.Photograph,
                'username': uinfo.TrueName,
                'content': enter_to_br(stuinfo.A2)
            }
            _a2list.append(_a2dic)
            _a3dic = {
                'usertype': 0,
                'satisfactionid': stuinfo.Id,
                'userimg': uinfo.Photograph,
                'username': uinfo.TrueName,
                'content': enter_to_br(stuinfo.A3)
            }
            _a3list.append(_a3dic)
        else:
            stuinfo = models.AppProbjectsatisfaction()
        coinfo = res_list.filter(userType=1).first()
        if coinfo:
            uinfo = get_coach_info(coinfo.User_Id)
            coachname = uinfo.TrueName
            _subdic = {
                'usertype': 1,
                'satisfactionid': coinfo.Id,
                'userimg': uinfo.Photograph,
                'username': uinfo.TrueName,
                'content': enter_to_br(coinfo.subject)
            }
            _sublist.append(_subdic)
            _actdic = {
                'usertype': 1,
                'satisfactionid': coinfo.Id,
                'userimg': uinfo.Photograph,
                'username': uinfo.TrueName,
                'content': enter_to_br(coinfo.actionplan)
            }
            _actlist.append(_actdic)
            _a1dic = {
                'usertype': 1,
                'satisfactionid': coinfo.Id,
                'userimg': uinfo.Photograph,
                'username': uinfo.TrueName,
                'content': enter_to_br(coinfo.A1)
            }
            _a1list.append(_a1dic)
            _a2dic = {
                'usertype': 1,
                'satisfactionid': coinfo.Id,
                'userimg': uinfo.Photograph,
                'username': uinfo.TrueName,
                'content': enter_to_br(coinfo.A2)
            }
            _a2list.append(_a2dic)
            _a3dic = {
                'usertype': 1,
                'satisfactionid': coinfo.Id,
                'userimg': uinfo.Photograph,
                'username': uinfo.TrueName,
                'content': enter_to_br(coinfo.A3)
            }
            _a3list.append(_a3dic)
        else:
            coinfo = models.AppProbjectsatisfaction()
        date = {
            'title': title,
            'truename': truename,
            'modelobser': stuinfo.modelobser,
            'coachname': coachname,
            'cocahrank': stuinfo.CocahRank,
            'growthrank': stuinfo.GrowthRank,
            'investmentrank': stuinfo.InvestmentRank,
            'A1': _a1list,
            'A2': _a2list,
            'A3': _a3list,
            'A4': enter_to_br(stuinfo.A4),
            'A5': enter_to_br(coinfo.A5),
            'subject': _sublist,
            'actionplan': _actlist,
            'CreateDate': stuinfo.CreateDate,
            'CreateDate2': coinfo.CreateDate
        }
        return Response(
            response.Content().ok('SUC', date).raw()
        )

    @action(methods=['get'], detail=False)
    def GetProbjectSatisfactionInfo(self, request):
        uid = int_arg(request.query_params.get('uid'))
        probjectid = int_arg(request.query_params.get('probjectid'))
        interviewId = int_arg(request.query_params.get('interviewId'))
        usertype = int_arg(request.query_params.get('usertype'), 0)
        res_list = models.AppProbjectsatisfaction.objects.filter(ProbjectId=probjectid, interviewId=interviewId)
        roleid = 0
        request_user = models.SysUser.objects.filter(User_Id=request.user.pk).first()
        if request_user:
            roleid = request_user.Role_Id

        if res_list.filter(User_Id=uid).count() == 0 and roleid != 3:
            return Response(
                response.Content().error('请先完成约谈记录').raw()
            )
        info = res_list.filter(User_Id=uid)
        data = self.serializer_class(info, many=True).data
        return Response(
            response.Content().ok('SUC', data).raw()
        )
