import datetime

from rest_framework.response import Response

from data import models
from utils import response


def get_model_template_info(modelreportid):
    info = models.AppModeltemplate.objects.filter(ModelReportId=modelreportid).first()
    if info:
        return info
    return models.AppModeltemplate()

def get_model_template_info_by_code(modelcode):
    info = models.AppModeltemplate.objects.filter(PageCode=modelcode).first()
    if info:
        return info
    return models.AppModeltemplate()