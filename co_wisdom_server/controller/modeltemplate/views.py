from rest_framework.decorators import action
from rest_framework.response import Response

from data import models, serializers, extension


class ModelTemplateViewSet(extension.ResponseViewSet):
    queryset = models.AppModeltemplate.objects.all()
    serializer_class = serializers.AppModeltemplateSerializer

    @action(methods=['post'], detail=False)
    def getpagedatafront(self, request):
        t = self.get_page_rows(request.data)
        return Response(
            self.page_data(t)
        )
