from django.urls import path, include, re_path
from django.conf.urls import url
from . import views
from rest_framework import routers
from .front.frontUrls import frontPath
from .easemob.urls import allPath as easemobUrls
from .vmodelreportreport.urls import allPath as vmodelUrls
from .company import views as company_views
from .projectcoach import views as projectcoach_views
from .member import views as member_views
from .project import views as project_views
from .projectsettings import views as projectsettings_views
from .projectsettingcoach import views as projectsettingcoach_views
from .projectsettingcoachneed import views as projectsettingcoachneed_views
from .projectsettingadmin import views as projectsettingadmin_views
from .projectcoachinterview import views as projectcoachinterview_views
from .projectinterview import views as projectinterview_views
from .projectrelation import views as projectrelation_views
from .projectreport import views as projectreport_views
from .projectuploadreport import views as projectuploadreport_views
from .projectexam import views as projectexam_views
from .coach import views as coach_views
from .coachrate import views as coachrate_views
from .coachschedule import views as coachschedule_views
from .growthdiary import views as growthdiary_views
from .useronline import views as useronline_views
from .front import views as front_views
from .message import views as message_views
from .user import views as user_views
from .evaluation import views as evaluation_views
from .evaluationresult import views as evaluationresult_views
from .evaluationreport import views as evaluationreport_views
from .modelreportreport import views as modelreportreport_views
from .modelreportresult import views as modelreportresult_views
from .modeltemplate import views as modeltemplate_views
from .projectlearningaction import views as learningaction_views
from .resource import views as resource_views
from .resourcecategory import views as resourcecategory_views
from .dictionary import views as dictionary_views
from .articlecategory import views as articlecategory_views
from .article import views as article_views
from .feedback import views as feedback_views
from .messagetemplate import views as messagetemplate_views
from .evaluationquestion import views as evaluationquestion_views
from .ildp import views as ildp_views
from .projectexamrelationuser import views as examrelationuser_views
from .im import views as im_views
from .hxmeetingdata import views as hxmeetingdata_views
from .CacheTmpResult import views as cachetmpresult_views
from .projectsatisfaction import views as satisfaction_views
from .projectneed import views as need_views
from .uniapp.views import UniAppView
from rest_framework_simplejwt.views import TokenObtainPairView, TokenRefreshView

router = routers.DefaultRouter(trailing_slash=False)
# router.register(r'company', viewset=views.CompanyViewSet, basename='company')
# router.register(r'team', viewset=views.TeamViewSet, basename='team')
# router.register(r'probject', viewset=views.ProjectViewSet, basename='project')






router.register(r'articlecategory', viewset=articlecategory_views.ArticleCategoryViewSet)
router.register(r'article', viewset=article_views.ArticleViewSet)

router.register(r'coach', viewset=coach_views.AppCoachViewSet)
router.register(r'CoachSchedule', viewset=coachschedule_views.CoachScheduleViewSet)
router.register(r'coachrate', viewset=coachrate_views.CoachRateViewSet)
router.register(r'company', viewset=company_views.CompanyViewSet, basename='company')
router.register(r'CacheTmpResult', viewset=cachetmpresult_views.CacheTmpResultViewSet)

router.register(r'Dictionary', viewset=dictionary_views.DictionaryViewSet)


router.register(r'evaluation', viewset=evaluation_views.EvaluationViewSet)
router.register(r'evalutionreport', viewset=evaluationreport_views.EvaluationReportViewSet)
router.register(r'evaluationresult', viewset=evaluationresult_views.EvaluationResultViewSet)
router.register(r'evaluationquestion', viewset=evaluationquestion_views.EvaluationQuestionViewSet)

router.register(r'front', viewset=front_views.FrontViewSet)
router.register(r'feedback', viewset=feedback_views.FeedbackViewSet)

router.register(r'growthdiary', viewset=growthdiary_views.GrowthDiaryViewSet)

router.register(r'HxMeetingData', viewset=hxmeetingdata_views.HXMeetingDataViewSet)

router.register(r'ildp', viewset=ildp_views.ILDPViewSet)

router.register(r'im', viewset=im_views.APPIMViewSet)

router.register(r'member', viewset=member_views.AppMemberViewSet, basename='member')
router.register(r'Message', viewset=message_views.AppMessageViewSet)
router.register(r'messagetemplates', viewset=messagetemplate_views.MessageTemplateViewSet)
router.register(r'modelreportresult', viewset=modelreportresult_views.ModelReportResultViewSet)
router.register(r'modelreportreport', viewset=modelreportreport_views.ModelReportReportViewSet)
router.register(r'modeltemplate', viewset=modeltemplate_views.ModelTemplateViewSet)


router.register(r'probject', viewset=project_views.ProjectViewSet)
router.register(r'probjectrelation', viewset=projectrelation_views.ProjectRelationViewSet)
router.register(r'ProbjectInterview', viewset=projectinterview_views.ProjectInterviewViewSet)
router.register(r'ProbjectReport', viewset=projectreport_views.ProjectReportViewSet)
router.register(r'ProbjectUploadReport', viewset=projectuploadreport_views.ProjectUploadReportViewSet)
router.register(r'ProbjectCoach', viewset=projectcoach_views.ProjectCoachViewSet)
router.register(r'ProbjectCoachinterview', viewset=projectcoachinterview_views.ProjectCoachInterviewViewSet)


router.register(r'probjectexam', viewset=projectexam_views.ProjectExamViewSet)
router.register(r'ProbjectExamRelationUser', viewset=examrelationuser_views.ProjectExamRelationUserViewSet)

router.register(r'ProbjectNeed', viewset=need_views.ProjectNeedViewSet)

router.register(r'ProbjectSatisfaction', viewset=satisfaction_views.ProjectSatisfactionViewSet)

router.register(r'probjectsetting', viewset=projectsettings_views.ProjectSettingViewSet)
router.register(r'probjectsettingcoach', viewset=projectsettingcoach_views.ProjectSettingCoachViewSet)
router.register(r'probjectsettingcoachneed', viewset=projectsettingcoachneed_views.ProjectSettingCoachNeedViewSet)
router.register(r'projectsettingadmin', viewset=projectsettingadmin_views.ProjectSettingAdminViewSet)

router.register(r'ProbjectLearningAction', viewset=learningaction_views.ProjectLearningActionViewSet)

router.register(r'resources', viewset=resource_views.ResourceViewSet)
router.register(r'rescategories', viewset=resourcecategory_views.ResourceCategoryViewSet)

router.register(r'user', viewset=user_views.SysUserViewSet)
router.register(r'UserOnline', viewset=useronline_views.AppUserOnlineViewSet)


urlpatterns = [
    re_path(r'^', include(router.urls)),
    path('front/', include(frontPath)),
    path('easemob/', include(easemobUrls)),
    path('vmodelreportreport/', include(vmodelUrls)),
    path('log', views.Log.as_view()),
    url(r'^Unipp/home$', UniAppView.as_view(), name='uniapp_home'),
    # path('user/', include('controller.account.urls')),
    # path('token/', TokenObtainPairView.as_view(), name='token_obtain_pair'),
    # path('token/refresh/', TokenRefreshView.as_view(), name='token_refresh'),
]
