import datetime
import json

from django.db.models import Q
from rest_framework.decorators import action
from rest_framework.response import Response

from data import models, serializers, extension
from utils import response, int_arg
from utils.messagecenter.center import push_message
from utils.messagecenter.actions import get_notice_to_coach_link, get_notice_to_student_link

class APPIMViewSet(extension.ResponseViewSet):
    queryset = models.AppIm.objects.all()
    serializer_class = serializers.AppImSerializer

    @action(methods=['post'], detail=False)
    def getpagedatafront(self, request):
        projectid = 0
        fromuserid = 0
        touserid = 0
        date = datetime.datetime.now()
        for where in json.loads(request.data["wheres"]):
            if where['name'] == 'probjectid':
                projectid = int_arg(where['value'])
            elif where['name'] == 'fromuserid':
                fromuserid = int_arg(where['value'])
            elif where['name'] == 'touserid':
                touserid = int_arg(where['value'])
            elif where['name'] == 'CreateDate':
                if where['value']:
                    date = where['value']
        t = models.AppIm.objects.filter(
            (Q(FromUserId=fromuserid) & Q(ToUserId=touserid)) | (Q(ToUserId=fromuserid) & Q(FromUserId=touserid)),
            ProbjectId=projectid, CreateDate__lte=date).order_by('-MsgId')
        if t.count() > 0:
            extra = []
            for item in t.all():
                dic = {}
                dic["eMsgId"] = item.MsgId
                fromuser = models.SysUser.objects.filter(User_Id=item.FromUserId).first()
                if fromuser:
                    dic["fromusername"] = fromuser.UserTrueName
                    dic["fromuserimg"] = fromuser.HeadImageUrl
                    dic["fromuserhxid"] = fromuser.MessageId
                touser = models.SysUser.objects.filter(User_Id=item.ToUserId).first()
                if touser:
                    dic["tousername"] = touser.UserTrueName
                    dic["touserimg"] = touser.HeadImageUrl
                    dic["touserhxid"] = touser.MessageId
                extra.append(dic)
            t.extra_list = extra
        return Response(
            self.page_data(t)
        )

    @action(methods=['post'], detail=False)
    def getpagedata(self, request):
        t = self.get_page_rows(request.data)
        if t.count() > 0:
            extra = []
            for item in t.all():
                dic = {}
                dic["eMsgId"] = item.MsgId
                fromuser = models.SysUser.objects.filter(User_Id=item.FromUserId).first()
                if fromuser:
                    dic["fromusername"] = fromuser.UserTrueName
                    dic["fromuserimg"] = fromuser.HeadImageUrl
                    dic["fromuserhxid"] = fromuser.MessageId
                touser = models.SysUser.objects.filter(User_Id=item.ToUserId).first()
                if touser:
                    dic["tousername"] = touser.UserTrueName
                    dic["touserimg"] = touser.HeadImageUrl
                    dic["touserhxid"] = touser.MessageId
                extra.append(dic)
            t.extra_list = extra
        return Response(
            self.page_data(t)
        )

    @action(methods=['post'], detail=False)
    def txnotice(self, request):
        fromuid = int_arg(request.query_params.get('fromuid'), 0)
        touid = int_arg(request.query_params.get('touid'), 0)
        msg = request.query_params.get('msg', '')
        finfo = models.SysUser.objects.filter(User_Id=fromuid).first()
        tinfo = models.SysUser.objects.filter(User_Id=touid).first()
        if msg and finfo and tinfo:
            msg = msg.replace('fromname', finfo.UserTrueName).replace('toname', tinfo.UserTrueName)
        else:
            return Response(
                response.Content().error('未找到用户信息').raw()
            )
        # 被教练者在AppMember里去company
        # 教练需要在Projectcoach里取到pid去拿
        if not finfo.Role_Id == 4:
            company = models.AppCompany.objects.get(pk=models.AppMember.objects.get(User_Id=finfo.pk).Company_Id).CompanyName
        else:
            company = None
        notice_to_coach = 'notice_to_coach'
        notice_to_student = 'notice_to_student'
        dic = {}
        dic["fromname"] = finfo.UserTrueName
        dic["msg"] = msg
        dic["company"] = company
        dic["time"] = datetime.datetime.now().strftime('%m月%d日 %H-%M')
        dic["path_link"] = '-'

        project_id = None
        userid = finfo.pk if tinfo.Role_Id == 4 else tinfo.pk
        project_relation = models.AppProbjectrelation.objects.filter(UserId=userid)
        if project_relation:
            project_id = project_relation.first().ProbjectId
        # 发送给教练
        if tinfo.Role_Id == 4:
            dic["path_link"] = get_notice_to_coach_link(finfo.pk, project_id)
            push_message.delay(tinfo, notice_to_coach, dic, project_id=project_id)
        elif tinfo.Role_Id == 6:
            dic["path_link"] = get_notice_to_student_link()
            push_message.delay(tinfo, notice_to_student, dic, project_id=project_id)

        return Response(
            response.Content().ok().raw()
        )
