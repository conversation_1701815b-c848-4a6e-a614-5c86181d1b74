import random

from rest_framework.decorators import action
from rest_framework.response import Response

from controller.survey.actions import get_survey_view
from controller.surveysubject.actions import get_survey_subject_detail
from data import models, serializers, extension
from utils import response, int_arg, null_or_blank


class SurveyViewSet(extension.ResponseViewSet):
    queryset = models.AppSurvey.objects.all()
    serializer_class = serializers.AppSurveySerializer

    @action(methods=['post'], detail=False)
    def add(self, request):
        main_data = request.data.get('mainData')
        pagecode = main_data.get('PageCode')
        if null_or_blank(pagecode):
            return Response(
                response.Content().error('请输入调用标识').raw()
            )
        if models.AppSurvey.objects.filter(PageCode=pagecode).exists():
            return Response(
                response.Content().error('重复的调用标识').raw()
            )
        res = self.add_row(main_data)
        return Response(
            res.raw()
        )

    @action(methods=['post'], detail=False, url_path='update')
    def update_new(self, request):
        main_data = request.data.get('mainData')
        pagecode = main_data.get('PageCode')
        if null_or_blank(pagecode):
            return Response(
                response.Content().error('请输入调用标识').raw()
            )
        survey = models.AppSurvey.objects.filter(PageCode=pagecode).first()
        SurveyId = int_arg(main_data.get('SurveyId'))
        if survey and survey.SurveyId != SurveyId:
            return Response(
                response.Content().error('重复的调用标识').raw()
            )
        res = self.update_row(main_data)
        return Response(
            res.raw()
        )

    @action(methods=['get'], detail=False)
    def getsurveyview(self, request):
        pici = request.query_params.get('pici', '')
        surveyid = int_arg(request.query_params.get('surveyid'))
        userid = int_arg(request.query_params.get('userid'))
        res = get_survey_view(surveyid, userid, pici)
        return Response(
            res.ok('suc').raw()
        )

    @action(methods=['post'], detail=False)
    def copysurvey(self, request):
        main_data = request.data.get('mainData')
        surveyid = int_arg(main_data.get('surveyid'))
        survey = models.AppSurvey.objects.filter(SurveyId=surveyid).first()
        survey_child = get_survey_subject_detail(surveyid)
        survey.pk = None
        survey.Title = survey.Title + '_副本'
        survey.PageCode = survey.PageCode + '_' + str(random.randint(1000, 9999))
        survey.save()
        for item in survey_child.all():
            item.SurveyId = survey.SurveyId
            subid = item.SubId
            item.SubId = None
            item.save()
            options = models.AppSurveyoption.objects.filter(SubId=subid)
            for option in options.all():
                option.pk = None
                option.save()
        return Response(
            response.Content().ok().raw()
        )
