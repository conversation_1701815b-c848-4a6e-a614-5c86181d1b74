from controller.surveysubject.actions import get_survey_subject_detail
from data import models, serializers
from utils import null_or_blank, response


def get_survey_info(surveyid):
    survey = models.AppSurvey.objects.filter(SurveyId=surveyid).first()
    if survey:
        return survey
    return models.AppSurvey()


def get_survey_view(surveyid, userid, pici=''):
    survey = models.AppSurvey.objects.filter(SurveyId=surveyid).first()
    survey_child = get_survey_subject_detail(surveyid)
    result = []
    if userid > 0 and not null_or_blank(pici):
        for item in survey_child.all():
            answer = models.AppSurveyanswers(App_SurveyId=surveyid, UserId=userid, SubjectId=item.SubId, pici=pici)
            if answer.count() > 0:
                result.append({
                    'answer': answer.first().Answers,
                    'other': answer.first().Ip
                })
            else:
                result.append({
                    'answer': '',
                    'other': ''
                })
    data = {
        'info': {
            'SurveyId': survey.SurveyId,
            'Title': survey.Title
        },
        'list': serializers.AppSurveysubjectSerializer(survey_child, many=True).data,
        'result': result
    }
    res = response.Content()
    res.data = data
    return res
