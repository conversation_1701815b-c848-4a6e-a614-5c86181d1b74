import datetime
import json
import random
from  django.conf import settings
from rest_framework.decorators import action
from rest_framework.response import Response

from controller.modelreportresult.actions import analysR45, analysR413, save_report, update_report_status
from controller.modeltemplate.actions import get_model_template_info_by_code, get_model_template_info
from controller.project.actions import get_project_info
from data import models, serializers, extension
from utils import response, int_arg, value_or_default, null_or_blank, enter_to_br
from utils.model import add_option
from utils.user import get_user_ids
from utils.messagecenter.center import push_message
from utils.messagecenter.actions import get_end_report_r24_link, get_end_report_link, get_exam_r112_coach_link,\
    get_exam_r45_end_to_student_link, get_exam_r45_end_to_manage_link, get_exam_r45_end_to_coach_link, \
    get_exam_r45_end_to_project_manage_link, get_exam_end_to_manage_padmin, get_exam_end_to_manage_cadmin,\
    get_r421_link_to_student, get_r421_link_to_cadmin, get_r421_link_to_padmin

from .actions import get_report_pici


class ModelReportResultViewSet(extension.ResponseViewSet):
    queryset = models.AppModelreportresult.objects.all()
    serializer_class = serializers.AppModelreportresultSerializer

    @action(methods=['post'], detail=False)
    def modelreportsubmit(self, request):
        main_data = request.data.get('mainData')
        ModelCode = main_data.get('ModelCode', '')
        interestedid = main_data.get('interestedid')
        if null_or_blank(ModelCode):
            return Response(
                response.Content().error('非法的报告提交').raw()
            )
        ModelReportId = get_model_template_info_by_code(ModelCode).ModelReportId
        main_data['ModelReportId'] = ModelReportId
        userid = int_arg(main_data.get('userid'), 0)
        dataid = int_arg(main_data.get('DataId'), 0)
        if userid == 0:
            userid = request.user.pk
            main_data['UserId'] = userid
        pici = main_data.get('pici', '')
        probjectid = int_arg(main_data.get('ProbjectId'), None)
        answer = main_data.get('Answer')
        if answer:
            answer_json = json.dumps(answer, ensure_ascii=False, default=str)
            main_data['Answer'] = answer_json
        umodel = models.AppModelreportresult.objects.filter(ProbjectId=probjectid, UserId=userid, ModelCode=ModelCode,
                                                            DataId=dataid, pici=pici).first()
        if umodel:
            umodel.Answer = main_data.get('Answer')
            umodel.save()
            update_report_status(umodel, True, interestedid)
            return Response(
                response.Content().ok().raw()
            )
        # else:
        #     return Response(response.Content().error('请勿重复提交利益相关者调研').raw())
        if null_or_blank(pici):
            main_data['pici'] = datetime.datetime.now().strftime('%Y%m%d%H%M%S') + str(get_report_pici())
        res = self.add_row(main_data)
        if res.status:
            update_report_status(res.row, interestedid=interestedid)
            try:
                # 利益相关者调研有传Dataid
                # row_obj.row.ModelCode
                if res.row.ModelCode == 'r24':
                    # 全部完成汇总报告提醒教练(10)
                    end_report = "end_report"
                    # 部分完成提醒教练(9)
                    end_report_r24 = "end_report_r24"
                    try:
                        coach = models.SysUser.objects.get(pk=models.Projectcoach.objects.get(ProbjectId=probjectid,
                                                                                              UserId=dataid).CoachId)
                    except models.Projectcoach.DoesNotExist:
                        coach = models.SysUser.objects.get(pk=models.AppProbjectrelation.objects.get(ProbjectId=probjectid,
                                                                                                     UserId=dataid,
                                                                                                     RolesId=6).CoachId)
                    student = models.SysUser.objects.get(pk=res.row.UserId)
                    interest = models.SysUser.objects.get(pk=res.row.DataId)
                    company = models.AppCompany.objects.get(pk=models.AppProbjectrelation.objects.get(ProbjectId=probjectid,
                                                                                                      UserId=dataid,
                                                                                                      RolesId=6).CompanyId)
                    report = models.AppModeltemplate.objects.get(pk=res.row.ModelReportId)
                    exam_status = list(models.AppProbjectexamrelationuser.objects.filter(PageCode='r24',
                                                                                         ProbjectId=probjectid,
                                                                                         interested_id=dataid
                                                                                         ).values_list('Status', flat=True))
                    dic = {
                        "studentname": student.UserTrueName,
                        "interestname": interest.UserTrueName,
                        "company": company.CompanyName,
                        "report": report.ModelName,
                        "total_count": len(exam_status),
                        "status_0": exam_status.count(0),
                        "status_1": exam_status.count(1),
                        "path_link": "-"
                    }
                    if 0 in exam_status:
                        dic['path_link'] = get_end_report_r24_link(probjectid, dataid)
                        push_message.delay(coach, end_report_r24, dic, probjectid)
                    else:
                        dic['path_link'] = get_end_report_link(probjectid, res.row.pici, dataid)
                        push_message.delay(coach, end_report, dic, probjectid)
                # 被教练者完成教练准备度调研后通知教练查看（4）
                if res.row.ModelCode == 'r112':
                    exam_r112_coach = 'exam_r112_coach'
                    try:
                        coach = models.SysUser.objects.get(pk=models.Projectcoach.objects.get(ProbjectId=probjectid,
                                                                                              UserId=userid).CoachId)
                    except models.Projectcoach.DoesNotExist:
                        coach = models.SysUser.objects.get(pk=models.AppProbjectrelation.objects.get(ProbjectId=probjectid,
                                                                                                     UserId=userid,
                                                                                                     RolesId=6).CoachId)
                    student = models.SysUser.objects.get(pk=userid)
                    company = models.AppCompany.objects.get(pk=models.AppProbjectrelation.objects.get(ProbjectId=probjectid,
                                                                                                      UserId=userid,
                                                                                                      RolesId=6).CompanyId)
                    report = models.AppModeltemplate.objects.get(pk=res.row.ModelReportId)

                    dic = {
                        "studentname": student.UserTrueName,
                        "company": company.CompanyName,
                        "report": report.ModelName,
                        "path_link": get_exam_r112_coach_link(probjectid, res.row.pici)
                    }
                    push_message.delay(coach, exam_r112_coach, dic, probjectid)

                    project = models.AppProbject.objects.get(pk=probjectid)

                    # 企业管理员可能有多个 IsManger project_id
                    user_ids = models.AppProbjectrelation.objects.filter(ProbjectId=project.pk,
                                                                         IsManger=True).values_list('UserId', flat=True)

                    company_user = models.SysUser.objects.filter(pk__in=user_ids)
                    dic = {
                        "studentname": student.UserTrueName,
                        "evaluation_name": report.ModelName,
                        "path_link": get_exam_end_to_manage_cadmin(project.pk, res.row.pici)
                    }
                    if company_user.exists:
                        for user in company_user:
                            push_message.delay(user, "exam_end_to_manage", dic, project.pk)

                        # 群智管理员就是项目管理员 AppProject: Relation_UserId
                        project_manage_user = models.SysUser.objects.get(pk=project.Relation_UserId)
                        dic['path_link'] = get_exam_end_to_manage_padmin(project.pk, res.row.pici)
                        push_message.delay(project_manage_user, "exam_end_to_manage", dic, project.pk)
                # 改变观察
                if res.row.ModelCode == 'r45':
                    report = models.AppModeltemplate.objects.get(pk=res.row.ModelReportId)

                    # 所员生成报告
                    # 群智管理员就是项目管理员 AppProject: Relation_UserId
                    exam_r45_end_to_manage = 'exam_r45_end_to_manage'
                    exams = models.AppProbjectexamrelationuser.objects.filter(PageCode='r45', interested_id=dataid,
                                                                              ProbjectId=probjectid, Status=0)
                    # 可能是dataid
                    student = models.SysUser.objects.get(pk=dataid)

                    if not exams.exists():
                        company = models.AppCompany.objects.get(
                            pk=models.AppProbjectrelation.objects.filter(ProbjectId=probjectid,
                                                                         UserId=userid).first().CompanyId)
                        project_manage_user = models.SysUser.objects.get(
                            pk=models.AppProbject.objects.get(pk=probjectid).Relation_UserId)
                        exam_r45_end_to_manage_param = {
                            "company": company.CompanyName,
                            "student": student.UserTrueName,
                            "evaluation_name": report.ModelName,
                            "path_link": get_exam_r45_end_to_project_manage_link(probjectid, res.row.pici)

                        }
                        push_message.delay(project_manage_user, exam_r45_end_to_manage, exam_r45_end_to_manage_param,
                                           probjectid)
            except Exception as e:
                print(e)
        return Response(
            res.raw()
        )

    @action(methods=['post'], detail=False)
    def CreateR241(self, request):
        main_data = request.data.get('mainData')
        probjectid = int_arg(main_data.get('ProbjectId'), None)
        UserId = int_arg(main_data.get('UserId'), None)
        CoachId = int_arg(main_data.get('CoachId'), None)
        pici = main_data.get('pici', '')
        if null_or_blank(pici):
            pici = datetime.datetime.now().strftime('%Y%m%d%H%M%S') + str(random.randint(10, 99))
        utype = int_arg(main_data.get('utype'), None)
        result = models.AppModelreportresult()
        result.ProbjectId = probjectid
        result.ModelReportId = 3
        result.UserId = UserId
        result.pici = pici
        result.ModelCode = "r241"
        result.DataId = CoachId
        uinfo = models.SysUser.objects.filter(User_Id=UserId).first()
        cinfo = models.SysUser.objects.filter(User_Id=CoachId).first()
        a1 = enter_to_br(main_data.get('A1', ''))
        a2 = enter_to_br(main_data.get('A2', ''))
        a3 = enter_to_br(main_data.get('A3', ''))
        a4 = enter_to_br(main_data.get('A4', ''))
        a5 = enter_to_br(main_data.get('A5', ''))
        a6 = enter_to_br(main_data.get('A6', ''))
        _title = '利益相关者调研总结'
        if utype == 1:
            _title = '利益相关者访谈总结'

        interested_name_list = []
        if utype == 0:
            interested_name_list = models.SysUser.objects.filter(
                User_Id__in=models.AppModelreportresult.objects.filter(
                    ProbjectId=probjectid, ModelCode='r24',DataId=UserId).values_list('UserId', flat=True))\
                .values_list('UserTrueName', flat=True)
        else:
            # 从利益相关约谈信息中获取利益相关者的用户名
            interested_name_list = models.SysUser.objects.filter(
                User_Id__in=models.AppProbjectinterview.objects.filter(
                    ProbjectId=probjectid, Coach_Id=CoachId, interviewType=1, masteruserid=UserId, pici__isnull=False)
                    .values_list('User_Id', flat=True)) \
                .values_list('UserTrueName', flat=True)
        
        data = {
            'reportdate': datetime.datetime.now().strftime('%Y-%m-%d'),
            'title': _title,
            'truename': uinfo.UserTrueName,
            'coachname': cinfo.UserTrueName,
            'relation_list': list(interested_name_list),
            'a1': a1,
            'a2': a2,
            'a3': a3,
            'a4': a4,
            'a5': a5,
            'a6': a6,
        }

        end_report_to_manage = 'end_report_to_manage'
        dic = {'title': _title,
               'path_link': get_r421_link_to_student(probjectid, result.pici)}
        # 生成总结报告后通知被教练者查看（通用）（17）
        push_message.delay(uinfo, end_report_to_manage, dic, probjectid)

        # 生成总结报告后通知群智管理员\企业管理员查看 (18)
        exam_end_to_manage = 'exam_end_to_manage'

        project = models.AppProbject.objects.get(pk=probjectid)
        project_manage_user = models.SysUser.objects.get(pk=project.Relation_UserId)
        exam_end_param = {
            'studentname': uinfo.UserTrueName,
            'evaluation_name': _title,
            'path_link': get_r421_link_to_padmin(probjectid)
        }
        push_message.delay(project_manage_user, exam_end_to_manage, exam_end_param, probjectid)
        # 推送企业管理员 企业管理员可能有多个 IsManger project_id
        user_ids = models.AppProbjectrelation.objects.filter(ProbjectId=project.pk,
                                                             IsManger=True).values_list('UserId', flat=True)
        company_user = models.SysUser.objects.filter(pk__in=user_ids)
        if company_user.exists:
            for user in company_user:
                exam_end_param['path_link'] = get_r421_link_to_cadmin(probjectid, result.pici)
                push_message.delay(user, exam_end_to_manage, exam_end_param, probjectid)

        result.Answer = json.dumps(data, ensure_ascii=False, default=str)
        save_report(result, _title, '3,4,5,6', 'r241')
        data = {'pici': result.pici}
        return Response(
            response.Content().ok('SUC', data).raw()
        )

    @action(methods=['post'], detail=False)
    def CreateR413(self, request):
        main_data = request.data.get('mainData')
        probjectid = int_arg(request.query_params.get('probjectid'), None)
        type = int_arg(request.query_params.get('type'), 0)
        analysR413(probjectid, '', type)
        return Response(
            response.Content().ok('SUC').raw()
        )

    @action(methods=['post'], detail=False)
    def CreateR45(self, request):
        reportid = int_arg(request.query_params.get('reportid'), None)
        model = models.AppModelreportreport.objects.filter(Id=reportid).first()
        if model:
            try:
                analysR45(model)
            except Exception as e:
                return Response(
                    response.Content().error('生成报告失败').raw()
                )
            return Response(
                response.Content().ok('SUC').raw()
            )
        else:
            return Response(
                response.Content().error('报告的模型不存在').raw()
            )

    @action(methods=['post'], detail=False)
    def PushR45(self, request):
        reportid = int_arg(request.query_params.get('reportid'), None)
        model = models.AppModelreportreport.objects.filter(Id=reportid).first()
        if model:
            model.status = 1
            model.rolesid = "3,4,5,6"
            model.save()

            # r45 利益相关者提交改变观察（21， 22）
            # 通知被教练者
            end_report_to_manage = 'end_report_to_manage'
            student = models.SysUser.objects.get(pk=model.UserId)
            try:
                coach = models.SysUser.objects.get(pk=models.Projectcoach.objects.get(ProbjectId=model.ProbjectId,
                                                                                      UserId=model.UserId).CoachId)
            except models.Projectcoach.DoesNotExist:
                coach = models.SysUser.objects.get(pk=models.AppProbjectrelation.objects.get(ProbjectId=model.ProbjectId,
                                                                                             UserId=model.UserId,
                                                                                             RolesId=6).CoachId)
            dic = {
                "title": model.title,
                "path_link": get_exam_r45_end_to_student_link(model.ProbjectId, model.pici)

            }
            push_message.delay(student, end_report_to_manage, dic, model.ProbjectId)

            # 通知企业管理员查看 企业管理员可能有多个 IsManger project_id
            exam_end_to_manage = 'exam_end_to_manage'
            user_ids = models.AppProbjectrelation.objects.filter(ProbjectId=model.ProbjectId,
                                                                 IsManger=True).values_list('UserId', flat=True)
            company_user = models.SysUser.objects.filter(pk__in=user_ids)
            to_manage_params = {
                "evaluation_name": model.title,
                "studentname": student.UserTrueName,
                "path_link": get_exam_r45_end_to_manage_link(model.ProbjectId, model.pici)
            }
            if company_user.exists:
                for user in company_user:
                    push_message.delay(user, exam_end_to_manage, to_manage_params, model.ProbjectId)

            # 通知教练查看
            to_manage_params['path_link'] = get_exam_r45_end_to_coach_link(model.ProbjectId, model.pici)
            push_message.delay(coach, exam_end_to_manage, to_manage_params, model.ProbjectId)

            # # 所有测评人填写完后通知群智管理员生成报告
            # # 群智管理员就是项目管理员 AppProject: Relation_UserId
            # exam_r45_end_to_manage = 'exam_r45_end_to_manage'
            # exams = models.AppProbjectexamrelationuser.objects.filter(PageCode='r45', interested_id=model.UserId,
            #                                                           ProbjectId=model.ProbjectId, Status=0)
            # if not exams.exists():
            #     company = models.AppCompany.objects.get(pk=models.AppProbjectrelation.objects.get(ProbjectId=model.ProbjectId,
            #                                                                                       UserId=model.UserId).CompanyId)
            #     project_manage_user = models.SysUser.objects.get(
            #         pk=models.AppProbject.objects.get(pk=model.ProbjectId).Relation_UserId)
            #     exam_r45_end_to_manage_param = {
            #         "company": company.CompanyName,
            #         "student": student.UserTrueName,
            #         "evaluation_name": model.title,
            #         "path_link": get_exam_r45_end_to_project_manage_link(model.ProbjectId, model.pici)
            #
            #     }
            #     push_message.delay(project_manage_user, exam_r45_end_to_manage, exam_r45_end_to_manage_param, model.ProbjectId)
            return Response(
                response.Content().ok('').raw()
            )
        else:
            return Response(
                response.Content().error('报告的模型不存在').raw()
            )

    @action(methods=['post'], detail=False)
    def getpagedata(self, request):
        for where in json.loads(request.data["wheres"]):
            if where['name'] == 'UserName':
                ids = get_user_ids(where['value'])
                if len(ids) > 0:
                    add_option(request.data, 'UserId', ids, 'checkbox')
            elif where['name'] == 'ModelName':
                ids = models.AppModeltemplate.objects.filter(Enable=1, ModelName__icontains=where['value']).values_list(
                    'ModelReportId', flat=True)
                if len(ids) > 0:
                    add_option(request.data, 'ModelReportId', ids, 'checkbox')
            elif where['name'] == 'ProName':
                ids = models.AppProbject.objects.filter(Enable=1, Name__icontains=where['value']).values_list(
                    'Probject_Id', flat=True)
                if len(ids) > 0:
                    add_option(request.data, 'ProbjectId', ids, 'checkbox')
            else:
                add_option(request.data, where['name'], where['value'], where['displayType'])
        t = self.get_page_rows(request.data)
        if t.count() > 0:
            extra = []
            for item in t.all():
                dic = {}
                dic["eId"] = item.Id
                modelinfo = get_model_template_info(value_or_default(item.ModelReportId, 0))
                dic["ModelName"] = modelinfo.ModelName
                dic["ModelCode"] = modelinfo.PageCode
                dic["ProName"] = get_project_info(item.ProbjectId).Name
                username = models.SysUser.objects.filter(User_Id=item.UserId).first().UserName
                dic["UserName"] = username
                extra.append(dic)
            t.extra_list = extra
        return Response(
            self.page_data(t)
        )

    @action(methods=['get'], detail=False)
    def getreportmodel(self, request):
        userid = int_arg(request.query_params.get('userid'), None)
        modelcode = request.query_params.get('modelcode')
        pici = request.query_params.get('pici', '')
        eva = models.AppModeltemplate.objects.filter(PageCode=modelcode).first()
        modelresult = ''
        if not null_or_blank(pici):
            result = models.AppModelreportresult.objects.filter(pici=pici).first()
            if result:
                modelresult = result.Answer
        result = {}
        if not null_or_blank(modelresult):
            result = json.loads(modelresult)
        data = {
            'info': {
                'id': eva.ModelReportId,
                'title': eva.ModelName,
                'code': eva.PageCode
            },
            'data': eva.Template,
            'example': eva.Example,
            'result': result
        }
        return Response(
            response.Content().ok('suc', data).raw()
        )
