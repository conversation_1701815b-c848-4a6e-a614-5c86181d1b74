import datetime
import json
import redis

from django.conf import settings
from django.db.models import <PERSON>, Sum, Q, Count
from rest_framework.response import Response


from controller.dictionary.actions import get_dictionary_list_value
from controller.ildp.actions import get_my_project_objectives
from controller.project.actions import get_project_info
from controller.projectinterview.actions import get_interview_times, update_times, get_subject_info
from controller.projectreport.views import get_two_number
from data import models
from utils import value_or_default, int_arg, null_or_blank
from utils.business import new_pici


def update_report_status(result, update=False, interestedid=None):
    dataid = result.DataId
    ModelCode = result.ModelCode
    if ModelCode == 'r17':  # 企业面试后的教练报告
        pin = models.ProjectCoachInterview.objects.filter(ProbjectId=result.ProbjectId, CoachId=result.UserId,
                                                          UserId=result.DataId).first()
        if pin:
            pin.qypici = result.pici
            pin.save()
        result.DateId = result.UserId
        result.UserId = value_or_default(dataid)
        save_report(result, '企业面试后的教练报告', '3,4')
    elif ModelCode == 'r24':  # 利益相关者调研问卷
        result.DataId = result.UserId
        result.UserId = value_or_default(dataid)
        rid24 = save_report(result, '利益相关者调研问卷', '3,4')
        if result.ProbjectId > 0:
            evuinfo = models.AppProbjectexamrelationuser.objects.filter(ProbjectId=result.ProbjectId,
                                                                        UserId=result.DataId,
                                                                        interested_id=result.UserId,
                                                                        ExId=result.ModelReportId, TypeId=2,
                                                                        Status=0).first()
            if evuinfo:
                evuinfo.pici = result.pici
                evuinfo.ReportId = rid24
                evuinfo.Status = 1
                evuinfo.CompleteDate = datetime.datetime.now()
                evuinfo.save()
    elif ModelCode == 'r25':  # 利益相关者约谈记录
        if result.ProbjectId > 0:
            pinfo = models.AppProbjectinterview.objects.filter(ProbjectId=result.ProbjectId, Coach_Id=result.UserId,
                                                                 User_Id=result.DataId, interviewType=1,
                                                                 status=1, masteruserid=interestedid).first()
            if pinfo:
                update_times(pinfo)
                pinfo.pici = result.pici
                pinfo.save()
        result.DateId = result.UserId
        result.UserId = value_or_default(dataid)
        save_report(result, '利益相关者调研问卷 教练的总结报告', '0')
    elif ModelCode == 'r41':  # 被教人约谈记录
        pass
    elif ModelCode == 'r42':  # 教练约谈记录
        pass
    elif ModelCode == 'r45':  # 改变观察 小型调研问卷
        if result.ProbjectId > 0:
            evuinfo = models.AppProbjectexamrelationuser.objects.filter(ProbjectId=result.ProbjectId,
                                                                        UserId=result.UserId,
                                                                        interested_id=result.DataId,
                                                                        ExId=result.ModelReportId,
                                                                        TypeId=2, Status=0).first()
            if evuinfo:
                evuinfo.pici = result.pici
                evuinfo.ReportId = 0
                evuinfo.Status = 1
                evuinfo.CompleteDate = datetime.datetime.now()
                evuinfo.save()
    elif ModelCode == 'r53':  # 最后一次约谈后 客户总结报告
        analysR53(result)
        if result.ProbjectId > 0:
            evuinfo = models.AppProbjectinterview.objects.filter(ProbjectId=result.ProbjectId, Coach_Id=result.DataId,
                                                                 User_Id=result.UserId, status=1, Times=None,
                                                                 Satisfaction=None).first()
            if evuinfo:
                evuinfo.pici = result.pici
                evuinfo.Satisfaction = 10
                evuinfo.inputSatisfied = 10
                evuinfo.growupSatisfied = 10
                evuinfo.save()
    elif ModelCode == 'r54':  # 最后一次约谈后 教练总结报告
        analysR54(result)
        if result.ProbjectId > 0:
            evuinfo = models.AppProbjectinterview.objects.filter(ProbjectId=result.ProbjectId, Coach_Id=result.UserId,
                                                                 User_Id=result.DataId, status=1, Times=None,
                                                                 Satisfaction=None).first()
            if evuinfo:
                update_times(evuinfo)
                evuinfo.pici = result.pici
                evuinfo.save()
    elif ModelCode == 'r55':  # 最后一次约谈后 综合报告
        pass
    elif ModelCode == 'r61':  # 教练满意度调查
        rid6 = save_report(result, '教练满意度调查', '3')
        if result.ProbjectId > 0:
            evuinfo = models.AppProbjectexamrelationuser.objects.filter(ProbjectId=result.ProbjectId,
                                                                        UserId=result.UserId, ExId=result.ModelReportId,
                                                                        TypeId=2, Status=0).first()
            if evuinfo:
                evuinfo.pici = result.pici
                evuinfo.ReportId = rid6
                evuinfo.Status = 1
                evuinfo.CompleteDate = datetime.datetime.now()
                evuinfo.save()
    elif ModelCode == 'r62':  # 企业管理员满意度调查
        ridx = save_report(result, '企业管理员满意度调查', '3,5')
        if result.ProbjectId > 0:
            evuinfo = models.AppProbjectexamrelationuser.objects.filter(ProbjectId=result.ProbjectId,
                                                                        UserId=result.UserId, ExId=result.ModelReportId,
                                                                        TypeId=2, Status=0).first()
            if evuinfo:
                evuinfo.pici = result.pici
                evuinfo.ReportId = ridx
                evuinfo.Status = 1
                evuinfo.CompleteDate = datetime.datetime.now()
                evuinfo.save()
    elif ModelCode == 'r63':  # 项目总结统计报告
        pass
    elif ModelCode == 'r111':  # 化学面谈后的教练报告
        pinx = models.ProjectCoachInterview.objects.filter(ProbjectId=result.ProbjectId, CoachId=result.UserId,
                                                           StudentUserId=result.DataId).first()
        if pinx:
            pinx.hxpici = result.pici
            pinx.save()
        save_report(result, '化学面谈后的教练报告', '3,4')
    elif ModelCode == 'r112':  # 被教练准备度的问卷
        rid = analysR112(result)
        if result.ProbjectId > 0:
            evuinfo = models.AppProbjectexamrelationuser.objects.filter(ProbjectId=result.ProbjectId,
                                                                        UserId=result.UserId, ExId=result.ModelReportId,
                                                                        TypeId=2, Status=0).first()
            if evuinfo:
                evuinfo.pici = result.pici
                evuinfo.ReportId = rid
                evuinfo.Status = 1
                evuinfo.CompleteDate = datetime.datetime.now()
                evuinfo.save()
    elif ModelCode == 'r213':  # CORE测评问卷
        rid = analysR213(result)
        if result.ProbjectId > 0:
            evuinfo = models.AppProbjectexamrelationuser.objects.filter(ProbjectId=result.ProbjectId,
                                                                        UserId=result.UserId, ExId=result.ModelReportId,
                                                                        TypeId=2, Status=0).first()
            if evuinfo:
                evuinfo.pici = result.pici
                evuinfo.ReportId = rid
                evuinfo.Status = 1
                evuinfo.CompleteDate = datetime.datetime.now()
                evuinfo.save()
    elif ModelCode == 'r313':  # 第一次三方会谈后的ILDP 项目目标
        if update:
            rinfo = models.AppModelreportreport.objects.filter(ProbjectId=result.ProbjectId, ModelCode=result.ModelCode,
                                                               pici=result.pici).first()
            if rinfo:
                rinfo.reportjson = result.Answer
                rinfo.save()
    elif ModelCode == 'r410':  # 中期阶段性总结 客户记录
        rid = analysR410(result)
        if result.ProbjectId > 0:
            evuinfo = models.AppProbjectexamrelationuser.objects.filter(ProbjectId=result.ProbjectId,
                                                                        UserId=result.UserId, ExId=result.ModelReportId,
                                                                        TypeId=2, Status=0).first()
            if evuinfo:
                evuinfo.pici = result.pici
                evuinfo.ReportId = rid
                evuinfo.Status = 1
                evuinfo.CompleteDate = datetime.datetime.now()
                evuinfo.save()
    elif ModelCode == 'r411':  # 中期阶段性总结 教练记录
        rid = analysR411(result)
        if result.ProbjectId > 0:
            evuinfo = models.AppProbjectexamrelationuser.objects.filter(ProbjectId=result.ProbjectId,
                                                                        UserId=result.UserId, ExId=result.ModelReportId,
                                                                        TypeId=2, Status=0).first()
            if evuinfo:
                evuinfo.pici = result.pici
                evuinfo.ReportId = rid
                evuinfo.Status = 1
                evuinfo.CompleteDate = datetime.datetime.now()
                evuinfo.save()


def save_report(result, title, rolesid, reportcode='', status=1, tindex=1):
    mreport = models.AppModelreportreport()
    mreport.ProbjectId = result.ProbjectId
    mreport.ModelReportId = result.ModelReportId
    mreport.UserId = result.UserId
    mreport.reportjson = result.Answer
    mreport.status = status
    mreport.pici = result.pici
    mreport.title = title
    mreport.rolesid = rolesid
    mreport.ModelCode = result.ModelCode
    mreport.ReportCode = reportcode
    mreport.CoachId = result.DataId
    mreport.tindex = tindex
    mreport.CreateDate = datetime.datetime.now().replace(microsecond=0)
    mreport.ModifyDate = datetime.datetime.now().replace(microsecond=0)
    mreport.save()
    return mreport.Id


def analysR112(result):
    strjson = json.loads(result.Answer)
    v1 = int_arg(strjson.get('A1'), 0) + int_arg(strjson.get('A2'), 0) + int_arg(strjson.get('A3'), 0) + int_arg(
        strjson.get('A4'), 0) + int_arg(strjson.get('A5'), 0) + int_arg(strjson.get('A6'), 0)
    v2x = round((int_arg(strjson.get('A10'), 0) + int_arg(strjson.get('A11'), 0)) / 2, 2)
    v2y = round((int_arg(strjson.get('A7'), 0) + int_arg(strjson.get('A8'), 0) + int_arg(strjson.get('A9'), 0)) / 3, 2)
    v3x = round((int_arg(strjson.get('A13'), 0) + int_arg(strjson.get('A16'), 0)) / 2, 2)
    v3y = round((int_arg(strjson.get('A12'), 0) + int_arg(strjson.get('A15'), 0)) / 2, 2)
    v4 = round((int_arg(strjson.get('A17'), 0) + int_arg(strjson.get('A18'), 0)) / 2, 2)
    v5 = round((int_arg(strjson.get('A19'), 0) + int_arg(strjson.get('A20'), 0)) / 2, 2)
    data = {
        'reportdate': datetime.datetime.now().strftime('%Y-%m-%d'),
        'userid': int_arg(strjson.get('userid'), 0),
        'truename': strjson.get('truename', ''),
        'sex': strjson.get('sex', ''),
        'companyname': strjson.get('companyname', ''),
        'duty': strjson.get('duty', ''),
        'v1': v1,
        'v2': {
            'x': v2x,
            'y': v2y
        },
        'v3': {
            'x': v3x,
            'y': v3y
        },
        'v4': v4,
        'v5': v5,
        'v6': {
            'val': strjson.get('A14', '[]'),
            'other': strjson.get('A14other', ''),
        },
        'v7': {
            'val': strjson.get('A21', '[]'),
            'other': strjson.get('A21other', ''),
        },
    }
    result.Answer = json.dumps(data, ensure_ascii=False, default=str)
    id = save_report(result, "教练准备调研报告", "3,4,5,6", result.ModelCode)
    return id


def valueFunc(e):
    return int(e['Key'][1:])


def analysR213(result):
    strjson = json.loads(result.Answer)
    # 联结 1 5 9  13 17
    #             //环境 2 6 10 14 18
    #             //敞开 3 7 11 15 19
    #             //角色 4 8 12 16 20
    top3 = []
    lower3 = []
    title = strjson.get('titles', [])
    _list = []
    for k, v in strjson.items():
        if k.startswith('A'):
            _list.append({
                'Key': k,
                'Val': int(v)
            })
    _list.sort(key=valueFunc, reverse=True)
    i = 0
    for kvp in _list:
        i += 1
        if i > 3:
            break
        _inx = int(kvp.get('Key').replace('A', '')) - 1
        if len(title) > _inx:
            top3.append(title[_inx])
    _list.sort(key=valueFunc)
    i = 0
    for kvp in _list:
        i += 1
        if i > 3:
            break
        _inx = int(kvp.get('Key').replace('A', '')) - 1
        if len(title) > _inx:
            lower3.append(title[_inx])
    v1 = int_arg(strjson.get('A1'), 0) + int_arg(strjson.get('A5'), 0) + int_arg(strjson.get('A9'), 0) + int_arg(
        strjson.get('A13'), 0) + int_arg(strjson.get('A17'), 0)
    v1x = round(v1 / 5, 2)
    v2 = int_arg(strjson.get('A2'), 0) + int_arg(strjson.get('A6'), 0) + int_arg(strjson.get('A10'), 0) + int_arg(
        strjson.get('A14'), 0) + int_arg(strjson.get('A18'), 0)
    v2x = round(v2 / 5, 2)
    v3 = int_arg(strjson.get('A3'), 0) + int_arg(strjson.get('A7'), 0) + int_arg(strjson.get('A11'), 0) + int_arg(
        strjson.get('A15'), 0) + int_arg(strjson.get('A19'), 0)
    v3x = round(v3 / 5, 2)
    v4 = int_arg(strjson.get('A4'), 0) + int_arg(strjson.get('A8'), 0) + int_arg(strjson.get('A12'), 0) + int_arg(
        strjson.get('A16'), 0) + int_arg(strjson.get('A20'), 0)
    v4x = round(v4 / 5, 2)
    uinfo = models.SysUser.objects.filter(User_Id=result.UserId).first()
    data = {
        'reportdate': datetime.datetime.now().strftime('%Y-%m-%d'),
        'truename': uinfo.UserTrueName,
        'top3': top3,
        'lower3': lower3,
        'c': v1x,
        'e': v2x,
        'o': v3x,
        'r': v4x
    }
    result.Answer = json.dumps(data, ensure_ascii=False, default=str)
    id = save_report(result, "岗位适应度测评报告", "3,4,5,6", result.ModelCode)
    return id


def analysR410(result):
    rinfo = models.AppModelreportresult.objects.filter(ProbjectId=result.ProbjectId, ModelCode='r411',
                                                       UserId=result.DataId).first()
    if rinfo:  # 分析结果
        strR411 = json.loads(rinfo.Answer)
        strjson = json.loads(result.Answer)
        mb = ''
        prinfo = get_my_project_objectives(result.ProbjectId, result.UserId)
        if prinfo.status:
            mb = prinfo.data.split('|||')[0]
        a1 = {
            'student': strjson.get('A1', ''),
            'coach': strR411.get('A1', '')
        }
        a2 = {
            'student': strjson.get('A2', ''),
            'coach': strR411.get('A2', '')
        }
        a3 = {
            'student': strjson.get('A3', ''),
            'coach': strR411.get('A3', '')
        }
        # 已完成测评和报告 已完成的教练小时数 已完成的行动计划次数 已完成的学习任务次数 计划中剩余的教练小时数 规划中的测评和报告

        evnums = models.AppProbjectexamrelationuser.objects.filter(Q(interested_id=result.UserId) | Q(UserId=result.UserId),
                                                                   ProbjectId=result.ProbjectId, Status__gte=1).values('ExId').annotate(Count('ExId')).count()
        times = get_interview_times(result.UserId, result.ProbjectId)
        acplan = models.AppProbjectlearningaction.objects.filter(ProbjectId=result.ProbjectId, User_Id=result.UserId,
                                                                 IsCompateActionPlan=1).count()
        learningplan = models.AppProbjectlearningaction.objects.filter(ProbjectId=result.ProbjectId,
                                                                       User_Id=result.UserId,
                                                                       IsCompateLearningPlan=1).count()
        pinfo = get_project_info(result.ProbjectId)
        sytimes = pinfo.Times - get_interview_times(0, result.ProbjectId)
        waitevnums = models.AppProbjectexamrelationuser.objects.filter(ProbjectId=result.ProbjectId,
                                                                       UserId=result.UserId, Status=0).count()
        uinfo = models.SysUser.objects.filter(User_Id=result.UserId).first()
        cinfo = models.SysUser.objects.filter(User_Id=result.DataId).first()
        data = {
            'reportdate': datetime.datetime.now().strftime('%Y-%m-%d'),
            'truename': uinfo.UserTrueName,
            'coachname': cinfo.UserTrueName,
            'mb': mb,
            'a1': a1,
            'a2': a2,
            'a3': a3,
            'evnums': evnums,
            'times': times,
            'acplan': acplan,
            'learningplan': learningplan,
            'sytimes': sytimes,
            'waitevnums': waitevnums
        }
        result.Answer = json.dumps(data, ensure_ascii=False, default=str)
        id = save_report(result, "阶段性回顾总结 综合报告 ", "3,4,5,6", result.ModelCode)
        return id
    return result.Id


def analysR411(result):
    rinfo = models.AppModelreportresult.objects.filter(ProbjectId=result.ProbjectId, ModelCode='r410',
                                                       UserId=result.DataId).first()
    if rinfo:  # 分析结果
        strR410 = json.loads(rinfo.Answer)
        strjson = json.loads(result.Answer)
        userid = value_or_default(result.DataId, 0)
        mb = ''
        prinfo = get_my_project_objectives(result.ProbjectId, result.UserId)
        if prinfo.status:
            mb = prinfo.data.split('|||')[0]
        a1 = {
            'student': strR410.get('A1', ''),
            'coach': strjson.get('A1', '')
        }
        a2 = {
            'student': strR410.get('A2', ''),
            'coach': strjson.get('A2', '')
        }
        a3 = {
            'student': strR410.get('A3', ''),
            'coach': strjson.get('A3', '')
        }
        # 已完成测评和报告 已完成的教练小时数 已完成的行动计划次数 已完成的学习任务次数 计划中剩余的教练小时数 规划中的测评和报告
        evnums = models.AppProbjectexamrelationuser.objects.filter(ProbjectId=result.ProbjectId, UserId=userid,
                                                                   Status__gt=1).count()
        times = get_interview_times(userid, result.ProbjectId)
        acplan = models.AppProbjectlearningaction.objects.filter(ProbjectId=result.ProbjectId, User_Id=userid,
                                                                 IsCompateActionPlan=1).count()
        learningplan = models.AppProbjectlearningaction.objects.filter(ProbjectId=result.ProbjectId,
                                                                       User_Id=userid,
                                                                       IsCompateLearningPlan=1).count()
        pinfo = get_project_info(result.ProbjectId)
        sytimes = pinfo.Times - get_interview_times(0, result.ProbjectId)
        waitevnums = models.AppProbjectexamrelationuser.objects.filter(ProbjectId=result.ProbjectId,
                                                                       UserId=userid, Status=0).count()
        uinfo = models.SysUser.objects.filter(User_Id=result.DataId).first()
        cinfo = models.SysUser.objects.filter(User_Id=result.UserId).first()
        data = {
            'reportdate': datetime.datetime.now().strftime('%Y-%m-%d'),
            'truename': uinfo.UserTrueName,
            'coachname': cinfo.UserTrueName,
            'mb': mb,
            'a1': a1,
            'a2': a2,
            'a3': a3,
            'evnums': evnums,
            'times': times,
            'acplan': acplan,
            'learningplan': learningplan,
            'sytimes': sytimes,
            'waitevnums': waitevnums
        }
        result.Answer = json.dumps(data, ensure_ascii=False, default=str)
        dataid = result.DataId
        result.DataId = result.UserId
        result.UserId = value_or_default(dataid, 0)
        id = save_report(result, "阶段性回顾总结 综合报告 ", "3,4,5,6", result.ModelCode)
        return id
    else:
        dataid = result.DataId
        result.DataId = result.UserId
        result.UserId = value_or_default(dataid, 0)
    return result.Id


def analysR413(probjectid, pici, type=0):
    pinfo = get_project_info(probjectid)
    # add interview subject info
    usetimes = get_subject_info(probjectid)
    if null_or_blank(pici):
        pici = new_pici()
    probject = {
        'name': pinfo.Name,
        'number': pinfo.Number,
        'starttime': pinfo.Starttime,
        'endtime': pinfo.Endtime,
        'times': pinfo.Times,
        'usetimes': usetimes
    }
    _listcp = []
    exam_list = models.AppProbjectexam.objects.filter(ProbjectId=probjectid, Enable=1)
    for item in exam_list.all():
        count = 0
        if item.ExId == 17:
            count = models.AppEvalutionreport.objects.filter(ProbjectId=probjectid, status=1).count()
        elif item.ExId == 18:
            # 他评不参与计算
            continue
        elif item.ExId == 3:
            # 利益相关调研只看最后的总结报告
            count = models.AppModelreportreport.objects.filter(ProbjectId=probjectid, status=1,
                                                               ModelReportId=3, ReportCode='r241').count()
        else:
            count = models.AppModelreportreport.objects.filter(ProbjectId=probjectid, status=1,
                                                               ModelReportId=item.ExId).count()

        docount = count
        waitdo = pinfo.Number - count
        if waitdo < 0:
            waitdo = 0
        _listcp.append({
            'docount': docount,
            'waitdo': waitdo,
            'pagecode': item.PageCode,
            'typeid': value_or_default(item.TypeId, 0),
            'title': item.ExName
        })
    _listA2 = []
    a2list = models.AppProbjectsatisfaction.objects.filter(ProbjectId=probjectid)
    actionplannum = 0
    for item in a2list.all():
        if item.userType == 0 and item.actionplan:
            actionplannum = actionplannum + len(item.actionplan.split('\n'))
        if item.userType == 1:
            # 获取被教练者
            interviewinfo = models.AppProbjectinterview.objects.filter(Id=item.interviewId).first()
            _userid = interviewinfo.User_Id
            # 目标及时长
            if not null_or_blank(item.A8):
                arr = item.A8.split('|||')
                for itemx in arr:
                    if not null_or_blank(itemx):
                        arrx = itemx.split('@@')
                        if len(arrx) < 2:
                            continue
                        behaviour = get_dictionary_list_value('behaviour', arrx[0])
                        _tims = 0
                        if arrx[1].isnumeric():
                            _tims = round(float(arrx[1]) / 60, 1)
                        if null_or_blank(behaviour) or _tims == 0:
                            continue
                        x = list(filter(lambda x: x.get('title') == behaviour, _listA2))
                        if len(x) > 0:
                            userids = x[0].get('userids')
                            if _userid not in userids:
                                x[0]['number'] = x[0]['number'] + 1
                                userids.append(_userid)
                                x[0]['userids'] = userids
                            x[0]['hours'] = x[0].get('hours') + _tims
                        else:
                            _listA2.append({
                                'title': behaviour,
                                'hours': _tims,
                                'number': 1,
                                'userid': _userid,
                                'userids': [_userid]
                            })
            elif not null_or_blank(item.A2):
                dic = get_dictionary_list_value('behaviour', item.A2)
                if null_or_blank(dic):
                    continue
                arr = dic.split(',')
                for title in arr:
                    if null_or_blank(title):
                        continue
                    x = list(filter(lambda x: x.get('title') == title, _listA2))
                    if len(x) > 0:
                        x[0]['hours'] = x[0].get('hours') + round(value_or_default(item.times, 0) / 60, 2)
                        userids = x[0].get('userids')
                        if _userid not in userids:
                            x[0]['number'] = x[0]['number'] + 1
                            userids.append(_userid)
                            x[0]['userids'] = userids
                    else:
                        _listA2.append({
                            'title': title,
                            'hours': round(value_or_default(item.times, 0) / 60, 2),
                            'number': 1,
                            'userid': _userid,
                            'userids': [_userid]
                        })
            elif not null_or_blank(item.A4) and item.ModelCode == 'r42':
                arrx = item.A4.split('@@')
                if len(arrx) < 2:
                    continue
                other_item = arrx[0]
                _tims = round(float(arrx[1]) / 60, 1)
                if null_or_blank(other_item) or _tims == 0:
                    continue
                x = list(filter(lambda x: x.get('title') == '其他', _listA2))
                if len(x) > 0:
                    x[0]['hours'] = x[0].get('hours') + _tims
                    userids = x[0].get('userids')
                    if _userid not in userids:
                        x[0]['number'] = x[0]['number'] + 1
                        userids.append(_userid)
                        x[0]['userids'] = userids
                    if 'other' not in x[0]:
                        x[0]['other'] = []
                    x[0]['other'] = x[0]['other'].append(other_item)
                else:
                    _listA2.append({
                        'title': '其他',
                        'hours': _tims,
                        'number': 1,
                        'userid': _userid,
                        'userids': [_userid],
                        'other': [other_item]
                    })
    # 学员改变程度
    cw = 0
    listcw = []
    cwlist = models.AppModelreportreport.objects.filter(ProbjectId=probjectid, ModelCode='r45', status=1)

    if cwlist.count() > 0:
        # 没成效 没改变 有进步
        v1 = 0
        v2 = 0
        v3 = 0
        for item in cwlist.all():
            if not null_or_blank(item.reportjson):
                strR45 = json.loads(item.reportjson)
                _dicmb = strR45.get('allmb', {})
                if _dicmb:
                    _tmpone = 0
                    dic_count = 0
                    for k, v in _dicmb.items():
                        pj = float(v[0])
                        _tmpone += pj
                        dic_count += 1
                    if _tmpone < 0:
                        v1 += 1
                    elif _tmpone > 0:
                        v3 += 1
                    else:
                        v2 += 1
                    div = dic_count
                    if dic_count == 0:
                        div = 1
                    cw = cw + round(_tmpone / div, 2)
        listcw.append({
            'name': '没成效(-3至-1分)',
            'value': v1
        })
        listcw.append({
            'name': '没改变(0)',
            'value': v2
        })
        listcw.append({
            'name': '有进步(1至3分)',
            'value': v3
        })
        cw = round(cw / cwlist.count(), 2)
    # 投入度
    listI = models.AppProbjectinterview.objects.filter(ProbjectId=probjectid, status=1, interviewType=0,
                                                       Satisfaction__gt=0)
    nowInterview = listI.aggregate(Max('nowInterview')).get('nowInterview__max')
    jltitle = []
    czdata = []
    trdata = []
    jldata = []
    for i in range(1, nowInterview + 1):
        jllist = listI.filter(nowInterview=i)
        if jllist.count() > 0:
            jltitle.append('第' + str(i) + '次')
            val = get_two_number(jllist.aggregate(Sum('Satisfaction')).get('Satisfaction__sum') / jllist.count())
            jldata.append(val)

            val2 = get_two_number(
                jllist.aggregate(Sum('growupSatisfied')).get('growupSatisfied__sum') / jllist.count())
            czdata.append(val2)

            val3 = get_two_number(
                jllist.aggregate(Sum('inputSatisfied')).get('inputSatisfied__sum') / jllist.count())
            trdata.append(val3)

    listcz = {
        'title': jltitle,
        'data': czdata
    }
    listtr = {
        'title': jltitle,
        'data': trdata
    }
    listjl = {
        'title': jltitle,
        'data': jldata
    }
    cz = 0
    if listI.count() > 0:
        cz = get_two_number(listI.aggregate(Sum('growupSatisfied')).get('growupSatisfied__sum') / listI.count())
    else:
        cz = get_two_number(listI.aggregate(Sum('growupSatisfied')).get('growupSatisfied__sum'))
    tr = 0
    if listI.count() > 0:
        tr = get_two_number(listI.aggregate(Sum('inputSatisfied')).get('inputSatisfied__sum') / listI.count())
    else:
        tr = get_two_number(listI.aggregate(Sum('inputSatisfied')).get('inputSatisfied__sum'))
    jl = 0
    if listI.count() > 0:
        jl = get_two_number(listI.aggregate(Sum('Satisfaction')).get('Satisfaction__sum') / listI.count())
    else:
        jl = get_two_number(listI.aggregate(Sum('Satisfaction')).get('Satisfaction__sum'))
    studyplan = models.AppProbjectlearningaction.objects.filter(ProbjectId=probjectid, IsCompateLearningPlan=1).count()
    _listpj = []
    # pj is not required for phase report
    if type != 0:
        propj = models.AppProbjectsatisfaction.objects.filter(ProbjectId=probjectid, ModelCode='r54', userType=0)
        for item in propj.all():
            a1 = item.subject
            if not null_or_blank(a1):
                _listpj.append(a1)
    data = {
        'probject': probject,
        'cp': _listcp,
        'mb': _listA2,
        'cw': cw,
        'cwlist': listcw,
        'cz': cz,
        'czlist': listcz,
        'tr': tr,
        'trlist': listtr,
        'jl': jl,
        'jllist': listjl,
        'studyplan': studyplan,
        'actionplannum': actionplannum,
        'pj': _listpj
    }
    m = models.AppProbjectreport()
    m.ProbjectId = probjectid
    m.Title = "项目总结性统计报告"
    if type == 0:
        m.Title = "项目阶段性统计报告"
    m.Json = json.dumps(data, ensure_ascii=False, default=str)
    m.otherjson = json.dumps(_listpj, ensure_ascii=False, default=str)
    m.Enable = 0
    m.pici = pici
    m.reporttype = "r63"
    m.CreateDate = datetime.datetime.now().replace(microsecond=0)
    if type == 0:
        m.reporttype = "r413"
    m.save()


def get_relation_to_master_member(projectid, mastermemberid, memberid):
    res = models.AppMemberinterested.objects.filter(ProbjectId=projectid, MasterMember_Id=mastermemberid,
                                                    Member_Id=memberid).first()
    if res:
        return res.Relation
    return ''


def analysR45(result):
    # 判断当前是第几次观察，获取截止时间，报告数据
    #             //获取已提交的观察数据及利益者关系
    #             // 汇总目标（12目标分次） 单一次 分关系 分次展示
    #             //allmb：{"自我成长":[0,1]},mbxm:{"自我成长":{"上级":[1,2,3],"平级":[1,2]}},a1:{"上级":[1,2,3],"平级":[1,2]},a2:[],a3:[]
    r45json = '{}'
    # 获取提交数据
    _resultlist = models.AppModelreportresult.objects.filter(ProbjectId=result.ProbjectId, DataId=result.UserId,
                                                             ModelCode=result.ModelCode)

    lastmodel = models.AppModelreportreport.objects.filter(ProbjectId=result.ProbjectId,
                                                           ReportCode=result.ReportCode, UserId=result.UserId,
                                                           status=1).last()
    if lastmodel:
        r45json = lastmodel.reportjson
        start_time = value_or_default(lastmodel.ModifyDate, datetime.datetime.now())
        _resultlist = _resultlist.filter(CreateDate__gt=start_time)

    # 按用户关系分组
    relation_member_list = []
    userlist = {}
    for item in _resultlist.all():
        relation = models.AppMemberinterested.objects.filter(ProbjectId=result.ProbjectId, Member_Id=item.UserId,
                                                             MasterMember_Id=result.UserId).first().Relation
        if relation not in userlist:
            userlist[relation] = [item.UserId]
        else:
            if item.UserId not in userlist[relation]:
                userlist[relation].append(item.UserId)
    for k, v in userlist.items():
        dic = {}
        dic['userids'] = v
        dic['relation'] = k
        dic['nums'] = len(v)
        relation_member_list.append(dic)

    # behaviour_score_dic:领导行为平均分
    # behaviour_relation_score_dic:领导行为分关系的平均分
    # first_question_score_dic:第一个问题分关系平均分
    behaviour_score_dic = {}
    behaviour_relation_score_dic = {}
    behaviour_remark_dic = {}
    first_question_score_dic = {}
    second_question_answer_list = []
    third_question_answer_list = []

    for each_relation_exam_user_info in relation_member_list:
        uids = each_relation_exam_user_info.get('userids')
        relation_text = each_relation_exam_user_info.get('relation')
        _filterresult = _resultlist.filter(UserId__in=uids)
        for result_item in _filterresult:
            raw_result = json.loads(result_item.Answer)
            _ability = '|||'
            if raw_result['ability']:
                _ability = raw_result['ability']
            behaviour_remark_list = _ability.split('|||')[1].split('@@')
            behaviour_text_list = _ability.split('|||')[0].split(',')

            # 设置被教练者ILDP目标描述
            for index, value in enumerate(behaviour_text_list):
                behaviour_remark_dic[value] = behaviour_remark_list[index]

            for item_key, item_value in raw_result.items():
                # 只处理 “MB”，“A1”，“A2”，“A3”
                if item_key.startswith('MB') and not null_or_blank(item_value):
                    behaviour_text = raw_result.get(item_key.replace('MB', 'MC'), '')
                    if behaviour_text in behaviour_score_dic:
                        behaviour_score_dic[behaviour_text].append(float(item_value))
                    else:
                        behaviour_score_dic[behaviour_text] = [float(item_value)]

                    if behaviour_text in behaviour_relation_score_dic:
                        # 有此类领导力数据存在
                        behaviour_sub_relation_score_dic = behaviour_relation_score_dic[behaviour_text]
                        if behaviour_sub_relation_score_dic and relation_text in behaviour_sub_relation_score_dic:
                            ## 有此类关系的数据存在
                            behaviour_sub_relation_score_dic[relation_text].append(float(item_value))
                        else:
                            behaviour_sub_relation_score_dic[relation_text] = [float(item_value)]
                    else:
                        behaviour_sub_relation_score_dic = {
                            relation_text: [float(item_value)]
                        }
                        behaviour_relation_score_dic[behaviour_text] = behaviour_sub_relation_score_dic

                elif item_key.startswith('A1') and not null_or_blank(item_value):
                    if relation_text in first_question_score_dic:
                        first_question_score_dic[relation_text].append(float(item_value))
                    else:
                        first_question_score_dic[relation_text] = [float(item_value)]
                elif item_key.startswith('A2') and not null_or_blank(item_value):
                    second_question_answer_list.append(value_or_default(item_value, ''))
                elif item_key.startswith('A3') and not null_or_blank(item_value):
                    third_question_answer_list.append(value_or_default(item_value, ''))

    # 计算平均数，将新分数和老分数合并
    oldreport = json.loads(r45json)
    if oldreport and 'allmb' in oldreport:
        # 有老数据
        last_behaviour_score_dic = oldreport.get('allmb', {})
        for behaviour_text, last_score_list in last_behaviour_score_dic.items():
            if behaviour_text in behaviour_score_dic:
                score_list = behaviour_score_dic[behaviour_text]
                avg_score = round(sum(score_list) / len(score_list), 2)
                last_score_list.append(avg_score)
        behaviour_score_dic = last_behaviour_score_dic
    else:
        new_dic = {}
        for behaviour_text, score_list in behaviour_score_dic.items():
            avg_score = round(sum(score_list) / len(score_list), 2)
            new_dic[behaviour_text] = [avg_score]
        behaviour_score_dic = new_dic

    if oldreport and 'mbxm' in oldreport:
        # 有老数据
        last_behaviour_relation_score_dic = oldreport.get('mbxm', {})
        for behaviour_text, last_relation_score_list in last_behaviour_relation_score_dic.items():
            if behaviour_text in behaviour_relation_score_dic:
                relation_score_list = behaviour_relation_score_dic[behaviour_text]
                for relation_text, last_score_list in last_relation_score_list.items():
                    if relation_text in relation_score_list:
                        score_list = relation_score_list[relation_text]
                        avg_score = round(sum(score_list) / len(score_list), 2)
                        last_score_list.append(avg_score)
        behaviour_relation_score_dic = last_behaviour_relation_score_dic
    else:
        new_dic = {}
        for behaviour_text, relation_score_list in behaviour_relation_score_dic.items():
            new_sub_dic = {}
            for relation_text, score_list in relation_score_list.items():
                avg_score = round(sum(score_list) / len(score_list), 2)
                new_sub_dic[relation_text] = [avg_score]
            new_dic[behaviour_text] = new_sub_dic
        behaviour_relation_score_dic = new_dic

    if oldreport and 'a1' in oldreport:
        # 有老数据
        last_first_question_score_dic = oldreport.get('a1', {})
        for relation_text, last_score_list in last_first_question_score_dic.items():
            if relation_text in first_question_score_dic:
                score_list = first_question_score_dic[relation_text]
                avg_score = round(sum(score_list) / len(score_list), 2)
                last_score_list.append(avg_score)
        first_question_score_dic = last_first_question_score_dic
    else:
        new_dic = {}
        for relation_text, score_list in first_question_score_dic.items():
            avg_score = round(sum(score_list) / len(score_list), 2)
            new_dic[relation_text] = [avg_score]
        first_question_score_dic = new_dic


    userid = value_or_default(result.UserId, 0)
    uinfo = models.SysUser.objects.filter(User_Id=userid).first()
    truename = uinfo.UserTrueName
    # 更新教练id
    rcoinfo = models.AppProbjectrelation.objects.filter(ProbjectId=result.ProbjectId, UserId=userid).first()
    if rcoinfo:
        result.CoachId = rcoinfo.CoachId
    data = {
        'reportdate': datetime.datetime.now().strftime('%Y-%m-%d'),
        'userid': userid,
        'truename': truename,
        'listmember': relation_member_list,
        'allmb': behaviour_score_dic,
        'mbxm': behaviour_relation_score_dic,
        'a1': first_question_score_dic,
        'a2': second_question_answer_list,
        'a3': third_question_answer_list,
        'mbremark': behaviour_remark_dic
    }
    result.reportjson = json.dumps(data, ensure_ascii=False, default=str)
    result.save()
    return result.Id


def add_empty_list(list, count):
    if len(list) < count:
        for i in range(len(list) - 1, count - 1):
            list.append(0)
        return True
    return False


def get_relation_to_master_member(probjectid, mastermemberid, memberid):
    m = models.AppMemberinterested.objects.filter(ProbjectId=probjectid, MasterMember_Id=mastermemberid,
                                                  Member_Id=memberid).first()
    if m:
        return m.Relation
    return ''


def analysR53(result):
    rinfo = models.AppModelreportresult.objects.filter(ProbjectId=result.ProbjectId, ModelCode='r54',
                                                       UserId=result.DataId).first()
    if rinfo:
        strR54 = json.loads(rinfo.Answer)
        strjson = json.loads(result.Answer)
        a1 = {
            'student': strjson.get('A1', ''),
            'coach': strR54.get('A1', '')
        }
        a2 = {
            'student': strjson.get('A2', ''),
            'coach': ''
        }
        a3 = {
            'student': strjson.get('A3', ''),
            'coach': strR54.get('A2', '')
        }
        a4 = {
            'student': strjson.get('A4', ''),
            'coach': ''
        }
        a5 = {
            'student': strjson.get('A5', ''),
            'coach': strR54.get('A3', '')
        }
        a6 = {
            'student': strjson.get('A6', ''),
            'coach': ''
        }
        evnums = models.AppProbjectexamrelationuser.objects.filter(ProbjectId=result.ProbjectId, UserId=result.UserId,
                                                                   Status__gt=1).count()
        times = get_interview_times(result.UserId, result.ProbjectId)
        acplan = models.AppProbjectlearningaction.objects.filter(ProbjectId=result.ProbjectId, User_Id=result.UserId,
                                                                 IsCompateActionPlan=1).count()
        learningplan = models.AppProbjectlearningaction.objects.filter(ProbjectId=result.ProbjectId,
                                                                       User_Id=result.UserId,
                                                                       IsCompateLearningPlan=1).count()
        # 投入度
        listI = models.AppProbjectinterview.objects.filter(ProbjectId=result.ProbjectId, status=1, interviewType=0,
                                                           Satisfaction__gt=0, User_Id=result.UserId)
        nowInterview = listI.aggregate(Max('nowInterview')).get('nowInterview__max')
        jltitle = []
        czdata = []
        trdata = []
        jldata = []
        for i in range(1, nowInterview + 1):
            jltitle.append('第' + str(i) + '次')
            jllist = listI.filter(nowInterview=i)
            val = 0
            if jllist.count() > 0:
                val = get_two_number(jllist.aggregate(Sum('Satisfaction')).get('Satisfaction__sum') / jllist.count())
            jldata.append(val)
            val2 = 0
            if jllist.count() > 0:
                val2 = get_two_number(
                    jllist.aggregate(Sum('growupSatisfied')).get('growupSatisfied__sum') / jllist.count())
            czdata.append(val2)
            val3 = 0
            if jllist.count() > 0:
                val3 = get_two_number(
                    jllist.aggregate(Sum('inputSatisfied')).get('inputSatisfied__sum') / jllist.count())
            trdata.append(val3)
        listcz = {
            'title': jltitle,
            'data': czdata
        }
        listtr = {
            'title': jltitle,
            'data': trdata
        }
        listjl = {
            'title': jltitle,
            'data': jldata
        }
        cz = 0
        if listI.count() > 0:
            cz = get_two_number(listI.aggregate(Sum('growupSatisfied')).get('growupSatisfied__sum') / listI.count())
        else:
            cz = get_two_number(listI.aggregate(Sum('growupSatisfied')).get('growupSatisfied__sum'))
        tr = 0
        if listI.count() > 0:
            tr = get_two_number(listI.aggregate(Sum('inputSatisfied')).get('inputSatisfied__sum') / listI.count())
        else:
            tr = get_two_number(listI.aggregate(Sum('inputSatisfied')).get('inputSatisfied__sum'))
        jl = 0
        if listI.count() > 0:
            jl = get_two_number(listI.aggregate(Sum('Satisfaction')).get('Satisfaction__sum') / listI.count())
        else:
            jl = get_two_number(listI.aggregate(Sum('Satisfaction')).get('Satisfaction__sum'))
        uinfo = models.SysUser.objects.filter(User_Id=result.UserId).first()
        cinfo = models.SysUser.objects.filter(User_Id=result.DataId).first()
        data = {
            'reportdate': datetime.datetime.now().strftime('%Y-%m-%d'),
            'truename': uinfo.UserTrueName,
            'coachname': cinfo.UserTrueName,
            'a1': a1,
            'a2': a2,
            'a3': a3,
            'a4': a4,
            'a5': a5,
            'a6': a6,
            'evnums': evnums,
            'times': times,
            'acplan': acplan,
            'learningplan': learningplan,
            'cz': cz,
            'czlist': listcz,
            'tr': tr,
            'trlist': listtr,
            'jl': jl,
            'jllist': listjl
        }
        result.Answer = json.dumps(data, ensure_ascii=False, default=str)
        id = save_report(result, "最后一次约谈后 客户总结报告", "3,4,5,6", "r55")
        # analysR413(result.ProbjectId, result.pici, 1)
        return id
    return result.Id


def analysR54(result):
    rinfo = models.AppModelreportresult.objects.filter(ProbjectId=result.ProbjectId, ModelCode='r53',
                                                       UserId=result.DataId).first()
    if rinfo:
        strR53 = json.loads(rinfo.Answer)
        strjson = json.loads(result.Answer)
        userid = value_or_default(result.DataId, 0)
        a1 = {
            'student': strR53.get('A1', ''),
            'coach': strjson.get('A1', '')
        }
        a2 = {
            'student': strR53.get('A2', ''),
            'coach': ''
        }
        a3 = {
            'student': strR53.get('A3', ''),
            'coach': strjson.get('A2', '')
        }
        a4 = {
            'student': strR53.get('A4', ''),
            'coach': ''
        }
        a5 = {
            'student': strR53.get('A5', ''),
            'coach': strjson.get('A3', '')
        }
        a6 = {
            'student': strR53.get('A6', ''),
            'coach': ''
        }
        evnums = models.AppProbjectexamrelationuser.objects.filter(ProbjectId=result.ProbjectId, UserId=userid,
                                                                   Status__gt=1).count()
        times = get_interview_times(userid, result.ProbjectId)
        acplan = models.AppProbjectlearningaction.objects.filter(ProbjectId=result.ProbjectId, User_Id=userid,
                                                                 IsCompateActionPlan=1).count()
        learningplan = models.AppProbjectlearningaction.objects.filter(ProbjectId=result.ProbjectId,
                                                                       User_Id=userid,
                                                                       IsCompateLearningPlan=1).count()
        # 投入度
        listI = models.AppProbjectinterview.objects.filter(ProbjectId=result.ProbjectId, status=1, interviewType=0,
                                                           Satisfaction__gt=0, User_Id=userid)
        nowInterview = listI.aggregate(Max('nowInterview')).get('nowInterview__max')
        jltitle = []
        czdata = []
        trdata = []
        jldata = []
        for i in range(1, nowInterview + 1):
            jltitle.append('第' + str(i) + '次')
            jllist = listI.filter(nowInterview=i)
            val = 0
            if jllist.count() > 0:
                val = get_two_number(jllist.aggregate(Sum('Satisfaction')).get('Satisfaction__sum') / jllist.count())
            jldata.append(val)
            val2 = 0
            if jllist.count() > 0:
                val2 = get_two_number(
                    jllist.aggregate(Sum('growupSatisfied')).get('growupSatisfied__sum') / jllist.count())
            czdata.append(val2)
            val3 = 0
            if jllist.count() > 0:
                val3 = get_two_number(
                    jllist.aggregate(Sum('inputSatisfied')).get('inputSatisfied__sum') / jllist.count())
            trdata.append(val3)
        listcz = {
            'title': jltitle,
            'data': czdata
        }
        listtr = {
            'title': jltitle,
            'data': trdata
        }
        listjl = {
            'title': jltitle,
            'data': jldata
        }
        cz = 0
        if listI.count() > 0:
            cz = get_two_number(listI.aggregate(Sum('growupSatisfied')).get('growupSatisfied__sum') / listI.count())
        else:
            cz = get_two_number(listI.aggregate(Sum('growupSatisfied')).get('growupSatisfied__sum'))
        tr = 0
        if listI.count() > 0:
            tr = get_two_number(listI.aggregate(Sum('inputSatisfied')).get('inputSatisfied__sum') / listI.count())
        else:
            tr = get_two_number(listI.aggregate(Sum('inputSatisfied')).get('inputSatisfied__sum'))
        jl = 0
        if listI.count() > 0:
            jl = get_two_number(listI.aggregate(Sum('Satisfaction')).get('Satisfaction__sum') / listI.count())
        else:
            jl = get_two_number(listI.aggregate(Sum('Satisfaction')).get('Satisfaction__sum'))
        uinfo = models.SysUser.objects.filter(User_Id=userid).first()
        cinfo = models.SysUser.objects.filter(User_Id=result.UserId).first()
        data = {
            'reportdate': datetime.datetime.now().strftime('%Y-%m-%d'),
            'truename': uinfo.UserTrueName,
            'coachname': cinfo.UserTrueName,
            'a1': a1,
            'a2': a2,
            'a3': a3,
            'a4': a4,
            'a5': a5,
            'a6': a6,
            'evnums': evnums,
            'times': times,
            'acplan': acplan,
            'learningplan': learningplan,
            'cz': cz,
            'czlist': listcz,
            'tr': tr,
            'trlist': listtr,
            'jl': jl,
            'jllist': listjl
        }
        result.Answer = json.dumps(data, ensure_ascii=False, default=str)
        id = save_report(result, "最后一次约谈后 教练总结报告", "3,4,5,6", "r55")
        # analysR413(result.ProbjectId, result.pici, 1)
        return id
    return result.Id


pici_redis = redis.Redis.from_url(settings.TOKEN_LOGIN_REDIS)


def get_report_pici():
    if pici_redis.get('pici'):
        pici_redis.set('pici', int(pici_redis.get('pici')) + 1)
    else:
        pici_redis.set('pici', 1)
    return pici_redis.get('pici').decode('utf8')
