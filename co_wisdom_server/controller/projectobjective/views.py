from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.viewsets import ModelViewSet, ViewSet, GenericViewSet

from controller.coach.actions import get_coach_info
from controller.dictionary.actions import get_dictionary_list_value
from controller.project.actions import get_start_project_num_by_company, get_complete_project_num_by_company, \
    get_sign_project_num_by_company, get_project_info, get_project_info_by_user
from data import models, serializers, extension
from utils import response, blank_to_none, null_or_blank, int_arg, value_or_default
import json

from utils.model import add_option
from utils.user import get_user_ids



class ProjectObjectiveViewSet(extension.ResponseViewSet):
    queryset = models.AppProbjectobjective.objects.all()
    serializer_class = serializers.AppProbjectobjectiveSerializer

    @action(methods=['post'], detail=False)
    def getpagedata(self, request):
        t = self.get_page_rows(request.data)
        if t.count() > 0:
            extra = []
            for item in t.all():
                dic = {}
                dic["eId"] = item.Id
                # 教练信息 姓名 头像 群智教练级别 认证 /职位 最近登陆时间
                comodel = get_coach_info(item.CoachId)
                if comodel and comodel.CoachId > 0:
                    dic["img"] = comodel.Photograph
                    dic["level"] = get_dictionary_list_value("coachlevel", comodel.CoachLevel)
                    dic["truename"] = comodel.TrueName
                    dic["jlzz"] = comodel.jlzz
                    dic["jlzw"] = comodel.jlzw
                dic["ProName"] = get_project_info(item.ProbjectId).Name
                extra.append(dic)
            t.extra_list = extra
        return Response(
            self.page_data(t)
        )

    @action(methods=['post'], detail=False)
    def add(self, request):
        main_data = request.data.get('mainData')
        uid = request.user.pk
        probjectid = int_arg(main_data.get('ProbjectId'), 0)
        if probjectid == 0:
            probjectid = get_project_info_by_user(uid).Probject_Id
            main_data['ProbjectId'] = probjectid
        coachid = 0
        ur = models.AppProbjectrelation.objects.filter(UserId=uid, ProbjectId=probjectid).first()
        if ur:
            coachid = ur.CoachId
        main_data['CoachId'] = coachid
        main_data['UserId'] = uid
        t = self.add_row(main_data)
        if t.status:
            if ur:
                ur.Objectives = main_data.get('Objectives')
                ur.PlanTime = main_data.get('PlanTime')
                ur.studyPlan = main_data.get('studyPlan', '')
                ur.save()
        return Response(
            t.raw()
        )