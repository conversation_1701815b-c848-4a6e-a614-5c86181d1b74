import datetime

from rest_framework.decorators import action
from rest_framework.response import Response

from data import models, serializers, extension
from utils import response, null_or_blank, delete_htmltag


class FeedbackViewSet(extension.ResponseViewSet):
    queryset = models.AppFeedback.objects.all()
    serializer_class = serializers.AppFeedbackSerializer

    @action(methods=['post'], detail=False)
    def submit(self, request):
        main_data = request.data.get('mainData')
        content = main_data.get('content')
        if null_or_blank(content):
            return Response(
                response.Content().error('请输入内容').raw()
            )

        main_data['title'] = delete_htmltag(content[:100])
        main_data['user_id'] = request.user.pk
        main_data['user_name'] = request.user.UserName
        main_data['is_lock'] = 1
        main_data['add_time'] = datetime.datetime.now()
        res = self.add_row(main_data)
        return Response(
            res.raw()
        )

    @action(methods=['post'], detail=False, url_path='update')
    def update_new(self, request):
        main_data = request.data.get('mainData')
        reply_content = main_data.get('reply_content')
        if null_or_blank(reply_content):
            return Response(
                response.Content().error('请输入内容').raw()
            )
        main_data['reply_time'] = datetime.datetime.now()
        res = self.update_row(main_data)
        return Response(
            res.raw()
        )
