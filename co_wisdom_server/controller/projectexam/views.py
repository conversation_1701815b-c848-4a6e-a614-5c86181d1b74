import datetime
import json
import random
from django.conf import settings
from rest_framework.decorators import action
from rest_framework.response import Response

from controller.project.actions import get_project_info_by_user
from controller.projectexam.actions import get_model_code
from controller.projectsettings.actions import get_project_config_val
from data import models, serializers, extension
from utils import response, int_arg, value_or_default, aesdecrypt
from utils.encrypt import EncryptData
from utils.messagecenter.center import push_message
from utils.messagecenter.actions import get_exam_r24_link, get_exam_r45_link, get_exam_other_link, get_exam_r112_student, get_exam_self_link
from .constant import EX_SELF, EX_OUTHER
from controller.modelreportresult.actions import get_report_pici

class ProjectExamViewSet(extension.ResponseViewSet):
    queryset = models.AppProbjectexam.objects.all()
    serializer_class = serializers.AppProbjectexamSerializer

    @action(methods=['get'], detail=False)
    def GetEvaluateList(self, request):
        probjectid = int_arg(request.query_params.get('probjectid'), None)
        # 获取已有测评id /报告id
        ids = models.AppProbjectexam.objects.filter(ProbjectId=probjectid)
        eveid = list(ids.filter(TypeId=1).values_list('ExId', flat=True))
        eveid.append(0)
        suid = list(ids.filter(TypeId=2).values_list('ExId', flat=True))
        suid.append(0)
        # 是否开启了测评/报告 cpldl bgopen
        cpldl = int_arg(get_project_config_val(probjectid, 'cpldl'), None)
        bgopen = int_arg(get_project_config_val(probjectid, 'bgopen'), None)
        if cpldl == 0 and bgopen == 0:
            return Response(
                response.Content().error('请在功能设置里先开启测评或报告').raw()
            )
        listEva = []
        listSuy = []
        if cpldl == 1:
            listEva = models.AppEvaluation.objects.filter(Enable=1).exclude(EvaluationId__in=eveid)[:]
        if bgopen == 1:
            listSuy = models.AppModeltemplate.objects.filter(Enable=1).exclude(ModelReportId__in=suid)[:]
        # 阶段性调研报告 bgjdbb  CPS bgcps ILDP bgidlp 教练准备度调研 cpbjr 学习成果调研 cpxxcg 领导力行为指数测评 cpldl
        #             //约谈后满意度调研 cpmdy 三方会谈报告  bgsfht 进度调研设置 cpjddy 项目总结报告 bgxmzj
        query = []
        for item in listEva:
            query.append({
                'exid': item.EvaluationId,
                'exname': item.EvalName,
                'typeid': 1,
                'pagecode': item.PageCode
            })
        for item in listSuy:
            query.append({
                'exid': item.ModelReportId,
                'exname': item.ModelName,
                'typeid': 2,
                'pagecode': item.PageCode
            })
        return Response(
            response.Content().ok('SUC', query).raw()
        )

    @action(methods=['post'], detail=False)
    def SetEvaluate(self, request):
        main_data = request.data.get('mainData')
        probjectid = int_arg(main_data.get('ProbjectId'), None)
        if probjectid == 0:
            return Response(
                response.Content().error('关联的项目不能空').raw()
            )
        rows = request.data.get('detailData')
        for item in rows:
            ino = models.AppProbjectexam()
            ino.ProbjectId = probjectid
            ino.ExId = int_arg(item["exid"], None)
            ino.ExName = str(item["exname"])
            ino.TypeId = int_arg(item["typeid"], None)
            ino.PageCode = str(item["pagecode"])
            ino.CompanyId = 0
            ino.CreateDate = datetime.datetime.now().replace(microsecond=0)
            ino.ModifyDate = datetime.datetime.now().replace(microsecond=0)
            ino.Creator = request.user.UserName
            ino.Enable = 1
            ino.save()
        return Response(
            response.Content().ok().raw()
        )

    @action(methods=['post'], detail=False)
    def SaveEavluteYQ(self, request):
        main_data = request.data.get('mainData')
        probjectid = int_arg(main_data.get('ProbjectId'), None)
        if probjectid == 0:
            return Response(
                response.Content().error('关联的项目不能空').raw()
            )
        exam_id = main_data.get('ExId')
        if exam_id in [EX_SELF, EX_OUTHER]:
            # if not main_data.get('group_text', 0):
            #     return Response(
            #     response.Content().error('邀请领导行为指数评估时需填写测评群体').raw()
            # )

            # 暂定 reporttype=r222
            if not models.AppProbjectreport.objects.filter(ProbjectId=probjectid, status__in=[0, 1],
                                                           reporttype='r222').exists():
                report_count = models.AppProbjectreport.objects.filter(ProbjectId=probjectid, status=2,
                                                                       reporttype='r222').count()
                remark = '第%s条LBI测评报告' % (report_count + 1)
                models.AppProbjectreport.objects.create(ProbjectId=probjectid, Remark=remark, reporttype='r222',
                                                        Title='领导力行为指数评估')

        # 全是被教练者的id 其它id不会传
        userids = main_data.get('UserIds').split(',')
        end_date = datetime.datetime.strptime(main_data.get('yqEditDate'), '%Y-%m-%d')
        end_date = end_date + datetime.timedelta(days=1) - datetime.timedelta(seconds=1)
        listAdd = []
        if len(main_data.get('UserIds')) > 0:
            for item in userids:
                exid = main_data.get('ExId')
                if exid == EX_SELF:
                    # 自评只能填写一次
                    if models.AppProbjectexamrelationuser.objects.filter(ProbjectId=probjectid, ExId=exid, UserId=int(item)).exists():
                        continue
                ino = models.AppProbjectexamrelationuser()
                ino.ProbjectId = probjectid
                ino.ExId = int_arg(main_data.get('ExId'), None)
                ino.ExName = main_data.get('ExName')
                ino.TypeId = int_arg(main_data.get('TypeId'), None)
                ino.PageCode = main_data.get('PageCode')
                ino.CompanyId = 0
                ino.Enable = 1
                ino.Status = 0
                ino.UserId = int(item)
                userinfo = models.SysUser.objects.filter(User_Id=request.user.pk).first()
                ino.roleid = userinfo.Role_Id
                ino.EndDate = end_date
                ino.CreateDate = datetime.datetime.now().replace(microsecond=0)
                ino.ModifyDate = datetime.datetime.now().replace(microsecond=0)
                now = datetime.datetime.now()
                flag = models.AppProbjectexamrelationuser.objects.filter(ProbjectId=probjectid, ExId=ino.ExId,
                                                                         TypeId=ino.TypeId,
                                                                         UserId=ino.UserId, EndDate__gte=now,
                                                                         Status=0, interested_id=0).exists()
                if not flag:
                    ino.save()
                    listAdd.append(ino)
        relation = None
        if main_data.get('relation'):
            relation = json.loads(main_data.get('relation', ''))

        if relation:
            for item in relation:
                ino = models.AppProbjectexamrelationuser()
                ino.ProbjectId = probjectid
                ino.ExId = int_arg(main_data.get('ExId'), None)
                ino.ExName = main_data.get('ExName')
                ino.TypeId = int_arg(main_data.get('TypeId'), None)
                ino.PageCode = main_data.get('PageCode')
                ino.CompanyId = 0
                ino.Enable = 1
                ino.Status = 0
                ino.UserId = int(item.get('UserId'))
                userinfo = models.SysUser.objects.filter(User_Id=request.user.pk).first()
                ino.roleid = userinfo.Role_Id
                ino.EndDate = end_date
                ino.CreateDate = datetime.datetime.now().replace(microsecond=0)
                ino.ModifyDate = datetime.datetime.now().replace(microsecond=0)
                ino.interested_id = int_arg(item.get('Interested_Id'), 0)
                now = datetime.datetime.now()
                flag = models.AppProbjectexamrelationuser.objects.filter(ProbjectId=probjectid, ExId=ino.ExId,
                                                                         TypeId=ino.TypeId,
                                                                         UserId=ino.UserId, EndDate__gte=now,
                                                                         Status=0, interested_id=item.get(
                        'Interested_Id')).exists()
                if not flag:
                    ino.save()
                    listAdd.append(ino)

        if len(listAdd) > 0:
            # 清除过期未做的记录
            # models.AppProbjectexamrelationuser.objects.filter(Status=0,
            #                                                   EndDate__lt=datetime.datetime.now(),
            #                                                   ProbjectId=probjectid).all().delete()
            if listAdd[0].PageCode == 'r45':  # 存储R45报告
                r45_list = []
                for item in listAdd:
                    userid = item.interested_id
                    if userid > 0 and userid not in r45_list:
                        r45_list.append(userid)

                        all_report = models.AppModelreportreport.objects.filter(ProbjectId=probjectid,
                                                                                ReportCode='r45',
                                                                                UserId=userid)
                        # 如果有没有推送的报告存在则不新建报告
                        unpublish_report = all_report.filter(status__lt=1)
                        if not unpublish_report.exists():
                            all_publish_report = all_report.filter(status=1)
                            mreport = models.AppModelreportreport()
                            mreport.ProbjectId = probjectid
                            mreport.ModelReportId = item.ExId
                            mreport.UserId = userid
                            mreport.reportjson = ""
                            mreport.status = 0
                            mreport.pici = datetime.datetime.now().strftime('%Y%m%d%H%M%S') + str(get_report_pici())
                            mreport.title = item.ExName
                            mreport.rolesid = "3"
                            mreport.ModelCode = "r45"
                            mreport.ReportCode = "r45"
                            mreport.CoachId = 0
                            mreport.tindex = all_publish_report.count() + 1
                            mreport.save()
            try:
                # 消息通知
                exam_r24_student_dic = {}
                exam_r45_student_dic = {}
                exam_other_dic = {}
                for exam in listAdd:
                    if exam.PageCode == 'r24':
                        student = models.SysUser.objects.get(pk=exam.interested_id)
                        if not exam_r24_student_dic.get(exam.UserId):
                            exam_r24_student_dic[exam.UserId] = {
                                'title': exam.ExName,
                                'num': 1,
                                'invitedate': exam.EndDate.strftime('%Y-%m-%d'),
                                "exname": exam.ExName,
                                "student_names": student.UserTrueName,
                                'path_link': '-',
                                'probjectid': probjectid,
                                'interested_id': exam.interested_id,
                                'flag': 0
                            }
                        else:
                            exam_r24_student_dic[exam.UserId]['student_names'] = exam_r24_student_dic[exam.UserId][
                                                                             'student_names'] + '、' + student.UserTrueName
                            exam_r24_student_dic[exam.UserId]['flag'] = 1
                            exam_r24_student_dic[exam.UserId]['num'] = exam_r24_student_dic[exam.UserId]['num'] + 1
                    if exam.PageCode == 'r45':
                        student = models.SysUser.objects.get(pk=exam.interested_id)

                        if not exam_r45_student_dic.get(exam.UserId):
                            exam_r45_student_dic[exam.UserId] = {
                                'title': exam.ExName,
                                'invitedate': exam.EndDate.strftime('%Y-%m-%d'),
                                "exname": exam.ExName,
                                "student_names": student.UserTrueName,
                                'path_link': '-',
                                'probjectid': probjectid,
                                'interested_id': exam.interested_id,
                                'flag': 0

                            }
                        else:
                            exam_r45_student_dic[exam.UserId]['student_names'] = exam_r45_student_dic[exam.UserId][
                                                                             'student_names'] + '、' + student.UserTrueName
                            exam_r45_student_dic[exam.UserId]['flag'] = 1

                    if exam.ExId == EX_OUTHER:
                        student = models.SysUser.objects.get(pk=exam.interested_id)

                        if not exam_other_dic.get(exam.UserId):
                            exam_other_dic[exam.UserId] = {
                                'title': exam.ExName,
                                'invitedate': exam.EndDate.strftime('%Y-%m-%d'),
                                "exname": exam.ExName,
                                "student_names": student.UserTrueName,
                                'probjectid': probjectid,
                                'interested_id': exam.interested_id,
                                'flag': 0

                            }
                        else:
                            exam_other_dic[exam.UserId]['student_names'] = exam_other_dic[exam.UserId][
                                                                             'student_names'] + '、' + student.UserTrueName
                            exam_other_dic[exam.UserId]['flag'] = 1

                # 消息通知
                for item in listAdd:
                    dic = {'title': item.ExName}
                    dic['invitedate'] = item.EndDate.strftime('%Y-%m-%d')
                    uinfo = models.SysUser.objects.filter(User_Id=item.UserId).first()
                    pinfo = get_project_info_by_user(item.UserId)
                    if pinfo:
                        minfo = models.SysUser.objects.filter(User_Id=pinfo.Relation_UserId).first()
                        if minfo:
                            dic["manager"] = minfo.UserTrueName
                            dic["mobile"] = minfo.PhoneNo
                            dic["email"] = minfo.Email
                            dic['probjectname'] = pinfo.Name
                        fromname = ''
                        coachname = ''
                        masteruid = item.interested_id
                        if masteruid > 0:
                            mrelation = models.AppProbjectrelation.objects.filter(ProbjectId=probjectid,
                                                                                  UserId=masteruid).first()
                            if mrelation:
                                finfo = models.SysUser.objects.filter(User_Id=mrelation.UserId).first()
                                cinfo = models.SysUser.objects.filter(User_Id=mrelation.CoachId).first()
                                if finfo:
                                    fromname = finfo.UserTrueName
                                if cinfo:
                                    coachname = cinfo.UserTrueName
                        sinfo = uinfo
                        password = aesdecrypt(sinfo.UserPwd)
                        dic["truename"] = uinfo.UserTrueName
                        dic["password"] = password
                        dic["fromname"] = fromname
                        dic["coachname"] = coachname
                        dic["gender"] = settings.GENDER[uinfo.Gender] if uinfo.Gender else '先生/女士'
                        dic["path_link"] = '-'
                        models.AppProbjectexamrelationuser.objects.filter(Status=0,
                                                                          EndDate__lt=datetime.datetime.now(),
                                                                          ProbjectId=probjectid).all()
                        # 被教练相关者names
                        names = models.SysUser.objects.filter(pk__in=models.AppProbjectrelation.objects.filter(
                            ProbjectId=item.ProbjectId, UserId=item.UserId, RolesId=6).values_list('UserId', flat=True)
                                                              ).values_list('UserTrueName')
                        # 项目管理员邀请被教练者填写教练准备度 教练准备度-to student
                        if item.PageCode == 'r112':
                            dic["path_link"] = get_exam_r112_student(probjectid, 0)

                            push_message.delay(uinfo, 'exam_r112_student', dic, probjectid)

                        # LBI 1: 自评，2: 他评
                        if item.ExId == EX_SELF:
                            dic["path_link"] = get_exam_self_link(probjectid)
                            if not uinfo.Role_Id == 4:
                                dic['user_name'] = '用户名：' + uinfo.UserName
                                dic['user_password'] = '密码：' + password
                            push_message.delay(uinfo, 'exam_self_student', dic, probjectid)
                # 利益相关者调研-to interest（8）
                if exam_r24_student_dic:
                    exam_r24_student = 'exam_r24_student'
                    for k, v in exam_r24_student_dic.items():
                        interest = models.SysUser.objects.get(User_Id=k)
                        v['path_link'] = get_exam_r24_link(k, probjectid, v['interested_id'], v['flag'])
                        push_message.delay(interest, exam_r24_student, v, probjectid)
                # 项目管理员邀请用户填写改变观察（19）
                if exam_r45_student_dic:
                    exam_r45_student = 'exam_r45_student'
                    for k, v in exam_r45_student_dic.items():
                        interest = models.SysUser.objects.get(pk=k)
                        v['path_link'] = get_exam_r45_link(k, probjectid, v['interested_id'], v['flag'])
                        push_message.delay(interest, exam_r45_student, v, probjectid)

                # 发送LBI 他评
                if exam_other_dic:
                    exam_other = 'exam_other'
                    for k, v in exam_other_dic.items():
                        interest = models.SysUser.objects.get(User_Id=k)
                        v['path_link'] = get_exam_other_link(k, probjectid, v['interested_id'], v['flag'])
                        push_message.delay(interest, exam_other, v, probjectid)
            except Exception as e:
                print(e)
            return Response(
                response.Content().ok().raw()
            )
        else:
            return Response(
                response.Content().error('已经邀约过该用户了，重复添加了').raw()
            )

    @action(methods=['get'], detail=False)
    def TxEavluteYQ(self, request):
        Id = int_arg(request.query_params.get('Id'), 0)
        item = models.AppProbjectexamrelationuser.objects.filter(Id=Id).first()
        if not item:
            return Response(
                response.Content().error('不存在的邀请数据').raw()
            )
        dic = {
            'title': item.ExName,
        }
        invitedate = value_or_default(item.EndDate, datetime.datetime.now())
        dic['invitedate'] = invitedate.strftime('%Y-%m-%d')
        uinfo = models.SysUser.objects.filter(User_Id=item.UserId).first()
        pinfo = get_project_info_by_user(item.UserId)
        minfo = models.SysUser.objects.filter(User_Id=pinfo.Relation_UserId).first()
        if minfo:
            dic["manager"] = minfo.UserTrueName
            dic["mobile"] = minfo.PhoneNo
            dic["email"] = minfo.Email
        push_message.delay(uinfo, 'inviteevaluation', dic, pinfo.pk)
        return Response(
            response.Content().ok().raw()
        )

    @action(methods=['post'], detail=False)
    def getpagedata(self, request):
        t = self.get_page_rows(request.data)
        if t.count() > 0:
            extra = []
            for item in t.all():
                dic = {
                    "eId": item.Id,
                    'ModeCode': get_model_code(value_or_default(item.TypeId, 0), item.ExId)
                }
                extra.append(dic)
            t.extra_list = extra
        return Response(
            self.page_data(t)
        )

    @action(methods=['post'], detail=False)
    def updatestatus(self, request):
        id = int_arg(request.query_params.get('id'), None)
        status = int_arg(request.query_params.get('status'), None)
        if id and id == 0:
            return Response(
                response.Content().error('非法 id').raw()
            )
        data = None
        res = models.AppProbjectexam.objects.filter(pk=id).first()
        if res:
            res.Enable = status
            res.save()
            data = self.serializer_class(res, many=False).data
        return Response(
            response.Content().ok('', data).raw()
        )
