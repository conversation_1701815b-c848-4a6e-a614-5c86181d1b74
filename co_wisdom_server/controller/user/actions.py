from django.contrib.auth import authenticate, login
from django.contrib.auth.models import User as sys_user

from data import models
from utils import int_arg


def get_default_project_to_student(uid):
    pid_list = models.AppProbjectrelation.objects.filter(UserId=uid).values_list('ProbjectId', flat=True)
    enable_pid_list = models.AppProbject.objects.filter(Probject_Id__in=pid_list, Enable=1).order_by('-Probject_Id').values_list('Probject_Id', flat=True)
    relation = models.AppProbjectrelation.objects.filter(UserId=uid, ProbjectId__in=enable_pid_list).first()
    dic = {}
    if relation:
        dic["Probject_Id"] = int(relation.ProbjectId)
        dic["CoachId"] = int(relation.CoachId)
        dic["MessageId"] = ""
        dic["CoachImg"] = ""
        dic["CoachTrueName"] = ""
    coachid = int_arg(dic.get('CoachId'), 0)
    if coachid > 0:
        inf = models.SysUser.objects.filter(User_Id=coachid).first()
        if inf:
            dic["MessageId"] = inf.MessageId
            dic["CoachImg"] = inf.HeadImageUrl
            dic["CoachTrueName"] = inf.UserTrueName
    return dic


def get_children_name(roleId):
    role = models.SysRole.objects.filter(Role_Id=roleId).first()
    if role:
        return role.RoleName
    return ''


def get_roles(roleid):
    if roleid == 1:
        return 'SuperAdmin'.lower()
    elif roleid == 2:
        return 'SAdmin'.lower()
    elif roleid == 3:
        return 'ProbjectAdmin'.lower()
    elif roleid == 4:
        return 'Coach'.lower()
    elif roleid == 5:
        return 'BusinessAdmin'.lower()
    elif roleid == 6:
        return 'Student'.lower()
    elif roleid == 7:
        return 'Interested'.lower()
    elif roleid == 8:
        return 'Evaluator'.lower()
    return ''


def update_django_user_pwd(user):
    django_user = sys_user.objects.filter(pk=user.User_Id).first()
    new_password = user.UserPwd
    django_user.set_password(new_password)
    django_user.save()


def add_sys_user(user):
    is_superuser = 0
    if user.Role_Id == 1:
        is_superuser = 1
    django_user = sys_user.objects.create_user(id=user.User_Id, username=user.UserName, email=user.Email,
                                               password=user.UserPwd, is_superuser=is_superuser,
                                               first_name=user.UserTrueName)
    return django_user


def sys_login(request, user):
    s_user = authenticate(username=user.UserName, password=user.UserPwd)
    if s_user is not None:
        login(request, s_user)
        return s_user
    return None


def get_user_menu(role_id):
    menus = []
    role_auth_set = models.RoleAuth.objects.filter(role_id=role_id)
    for role_auth in role_auth_set:
        menu = {"icon": role_auth.menu.icon, "id": role_auth.menu.id, "name": role_auth.menu.menu_name,
                "parentId": role_auth.menu.parent_id, "url": role_auth.menu.url,
                "permission": role_auth.auth_value.split(",")}
        menus.append(menu)
    return menus
