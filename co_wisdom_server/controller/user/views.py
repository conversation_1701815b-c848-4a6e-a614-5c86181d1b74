import redis
import datetime
from django.conf import settings
from django.db.models import Q
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework_simplejwt.tokens import RefreshToken

from controller.user.actions import get_default_project_to_student, get_children_name, get_roles, \
    get_user_menu, sys_login, update_django_user_pwd, add_sys_user
from data import models, serializers, extension
from utils import response, int_arg, value_or_default, aesencrypt, randomPassword, null_or_blank, aesdecrypt
from utils.easemob.api import EasemobAPI
from utils.easemob.common import enable_im
from utils.response import ResponseType
from utils.encrypt import EncryptData
from drf_yasg.utils import swagger_auto_schema
from drf_yasg import openapi
from utils.authentication import get_token

token_redis = redis.Redis.from_url(settings.TOKEN_LOGIN_REDIS)

# 展示ILDP一级菜单的项目
IS_SHOW_ILDP_TAB_PROJECT = [62, 53]


class SysUserViewSet(extension.ResponseViewSet):
    queryset = models.SysUser.objects.all()
    serializer_class = serializers.SysUserSerializer

    @action(methods=['post'], detail=False, authentication_classes=[])
    def login(self, request):
        username = request.data.get('userName')
        password = request.data.get('passWord').strip()
        if not username or not password:
            return Response(
                response.Content().error().lower_raw()
            )
        user = models.SysUser.objects.filter(UserName=username).first()
        if not user or aesencrypt(password) != user.UserPwd:
            return Response(
                response.Content().error_type(ResponseType.LoginError).lower_raw()
            )
        if user.Enable == 0:
            return Response(
                response.Content().error('账号等待审核中,请稍后重试').lower_raw()
            )
        s_user = sys_login(request, user)
        if s_user is None:
            return Response(
                response.Content().error('登录失败').lower_raw()
            )
        # # jwt token
        # refresh = RefreshToken.for_user(s_user)
        token = get_token(user)
        hxuser = ''
        hxpwd = ''
        if not null_or_blank(user.MessageId):
            hxuser = user.MessageId
            hxpwd = aesdecrypt(user.MessageIdPwd)
        companyid = 0
        cominfo = models.AppMember.objects.filter(User_Id=user.User_Id).first()
        if cominfo:
            companyid = int_arg(cominfo.Company_Id, 0)
        pinfo = get_default_project_to_student(user.User_Id)
        probjectid = 0
        coachid = 0
        mtbjr = 0
        interested_enable = 0
        coachmessageid = ''
        coachimg = ''
        coachtruename = ''
        if pinfo and len(pinfo) > 0:
            probjectid = int_arg(pinfo["Probject_Id"], 0)
            coachid = int_arg(pinfo["CoachId"], 0)
            coachmessageid = value_or_default(pinfo["MessageId"], '')
            coachimg = value_or_default(pinfo["CoachImg"], '')
            coachtruename = value_or_default(pinfo["CoachTrueName"], '')
        messages = models.AppMessage.objects.filter(accept_userid=user.User_Id).exclude(is_read=1)
        msgnumber = messages.count()
        prosetting = models.AppProbjectsetting.objects.filter(Probject_Id=probjectid, KeyName='mtbjr').first()
        if prosetting:
            mtbjr = int_arg(prosetting.KeyValue, 0)
        relation_exist = models.AppProbjectrelation.objects.filter(
            Q(Interested_Id=user.User_Id) | (Q(RolesId=7) & Q(UserId=user.User_Id))
            , ProbjectId=probjectid).exists()
        interested_enable = 1 if relation_exist else 0
        menus = get_user_menu(user.Role_Id)
        res = response.Content()
        is_show_ildp_tab = True if probjectid in IS_SHOW_ILDP_TAB_PROJECT else False
        data = {
            'userinfo':
                {
                    'token': token,
                    'userId': user.User_Id,
                    'userName': user.UserTrueName,
                    'userEmail': user.Email,
                    'img': user.HeadImageUrl,
                    'roles': get_roles(user.Role_Id),
                    'roleName': user.RoleName,
                    'roleid': user.Role_Id,
                    'msgnumber': msgnumber,
                    'hxuser': hxuser,
                    'hxpwd': hxpwd,
                    'companyid': companyid,
                    'probjectid': probjectid,
                    'coachid': coachid,
                    'coachmessageid': coachmessageid,
                    'coachimg': coachimg,
                    'coachtruename': coachtruename,
                    'mtbjr': mtbjr,
                    'interested_enable': interested_enable,
                    'is_show_ildp_tab': is_show_ildp_tab

                },
            'menus': menus
        }
        user.Token = token
        user.save()
        return Response(
            {
                "status": 301,
                'message': "ok",
                'data': data
            }
        )

    @action(methods=['post'], detail=False)
    def autologin(self, request):
        main_data = request.data.get('mainData')
        uid = int_arg(main_data.get('uid'), None)
        user = models.SysUser.objects.filter(User_Id=uid).first()
        if not user:
            return Response(
                response.Content().error().raw()
            )
        uinfo = user
        hxuser = ''
        hxpwd = ''
        if not null_or_blank(user.MessageId):
            hxuser = user.MessageId
            hxpwd = aesdecrypt(user.MessageIdPwd)
        companyid = 0
        cominfo = models.AppMember.objects.filter(User_Id=user.User_Id).first()
        if cominfo:
            companyid = int_arg(cominfo.Company_Id, 0)
        pinfo = get_default_project_to_student(user.User_Id)
        probjectid = 0
        coachid = 0
        coachmessageid = ''
        coachimg = ''
        coachtruename = ''
        if pinfo and len(pinfo) > 0:
            probjectid = int_arg(pinfo["Probject_Id"], 0)
            coachid = int_arg(pinfo["CoachId"], 0)
            coachmessageid = value_or_default(pinfo["MessageId"], '')
            coachimg = value_or_default(pinfo["CoachImg"], '')
            coachtruename = value_or_default(pinfo["CoachTrueName"], '')
        res = response.Content()
        res.data = {
            'userinfo':
                {
                    'token': user.Token,
                    'userId': user.User_Id,
                    'userName': user.UserTrueName,
                    'userEmail': user.Email,
                    'img': user.HeadImageUrl,
                    'roles': get_roles(user.Role_Id),
                    'roleName': user.RoleName,
                    'roleid': user.Role_Id,
                    'hxuser': hxuser,
                    'hxpwd': hxpwd,
                    'companyid': companyid,
                    'probjectid': probjectid,
                    'coachid': coachid,
                    'coachmessageid': coachmessageid,
                    'coachimg': coachimg,
                    'coachtruename': coachtruename
                }
        }
        return Response(
            res.ok_type(ResponseType.LoginSuccess).raw()
        )

    @action(methods=['post'], detail=False)
    def replaceToken(self, request):
        main_data = request.data.get('mainData')
        uid = int_arg(main_data.get('uid'), None)

    @action(methods=['post'], detail=False)
    def modifypwd(self, request):
        oldPwd = request.query_params.get('oldPwd')
        newPwd = request.query_params.get('newPwd')
        res = response.Content()
        res.lower_result = 1
        if null_or_blank(oldPwd):
            return Response(
                res.error('旧密码不能为空').raw()
            )
        if null_or_blank(newPwd):
            return Response(
                res.error('新密码不能为空').raw()
            )
        if len(newPwd) < 6:
            return Response(
                res.error('密码不能少于6位').raw()
            )
        if oldPwd == newPwd:
            return Response(
                res.error('新密码不能与旧密码相同').raw()
            )
        userId = request.user.pk
        userCurrent = models.SysUser.objects.filter(User_Id=userId).first()
        _oldPwd = aesencrypt(oldPwd)
        if _oldPwd != userCurrent.UserPwd:
            return Response(
                res.error('旧密码不正确').raw()
            )
        userCurrent.UserPwd = aesencrypt(newPwd)
        userCurrent.LastModifyPwdDate = datetime.datetime.now()
        userCurrent.save()
        update_django_user_pwd(userCurrent)
        return Response(
            res.ok('密码修改成功').raw()
        )

    @action(methods=['post'], detail=False)
    def resetpwd(self, request):
        userId = int_arg(request.query_params.get('userId'), None)
        newPwd = request.query_params.get('newPwd').strip()
        res = response.Content()
        res.lower_result = 1
        if not userId or userId <= 0:
            return Response(
                res.error('用户不能为空').raw()
            )
        if null_or_blank(newPwd):
            return Response(
                res.error('新密码不能为空').raw()
            )
        if len(newPwd) < 6:
            return Response(
                res.error('密码不能少于6位').raw()
            )

        userCurrent = models.SysUser.objects.filter(User_Id=userId).first()
        _newPwd = aesencrypt(newPwd)
        if _newPwd == userCurrent.UserPwd:
            return Response(
                res.error('新密码不能与旧密码相同').raw()
            )
        userCurrent.UserPwd = _newPwd
        userCurrent.LastModifyPwdDate = datetime.datetime.now()
        userCurrent.save()
        update_django_user_pwd(userCurrent)
        return Response(
            res.ok('密码重置成功').raw()
        )

    @action(methods=['post'], detail=False)
    def GetCurrentUserInfo(self, request):
        data = models.SysUser.objects.filter(User_Id=request.user.pk).first()
        data_dic = self.serializer_class(data, many=False).data
        data_dic['roles'] = get_roles(data.Role_Id)
        return Response(
            response.Content().ok('', data_dic).raw()
        )

    @action(methods=['post'], detail=False)
    def getpagedata(self, request):
        my_roleid = models.SysUser.objects.filter(User_Id=request.user.pk).first().Role_Id
        # sub_role_ids = get_all_children_roleid(my_roleid)
        # for where in json.loads(request.data["wheres"]):
        #     add_option(request.data, where['name'], where['value'], where['displayType'])
        # add_option(request.data, 'Role_Id', sub_role_ids, 'checkbox')
        t = self.get_page_rows(request.data)
        return Response(
            self.page_data(t)
        )

    @action(methods=['post'], detail=False)
    def add(self, request):
        main_data = request.data.get('mainData')
        roleId = int_arg(main_data.get('Role_Id'), 0)
        if roleId > 0:
            roleName = get_children_name(roleId)
            if null_or_blank(roleName):
                return Response(
                    response.Content().error('不能选择此角色').raw()
                )
            main_data['RoleName'] = roleName
        mobile = main_data.get('PhoneNo', '')
        # TODO check cell phone number
        # if not null_or_blank(mobile):
        #     cfmobile = models.SysUser.objects.filter(PhoneNo=mobile).exists()
        #     if cfmobile:
        #         return Response(
        #             response.Content().error('该手机号已经存在了').raw()
        #         )
        email = main_data.get('Email', '')
        # if not null_or_blank(email):
        #     cfemail = models.SysUser.objects.filter(Email=email).exists()
        #     if cfemail:
        #         return Response(
        #             response.Content().error('该邮箱已经存在了').raw()
        #         )
        pwd = randomPassword(6)
        UserName = main_data.get('UserName', '')
        if models.SysUser.objects.filter(UserName=UserName).exists():
            return Response(
                response.Content().error('用户名已经被注册').raw()
            )
        res = self.add_row(main_data)
        row = res.row
        if null_or_blank(row.UserPwd):
            row.UserPwd = aesencrypt(pwd)
        else:
            row.UserPwd = aesencrypt(row.UserPwd)
        row.save()
        # create sys user
        add_sys_user(row)
        res.data = self.serializer_class(row, many=False).data
        return Response(
            res.ok().raw()
        )

    @action(methods=['post'], detail=False, url_path='del')
    def Del(self, request):
        if request.user.Role_Id == 1:
            uid_list = request.data
            models.AppMember.objects.filter(User_Id__in=uid_list).all().delete()
            models.AppCoach.objects.filter(User_Id__in=uid_list).all().delete()
            models.SysUser.objects.filter(User_Id__in=uid_list).all().delete()
        return Response(
            response.Content().ok().raw()
        )

    @action(methods=['post'], detail=False, url_path='update')
    def Update_new(self, request):
        main_data = request.data.get('mainData')
        if main_data.get('RoleName'):
            del main_data['RoleName']
        roleId = int_arg(main_data.get('Role_Id'), 0)
        if roleId > 0:
            roleName = get_children_name(roleId)
            if null_or_blank(roleName):
                return Response(
                    response.Content().error('不能选择此角色').raw()
                )
            main_data['RoleName'] = roleName
        uid = int_arg(main_data.get('User_Id'), 0)
        mobile = main_data.get('PhoneNo', '')
        # TODO check cell phone number
        # if not null_or_blank(mobile):
        #     cfmobile = models.SysUser.objects.filter(PhoneNo=mobile).exclude(User_Id=uid).exists()
        #     if cfmobile:
        #         return Response(
        #             response.Content().error('该手机号已经存在了').raw()
        #         )
        email = main_data.get('Email', '')
        # if not null_or_blank(email):
        #     cfemail = models.SysUser.objects.filter(Email=email).exclude(User_Id=uid).exists()
        #     if cfemail:
        #         return Response(
        #             response.Content().error('该邮箱已经存在了').raw()
        #         )
        res = self.update_row(main_data)
        return Response(
            res.raw()
        )

    @action(methods=['post'], detail=False)
    def openhx(self, request):
        ids = request.data
        if ids is None or len(ids) == 0:
            return Response(
                response.Content().error('请选择开通的账户').raw()
            )
        user_list = models.SysUser.objects.filter(User_Id__in=ids)
        for user in user_list.all():
            if null_or_blank(user.MessageId):
                enable_im(user.User_Id)
        return Response(
            response.Content().ok('所选的账户开通IM成功').raw()
        )

    @action(methods=['post'], detail=False)
    def closehx(self, request):
        ids = request.data
        if ids is None or len(ids) == 0:
            return Response(
                response.Content().error('请选择需要关闭的账户').raw()
            )
        user_list = models.SysUser.objects.filter(User_Id__in=ids)
        for user in user_list.all():
            if not null_or_blank(user.MessageId):
                easemob_id = user.MessageId
                user.OpenIM = 0
                user.MessageId = ''
                user.MessageIdPwd = ''
                user.save()
                EasemobAPI().delete_user(easemob_id)
        return Response(
            response.Content().ok('所选的账户关闭IM成功').raw()
        )

    @swagger_auto_schema(
        operation_id='token登录接口',
        operation_summary='token登录接口',
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'code': openapi.Schema(type=openapi.TYPE_STRING, description='加密key'),
            }
        ),
        tags=['token登录接口']
    )
    @action(methods=['post'], detail=False, authentication_classes=[])
    def user_token_login(self, request):
        code = request.data.get('code')
        secret = EncryptData()
        res = secret.verify_token(code)
        if res and res.get('status') == 3:
            return Response(
                response.Content().error('该链接已失效').lower_raw()
            )
        if res:
            # 返回用户token, 帮用户登录一次(刷新用户token),检查是否是已删除token
            user = models.SysUser.objects.get(User_Id=res['uid'])
            sys_user = sys_login(request, user)
            token = get_token(user)
            # refresh = RefreshToken.for_user(sys_user)
            # token = refresh.access_token
            # jti = token.payload['jti']
            secret.update_md5(user, status=2)
            # 用户信息
            hxuser = ''
            hxpwd = ''
            if not null_or_blank(user.MessageId):
                hxuser = user.MessageId
                hxpwd = aesdecrypt(user.MessageIdPwd)
            companyid = 0
            cominfo = models.AppMember.objects.filter(User_Id=user.User_Id).first()
            if cominfo:
                companyid = int_arg(cominfo.Company_Id, 0)
            pinfo = get_default_project_to_student(user.User_Id)
            probjectid = 0
            coachid = 0
            mtbjr = 0
            interested_enable = 0
            coachmessageid = ''
            coachimg = ''
            coachtruename = ''
            if pinfo and len(pinfo) > 0:
                probjectid = int_arg(pinfo["Probject_Id"], 0)
                coachid = int_arg(pinfo["CoachId"], 0)
                coachmessageid = value_or_default(pinfo["MessageId"], '')
                coachimg = value_or_default(pinfo["CoachImg"], '')
                coachtruename = value_or_default(pinfo["CoachTrueName"], '')
            messages = models.AppMessage.objects.filter(accept_userid=user.User_Id).exclude(is_read=1)
            msgnumber = messages.count()
            prosetting = models.AppProbjectsetting.objects.filter(Probject_Id=probjectid, KeyName='mtbjr').first()
            if prosetting:
                mtbjr = int_arg(prosetting.KeyValue, 0)
            interested_interview = models.AppProbjectsetting.objects.filter(Probject_Id=probjectid, KeyName='mtlyxgz').first()
            if interested_interview:
                interested_enable = int_arg(interested_interview.KeyValue, 0)
            menus = get_user_menu(user.Role_Id)
            data = {
                'userinfo':
                    {
                        'token': token,
                        'userId': user.User_Id,
                        'userName': user.UserTrueName,
                        'userEmail': user.Email,
                        'img': user.HeadImageUrl,
                        'roles': get_roles(user.Role_Id),
                        'roleName': user.RoleName,
                        'roleid': user.Role_Id,
                        'msgnumber': msgnumber,
                        'hxuser': hxuser,
                        'hxpwd': hxpwd,
                        'companyid': companyid,
                        'probjectid': probjectid,
                        'coachid': coachid,
                        'coachmessageid': coachmessageid,
                        'coachimg': coachimg,
                        'coachtruename': coachtruename,
                        'mtbjr': mtbjr,
                        'interested_enable': interested_enable
                    },
                'menus': menus
            }
            user.Token = str(token)
            user.save()
            return Response({"status": 301, 'message': "ok", 'data': data})
        else:
            return Response(
                response.Content().error('链接格式错误').lower_raw()
            )

    @swagger_auto_schema(
        operation_id='设置token过期',
        operation_summary='设置token过期',
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'uid': openapi.Schema(type=openapi.TYPE_NUMBER, description='uid'),
            }
        ),
        tags=['token登录接口']
    )
    @action(methods=['post'], detail=False, authentication_classes=[])
    def del_token(self, request):
        user = models.SysUser.objects.get(pk=request.data.get('uid'))
        secret = EncryptData()
        # 更新token状态为删除状态
        if secret.update_md5(user, status=3):
            return Response(response.Content().ok('token已删除').raw())
        else:
            return Response(response.Content().error('token删除失败').raw())

    @swagger_auto_schema(
        operation_id='发放token',
        operation_summary='发放token',
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'uid': openapi.Schema(type=openapi.TYPE_NUMBER, description='uid'),
            }
        ),
        tags=['token登录接口']
    )
    @action(methods=['post'], detail=False, authentication_classes=[])
    def get_token(self, request):
        user = models.SysUser.objects.get(pk=request.data.get('uid'))
        secret = EncryptData().get_md5(user.User_Id, flag=True)
        return Response(response.Content().ok(data={'code': secret}).raw())
