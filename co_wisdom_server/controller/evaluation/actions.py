import datetime

from rest_framework.response import Response

from controller.evaluationquestion.actions import get_evaluation_detail
from controller.modeltemplate.actions import get_model_template_info
from controller.project.actions import get_project_info_by_user
from data import models
from utils import value_or_default, int_arg, null_or_blank, response


def get_model_code_by(typeid, exid):
    if typeid == 2 and exid > 0:
        return get_model_template_info(exid).PageCode
    return ''

def get_evaluation_info(evaluationid):
    eva = models.AppEvaluation.objects.filter(EvaluationId=evaluationid).first()
    if eva:
        return eva
    return models.AppEvaluation()