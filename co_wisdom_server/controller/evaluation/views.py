import datetime

from rest_framework.decorators import action
from rest_framework.response import Response

from controller.evaluation.actions import get_model_code_by
from controller.evaluationquestion.actions import get_evaluation_detail
from controller.project.actions import get_project_info_by_user
from data import models, serializers, extension
from utils import response, int_arg, value_or_default, null_or_blank, lower_dic
from django.db.models import Q


class EvaluationViewSet(extension.ResponseViewSet):
    queryset = models.AppEvaluation.objects.all()
    serializer_class = serializers.AppEvaluationSerializer

    @action(methods=['get'], detail=False)
    def getmylist(self, request):
        ProbjectId = int_arg(request.query_params.get('ProbjectId'), 0)
        typeid = int_arg(request.query_params.get('typeid'), 1)
        now = datetime.datetime.now()
        uid = request.user.pk
        if ProbjectId == 0:
            pino = get_project_info_by_user(uid)
            ProbjectId = int_arg(pino.Probject_Id, 0)
        evalist = models.AppProbjectexamrelationuser.objects.filter(UserId=uid)
        if typeid > 0:
            evalist = evalist.filter(TypeId=typeid)
        query = evalist
        if ProbjectId > 0:
            query = query.filter(ProbjectId=ProbjectId)
        query = query.order_by('-Id')
        data = query.values('ProbjectId', 'ExId', 'ExName', 'Status', 'CreateDate', 'EndDate', 'ReportId', 'TypeId',
                            'pici', 'interested_id')
        res_list = []
        for item in data:
            item['ModeCode'] = get_model_code_by(value_or_default(item.get('TypeId'), 0), item.get('ExId'))
            username = ''
            if int_arg(item.get('interested_id'), 0) > 0:
                interest_user = models.SysUser.objects.filter(User_Id=int_arg(item.get('interested_id'))).first()
                if interest_user:
                    username = interest_user.UserTrueName
            else:
                username = models.SysUser.objects.get(pk=request.user.pk).UserTrueName
            item['evaluation_user_name'] = username
            res_list.append(lower_dic(item))

        return Response(
            response.Content().ok('suc', res_list).raw()
        )

    @action(methods=['get'], detail=False)
    def getmyreport(self, request):
        ProbjectId = int_arg(request.query_params.get('ProbjectId'), 0)
        typeid = int_arg(request.query_params.get('typeid'), 1)
        groupid = models.SysUser.objects.filter(User_Id=request.user.pk).first().Role_Id
        now = datetime.datetime.now()
        uid = request.user.pk
        if ProbjectId == 0:
            pino = get_project_info_by_user(uid)
            ProbjectId = pino.Probject_Id
        evalist = models.AppProbjectexamrelationuser.objects.filter(Status__gte=1, TypeId=1, ReportId__gt=0, UserId=uid)
        if ProbjectId > 0:
            evalist = evalist.filter(ProbjectId == ProbjectId)
        data = evalist.values('ProbjectId', 'ExId', 'ExName', 'CreateDate', 'ReportId', 'TypeId',
                              'pici')
        for item in data:
            item['ReportId'] = value_or_default(item.get('ReportId'), 0)
            item['Pici'] = item.get('pici')
            item['ModeCode'] = get_model_code_by(value_or_default(item.TypeId, 0), item.ExId)
        modelreport = models.AppModelreportreport.objects.filter(status=1, UserId=uid, rolesid__contains=str(groupid))
        data_report = []
        for item in modelreport.all():
            data_report.append({
                'ProbjectId': item.ProbjectId,
                'ExId': item.ModelReportId,
                'ExName': item.title,
                'CreateDate': item.CreateDate,
                'ReportId': 0,
                'TypeId': 2,
                'Pici': item.pici,
                'ModeCode': item.ReportCode
            })
        if typeid == 0:
            return Response(
                response.Content().ok('suc', data).raw()
            )
        elif typeid == 1:
            return Response(
                response.Content().ok('suc', data).raw()
            )
        elif typeid == 2:
            return Response(
                response.Content().ok('suc', data_report).raw()
            )
        return Response(
            response.Content().raw()
        )

    @action(methods=['get'], detail=False)
    def getevalview(self, request):
        evaluationid = int_arg(request.query_params.get('evaluationid'))
        userid = int_arg(request.query_params.get('userid'))
        interestedid = int_arg(request.query_params.get('interestedid'), 0)
        pici = request.query_params.get('pici', '')
        eva = models.AppEvaluation.objects.filter(EvaluationId=evaluationid).first()
        evachild = get_evaluation_detail(evaluationid)
        result = []
        tmpresult = []
        if userid > 0 and not null_or_blank(pici):
            for item in evachild:
                res_list = models.AppEvaluationresult.objects.filter(EvaluationId=evaluationid, Userid=userid,
                                                                     EqId=item.EQid, pici=pici, Interested_Id=interestedid)
                if res_list.count() > 0:
                    result.append({
                        'answer': res_list[0].Answer
                    })
                else:
                    result.append({
                        'answer': '',
                        'other': ''
                    })
        elif userid > 0:
            for item in evachild:
                res_list = models.AppEvaluationtmpresult.objects.filter(EvaluationId=evaluationid, Userid=userid,
                                                                     EqId=item.EQid, Interested_Id=interestedid)
                if res_list.count() > 0:
                    tmpresult.append({
                        'answer': res_list[0].Answer,
                        'eqid': res_list[0].EqId
                    })
                else:
                    tmpresult.append({
                        'answer': '',
                        'eqid': 0
                    })
        listdata = []
        for item in evachild.all():
            options = models.AppEvaluationoption.objects.filter(EQid=item.pk).all()
            dic = serializers.AppEvaluationquestionSerializer(item, many=False).data
            lower_option_list = []
            if len(options) == 0:
                options = models.AppEvaluationoption.objects.filter(EQid=600).all()

            option_list = serializers.AppEvaluationoptionSerializer(options, many=True).data
            for o in option_list:
                lower_option_list.append(lower_dic(o))

            dic['app_EvaluationOption'] = lower_option_list
            listdata.append(lower_dic(dic))
        data = {
            'info': {
                'evaluationId': eva.EvaluationId,
                'evalName': eva.EvalName,
                'remark': eva.Remark
            },
            'list': listdata,
            'result': result,
            'tmpresult': tmpresult
        }
        return Response(
            response.Content().ok('suc', data).raw()
        )

    @action(methods=['get'], detail=False)
    def copyeval(self, request):
        evaluationid = int_arg(request.query_params.get('evaluationid'))
        eva = models.AppEvaluation.objects.filter(EvaluationId=evaluationid).first()
        evachild = get_evaluation_detail(evaluationid)
        eva.EvaluationId = 0
        eva.EvalName = eva.EvalName + '_副本'
        eva.pk = None
        eva.save()
        for item in evachild:
            x = item
            x.EvaluationId = eva.EvaluationId
            x.EQid = None
            x.save()
            option_list = models.AppEvaluationoption.objects.filter(EQid=item.pk)
            for option in option_list.all():
                option.EQid = x.pk
                option.pk = None
                option.save()
        return Response(
            response.Content().ok().raw()
        )
