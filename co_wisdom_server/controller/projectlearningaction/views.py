import datetime

from django.db.models import F
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.viewsets import ModelViewSet, ViewSet, GenericViewSet

from controller.dictionary.actions import get_dictionary_list_value
from controller.project.actions import get_start_project_num_by_company, get_complete_project_num_by_company, \
    get_sign_project_num_by_company, get_project_info
from controller.resource.actions import get_resources_info
from data import models, serializers, extension
from utils import response, blank_to_none, null_or_blank, int_arg, value_or_default
import json

from utils.messagecenter.center import push_message
from utils.model import add_option
from utils.queryset import combine_values
from utils.user import get_user_ids

def get_cate(resid):
    behaviour = ''
    resinfo = models.AppResources.objects.filter(ResId=resid).first()
    if resinfo:
        behaviour = get_dictionary_list_value('behaviour', resinfo.Cate2)
    return behaviour


class ProjectLearningActionViewSet(extension.ResponseViewSet):
    queryset = models.AppProbjectlearningaction.objects.all()
    serializer_class = serializers.AppProbjectlearningactionSerializer

    @action(methods=['post'], detail=False)
    def getpagedatafront(self, request):
        t = self.get_page_rows(request.data)
        return Response(
            self.page_data(t)
        )

    @action(methods=['post'], detail=False)
    def getpagedata(self, request):
        for where in json.loads(request.data["wheres"]):
            if where['name'] == 'Cate1':
                ids = models.AppResources.objects.filter(Enable=1, IsShow=True, Cate1__iexact=where['value']).values_list('ResId', flat=True)
                if len(ids) > 0:
                    add_option(request.data, 'ResId', ids, 'checkbox')
            if where['name'] == 'Cate2':
                ids = models.AppResources.objects.filter(Enable=1, IsShow=True, Cate2__iexact=where['value']).values_list('ResId', flat=True)
                if len(ids) > 0:
                    add_option(request.data, 'ResId', ids, 'checkbox')
            if where['name'] == 'Category':
                cateid = int(where['value'])
                ids = models.AppResources.objects.filter(Enable=1, IsShow=True, CategoryId=cateid).values_list('ResId', flat=True)
                if len(ids) > 0:
                    add_option(request.data, 'ResId', ids, 'checkbox')
            elif where['name'] == 'ProName':
                ids = models.AppProbject.objects.filter(Enable=1, Name__icontains=where['value']).values_list(
                    'Probject_Id', flat=True)
                if len(ids) > 0:
                    add_option(request.data, 'ProbjectId', ids, 'checkbox')
            else:
                add_option(request.data, where['name'], where['value'], where['displayType'])
        t = self.get_page_rows(request.data)
        if t.count() > 0:
            extra = []
            for item in t.all():
                dic = {}
                dic["eId"] = item.Id
                dic["ProName"] = get_project_info(item.ProbjectId).Name
                if item.ResId > 0:
                    resinfo = get_resources_info(value_or_default(item.ResId, 0))
                    if resinfo:
                        behaviour = get_dictionary_list_value('behaviour', resinfo.Cate2)
                        dic["behaviour"] = behaviour
                        dic["Description"] = resinfo.Description
                extra.append(dic)
            t.extra_list = extra
        return Response(
            self.page_data(t)
        )

    @action(methods=['post'], detail=False)
    def SaveShare(self, request):
        main_data = request.data.get('mainData')
        ResId = int_arg(main_data.get('ResId'), None)
        if ResId == 0:
            return Response(
                response.Content().error('请选择分享的内容').raw()
            )
        resinfo = get_resources_info(ResId)
        if type(resinfo.IsShow) == bool and resinfo.IsShow == False:
            return Response(
                response.Content().error('此内容禁止分享').raw()
            )
        interviewId = int_arg(main_data.get('interviewId'), 0)
        userids = main_data.get('UserIds').split(',')
        CoachId = int_arg(main_data.get('CoachId'), None)
        for item in userids:
            arr = item.split('_')
            ino = models.AppProbjectlearningaction()
            ino.ProbjectId = int(arr[0])
            ino.User_Id = int(arr[1])
            ino.Coach_Id = CoachId
            ino.ResId = resinfo.ResId
            ino.Title = resinfo.Title
            ino.LearningPlan = resinfo.Content
            ino.interviewId = interviewId
            ino.CreateDate = datetime.datetime.now()
            ino.Creator = request.user.UserName
            if not models.AppProbjectlearningaction.objects.filter(ProbjectId=ino.ProbjectId, User_Id=ino.User_Id, Coach_Id=ino.Coach_Id, ResId=ino.ResId).exists():
                ino.save()
                # 消息通知
                dic = {
                    'title': ino.Title
                }
                uinfo = models.SysUser.objects.filter(User_Id=ino.User_Id).first()
                push_message.delay(uinfo, 'studyplan', dic)
        return Response(
            response.Content().ok().raw()
        )

    @action(methods=['post'], detail=False)
    def CompateLearningAction(self, request):
        main_data = request.data.get('mainData')
        uid = request.user.pk
        id = int_arg(main_data.get('Id'), None)
        note = main_data.get('note')
        if id > 0:
            ifo = models.AppProbjectlearningaction.objects.filter(User_Id=uid, Id=id).first()
            if ifo:
                IsCompateLearningPlan = int_arg(main_data.get('IsCompateLearningPlan'), 0)
                if IsCompateLearningPlan > 0:
                    ifo.IsCompateLearningPlan = IsCompateLearningPlan
                IsCompateActionPlan = int_arg(main_data.get('IsCompateActionPlan'), 0)
                if IsCompateActionPlan > 0:
                    ifo.IsCompateActionPlan = IsCompateActionPlan
                LearningReport = main_data.get('LearningReport', '')
                if null_or_blank(LearningReport):
                    ifo.LearningReport = LearningReport
                ActionReport = main_data.get('ActionReport', '')
                if null_or_blank(ActionReport):
                    ifo.ActionReport = ActionReport
                ActionReportReview = main_data.get('ActionReportReview', '')
                if null_or_blank(ActionReportReview):
                    ifo.LearningReport = ActionReportReview

                ifo.save()
                # 完成任务通知教练 消息
                cinfo = models.SysUser.objects.filter(User_Id=ifo.Coach_Id).first()
                uinfo = models.SysUser.objects.filter(User_Id=ifo.User_Id).first()
                dic = {
                    'title': uinfo.UserTrueName + '已更新' + ifo.Title + '的学习',
                    'msgtype': 'completestudy'
                }
                push_message.delay(cinfo, 'notice', dic)

                # update notes
                if note:
                    new_note = models.AppLearningActionNote(learning_action=ifo, note=note)
                    new_note.save()

                return Response(
                    response.Content().ok().raw()
                )
        return Response(
            response.Content().error('无效的学习任务').raw()
        )

    @action(methods=['get'], detail=False)
    def GetMyLearningTags(self, request):
        uid = request.user.pk
        action_list = models.AppProbjectlearningaction.objects.filter(User_Id=uid, ResId__gt=0)
        query = action_list.order_by('Id').values_list('ResId', flat=True)[:100]
        reslist_behaviour = set(models.AppResources.objects.filter(ResId__in=query).exclude(Cate2=None).values_list('Cate2', flat=True))
        listtags = []
        for item in reslist_behaviour:
            _aname = get_dictionary_list_value('behaviour', item)
            dic = {}
            dic["category"] = 2
            dic["key"] = item
            dic["value"] = _aname
            listtags.append(dic)
        return Response(
            response.Content().ok(None, listtags).raw()
        )

    @action(methods=['get'], detail=False)
    def GetMyLearningByInterView(self, request):
        uid = int_arg(request.query_params.get('uid'))
        interviewid = int_arg(request.query_params.get('interviewId'))
        probjectid = int_arg(request.query_params.get('probjectid'))
        type = int_arg(request.query_params.get('type'), 0)
        _list = models.AppProbjectlearningaction.objects.filter(User_Id=uid, interviewId=interviewid, ProbjectId=probjectid)
        if type == 1:
            query = _list.filter(ResId=None).values('Id', 'Title', 'ActionPlan', 'CreateDate', 'IsCompateActionPlan',
                                                    'applearningactionnote__note', 'applearningactionnote__ModifyDate')
            query = combine_values(query, 'Id', 'notes',
                                   ['applearningactionnote__note', 'applearningactionnote__ModifyDate'],
                                   ['note', 'date'])
            return Response(
                response.Content().ok('SUC', query).raw()
            )
        else:
            query = _list.filter(ResId__gt=0).values('Id', 'Title', 'LearningPlan', 'CreateDate', 'IsCompateActionPlan', 'IsCompateLearningPlan', 'ResId')
            for item in query:
                cate = get_cate(value_or_default(item.get('ResId'), 0))
                item['cate'] = cate
            return Response(
                response.Content().ok('SUC', query).raw()
            )

    @action(methods=['get'], detail=False)
    def GetMyLearning(self, request):
        uid = int_arg(request.query_params.get('uid'))
        probjectid = int_arg(request.query_params.get('probjectid'))
        type = int_arg(request.query_params.get('type'), 0)
        _list = models.AppProbjectlearningaction.objects.filter(User_Id=uid, ProbjectId=probjectid)
        if type == 1:
            query = _list.filter(ResId=None).values('Id', 'Title', 'ActionPlan', 'CreateDate', 'IsCompateActionPlan', 'applearningactionnote__note', 'applearningactionnote__ModifyDate')
            query = combine_values(query, 'Id', 'notes', ['applearningactionnote__note', 'applearningactionnote__ModifyDate'], ['note', 'date'])
            return Response(
                response.Content().ok('SUC', query).raw()
            )
        else:
            query = _list.filter(ResId__gt=0).values('Id', 'Title', 'LearningPlan', 'CreateDate', 'IsCompateActionPlan',
                                                     'IsCompateLearningPlan', 'ResId')
            for item in query:
                cate = get_cate(value_or_default(item.get('ResId'), 0))
                item['cate'] = cate
            return Response(
                response.Content().ok('SUC', query).raw()
            )