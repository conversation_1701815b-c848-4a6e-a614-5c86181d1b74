from django.db.models import Sum, F, Count

from data import models
from utils import response


def get_need_item(probjectid, userid=0):
    where = 'WHERE ProbjectId='+str(probjectid)
    proneeds = models.AppProbjectneed.objects.filter(ProbjectId=probjectid)
    if userid > 0:
        where += ' AND UserId='+str(userid)
        proneeds = proneeds.filter(UserId=userid)
    res_list = []
    if proneeds.count() > 0:
        dic = {
            'sex': 0,
            'ages': 0,
            'education': 1,
            'jlzz': 1,
            'cynx': 0,
            'xyjb': 1,
            'companyattr': 1,
            'jlxssOne': 0,
            'jlxssTearm': 0,
            'jlhy': 1,
            'objective': 1,
            'jlqyhy': 1,
            'jlzw': 1,
            'jlgzhy': 1
        }
        for k, v in dic.items():
            if v == 1:
                sql = 'SELECT Id, '+k+' AS k, COUNT('+k+') AS c FROM app_probjectneed '+where+' GROUP BY '+k+' ORDER BY c desc'
                query = models.AppProbjectneed.objects.raw(sql)
                rank = 0
                if len(query) > 1:
                    rank = query[0].c
                val_list = []
                for item in query:
                    if item.k and item.k not in val_list:
                        val_list.append(item.k)
                if val_list:
                    res_list.append({
                        'Key': k,
                        'Value': ','.join(val_list),
                        'Rank': rank / proneeds.count()
                    })
    res = response.Content()
    return res.ok('SUC', res_list)
