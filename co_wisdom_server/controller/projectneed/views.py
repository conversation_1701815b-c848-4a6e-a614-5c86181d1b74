from rest_framework.decorators import action
from rest_framework.response import Response

from controller.company.actions import get_company_info
from controller.member.actions import get_member_info
from controller.project.actions import get_project_info
from data import models, serializers, extension
from utils import int_arg, response
from utils.messagecenter.center import push_message


class ProjectNeedViewSet(extension.ResponseViewSet):
    queryset = models.AppProbjectneed.objects.all()
    serializer_class = serializers.AppProbjectneedSerializer

    @action(methods=['post'], detail=False)
    def getpagedata(self, request):
        t = self.get_page_rows(request.data)
        if t.count() > 0:
            extra = []
            for item in t.all():
                dic = {}
                dic["eId"] = item.Id
                uinfo = get_member_info(item.UserId)
                if uinfo:
                    dic["Photograph"] = uinfo.Photograph
                    dic["TrueName"] = uinfo.TrueName
                    dic["CompanyName"] = uinfo.CompanyName

                dic["ProName"] = get_project_info(item.ProbjectId).Name
                extra.append(dic)
            t.extra_list = extra
        return Response(
            self.page_data(t)
        )

    @action(methods=['post'], detail=False)
    def add(self, request):
        main_data = request.data.get('mainData')
        main_data['UserId'] = request.user.pk
        t = self.add_row(main_data)
        if t.status:
            # 发送通知给项目负责人
            projectid = int_arg(main_data['ProbjectId'], 0)
            projectinfo = get_project_info(projectid)
            if projectinfo and projectinfo.Relation_UserId and projectinfo.Relation_UserId > 0:
                uinfo = models.SysUser.objects.filter(User_Id=projectinfo.Relation_UserId).first()
                if uinfo:
                    dic = {
                        'probjectname': projectinfo.Name,
                        'companyname': get_company_info(projectinfo.Company_Id).ShortName,
                        'isnew': '提交',
                        'tips': '',
                        'companymanager': request.user.UserName,
                        'mamager': uinfo.UserTrueName,
                        'email': request.user.email
                    }
                    push_message.delay(uinfo, 'needcoach', dic, projectid)
                    # 通知企业收到反馈
                    qinfo = models.SysUser.objects.filter(User_Id=request.user.pk).first()
                    if qinfo:
                        push_message.delay(qinfo, 'needcoachreply', {
                            'probjectname': projectinfo.Name,
                            'companyname': get_company_info(projectinfo.Company_Id).ShortName,
                            'companymanager': request.user.UserName,
                            'mamager': uinfo.UserTrueName,
                            'email': request.user.email
                        }, projectid)

        return Response(
            t.raw()
        )

    @action(methods=['get'], detail=False)
    def TxNeedCoach(self, request):
        projectid = int_arg(request.query_params.get('probjectid'), 0)
        projectinfo = get_project_info(projectid)
        if projectinfo and projectinfo.Relation_UserId and projectinfo.Relation_UserId > 0:
            uinfo = models.SysUser.objects.filter(User_Id=projectinfo.Relation_UserId).first()
            if uinfo:
                dic = {
                    'probjectname': projectinfo.Name,
                    'companyname': get_company_info(projectinfo.Company_Id).ShortName,
                    'isnew': '提交',
                    'tips': '',
                    'companymanager': request.user.UserName,
                    'mamager': uinfo.UserTrueName,
                    'email': request.user.email
                }
                push_message.delay(uinfo, 'needcoach', dic, projectinfo.pk)
        return Response(
            response.Content().ok().raw()
        )

    @action(methods=['post'], detail=False, url_path='update')
    def update_new(self, request):
        main_data = request.data.get('mainData')
        main_data['UserId'] = request.user.pk
        res = self.update_row(main_data)
        if res.status:
            projectid = int_arg(main_data.get('ProbjectId'), 0)
            projectinfo = get_project_info(projectid)
            if projectinfo and projectinfo.Relation_UserId and projectinfo.Relation_UserId > 0:
                uinfo = models.SysUser.objects.filter(User_Id=projectinfo.Relation_UserId).first()
                if uinfo:
                    dic = {
                        'probjectname': projectinfo.Name,
                        'companyname': get_company_info(projectinfo.Company_Id).ShortName,
                        'isnew': '修改',
                        'tips': '请根据新的需求',
                        'companymanager': request.user.UserName,
                        'mamager': uinfo.UserTrueName,
                        'email': request.user.email
                    }
                    push_message.delay(uinfo, 'needcoach', dic, projectinfo.pk)
        return Response(
            res.raw()
        )

    @action(methods=['get'], detail=False)
    def GetProbjectNeedInfo(self, request):
        projectid = int_arg(request.query_params.get('probjectid'), 0)
        userid = int_arg(request.query_params.get('userid'), 0)
        info = models.AppProbjectneed.objects.filter(ProbjectId=projectid, UserId=userid).first()
        if info:
            coach_exist = models.Projectcoach.objects.filter(ProbjectId=projectid, UserId=userid).exists()
            dic = self.serializer_class(info, many=False).data
            dic['noNeed'] = 0
            if coach_exist:
                dic['noNeed'] = 1
            return Response(
                response.Content().ok(data=dic).raw()
            )
        return Response(
            response.Content().error('还未添加教练需求').raw()
        )