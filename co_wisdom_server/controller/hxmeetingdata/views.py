import datetime

from rest_framework.decorators import action
from rest_framework.response import Response

from controller.hxmeetingdata.actions import get_hxmeeting_data
from data import models, serializers, extension
import json

from utils import int_arg, response


class HXMeetingDataViewSet(extension.ResponseViewSet):
    queryset = models.AppHxmeetingdata.objects.all()
    serializer_class = serializers.AppHxmeetingdataSerializer

    @action(methods=['post'], detail=False)
    def UpdateInterViewHxData(self, request):
        main_data = request.data.get('mainData')
        TypeId = int_arg(main_data.get('TypeId'), 0)
        HxData = main_data.get('HxData', '')
        InterViewId = int_arg(main_data.get('InterViewId'), 0)
        pagecode = str(TypeId) + '_' + str(InterViewId)
        _model = get_hxmeeting_data(pagecode)
        if _model and _model.Id > 0:
            _model.HxData = HxData
            _model.save()
        else:
            main_data['PageCode'] = pagecode
            self.add_row(main_data)
        return Response(
            response.Content().ok().raw()
        )

    @action(methods=['get'], detail=False)
    def GetInterViewHxData(self, request):
        pagecode = request.query_params.get('pagecode')
        ret = '{}'
        _model = get_hxmeeting_data(pagecode)
        if _model and _model.Id > 0:
            ret = _model.HxData
        return Response(
            response.Content().ok('SUC', ret).raw()
        )