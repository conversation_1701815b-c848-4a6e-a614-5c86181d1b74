import datetime

import numpy as np
from django.conf import settings
from rest_framework.response import Response
from django.db.models import Avg
from controller.project.actions import get_project_info
from data import models
from utils import response
from utils.messagecenter.center import push_message

SPIDER_MAP = {
            'A': '突破进取',
            'B': '抗压复原',
            'C': '自我成长',
            'D': '敏捷应变',
            'E': '引领变革',
            'F': '战略远见',
            'G': '激励赋能',
            'H': '发展下属',
            'J': '果敢决策',
            'K': '系统思考',
            'L': '多方协同',
            'M': '集体担责',
        }

def tx_company_complete_report(reportid):
    pinfo = models.AppProbjectreport.objects.filter(Id=reportid).first()
    if not pinfo:
        return Response(
            response.Content().error('不存在的报告').raw()
        )
    relation = models.AppProbjectrelation.objects.filter(ProbjectId=pinfo.ProbjectId, IsManger=1)
    query = relation.values_list('UserId', flat=True)
    if len(query) > 0:
        prinfo = get_project_info(pinfo.ProbjectId)
        for item in query:
            uinfo = models.SysUser.objects.filter(User_Id=item).first()
            if uinfo:
                dic = {'probjectname': prinfo.Name,
                       'probjectid': prinfo.Probject_Id,
                       'starttime': pinfo.starttime,
                       'endtime': pinfo.endtime,
                       'url': settings.SITE_URL + 'cadmin/report'
                       }
                push_message.delay(uinfo, 'prolabreporttocompany', dic, project_id=prinfo.Probject_Id)
    return response.Content().ok()


def get_all_working_time(users):
    """获取所有用户总工作年限"""
    all_working_time = 0
    if users:
        for user in users:
            all_working_time += datetime.datetime.now().year - user.WorkingDate.year
    return all_working_time


def get_circle(title, user_count, sub_title, is_line=None):
    """
    title: 圆主题
    user_count 测评人数
    sub_title 圆下方小标题 list 0: 颜色 1: 数量（求百分比用）
    is_line 是否带引线的圆
    示例：{'5年以内': ['#EE6666',  3], '10年以内': ['#5470c6',  4], '15年以内': ['#fac858',  5], '超过15年': ['#91cc75',  6]}
    """
    values_content = []
    subtitle = []
    color_set = ['#5470c6', '#EE6666', '#fac858', '#91cc75', '#73c0de']
    for item in sub_title:
        dic_content = {}
        dic_obj = {}
        if item['value']:
            ratio = str(int(item['value'] / user_count * 100 + 0.5)) if item['value'] else '0'
            dic_obj['color'] = color_set[len(subtitle) % len(color_set)]
            dic_obj['value'] = ratio
            dic_content['color'] = color_set[len(subtitle) % len(color_set)]
            circle_value = item['label'] + ratio + '%'
            dic_content['value'] = ratio
            dic_content['label'] = circle_value
            subtitle.append(dic_obj)
            values_content.append(dic_content)
    dic = {'class': 'circle', 'title': '', 'subtitle': '',
           'values': values_content, 'value_desc': subtitle}
    return dic


def get_json(queryset, evaluation_id=None):
    # A突破进取 B抗压复原 C自我成长 D敏捷应变 E引领变革 F战略远见 G激励赋能 H发展下属 J果敢决策 K系统思考 L多方协同 M集体担责
    dic = {
        'A': {'sort': [24, 27, 30, 50], 'score': [], 'user_id': []},
        'B': {'sort': [15, 16, 23, 39, 55, 62, 74, 77], 'score': [], 'user_id': []},
        'C': {'sort': [2, 11, 14, 26, 38, 70], 'score': [], 'user_id': []},
        'D': {'sort': [5, 17, 18, 29, 54, 65, 76], 'score': [], 'user_id': []},
        'E': {'sort': [6, 9, 13, 25, 31, 78], 'score': [], 'user_id': []},
        'F': {'sort': [3, 4, 10, 22, 28, 40, 58], 'score': [], 'user_id': []},
        'G': {'sort': [7, 19, 43, 49, 63, 64, 67, 69], 'score': [], 'user_id': []},
        'H': {'sort': [8, 32, 44, 51, 68], 'score': [], 'user_id': []},
        'J': {'sort': [1, 33, 41, 52], 'score': [], 'user_id': []},
        'K': {'sort': [21, 42, 45, 56, 61, 66, 71, 73, 75], 'score': [], 'user_id': []},
        'L': {'sort': [12, 35, 36, 37, 47, 60, 72], 'score': [], 'user_id': []},
        'M': {'sort': [20, 34, 46, 48, 53, 57, 59], 'score': [], 'user_id': []},
    }
    if evaluation_id:
        queryset = queryset.filter(EvaluationId=evaluation_id)
    for r in queryset:
        for _k, _v in dic.items():
            if r.tIndex in _v['sort']:
                dic[_k]['score'].append(r.Score)
                dic[_k]['user_id'].append(r.Userid)
    return dic


def get_default_histogram(query_dict, other_dict):
    default_histogram_list = []
    for adv_k, adv in query_dict.items():
        default_histogram = {'class': 'bar', 'title': SPIDER_MAP[adv_k],
                             'splitArea': False, 'is_overlap': False,
                             'fixed_text': '', 'top_text_list': '',
                             'steps': [],
                             'axisx': [{'label': '', 'bar_label': [1, 2, 3, 4, 5, 6], 'range': [-2, 3]}],
                             'axisy': [{'label': '', 'bar_label': ['自评', '下级', '平级', '上级']}],
                             'bar': []
                             }
        default_bar = {'value': [], 'color': [], 'label': []}
        default_bar['value'].append(round(adv['avg'] - 3, 1))
        default_bar['label'].append(adv['avg'])
        default_bar['color'].append('#5470c6')
        bar_label = ['自评']
        for other_k, other_v in other_dict.items():
            if adv_k == other_k:
                # 先获取该领导力对应的所有问题
                # 再获取问题的所有他评回答
                # 根据回答者和测评对象的关系做分组
                # 计算各分组的平均分
                eq_id_list = models.AppEvaluationquestion.objects.filter(EvaluationId=18, behaviour=adv_k).values_list('EQid', flat=True)
                res = models.AppEvaluationresult.objects.filter(Userid__in=other_v['user_id'],
                                                                EvaluationId=18,
                                                                Interested_Id__in=adv['user_id'],
                                                                EqId__in=eq_id_list).values('Userid', 'Score', 'Interested_Id')
                first_list = []
                second_list = []
                third_list = []
                for item in res:
                    memeber = models.AppMemberinterested.objects.filter(Member_Id=item['Userid'],
                                                                        MasterMember_Id=item['Interested_Id']).first()

                    if memeber.Relation == '下级':
                        third_list.append(item['Score'])

                    if memeber.Relation == '平级':
                        second_list.append(item['Score'])

                    if memeber.Relation == '上级':
                        first_list.append(item['Score'])

                # 根据插入顺序决定页面显示的顺序
                if third_list:
                    default_bar['value'].append(round(np.mean(third_list) - 3, 1))
                    default_bar['color'].append('#fac858')
                    default_bar['label'].append(round(np.mean(third_list), 1))
                    bar_label.append('下级')

                if second_list:
                    default_bar['value'].append(round(np.mean(second_list) - 3, 1))
                    default_bar['color'].append('#91cc75')
                    default_bar['label'].append(round(np.mean(second_list), 1))
                    bar_label.append('平级')

                if first_list:
                    default_bar['value'].append(round(np.mean(first_list) - 3, 1))
                    default_bar['color'].append('#73c0de')
                    default_bar['label'].append(round(np.mean(first_list), 1))
                    bar_label.append('上级')


        default_histogram['bar'].append(default_bar)
        # 根据实际关系设置轴上的文案
        default_histogram['axisy'][0]['bar_label'] = bar_label
        default_histogram_list.append(default_histogram)
    return default_histogram_list
