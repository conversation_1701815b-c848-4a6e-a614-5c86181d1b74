import datetime
import json
import numpy as np

from drf_yasg import openapi
from drf_yasg.utils import swagger_auto_schema
from dateutil.relativedelta import relativedelta
from django.db.models import Avg, Su<PERSON>, Max, F, Count, Q
from rest_framework.decorators import action
from rest_framework.response import Response

from controller.projectreport.actions import tx_company_complete_report
from data import models, serializers, extension
from utils import response, int_arg
from .actions import get_all_working_time, get_circle, get_json, get_default_histogram
from collections import Counter
from django.db.models import Variance
from utils.messagecenter.center import push_message
from utils.messagecenter.actions import get_lbi_report_to_manage


def get_two_number(input, num=2):
    return round(input, num)


class ProjectReportViewSet(extension.ResponseViewSet):
    queryset = models.AppProbjectreport.objects.all()
    serializer_class = serializers.AppProbjectreportSerializer

    @action(methods=['get'], detail=False)
    def GetReportData(self, request):
        probjectid = int_arg(request.query_params.get('probjectid'), None)
        if request.user.Role_Id != 1:
            relation = models.AppProbjectrelation.objects.filter(ProbjectId=probjectid, UserId=request.user.pk).exists()
            if not relation:
                return Response(
                    response.Content().error('还未进行项目，请于管理员联系').raw()
                )
        pinfo = models.AppProbject.objects.filter(Probject_Id=probjectid).first()
        listI = models.AppProbjectinterview.objects.filter(ProbjectId=probjectid, status=1, interviewType=0,
                                                           Satisfaction__gt=0)
        nowInterview = listI.aggregate(Max('nowInterview')).get('nowInterview__max')
        jltitle = []
        czdata = []
        trdata = []
        jldata = []
        for i in range(1, nowInterview + 1):
            jltitle.append('第' + str(i) + '次约谈')
            jllist = listI.filter(nowInterview=i)
            val = 0
            if jllist.count() > 0:
                val = get_two_number(jllist.aggregate(Sum('Satisfaction')).get('Satisfaction__sum') / jllist.count())
            jldata.append(val)
            val2 = 0
            if jllist.count() > 0:
                val2 = get_two_number(
                    jllist.aggregate(Sum('growupSatisfied')).get('growupSatisfied__sum') / jllist.count())
            czdata.append(val2)
            val3 = 0
            if jllist.count() > 0:
                val3 = get_two_number(
                    jllist.aggregate(Sum('inputSatisfied')).get('inputSatisfied__sum') / jllist.count())
            trdata.append(val3)
        listcz = {
            'title': jltitle,
            'data': czdata
        }
        listtr = {
            'title': jltitle,
            'data': trdata
        }
        listjl = {
            'title': jltitle,
            'data': jldata
        }
        times = listI.aggregate(Sum('Times')).get('Times__sum') / 60
        mydd = 0
        if listI.count() > 0:
            mydd = get_two_number(listI.aggregate(Sum('ProbjectRank')).get('ProbjectRank__sum') / listI.count())
        else:
            mydd = get_two_number(listI.aggregate(Sum('ProbjectRank')).get('ProbjectRank__sum'))
        cz = 0
        if listI.count() > 0:
            cz = get_two_number(listI.aggregate(Sum('growupSatisfied')).get('growupSatisfied__sum') / listI.count())
        else:
            cz = get_two_number(listI.aggregate(Sum('growupSatisfied')).get('growupSatisfied__sum'))
        tr = 0
        if listI.count() > 0:
            tr = get_two_number(listI.aggregate(Sum('inputSatisfied')).get('inputSatisfied__sum') / listI.count())
        else:
            tr = get_two_number(listI.aggregate(Sum('inputSatisfied')).get('inputSatisfied__sum'))
        jl = 0
        if listI.count() > 0:
            jl = get_two_number(listI.aggregate(Sum('Satisfaction')).get('Satisfaction__sum') / listI.count())
        else:
            jl = get_two_number(listI.aggregate(Sum('Satisfaction')).get('Satisfaction__sum'))
        data = {
            'Name': pinfo.Name,
            'Number': pinfo.Number,
            'Times': pinfo.Times,
            'usetime': times,
            'myd': mydd,
            'cz': cz,
            'czlist': listcz,
            'tr': tr,
            'trlist': listtr,
            'jl': jl,
            'jllist': listjl
        }
        return Response(
            response.Content().ok('SUC', data).raw()
        )

    # /// <summary>
    #         /// 教练数据统计（学员/约谈时长/约谈次数/满意度
    #         /// 化学面试成功率（成功 不成功 待定
    #         /// 教练满意度（
    #         /// 在线时长（本周/总在线
    #         /// 客户满意度（头像/姓名/约谈 累计时长 成长长度 投入程度 教练满意度
    @action(methods=['get'], detail=False)
    def GetCoachReportData(self, request):
        coachid = int_arg(request.query_params.get('coachid'), None)
        if coachid == 0:
            coachid = request.user.pk
        students = models.AppProbjectrelation.objects.filter(CoachId=coachid, UserId__gt=0).count()
        cintervies = models.AppProbjectinterview.objects.filter(Coach_Id=coachid, status=1, interviewType=0,
                                                                Satisfaction__gt=0)
        times = 0
        if cintervies.exists():
            times = cintervies.aggregate(Sum('Times')).get('Times__sum', 0) / 60
        interviews = cintervies.count()
        myd = 0
        if interviews > 0:
            myd = cintervies.aggregate(Sum('Satisfaction')).get('Satisfaction__sum', 0) / interviews
        myd = get_two_number(myd)
        nowInterview = 0
        if cintervies.exists():
            nowInterview = cintervies.aggregate(Max('nowInterview')).get('nowInterview__max', 0)
        listI = models.ProjectCoachInterview.objects.filter(CoachId=coachid, StudentStatus__gt=0)
        listcgl = []
        # 化学面试成功率 成功 不成功 待定
        suclist = listI.filter(StudentStatus=1)
        errlist = listI.filter(StudentStatus=2)
        waitlist = listI.filter(StudentStatus=3)
        cgl = 0
        if listI.count() > 0:
            cgl = get_two_number(suclist.count() / listI.count())
        suc_value = 0
        if listI.count() > 0:
            suc_value = get_two_number(suclist.count() / listI.count())
        listcgl.append({
            'name': '成功率',
            'value': suc_value
        })
        err_value = 0
        if listI.count() > 0:
            err_value = get_two_number(errlist.count() / listI.count())
        listcgl.append({
            'name': '不成功率',
            'value': err_value
        })
        wait_value = 0
        if listI.count() > 0:
            wait_value = get_two_number(waitlist.count() / listI.count())
        listcgl.append({
            'name': '待定率',
            'value': wait_value
        })
        # 教练满意度
        jltitle = []
        jldata = []
        for i in range(1, nowInterview + 1):
            jltitle.append('第' + str(i) + '次约谈')
            jllist = cintervies.filter(nowInterview=i)
            val = 0
            if jllist.count() > 0:
                val = get_two_number(jllist.aggregate(Sum('Satisfaction')).get('Satisfaction__sum', 0) / jllist.count())
            jldata.append(val)
        listjl = {
            'title': jltitle,
            'data': jldata
        }
        # 满意度
        studentgroup = cintervies.values('User_Id').annotate(key=F('User_Id'), avg=Avg('Satisfaction')).order_by(
            '-avg')[:10]
        res_satisfaction = []
        for item in studentgroup:
            uid = item.get('key')
            user_interview_list = cintervies.filter(User_Id=uid)
            uinfo = models.AppMember.objects.filter(User_Id=uid).first()
            if uinfo:
                # 约谈 累计时长 成长长度 投入程度 教练满意度
                count = 1
                if user_interview_list.count() > 0:
                    count = user_interview_list.count()
                trd = get_two_number(
                    user_interview_list.aggregate(Sum('inputSatisfied')).get('inputSatisfied__sum', 0) / count)
                czd = get_two_number(
                    user_interview_list.aggregate(Sum('growupSatisfied')).get('growupSatisfied__sum', 0) / count)
                jlmyd = get_two_number(
                    user_interview_list.aggregate(Sum('Satisfaction')).get('Satisfaction__sum', 0) / count)
                during = 0
                user_during = user_interview_list.aggregate(Sum('Times')).get('Times__sum', 0)
                if user_during and user_during > 0:
                    during = user_during
                result = {
                    'yt': user_interview_list.count(),
                    'sc': round(during / 60, 2),
                    'czd': czd,
                    'trd': trd,
                    'jlmyd': jlmyd
                }
                dic = {}
                dic["truename"] = uinfo.TrueName
                dic["photograph"] = uinfo.Photograph
                dic["duty"] = uinfo.Duty
                dic["email"] = uinfo.email
                dic["result"] = result
                res_satisfaction.append(dic)
        # 在线时长（本周/总在线
        listweek = []
        weeks = ["周一", "周二", "周三", "周四", "周五", "周六", "周日"]
        weekdata = []
        listmonth = []
        monthdata = []
        now = datetime.datetime.now()
        week = now.weekday()
        if week == 0:
            week = 7
        for i in range(0, 7):
            sp = i + 1 - week
            ndt = now + datetime.timedelta(days=sp)
            listweek.append(weeks[i])
            oinfo = models.AppUseronline.objects.filter(UserId=coachid, logindate=ndt.strftime('%Y-%m-%d')).first()
            if oinfo:
                weekdata.append(round(oinfo.times / 60, 2))
            else:
                weekdata.append(0)
        months = now.month
        for i in range(0, months):
            ndt = datetime.datetime.strptime(str(now.year) + '-' + str(i + 1) + '-1', '%Y-%m-%d')
            nedt = ndt + relativedelta(months=+1)
            listmonth.append(str(i + 1) + '月')
            oinfo = models.AppUseronline.objects.filter(UserId=coachid, logindate__gte=ndt, logindate__lt=nedt)
            if oinfo.count() > 0:
                monthdata.append(round(oinfo.aggregate(Sum('times')).get('times__sum', 0) / 60, 2))
            else:
                monthdata.append(0)
        online = {
            'week': {
                'title': listweek,
                'data': weekdata
            },
            'month': {
                'title': listmonth,
                'data': monthdata
            }
        }
        data = {
            'students': students,
            'times': times,
            'interviews': interviews,
            'myd': myd,
            'cgl': cgl,
            'cgllist': listcgl,
            'jlmyd': listjl,
            'khmyd': res_satisfaction,
            'online': online
        }
        return Response(
            response.Content().ok('SUC', data).raw()
        )

    @action(methods=['get'], detail=False)
    def GetCoachReportDataByProbject(self, request):
        coachid = int_arg(request.query_params.get('coachid'), None)
        probjectid = int_arg(request.query_params.get('probjectid'), None)
        if coachid == 0:
            coachid = request.user.pk
        students = models.AppProbjectrelation.objects.filter(CoachId=coachid, UserId__gt=0,
                                                             ProbjectId=probjectid).count()
        cintervies = models.AppProbjectinterview.objects.filter(ProbjectId=probjectid, Coach_Id=coachid, status=1,
                                                                interviewType=0,
                                                                Satisfaction__gt=0)
        if not cintervies.exists():
            return Response(
                response.Content().ok().raw()
            )
        times = int_arg(cintervies.aggregate(Sum('Times')).get('Times__sum', 0), 0) / 60
        interviews = cintervies.count()
        myd = 0
        if interviews > 0:
            myd = cintervies.aggregate(Sum('Satisfaction')).get('Satisfaction__sum', 0) / interviews
        myd = get_two_number(myd)
        nowInterview = cintervies.aggregate(Max('nowInterview')).get('nowInterview__max', 0)
        listI = models.ProjectCoachInterview.objects.filter(CoachId=coachid, ProbjectId=probjectid, StudentStatus__gt=0)
        listcgl = []
        # 化学面试成功率 成功 不成功 待定
        suclist = listI.filter(StudentStatus=1)
        errlist = listI.filter(StudentStatus=2)
        waitlist = listI.filter(StudentStatus=3)
        cgl = 0
        if listI.count() > 0:
            cgl = get_two_number(suclist.count() / listI.count())
        suc_value = 0
        if listI.count() > 0:
            suc_value = get_two_number(suclist.count() / listI.count())
        listcgl.append({
            'name': '成功率',
            'value': suc_value
        })
        err_value = 0
        if listI.count() > 0:
            err_value = get_two_number(errlist.count() / listI.count())
        listcgl.append({
            'name': '不成功率',
            'value': err_value
        })
        wait_value = 0
        if listI.count() > 0:
            wait_value = get_two_number(waitlist.count() / listI.count())
        listcgl.append({
            'name': '待定率',
            'value': wait_value
        })
        # 教练满意度
        jltitle = []
        jldata = []
        for i in range(1, nowInterview + 1):
            jltitle.append('第' + str(i) + '次约谈')
            jllist = cintervies.filter(nowInterview=i)
            val = 0
            if jllist.count() > 0:
                val = get_two_number(jllist.aggregate(Sum('Satisfaction')).get('Satisfaction__sum', 0) / jllist.count())
            jldata.append(val)
        listjl = {
            'title': jltitle,
            'data': jldata
        }
        #  客户满意度
        studentgroup = cintervies.values('User_Id').annotate(key=F('User_Id'), avg=Avg('Satisfaction')).order_by(
            '-avg')[:10]
        res_satisfaction = []
        for item in studentgroup:
            uid = item.get('key')
            user_interview_list = cintervies.filter(User_Id=uid)
            uinfo = models.AppMember.objects.filter(User_Id=uid).first()
            if uinfo:
                # 约谈 累计时长 成长长度 投入程度 教练满意度
                count = 1
                if user_interview_list.count() > 0:
                    count = user_interview_list.count()
                trd = get_two_number(
                    user_interview_list.aggregate(Sum('inputSatisfied')).get('inputSatisfied__sum', 0) / count)
                czd = get_two_number(
                    user_interview_list.aggregate(Sum('growupSatisfied')).get('growupSatisfied__sum', 0) / count)
                jlmyd = get_two_number(
                    user_interview_list.aggregate(Sum('Satisfaction')).get('Satisfaction__sum', 0) / count)
                result = {
                    'yt': user_interview_list.count(),
                    'sc': int_arg(user_interview_list.aggregate(Sum('Times')).get('Times__sum'), 0) / 60,
                    'czd': czd,
                    'trd': trd,
                    'jlmyd': jlmyd
                }
                dic = {}
                dic["truename"] = uinfo.TrueName
                dic["photograph"] = uinfo.Photograph
                dic["duty"] = uinfo.Duty
                dic["email"] = uinfo.email
                dic["result"] = result
                res_satisfaction.append(dic)
        data = {
            'students': students,
            'times': times,
            'interviews': interviews,
            'myd': myd,
            'cgl': cgl,
            'cgllist': listcgl,
            'jlmyd': listjl,
            'khmyd': res_satisfaction
        }
        return Response(
            response.Content().ok('SUC', data).raw()
        )

    @action(methods=['post'], detail=False, url_path='update')
    def update_new(self, request):
        main_data = request.data.get('mainData')
        json_dic = main_data.get('Json')
        if json_dic:
            main_data['Json'] = json.dumps(json_dic, ensure_ascii=False, default=str)
        res = self.update_row(main_data)
        status = int_arg(main_data.get('status'), None)
        Id = int_arg(main_data.get('Id'), None)
        if res.status and status == 1:
            tx_company_complete_report(Id)
        return Response(
            res.raw()
        )

    @action(methods=['post'], detail=False)
    def TxCompanyCompleteReport(self, request):
        main_data = request.data.get('mainData')
        reportid = int_arg(main_data.get('reportid'), None)
        res = tx_company_complete_report(reportid)
        return Response(
            res.raw()
        )

    @action(methods=['post'], detail=False)
    def getpagedatafront(self, request):
        t = self.get_page_rows(request.data)
        return Response(
            self.page_data(t)
        )

    @swagger_auto_schema(
        operation_id='LBI项目报告未来发展建议',
        operation_summary='LBI项目报告未来发展建议',
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'report_id': openapi.Schema(type=openapi.TYPE_INTEGER, description='报告id'),
                'advice': openapi.Schema(type=openapi.TYPE_INTEGER, description='发展建议'),
            }
        ),
    )
    @action(methods=['post'], detail=False, authentication_classes=[])
    def update_advice(self, request):
        try:
            report = models.AppProbjectreport.objects.get(pk=request.data.get('report_id'))
        except models.AppProbjectreport.DoesNotExist:
            return Response(response.Content().error('报告ID错误').raw())
        res = json.loads(report.Json)
        res['advice'] = request.data.get('advice', None)
        report.Json = json.dumps(res, ensure_ascii=False, default=str)
        report.save()
        return Response(response.Content().ok({'advice': request.data.get('advice', None)}).raw())

    @swagger_auto_schema(
        operation_id='LBI项目报告',
        operation_summary='推送LBI项目报告',
        manual_parameters=[openapi.Parameter('keyvalue', openapi.IN_QUERY, type=openapi.TYPE_INTEGER,
                                             description='LBI项目报告id')])
    @action(methods=['post'], detail=False, authentication_classes=[])
    def push_report(self, request):
        try:
            report = models.AppProbjectreport.objects.get(pk=int(request.data.get('keyvalue')))
        except models.AppProbjectreport.DoesNotExist:
            return Response(response.Content().error('报告ID错误').raw())
        user_ids = models.AppProbjectrelation.objects.filter(ProbjectId=report.ProbjectId,
                                                             IsManger=True).values_list('UserId', flat=True)
        company_user = models.SysUser.objects.filter(pk__in=user_ids)
        try:
            project_relation = models.AppProbjectrelation.objects.filter(ProbjectId=report.ProbjectId).first()
            company_name = models.AppCompany.objects.get(pk=project_relation.CompanyId).CompanyName
        except models.AppCompany.DoesNotExist:
            company_name = None
        dic = {
            'report_name': report.Title,
            'company_name': company_name,
            'path_link': '-'
        }
        if company_user.exists:
            for user in company_user:
                dic['path_link'] = get_lbi_report_to_manage(report.pk)
                push_message.delay(user, 'lbi_report_to_manage', dic, report.ProbjectId)

        report.status = 2
        report.Enable = 1
        report.save()
        return Response(response.Content().ok().raw())

    @swagger_auto_schema(
        operation_id='LBI项目报告详情接口',
        operation_summary='LBI项目报告详情接口',
        manual_parameters=[openapi.Parameter('report_id', openapi.IN_QUERY, type=openapi.TYPE_INTEGER,
                                             description='LBI项目报告id')])
    @action(methods=['get'], detail=False, url_path='reportdetail', authentication_classes=[])
    def report_detail(self, request):
        try:
            report = models.AppProbjectreport.objects.get(pk=int(request.query_params.get('report_id')))
        except models.AppProbjectreport.DoesNotExist:
            return Response(response.Content().error('报告ID错误').raw())
        try:
            project_relation = models.AppProbjectrelation.objects.filter(ProbjectId=report.ProbjectId).first()
            company_name = models.AppCompany.objects.get(pk=project_relation.CompanyId).CompanyName
        except models.AppCompany.DoesNotExist:
            company_name = None
        data = {
            'report_name': report.Title,
            'company_name': company_name,
            'report_date': report.CreateDate.strftime("%Y-%m-%d"),
            'status': report.status,
            'user_group': '',
            'report_id': report.pk,
            'report_type': report.reporttype,
            'json': report.Json
        }
        return Response(response.Content().ok('SUC', data=data).raw())

    @swagger_auto_schema(
        operation_id='LBI项目报告分析接口',
        operation_summary='LBI项目报告分析接口',
        manual_parameters=[openapi.Parameter('keyvalue', openapi.IN_QUERY, type=openapi.TYPE_INTEGER,
                                             description='LBI项目报告id')])
    @action(methods=['get'], detail=False, url_path='analysereport', authentication_classes=[])
    def analyse_report(self, request):
        try:
            report = models.AppProbjectreport.objects.get(pk=int(request.query_params.get('keyvalue')))
        except models.AppProbjectreport.DoesNotExist:
            return Response(response.Content().error('报告ID错误').raw())

        project_id = report.ProbjectId
        # 该项目所有的LBI报告
        results = models.AppEvaluationresult.objects.filter(ProbjectId=project_id, EvaluationId__in=[17, 18])
        # 项目中没有自评或者他评就返回错误
        if not results.filter(EvaluationId=17).exists() or not results.filter(EvaluationId=18).exists():
            return Response(response.Content().error('没有足够的数据可以分析，稍后再试').raw())
        report_users = set(
            results.filter(EvaluationId=17).values('Userid').annotate(Count('Userid')).values_list('Userid', flat=True))
        users = models.AppMember.objects.filter(User_Id__in=report_users)
        # 测评总人数
        self_users = len(report_users)
        # 工作年限分类
        year_5 = 0
        year_5_10 = 0
        year_11_15 = 0
        year_16 = 0

        # 司龄年限分类
        user_company_working_1 = 0
        user_company_working_3 = 0
        user_company_working_5 = 0
        user_company_working_10 = 0
        user_company_working_11 = 0

        # 性别分类
        sex_1 = 0
        sex_2 = 0

        # 管理幅度
        employee_10 = 0
        employee_11_20 = 0
        employee_21_50 = 0
        employee_51 = 0

        # 管理层级 1:一线经理 2:部门经理 3:事业部经理 4:事业部总经理 5:集团高管 6:首席执行官
        manage_role_1 = 0
        manage_role_2 = 0
        manage_role_3 = 0
        manage_role_4 = 0
        manage_role_5 = 0
        manage_role_6 = 0
        for user in users:
            # 工作年限
            if user.WorkingDate:
                user_working_time = datetime.datetime.now().year - user.WorkingDate.year
                if user_working_time < 5:
                    year_5 += 1
                elif 5 <= user_working_time < 10:
                    year_5_10 += 1
                elif 10 <= user_working_time < 15:
                    year_11_15 += 1
                elif user_working_time >= 15:
                    year_16 += 1
                else:
                    year_5 += 1
            else:
                year_5 += 1
            # 司龄年限
            if user.CompanyWorkingDate:
                user_company_working_time = datetime.datetime.now().year - user.CompanyWorkingDate.year
                if user_company_working_time <= 1:
                    user_company_working_1 += 1
                elif user_company_working_time <= 3:
                    user_company_working_3 += 1
                elif user_company_working_time <= 5:
                    user_company_working_5 += 1
                elif user_company_working_time <= 10:
                    user_company_working_10 += 1
                elif user_company_working_time > 10:
                    user_company_working_11 += 1
            else:
                user_company_working_1 += 1

            # 性别分布
            user_sex = user.Sex
            if user_sex is not None:
                if user_sex == 1:
                    sex_1 += 1
                else:
                    sex_2 += 1

            # 管理幅度
            if user.employee is not None:
                user_employee = user.employee
                if user_employee <= 10:
                    employee_10 += 1
                elif user_employee <= 20:
                    employee_11_20 += 1
                elif user_employee <= 50:
                    employee_21_50 += 1
                else:
                    employee_51 += 1

            # 管理层级
            if user.manage_role:
                manage_role = user.manage_role
                if manage_role == 1:
                    manage_role_1 += 1
                elif manage_role == 2:
                    manage_role_2 += 1
                elif manage_role == 3:
                    manage_role_3 += 1
                elif manage_role == 4:
                    manage_role_4 += 1
                elif manage_role == 5:
                    manage_role_5 += 1
                elif manage_role == 6:
                    manage_role_6 += 1
        try:
            project = models.AppProbject.objects.filter(pk=project_id).first()
            project_name = project.Name
            company_name = models.AppCompany.objects.get(pk=project.Company_Id).CompanyName
        except models.AppCompany.DoesNotExist:
            company_name = None
            project_name = None
        data = {
            'report_name': '领导力行为指数评估',
            'company_name': company_name,
            'project_name': project_name,
            'report_date': report.CreateDate.strftime("%Y-%m-%d"),
            'user_count': len(report_users),
            'status': report.status,
            'user_group': '',
            'report_id': report.pk,
            'report_type': report.reporttype
        }

        # 优势和挑战
        pros_and_challenge = []

        # 测评群体-5个饼图组对象
        exam_user_list = []

        # 工作年限对象
        exam_user_working = {
            "class": "circle_list",
            "circle": [],
            "capacity": 1,
            "title": '工作年限',
        }
        working_circle = get_circle(title='工作年限', user_count=self_users,
                                    sub_title=[{'label': '5年以内', 'value' : year_5},
                                               {'label': '5-10年', 'value' : year_5_10},
                                               {'label': '11-15年', 'value': year_11_15},
                                               {'label': '超过15年', 'value': year_16}]
                                    )
        exam_user_working['circle'].append(working_circle)
        exam_user_list.append(exam_user_working)

        # 司龄年限对象
        exam_user_company_working = {
            "class": "circle_list",
            "circle": [],
            "capacity": 1,
            "title": '司龄年限',
        }

        company_working_circle = get_circle(title='司龄年限', user_count=self_users,
                                            sub_title=[{'label': '1年以内', 'value' : user_company_working_1},
                                               {'label': '3年以内', 'value' : user_company_working_3},
                                               {'label': '5年以内', 'value': user_company_working_5},
                                               {'label': '10年以内', 'value': user_company_working_10},
                                               {'label': '10年以上', 'value': user_company_working_11}]
                                            )
        exam_user_company_working['circle'].append(company_working_circle)
        exam_user_list.append(exam_user_company_working)

        # 性别分布
        exam_user_sex = {
            "class": "circle_list",
            "circle": [],
            "capacity": 1,
            "title": '性别分布',
        }

        sex_circle = get_circle(title='性别分布', user_count=self_users,
                                sub_title=[{'label': '男', 'value' : sex_1},
                                               {'label': '女', 'value' : sex_2}]
                                )
        exam_user_sex['circle'].append(sex_circle)
        exam_user_list.append(exam_user_sex)

        # 管理幅度
        exam_user_underling = {
            "class": "circle_list",
            "circle": [],
            "capacity": 1,
            "title": '管理幅度',
        }
        underling_circle = get_circle(title='管理幅度 ', user_count=self_users,
                                      sub_title=[{'label': '少于10人', 'value' : employee_10},
                                               {'label': '11-20人', 'value' : employee_11_20},
                                               {'label': '21-50人', 'value': employee_21_50},
                                               {'label': '超过50人', 'value': employee_51}]
                                      )
        exam_user_underling['circle'].append(underling_circle)
        exam_user_list.append(exam_user_underling)

        # 管理层级
        exam_user_pyramid = {
            "class": "circle_list",
            "circle": [],
            "capacity": 1,
            "title": '管理层级',
        }
        pyramid_circle = get_circle(title='管理层级', user_count=self_users,
                                    sub_title=[{'label': '一线经理', 'value' : manage_role_1},
                                               {'label': '部门经理', 'value' : manage_role_2},
                                               {'label': '事业部经理', 'value': manage_role_3},
                                               {'label': '事业部总经理', 'value': manage_role_4},
                                               {'label': '集团高管', 'value': manage_role_5},
                                               {'label': '首席执行官', 'value': manage_role_6}]
                                            )
        exam_user_pyramid['circle'].append(pyramid_circle)
        exam_user_list.append(exam_user_pyramid)

        # 测评群体信息
        data['exam_user'] = exam_user_list

        # 效能总览--网状态图
        spider_data = {'fixed_text': '本图形呈现了所有的测评对象领导力自评的平均分，基于测评对象自我评价，可以看到在不同领导能力项上群体认知的对比。',
                       'values_list': []}

        SPIDER_MAP = {
            'A': '突破进取',
            'B': '抗压复原',
            'C': '自我成长',
            'D': '敏捷应变',
            'E': '引领变革',
            'F': '战略远见',
            'G': '激励赋能',
            'H': '发展下属',
            'J': '果敢决策',
            'K': '系统思考',
            'L': '多方协同',
            'M': '集体担责',
        }
        self_dic = get_json(results, 17)
        # 按照前端给的数据排序 ["突破进取", "抗压复原", "自我成长", "果敢决策", "多方协同", "系统思考", "战略远见", "引领变革", "敏捷应变", "发展下属", "激励赋能", "集体担责"]
        spider_list = []
        for spider, score in self_dic.items():
            spider_list.append({'title': SPIDER_MAP[spider], 'values': round(np.mean(score['score']), 1)})
            self_dic[spider]['avg'] = round(np.mean(score['score']), 1)

        sort_spider = [{'title': '突破进取', 'values': ''}, {'title': '抗压复原', 'values': ''},
                       {'title': '自我成长', 'values': ''}, {'title': '果敢决策', 'values': ''},
                       {'title': '多方协同', 'values': ''}, {'title': '系统思考', 'values': ''},
                       {'title': '战略远见', 'values': ''}, {'title': '引领变革', 'values': ''},
                       {'title': '敏捷应变', 'values': ''},
                       {'title': '发展下属', 'values': ''}, {'title': '激励赋能', 'values': ''},
                       {'title': '集体担责', 'values': ''}]
        for i in spider_list:
            for s in sort_spider:
                if i['title'] == s['title']:
                    s['values'] = [i['values']]
        spider_data['values_list'] = sort_spider

        # 领导力效能总览
        leadership_efficiency = []

        # 添加网状图数据
        leadership_efficiency.append({'class': 'spider', 'values': spider_data})
        # ============================================= 柱状图（趋同度） =================================================
        # 效能总览--柱状图（趋同度）
        # top_text_list 上方文字 steps
        # bar value 是从下往上的y值（条行上的值） label是条行上的文字

        histogram_data = {'class': 'bar', 'title': '',
                          'splitArea': False, 'is_overlap': True,
                          'fixed_text': '本部分展示了所有测评对象基于自我评价视角的领导力有效性分布，按照群体趋同度由高到低进行排序，反应了管理者视角下的群体领导力有效性现状。',
                          'top_text_list': [],
                          'steps': [{'title': '效能不佳', 'color': '#EE6666'},
                                    {'title': '相对有效', 'color': 'pink'},
                                    {'title': '稳定发挥', 'color': '#5470c6'},
                                    {'title': '充分发挥', 'color': '#91cc75'}], 'axisx': [], 'axisy': [], 'bar': []
                          }

        bar_list = []
        histogram_data_axisy_label = []
        histogram_label = []
        histogram = {'class': 'bar', 'title': '',
                          'splitArea': True, 'is_overlap': False,
                          'fixed_text': '本部分展示了所有测评对象视角和视角对比之下的领导力有效性评价，按照自评和他评的一致性由高到低进行排序，从不同维度反应了群体领导力有效性现状。',
                          'top_text_list': ['效能不佳', '', '相对有效', '稳定发挥', '充分发挥'],
                          'steps': [{'title': '自评', 'color': '#EE6666'},
                                    {'title': '他评', 'color': '#5470c6'}], 'axisx': [], 'axisy': [], 'bar': []
                          }

        histogram_axisx = {'label': '', 'bar_label': [1, 2, 3, 4, 5, 6], 'range': [1,6], 'hideLable': True, 'interval': 1}


        for n, s in self_dic.items():
            cnt = dict(Counter(s['score']))
            for k, v in cnt.items():
                cnt[k] = v / len(s['score'])
            # 把小于3的加在3上
            if cnt.get(1):
                if cnt.get(3):
                    cnt[3] += cnt.get(1)
                else:
                    cnt[3] = cnt.get(1)
                cnt.pop(1)

            if cnt.get(2):
                if cnt.get(3):
                    cnt[3] += cnt.get(2)
                else:
                    cnt[3] = cnt.get(2)
                cnt.pop(2)

            self_dic[n]['percentage'] = cnt

        for k, v in self_dic.items():
            self_dic[k]['var'] = np.var(v['score'])

        # 按照自评方差排序
        sort_data = dict(sorted(self_dic.items(), key=lambda item: item[1]['var'], reverse=True))
        dic_3 = {'value': [], 'color': [], 'label': []}
        dic_4 = {'value': [], 'color': [], 'label': []}
        dic_5 = {'value': [], 'color': [], 'label': []}
        dic_6 = {'value': [], 'color': [], 'label': []}
        for _key, _val in sort_data.items():
            # y 轴名称排序 '%.2f%%' % (a * 100)
            histogram_data_axisy_label.append(SPIDER_MAP[_key] + str(_val['avg']))
            dic_3['value'].append(_val['percentage'].get(3))
            dic_3['label'].append(str(int(_val['percentage'].get(3, 0) * 100 + 0.5)) + '%')
            dic_3['color'].append('#EE6666')
            dic_4['value'].append(_val['percentage'].get(4))
            dic_4['label'].append(str(int(_val['percentage'].get(4, 0) * 100 + 0.5)) + '%')
            dic_4['color'].append('#fac858')
            dic_5['value'].append(_val['percentage'].get(5))
            dic_5['label'].append(str(int(_val['percentage'].get(5, 0) * 100 + 0.5)) + '%')
            dic_5['color'].append('#73c0de')
            dic_6['value'].append(_val['percentage'].get(6))
            dic_6['label'].append(str(int(_val['percentage'].get(6, 0) * 100 + 0.5)) + '%')
            dic_6['color'].append('#91cc75')



        bar_list.append(dic_3)
        bar_list.append(dic_4)
        bar_list.append(dic_5)
        bar_list.append(dic_6)

        # 柱状图bar
        histogram_data['bar'] = bar_list

        histogram_data_axisx = {'label': '', 'bar_label': [1, 2, 3, 4, 5, 6], 'range': [0], 'interval': True, 'hideLable': True,}
        histogram_data_axisy = {'label': '', 'bar_label': []}
        histogram_data_axisy['bar_label'] = histogram_data_axisy_label

        # 柱状图x
        histogram_data['axisx'].append(histogram_data_axisx)
        # 柱状图y
        histogram_data['axisy'].append(histogram_data_axisy)
        # 添加柱状图-趋同度
        leadership_efficiency.append({'class': 'histogram_1', 'values': histogram_data})

        # ============================================= 柱状图（一致性） =================================================
        # 临时存储他评平均值
        other_data = get_json(results, 18)

        for s, t in other_data.items():
            other_data[s]['avg'] = round(np.mean(t['score']), 1)
            other_data[s]['var'] = round(np.var(t['score']), 1)

        histogram_bar_list = []
        # all_data = get_json(results)
        # for k, v in all_data.items():
        #     all_data[k]['var'] = np.var(v['score'])

        # 按照自评+他评方差排序
        for sk, sv in self_dic.items():
            for _k, _v in other_data.items():
                if sk == _k:
                    self_dic[sk]['all_var'] = round(np.var(self_dic[sk]['score'] + other_data[_k]['score']), 1)
                    other_data[_k]['all_var'] = round(np.var(self_dic[sk]['score'] + other_data[_k]['score']), 1)


        # 自评 他评排序取值
        self_dic = dict(sorted(self_dic.items(), key=lambda item: item[1]['all_var'], reverse=True))
        other_data = dict(sorted(other_data.items(), key=lambda item: item[1]['all_var'], reverse=True))
        dic_self = {'height': 15, 'value': [], 'color': [], 'label': []}
        dic_other = {'height': 15, 'value': [], 'color': [], 'label': []}
        for k, v in other_data.items():
            histogram_label.append(SPIDER_MAP[k])
            dic_other['value'].append(v['avg'])
            dic_other['label'].append(v['avg'])
            dic_other['color'].append('#EE6666')

        for value in self_dic.values():
            dic_self['value'].append(value['avg'])
            dic_self['label'].append(value['avg'])
            dic_self['color'].append('#5470c6')

        histogram_bar_list.append(dic_self)
        histogram_bar_list.append(dic_other)
        # 柱状图（自评 他评）bar
        histogram['bar'] = histogram_bar_list
        # 柱状图（自评 他评）x
        histogram['axisx'].append(histogram_axisx)
        # 柱状图（自评 他评）y
        histogram_axisy = {'label': '', 'bar_label': histogram_label}
        histogram['axisy'].append(histogram_axisy)
        # 添加柱转图- 一致性
        leadership_efficiency.append({'class': 'histogram_2', 'values': histogram})

        # 领导效能
        data['leadership_efficiency'] = leadership_efficiency

        # ===================================================================

        self_dic_all = sorted(self_dic.items(), key=lambda item: item[1]['all_var'], reverse=False)
        dic_var_6 = dict(self_dic_all[0:6])
        for k, v in dic_var_6.items():
            v['all_avg'] = self_dic.get(k)['avg'] + other_data.get(k)['avg']
        # reverse=True 从大到小
        dic_var_6_sort = sorted(dic_var_6.items(), key=lambda item: item[1]['all_avg'], reverse=True)

        # 优势
        advantage = dict(dic_var_6_sort[0:3])
        advantage_histogram_list = get_default_histogram(advantage, other_data)
        advantage_data = {'title': '群体优势', 'fixed_text': '在群体评价高度趋同的领导能力项中，以下三项领导能力项的自评和他评一致性高且得分最高，说明该群体在这三项领导力中呈现出了明显优势：', 'histogram_list': advantage_histogram_list, 'question': {}}

        # 挑战
        challenge = dict(dic_var_6_sort[3:6])
        challenge_histogram_list = get_default_histogram(challenge, other_data)
        challenge_data = {'title': '群体挑战', 'fixed_text': '在群体评价高度趋同的领导能力项中，以下三项领导能力项的自评和他评一致性高且得分最低，说明该群体在这三项领导力中呈现出了明显挑战：', 'histogram_list': challenge_histogram_list, 'question': {}}

        # 盲区
        unknown = dict(self_dic_all[6:9])
        unknown_histogram_list = get_default_histogram(unknown, other_data)
        unknown_data = {'title': '群体盲区', 'fixed_text': '在群体评价高度趋同的领导能力项中，以下三项领导能力项的自我评价和他人评价差异较大，说明以下该群体在这三项领导力中存在盲区：', 'histogram_list': unknown_histogram_list, 'question': {}}

        # 发展
        progress = dict(self_dic_all[9:12])
        progress_histogram_list = get_default_histogram(progress, other_data)
        progress_data = {'title': '个性化发展项', 'fixed_text': '在群体自我评价差异较大的领导能力项中，该群体对以下三项领导力评价的趋同度最低：', 'histogram_list': progress_histogram_list, 'question': {}}

        pros_and_challenge.append(advantage_data)
        pros_and_challenge.append(challenge_data)
        pros_and_challenge.append(unknown_data)
        pros_and_challenge.append(progress_data)
        data['pros_and_challenge'] = pros_and_challenge
        results = models.AppEvaluationresult.objects.filter(ProbjectId=project_id, EvaluationId__in=[17, 18])

        self_sort = results.filter(EvaluationId=17).values('EqId', 'tIndex').annotate(score_var=Variance('Score')).distinct()
        other_sort = results.filter(EvaluationId=18).values('EqId', 'tIndex').annotate(score_var=Variance('Score')).distinct()
        for self_obj in self_sort:
            for other_obj in other_sort:
                if self_obj['tIndex'] == other_obj['tIndex']:
                    self_obj['score_var'] += other_obj['score_var']
                    break
        self_sort_data = sorted(self_sort, key=lambda item: item['score_var'], reverse=False)
        # 发展-问题
        progress_list = self_sort_data[len(self_sort_data)-5:]
        progress_question_dic = {'fixed_text': '在群体自我评价差异较大的领导行为中，该群体对以下五项领导行为的评价差异最大：', 'question_value': []}
        for pro in progress_list:
            title = models.AppEvaluationquestion.objects.get(pk=pro['EqId']).Title
            progress_question_dic['question_value'].append({'question_content': title, 'background_color': '#ee6666'})
        # 盲区-问题 self_sort前50%计算自评和他评分数合并后的方差
        res = self_sort_data[0:int(len(self_sort_data) / 2)]
        index_ids = [i['tIndex'] for i in res]
        all_res = results.filter(tIndex__in=index_ids).values('tIndex').annotate(t_var=Variance('Score')).order_by('t_var')
        unknown_list = all_res[len(all_res)-5:]
        unknown_question_dic = {'fixed_text': '在自己和他人评价差异较大的领导力行为中，该群体对以下五项的领导行为的评价偏差最为明显：', 'question_value': []}
        for unk in unknown_list:
            title = models.AppEvaluationquestion.objects.filter(Sort=unk['tIndex'], EvaluationId=17).first().Title
            unknown_question_dic['question_value'].append({'question_content': title, 'background_color': '#ee6666'})
        last_list = all_res[0:int(len(all_res) / 2)]
        index_ids_last = [i['tIndex'] for i in last_list]
        all_res_last = results.filter(tIndex__in=index_ids_last).values('tIndex').annotate(t_avg=Avg('Score')).order_by('-t_avg')
        # 优势-问题
        advantage_question_dic = {'fixed_text': '在自我评价和他人评价一致度高的领导行为中，该群体在以下五项领导行为中表现最佳：', 'question_value': []}
        for height_avg in all_res_last[0:5]:
            title = models.AppEvaluationquestion.objects.filter(Sort=height_avg['tIndex'], EvaluationId=17).first().Title
            advantage_question_dic['question_value'].append({'question_content': title, 'background_color': '#ee6666'})
        # 挑战-问题
        challenge_question_dic = {'fixed_text': '在自我评价和他人评价一致度高的领导行为中，该群体在以下五项领导行为中的进步空间最大：', 'question_value': []}
        for low_avg in all_res_last[len(all_res_last)-5:]:
            title = models.AppEvaluationquestion.objects.filter(Sort=low_avg['tIndex'], EvaluationId=17).first().Title
            challenge_question_dic['question_value'].append(
                {'question_content': title, 'background_color': '#ee6666'})
        progress_data['question'] = progress_question_dic
        unknown_data['question'] = unknown_question_dic
        challenge_data['question'] = challenge_question_dic
        advantage_data['question'] = advantage_question_dic
        data['status'] = 1
        if report.Json:
            json_data = json.loads(report.Json)
            data['advice'] = json_data.get('advice') if json_data else None
        report.Json = json.dumps(data, ensure_ascii=False, default=str)
        report.status = 1
        report.save()
        return Response(
            response.Content().ok('SUC', data=data).raw()
        )
