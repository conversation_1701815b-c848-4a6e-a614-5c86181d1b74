import datetime

from rest_framework.decorators import action
from rest_framework.response import Response

from data import models, serializers, extension
from utils import response


class AppUserOnlineViewSet(extension.ResponseViewSet):
    queryset = models.AppUseronline.objects.all()
    serializer_class = serializers.AppUseronlineSerializer

    @action(methods=['get'], detail=False)
    def UpdateOnline(self, request):
        uid = request.user.pk
        now = datetime.datetime.now()
        first = models.AppUseronline.objects.filter(UserId=uid, logindate=now.date()).first()
        if first:
            lasttime = first.lasttime
            if not lasttime:
                lasttime = now - datetime.timedelta(days=1)
            delta = now - lasttime
            minute = delta.total_seconds() / 60
            if minute < 30:
                first.times = first.times + minute
            first.lasttime = now
            first.save()
        else:
            first = models.AppUseronline()
            first.UserId = uid
            first.times = 0
            first.lasttime = now
            first.logindate = now.date()
            first.save()
        return Response(
            response.Content().ok().raw()
        )
