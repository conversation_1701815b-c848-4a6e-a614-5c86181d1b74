import random

from rest_framework.decorators import action
from rest_framework.response import Response

from data import models, serializers, extension
from utils import response, int_arg
from utils.messagecenter.center import push_message


class MessageTemplateViewSet(extension.ResponseViewSet):
    queryset = models.AppMessagetemplates.objects.all()
    serializer_class = serializers.AppMessagetemplatesSerializer

    @action(methods=['post'], detail=False)
    def add(self, request):
        main_data = request.data.get('mainData')
        res = self.add_row(main_data)
        return Response(
            res.raw()
        )

    @action(methods=['post'], detail=False, url_path='update')
    def update_new(self, request):
        main_data = request.data.get('mainData')
        res = self.update_row(main_data)
        return Response(
            res.raw()
        )

    @action(methods=['post'], detail=False)
    def copytemplate(self, request):
        main_data = request.data.get('mainData')
        templateid = int_arg(main_data.get('templateid'))
        tmpmode = models.AppMessagetemplates.objects.filter(MessageTemplateId=templateid).first()
        tmpmode.pk = None
        tmpmode.MessageType = tmpmode.MessageType + "_" + str(random.randint(1000, 9999))
        tmpmode.save()
        return Response(
            response.Content().ok().raw()
        )

    @action(methods=['get'], detail=False)
    def TestSend(self, request):
        messagetype = request.query_params.get('messagetype')
        user = models.SysUser.objects.filter(User_Id=request.user.pk).first()
        push_message.delay(user, messagetype, None)
        return Response(
            response.Content().ok().lower_raw()
        )
