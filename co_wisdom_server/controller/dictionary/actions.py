import datetime

from django.db.models import Sum
from rest_framework.response import Response

from controller.coach.actions import get_coach_info
from controller.hxmeetingdata.actions import get_hxmeeting_data
from controller.project.actions import get_project_info
from controller.projectsettings.actions import get_project_config_val
from data import models
from utils import null_or_blank, response, int_arg
from utils.easemob.common import get_im_id

def get_dictionary(dic_no):
    dic_list = models.SysDictionary.objects.filter(dic_no=dic_no).order_by('order_no')
    if dic_list.exists():
        return dic_list
    return None

def get_dictionary_list_value(dic_no, dic_name, separator=','):
    dic_list = models.SysDictionaryList.objects.filter(dictionary__dic_no=dic_no).order_by('order_no')
    if dic_list.exists() and dic_name:
        ret = ''
        name_list = dic_name.split(',')
        for value in name_list:
            sub_dic = dic_list.filter(dic_value=value).first()
            if sub_dic:
                ret += sub_dic.dic_name + separator
        return ret[:len(ret)-len(separator)]
    return dic_name

