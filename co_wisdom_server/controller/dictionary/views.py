from django.db.models import Q
from rest_framework.decorators import action
from rest_framework.response import Response

from data import models, serializers, extension
from utils import int_arg


class DictionaryViewSet(extension.ResponseViewSet):
    queryset = models.AppDictionary.objects.all()
    serializer_class = serializers.AppDictionarySerializer

    @action(methods=['post'], detail=False)
    def add(self, request):
        main_data = request.data.get('mainData')
        res = self.add_row(main_data)
        if res.status:
            dic = res.row
            sysdic = models.SysDictionary(config_json=dic.ConfigJson, dic_name=dic.DicName, dic_no=dic.DicNo,
                                          order_no=dic.OrderNo, remark=dic.Remark, enable=dic.Enable, id=dic.Dic_ID)
            sysdic.save()
        return Response(
            res.raw()
        )

    @action(methods=['post'], detail=False, url_path='update')
    def update_new(self, request):
        main_data = request.data.get('mainData')
        delKeys = request.data.get('delKeys')
        if delKeys:
            models.AppDictionarylist.objects.filter(pk__in=delKeys).all().delete()
            models.SysDictionaryList.objects.filter(pk__in=delKeys).all().delete()
        detailData = request.data.get('detailData')
        if detailData:
            for item in detailData:
                dic_data = item
                del dic_data['elementIdex']
                dic = models.AppDictionarylist(**dic_data)
                if not dic.pk:
                    dic = models.AppDictionarylist(DicName=dic_data.get('DicName'), DicValue=dic_data.get('DicValue'),
                                                   Enable=dic_data.get('Enable'), Dic_ID=main_data.get('Dic_ID'),
                                                   OrderNo=int_arg(main_data.get('OrderNo'), 0), Remark=main_data.get('Remark'))
                dic.save()
                sysdiclist = models.SysDictionaryList(id=dic.DicList_ID, dic_name=dic.DicName, dic_value=dic.DicValue,
                                                      order_no=dic.OrderNo, remark=dic.Remark, dictionary_id=dic.Dic_ID,
                                                      enable=dic.Enable)
                sysdiclist.save()

        ParentId = int_arg(main_data.get('ParentId'))
        main_data['ParentId'] = ParentId
        res = self.update_row(main_data)
        if res.status:
            row = res.row
            sysdic = models.SysDictionary(id=row.Dic_ID, config_json=row.ConfigJson, sql=row.DbSql,
                                          dic_name=row.DicName,
                                          dic_no=row.DicNo, order_no=row.OrderNo, remark=row.Remark,
                                          enable=row.Enable)
            sysdic.save()
        return Response(
            res.raw()
        )

    @action(methods=['post'], detail=False)
    def getDetailPage(self, request):
        value = request.data.get('value')
        order = request.data.get('order')
        sort = request.data.get('sort')
        page = int_arg(request.data.get('page'))
        rows = int_arg(request.data.get('rows'))
        if order == 'desc':
            sort = '-' + str(sort)
        res = models.AppDictionarylist.objects.filter(Dic_ID=value).order_by(sort)
        start = (page - 1) * rows
        end = page * rows
        new_res = res
        total = len(new_res)
        if len(res) > start:
            new_res = res[start:end]
        if len(new_res) > 0:
            total = len(new_res)
        return Response(
            {
                "status": 0,
                'msg': None,
                "rows": serializers.AppDictionarylistSerializer(new_res, many=True).data,
                "extra": None,
                "total": total,
                'summary': None
            }
        )

    @action(methods=['post'], detail=False)
    def GetTableDictionary(self, request):
        keyData = request.data
        dic_no_list = list(keyData.keys())
        dic_list = models.SysDictionary.objects.filter(dic_no__in=dic_no_list, sql__isnull=False, enable=1)
        res_list = []
        for item in dic_list.all():
            if item.dic_no == 'company':
                ids = keyData.get(str(item.dic_no))
                sub_list = models.AppCompany.objects.filter(Enable=1, Company_Id__in=ids).order_by('-OrderNo')
                data_list = []
                for sub in sub_list.all():
                    dic = {
                        'key': str(sub.pk),
                        'value': sub.ShortName + '(' + sub.CompanyName + ')'
                    }
                    data_list.append(dic)
            elif item.dic_no == 'qzmanager':
                ids = keyData.get(str(item.dic_no))
                sub_list = models.SysUser.objects.filter(Enable=1, Role_Id__lt=4, User_Id__in=ids)
                data_list = []
                for sub in sub_list.all():
                    dic = {
                        'key': str(sub.User_Id),
                        'value': sub.UserTrueName
                    }
                    data_list.append(dic)
            elif item.dic_no == 'froles':
                ids = keyData.get(str(item.dic_no))
                sub_list = models.SysRole.objects.filter(Enable=1, Role_Id__gt=4, Role_Id__in=ids)
                data_list = []
                for sub in sub_list.all():
                    dic = {
                        'key': str(sub.Role_Id),
                        'value': sub.RoleName
                    }
                    data_list.append(dic)
            elif item.dic_no == 'roles':
                ids = keyData.get(str(item.dic_no))
                sub_list = models.SysRole.objects.filter(Enable=1, Role_Id__in=ids)
                data_list = []
                for sub in sub_list.all():
                    dic = {
                        'key': sub.Role_Id,
                        'value': sub.RoleName
                    }
                    data_list.append(dic)
            elif item.dic_no == 'rescategory':
                ids = keyData.get(str(item.dic_no))
                sub_list = models.AppRescategories.objects.filter(ParentId=0, IsShow=1, CategoryId__in=ids)
                data_list = []
                for sub in sub_list.all():
                    dic = {
                        'key': str(sub.CategoryId),
                        'value': sub.Name
                    }
                    data_list.append(dic)
            elif item.dic_no == 'artcategory':
                ids = keyData.get(str(item.dic_no))
                sub_list = models.AppArticlecategory.objects.filter(ParentId=0, Enable=1, Cate_Id__in=ids)
                data_list = []
                for sub in sub_list.all():
                    dic = {
                        'key': str(sub.Cate_Id),
                        'value': sub.CateName
                    }
                    data_list.append(dic)
            res_list.append({
                'key': item.dic_no,
                'data': data_list
            })
        return Response(
            res_list
        )

    @action(methods=['post'], detail=False)
    def GetVueDictionary(self, request):
        result = []
        for dic_no in request.data:
            data_list = []
            dic = models.SysDictionary.objects.filter(dic_no=dic_no, enable=1).first()
            if not dic:
                continue
            dic_list_set = models.SysDictionaryList.objects.filter(dictionary=dic, enable=1).order_by('-order_no')
            if dic_list_set.exists():
                for dic_list in dic_list_set.all():
                    data_list.append({'key': dic_list.dic_value, 'value': dic_list.dic_name})
                result.append({'dicNo': dic_no, 'config': dic.config_json, 'data': data_list})
            else:
                sql = dic.sql
                if sql:
                    if dic_no == 'company':
                        sub_list = models.AppCompany.objects.filter(Enable=1).order_by('-OrderNo')
                        for sub in sub_list.all():
                            if sub.ShortName and sub.CompanyName:
                                data_list.append({
                                    'key': str(sub.pk),
                                    'value': sub.ShortName + '(' + sub.CompanyName + ')'
                                })
                    elif dic_no == 'qzmanager':
                        sub_list = models.SysUser.objects.filter(Enable=1, Role_Id__lt=4)
                        for sub in sub_list.all():
                            data_list.append({
                                'key': str(sub.User_Id),
                                'value': sub.UserTrueName
                            })
                    elif dic_no == 'froles':
                        sub_list = models.SysRole.objects.filter(Enable=1, Role_Id__gt=4)
                        for sub in sub_list.all():
                            data_list.append({
                                'key': str(sub.Role_Id),
                                'value': sub.RoleName
                            })
                    elif dic_no == 'roles':
                        sub_list = models.SysRole.objects.filter(Enable=1)
                        for sub in sub_list.all():
                            data_list.append({
                                'key': str(sub.Role_Id),
                                'value': sub.RoleName
                            })
                    elif dic_no == 'rescategory':
                        sub_list = models.AppRescategories.objects.filter(ParentId=0, IsShow=1)
                        for sub in sub_list.all():
                            data_list.append({
                                'key': str(sub.CategoryId),
                                'value': sub.Name
                            })
                    elif dic_no == 'artcategory':
                        sub_list = models.AppArticlecategory.objects.filter(ParentId=0, Enable=1)
                        for sub in sub_list.all():
                            data_list.append({
                                'key': str(sub.Cate_Id),
                                'value': sub.CateName
                            })
                    result.append({'dicNo': dic_no, 'config': dic.config_json, 'data': data_list})
        return Response(result)

    @action(methods=['post'], detail=False)
    def GetSearchDictionary(self, request):
        keyData = request.query_params
        dicNo = keyData.get('dicNo')
        value = keyData.get('value')
        data_list = []
        if dicNo == 'company':
            sub_list = models.AppCompany.objects.filter(Q(ShortName__icontains=value) | Q(CompanyName__icontains=value),
                                                        Enable=1).order_by('-OrderNo')
            for sub in sub_list.all():
                dic = {
                    'key': str(sub.pk),
                    'value': sub.ShortName + '(' + sub.CompanyName + ')'
                }
                data_list.append(dic)
        elif dicNo == 'qzmanager':
            sub_list = models.SysUser.objects.filter(Enable=1, Role_Id__lt=4, UserTrueName__icontains=value)
            for sub in sub_list.all():
                dic = {
                    'key': str(sub.User_Id),
                    'value': sub.UserTrueName
                }
                data_list.append(dic)
        elif dicNo == 'froles':
            sub_list = models.SysRole.objects.filter(Enable=1, Role_Id__gt=4, RoleName__icontains=value)
            for sub in sub_list.all():
                dic = {
                    'key': str(sub.Role_Id),
                    'value': sub.RoleName
                }
                data_list.append(dic)
        elif dicNo == 'roles':
            sub_list = models.SysRole.objects.filter(Enable=1, RoleName__icontains=value)
            for sub in sub_list.all():
                dic = {
                    'key': str(sub.Role_Id),
                    'value': sub.RoleName
                }
                data_list.append(dic)
        elif dicNo == 'rescategory':
            sub_list = models.AppRescategories.objects.filter(ParentId=0, IsShow=1, Name__icontains=value)
            for sub in sub_list.all():
                dic = {
                    'key': str(sub.CategoryId),
                    'value': sub.Name
                }
                data_list.append(dic)
        elif dicNo == 'artcategory':
            sub_list = models.AppArticlecategory.objects.filter(ParentId=0, Enable=1, CateName__icontains=value)
            for sub in sub_list.all():
                dic = {
                    'key': str(sub.Cate_Id),
                    'value': sub.CateName
                }
                data_list.append(dic)
        return Response(
            data_list
        )
