import datetime

from django.db.models import Q
from rest_framework.decorators import action
from rest_framework.response import Response

from controller.coach.actions import get_coach_info
from controller.dictionary.actions import get_dictionary_list_value
from controller.member.actions import get_member_info
from controller.project.actions import get_project_info
from controller.projectcoach.actions import get_has_matching, get_auto_matching, tx_company_no_interview_matching_coach, \
    auto_coach_to_project, matching, get_hxdata
from controller.projectneed.actions import get_need_item
from controller.projectsettings.actions import get_project_config_val
from data import models, serializers, extension
from utils import response, int_arg
from utils.easemob.common import get_im_id
from utils.messagecenter.center import push_message


class ProjectCoachViewSet(extension.ResponseViewSet):
    queryset = models.Projectcoach.objects.all()
    serializer_class = serializers.ProjectCoachSerializer

    @action(methods=['post'], detail=False)
    def getpagedatafront(self, request):
        t = self.get_page_rows(request.data)
        if t.count() > 0:
            extra = []
            for item in t.all():
                dic = {}
                dic['eId'] = item.Id
                comodel = models.AppCoach.objects.filter(User_Id=item.CoachId).first()
                dic["img"] = comodel.Photograph
                dic["level"] = get_dictionary_list_value("coachlevel", comodel.CoachLevel)
                dic["truename"] = comodel.TrueName
                dic["jlzz"] = comodel.jlzz
                dic["jlzw"] = comodel.jlzw
                dic["ProName"] = get_project_info(item.ProbjectId).Name
                dic["lastlogin"] = models.SysUser.objects.filter(User_Id=comodel.User_Id).first().LastLoginDate
                extra.append(dic)
            t.extra_list = extra
        return Response(
            self.page_data(t)
        )

    @action(methods=['get'], detail=False)
    def GetProbjectCoachSetting(self, request):
        probjectid = int_arg(request.query_params.get('probjectid'), None)
        isexit = models.AppProbjectneed.objects.filter(ProbjectId=probjectid).exists()
        hasmatchingdata = get_has_matching(request)
        hasmatching = None
        if hasmatchingdata.status:
            hasmatching = hasmatchingdata.data
        automatching_data = get_auto_matching(request)
        automatching = None
        if automatching_data.status:
            automatching = automatching_data.data
        mtfzr = int_arg(get_project_config_val(probjectid, 'mtfzr'), None)
        res = response.Content()
        res.data = {
            'mtfzr': mtfzr,
            'noneed': isexit,
            'hasmatching': hasmatching,
            'automatching': automatching
        }
        return Response(
            res.ok('SUC').raw()
        )

    @action(methods=['get'], detail=False)
    def GetHasMatching(self, request):
        res = get_has_matching(request)
        return Response(
            res.raw()
        )

    @action(methods=['get'], detail=False)
    def GetAutoMatching(self, request):
        res = get_has_matching(request)
        return Response(
            res.raw()
        )

    @action(methods=['post'], detail=False)
    def addfront(self, request):
        main_data = request.data.get('mainData')
        ProbjectId = int_arg(main_data.get('ProbjectId'), None)
        if ProbjectId == 0:
            return Response(
                response.Content().error('关联的项目不能空').raw()
            )
        CoachId = int_arg(main_data.get('CoachId'), None)
        if CoachId == 0:
            return Response(
                response.Content().error('请选择教练').raw()
            )
        if not models.AppProbjectrelation.objects.filter(ProbjectId=ProbjectId, IsManger=1).exists():
            return Response(
                response.Content().error('请先添加企业管理员').raw()
            )
        company_name = main_data.get('CompanyName', '')
        if not models.Projectcoach.objects.filter(ProbjectId=ProbjectId, CoachId=CoachId).exists():
            main_data['MatchingTimes'] = 0
            res = self.add_row(main_data)
            if res.status:
                mtfzr = int_arg(get_project_config_val(ProbjectId, 'mtfzr'), None)
                if mtfzr == 0:
                    tx_company_no_interview_matching_coach(ProbjectId)
                    auto_coach_to_project(ProbjectId, CoachId)
            return Response(
                res.raw()
            )
        return Response(
            response.Content().ok().raw()
        )

    @action(methods=['post'], detail=False)
    def SetCoach(self, request):
        main_data = request.data.get('mainData')
        ProbjectId = int_arg(main_data.get('ProbjectId'), None)
        if ProbjectId == 0:
            return Response(
                response.Content().error('关联的项目不能空').raw()
            )
        if not models.AppProbjectrelation.objects.filter(ProbjectId=ProbjectId, IsManger=1).exists():
            return Response(
                response.Content().error('请先添加企业管理员').raw()
            )
        needitem = get_need_item(ProbjectId)
        needlist = []
        if needitem.status:
            needlist = needitem.data
        rows = request.data.get('DetailData')
        listAdd = []
        for item in rows:
            ino = models.Projectcoach()
            uid = int(item['User_Id'])
            if not models.Projectcoach.objects.filter(ProbjectId=ProbjectId, CoachId=uid).exists():
                cinfo = models.AppCoach.objects.filter(User_Id=uid).first()
                ino.ProbjectId = ProbjectId
                ino.CoachId = uid
                ino.Matching = float(matching(cinfo, needlist))
                ino.CompanyId = 0
                ino.Status = 0
                ino.MatchingTimes = 0
                ino.save()
                listAdd.append(ino)
        if len(listAdd) > 0:
            mtfzr = int_arg(get_project_config_val(ProbjectId, 'mtfzr'), None)
            if mtfzr == 0:
                tx_company_no_interview_matching_coach(ProbjectId)
                for item in listAdd:
                    item.Status = 2
                    item.save()
                    auto_coach_to_project(ProbjectId, item.CoachId)
        return Response(
            response.Content().ok().raw()
        )

    @action(methods=['get'], detail=False)
    def GetProbjectCoachList(self, request):
        probjectid = int_arg(request.query_params.get('probjectid'), None)
        projectcoach_list = models.Projectcoach.objects.filter(ProbjectId=probjectid, Status__in=[0, 1])
        relist = []
        for item in projectcoach_list.all():
            dic = {}
            coachinterview = models.ProjectCoachInterview.objects.filter(ProbjectId=item.ProbjectId,
                                                                         CoachId=item.CoachId).first()
            if coachinterview:
                dic["Id"] = coachinterview.Id
                dic["Status"] = int_arg(coachinterview.Status, 0)
                # 预约时间
                if coachinterview.interviewId > 0:
                    coachsh = models.AppCoachschedule.objects.filter(ScheduleId=coachinterview.interviewId).first()
                    if coachsh:
                        dic["interviewTime"] = coachsh.Starttime.strftime(
                            '%Y-%m-%d %H:%M') + ' -' + coachsh.EndTime.strftime('%H:%M')
                        hxdata = get_hxdata(coachinterview.Id)
                        if hxdata:
                            dic["hxdata"] = serializers.AppHxmeetingdataSerializer(hxdata, many=False).data
                else:
                    dic["interviewTime"] = ""
                uinfo = get_member_info(item.UserId)
                dic["User_Id"] = uinfo.User_Id
                dic["photograph"] = uinfo.Photograph
                dic["userName"] = uinfo.TrueName
                dic["department"] = uinfo.Department
                dic["duty"] = uinfo.Duty
                dic["companyName"] = uinfo.CompanyName
                dic["messageid"] = get_im_id(int_arg(uinfo.User_Id, 0))
            else:
                dic["Status"] = 0
                dic["interviewTime"] = ""
                dic["photograph"] = ""
                dic["userName"] = ""
                dic["department"] = ""
                dic["duty"] = ""
                dic["companyName"] = ""
                dic["messageid"] = ""
            comodel = get_coach_info(item.CoachId)
            dic["CoachId"] = comodel.User_Id
            dic["img"] = comodel.Photograph
            dic["coachlevel"] = get_dictionary_list_value("coachlevel", comodel.CoachLevel)
            dic["jlzz"] = get_dictionary_list_value("jlzz", comodel.jlzz)
            dic["jlzw"] = get_dictionary_list_value("jlzw", comodel.jlzw)
            dic["coachmessageid"] = get_im_id(item.CoachId)
            dic["truename"] = comodel.TrueName
            relist.append(dic)
        res = response.Content()
        res.data = {
            'rows': relist,
            'total': len(relist)
        }
        if len(relist) > 0:
            return Response(
                res.ok('SUC').raw()
            )
        return Response(
            response.Content().error('正在等待项目负责人匹配教练').raw()
        )

    @action(methods=['post'], detail=False)
    def DelProbjectCoach(self, request):
        probjectid = int_arg(request.query_params.get('probjectid'), None)
        coachids = request.query_params.get('coachids')
        ids = coachids
        if type(coachids) is str:
            ids = [coachids]
        if models.ProjectCoachInterview.objects.filter(ProbjectId=probjectid, CoachId__in=ids,
                                                       StudentUserId__gt=0).exists():
            return Response(
                response.Content().error('已经匹配的教练不能删除').lower_raw()
            )
        coach_list = models.Projectcoach.objects.filter(ProbjectId=probjectid, CoachId__in=ids)
        coach_list.all().delete()
        interview_list = models.ProjectCoachInterview.objects.filter(ProbjectId=probjectid, CoachId__in=ids)
        interview_list.all().delete()
        return Response(
            response.Content().ok().lower_raw()
        )

    @action(methods=['post'], detail=False)
    def SetNewCoachToStudent(self, request):
        probjectid = int_arg(request.query_params.get('probjectid'), None)
        coachid = int_arg(request.query_params.get('coachid'), None)
        studentid = int_arg(request.query_params.get('studentid'), None)
        if probjectid == 0 or coachid == 0 or studentid == 0:
            return Response(
                response.Content().error('参数不能空').raw()
            )
        pmod = models.AppProbjectrelation.objects.filter(ProbjectId=probjectid, UserId=studentid).first()
        if not pmod:
            return Response(
                response.Content().error('指定的用户与项目不匹配').raw()
            )
        now = datetime.datetime.now()
        cinterlist = models.AppProbjectinterview.objects.filter(Q(EndTime__gt=now) | Q(Times=None), ProbjectId=probjectid, User_Id=studentid,
                                                                status=1)
        if cinterlist.count() > 0:
            return Response(
                response.Content().error('有未完成的约谈，请完成后再更换教练').raw()
            )
        pmod.CoachId = coachid
        pmod.save()
        # 插入一条化学约谈信息
        coachinterview = models.ProjectCoachInterview()
        coachinterview.ProbjectId = probjectid
        coachinterview.UserId = pmod.UserId
        coachinterview.PassReason = "系统自动通过"
        coachinterview.NoPassReason = ""
        coachinterview.CoachId = coachid
        coachinterview.Status = 1
        coachinterview.iscomplate = 1
        coachinterview.Studentiscomplate = 1
        coachinterview.StudentStatus = 1
        coachinterview.StudentUserId = pmod.UserId
        coachinterview.StudentPassReason = "系统自动通过"
        coachinterview.StudentNoPassReason = ""
        coachinterview.save()
        # 发送通知
        prinfo = get_project_info(probjectid)
        minfo = models.SysUser.objects.filter(User_Id=prinfo.Relation_UserId).first()
        if minfo:
            oldcinfo = models.SysUser.objects.filter(User_Id=pmod.CoachId).first()
            cinfo = models.SysUser.objects.filter(User_Id=coachid).first()
            uinfo = models.SysUser.objects.filter(User_Id=studentid).first()
            dic = {
                'probjectname': prinfo.Name,
                'coachname': cinfo.UserTrueName,
                'studentname': uinfo.UserTrueName,
                'manager': minfo.UserTrueName,
                'email': minfo.Email
            }
            push_message.delay(uinfo, 'changecoach', dic, prinfo.pk)
            push_message.delay(cinfo, 'changecoachtocoach', dic, prinfo.pk)
            push_message.delay(oldcinfo, 'changecoachtooldcoach', dic, prinfo.pk)
        return Response(
            response.Content().ok().raw()
        )

    @action(methods=['get'], detail=False)
    def GetCoachbyProbject(self, request):
        probjectid = int_arg(request.query_params.get('probjectid'), None)
        projectcoach_list = models.Projectcoach.objects.filter(ProbjectId=probjectid, Status=2)
        listdic = []
        for item in projectcoach_list.all():
            dic = self.serializer_class(item, many=False).data
            cinfo = models.SysUser.objects.filter(User_Id=item.CoachId).first()
            dic['coachname'] = cinfo.UserTrueName
            listdic.append(dic)
        res = response.Content()
        res.data = listdic
        return Response(
            res.ok('SUC').raw()
        )

    @action(methods=['get'], detail=False)
    def GetProbjectMyCoach(self, request):
        probjectid = int_arg(request.query_params.get('probjectid'), None)
        uid = int_arg(request.query_params.get('uid'), None)
        coachids = set(models.ProjectCoachInterview.objects.filter(ProbjectId=probjectid, StudentStatus=1,
                                                                   StudentUserId=uid).values_list('CoachId', flat=True))
        olist = models.AppCoach.objects.filter(User_Id__in=coachids)
        data = []
        if olist.exists():
            for item in olist.all():
                ddic = {}
                ddic["ProbjectId"] = probjectid
                ddic["Coach_Id"] = item.User_Id
                ddic["img"] = item.Photograph
                ddic["level"] = get_dictionary_list_value('coachlevel', item.CoachLevel)
                ddic["truename"] = item.TrueName
                ddic["jlzz"] = item.jlzz
                ddic["jlzw"] = item.jlzw
                # 已预约未完成的约谈时间 教练最近可预约的空闲时间 已约谈数
                interview_list = models.AppProbjectinterview.objects.filter(ProbjectId=probjectid, User_Id=uid, Coach_Id=item.User_Id, status=1).order_by('-Id')
                if interview_list.exists():
                    ddic["ytnum"] = interview_list.count()
                    interview = interview_list.first()
                    ddic["yttime"] = interview.Starttime.strftime(
                        '%Y-%m-%d %H:%M') + ' -' + interview.EndTime.strftime('%H:%M')
                    ddic["coachtime"] = ""
                else:
                    ddic["ytnum"] = ""
                    ddic["yttime"] = ""
                    ddic["coachtime"] = ""
                # 环信
                ddic["coachmessageid"] = get_im_id(item.CoachId)
                data.append(ddic)
        return Response(
            response.Content().ok('SUC', data).raw()
        )