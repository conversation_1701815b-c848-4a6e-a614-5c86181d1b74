import datetime

from django.conf import settings
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.viewsets import ModelViewSet, ViewSet, GenericViewSet

from controller.dictionary.actions import get_dictionary_list_value
from controller.hxmeetingdata.actions import get_hxmeeting_data
from controller.project.actions import get_project_info, get_project_def_coach_by_user, get_project_info_by_user
from controller.projectneed.actions import get_need_item
from data import models, serializers, extension
from data.user.models import User
from utils import response, blank_to_none, int_arg, value_or_default, aesencrypt, randomPassword, float_arg, \
    null_or_blank
import json
from django.db.models import Avg, Count, Min, Sum, Value, F

from utils.messagecenter.center import push_message


def get_has_matching(request):
    probjectid = int_arg(request.query_params.get('probjectid'), None)
    coachids = models.Projectcoach.objects.filter(ProbjectId=probjectid).order_by('-Matching')
    res_list = []
    for item in coachids:
        coachinfo = models.AppCoach.objects.filter(IsOpen=1, User_Id=item.CoachId).first()
        if coachinfo:
            dic = {}
            dic["UserId"] = coachinfo.User_Id
            dic["img"] = coachinfo.Photograph
            dic["truename"] = coachinfo.TrueName
            dic["coachlevel"] = get_dictionary_list_value("coachlevel", coachinfo.CoachLevel)
            dic["jlzz"] = get_dictionary_list_value("jlzz", coachinfo.jlzz)
            dic["jlzw"] = get_dictionary_list_value("jlzw", coachinfo.jlzw)
            dic["ProName"] = get_project_info(probjectid).Name
            dic["lastlogin"] = models.SysUser.objects.filter(User_Id=coachinfo.User_Id).first().LastLoginDate
            dic["matching"] = item.Matching
            cinterview = models.ProjectCoachInterview.objects.filter(ProbjectId=probjectid, CoachId=coachinfo.User_Id).first()
            if cinterview:
                dic["coachinterviewid"] = cinterview.Id
                dic["interviewId"] = cinterview.interviewId
                dic["status"] = cinterview.Status
                dic["interviewTime"] = cinterview.interviewTime
            else:
                dic["coachinterviewid"] = 0
                dic["status"] = 0
                dic["interviewTime"] = ""
                dic["interviewId"] = 0
            res_list.append(dic)
    res = response.Content()
    res.data = res_list
    return res.ok('SUC')


def match_year_suc(val, regx):
    year = int(val)
    arr = regx.split(',')
    for item in arr:
        if int(item) == 20:
            if year >= 20 or year < 30:
                return True
        elif int(item) == 30:
            if year >= 30 or year < 40:
                return True
        elif int(item) == 40:
            if year >= 40 or year < 50:
                return True
        elif int(item) == 50:
            if year >= 50 or year < 60:
                return True
        elif int(item) == 60:
            return True
    return match_suc(val, regx)


def match_suc(val, regx):
    if val in regx or '0' in regx or '不限' in regx:
        return True
    return False


def matching(coach, needItemList):
    ret = 0
    dic = serializers.AppCoachSerializer(coach, many=False).data
    if not needItemList or len(needItemList) == 0:
        return 100
    for item in needItemList:
        if item.get('Key') == 'sex':
            if match_suc(str(dic['Sex']), item.get('Value')):
                ret += 1
        elif item.get('Key') == 'ages':
            if match_year_suc(str(dic['Age']), item.get('Value')):
                ret += 1
        elif item.get('Key') == 'objective':
            if match_suc(str(dic['scly']), item.get('Value')):
                ret += 1
        elif item.get('Key') == 'companyattr':
            if match_suc(str(dic['qysx']), item.get('Value')):
                ret += 1
        elif item.get('Key') == 'jlqyhy':
            if match_suc(str(dic['jlhy']), item.get('Value')):
                ret += 1
        elif item.get('Key') == 'jlgzhy':
            if match_suc(str(dic['industry']), item.get('Value')):
                ret += 1
        elif item.get('Key') == 'jlxssOne':
            if match_year_suc(str(dic['jlxssOne']), item.get('Value')):
                ret += 1
        elif item.get('Key') == 'jlxssTearm':
            if match_year_suc(str(dic['jlxssTearm']), item.get('Value')):
                ret += 1
        else:
            key = item.get('Key')
            if match_suc(str(dic[str(key)]), item.get('Value')):
                ret += 1
    return round(ret / len(needItemList) * 100, 2)


def get_auto_matching(request):
    probjectid = int_arg(request.query_params.get('probjectid'), None)
    needitem = get_need_item(probjectid)
    if not needitem.status:
        return response.Content().error()
    needlist = needitem.data
    coachids = models.Projectcoach.objects.filter(ProbjectId=probjectid).values_list('CoachId', flat=True)
    # All coaches who have not joined this project
    coachlist = models.AppCoach.objects.filter(IsOpen=1).exclude(User_Id__in=coachids)
    if coachlist.count() < 10:
        matching_list = []
        for item in coachlist.all():
            matching_list.append({
                'coach': item,
                'matching': matching(item, needlist)
            })
        query = sorted(matching_list, key=lambda x : x['matching'], reverse=True)
        res_list = []
        for subitem in query:
            item = subitem['coach']
            dic = {}
            dic["UserId"] = item.User_Id
            dic["img"] = item.Photograph
            dic["truename"] = item.TrueName
            dic["coachlevel"] = get_dictionary_list_value("coachlevel", item.CoachLevel)
            dic["jlzz"] = get_dictionary_list_value("jlzz", item.jlzz)
            dic["jlzw"] = get_dictionary_list_value("jlzw", item.jlzw)
            dic["ProName"] = get_project_info(probjectid).Name
            dic["lastlogin"] = models.SysUser.objects.filter(User_Id=item.User_Id).first().LastLoginDate
            dic["matching"] = subitem['matching']
            res_list.append(dic)
        res = response.Content()
        res.data = res_list
        return res.ok('SUC')
    else:
        top100 = coachlist.order_by('-Source')[:100]
        matching_list = []
        for item in top100.all():
            matching_list.append({
                'coach': item,
                'matching': matching(item, needlist)
            })
        query = sorted(matching_list, key=lambda x : x['matching'], reverse=True)
        res_list = []
        for subitem in query:
            item = subitem['coach']
            dic = {}
            dic["UserId"] = item.User_Id
            dic["img"] = item.Photograph
            dic["truename"] = item.TrueName
            if not null_or_blank(item.CoachLevel):
                dic["coachlevel"] = get_dictionary_list_value("coachlevel", item.CoachLevel)
            else:
                dic["coachlevel"] = ''
            dic["jlzz"] = item.jlzz
            dic["jlzw"] = item.jlzw
            dic["ProName"] = get_project_info(probjectid).Name
            dic["lastlogin"] = models.SysUser.objects.filter(User_Id=item.User_Id).first().LastLoginDate
            dic["matching"] = subitem['matching']
            res_list.append(dic)
        res = response.Content()
        res.data = res_list
        return res.ok('SUC')

def auto_coach_to_project(projectid, coachid):
    minfo = models.AppProbjectrelation.objects.filter(ProbjectId=projectid, IsManger=1).first()
    projectcoach = models.Projectcoach.objects.filter(ProbjectId=projectid, CoachId=coachid).first()
    if projectcoach:
        projectcoach.Status = 2
        projectcoach.save()
    uid = minfo.UserId if minfo else 0
    pinterview = models.ProjectCoachInterview()
    pinterview.ProbjectId = projectid
    pinterview.Status = 1
    pinterview.PassReason = "系统自动通过"
    pinterview.NoPassReason = ""
    pinterview.iscomplate = 1
    pinterview.UserId = uid
    pinterview.CoachId = coachid
    pinterview.interviewTime = datetime.datetime.now()
    pinterview.save()


def get_hxdata(interviewid):
    model = get_hxmeeting_data('1_'+ str(interviewid))
    return model

def tx_company_no_interview_matching_coach(projectid):
    projectcoach = models.AppProbjectrelation.objects.filter(ProbjectId=projectid, IsManger=1)
    if projectcoach.count() > 0:
        query = projectcoach.values_list('UserId', flat=True)
        prinfo = get_project_info(projectid)
        minfo = models.SysUser.objects.filter(User_Id=prinfo.Relation_UserId).first()
        if not minfo:
            return
        for item in query:
            uinfo = models.SysUser.objects.filter(User_Id=item).first()
            if uinfo:
                dic = {
                    'probjectname': prinfo.Name,
                    'probjectid': prinfo.Probject_Id,
                    'manager': minfo.UserTrueName,
                    'email': minfo.Email
                }
                dic['url'] = settings.SITE_URL + 'cadmin/probject_info?probjectid=' + str(prinfo.Probject_Id)
                push_message.delay(uinfo, 'nointerviewmatchingcoach', dic, project_id=prinfo.Probject_Id)