import datetime

from rest_framework.response import Response

from data import models
from utils import response

def get_project_info(proid):
    project_info = models.AppProbject.objects.filter(Probject_Id=proid).first()
    if project_info:
        return project_info
    else:
        return models.AppProbject()


def get_project_company_manager(projectid):
    memberids = models.AppProbjectrelation.objects.filter(IsManger=1, ProbjectId=projectid).values_list('UserId',
                                                                                                        flat=True)
    return models.AppMember.objects.filter(User_Id__in=memberids)


def get_start_project_num_by_company(companyid=0):
    if companyid == 0:
        return 0
    now = datetime.datetime.now()
    rows = models.AppProbject.objects.filter(Starttime__lt=now, Endtime__gt=now, Status=1, Company_Id=companyid)
    return rows.count()


def get_complete_project_num_by_company(companyid=0):
    if companyid == 0:
        return 0
    now = datetime.datetime.now()
    rows = models.AppProbject.objects.filter(Endtime__lt=now, Company_Id=companyid)
    return rows.count()


def get_sign_project_num_by_company(companyid=0, status=0):
    if companyid == 0:
        return 0
    now = datetime.datetime.now()
    rows = models.AppProbject.objects.filter(Status=status, Company_Id=companyid)
    return rows.count()


def get_project_info_by_user(uid):
    relation = models.AppProbjectrelation.objects.filter(UserId=uid).first()
    if not relation:
        return None
    project = models.AppProbject.objects.filter(Probject_Id=relation.ProbjectId, Enable=1).order_by('-Probject_Id').first()
    if not project:
        return None
    return project


def get_project_def_coach_by_user(uid):
    app = get_project_info_by_user(uid)
    if app and app.Probject_Id > 0:
        apr = models.AppProbjectrelation.objects.filter(ProbjectId=app.Probject_Id, UserId=uid).first()
        if apr:
            return apr.CoachId
    return 0


def is_pro_manager(uid, projectid):
    rp = models.AppProbjectrelation.objects.filter(UserId=uid, ProbjectId=projectid).first()
    if rp and rp.IsManger == 1:
        return 1
    return 0


def get_pro_status(start_time, end_time):
    now = datetime.datetime.now()
    if start_time <= now and now <= end_time:
        return 0  # 进行中
    elif start_time > now and now <= end_time:
        return 2  # 即将开始
    return 1  # 已经完成

def get_work_num(dt):
    if not dt:
        return 0
    year = datetime.datetime.now().year - dt.year
    return year


def get_project_list(uid, companyid):
    if companyid > 0:
        query = []
        project_list = models.AppProbject.objects.filter(Company_Id=companyid, Enable=1).order_by('Probject_Id') \
            .values('Probject_Id', 'Name', 'Number', 'Times', 'Starttime', 'Endtime', 'Status')
        for project in project_list:
            project['proStatus'] = get_pro_status(project['Starttime'], project['Endtime'])
            project['ismanager'] = is_pro_manager(uid, project['Probject_Id'])
            query.append(project)
        res = response.Content()
        res.data = query
        return Response(
            res.ok().raw()
        )
    else:
        query = []
        relation_list = models.AppProbjectrelation.objects.filter(UserId=uid).values_list('ProbjectId', flat=True)
        project_list = models.AppProbject.objects.filter(Enable=1, Probject_Id__in=relation_list).order_by(
            'Probject_Id') \
            .values('Probject_Id', 'Name', 'Number', 'Times', 'Starttime', 'Endtime', 'Status')
        for project in project_list:
            project['proStatus'] = get_pro_status(project['Starttime'], project['Endtime'])
            project['ismanager'] = is_pro_manager(uid, project['Probject_Id'])
            query.append(project)
        res = response.Content()
        res.data = query
        return Response(
            res.ok().raw()
        )

def add_or_update_dic(dic, keyname, val):
    if keyname in dic:
        dic[keyname] = dic[keyname] + '\n' + val
    else:
        dic[keyname] = val