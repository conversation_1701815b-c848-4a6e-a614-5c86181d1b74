import datetime
import json

from django.conf import settings
from django.db.models import Sum, Q
from rest_framework.decorators import action
from rest_framework.response import Response

from controller.company.actions import get_company_info
from controller.member.actions import get_member_info
from controller.project.actions import get_project_company_manager, get_project_info, get_work_num, add_or_update_dic, \
    get_project_info_by_user
from controller.projectinterview.actions import get_interview_nums, get_interview_times
from controller.projectrelation.actions import relation_filter_by_interest
from controller.projectsettings.actions import get_project_config_val
from data import models, serializers, extension
from utils import response, int_arg, value_or_default, aesdecrypt, null_or_blank
from utils.messagecenter.actions import get_remindinterviewstudent_link, get_exam_r25_student_default_link, \
    get_exam_r25_interested_link, get_exam_r25_student_link
from utils.messagecenter.center import push_message
from utils.model import add_option
from utils.queryset import distinct


class ProjectViewSet(extension.ResponseViewSet):
    queryset = models.AppProbject.objects.all()
    serializer_class = serializers.AppProbjectSerializer

    @action(methods=['post'], detail=False)
    def getpagedata(self, request):
        has_manageid = False
        for where in json.loads(request.data["wheres"]):
            if where['name'] == 'Relation_UserId':
                has_manageid = True
            add_option(request.data, where['name'], where['value'], where['displayType'])
        if not has_manageid and request.user.Role_Id != 1:
            add_option(request.data, 'Relation_UserId', request.user.pk, 'int')
        t = self.get_page_rows(request.data)
        return Response(
            self.page_data(t)
        )

    @action(methods=['post'], detail=False)
    def add(self, request):
        main_data = request.data.get('mainData')
        relation_id = main_data.get('Relation_UserId')
        if null_or_blank(relation_id):
            main_data['Relation_UserId'] = request.user.pk
        video = main_data.get('Video', '')
        if video and type(video) is list and 'path' in video[0]:
            main_data['Video'] = video[0].get('path')
        start_time = main_data.get('Starttime')
        end_time = main_data.get('Endtime')
        main_data['Starttime'] = datetime.datetime.strptime(start_time, '%Y-%m-%d')
        main_data['Endtime'] = datetime.datetime.strptime(end_time, '%Y-%m-%d')
        res = self.add_row(main_data)
        if res.status:
            # set chemistry coach matching number to 1 as default
            chemistry_coach_matching_setting = models.AppProbjectsetting(Probject_Id=res.row.Probject_Id,
                                                                         KeyName='jlppsl', KeyValue='1')
            chemistry_coach_matching_setting.save()
        return Response(
            res.raw()
        )

    @action(methods=['get'], detail=False)
    def getentity(self, request):
        res = self.get_entity(request)
        if res.status:
            info = res.row
            dic = self.serializer_class(info, many=False).data
            cinfo = get_company_info(info.Company_Id)
            if cinfo:
                dic['companyname'] = cinfo.ShortName
            if info.Relation_UserId > 0:
                user = models.SysUser.objects.filter(User_Id=info.Relation_UserId).first()
                if user:
                    dic["ProjectQZUser"] = user.UserTrueName
                    dic["ProjectQZEmail"] = user.Email
                    dic["ProjectQZMobile"] = user.PhoneNo
            rlist = get_project_company_manager(info.Probject_Id)
            if len(rlist) > 0:
                dic["ProjectComUser"] = rlist[0].TrueName
                dic["ProjectComEmail"] = rlist[0].email
                dic["ProjectComMobile"] = rlist[0].mobile
            # 约谈次数 ytcs 三方会谈次数 ytsfht 约谈顺序 ysconfig
            dic["ytcs"] = get_project_config_val(info.Probject_Id, 'ytcs')
            dic["ytsfht"] = get_project_config_val(info.Probject_Id, 'ytsfht')
            uid = request.user.pk
            dic["ytconfig"] = get_project_config_val(info.Probject_Id, 'ytconfig' + str(uid))
            dic["mrytsc"] = get_project_config_val(info.Probject_Id, 'mrytsc')
            res.data = dic
        return Response(
            res.raw()
        )

    @action(methods=['post'], detail=False, url_path='update')
    def update_new(self, request):
        main_data = request.data.get('mainData')
        video = main_data.get('Video', '')
        if video and type(video) is list and 'path' in video[0]:
            main_data['Video'] = video[0].get('path')
        res = self.update_row(main_data)
        return Response(
            res.raw()
        )

    @action(methods=['get'], detail=False)
    def GetUserByProbject(self, request):
        project_id = request.query_params.get('probjectid')
        if project_id:
            project_id = int(project_id)
        roleid = request.query_params.get('roleid', '')
        proinfo = models.AppProbject.objects.filter(Probject_Id=project_id).first()
        res = response.Content()
        if not proinfo:
            return Response(
                res.error('项目不存在').raw()
            )
        list_project_relation = models.AppProbjectrelation.objects.filter(ProbjectId=project_id)
        if roleid != '':
            arr = roleid.split(',')
            list_project_relation = list_project_relation.filter(RolesId__in=arr)
            # // 企业负责人 / 被教人 / 测评者
        relation_list = list_project_relation.filter(RolesId__in=[5, 6, 8]).values_list('UserId', flat=True)
        query = []
        query_list = models.SysUser.objects.filter(User_Id__in=relation_list)
        for item in query_list.all():
            dic = {
                'UserId': item.User_Id,
                'TrueName': item.UserTrueName
            }
            member_interested_info = models.AppMemberinterested.objects.filter(Member_Id=item.User_Id,
                                                                               ProbjectId=project_id).first()

            if member_interested_info:
                uinfo = models.SysUser.objects.filter(User_Id=member_interested_info.MasterMember_Id).first()
                if uinfo:
                    dic["Interested_Id"] = uinfo.User_Id
                    dic["Interested_Name"] = uinfo.UserTrueName
            query.append(dic)
        # // 关联测评人
        relation_eva_list = list_project_relation.filter(RolesId=7)
        listr = []
        for item in relation_eva_list:
            uid = item.UserId
            interest_uid = item.Interested_Id
            user_truename = models.SysUser.objects.filter(User_Id=uid).first()
            interest_truename = models.SysUser.objects.filter(User_Id=interest_uid).first()
            if user_truename and interest_truename:
                dic = {
                    'UserId': uid,
                    'TrueName': user_truename.UserTrueName,
                    'Interested_Id': interest_uid,
                    'Interested_Name': interest_truename.UserTrueName
                }
            listr.append(dic)
        # coach
        coach_list = distinct(list_project_relation.filter(CoachId__gt=0), ['CoachId'])
        coquery = []
        for coach in coach_list.all():
            user = models.SysUser.objects.filter(User_Id=coach.CoachId).first()
            coquery.append({
                'UserId': user.User_Id,
                'TrueName': user.UserTrueName
            })
        res.data = {
            'user': query,
            'retion': listr,
            'coach': coquery
        }
        return Response(
            res.ok('SUC').raw()
        )

    @action(methods=['get'], detail=False)
    def GetProbjectMembers(self, request):
        probjectid = request.query_params.get('probjectid')
        if probjectid:
            probjectid = int(probjectid)
        res = response.Content()
        relation_uid = models.AppProbjectrelation.objects.filter(RolesId=6, ProbjectId=probjectid).values_list('UserId',
                                                                                                               flat=True)
        member_list = models.AppMember.objects.filter(User_Id__in=relation_uid).values('Photograph', 'User_Id',
                                                                                       'TrueName'
                                                                                       , 'Department', 'Duty')
        members = [item for item in member_list]
        res.data = members
        return Response(
            res.ok().raw()
        )

    @action(methods=['get'], detail=False)
    def GetUserByCoach(self, request):
        coachid = request.query_params.get('coachid')
        if coachid:
            coachid = int(coachid)
        projectid = request.data.get('probjectid')
        if projectid:
            projectid = int(projectid)
        else:
            projectid = 0
        now = datetime.datetime.now()
        probjectids = models.AppProbject.objects.filter(Enable=1, Starttime__lte=now, Endtime__gt=now)
        if projectid > 0:
            probjectids = probjectids.filter(Probject_Id=projectid).values_list('Probject_Id', flat=True)
        relation_list = models.AppProbjectrelation.objects.filter(ProbjectId__in=probjectids, CoachId=coachid)
        query = []
        for relation in relation_list.all():
            member = models.AppMember.objects.filter(Enable=1, User_Id=relation.UserId).first()
            if member:
                query.append({
                    'ProbjectId': relation.ProbjectId,
                    'UserId': relation.UserId,
                    'TrueName': member.TrueName
                })
        res = response.Content()
        res.data = query
        return Response(
            res.ok('SUC').raw()
        )

    @action(methods=['post'], detail=False)
    def GetProbjectListByCoach(self, request):
        coachid = request.user.pk
        prids = models.AppProbjectrelation.objects.filter(CoachId=coachid).values_list('ProbjectId')
        data = models.AppProbject.objects.filter(Probject_Id__in=prids, Enable=1)
        return Response(
            self.page_data(data)
        )

    @action(methods=['get'], detail=False)
    def GetUserProbjectByCoach(self, request):
        now = datetime.datetime.now()
        proids = []
        status = int_arg(request.query_params.get('status'), 0)
        coachid = int_arg(request.query_params.get('coachid'), None)
        if status == 1:
            proids = models.AppProbject.objects.filter(Enable=1, Status=1, Endtime__lt=now).values_list('Probject_Id',
                                                                                                        flat=True)
        else:
            proids = models.AppProbject.objects.filter(Enable=1, Status=1, Starttime__lte=now,
                                                       Endtime__gt=now).values_list('Probject_Id', flat=True)
        relation_list = models.ProjectCoachInterview.objects.filter(ProbjectId__in=proids, CoachId=coachid,
                                                                    iscomplate=1, StudentStatus=1,
                                                                    StudentUserId__gt=0).order_by('-ModifyDate')
        query = distinct(relation_list, ['StudentUserId'])
        # 头像 姓名 职位 约谈 上次，下次 第N次 约谈形式
        listpro = []
        for item in query.all():
            userid = int_arg(item.StudentUserId, 0)
            projectid = item.ProbjectId
            umod = get_member_info(userid)
            if not umod:
                continue
            userinfo = models.SysUser.objects.filter(User_Id=userid).first()
            dicuser = {
                'Photograph': umod.Photograph,
                'TrueName': umod.TrueName,
                'Duty': umod.Duty,
                'CompanyName': umod.CompanyName,
                'ProName': get_project_info(projectid).Name,
                'MessageId': userinfo.MessageId
            }
            dicyt = {}
            # 约谈信息
            dicyt["yttype"] = get_project_config_val(projectid, "yttype")
            dicyt["ytcs"] = get_project_config_val(projectid, "ytcs")
            dicyt["ytsfht"] = get_project_config_val(projectid, "ytsfht")
            objytconfig = get_project_config_val(projectid, 'ytconfig' + '_' + str(userid))
            ytconfig = ''
            if objytconfig:
                ytconfig = str(objytconfig)
            dicyt["ytconfig"] = ytconfig
            # 最近两条约谈
            ytlist = models.AppProbjectinterview.objects.filter(User_Id=userid, ProbjectId=projectid,
                                                                status=1).order_by('Id')[:2]
            dicinv = {}
            if len(ytlist) > 0:
                if len(ytlist) == 2:
                    st = ytlist[0].StartTime
                    if st < datetime.datetime.now():
                        dicinv["lastinterview"] = ytlist[0].StartTime
                        dicinv["lastinterviewtitle"] = ytlist[0].Title
                        dicinv["nexinterview"] = ""
                    else:
                        dicinv["lastinterview"] = ytlist[0].StartTime
                        dicinv["lastinterviewtitle"] = ytlist[0].Title
                        dicinv["nexinterview"] = ytlist[0].StartTime
                        dicinv["nexinterviewtitle"] = ytlist[0].Title
                else:
                    st = ytlist[0].StartTime
                    if st < datetime.datetime.now():
                        dicinv["lastinterview"] = ytlist[0].StartTime
                        dicinv["lastinterviewtitle"] = ytlist[0].Title
                        dicinv["nexinterview"] = ""
                    else:
                        dicinv["lastinterview"] = ""
                        dicinv["nexinterview"] = ytlist[0].StartTime
                        dicinv["nexinterviewtitle"] = ytlist[0].Title
            else:
                dicinv["lastinterview"] = ""
                dicinv["nexinterview"] = ""
            ytcs = get_interview_nums(userid, projectid)
            if ytcs > 0:
                dicinv["ytcs"] = ytcs
            else:
                dicinv["ytcs"] = 1
            if ytconfig:
                arr = ytconfig.split(',')
                if ytcs >= len(arr):
                    dicinv["ytcst"] = arr[-1]
                else:
                    dicinv["ytcst"] = arr[ytcs]
            prodic = {
                'probjectid': item.ProbjectId,
                'userid': userid,
                'user': dicuser,
                'yt': dicyt,
                'interview': dicinv,
            }
            # 插入约谈次数 满意度，成长度，投入度
            if status == 1:
                cintervies = models.AppProbjectinterview.objects.filter(User_Id=userid, ProbjectId=projectid,
                                                                        Coach_Id=coachid, status=1,
                                                                        interviewType=0, Satisfaction__gt=0)
                times = cintervies.aggregate(Sum('Times'))['Times__sum']
                interviews = cintervies.count()
                myd = 0
                czd = 0
                trd = 0
                if interviews > 0:
                    myd = cintervies.aggregate(Sum('Satisfaction'))['Satisfaction__sum'] / interviews
                if interviews > 0:
                    czd = cintervies.aggregate(Sum('growupSatisfied'))['growupSatisfied__sum'] / interviews
                if interviews > 0:
                    myd = cintervies.aggregate(Sum('inputSatisfied'))['inputSatisfied__sum'] / interviews
                prodic["mytimes"] = times
                prodic["myinterviews"] = interviews
                prodic["mymyd"] = myd
                prodic["myczd"] = czd
                prodic["mytrd"] = trd
            listpro.append(prodic)
        res = response.Content()
        res.data = listpro
        return Response(
            res.ok('SUC').raw()
        )

    @action(methods=['get'], detail=False)
    def GetUserProbjectInterView(self, request):
        now = datetime.datetime.now()
        probjectid = int_arg(request.query_params.get('probjectid'), 0)
        status = int_arg(request.query_params.get('status'), 0)
        coachid = int_arg(request.query_params.get('coachid'), None)
        plist = models.AppProbject.objects.filter(Enable=1, Starttime__lte=now, Endtime__gt=now).values_list(
            'Probject_Id', flat=True)
        pquery = models.Projectcoach.objects.filter(CoachId=coachid, ProbjectId__in=plist).values_list('ProbjectId',
                                                                                                       flat=True)
        limit_list = []
        for item in pquery:
            pid = item
            if pid == 0:
                continue
            if status > 0:
                mtbjr = int_arg(get_project_config_val(pid, 'mtbjr'), None)
                if mtbjr == 1:
                    limit_list.append(pid)
            else:
                mtfzr = int_arg(get_project_config_val(pid, 'mtfzr'), None)
                if mtfzr == 1:
                    limit_list.append(pid)
        xlist = models.ProjectCoachInterview.objects.filter(CoachId=coachid, ProbjectId__in=limit_list)
        if status > 0:
            xlist = xlist.filter(iscomplate=1, Status=1, StudentStatus__in=[0, 1], StudentUserId__gt=0)
            # xlist = distinct(xlist, ['CoachId', 'StudentUserId'])
        else:
            xlist = xlist.filter(StudentUserId__isnull=True)
            # xlist = distinct(xlist, ['CoachId', 'StudentUserId'])
        if probjectid > 0:
            xlist = xlist.filter(ProbjectId=probjectid)
        if status > 0:
            xlist = xlist.order_by('-StudentinterviewTime', '-Id')
        else:
            xlist = xlist.order_by('-interviewTime', '-Id')
        listpro = []
        for item in xlist.all():
            prodic = {}
            _probjectid = item.ProbjectId
            userid = item.UserId
            suserid = item.StudentUserId
            _puid = userid
            if status > 0:
                _puid = suserid
            if _puid == 0:
                continue
            prodic["probjectid"] = _probjectid
            prodic["userid"] = _puid
            umod = get_member_info(_puid)
            dicuser = {}
            if umod:
                dicuser["Photograph"] = umod.Photograph
                dicuser["TrueName"] = umod.TrueName
                dicuser["Duty"] = umod.Duty
                dicuser["CompanyName"] = umod.CompanyName
            dicuser["ProName"] = get_project_info(_probjectid).Name
            userinfo = models.SysUser.objects.filter(User_Id=_puid).first()
            if userinfo:
                dicuser["MessageId"] = userinfo.MessageId
            prodic['user'] = dicuser
            prodic['interview'] = item.interviewTime
            if status > 0:
                prodic['interview'] = item.StudentinterviewTime
            prodic["qypici"] = item.qypici
            prodic["hxpici"] = item.hxpici
            prodic["Id"] = item.Id
            listpro.append(prodic)
        res = response.Content()
        res.data = listpro
        return Response(
            res.ok('SUC').raw()
        )

    @action(methods=['get'], detail=False)
    def GetUserInfoAndProbject(self, request):
        probjectid = int_arg(request.query_params.get('probjectid'), None)
        uid = int_arg(request.query_params.get('uid'), None)
        pinfo = get_project_info(probjectid)
        uinfo = get_member_info(uid)
        dic = {}
        dic["photograph"] = uinfo.Photograph
        dic["truename"] = uinfo.TrueName
        dic["email"] = uinfo.email
        dic["mobile"] = uinfo.mobile
        dic["companyname"] = uinfo.CompanyName
        department = ''
        if uinfo.Department:
            department = uinfo.Department
        duty = ''
        if uinfo.Duty:
            duty = uinfo.Duty
        dic["duty"] = department + " " + duty
        # WorkingDateNum CompanyWorkingDateNum GwWorkingDateNum
        dic["WorkingDateNum"] = get_work_num(uinfo.WorkingDate)
        dic["CompanyWorkingDateNum"] = get_work_num(uinfo.CompanyWorkingDate)
        dic["GwWorkingDateNum"] = get_work_num(uinfo.GwWorkingDate)
        dic["remark"] = uinfo.ManagerRemark
        dic["prozq"] = pinfo.Starttime.strftime('%Y-%m-%d') + "-" + pinfo.Endtime.strftime('%Y-%m-%d')
        mrytsc = get_project_config_val(probjectid, 'mrytsc')
        dic["proytsc"] = mrytsc
        dic["proyytsc"] = get_interview_times(uid, probjectid)
        oytcs = get_project_config_val(probjectid, 'ytcs')
        pytcs = int_arg(oytcs, 0)
        if not oytcs:
            pytcs = 1
        oytsfht = get_project_config_val(probjectid, 'ytsfht')
        pytsfht = int_arg(oytsfht, 0)
        if not oytsfht:
            pytsfht = 1
        ytcs = get_interview_nums(uid, probjectid)
        dic["proytcs"] = str(ytcs) + "/" + str((pytcs + pytsfht))
        res = response.Content()
        res.data = dic
        return Response(
            res.ok('SUC').raw()
        )

    @action(methods=['get'], detail=False)
    def GetUserInterestedInterView(self, request):
        probjectid = int_arg(request.query_params.get('probjectid'), None)
        uid = int_arg(request.query_params.get('uid'), None)
        coachid = request.user.pk
        retionusers = models.AppMemberinterested.objects.filter(ProbjectId=probjectid, MasterMember_Id=uid,
                                                                Enable__gte=0)
        listdata = []
        for item in retionusers.all():
            dic = {
                'Enable': item.Enable
            }
            uinfo = models.AppMember.objects.filter(User_Id=item.Member_Id).first()
            if uinfo:
                dic["User_Id"] = uinfo.User_Id
                dic["TrueName"] = uinfo.TrueName
                dic["Photograph"] = uinfo.Photograph
                dic["Department"] = uinfo.Department
                dic["Duty"] = uinfo.Duty
                dic["Relation"] = item.Relation
                cinfo = models.SysUser.objects.filter(User_Id=uinfo.User_Id).first()
                if cinfo:
                    dic['MessageId'] = cinfo.MessageId
                if uinfo.Enable == 0:
                    dic['yt'] = 0
                else:
                    ytlist = models.AppProbjectinterview.objects.filter(User_Id=item.Member_Id, ProbjectId=probjectid,
                                                                        status=1, interviewType=1,
                                                                        Coach_Id=coachid,
                                                                        masteruserid=item.MasterMember_Id).order_by(
                        'Id').first()
                    if ytlist:
                        ytinfo = ytlist
                        dic["yt"] = 1
                        dic["ytinterviewid"] = ytinfo.Id
                        dic["ytstatus"] = ytinfo.status
                        dic["ytstarttime"] = ytinfo.StartTime.strftime('%Y-%m-%d %H:%M')
                        dic["ytendtime"] = ytinfo.EndTime.strftime('%Y-%m-%d %H:%M')
                        dic["pici"] = ytinfo.pici
                        dic["Id"] = ytinfo.Id
                        dic["times"] = ytinfo.Times
                        dic["Satisfaction"] = ytinfo.Satisfaction
                    else:
                        moinfo = models.AppModelreportresult.objects.filter(ProbjectId=probjectid, DataId=item.Member_Id
                                                                            , ModelCode='r25').first()
                        if moinfo:
                            dic['pici'] = moinfo.pici
                        dic['yt'] = 0
                    # 判断是否有调查报告的
                    evuinfo = models.AppProbjectexamrelationuser.objects.filter(
                        Q(interested_id=uid) | Q(interested_id=0), ProbjectId=probjectid,
                        UserId=item.Member_Id
                        , PageCode='r24', TypeId=2).first()
                    if evuinfo:
                        dic["vote"] = 1
                        dic["pici"] = evuinfo.pici
                        if evuinfo.pici:
                            dic["date"] = evuinfo.CompleteDate.strftime('%Y-%m-%d %H:%M')
                        else:
                            dic["date"] = evuinfo.CreateDate.strftime('%Y-%m-%d %H:%M')
                    else:
                        dic["vote"] = 0
                listdata.append(dic)
        # 汇总的综合报告 /r241
        summary_exist = False
        summary_pici = None
        report = models.AppModelreportreport.objects.filter(ProbjectId=probjectid, UserId=uid, ReportCode='r241').first()
        if report:
            summary_exist = True
            summary_pici = report.pici
        stakeholder_interview_enable = int_arg(get_project_config_val(probjectid, 'mtlyxgz'), None)
        data = {
            'data': listdata,
            'stakeholder_interview_or_exam': stakeholder_interview_enable,
            'summary_exist': summary_exist,
            'summary_pici': summary_pici
        }
        res = response.Content()
        res.data = data
        return Response(
            res.ok('SUC').raw()
        )

    @action(methods=['get'], detail=False)
    def GetUserInterestedReport(self, request):
        probjectid = int_arg(request.query_params.get('probjectid'), None)
        uid = int_arg(request.query_params.get('uid'), None)
        utype = int_arg(request.query_params.get('utype'), 0)
        coach_id = request.user.pk
        retionusers = models.AppMemberinterested.objects.filter(ProbjectId=probjectid, MasterMember_Id=uid,
                                                                Enable__gte=0)
        retionusersids = retionusers.values_list('Member_Id', flat=True)
        r241 = models.AppModelreportreport.objects.filter(ProbjectId=probjectid, UserId=uid, ReportCode='r241').first()
        if r241:
            return Response(
                response.Content().error('已经生成过改被教练者的总结了').raw()
            )
        _modelcode = 'r25'
        if utype == 0:
            _modelcode = 'r24'
        result = models.AppModelreportresult.objects.filter(ProbjectId=probjectid, UserId__in=retionusersids,
                                                            ModelCode=_modelcode, DataId=uid)
        if _modelcode == 'r25':
            result = models.AppModelreportresult.objects.filter(ProbjectId=probjectid, DataId__in=retionusersids,
                                                                ModelCode=_modelcode, UserId=coach_id)
        if result.count() == 0:
            return Response(
                response.Content().error('还没有利益相关者完成调研访谈').raw()
            )
        dic = {}
        for item in result.all():
            uidx = item.UserId
            if _modelcode == 'r25':
                uidx = value_or_default(item.DataId, 0)
            uinfo = models.SysUser.objects.filter(User_Id=uidx).first()
            answer_json = json.loads(item.Answer)
            if answer_json:
                dic['userid'] = answer_json['userid']
                dic['truename'] = answer_json['truename']
                add_or_update_dic(dic, 'A1', uinfo.UserTrueName + " " + str(answer_json['A1']))
                add_or_update_dic(dic, 'A2', answer_json.get('A2', ''))
                add_or_update_dic(dic, 'A3', answer_json.get('A3', ''))
                add_or_update_dic(dic, 'A4', answer_json.get('A4', ''))
                add_or_update_dic(dic, 'A5', answer_json.get('A5', ''))
                add_or_update_dic(dic, 'A6', answer_json.get('A6', ''))

        return Response(
            response.Content().ok('SUC', dic).raw()
        )

    @action(methods=['get'], detail=False)
    def GetRelationUserByUser(self, request):
        probjectid = int_arg(request.query_params.get('probjectid'), 0)
        uid = request.user.pk
        if probjectid == 0:
            pinfo = get_project_info_by_user(uid)
            if pinfo:
                probjectid = pinfo.Probject_Id
        memberids = relation_filter_by_interest([uid]).filter(ProbjectId=probjectid).values_list('UserId', flat=True)
        listdata = []

        if len(memberids) > 0:
            ids = ' WHERE m.User_Id IN ('
            for memberid in memberids:
                ids += str(memberid) + ','
            ids = ids[:len(ids) - 1] + ')'
            listres = models.AppMember.objects.raw(
                'SELECT u.UserName, u.User_Id, m.Member_Id, m.email, m.mobile, m.TrueName, m.Photograph, m.Department, m.Duty, m.Relation FROM app_member AS m INNER JOIN sys_user AS u ON m.User_Id = u.User_id' + ids)

            for res in listres:
                relation = res.Relation
                if not res.Relation:
                    interest = models.AppMemberinterested.objects.filter(Member_Id=res.User_Id,
                                                                         MasterMember_Id=str(uid)).first()
                    if interest:
                        relation = interest.Relation
                listdata.append({
                    'UserName': res.UserName,
                    'User_Id': res.User_Id,
                    'Member_Id': res.Member_Id,
                    'email': res.email,
                    'mobile': res.mobile,
                    'TrueName': res.TrueName,
                    'Photograph': res.Photograph,
                    'Department': res.Department,
                    'Duty': res.Duty,
                    'Relation': relation,
                })

        mtlyxgz = int_arg(get_project_config_val(probjectid, 'mtlyxgz'), None)
        resdata = []
        for item in listdata:
            dic = item
            ytlist = models.AppProbjectinterview.objects.filter(User_Id=item.get('User_Id'),
                                                                ProbjectId=probjectid,
                                                                masteruserid=uid, interviewType=1).order_by(
                'Id').first()
            if ytlist:
                dic["yt"] = 1
                dic["ytstatus"] = ytlist.status
                dic["ytstarttime"] = ytlist.StartTime
                dic["ytendtime"] = ytlist.EndTime
            else:
                dic["yt"] = 0
            dic["mtlyxgz"] = mtlyxgz
            resdata.append(dic)
        res = response.Content()
        res.data = resdata
        return Response(
            res.ok('SUC').raw()
        )

    @action(methods=['get'], detail=False)
    def GetUserAndProbjectbyRelation(self, request):
        uid = int_arg(request.query_params.get('uid'), None)
        dt = datetime.datetime.now().strftime('%Y-%m-%d %H:%M')
        plist = models.AppProbject.objects.raw(
            'SELECT a.Name, b.ProbjectId as Probject_Id, b.MasterMember_id as Userid FROM app_probject AS a INNER JOIN app_memberinterested AS b ON a.Probject_Id = b.ProbjectId WHERE b.Member_Id=' + str(
                uid) + " AND a.Enable=1 AND a.Starttime<=\'" + dt + "\' AND a.Endtime>\'" + dt + "\'")
        listdata = []
        for item in plist:
            mtlyxgz = int_arg(get_project_config_val(value_or_default(item.Probject_Id, 0), 'mtlyxgz'), None)
            if mtlyxgz == 0:
                continue
            dic = {
                'Name': item.Name,
                'ProbjectId': item.Probject_Id,
                'Userid': item.Userid
            }
            uinfo = models.SysUser.objects.filter(User_Id=item.Userid).first()
            if uinfo:
                dic["TrueName"] = uinfo.UserTrueName
            prinfo = models.AppProbjectrelation.objects.filter(ProbjectId=item.Probject_Id, UserId=item.Userid).first()
            if prinfo:
                dic["CoachId"] = prinfo.CoachId
                cinfo = models.SysUser.objects.filter(User_Id=prinfo.CoachId).first()
                if cinfo:
                    dic["CoachId"] = cinfo.User_Id
                    dic["CoachTrueName"] = cinfo.UserTrueName
                    dic["CoachMessageId"] = cinfo.MessageId
                    dic["CoachCoachImg"] = cinfo.HeadImageUrl
            ytlist = models.AppProbjectinterview.objects.filter(User_Id=uid, ProbjectId=item.Probject_Id,
                                                                status=1, Coach_Id=prinfo.CoachId,
                                                                masteruserid=item.Userid).order_by(
                'Id').first()
            if ytlist:
                ytinfo = ytlist
                dic["yt"] = 1
                dic["Id"] = ytinfo.Id
                dic["ytstatus"] = ytinfo.status
                dic["ytstarttime"] = ytinfo.StartTime.strftime('%Y-%m-%d %H:%M')
                dic["ytendtime"] = ytinfo.EndTime.strftime('%Y-%m-%d %H:%M')
                dic["pici"] = ytinfo.pici
                dic["times"] = ytinfo.Times
                dic["Satisfaction"] = ytinfo.Satisfaction
            else:
                dic["yt"] = 0
            listdata.append(dic)
        res = response.Content()
        res.data = listdata
        return Response(
            res.ok('SUC').raw()
        )

    @action(methods=['get'], detail=False)
    def GetTxNeedsCoachToCompany(self, request):
        probjectid = int_arg(request.query_params.get('probjectid'), None)
        x = models.AppProbjectrelation.objects.filter(ProbjectId=probjectid, IsManger=1)
        query = x.values_list('UserId', flat=True)
        if len(query) > 0:
            prinfo = get_project_info(probjectid)
            for item in query:
                uinfo = models.SysUser.objects.filter(User_Id=item).first()
                if uinfo:
                    dic = {
                        'probjectname': prinfo.Name
                    }
                    push_message.delay(uinfo, 'txcoachneed', dic, probjectid)
        return Response(
            response.Content().ok().raw()
        )

    @action(methods=['get'], detail=False)
    def GetTxInterviewCoachToRelation(self, request):
        uid = int_arg(request.query_params.get('uid'), None)
        relationid = int_arg(request.query_params.get('relationid'), None)
        prinfo = get_project_info_by_user(uid)
        if prinfo:
            uinfo = models.SysUser.objects.filter(User_Id=relationid).first()
            if uinfo:
                password = aesdecrypt(uinfo.UserPwd)
                proname = prinfo.Name
                manager = ''
                mobile = ''
                email = ''
                minfo = models.SysUser.objects.filter(User_Id=int_arg(prinfo.Relation_UserId, 0)).first()
                if minfo:
                    manager = minfo.UserTrueName
                    mobile = minfo.PhoneNo
                    email = minfo.Email
                dic = {}
                dic["password"] = password
                dic["probjectname"] = proname
                dic["manager"] = manager
                dic["mobile"] = mobile
                dic["email"] = email
                dic["fromname"] = request.user.first_name
                coachname = ''
                mrelation = models.AppProbjectrelation.objects.filter(ProbjectId=prinfo.Probject_Id, UserId=uid).first()
                if mrelation:
                    finfo = models.SysUser.objects.filter(User_Id=mrelation.CoachId).first()
                    if finfo:
                        coachname = finfo.UserTrueName
                dic["coachname"] = coachname
                mtlyxgz = int_arg(get_project_config_val(prinfo.Probject_Id, 'mtlyxgz'), None)
                if mtlyxgz and mtlyxgz == 0:
                    push_message.delay(uinfo, 'inviteinterestedvote', dic, prinfo.pk)
                else:
                    dic['url'] = settings.SITE_URL
                    push_message.delay(uinfo, 'inviteinterestedinterview', dic, prinfo.pk)
        return Response(
            response.Content().ok().raw()
        )

    @action(methods=['get'], detail=False)
    def GetTxInterViewToStudent(self, request):
        """ 教练提醒被教练者正式约谈（36） """
        uid = int_arg(request.query_params.get('uid'), None)  # 被提醒人id
        uinfo = models.SysUser.objects.filter(User_Id=uid).first()
        if uinfo:
            # password = aesdecrypt(uinfo.UserPwd)
            prinfo = get_project_info_by_user(uid)
            proname = ''
            manager = ''
            mobile = ''
            email = ''
            coach_name = ''

            if prinfo:
                proname = prinfo.Name
                minfo = models.SysUser.objects.filter(User_Id=int_arg(prinfo.Relation_UserId, 0)).first()
                try:
                    coach = models.Projectcoach.objects.get(ProbjectId=prinfo.pk, UserId=uinfo.pk).CoachId
                except models.Projectcoach.DoesNotExist:
                    coach = models.AppProbjectrelation.objects.filter(ProbjectId=prinfo.pk, RolesId=6,
                                                                      UserId=uinfo.pk).first().CoachId

                coach_name = models.SysUser.objects.get(pk=coach).UserTrueName

                if minfo:
                    manager = minfo.UserTrueName
                    mobile = minfo.PhoneNo
                    email = minfo.Email
            dic = {}
            dic["coach_name"] = coach_name
            dic['gender'] = settings.GENDER[uinfo.Gender] if uinfo.Gender else '先生/女士'
            dic["probjectname"] = proname
            dic["manager"] = manager
            dic["mobile"] = mobile
            dic["email"] = email
            dic['url'] = settings.SITE_URL
            dic['path_link'] = get_remindinterviewstudent_link()
            push_message.delay(uinfo, 'remindinterviewstudent', dic, prinfo.pk)

        return Response(
            response.Content().ok().raw()
        )

    @action(methods=['post'], detail=False)
    def GetTxInviteInterested(self, request):
        uid = int_arg(request.query_params.get('uid'), None)
        probjectid = int_arg(request.query_params.get('probjectid'), None)
        # 被教练者的id
        interestid = int_arg(request.query_params.get('interestid'), None)
        typeid = int_arg(request.query_params.get('typeid'), None)
        uinfo = models.SysUser.objects.filter(User_Id=interestid).first()
        if uinfo:
            password = aesdecrypt(uinfo.UserPwd)
            proname = ''
            manager = ''
            mobile = ''
            email = ''
            prinfo = get_project_info(probjectid)
            if prinfo:
                proname = prinfo.Name
                minfo = models.SysUser.objects.filter(User_Id=int_arg(prinfo.Relation_UserId, 0)).first()
                if minfo:
                    manager = minfo.UserTrueName
                    mobile = minfo.PhoneNo
                    email = minfo.Email
            coachname = ''
            mrelation = models.AppProbjectrelation.objects.filter(ProbjectId=prinfo.Probject_Id, UserId=uid).first()
            if mrelation:
                finfo = models.SysUser.objects.filter(User_Id=mrelation.CoachId).first()
                if finfo:
                    coachname = finfo.UserTrueName
            cinfo = models.SysUser.objects.filter(User_Id=uid).first()
            dic = {}
            dic["password"] = password
            dic["probjectname"] = proname
            dic["manager"] = manager
            dic["mobile"] = mobile
            dic["email"] = email
            dic["fromname"] = cinfo.UserTrueName
            dic["coachname"] = coachname
            dic["studentname"] = uinfo.UserTrueName
            dic['path_link'] = get_exam_r25_student_default_link(probjectid, uid)
            user = models.AppProbjectrelation.objects.filter(ProbjectId=probjectid, UserId=uinfo.pk, RolesId=6)
            if not user.exists():
                dic['path_link'] = get_exam_r25_interested_link(uinfo.pk)
            else:
                dic['path_link'] = get_exam_r25_student_link(uinfo.pk)

            # 教练操作页面按钮提醒用户预约利益相关者访谈 r25 (13)
            r25_remind = 'r25_remind'

            # r25利益相关者访谈-to student (12)
            # 利益相关者 typeid=2 教练
            if typeid == 2:
                push_message.delay(uinfo, r25_remind, dic, probjectid)

            # typeid=0 管理员
            else:
                dic['user_info'] = '请妥善保存以下信息：<br /> 用户名：{} <br /> 密码：{} <br />'.format(uinfo.UserTrueName, password)
                push_message.delay(uinfo, "exam_r25_student", dic, probjectid)
        return Response(
            response.Content().ok().raw()
        )
