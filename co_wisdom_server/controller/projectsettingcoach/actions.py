from data import models


def get_project_setting_all(projectid):
    setting_list = models.AppProbjectsettingcoach.objects.filter(Probject_Id=projectid)
    dic = {}
    for item in setting_list.all():
        key = item.KeyName.lower()
        dic[key] = item.KeyValue
    return dic

def get_project_config_val(pid, key):
    dic = get_project_setting_all(pid)
    if dic and hasattr(dic, key.lower()):
        obj = dic[key.lower()]
        if obj:
            return obj
    return None