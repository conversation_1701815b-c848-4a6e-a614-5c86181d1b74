import datetime

from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.viewsets import ModelViewSet, ViewSet, GenericViewSet

from controller.coach.actions import get_coach_info
from controller.company import views as company_views
from controller.company.actions import get_company_info
from controller.dictionary.actions import get_dictionary_list_value
from controller.member.actions import get_member_info
from controller.project.actions import get_project_info, get_project_def_coach_by_user, get_project_info_by_user
from controller.projectsettings.actions import get_project_config_val
from data import models, serializers, extension
from data.user.models import User
from utils import response, blank_to_none, int_arg, value_or_default, aesencrypt, randomPassword, float_arg, \
    null_or_blank
import json
from django.db.models import Avg, Count, Min, Sum


class ProjectRelationViewSet(extension.ResponseViewSet):
    queryset = models.AppProbjectrelation.objects.all()
    serializer_class = serializers.AppProbjectrelationSerializer

    @action(methods=['post'], detail=False)
    def getpagedata(self, request):
        t = self.get_page_rows(request.data)
        if t.count() > 0:
            extra = []
            for item in t.all():
                dic = {}
                dic['eId'] = item.Id
                uinfo = models.AppMember.objects.filter(User_Id=item.UserId).first()
                if uinfo:
                    dic["Photograph"] = uinfo.Photograph
                    dic["TrueName"] = uinfo.TrueName
                    dic["Department"] = uinfo.Department
                    dic["Duty"] = uinfo.Duty
                comodel = get_coach_info(item.CoachId)
                if comodel:
                    dic["img"] = comodel.Photograph
                    dic["coachlevel"] = get_dictionary_list_value("coachlevel", comodel.CoachLevel)
                    dic["jlzz"] = get_dictionary_list_value("jlzz", comodel.jlzz)
                    dic["jlzw"] = get_dictionary_list_value("jlzw", comodel.jlzw)
                    dic['coachtruename'] = comodel.TrueName
                extra.append(dic)
            t.extra_list = extra
        return Response(
            self.page_data(t)
        )

    @action(methods=['post'], detail=False)
    def getpagedatafront(self, request):
        t = self.get_page_rows(request.data)
        if t.count() > 0:
            extra = []
            for item in t.all():
                dic = {}
                dic['eId'] = item.Id
                uinfo = models.AppMember.objects.filter(User_Id=item.UserId).first()
                if uinfo:
                    dic["Photograph"] = uinfo.Photograph
                    dic["TrueName"] = uinfo.TrueName
                    dic["Department"] = uinfo.Department
                    dic["Duty"] = uinfo.Duty
                comodel = get_coach_info(item.CoachId)
                if comodel:
                    dic["img"] = comodel.Photograph
                    dic["coachlevel"] = get_dictionary_list_value("coachlevel", comodel.CoachLevel)
                    dic["jlzz"] = get_dictionary_list_value("jlzz", comodel.jlzz)
                    dic["jlzw"] = get_dictionary_list_value("jlzw", comodel.jlzw)
                    dic['coachtruename'] = comodel.TrueName
                extra.append(dic)
            t.extra_list = extra
        return Response(
            self.page_data(t)
        )

