import datetime
import random

from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.viewsets import ModelViewSet, ViewSet, GenericViewSet

from controller.coach.actions import get_coach_info
from controller.coachschedule.actions import get_coach_schedule_list
from controller.company import views as company_views
from controller.company.actions import get_company_info
from controller.member.actions import get_project_member_numbers, get_work_time, get_member_info, \
    add_or_update_member_interested, get_work_year
from controller.project.actions import get_project_info, get_project_def_coach_by_user, get_project_info_by_user, \
    get_project_company_manager
from controller.projectexam.actions import get_model_code
from controller.projectinterview.actions import get_interview_times, get_interview_nums
from controller.projectsettings.actions import get_project_config_val
from data import models, serializers, extension
from data.user.models import User
from utils import response, blank_to_none, int_arg, value_or_default, aesencrypt, randomPassword, float_arg, \
    null_or_blank, bool_arg, aesdecrypt
import json
from django.db.models import Avg, Count, Min, Sum

from utils.model import get_option, add_option
from utils.user import get_user_ids


class ProjectInterviewShareViewSet(extension.ResponseViewSet):
    queryset = models.AppProbjectInterviewShare.objects.all()
    serializer_class = serializers.AppProjectInterviewShareSerializer




