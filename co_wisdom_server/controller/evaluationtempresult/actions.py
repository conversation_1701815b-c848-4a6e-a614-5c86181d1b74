import datetime
import json

from rest_framework.response import Response

# from controller.evaluationresult.actions import get_score
from data import models
from utils import int_arg, response
from utils.business import new_pici





def get_tmp_resultid(userid, evaluationid, eqid, projectid=0):
    result = models.AppEvaluationtmpresult.objects.filter(ProbjectId=projectid, Userid=userid,
                                                          EvaluationId=evaluationid, EqId=eqid).first()
    if result:
        return result.Id
    return 0


def del_tmp_result(userid, evaluationid, projectid=0):
    result = models.AppEvaluationtmpresult.objects.filter(ProbjectId=projectid, Userid=userid,
                                                          EvaluationId=evaluationid).first()
    if result:
        result.delete()
