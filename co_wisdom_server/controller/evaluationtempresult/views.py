import datetime
import json

from rest_framework.decorators import action
from rest_framework.response import Response

from controller.evaluationresult.actions import get_score
from controller.evaluationtempresult.actions import get_tmp_resultid
from data import models, serializers, extension
from utils import int_arg, response
from utils.business import new_pici


class EvaluationTempResultViewSet(extension.ResponseViewSet):
    queryset = models.AppEvaluationtmpresult.objects.all()
    serializer_class = serializers.AppEvaluationtmpresultSerializer

    @action(methods=['post'], detail=False)
    def evaluationsubmit(self, request):
        main_data = request.data.get('mainData')
        evaluationid = int_arg(main_data.get('evaluationid'))
        if evaluationid == 0:
            return Response(
                response.Content().error('非法的测评提交').raw()
            )
        evalitem = request.data.get('Extra')
        if len(evalitem) < 1:
            return Response(
                response.Content().error('请填写后在提交').raw()
            )
        userid = request.user.pk
        probjectId = int_arg(main_data.get('probjectId'), 0)
        Interested_Id = int_arg(main_data.get('interestedid'), 0)
        pici = new_pici()
        for index, item in enumerate(evalitem):
            mo = models.AppEvaluationtmpresult()
            mo.Userid = userid
            mo.EvaluationId = evaluationid
            mo.ProbjectId = probjectId
            mo.EqId = int_arg(item.get("name"))
            mo.Answer = item.get("val", "")
            mo.OtherInput = item.get("input", "")
            mo.Score = get_score(mo.EqId, int_arg(mo.Answer))
            mo.CreateTime = datetime.datetime.now()
            mo.tIndex = index
            mo.pici = pici
            mo.Interested_Id = Interested_Id
            resultid = get_tmp_resultid(userid, evaluationid, mo.EqId, probjectId)
            if resultid > 0:
                mo.Id = resultid
            mo.save()
        return Response(
            response.Content().ok().raw()
        )
