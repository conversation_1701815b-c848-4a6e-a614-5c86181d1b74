import datetime
import json

from rest_framework.decorators import action
from rest_framework.response import Response

from controller.dictionary.actions import get_dictionary_list_value
from controller.evaluation.actions import get_evaluation_info
from controller.evaluationquestion.actions import get_evaluation_detail, get_evaluation_question
from controller.evaluationreport.actions import get_reportid
from controller.evaluationresult.actions import get_score, evaluation_view, get_relation_score, get_result_report, \
    get_variance, evaluation_result_other_lowest_score
from controller.evaluationtempresult.actions import del_tmp_result
from controller.project.actions import get_project_info
from data import models, serializers, extension
from utils import response, int_arg, null_or_blank, bool_arg
from utils.business import new_pici
from utils.model import add_option
from utils.user import get_user_ids
from utils.task import send_email_of_evaluation_end


class EvaluationResultViewSet(extension.ResponseViewSet):
    queryset = models.AppEvaluationresult.objects.all()
    serializer_class = serializers.AppEvaluationresultSerializer

    @action(methods=['post'], detail=False)
    def evaluationsubmit(self, request):
        main_data = request.data.get('mainData')
        evaluationid = int_arg(main_data.get('evaluationid'))
        if evaluationid == 0:
            return Response(
                response.Content().error('非法的测评提交').raw()
            )
        evalitem = request.data.get('Extra')
        evachild = get_evaluation_detail(evaluationid)
        if len(evalitem) != len(evachild):
            return Response(
                response.Content().error('请全部填写完整在提交').raw()
            )
        userid = request.user.pk
        probjectId = int_arg(main_data.get('probjectId'), 0)
        Interested_Id = int_arg(main_data.get('interestedid'), 0)
        if models.AppEvaluationresult.objects.filter(ProbjectId=probjectId, Userid=userid,
                                                     EvaluationId=evaluationid, Interested_Id=Interested_Id).exists():
            return Response(
                response.Content().error('您已经提交过了').raw()
            )
        pici = new_pici()
        for index, item in enumerate(evalitem):
            mo = models.AppEvaluationresult()
            mo.Userid = userid
            mo.EvaluationId = evaluationid
            mo.ProbjectId = probjectId
            mo.EqId = int_arg(item.get("name"))
            mo.Answer = item.get("val", "")
            mo.OtherInput = item.get("input", "")
            mo.Score = get_score(mo.EqId, int_arg(mo.Answer))
            mo.CreateTime = datetime.datetime.now()
            mo.tIndex = get_evaluation_question(mo.EqId).Sort
            mo.pici = pici
            mo.Interested_Id = Interested_Id
            mo.save()
            del_tmp_result(userid, evaluationid, probjectId)
            if probjectId > 0:
                evuinfo = models.AppProbjectexamrelationuser.objects.filter(ProbjectId=probjectId, UserId=userid,
                                                                            ExId=evaluationid, TypeId=1,
                                                                            interested_id=Interested_Id).first()
                if evuinfo:
                    evuinfo.Status = 1
                    evuinfo.CompleteDate = datetime.datetime.now()
                    evuinfo.save()
        send_email_of_evaluation_end.delay(probjectId, Interested_Id)
        return Response(
            response.Content().ok().raw()
        )

    @action(methods=['post'], detail=False)
    def getpagedata(self, request):
        for where in json.loads(request.data["wheres"]):
            if where['name'] == 'UserName':
                ids = get_user_ids(where['value'])
                if len(ids) > 0:
                    add_option(request.data, 'UserId', ids, 'checkbox')
            elif where['name'] == 'EvalName':
                ids = models.AppEvaluation.objects.filter(Enable=1, EvalName__icontains=where['value']).values_list(
                    'EvaluationId', flat=True)
                if len(ids) > 0:
                    add_option(request.data, 'EvaluationId', ids, 'checkbox')
            elif where['name'] == 'EvalType':
                ids = models.AppEvaluation.objects.filter(Enable=1, EvalType=int(where['value'])).values_list(
                    'EvaluationId', flat=True)
                if len(ids) > 0:
                    add_option(request.data, 'EvaluationId', ids, 'checkbox')
            elif where['name'] == 'ProName':
                ids = models.AppProbject.objects.filter(Enable=1, Name__icontains=where['value']).values_list(
                    'Probject_Id', flat=True)
                if len(ids) > 0:
                    add_option(request.data, 'ProbjectId', ids, 'checkbox')
            elif where['name'] == 'CoachId':
                ids = models.AppProbjectrelation.objects.filter(CoachId=int(where['value'])).values_list(
                    'UserId', flat=True)
                if len(ids) > 0:
                    add_option(request.data, 'Userid', ids, 'checkbox')
            else:
                add_option(request.data, where['name'], where['value'], where['displayType'])
        t = self.get_page_rows(request.data)
        if t.count() > 0:
            extra = []
            for item in t.all():
                dic = {}
                dic["eId"] = item.Id
                evmodel = get_evaluation_info(item.EvaluationId)
                dic["EvalType"] = evmodel.EvalType
                dic["EvalName"] = evmodel.EvalName
                dic["ProName"] = get_project_info(item.ProbjectId).Name
                username = models.SysUser.objects.filter(User_Id=item.Userid).first().UserName
                dic["UserName"] = username
                dic['ReportId'] = get_reportid(item.Userid, item.EvaluationId, item.ProbjectId)
                extra.append(dic)
            t.extra_list = extra
        return Response(
            self.page_data(t)
        )

    @action(methods=['post'], detail=False, url_path='del')
    def Del(self, request):
        main_data = request.data.get('mainData')
        keys = main_data.get('keys')
        delList = bool_arg(main_data.get('delList'), True)
        for item in keys:
            mo = models.AppEvaluationresult.objects.filter(Id=item).first()
            if mo:
                mo.delete()
        return Response(
            response.Content().ok().lower_raw()
        )

    @action(methods=['post'], detail=False)
    def DelResult(self, request):
        main_data = request.data.get('mainData')
        userid = int_arg(main_data.get('userid'))
        evaluationid = int_arg(main_data.get('evaluationid'))
        probjectid = int_arg(main_data.get('probjectid'), 0)
        mo = models.AppEvaluationresult.objects.filter(ProbjectId=probjectid, Userid=userid, EvaluationId=evaluationid)
        if mo.exists():
            mo.all().delete()
        return Response(
            response.Content().ok().raw()
        )

    # 测评者信息 关联者信息
    #         /// 9项能力+12项行为 分 开放问题汇总
    @action(methods=['post'], detail=False)
    def ExportResultReport(self, request):
        main_data = request.data.get('mainData')
        resultid = int_arg(main_data.get('resultid'))
        mo = models.AppEvaluationresult.objects.filter(Id=resultid).first()
        if not mo:
            return Response(
                response.Content().error('非法的报告数据').raw()
            )
        wheres = 'Userid=' + str(mo.Userid) + ' and ev.EvaluationId=' + str(mo.EvaluationId)
        if mo.ProbjectId > 0:
            wheres += ' and ProbjectId=' + str(mo.ProbjectId)
        res_list = evaluation_view(wheres)
        for item in res_list:
            userinfo = models.SysUser.objects.filter(User_Id=item.Userid).first()
            item.UserName = userinfo.UserTrueName
            if not null_or_blank(item.behaviour):
                item.behaviour = get_dictionary_list_value('lbibehaviour', item.behaviour)
            if item.TypeId == 1:
                item.Answer = ''
        # TODO export excel
        return Response(
            response.Content().ok().raw()
        )

    @action(methods=['get'], detail=False)
    def Analyse(self, request):
        resultid = int_arg(request.query_params.get('resultid'), None)
        resulttype = int_arg(request.query_params.get('resulttype'), 0)
        res = models.AppEvaluationresult.objects.filter(Id=resultid).first()
        if not res:
            return Response(
                response.Content().error('非法的报告数据').raw()
            )
        lbi_finished_user_list = models.AppProbjectexamrelationuser.objects.filter(ProbjectId=res.ProbjectId,
                                                                                   interested_id=res.Userid, Status=1,
                                                                                   ExId=18).values_list('UserId',
                                                                                                        flat=True)

        relation_list = models.AppMemberinterested.objects.filter(ProbjectId=res.ProbjectId, MasterMember_Id=res.Userid,
                                                                  Member_Id__in=lbi_finished_user_list)
        wheres = 'Userid=' + str(res.Userid) + ' and ev.EvaluationId=' + str(res.EvaluationId)
        if res.ProbjectId > 0:
            wheres += ' and ProbjectId=' + str(res.ProbjectId)
        if resulttype == 1:
            wheres += " and ability<>''"
        elif resulttype == 2:
            wheres += " and behaviour<>''"
        wheres += ' and TypeId=1 order by sort asc'
        res_list = evaluation_view(wheres)
        eva_list = []
        for item in res_list:
            dic = {}
            dic["ProbjectId"] = item.ProbjectId
            dic["Userid"] = item.Userid
            dic["EvaluationId"] = item.EvaluationId
            dic["EQid"] = item.EQid
            dic["Sort"] = item.Sort
            dic["Title"] = item.Title
            dic["ability"] = item.ability
            dic["behaviour"] = item.behaviour
            dic["Score"] = item.Score
            for index, relation_item in enumerate(relation_list.all()):
                key = 'Score' + str(index)
                score = get_relation_score(relation_item.Member_Id, item.ProbjectId, item.Sort, relation_item.MasterMember_Id)
                if score == 0:
                    continue
                dic[key] = score
            eva_list.append(dic)
        listcol = []
        for index, item in enumerate(relation_list.all()):
            dic = {}
            relation = item.Relation
            user = models.SysUser.objects.filter(User_Id=item.Member_Id).first()
            dic["field"] = "Score" + str(index)
            dic["title"] = user.UserTrueName + "(" + relation + ")"
            dic["type"] = "int"
            dic["width"] = 70
            dic["align"] = "left"
            listcol.append(dic)
        data = {
            'data': eva_list,
            'columns': listcol
        }
        return Response(
            response.Content().ok('suc', data).raw()
        )

    @action(methods=['get'], detail=False)
    def Analyse10(self, request):
        resultid = int_arg(request.query_params.get('resultid'), None)
        steps = int_arg(request.query_params.get('steps'))
        res = models.AppEvaluationresult.objects.filter(Id=resultid).first()
        if not res:
            return Response(
                response.Content().error('非法的报告数据').raw()
            )
        wheres = 'Userid=' + str(res.Userid) + ' and ev.EvaluationId=' + str(res.EvaluationId)
        if res.ProbjectId > 0:
            wheres += ' and ProbjectId=' + str(res.ProbjectId)
        if steps <= 2:
            query = []
            if steps == 1:
                query = evaluation_view(wheres + ' and TypeId=1 order by Score desc, sort asc limit 10')
            else:
                query = evaluation_view(wheres + ' and TypeId=1 order by Score asc, sort asc limit 10')
            eva_list = []
            for item in query:
                dic = {}
                dic["ProbjectId"] = item.ProbjectId
                dic["Userid"] = item.Userid
                dic["EvaluationId"] = item.EvaluationId
                dic["EQid"] = item.EQid
                dic["Sort"] = item.Sort
                dic["Title"] = item.Title
                dic["ability"] = item.ability
                dic["behaviour"] = item.behaviour
                dic["Score"] = item.Score
                eva_list.append(dic)
            data = {
                'data': eva_list,
                'columns': []
            }
            return Response(
                response.Content().ok('suc', data).raw()
            )
        # 他测  EvalType=2 包含领导力及行为
        lbi_finished_user_list = models.AppProbjectexamrelationuser.objects.filter(ProbjectId=res.ProbjectId,
                                                                                   interested_id=res.Userid, Status=1,
                                                                                   ExId=18).values_list('UserId',
                                                                                                        flat=True)

        memberInterestedList = models.AppMemberinterested.objects.filter(ProbjectId=res.ProbjectId,
                                                                         MasterMember_Id=res.Userid,
                                                                         Member_Id__in=lbi_finished_user_list)

        memberids = memberInterestedList.values_list('Member_Id', flat=True)
        group_relation = {}
        for item in memberInterestedList:
            if item.Relation not in group_relation:
                group_relation[item.Relation] = [item.Member_Id]
            else:
                group_relation[item.Relation].append(item.Member_Id)
        sql = 'Userid in (' + ','.join(str(x) for x in memberids) + ') and EvalType=2 and Interested_Id=(' + str(
            res.Userid) + ') '
        if res.ProbjectId > 0:
            sql += 'and ProbjectId=' + str(res.ProbjectId)
        if steps == 3:
            listcy = []
            cy_sql = wheres + ' and TypeId=1 and behaviour is not null order by sort asc'
            cybehaviour = evaluation_view(cy_sql)
            for item in cybehaviour:
                listvar = [float(item.Score)]
                tpretions_sql = sql + ' and Sort=' + str(item.Sort)
                _tplist = []
                for k, v in group_relation.items():
                    if len(v) > 0:
                        sum_res = evaluation_view(
                            tpretions_sql + ' and Userid in (' + ','.join(str(x) for x in v) + ')')
                        score_sum = 0
                        for j in sum_res:
                            score_sum += j.Score
                        avg_score = round(score_sum / len(v), 2)
                        listvar.append(avg_score)
                        _tplist.append({'relation': k, 'AveScore': avg_score})
                listcy.append({
                    'Title': item.Title,
                    'zpsocre': item.Score,
                    'Sort': item.Sort,
                    'TP': _tplist,
                    'cy': get_variance(listvar)
                })
            data_list = []
            newlist = sorted(listcy, key=lambda k: k['cy'], reverse=True)[:10]
            for itemx in newlist:
                dic = {}
                eva_list = evaluation_view(wheres + ' and TypeId=1 and Sort=' + str(itemx.get('Sort')))
                item = eva_list[0]
                if item:
                    dic["ProbjectId"] = item.ProbjectId
                    dic["Userid"] = item.Userid
                    dic["EvaluationId"] = item.EvaluationId
                    dic["EQid"] = item.EQid
                    dic["Sort"] = item.Sort
                    dic["Title"] = item.Title
                    dic["ability"] = item.ability
                    dic["behaviour"] = item.behaviour
                    dic["Score"] = itemx.get('cy')
                data_list.append(dic)
            return Response(
                response.Content().ok('suc', {
                    'data': data_list,
                    'columns': []
                }).raw()
            )
        if steps == 4:
            data_list = []
            query = evaluation_result_other_lowest_score(sql + ' GROUP BY sort ORDER BY total ASC, sort ASC limit 10')
            for item in query:
                dic = {}
                dic["ProbjectId"] = item.ProbjectId
                dic["Userid"] = item.Userid
                dic["EvaluationId"] = item.EvaluationId
                dic["EQid"] = item.EQid
                dic["Sort"] = item.Sort
                dic["Title"] = item.Title
                dic["ability"] = item.ability
                dic["behaviour"] = item.behaviour
                dic["Score"] = round(item.total / len(memberids), 2)
                data_list.append(dic)
            return Response(
                response.Content().ok('suc', {
                    'data': data_list,
                    'columns': []
                }).raw()
            )
        return Response(
            response.Content().error('未知的选择').raw()
        )

    @action(methods=['get'], detail=False)
    def GetResultReport(self, request):
        resultid = int_arg(request.query_params.get('resultid'), None)
        analyseids = request.query_params.get('analyseids', '')
        get_result_report(resultid, analyseids)
        return Response(
            response.Content().raw()
        )

    @action(methods=['post'], detail=False)
    def GetResultReportView(self, request):
        resultid = int_arg(request.query_params.get('resultid'), None)
        reportId = int_arg(request.query_params.get('reportid'), 0)
        if reportId > 0:
            res = models.AppEvalutionreport.objects.filter(pk=reportId).first()
            if res:
                data = json.loads(res.reportjson)
                return Response(
                    response.Content().ok('SUC', data).raw()
                )
        res = models.AppEvaluationresult.objects.filter(Id=resultid).first()
        if not res:
            return Response(
                response.Content().error('非法的报告数据').raw()
            )
        report_info = models.AppEvalutionreport.objects.filter(Userid=res.Userid, EvaluationId=res.EvaluationId,
                                                               ProbjectId=res.ProbjectId).first()
        if report_info:
            data = json.loads(report_info.reportjson)
            return Response(
                response.Content().ok('SUC', data).raw()
            )
        return get_result_report(resultid)
