import datetime
from statistics import variance

from django.db.models import F
from rest_framework.response import Response

from controller.company.actions import get_company_info
from controller.evaluationquestion.actions import get_evaluation_question
from controller.projectrelation.actions import relation_filter_by_interest
from data import models
from utils import null_or_blank, response, int_arg


def get_score(eqid, qid):
    mo = get_evaluation_question(eqid)
    if mo:
        option_list = models.AppEvaluationoption.objects.filter(EQid=mo.pk)
        if option_list.exists():
            for index, item in enumerate(list(option_list)):
                if item.Id == qid:
                    return index+1
        else:
            score = qid - 3588
            return score
    return 0


def evaluation_view(wheres):
    sql = 'SELECT eq.Title, eq.ability, eq.behaviour, eq.TypeId, re.Userid, re.Answer, re.Score, ev.EvaluationId, ev.EvalName, ev.EvalType, eq.Sort, re.ProbjectId, re.Interested_Id, re.pici, eq.EQid FROM app_evaluationquestion AS eq INNER JOIN app_evaluationresult AS re ON eq.EQid = re.EqId INNER JOIN app_evaluation AS ev ON eq.EvaluationId = ev.EvaluationId'
    if not null_or_blank(wheres):
        sql += ' where ' + wheres
    res_list = models.AppEvaluation.objects.raw(sql)
    return res_list


def evaluation_report_sum_score(wheres):
    sql = 'SELECT SUM(re.Score) AS total, COUNT(re.Score) AS c, eq.Title, eq.ability, eq.behaviour, eq.TypeId, re.Userid, re.Answer, re.Score, ev.EvaluationId, ev.EvalName, ev.EvalType, eq.Sort, re.ProbjectId, re.Interested_Id, re.pici, eq.EQid FROM app_evaluationquestion AS eq INNER JOIN app_evaluationresult AS re ON eq.EQid = re.EqId INNER JOIN app_evaluation AS ev ON eq.EvaluationId = ev.EvaluationId'
    if not null_or_blank(wheres):
        sql += ' where ' + wheres
    res_list = models.AppEvaluation.objects.raw(sql)
    return res_list


def evaluation_report_title_score(wheres):
    sql = 'SELECT eq.Title, re.Score, eq.EQid FROM app_evaluationquestion AS eq INNER JOIN app_evaluationresult AS re ON eq.EQid = re.EqId INNER JOIN app_evaluation AS ev ON eq.EvaluationId = ev.EvaluationId'
    if not null_or_blank(wheres):
        sql += ' where ' + wheres
    res_list = models.AppEvaluationquestion.objects.raw(sql)
    return res_list


def evaluation_result_other_lowest_score(wheres):
    sql = 'SELECT eq.Title, eq.ability, eq.behaviour, eq.TypeId, re.Userid, re.Answer, re.Score, ev.EvaluationId, ev.EvalName, ev.EvalType, eq.Sort, re.ProbjectId, re.Interested_Id, re.pici, eq.EQid, SUM(re.Score) AS total FROM app_evaluationquestion AS eq INNER JOIN app_evaluationresult AS re ON eq.EQid = re.EqId INNER JOIN app_evaluation AS ev ON eq.EvaluationId = ev.EvaluationId'
    if not null_or_blank(wheres):
        sql += ' where ' + wheres
    res_list = models.AppEvaluation.objects.raw(sql)
    return res_list


def get_relation_score(userid, projectid, sort, interestedid):
    wheres = 'Userid=' + str(userid) + ' and Sort=' + str(sort) + ' and ProbjectId=' + str(projectid) + ' and Interested_Id=' + str(interestedid)
    res_list = evaluation_view(wheres)
    if len(res_list) > 0:
        return res_list[0].Score
    return 0


def get_result_report(resultid, analyseids=''):
    res = models.AppEvaluationresult.objects.filter(Id=resultid).first()
    if not res:
        return Response(
            response.Content().error('非法的报告数据').raw()
        )
    lbi_finished_user_list = models.AppProbjectexamrelationuser.objects.filter(ProbjectId=res.ProbjectId,
                                                                               interested_id=res.Userid, Status=1,
                                                                               ExId=18).values_list('UserId',
                                                                                                    flat=True)

    member_interested_list = models.AppMemberinterested.objects.filter(ProbjectId=res.ProbjectId,
                                                                     MasterMember_Id=res.Userid,
                                                                     Member_Id__in=lbi_finished_user_list)

    memberids = member_interested_list.values_list('Member_Id', flat=True)
    if not memberids:
        memberids = [0]
    dicbeh = list(models.SysDictionaryList.objects.filter(dictionary__dic_no='lbibehaviour', enable=1).order_by('-order_no').annotate(
        key=F('dic_value'), name=F('dic_name')).values('key', 'name'))
    mouser = models.AppMember.objects.filter(User_Id=res.Userid).first()
    if not mouser:
        return Response(
            response.Content().error('没找到用户').raw()
        )
    cinfo = get_company_info(mouser.Company_Id)
    mdata = {
        'userid': mouser.User_Id,
        'truename': mouser.TrueName,
        'duty': mouser.Duty,
        'compayname': cinfo.CompanyName, # TODO api typo
    }
    otheruser = models.AppMember.objects.filter(User_Id__in=memberids)
    otheruser_result = []
    group_relation = {}
    for item in otheruser.all():
        interested_info = member_interested_list.filter(Member_Id=item.User_Id).first()
        relation = ''
        concertyears = 0
        if interested_info:
            relation = interested_info.Relation
            concertyears = interested_info.Concertyears
        dic = {
            'userid': int_arg(item.User_Id, 0),
            'truename': item.TrueName,
            'duty': item.Duty,
            'relation': relation,
            'concertyears': concertyears
        }
        otheruser_result.append(dic)
        if relation not in group_relation:
            group_relation[relation] = [item.User_Id]
        else:
            group_relation[relation].append(item.User_Id)
    # 报告时间
    reportdate = datetime.datetime.now().strftime('%y-%m-%d %H:%M:%S')
    reportInfo = models.AppProbjectexamrelationuser.objects.filter(ProbjectId=res.ProbjectId, UserId=res.Userid,
                                                                   ExId=res.EvaluationId).first()
    if reportInfo and reportInfo.ModifyDate:
        reportdate = reportInfo.ModifyDate.strftime('%y-%m-%d %H:%M:%S')
    # 自测 EvalType=1  and ability <> ''
    sql = 'Userid=' + str(res.Userid) + ' and ev.EvaluationId=' + str(res.EvaluationId)
    if res.ProbjectId > 0:
        sql += ' and ProbjectId=' + str(res.ProbjectId)

    group_ability = evaluation_report_sum_score(
        sql + ' and TypeId=1 and ability is not null group by ability order by ability asc')
    dicAbil = {}
    for item in group_ability:
        dicAbil[item.get('ability')] = round(float(item.get('total')) / item.get('c'), 2)
    kf_self = evaluation_view(sql + ' and TypeId=0')
    kfSelf_res = []
    for item in kf_self:
        kfSelf_res.append(item.get('Answer'))
    # 加入指定题目
    anids = analyseids.split('|')
    top5_sql = sql + ' order by Score desc limit 5'
    lower5_sql = sql + ' order by Score asc limit 5'
    if anids and len(anids) >= 1:
        tmlist = anids[0].split(',')
        sort_list = []
        for item in tmlist:
            sort_list.append(int(item))
        top5_sql = sql + ' and Sort in (' + ','.join(str(x) for x in sort_list) + ')  order by Score desc limit 5'
    if anids and len(anids) >= 2:
        tmlist = anids[1].split(',')
        sort_list = []
        for item in tmlist:
            sort_list.append(int(item))
        lower5_sql = sql + ' and Sort in (' + ','.join(str(x) for x in sort_list) + ')  order by Score asc limit 5'
    top5_res = evaluation_report_title_score(top5_sql)
    top5_res_data = []
    if len(top5_res) > 0:
        for item in top5_res:
            top5_res_data.append({
                'Title': item.Title,
                'Score': item.Score
            })
    lower5_res = evaluation_report_title_score(lower5_sql)
    lower5_res_data = []
    if len(lower5_res) > 0:
        for item in lower5_res:
            lower5_res_data.append({
                'Title': item.Title,
                'Score': item.Score
            })
    # 行为
    group_behaviour = evaluation_report_sum_score(
        sql + ' and TypeId=1 and behaviour is not null group by behaviour order by behaviour asc')
    dic_behaviour = {}
    for item in group_behaviour:
        dic_behaviour[str(item.behaviour).lower()] = round(float(item.total) / item.c, 2)

    # 他测  EvalType=2 包含领导力及行为
    other_sql = 'Userid in (' + ','.join(str(x) for x in memberids) + ') and EvalType=2 and Interested_Id=(' + str(res.Userid) + ') '
    if res.ProbjectId > 0:
        other_sql += 'and ProbjectId=' + str(res.ProbjectId)
    listcy = []
    cy_sql = sql + ' and TypeId=1 and behaviour is not null order by sort asc'
    cybehaviour = evaluation_view(cy_sql)
    for item in cybehaviour:
        listvar = [float(item.Score)]
        tpretions_sql = other_sql + ' and Sort=' + str(item.Sort)
        _tplist = []
        for k, v in group_relation.items():
            if len(v) > 0:
                sum_res = evaluation_view(tpretions_sql + ' and Userid in (' + ','.join(str(x) for x in v) + ')')
                score_sum = 0
                for j in sum_res:
                    score_sum += j.Score
                listvar.append(round(score_sum / len(v), 2))
                _tplist.append({'relation': k, 'AveScore': round(score_sum / len(v), 2)})
        listcy.append({
            'Title': item.Title,
            'zpsocre': item.Score,
            'Sort': item.Sort,
            'TP': _tplist,
            'cy': get_variance(listvar)
        })

    top5cy = sorted(listcy, key=lambda k: k['cy'], reverse=True)[:5]
    lower5cy_sql = other_sql + ' order by Score asc limit 5'
    if anids and len(anids) >= 3:
        tmlist = anids[2].split(',')
        sort_list = []
        for item in tmlist:
            sort_list.append(int(item))
        filter_list = list(filter(lambda x: x.get('Sort') in sort_list, listcy))
        top5cy = sorted(filter_list, key=lambda k: k['cy'], reverse=True)[:5]
    if anids and len(anids) >= 4:
        fourth_answer = anids[3]
        if not null_or_blank(fourth_answer):
            tmlist = anids[3].split(',')
            sort_list = []
            for item in tmlist:
                sort_list.append(int(item))
            lower5cy_sql = other_sql + ' and Sort in (' + ','.join(str(x) for x in sort_list) + ')  order by Score asc limit 5'
    lower5Tr = evaluation_report_title_score(lower5cy_sql)
    lower5_tr_data = []
    if len(lower5Tr) > 0:
        for item in lower5Tr:
            lower5_tr_data.append({
                'Title': item.Title,
                'Score': item.Score
            })

    kfTr = evaluation_view(other_sql + ' and TypeId=0')
    kftr_res = []
    for item in kfTr:
        kftr_res.append(item.Answer)

    # 合并 用户 分组显示
    queryalitr = evaluation_view(other_sql + ' and TypeId=1 and behaviour is not null order by behaviour asc ')
    queryali = evaluation_view(sql + ' and TypeId=1 and behaviour is not null order by behaviour asc')
    havRelation = True
    if len(memberids) == 1 and memberids[0] == 0:
        havRelation = False
    group_behaviour_all = {}
    for item in queryalitr:
        if str(item.behaviour) not in group_behaviour_all:
            group_behaviour_all[str(item.behaviour)] = [item]
        else:
            group_behaviour_all[str(item.behaviour)].append(item)
    for item in queryali:
        if str(item.behaviour) not in group_behaviour_all:
            group_behaviour_all[str(item.behaviour)] = [item]
        else:
            group_behaviour_all[str(item.behaviour)].append(item)
    dicAllbehav = {}
    for k, v in group_behaviour_all.items():
        udic = {}
        group_user = {}
        for i in v:
            if str(i.Userid) not in group_user:
                group_user[str(i.Userid)] = [i]
            else:
                group_user[str(i.Userid)].append(i)
        for kk, vv in group_user.items():
            if res.Userid == kk and str(k.lower()) in dic_behaviour:
                udic[str(k)] = dic_behaviour[str(k.lower())]
            else:
                if len(vv) > 0:
                    total = 0
                    for sub in vv:
                        total += sub.Score
                    udic[str(kk)] = round(float(total) / len(vv), 2)
        dicAllbehav[str(k.lower())] = udic
    data = {
        'reportdate': reportdate,
        'dicbeh': dicbeh,
        'user': mdata,
        'relation': otheruser_result,
        'ability': dicAbil,
        'behaviour': dic_behaviour,
        'kfself': kfSelf_res,
        'top5self': top5_res_data,
        'lower5self': lower5_res_data,
        'kftr': kftr_res,
        'top5cy': top5cy,
        'lower5tr': lower5_tr_data,
        'dicAllbehav': dicAllbehav,
        'havrelation': havRelation
    }
    return Response(
        response.Content().ok('suc', data).raw()
    )


def get_variance(inputs):
    if len(inputs) == 0:
        return 0
    if len(inputs) == 1:
        return inputs[0]
    base = inputs[0]
    other = 0
    for item in inputs[1:]:
        other += item
    return round(abs(base - other/len(inputs[1:])), 2)
