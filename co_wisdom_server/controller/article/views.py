from rest_framework.decorators import action
from rest_framework.response import Response

from data import models, serializers, extension
from utils import response, int_arg, null_or_blank


class ArticleViewSet(extension.ResponseViewSet):
    queryset = models.AppArticle.objects.all()
    serializer_class = serializers.AppArticleSerializer

    @action(methods=['post'], detail=False)
    def add(self, request):
        main_data = request.data.get('mainData')
        pagecode = main_data.get('PageCode')
        if null_or_blank(pagecode):
            return Response(
                response.Content().error('请输入调用标识').raw()
            )
        if models.AppArticle.objects.filter(PageCode=pagecode).exists():
            return Response(
                response.Content().error('重复的调用标识').raw()
            )
        res = self.add_row(main_data)
        return Response(
            res.raw()
        )

    @action(methods=['get'], detail=False)
    def GetUseHelp(self, request):
        permiss = int_arg(request.query_params.get('permiss'), 1)
        cateid = int_arg(request.query_params.get('cateid'), 0)
        res_list = None
        if cateid == 0:
            typesid = models.AppArticlecategory.objects.filter(ParentId=4).values_list('Cate_Id', flat=True)
            res_list = models.AppArticle.objects.filter(Enable=1, TypeId__in=typesid)
        else:
            res_list = models.AppArticle.objects.filter(Enable=1, TypeId=cateid)
        if permiss == 1:
            roleid = models.SysUser.objects.filter(User_Id=request.user.pk).first().Role_Id
            res_list = res_list.filter(ShareUsers__contains=roleid)
        data = res_list.order_by('-Sort').values('Id', 'Title', 'TypeId', 'ShareUsers', 'Contents', 'PageCode')
        return Response(
            response.Content().ok('SUC', data).raw()
        )

    @action(methods=['get'], detail=False)
    def GetArticleInfo(self, request):
        pagecode = request.query_params.get('pagecode')
        artid = int_arg(request.query_params.get('artid'), 0)
        if artid > 0:
            ino = models.AppArticle.objects.filter(Id=artid, Enable=1).first()
            if ino:
                data = self.serializer_class(ino, many=False).data
                return Response(
                    response.Content().ok('SUC', data).raw()
                )
            return Response(
                response.Content().error('内容不存在或未开启').raw()
            )
        else:
            ino = models.AppArticle.objects.filter(PageCode=pagecode, Enable=1).first()
            if ino:
                data = self.serializer_class(ino, many=False).data
                return Response(
                    response.Content().ok('SUC', data).raw()
                )
            return Response(
                response.Content().error('内容不存在或未开启').raw()
            )
