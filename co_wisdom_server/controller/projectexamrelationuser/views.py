import datetime
import json

from django.db.models import Q
from rest_framework.decorators import action
from rest_framework.response import Response

from controller.project.actions import get_project_info
from controller.projectexam.actions import get_model_code
from data import models, serializers, extension
from utils import response, int_arg, value_or_default
from utils.model import add_option
from utils.user import get_user_ids


class ProjectExamRelationUserViewSet(extension.ResponseViewSet):
    queryset = models.AppProbjectexamrelationuser.objects.all()
    serializer_class = serializers.AppProbjectexamrelationuserSerializer

    # 获取利益者关联的测评/报告
    @action(methods=['get'], detail=False)
    def GetEvaluationToRelateionUser(self, request):
        ProbjectId = int_arg(request.query_params.get('ProbjectId'), 0)
        uid = int_arg(request.query_params.get('uid'), 0)
        typeid = int_arg(request.query_params.get('typeid'), 1)
        now_date = datetime.datetime.now().date()
        if uid == 0:
            uid = request.user.pk
        evalist = models.AppProbjectexamrelationuser.objects.filter(UserId=uid)
        if typeid > 0:
            evalist = evalist.filter(TypeId=typeid)
        query = evalist
        if ProbjectId > 0:
            query = query.filter(ProbjectId=ProbjectId)
        query = query.order_by('-Id')
        # 按项目获取被教人关系及排列
        listdata = []
        for item in query.all():
            probjectid = item.ProbjectId
            masteruid = models.AppMemberinterested.objects.filter(Member_Id=uid, ProbjectId=probjectid)
            if item.interested_id > 0:
                masteruid = masteruid.filter(MasterMember_Id=item.interested_id)
            dic = {}
            for u in masteruid.all():
                dic["ProbjectId"] = item.ProbjectId
                dic["ExId"] = item.ExId
                dic["ExName"] = item.ExName
                dic["Status"] = item.Status
                dic["CreateDate"] = item.CreateDate
                dic["EndDate"] = item.EndDate
                dic["ReportId"] = item.ReportId
                dic["TypeId"] = item.TypeId
                dic["pici"] = item.pici
                dic["ModeCode"] = get_model_code(value_or_default(item.TypeId, 0), item.ExId)
                dic["UserId"] = u.MasterMember_Id
                uinfo = models.SysUser.objects.filter(User_Id=u.MasterMember_Id).first()
                dic["UserTrueName"] = uinfo.UserTrueName
                dic["Relation"] = u.Relation
            listdata.append(dic)
        return Response(
            response.Content().ok('SUC', listdata).raw()
        )

    # 获取教练关联的测评/报告
    @action(methods=['get'], detail=False)
    def GetEvaluationToCoachUser(self, request):
        ProbjectId = int_arg(request.query_params.get('ProbjectId'), None)
        uid = int_arg(request.query_params.get('uid'), None)
        typeid = int_arg(request.query_params.get('typeid'), 1)
        now_date = datetime.datetime.now().date()
        userid = request.user.pk
        evalist = models.AppProbjectexamrelationuser.objects.filter(EndDate__gte=now_date, UserId=uid)
        if typeid > 0:
            evalist = evalist.filter(TypeId=typeid)
        query = evalist
        if ProbjectId > 0:
            query = query.filter(ProbjectId=ProbjectId)
        query = query.order_by('-Id')
        # 按项目获取被教人
        listdata = []
        for item in query.all():
            _probjectid = item.ProbjectId
            masteruid = models.AppProbjectrelation.objects.filter(CoachId=userid, ProbjectId=_probjectid,
                                                                  UserId=uid)
            for u in masteruid.all():
                dic = {}
                dic["ProbjectId"] = item.ProbjectId
                dic["ExId"] = item.ExId
                dic["ExName"] = item.ExName
                dic["Status"] = item.Status
                dic["CreateDate"] = item.CreateDate
                dic["EndDate"] = item.EndDate
                dic["ReportId"] = item.ReportId
                dic["TypeId"] = item.TypeId
                dic["pici"] = item.pici
                dic["ModeCode"] = get_model_code(value_or_default(item.TypeId, 0), item.ExId)
                dic["UserId"] = u.UserId
                uinfo = models.SysUser.objects.filter(User_Id=u.UserId).first()
                dic["UserTrueName"] = uinfo.UserTrueName
                listdata.append(dic)
        return Response(
            response.Content().ok('SUC', listdata).raw()
        )

    @action(methods=['post'], detail=False)
    def getpagedata(self, request):
        for where in json.loads(request.data["wheres"]):
            if where['name'] == 'UserName':
                ids = get_user_ids(where['value'])
                if len(ids) > 0:
                    add_option(request.data, 'UserId', ids, 'checkbox')
            elif where['name'] == 'ProName':
                ids = models.AppProbject.objects.filter(Enable=1, Name__in=where['value']).values_list('Probject_Id',
                                                                                                       flat=True)
                if len(ids) > 0:
                    add_option(request.data, 'ProbjectId', ids, 'checkbox')
            else:
                add_option(request.data, where['name'], where['value'], where['displayType'])
        t = self.get_page_rows(request.data)
        if t.count() > 0:
            extra = []
            for item in t.all():
                user_info = models.SysUser.objects.filter(User_Id=item.UserId).first()
                dic = {
                    "eId": item.Id,
                    'ProName': get_project_info(value_or_default(item.ProbjectId, 0)).Name,
                    'UserName': user_info.UserTrueName + '(' + user_info.UserName + ')',
                    'UserRole': user_info.RoleName,
                    'interested_username': ''
                }
                if item.interested_id:
                    interest_user = models.SysUser.objects.filter(User_Id=item.interested_id).first()
                    if interest_user:
                        dic['interested_username'] = interest_user.UserTrueName
                extra.append(dic)
            t.extra_list = extra
        return Response(
            self.page_data(t)
        )
