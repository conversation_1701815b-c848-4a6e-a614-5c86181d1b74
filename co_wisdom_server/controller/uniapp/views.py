import datetime

from drf_yasg import openapi
from drf_yasg.utils import swagger_auto_schema
from rest_framework.views import APIView
from rest_framework.response import Response

from data import models
from utils import response

from controller.projectinterview.views import ProjectInterViewSerializer


class UniAppView(APIView):

    @swagger_auto_schema(
        operation_id='微信小程序首页接口',
        operation_summary='微信小程序首页接口',
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'uid': openapi.Schema(type=openapi.TYPE_NUMBER, description='用户ID'),
            }
        ),
        tags=['微信小程序接口']
    )
    def post(self, request):
        try:
            uid = int(request.data.get('uid'))
            user = models.SysUser.objects.get(User_Id=uid)
        except (TypeError, ValueError, models.SysUser.DoesNotExist):
            return Response(response.Content().error('请求参数错误').lower_raw())

        filter_dict = {}
        # 被教练者
        if user.Role_Id == 6:
            filter_dict.setdefault('User_Id', user.User_Id)
            filter_dict.setdefault('Satisfaction', None)
        # 教练
        if user.Role_Id == 4:
            filter_dict.setdefault('Coach_Id', user.User_Id)
            filter_dict.setdefault('Times', None)

        project_interview = models.AppProbjectinterview.objects.filter(**filter_dict, status=1).order_by('StartTime')
        new_data = {'hint_text': '终于等到您啦，教练正在等待您的预约哟~',
                    'action_text': '新建约谈'}
        if not project_interview.exists():
            return Response(response.Content().ok('suc', new_data).raw())
        else:
            # 当天的即将开始的一次约谈
            first_data = project_interview.filter(StartTime__date=datetime.datetime.now().date(),
                                                  EndTime__gt=datetime.datetime.now()).first()
            recently = ProjectInterViewSerializer(first_data).data
            # 包含等待开始和待填写记录

            other_data = project_interview.filter(
                EndTime__lt=datetime.datetime.now().date() + datetime.timedelta(days=1)).exclude(Id=first_data.Id)
            interview = ProjectInterViewSerializer(other_data, many=True).data
            return Response(response.Content().ok({'recently': recently, 'interview': interview}).raw())
