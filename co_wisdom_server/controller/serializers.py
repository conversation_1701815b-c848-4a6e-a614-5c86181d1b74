from rest_framework import serializers
from data.project.models import Project, Membership, Matching
from data.company.models import Company
from data.company.team.models import Team
from data import models
from data.user.models import User
from data.user.coach.models import Coach


class CompanySerializer(serializers.ModelSerializer):
    class Meta:
        model = Company
        fields = "__all__"


class TeamSerializer(serializers.HyperlinkedModelSerializer):
    class Meta:
        model = Team
        fields = "__all__"


class ProjectSerializer(serializers.ModelSerializer):
    class Meta:
        model = Project
        fields = "__all__"


class ArticleSerializer(serializers.ModelSerializer):
    class Meta:
        model = models.Article
        fields = "__all__"


class MemberSerializer(serializers.ModelSerializer):
    class Meta:
        model = Membership
        fields = "__all__"


class MatchingSerializer(serializers.ModelSerializer):
    class Meta:
        model = Matching
        fields = "__all__"



