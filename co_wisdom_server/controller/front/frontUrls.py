from django.urls import path

from . import views
from ..coach import views as coach_views
from ..evaluation import views as evaluation_views
from ..evaluationresult import views as evaluationresult_views
from ..evaluationtempresult import views as evaluationtempresult_views
from ..feedback import views as feedback_views
from ..modelreportresult import views as modelreportresult_views
from ..project import views as project_views
from ..projectinterview import views as projectinterview_views
from ..projectsatisfaction import views as satisfaction_views
from ..survey import views as survey_views
from ..surveyanswer import views as surveyanswer_views
from ..dictionary import views as dictionary_views
from ..user import views as user_views
from ..member import views as member_views
from ..projectinterview import views as projectinterview_views
from ..projectsettings import views as projectsetting_views

getmyevallist = evaluation_views.EvaluationViewSet.as_view({
    'get': 'getmylist'
})

getmyevalreport = evaluation_views.EvaluationViewSet.as_view({
    'get': 'getmyreport'
})

getevalview = evaluation_views.EvaluationViewSet.as_view({
    'get': 'getevalview'
})

evaluationsubmit = evaluationresult_views.EvaluationResultViewSet.as_view({
    'post': 'evaluationsubmit'
})

evaluationtmpsubmit = evaluationtempresult_views.EvaluationTempResultViewSet.as_view({
    'post': 'evaluationsubmit'
})

getsurveyview = survey_views.SurveyViewSet.as_view({
    'get': 'getsurveyview'
})

surveysubmit = surveyanswer_views.SurveyAnswerViewSet.as_view({
    'post': 'surveysubmit'
})

getreportmodel = modelreportresult_views.ModelReportResultViewSet.as_view({
    'get': 'getreportmodel'
})

modelreportsubmit = modelreportresult_views.ModelReportResultViewSet.as_view({
    'post': 'modelreportsubmit'
})

updateCoachInfo = coach_views.AppCoachViewSet.as_view({
    'post': 'update_new'
})

CancelInterview = projectinterview_views.ProjectInterviewViewSet.as_view({
    'post': 'Cancel'
})

GetProbjectMyCoach = projectinterview_views.ProjectInterviewViewSet.as_view({
    'get': 'GetProbjectMyCoach'
})

GetRelationUserByUser = project_views.ProjectViewSet.as_view({
    'get': 'GetRelationUserByUser'
})

ProbjectSatisfaction = satisfaction_views.ProjectSatisfactionViewSet.as_view({
    'post': 'add'
})

GetProbjectSatisfaction = satisfaction_views.ProjectSatisfactionViewSet.as_view({
    'get': 'GetProbjectSatisfaction'
})

feedbacksubmit = feedback_views.FeedbackViewSet.as_view({
    'post': 'submit'
})

GetSearchDictionary = dictionary_views.DictionaryViewSet.as_view({
    'post': 'GetSearchDictionary'
})

modifypwd = user_views.SysUserViewSet.as_view({
    'post': 'modifypwd'
})

personUpdate = member_views.AppMemberViewSet.as_view({
    'post': 'personUpdate'
})

GetVueDictionary = dictionary_views.DictionaryViewSet.as_view({
    'post': 'GetVueDictionary'
})

frontPath = [
    path('getartdetail', views.getartdetail.as_view(), name='getartdetail'),
    path('getartlist', views.getartlist.as_view(), name='getartlist'),
    path('getmyevallist', getmyevallist),
    path('getmyevalreport', getmyevalreport),
    path('getevalview', getevalview),
    path('evaluationsubmit', evaluationsubmit),
    path('evaluationtmpsubmit', evaluationtmpsubmit),
    path('getsurveyview', getsurveyview),
    path('surveysubmit', surveysubmit),
    path('getreportmodel', getreportmodel),
    path('modelreportsubmit', modelreportsubmit),
    path('updateCoachInfo', updateCoachInfo),
    path('CancelInterview', CancelInterview),
    path('GetProbjectMyCoach', GetProbjectMyCoach),
    path('GetRelationUserByUser', GetRelationUserByUser),
    path('ProbjectSatisfaction', ProbjectSatisfaction),
    path('GetProbjectSatisfaction', GetProbjectSatisfaction),
    path('feedbacksubmit', feedbacksubmit),
    path('GetSearchDictionary', GetSearchDictionary),
    path('modifypwd', modifypwd),
    path('personUpdate', personUpdate),
    path('GetVueDictionary', GetVueDictionary),
]
