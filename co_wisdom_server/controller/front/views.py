from django.db.models import Q
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.views import APIView
from rest_framework.viewsets import ViewSet

from controller.project.actions import get_project_list, get_project_info_by_user
from data import models, serializers
from utils import response, int_arg, aesdecrypt, null_or_blank
from ..dictionary.actions import get_dictionary_list_value
from ..member.actions import get_member_info
from ..projectinterview.actions import get_project_interview, get_project_interview_survey
from ..projectsettings.actions import get_project_config_val
from ..serializers import ArticleSerializer


class FrontViewSet(ViewSet):
    queryset = models.AppUseronline.objects.all()
    serializer_class = serializers.AppUseronlineSerializer

    @action(methods=['get'], detail=False)
    def getPersonInfo(self, request):
        uid = int_arg(request.query_params.get('uid'), None)
        if uid > 0:
            res = models.AppMember.objects.filter(User_Id=uid).first()
            if res:
                return Response(
                    response.Content().ok(data=serializers.AppMemberSerializer(res, many=False).data).raw()
                )
        return Response(
            response.Content().error('非法的用户').raw()
        )

    @action(methods=['get'], detail=False)
    def getMessages(self, request):
        uid = int_arg(request.query_params.get('uid'), None)
        message_list = models.AppMessage.objects.filter(accept_userid=uid).exclude(is_read=1)
        if message_list.exists():
            return Response(
                response.Content().ok('SUC', message_list.count()).raw()
            )
        return Response(
            response.Content().ok('SUC', 0).raw()
        )

    @action(methods=['get'], detail=False)
    def getCoachInfo(self, request):
        uid = int_arg(request.query_params.get('uid'), None)
        if uid > 0:
            coach = models.AppCoach.objects.filter(User_Id=uid).first()
            if coach:
                dic = serializers.AppCoachSerializer(coach, many=False).data
                dic["CoachLevelext"] = get_dictionary_list_value("coachlevel", coach.CoachLevel)
                dic["jlzzext"] = get_dictionary_list_value("jlzz", coach.jlzz)
                dic["kcrzext"] = get_dictionary_list_value("kcrz", coach.kcrz)
                dic["cynxext"] = get_dictionary_list_value("cynx", str(coach.cynx))
                dic["xyjbext"] = get_dictionary_list_value("xyjb", coach.xyjb)
                dic["qysxext"] = get_dictionary_list_value("companyattr", coach.qysx)
                dic["jlgzhyext"] = get_dictionary_list_value("jlhy", coach.jlgzhy)
                dic["sclyext"] = get_dictionary_list_value("pxobjective", coach.scly)
                dic["jlhyext"] = get_dictionary_list_value("jlhy", coach.jlhy)
                dic["jlzwext"] = get_dictionary_list_value("jlzw", coach.jlzw)
                return Response(
                    response.Content().ok('', dic).raw()
                )
        return Response(
            response.Content().error('非法的用户').raw()
        )

    @action(methods=['get'], detail=False)
    def getMyDefProbjectInfo(self, request):
        uid = request.user.pk
        res = get_project_info_by_user(uid)
        return Response(
            response.Content().ok('', serializers.AppProbjectSerializer(res, many=False).data).raw()
        )

    @action(methods=['get'], detail=False)
    def getProbjectList(self, request):
        uid = request.user.pk
        companyid = int_arg(request.query_params.get('companyid'), None)
        return get_project_list(uid, companyid)

    @action(methods=['get'], detail=False)
    def getProbjectsetting(self, request):
        key = request.query_params.get('key')
        isjson = int_arg(request.query_params.get('isjson'), 1)
        probjectid = int_arg(request.query_params.get('probjectid'), 0)
        if probjectid == 0:
            uid = request.user.pk
            pinfo = get_project_info_by_user(uid)
            if pinfo.pk:
                probjectid = pinfo.Probject_Id
        res = get_project_config_val(probjectid, key, bool(isjson))
        return Response(
            response.Content().ok('', res).raw()
        )

    @action(methods=['get'], detail=False)
    def getProbjectInterview(self, request):
        status = int_arg(request.query_params.get('status'), 0)
        probjectid = int_arg(request.query_params.get('probjectid'))
        uid = request.user.pk
        if probjectid == 0:
            uid = request.user.pk
            pinfo = get_project_info_by_user(uid)
            if pinfo.pk:
                probjectid = pinfo.Probject_Id
        res = get_project_interview(uid, probjectid, status, 0, uid)
        return Response(
            res.raw()
        )

    @action(methods=['get'], detail=False)
    def GetProbjectInterViewSurvey(self, request):
        probjectid = int_arg(request.query_params.get('ProbjectId'), 0)
        uid = request.user.pk
        if probjectid == 0:
            uid = request.user.pk
            pinfo = get_project_info_by_user(uid)
            if pinfo:
                probjectid = pinfo.Probject_Id
        res = get_project_interview_survey(uid, probjectid)
        return Response(
            res.raw()
        )

    @action(methods=['get'], detail=False)
    def gettips(self, request):
        probjectid = int_arg(request.query_params.get('probjectid'))
        uid = request.user.pk
        uinfo = models.AppMember.objects.filter(User_Id=uid).first()
        tipperson = 1
        if uinfo and uinfo.CompanyWorkingDate and uinfo.WorkingDate:
            tipperson = 0
        mtbjr = int_arg(get_project_config_val(probjectid, 'mtbjr'))
        tipinterview = 0
        relation = models.AppProbjectrelation.objects.filter(ProbjectId=probjectid, UserId=uid).first()
        if relation and relation.CoachId > 0:
            interview_list = models.AppProbjectinterview.objects.filter(ProbjectId=probjectid, User_Id=uid,
                                                                        Coach_Id=relation.CoachId, status=1)
            tipinterview = 1
            if interview_list.count() > 0:
                tipinterview = 0
        data = {
            'person': tipperson,
            'mtbjr': mtbjr,
            'interviews': tipinterview
        }
        return Response(
            response.Content().ok('suc', data).raw()
        )

    @action(methods=['get'], detail=False)
    def autoHxLogin(self, request):
        uid = int_arg(request.query_params.get('uid'))
        user = models.SysUser.objects.filter(User_Id=uid).first()
        if user and not null_or_blank(user.MessageId):
            data = {
                'hxuser': user.MessageId,
                'hxpwd': aesdecrypt(user.MessageIdPwd)
            }
            return Response(
                response.Content().ok('SUC', data).raw()
            )

        return Response(
            response.Content().error('未开通即时通讯IM账号，请与群智项目负责人联系').raw()
        )

    @action(methods=['post'], detail=False)
    def updatePersonInfo(self, request):
        main_data = request.data.get('mainData')
        Member_Id = int_arg(main_data.get('User_Id'), 0)
        minfo = get_member_info(Member_Id)
        if minfo:
            for item in request.get('Extra'):
                # TODO update person info
                pass
            minfo.save()
            return Response(
                response.Content().ok().raw()
            )
        else:
            return Response(
                response.Content().error('非法的用户').raw()
            )


class getartdetail(APIView):
    authentication_classes = []

    def get(self, request):
        param = request.query_params.dict()
        artid = int(param["artid"])
        article = None
        if artid == 0:
            pageCode = param["pagecode"]
            article = models.Article.objects.filter(pageCode=pageCode).first()
        else:
            article = models.Article.objects.filter(id=artid).first()
        if article is None and int(param["hasrow"]) == 1:
            article = models.Article.objects.all().first()
        if article:
            return Response({
                "status": True,
                'message': "OperSuccess",
                'data': ArticleSerializer(article, many=False).data
            })
        else:
            return Response({
                "status": True,
                'message': "OperSuccess",
                'data': None
            })

class getartlist(APIView):
    def get(self, request):
        typeid = request.query_params.get('typeid')
        role = models.SysUser.objects.filter(User_Id=request.user.pk).first().Role_Id
        article_list = models.Article.objects.filter(Q(shareUsers__isnull=True) | Q(shareUsers__contains=role), typeId=typeid, enable=1).order_by('-sort')
        data = []
        for item in article_list.all():
            data.append({
                'id': item.id,
                'pageCode': item.pageCode,
                'title': item.title
            })
        return Response(
            response.Content().ok('suc', data).raw()
        )