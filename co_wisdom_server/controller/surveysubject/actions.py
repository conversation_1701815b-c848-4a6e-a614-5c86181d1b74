from data import models


def get_survey_subject_detail(surveyid):
    subject_list = models.AppSurveysubject.objects.filter(SurveyId=surveyid).order_by('Sort')
    for item in subject_list.all():
        item['App_SurveyOption'] = models.AppSurveyoption.objects.filter(SubId=item.SubId)
    return subject_list


def get_survey_subject(subid):
    subject_list = models.AppSurveysubject.objects.filter(SubId=subid).order_by('Sort')
    if subject_list.exists():
        for item in subject_list.all():
            item['App_SurveyOption'] = models.AppSurveyoption.objects.filter(SubId=item.SubId)
        return subject_list
    return None
