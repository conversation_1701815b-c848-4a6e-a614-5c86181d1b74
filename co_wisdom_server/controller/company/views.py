import json

from rest_framework.decorators import action
from rest_framework.response import Response

from controller.project.actions import get_start_project_num_by_company, get_complete_project_num_by_company, \
    get_sign_project_num_by_company
from data import models, serializers, extension
from utils import response, blank_to_none
from utils.model import add_option


class CompanyViewSet(extension.ResponseViewSet):
    queryset = models.AppCompany.objects.all()
    serializer_class = serializers.AppCompanySerializer

    @action(methods=['post'], detail=False)
    def getpagedata(self, request):
        has_manageid = False
        for where in json.loads(request.data["wheres"]):
            if where['name'] == 'Manage_Id':
                has_manageid = True
                add_option(request.data, where['name'], where['value'], where['displayType'])
            else:
                add_option(request.data, where['name'], where['value'], where['displayType'])
        if not has_manageid and request.user.Role_Id != 1:
            add_option(request.data, 'Manage_Id', request.user.pk, 'int')
        t = self.get_page_rows(request.data)
        if t.count() > 0:
            extra = []
            for company in t.all():
                project_num = get_sign_project_num_by_company(company.Company_Id, 1)
                extra.append({"eCompany_Id": company.Company_Id, "ProbjectNum": project_num})
            t.extra_list = extra
        return Response(
            self.page_data(t)
        )

    @action(methods=['post'], detail=False)
    def add(self, request):
        main_data = request.data.get('mainData')
        if main_data.get('Manage_Id') is None:
            main_data['Manage_Id'] = request.user.pk
        company_name = main_data.get('CompanyName', '')
        if models.AppCompany.objects.filter(CompanyName=company_name).exists():
            return Response(
                response.Content().error('该公司已经存在公司库中了').raw()
            )
        main_data['OrderNo'] = blank_to_none(main_data.get('OrderNo'))
        return Response(
            self.add_row(main_data).raw()
        )

    @action(methods=['get'], detail=False)
    def getentity(self, request):
        res = self.get_entity(request)
        if res.status:
            data = self.serializer_class(res.row, many=False).data
            data["startProbject"] = get_start_project_num_by_company(res.row.Company_Id)
            data["completeProbject"] = get_complete_project_num_by_company(res.row.Company_Id)
            data["noSignProbject"] = get_sign_project_num_by_company(res.row.Company_Id)
            res.data = data
        return Response(
            res.raw()
        )

    @action(methods=['post'], detail=False, url_path='update')
    def update_new(self, request):
        main_data = request.data.get('mainData')
        logo = main_data.get('Logo')
        if logo and type(logo) is list and 'path' in logo[0]:
            main_data['Logo'] = logo[0].get('path')
        res = self.update_row(main_data)
        return Response(
            res.lower_raw()
        )
