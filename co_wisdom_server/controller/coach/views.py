import datetime
import json

from django.db.models import F
from rest_framework.decorators import action
from rest_framework.response import Response

from controller.dictionary.actions import get_dictionary_list_value
from controller.user.actions import add_sys_user
from data import models, serializers, extension
from data.models import AppCoach
from utils import response, int_arg, aesencrypt, randomPassword, null_or_blank
from utils.easemob.common import enable_im
from utils.messagecenter.business import new_user_message
from utils.model import get_option, add_option

from .actions import sync_v2


class AppCoachViewSet(extension.ResponseViewSet):
    queryset = models.AppCoach.objects.all()
    serializer_class = serializers.AppCoachSerializer

    @action(methods=['post'], detail=False)
    def add(self, request):
        main_data = request.data.get('mainData')
        TrueName = main_data.get('TrueName', '')
        mobile = main_data.get('mobile', '')
        password = main_data.get('password', '')
        UserName = main_data.get('UserName', '')
        email = main_data.get('email', '')
        if null_or_blank(TrueName) or null_or_blank(mobile):
            return Response(
                response.Content().error('姓名/手机号/邮箱不能空').raw()
            )
        if null_or_blank(UserName):
            return Response(
                response.Content().error('登录名不能空').raw()
            )
        if not null_or_blank(UserName):
            cfusername = models.SysUser.objects.filter(UserName=UserName).exists()
            if cfusername:
                return Response(
                    response.Content().error('该登陆名已经存在了').raw()
                )
        Manage_Id = int_arg(main_data.get('Manage_Id', ''), 0)
        if Manage_Id == 0:
            main_data['Manage_Id'] = request.user.pk
        User_Id = int_arg(main_data.get('User_Id', ''), 0)
        if User_Id == 0:
            userModel = models.SysUser()
            userModel.Email = email
            userModel.PhoneNo = mobile
            userModel.UserPwd = password
            userModel.UserName = UserName
            userModel.UserTrueName = TrueName
            userModel.Role_Id = 4
            userModel.RoleName = '教练组'
            if null_or_blank(userModel.UserPwd):
                userModel.UserPwd = randomPassword(6)
            userModel.UserPwd = aesencrypt(userModel.UserPwd)
            userModel.CreateDate = datetime.datetime.now().replace(microsecond=0)
            userModel.ModifyDate = datetime.datetime.now().replace(microsecond=0)
            userModel.Creator = request.user.UserName
            userModel.Enable = 1
            isexist = models.SysUser.objects.filter(UserName=userModel.UserName).exists()
            if isexist:
                return Response(
                    response.Content().error('用户名已经被注册').raw()
                )
            userModel.save()
            add_sys_user(userModel)
            main_data['User_Id'] = userModel.User_Id
            if userModel.User_Id > 0:
                enable_im(userModel.User_Id)
        new_user_message(userModel, {'password': password}, 'newcoach')
        main_data['IsOpen'] = 1
        main_data['Sendemail'] = 1
        main_data['Sendmsg'] = 1
        main_data['openday'] = 1
        res = self.add_row(main_data)
        sync_v2(res.row)
        return Response(
            res.raw()
        )

    @action(methods=['post'], detail=False, url_path='update')
    def update_new(self, request):
        main_data = request.data.get('mainData')
        CoachId = int_arg(main_data.get('CoachId'), 0)
        if not CoachId or CoachId == 0:
            return Response(
                response.Content().error('教练数据丢失').raw()
            )
        mobile = main_data.get('mobile', '')
        email = main_data.get('email', '')
        Photograph = main_data.get('Photograph', '')
        if Photograph and type(Photograph) is list and 'path' in Photograph[0]:
            main_data['Photograph'] = Photograph[0].get('path')
        elif not Photograph:
            del main_data['Photograph']
        cerfile = main_data.get('cerfile', '')
        if cerfile and type(cerfile) is list and 'path' in cerfile[0]:
            main_data['cerfile'] = cerfile[0].get('path')
        elif not cerfile:
            del main_data['cerfile']
        email_enable = main_data.get('Sendemail')
        if email_enable == '':
            main_data['Sendemail'] = 0
        sms_enable = main_data.get('Sendmsg')
        if sms_enable == '':
            main_data['Sendmsg'] = 0
        res = self.update_row(main_data)
        if res.status:
            uid = int_arg(main_data.get('User_Id'), 0)
            uifo = models.SysUser.objects.filter(User_Id=uid).first()
            TrueName = main_data.get('TrueName', '')
            if uifo.HeadImageUrl != Photograph or null_or_blank(uifo.HeadImageUrl):
                uifo.HeadImageUrl = Photograph
            if uifo.UserTrueName != TrueName or null_or_blank(uifo.UserTrueName):
                uifo.UserTrueName = TrueName
            if uifo.PhoneNo != mobile or null_or_blank(uifo.PhoneNo):
                uifo.PhoneNo = mobile
            if uifo.Email != email or null_or_blank(uifo.Email):
                uifo.Email = email
            uifo.save()
            # 同步数据到v2
            sync_v2(res.row)
        return Response(
            res.lower_raw()
        )

    @action(methods=['post'], detail=False)
    def getpagedata(self, request):
        for where in json.loads(request.data["wheres"]):
            if where['name'] == 'ProbjectId':
                ids = models.AppProbjectrelation.objects.filter(ProbjectId=where['value']).values_list('CoachId',
                                                                                                       flat=True)
                if len(ids) > 0:
                    add_option(request.data, 'User_Id', ids, 'checkbox')
                else:
                    add_option(request.data, 'User_Id', 0, 'int')
            else:
                add_option(request.data, where['name'], where['value'], where['displayType'])
        t = self.get_page_rows(request.data)
        if t.count() > 0:
            extra = []
            for item in t.all():
                dic = {"eUser_Id": item.User_Id}
                pinfo = models.SysUser.objects.filter(User_Id=item.User_Id).first()
                if pinfo and pinfo.User_Id > 0:
                    dic["lasttime"] = pinfo.LastLoginDate
                extra.append(dic)
            t.extra_list = extra
        return Response(
            self.page_data(t)
        )

    @action(methods=['post'], detail=False)
    def GetPageDataChoose(self, request):
        ProbjectId = get_option(request.data, 'ProbjectId')
        NotCoachId = get_option(request.data, 'NotCoachId')
        for where in json.loads(request.data["wheres"]):
            if where['name'] == 'ProbjectId':
                ids = models.AppProbjectrelation.objects.filter(ProbjectId=where['value']).values_list('CoachId',
                                                                                                       flat=True)
                if len(ids) > 0:
                    add_option(request.data, 'User_Id', ids, 'checkbox')
                else:
                    add_option(request.data, 'User_Id', 0, 'int')
            elif where['name'] == 'NotCoachId':
                ids = models.AppProbjectrelation.objects.filter(ProbjectId=ProbjectId).values_list('CoachId', flat=True)
                if len(ids) > 0:
                    add_option(request.data, 'User_Id', ids, 'NotContains')
            else:
                add_option(request.data, where['name'], where['value'], where['displayType'])
        t = self.get_page_rows(request.data)
        if t.count() > 0:
            extra = []
            for item in t.all():
                dic = {"eUser_Id": item.User_Id, 'CoachLevel': get_dictionary_list_value('coachlevel', item.CoachLevel)}
                pinfo = models.SysUser.objects.filter(User_Id=item.User_Id).first()
                if pinfo and pinfo.User_Id > 0:
                    dic["lasttime"] = pinfo.LastLoginDate
                extra.append(dic)
            t.extra_list = extra
        return Response(
            self.page_data(t)
        )

    @action(methods=['get'], detail=False)
    def getentity(self, request):
        res = self.get_entity(request)
        if res.status:
            cinfo = res.row
            dic = self.serializer_class(res.row, many=False).data
            dic["CoachLevelext"] = get_dictionary_list_value("coachlevel", cinfo.CoachLevel)
            dic["jlzzext"] = get_dictionary_list_value("jlzz", cinfo.jlzz)
            dic["kcrzext"] = get_dictionary_list_value("kcrz", cinfo.kcrz)
            dic["cynxext"] = get_dictionary_list_value("cynx", str(cinfo.cynx))
            dic["xyjbext"] = get_dictionary_list_value("xyjb", cinfo.xyjb)
            dic["qysxext"] = get_dictionary_list_value("companyattr", cinfo.qysx)
            dic["jlgzhyext"] = get_dictionary_list_value("jlhy", cinfo.jlgzhy)
            dic["sclyext"] = get_dictionary_list_value("pxobjective", cinfo.scly)
            dic["jlhyext"] = get_dictionary_list_value("jlhy", cinfo.jlhy)
            dic["jlzwext"] = get_dictionary_list_value("jlzw", cinfo.jlzw)
            res.data = dic
        return Response(
            res.ok().raw()
        )

    @action(methods=['post'], detail=False)
    def suggest(self, request):
        keyword = request.data.get('keyword')
        coach_list = AppCoach.objects
        if keyword:
            coach_list = coach_list.filter(TrueName__contains=keyword)
        data = coach_list.annotate(uid=F('User_Id'), name=F('TrueName')).values('uid', 'name')
        return Response(
            response.Content().ok('suc', data).raw()
        )
