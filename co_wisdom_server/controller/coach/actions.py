from data import models
from wisdom_v2.models import Schedule, <PERSON>Attr, Coach, User, Resume

from django.conf import settings
from utils import int_arg
from bs4 import BeautifulSoup

def get_coach_info(uid):
    coach = models.AppCoach.objects.filter(User_Id=uid).first()
    if not coach:
        return None
    return coach


def sync_v2(coach):
    if coach:
        user = models.SysUser.objects.filter(User_Id=coach.User_Id).first()
        unique_name = coach.TrueName
        v2_coach = Coach.objects.filter(user__true_name=unique_name).first()
        if not v2_coach:
            v2_user = User.objects.create(name=user.UserName, email=user.Email, phone=user.PhoneNo, true_name=coach.TrueName,
                                          password=user.UserPwd)
            v2_coach = Coach.objects.create(user=v2_user)
            Resume.objects.create(coach=v2_coach, is_customization=False)
        if coach.Photograph is not None:
            v2_coach.user.head_image_url = settings.SITE_URL + 'coapi/' + coach.Photograph
        v2_coach.user.age = coach.Age
        v2_coach.user.phone = coach.mobile
        v2_coach.user.email = coach.email
        if coach.Sex:
            v2_coach.user.gender = coach.Sex
        # 从业年限
        v2_coach.working_years = coach.cynx
        # 教练服务时长
        v2_coach.coach_experience = int_arg(coach.jlxssOne, 0)
        # 教练简历
        if coach.overallIntroduction is not None:
            v2_coach.brief = BeautifulSoup(coach.overallIntroduction, 'html.parser').get_text()
        # 工作经历
        if coach.gzjl is not None:
            v2_coach.work_experience = BeautifulSoup(coach.gzjl, 'html.parser').get_text()
        # 擅长领域
        v2_coach.domain = coach.scly
        # 教练风格
        if coach.coachstyle is not None:
            v2_coach.style = BeautifulSoup(coach.coachstyle, 'html.parser').get_text()
        # 教练过的行业
        v2_coach.industry = coach.jlhy
        # 教练资质
        v2_coach.qualification = coach.jlzz
        # 客户证言
        if coach.endorsement is not None:
            v2_coach.customer_evaluate = BeautifulSoup(coach.endorsement, 'html.parser').get_text()
        v2_coach.user.save()
        v2_coach.save()



