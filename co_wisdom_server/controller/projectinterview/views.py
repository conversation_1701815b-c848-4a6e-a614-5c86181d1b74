import datetime
import json

from django.db import transaction
from django.db.models import Sum
from drf_yasg import openapi
from drf_yasg.utils import swagger_auto_schema
from rest_framework import serializers as ser
from rest_framework.decorators import action
from rest_framework.response import Response

from controller.coach.actions import get_coach_info
from controller.member.actions import get_member_info
from controller.project.actions import get_project_info, get_project_info_by_user
from controller.projectinterview.actions import get_project_interview, get_project_my_coach, get_interview_all_nums, \
    get_project_interview_survey, update_times
from data import models, serializers, extension
from utils import response, int_arg, null_or_blank
from utils.color_constant import COLOR_MAP
from utils.messagecenter.center import push_message
from utils.model import add_option
from utils.user import get_user_ids
from .project_interview_constant import TYPE_MAP
from ..coachschedule.actions import schedule_conflict
from controller.coachschedule.actions import get_type_text
from wisdom_v2.models import Schedule, PublicAttr

class ProjectInterViewSerializer(ser.ModelSerializer):
    coach_name = ser.SerializerMethodField()
    interview_type = ser.SerializerMethodField()
    tag = ser.SerializerMethodField()
    StartTime = ser.DateTimeField(format='%Y-%m-%d %H:%M')
    EndTime = ser.DateTimeField(format='%Y-%m-%d %H:%M')

    class Meta:
        model = models.AppProbjectinterview
        fields = ('Id', 'ScheduleId', 'ProbjectId', 'coach_name',
                  'StartTime', 'EndTime', 'status', 'nowInterview', 'interview_type', 'tag')

    def get_coach_name(self, obj):
        try:
            return models.AppCoach.objects.get(CoachId=obj.Coach_Id).TrueName
        except models.AppCoach.DoesNotExist:
            return

    def get_interview_type(self, obj):
        if obj.interviewsubject is not None:
            return TYPE_MAP[obj.interviewsubject]

    def get_tag(self, obj):
        if obj.StartTime > datetime.datetime.now():
            return {'background_color': COLOR_MAP['black'], 'text_color': COLOR_MAP['black'], 'help_text': '等待开始'}
        else:
            return {'background_color': COLOR_MAP['black'], 'text_color': COLOR_MAP['black'], 'help_text': '待填写记录'}


class ProjectInterviewViewSet(extension.ResponseViewSet):
    queryset = models.AppProbjectinterview.objects.all()
    serializer_class = serializers.AppProbjectinterviewSerializer

    @action(methods=['post'], detail=False)
    def getpagedata(self, request):
        for where in json.loads(request.data["wheres"]):
            if where['name'] == 'UserName':
                ids = get_user_ids(where['value'])
                if len(ids) > 0:
                    add_option(request.data, 'User_Id', ids, 'checkbox')
            elif where['name'] == 'CoachName':
                ids = get_user_ids(where['value'])
                if len(ids) > 0:
                    add_option(request.data, 'Coach_Id', ids, 'checkbox')
            else:
                add_option(request.data, where['name'], where['value'], where['displayType'])
        t = self.get_page_rows(request.data)
        if t.count() > 0:
            extra = []
            for item in t.all():
                dic = {}
                dic['eId'] = item.Id
                uinfo = get_member_info(item.User_Id)
                if uinfo:
                    dic["photograph"] = uinfo.Photograph
                    dic["TrueName"] = uinfo.TrueName
                    dic["department"] = uinfo.Department
                    dic["duty"] = uinfo.Duty
                    dic["companyName"] = uinfo.CompanyName
                cinfo = get_coach_info(item.Coach_Id)
                if cinfo:
                    dic["CoachPhotograph"] = cinfo.Photograph
                    dic["CoachTrueName"] = cinfo.TrueName
                interested_id = item.masteruserid
                if interested_id:
                    interested_user = models.SysUser.objects.filter(User_Id=interested_id).first()
                    if interested_user:
                        dic["interested_name"] = interested_user.UserTrueName
                learning_action_list = models.AppProbjectlearningaction.objects.filter(User_Id=item.User_Id,
                                                                                       interviewId=item.Id,
                                                                                       ProbjectId=item.ProbjectId)
                incomplete_action_list = learning_action_list.filter(ActionPlan__isnull=False).exclude(
                    IsCompateActionPlan=1)
                incomplete_count = incomplete_action_list.count()
                action_count = learning_action_list.filter(ActionPlan__isnull=False).count()
                learning_count = learning_action_list.filter(LearningPlan__isnull=False).count()
                dic["actionPlanCount"] = incomplete_count
                dic["actionCount"] = action_count
                dic["learningCount"] = learning_count
                dic['coach_reocrd_status'] = bool(item.Times)
                dic['coachee_record_status'] = bool(item.Satisfaction)
                type_text = get_type_text(item.pk)
                dic['type_text'] = type_text
                extra.append(dic)
            t.extra_list = extra
        return Response(
            self.page_data(t)
        )

    @action(methods=['post'], detail=False)
    def AddEx(self, request):
        main_data = request.query_params
        interviewid = int_arg(main_data.get('interviewid'), None)
        intertype = int_arg(main_data.get('intertype'), None)
        myself = int_arg(main_data.get('myself'), 0)
        info = models.AppProbjectinterview.objects.filter(Id=interviewid).first()
        if info:
            uid = request.user.pk
            if (intertype == 0 and uid != info.User_Id) or (intertype == 1 and uid != info.Coach_Id):
                return Response(
                    response.Content().error('非法的约谈处理')
                )

            if intertype == 0:
                if myself == 1:
                    remark = '被教练者异常取消'
                else:
                    remark = '教练异常取消'
                info.CloseReason = "超过约谈时间30分钟，未能上线"
                info.Remark = remark
            else:
                if myself == 1:
                    info.CloseReason = "教练异常取消"
                    info.CloseType = "教练异常取消"
                else:
                    info.Remark = "被教练者异常取消"

            info.status = 2
            info.interviewStatus = 1
            info.save()
            # 异常取消通知项目负责人、企业负责人
            cinfo = models.SysUser.objects.filter(User_Id=info.Coach_Id).first()
            uinfo = models.SysUser.objects.filter(User_Id=info.User_Id).first()
            proinf = get_project_info(info.ProbjectId)
            if proinf and proinf.Probject_Id > 0:
                dic = {
                    'probjectname': proinf.Name,
                    'interviewTime': info.StartTime.strftime('%Y-%m-%d %H:%M') + ' -' + info.EndTime.strftime(
                        '%H:%M'),
                    'qxtype': myself,
                    'fromname': uinfo.UserTrueName,
                    'toname': cinfo.UserTrueName,
                    'reson': '超时30分钟未能上线而取消'
                }
                minfo = models.SysUser.objects.filter(User_Id=proinf.Relation_UserId).first()
                if minfo:
                    dic["manager"] = minfo.UserTrueName
                    dic["mobile"] = minfo.PhoneNo
                    dic["email"] = minfo.Email
                    push_message.delay(minfo, 'interviewex', dic, proinf.pk)
                relation = models.AppProbjectrelation.objects.filter(ProbjectId=info.ProbjectId,
                                                                     IsManger=1).values_list('UserId', flat=True)
                if len(relation) > 0:
                    for item in relation:
                        uinfox = models.SysUser.objects.filter(User_Id=item).first()
                        if uinfox:
                            push_message.delay(uinfox, 'interviewex', dic, proinf.pk)
            return Response(
                response.Content().ok().raw()
            )
        return Response(
            response.Content().error('不存在的约谈数据').raw()
        )

    @action(methods=['post'], detail=False)
    def ExceptionHand(self, request):
        reason = request.data.get('reason', None)
        id = int_arg(request.data.get('id'), 0)
        info = models.AppProbjectinterview.objects.filter(Id=id).first()
        if info:
            info.Remark = reason
            info.status = 2
            info.save()
            return Response(
                response.Content().ok().raw()
            )
        return Response(
            response.Content().error().raw()
        )

    @action(methods=['post'], detail=False)
    def Cancel(self, request):
        main_data = request.data.get('MainData')
        id = int_arg(main_data.get('id'), None)
        reson = main_data.get('reson', '')
        closetype = main_data.get('closetype', '')
        info = models.AppProbjectinterview.objects.filter(Id=id).first()
        if info:
            close_reason = reson
            if null_or_blank(reson):
                close_reason = closetype
            info.CloseReason = close_reason
            info.status = 2
            info.CloseType = closetype
            info.save()
            # 同步取消教练预约日程
            # schedule = Schedule.objects.filter(type=3, platform=2, schedule_id=info.ScheduleId)
            # PublicAttr.objects.filter(type=2, schedule_public_attr__in=schedule).update(status=6)
            # schedule.update(deleted=1)
            sinfo = models.AppCoachschedule.objects.filter(ScheduleId=info.ScheduleId, CoachId=info.Coach_Id).first()
            if sinfo:
                sinfo.delete()
            # 教练取消约谈，通知被教练者、企业负责人、项目管理员所有人
            if request.user.pk == info.Coach_Id:
                cinfo = models.SysUser.objects.filter(User_Id=info.Coach_Id).first()
                uinfo = models.SysUser.objects.filter(User_Id=info.User_Id).first()
                proinf = get_project_info(info.ProbjectId)
                if proinf and proinf.Probject_Id > 0:
                    dic = {
                        'probjectname': proinf.Name,
                        'reason': info.CloseReason,
                        'interviewTime': info.StartTime.strftime('%Y-%m-%d %H:%M') + ' -' + info.EndTime.strftime(
                            '%H:%M'),
                        'toname': cinfo.UserTrueName,
                    }
                    push_message.delay(uinfo, 'interviewcancel', dic, proinf.pk)
                    dicx = {
                        'probjectname': proinf.Name,
                        'reson': info.CloseReason,
                        'interviewTime': info.StartTime.strftime('%Y-%m-%d %H:%M') + ' -' + info.EndTime.strftime(
                            '%H:%M'),
                        'toname': cinfo.UserTrueName,
                        'fromname': uinfo.UserTrueName,
                        'reason': info.CloseReason,
                        'qxtype': 1
                    }
                    minfo = models.SysUser.objects.filter(User_Id=proinf.Relation_UserId).first()
                    push_message.delay(minfo, 'interviewex', dicx, proinf.pk)
        return Response(
            response.Content().ok().raw()
        )

    @action(methods=['get'], detail=False)
    def GetProbjectInterview(self, request):
        uid = int_arg(request.query_params.get('uid'), 0) # 773
        probjectid = int_arg(request.query_params.get('probjectid'), 0) # 42
        status = int_arg(request.query_params.get('status'), 0) # 1
        coachid = int_arg(request.query_params.get('coachid'), 0)
        if uid == 0:
            uid = request.user.pk
        if probjectid == 0:
            pinfo = get_project_info_by_user(uid)
            if pinfo:
                probjectid = pinfo.Probject_Id
        res = get_project_interview(uid, probjectid, status, coachid, request.user.pk)
        return Response(
            res.raw()
        )

    @action(methods=['get'], detail=False)
    def GetProbjectMyCoach(self, request):
        res = get_project_my_coach(request)
        return Response(
            res.raw()
        )

    @action(methods=['get'], detail=False)
    def GetProbjectInterViewSurvey(self, request):
        uid = int_arg(request.query_params.get('uid'), None)
        probjectid = int_arg(request.query_params.get('probjectid'), None)
        res = get_project_interview_survey(uid, probjectid)
        return Response(
            res.raw()
        )

    @action(methods=['get'], detail=False)
    def GetProbjectInterViewProcces(self, request):
        probjectid = int_arg(request.query_params.get('probjectid'), None)
        query = models.AppProbjectrelation.objects.filter(ProbjectId=probjectid, RolesId=6).values_list('UserId',
                                                                                                        flat=True)
        if len(query) == 0:
            query = [0]
        res_list = []
        members = models.AppMember.objects.filter(User_Id__in=query).values('Photograph', 'User_Id', 'TrueName',
                                                                            'Department', 'Duty')
        for item in members:
            dic = item
            stinvlist = models.AppProbjectinterview.objects.filter(status=1,
                ProbjectId=probjectid, User_Id=item['User_Id']).order_by('-Id')
            interview_list = stinvlist.filter(growupSatisfied__gt=0)
            if stinvlist.exists():
                count = interview_list.count()
                one = stinvlist.first()
                dic["yt"] = str(one.nowInterview) + "/" + str(one.AllInterview)
                dic["times"] = int_arg(stinvlist.aggregate(Sum('Times')).get('Times__sum'), 0) / 60
                if count == 0:
                    dic["growupSatisfied"] = "--"
                    dic["inputSatisfied"] = "--"
                    dic["Satisfaction"] = "--"
                else:
                    dic["growupSatisfied"] = round(
                        interview_list.aggregate(Sum('growupSatisfied'))['growupSatisfied__sum'] / count, 2)
                    dic["inputSatisfied"] = round(interview_list.aggregate(Sum('inputSatisfied'))['inputSatisfied__sum'] / count,
                                                  2)
                    dic["Satisfaction"] = round(interview_list.aggregate(Sum('Satisfaction'))['Satisfaction__sum'] / count, 2)
            else:
                ytcs = get_interview_all_nums(probjectid)[0]
                dic["yt"] = "--/" + str(ytcs)
                dic["times"] = "--"
                dic["growupSatisfied"] = "--"
                dic["inputSatisfied"] = "--"
                dic["Satisfaction"] = "--"
            res_list.append(dic)
        res = response.Content()
        res.data = res_list
        return Response(
            res.ok('SUC').raw()
        )

    @action(methods=['post'], detail=False)
    def UpdateProbjectInterViewType(self, request):
        main_data = request.data.get('MainData')
        interviewId = int_arg(main_data.get('interviewId'), 0)
        interviewtype = int_arg(main_data.get('interviewtype'), 0)
        if interviewId == 0:
            return Response(
                response.Content().error('非法的约谈数据').raw()
            )
        res = models.AppProbjectinterview.objects.filter(pk=interviewId).first()
        if not res:
            return Response(
                response.Content().error('不存在的约谈数据').raw()
            )
        interview = res
        interview.interviewsubject = interviewtype
        interview.save()
        return Response(
            response.Content().ok('SUC').raw()
        )

    @action(methods=['post'], detail=False)
    def UpdateProbjectInterViewShare(self, request):
        main_data = request.data.get('mainData')
        userid = int_arg(main_data.get('userid'), 0)
        probjectid = int_arg(main_data.get('probjectid'), 0)
        coachids = main_data.get('coachids', '')
        interviewids = main_data.get('interviewids', '')
        if userid == 0:
            userid = request.user.pk
        if null_or_blank(interviewids):
            return Response(
                response.Content().error('请选择待分享的约谈数据').raw()
            )
        if null_or_blank(coachids):
            return Response(
                response.Content().error('请选择待分享的教练').raw()
            )
        arrcoachids = coachids.split(',')
        arrinterviewids = interviewids.split(',')
        for arr in arrinterviewids:
            interviewId = int(arr)
            for item in arrcoachids:
                coachid = int(item)
                interview_exist = models.AppProbjectinterview.objects.filter(Id=interviewId, User_Id=userid,
                                                                             Coach_Id=coachid).exists()
                interview_share_exist = models.AppProbjectInterviewShare.objects.filter(Id=interviewId, User_Id=userid,
                                                                                        Coach_Id=coachid).exists()
                if not interview_exist and not interview_share_exist:
                    share = models.AppProbjectInterviewShare()
                    share.User_Id = userid
                    share.InterviewId = interviewId
                    share.Coach_Id = coachid
                    share.ProbjectId = probjectid
                    share.CreateDate = datetime.datetime.now().replace(microsecond=0)
                    share.ModifyDate = datetime.datetime.now().replace(microsecond=0)
                    share.Creator = request.user.UserName
                    share.save()
        return Response(
            response.Content().ok('SUC').raw()
        )

    @action(methods=['post'], detail=False)
    def updateneedrecord(self, request):
        interviewId = int_arg(request.data.get('interviewid'), 0)
        needrecord = int_arg(request.data.get('needrecord'), 0)
        if interviewId == 0:
            return Response(
                response.Content().error('非法的约谈数据').raw()
            )
        res = models.AppProbjectinterview.objects.filter(pk=interviewId).first()
        if not res:
            return Response(
                response.Content().error('不存在的约谈数据').raw()
            )

        res.needrecord = needrecord
        res.save()
        update_times(res)
        return Response(
            response.Content().ok('SUC').raw()
        )

    @swagger_auto_schema(
        operation_id='待填写约谈列表',
        operation_summary='待填写约谈列表',
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'uid': openapi.Schema(type=openapi.TYPE_NUMBER, description='用户id'),
                'pid': openapi.Schema(type=openapi.TYPE_NUMBER, description='项目id'),
            }
        ),
        tags=['api']
    )
    @action(methods=['post'], detail=False, url_path='recordinterview')
    def RecordInterview(self, request):

        try:
            uid = int(request.data.get('uid'))
            pid = int(request.data.get('pid', 0))
            user = models.SysUser.objects.get(User_Id=uid)
        except (TypeError, ValueError, models.SysUser.DoesNotExist):
            return Response(response.Content().error('请求参数错误').raw())

        filter_dict = {}
        # 被教练者
        if user.Role_Id == 6:
            filter_dict.setdefault('User_Id', user.User_Id)
            filter_dict.setdefault('Satisfaction', None)

        # 教练
        if user.Role_Id == 4:
            filter_dict.setdefault('Coach_Id', user.User_Id)
            filter_dict.setdefault('Times', None)

        project_interview = models.AppProbjectinterview.objects.filter(**filter_dict, status=1).order_by('StartTime')
        if pid:
            project_interview = project_interview.filter(ProbjectId=pid)
        serializer = ProjectInterViewSerializer(project_interview, many=True)
        return Response(response.Content().ok(serializer.data).raw())

    @swagger_auto_schema(
        operation_id='修改约谈时间接口',
        operation_summary='修改约谈时间接口',
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'start': openapi.Schema(type=openapi.TYPE_STRING, description='开始时间'),
                'end': openapi.Schema(type=openapi.TYPE_STRING, description='结束时间'),
                'date': openapi.Schema(type=openapi.TYPE_STRING, description='日期'),
                'interview_id': openapi.Schema(type=openapi.TYPE_NUMBER, description='约谈ID'),
            }
        ),
        tags=['api']
    )
    @action(methods=['post'], detail=False, url_path='updateschedule')
    def UpdateScheduleView(self, request):
        try:
            interview_id = int(request.data.get('interview_id'))
            s_time = datetime.datetime.strptime(
                request.data.get('date') + ' ' + request.data.get('start'), '%Y-%m-%d %H:%M')
            e_time = datetime.datetime.strptime(
                request.data.get('date') + ' ' + request.data.get('end'), '%Y-%m-%d %H:%M')
            interview = models.AppProbjectinterview.objects.get(Id=interview_id)
        except (TypeError, ValueError, models.AppProbjectinterview.DoesNotExist):
            return Response(response.Content().error('请求参数错误').raw())

        try:
            # 事务方式保存
            with transaction.atomic():
                # TODO 修改时间需要发送通知
                coach = models.AppCoach.objects.get(CoachId=interview.Coach_Id)
                if schedule_conflict(s_time, e_time, coach.CoachId, interview.User_Id):
                    return response.Content().error('该时间段冲突，请换个时间段').raw()
                interview.StartTime = s_time
                interview.EndTime = e_time
                interview.save()

                coach_schedule = models.AppCoachschedule.objects.get(InterViewId=interview.Id)
                coach_schedule.Starttime = s_time
                coach_schedule.EndTime = e_time
                coach_schedule.save()
                res = {'interview': ProjectInterViewSerializer(interview).data}
                return Response(response.Content().ok(res).raw())
        except Exception as e:
            print(e)
            return Response(response.Content().error('请求参数错误').raw())

    # 获取所有利益相关者和访谈信息
    @action(methods=['post'], detail=False)
    def getinterestedinterview(self, request):
        try:
            pid = int(request.data.get('pid'))
        except Exception as e:
            return Response(
                response.Content().error('请求参数错误').raw()
            )
        interested_list = models.AppProbjectrelation.objects.filter(ProbjectId=pid, RolesId=7)
        data = []
        for item in interested_list.all():
            interested_name = ''
            user_name = ''
            coach_name = ''
            date = '等待预约'
            interview_id = None
            interested_user = models.SysUser.objects.filter(User_Id=item.Interested_Id).first()
            if interested_user:
                interested_name = interested_user.UserTrueName
            user_user = models.SysUser.objects.filter(User_Id=item.UserId).first()
            if user_user:
                user_name = user_user.UserTrueName
            interested_relation = models.AppProbjectrelation.objects.filter(ProbjectId=pid, RolesId=6, UserId=item.Interested_Id).first()
            coachid = None
            if interested_relation:
                coachid = interested_relation.CoachId
            if coachid:
                coach_user = models.SysUser.objects.filter(User_Id=coachid).first()
                if coach_user:
                    coach_name = coach_user.UserTrueName
                interview = models.AppProbjectinterview.objects.filter(ProbjectId=pid, interviewType=1,
                                                                       User_Id=item.UserId,
                                                                       Coach_Id=coachid, status=1,
                                                                       masteruserid=item.Interested_Id).first()
                if interview:
                    date = datetime.datetime.strftime(interview.StartTime, '%Y-%m-%d %H:%M')
                    interview_id = interview.Id

            data.append({
                'interested_name': interested_name,
                'user_name': user_name,
                'coach_name': coach_name,
                'date': date,
                'interview_id': interview_id
            })
        return Response(
            response.Content().ok('', data).raw()
        )

    @action(methods=['get'], detail=False, url_path='getavailabletype')
    def get_available_type(self, request):
        project_id = int(request.query_params.get('pid', 0))
        try:
            project = models.AppProbject.objects.get(pk=project_id)
        except models.AppProbject.DoesNotExist:
            return Response(
                response.Content().error('请求参数错误').raw()
            )
        project_setting = models.AppProbjectsetting.objects.filter(Probject_Id=project.pk)
        available_types = []
        for k, v in TYPE_MAP.items():
            dic = {'type_id': k, 'type_name': v}
            available_types.append(dic)

        # 影子观察
        project_setting.filter(KeyName='qzyzgc', KeyValue=1)
        if not project_setting.filter(KeyName='qzyzgc', KeyValue=1).exists():
            del available_types[6]

        return Response(
            response.Content().ok('SUC', {"available_types": available_types}).raw()
        )

    @action(methods=['post'], detail=False, url_path='update_interview_type')
    def update_interview_type(self, request):
        try:
            interview_id = int(request.data.get('interview_id', 0))
            type_id = request.data.get('type_id', None)
        except (TypeError, ValueError):
            return Response(
                response.Content().error('请求参数错误').raw()
            )

        project_interview = models.AppProbjectinterview.objects.get(pk=interview_id)
        if not project_interview.interviewType == 0:
            return Response(
                response.Content().error('该约谈不是正式约谈，不能修改约谈类型').raw()
            )
        project_interview.interviewsubject = type_id
        project_interview.save()
        return Response(response.Content().ok().raw())
