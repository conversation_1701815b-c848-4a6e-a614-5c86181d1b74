import datetime

from django.db.models import Sum, Q
from rest_framework.response import Response

from controller.coach.actions import get_coach_info
from controller.dictionary.actions import get_dictionary_list_value
from controller.hxmeetingdata.actions import get_hxmeeting_data
from controller.project.actions import get_project_info
from controller.projectsettings.actions import get_project_config_val
from data import models
from utils import null_or_blank, response, int_arg
from utils.easemob.common import get_im_id
from controller.coachschedule.actions import get_type_text

def get_subject_info(pid):
    interview_list = models.AppProbjectinterview.objects.filter(ProbjectId=pid, status=1, Times__gt=0)
    subject_dic = {}
    # subject = ['常规约谈', '目标约谈', '三方约谈', '阶段回顾', '总结约谈', '一对多约谈', '影子观察']
    subject_list = ['一对一约谈', '三方约谈', '影子观察', '利益相关者访谈']
    total_times = 0
    for item in interview_list.all():
        subject_index = 0
        if item.interviewsubject in [0, 1, 3, 4, 5]:
            subject_index = 0
        elif item.interviewsubject == 2:
            subject_index = 1
        elif item.interviewsubject == 6:
            subject_index = 2
        # stakeholder interview
        if item.interviewType == 1:
            subject_index = 3

        if subject_index < len(subject_list):
            subject = subject_list[subject_index]
            if subject in subject_dic.keys():
                subject_dic[str(subject)] = subject_dic[subject] + round(item.Times / 60, 2)
            else:
                subject_dic[str(subject)] = round(item.Times / 60, 2)
            total_times += round(item.Times / 60, 2)
    if total_times == 0:
        return ''
    if total_times.is_integer():
        total_times = int(total_times)
    ret = str(total_times) + ' 小时 ('
    for k, v in subject_dic.items():
        count = v
        if v.is_integer():
            count = int(v)
        ret += k + ': ' + str(count) + ' 小时，'
    if len(ret) > 0:
        ret = ret[:-1]
    ret += ')'
    return ret


def update_times(pinfo):
    if pinfo:
        interview_times = pinfo.EndTime - pinfo.StartTime
        pinfo.Times = int(interview_times.total_seconds() / 60)
        pinfo.save()


def get_project_status(start_time, end_time):
    now = datetime.datetime.now()
    if start_time <= now and now <= end_time:
        return 0  # 进行中
    elif start_time > now and now <= end_time:
        return 2  # 即将开始
    return 1  # 已完成


def get_hxdata(interviewid):
    model = get_hxmeeting_data('0_' + str(interviewid))
    if model:
        return model.HxData
    return None


def get_project_ildp(uid, probjectid):
    flag = models.AppIldp.objects.filter(ProbjectId=probjectid, UserId=uid).exists()
    if flag:
        return 1
    return 0


def get_project_learning_or_action(uid, probjectid, interviewid, type=0):
    flag = None
    if type == 1:
        flag = models.AppProbjectlearningaction.objects.filter(User_Id=uid, interviewId=interviewid,
                                                               ProbjectId=probjectid, ResId=None).exists()
    else:
        flag = models.AppProbjectlearningaction.objects.filter(User_Id=uid, interviewId=interviewid,
                                                               ProbjectId=probjectid, ResId__gt=0).exists()
    if flag:
        return 1
    return 0


def get_interview_nums(userId, probjectid):
    query = models.AppProbjectinterview.objects.filter(ProbjectId=probjectid, interviewType=0, status=1,
                                                       Times__isnull=False)
    # if status > 0:
    #     query = query.filter(status=status)
    if userId > 0:
        query = query.filter(User_Id=userId)
    return query.count()


def get_next_interview_index(userid, projectid):
    query = models.AppProbjectinterview.objects.filter(ProbjectId=projectid, status=1, User_Id=userid,
                                                       interviewType=0).order_by(
        'nowInterview')
    index = 1
    for item in query.all():
        if item.nowInterview == index:
            index += 1
        else:
            break
    return index


def get_interview_all_nums(probjectid, more=False):
    oytcs = get_project_config_val(probjectid, 'ytcs')
    pytcs = 0
    if oytcs:
        pytcs = int(oytcs)
    oytsfht = get_project_config_val(probjectid, 'ytsfht')
    pytsfht = 0
    if oytsfht:
        pytsfht = int(oytsfht)
    res_list = [pytcs + pytsfht]
    if more:
        res_list.append(pytcs)
        res_list.append(pytsfht)
    return res_list


def get_interview_times(userId, probjectid):
    query = models.AppProbjectinterview.objects.filter(ProbjectId=probjectid, interviewType=0, status=1,
                                                       Times__isnull=False)
    if userId > 0:
        query = query.filter(User_Id=userId, status=1)
    allminuties = 0
    for item in query.all():
        delta = item.EndTime - item.StartTime
        allminuties += delta.total_seconds() / 60
    times = round(allminuties / 60, 2)
    if times.is_integer():
        times = int(times)
    return times


def get_student_times(project_id):
    """ 获取项目每人约谈时长 """
    try:
        total_times = models.AppProbjectsetting.objects.get(Probject_Id=project_id, KeyName='mrytsc').KeyValue

        if isinstance(eval(total_times), (int, float)):
            total_times = eval(total_times)
        else:
            total_times = 0

    except models.AppProbjectsetting.DoesNotExist:
        total_times = 0
    except Exception as e:
        total_times = 0
    return total_times


def serializer_interview(data_set):
    res_list = []
    dic = {}
    for data in data_set.values('Id', 'Coach_Id', 'Title', 'status', 'StartTime', 'EndTime', 'Times', 'nowInterview',
                                'pici', 'AllInterview', 'Satisfaction',
                                'growupSatisfied', 'inputSatisfied', 'User_Id', 'ProbjectId', 'interviewStatus',
                                'Remark', 'interviewsubject', 'needrecord'):
        dic = data
        dic['StartTime'] = dic['StartTime'].strftime('%Y-%m-%d %H:%M:%S')
        dic['EndTime'] = dic['EndTime'].strftime('%Y-%m-%d %H:%M:%S')
        dic['ildp'] = get_project_ildp(data.get('User_Id'), data.get('ProbjectId'))
        hxdata = get_hxdata(data.get('Id'))
        if hxdata:
            dic["hxdata"] = hxdata
        dic['islearning'] = get_project_learning_or_action(data.get('User_Id'), data.get('ProbjectId'), data.get('Id'),
                                                           0)
        dic['isaction'] = get_project_learning_or_action(data.get('User_Id'), data.get('ProbjectId'), data.get('Id'), 1)
        type_text = get_type_text(data.get('Id'))
        dic['type_text'] = type_text
        res_list.append(dic)
    return res_list


def get_project_interview(uid, probjectid, status, coachid, current_uid):
    interview_list = models.AppProbjectinterview.objects.filter(status=1, User_Id=uid, ProbjectId=probjectid, interviewType=0)
    now = datetime.datetime.now()
    if status > 0:
        # interview which record has been input
        # plus, endtime passed and does not need record
        interview_list = interview_list.filter(Satisfaction__gt=0, Times__gt=0) | interview_list.filter(
            EndTime__lte=now, needrecord=0)
    else:
        interview_list = interview_list.filter(Q(Satisfaction=None) | Q(Times=None)).exclude(EndTime__lte=now,
                                                                                             needrecord=0)
    if coachid > 0:
        interview_list = interview_list.filter(Coach_Id=coachid)
    # 教练关联其他报告
    if models.SysUser.objects.filter(User_Id=current_uid).first().Role_Id == 4:
        coachid = current_uid
        interviewids = models.AppProbjectInterviewShare.objects.filter(ProbjectId=probjectid, User_Id=uid,
                                                                       Coach_Id=coachid).values_list('InterviewId',
                                                                                                     flat=True)
        if interviewids and len(interviewids) > 0:
            interview_list = interview_list.filter(Q(Coach_Id=coachid) | Q(Id__in=interviewids))
    query = interview_list.order_by('nowInterview')
    diccoach = {}
    pinfox = get_project_info(probjectid)
    yytimes = get_interview_times(uid, probjectid)
    allyytimes = get_interview_times(0, probjectid)
    omrytsc = int_arg(get_project_config_val(probjectid, 'mrytsc'), 0)
    canusetimes = pinfox.Times
    hh = allyytimes
    if omrytsc > 0:
        canusetimes = omrytsc
        hh = yytimes
    caninterview = canusetimes - hh
    if coachid:
        cinfo = get_coach_info(coachid)
        if cinfo and cinfo.CoachId > 0:
            diccoach["ProbjectId"] = probjectid
            diccoach["CoachId"] = cinfo.User_Id
            diccoach["CoachPhotograph"] = cinfo.Photograph
            diccoach["CoachTrueName"] = cinfo.TrueName
            diccoach["CoachEnName"] = cinfo.englistname
            diccoach["CoachLevel"] = get_dictionary_list_value("coachlevel", cinfo.CoachLevel)
            diccoach["CoachMessageid"] = get_im_id(int_arg(cinfo.User_Id, 0))
            diccoach["caninterview"] = caninterview
    else:
        upr = models.AppProbjectrelation.objects.filter(UserId=uid, ProbjectId=probjectid).first()
        if upr:
            cinfo = get_coach_info(upr.CoachId)
            if cinfo and cinfo.CoachId > 0:
                diccoach["ProbjectId"] = probjectid
                diccoach["CoachId"] = cinfo.User_Id
                diccoach["CoachPhotograph"] = cinfo.Photograph
                diccoach["CoachTrueName"] = cinfo.TrueName
                diccoach["CoachEnName"] = cinfo.englistname
                diccoach["CoachLevel"] = get_dictionary_list_value("coachlevel", cinfo.CoachLevel)
                diccoach["CoachMessageid"] = get_im_id(int_arg(cinfo.User_Id, 0))
                diccoach["caninterview"] = caninterview
    res = response.Content()
    res.data = {
        'coach': diccoach,
        'interview': serializer_interview(query)
    }
    return res.ok()


def get_project_my_coach(request):
    uid = int_arg(request.query_params.get('uid'), None)
    probjectid = int_arg(request.query_params.get('probjectid'), None)
    dic = {}
    newcoach = models.AppProbjectrelation.objects.filter(UserId=uid, ProbjectId=probjectid).first()
    coachid = 0
    if newcoach and newcoach.CoachId > 0:
        pinfo = get_project_info(probjectid)
        coachid = newcoach.CoachId
        comodel = get_coach_info(newcoach.CoachId)
        dic["ProbjectId"] = probjectid
        if pinfo:
            dic["Status"] = pinfo.Status
            dic["ProbjectStatus"] = get_project_status(pinfo.Starttime, pinfo.Endtime)
        dic["Coach_Id"] = newcoach.CoachId
        dic["img"] = comodel.Photograph
        dic["level"] = get_dictionary_list_value("coachlevel", comodel.CoachLevel)
        dic["truename"] = comodel.TrueName
        dic["jlzz"] = comodel.jlzz
        dic["jlzw"] = comodel.jlzw
    oldlist = []
    coachids = set(models.AppProbjectinterview.objects.filter(ProbjectId=probjectid, status=1, User_Id=uid, interviewType=0) \
                   .exclude(Coach_Id=coachid).values_list('Coach_Id', flat=True))
    olist = models.AppCoach.objects.filter(CoachId__in=coachids)
    if olist.count() > 0:
        for item in olist.all():
            odic = {}
            odic["ProbjectId"] = probjectid
            odic["Coach_Id"] = item.CoachId
            odic["img"] = item.Photograph
            odic["level"] = get_dictionary_list_value("coachlevel", item.CoachLevel)
            odic["truename"] = item.TrueName
            odic["jlzz"] = item.jlzz
            odic["jlzw"] = item.jlzw
            oldlist.append(odic)
    res = response.Content()
    res.data = {
        'current': dic,
        'old': oldlist
    }
    return Response(
        res.ok('SUC').raw()
    )


def get_ability_title(key):
    if null_or_blank(key) or '-' not in key:
        return '其他'
    arr = key.split('-')
    dicabi = ''
    ability = models.SysDictionaryList.objects.filter(dictionary__dic_no='ability', enable=1).filter(
        dic_value__iexact=arr[1]).first()
    if ability:
        dicabi = ability.dic_name
    dicbeh = ''
    behaviour = models.SysDictionaryList.objects.filter(dictionary__dic_no='behaviour', enable=1).filter(
        dic_value__iexact=arr[1]).first()
    if behaviour:
        dicbeh = behaviour.dic_name
    pre = '领导行为'
    if arr[0] == 'a':
        pre = '领导力'
    hz = dicbeh
    if arr[0] == 'a':
        hz = dicabi
    return pre + '-' + hz


def get_project_interview_survey(uid, probjectid):
    objlist = models.AppProbjectobjective.objects.filter(UserId=uid, ProbjectId=probjectid)
    selobj = objlist.order_by('Id')
    ytcs = get_interview_nums(uid, probjectid)
    pyt = get_interview_all_nums(probjectid)
    sellist = models.AppProbjectupability.objects.filter(User_Id=uid, ProbjectId=probjectid).values(
        'UpAbility').annotate(
        total_times=Sum('Times'))
    listdic = []
    for item in sellist.all():
        listdic.append({
            'key': get_ability_title(item.get('UpAbility')),
            'sum': round(item.get('total_times', 0) / 60, 1)
        })
    res = response.Content()
    res.data = {
        'objectives': selobj,
        'yt': str(ytcs) + '/' + str(pyt[0]),
        'ability': listdic

    }
    return res.ok('SUC')
