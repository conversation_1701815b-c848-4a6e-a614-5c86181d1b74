import json

from rest_framework.decorators import action
from rest_framework.response import Response

from controller.CacheTmpResult.actions import del_key
from data import models, serializers, extension
from utils import response, null_or_blank


class CacheTmpResultViewSet(extension.ResponseViewSet):
    queryset = models.AppCacheTmpResult.objects.all()
    serializer_class = serializers.AppCacheTmpResultSerializer

    @action(methods=['get'], detail=False)
    def GetKeyVal(self, request):
        keyname = request.query_params.get('keyname', '')
        ret = ''
        res = models.AppCacheTmpResult.objects.filter(KeyName=keyname).first()
        if res:
            ret = res.KeyVal
        return Response(
            response.Content().ok('SUC', ret).raw()
        )

    @action(methods=['post'], detail=False)
    def DelKey(self, request):
        keyname = request.query_params.get('keyname', '')
        del_key(keyname)
        return Response(
            response.Content().ok('SUC').raw()
        )

    @action(methods=['post'], detail=False)
    def modelsubmit(self, request):
        main_data = request.data.get('mainData')
        keyname = main_data.get('KeyName', '')
        if null_or_blank(keyname):
            return Response(
                response.Content().error('保存数据失败，无法识别格式').raw()
            )
        res = models.AppCacheTmpResult.objects.filter(KeyName=keyname).first()
        if res:
            jsonval = json.dumps(main_data.get('KeyVal'), ensure_ascii=False, default=str)
            res.KeyVal = jsonval
            res.save()
            return Response(
                response.Content().ok('SUC').raw()
            )
        else:
            jsonval = json.dumps(main_data.get('KeyVal'), ensure_ascii=False, default=str)
            main_data['KeyVal'] = jsonval
            res = self.add_row(main_data)
            return Response(
                res.raw()
            )
