from django.db.models import Q

from data import models
from controller.projectinterview.project_interview_constant import TYPE_MAP, SCHEDULE_TYPE_MAP


def schedule_conflict(start, end, coachid, userid=None, excludeid=0):
    filter_dict = {'CoachId': coachid}
    if userid:
        filter_dict['UserId'] = userid
    schedule_list = models.AppCoachschedule.objects.filter(
        (Q(EndTime__gt=start) & Q(Starttime__lte=start)) | (
                Q(EndTime__gte=end) & Q(Starttime__lt=end)) | (
                Q(EndTime__lte=end) & Q(Starttime__gte=start)) | (
                Q(EndTime__gte=end) & Q(Starttime__lte=start)), **filter_dict)

    if excludeid > 0:
        schedule_list = schedule_list.exclude(ScheduleId=excludeid)
    return schedule_list.count() > 0


def get_type_text(interview_id):
    """
        interviewsubject
            0: '常规约谈',
            1: '目标约谈',
            2: '三方约谈',
            3: '阶段回顾',
            4: '总结约谈',
            5: '一对多约谈',
            6: '影子观察',
    """
    try:
        interview = models.AppProbjectinterview.objects.get(pk=interview_id)
    except models.AppProbjectinterview.DoesNotExist:
        return None
    if interview.interviewType == 0:
        if interview.interviewsubject is not None:
            type_text = TYPE_MAP[interview.interviewsubject]
            return type_text


def get_schedule_type_text(type_id):
    """
        schedule_type
            0：正式约谈
            1：企业面试
            2：化学面试
            3：公开课
            4: 企业服务
            5: 其他活动
            6: 忙碌，教练其他日程
            7: 利益相关者约谈
    """
    if type_id <= 7:
        return SCHEDULE_TYPE_MAP[type_id]
    else:
        return "其他"

