import datetime
import datetime
import json

from django.conf import settings
from django.db.models import Q
from rest_framework.decorators import action
from rest_framework.response import Response

from controller.coach.actions import get_coach_info
from controller.coachschedule.actions import schedule_conflict
from controller.company.actions import get_company_info
from controller.member.actions import get_member_info
from controller.project.actions import get_project_info, get_project_def_coach_by_user, get_project_info_by_user, \
    get_project_company_manager
from controller.projectinterview.actions import get_interview_times, get_next_interview_index
from controller.projectsettings.actions import get_project_config_val
from data import models, serializers, extension
from data.models import AppScheduleAttendee, SysUser
from utils import response, int_arg
from utils.easemob.common import enable_im_and_add_friend
from utils.messagecenter.center import push_message
from utils.model import add_option
from utils.user import get_user_ids
from .actions import get_type_text, get_schedule_type_text
from utils.task import get_str_name
from wisdom_v2.models import Schedule, PublicAttr, User

from wisdom_v2.views.constant import SCHEDULE_TYPE_INTERVIEW, SCHEDULE_PLATFORM_V1


class CoachScheduleViewSet(extension.ResponseViewSet):
    queryset = models.AppCoachschedule.objects.all()
    serializer_class = serializers.AppCoachscheduleSerializer

    @action(methods=['post'], detail=False)
    def getpagedata(self, request):
        for where in json.loads(request.data["wheres"]):
            if where['name'] == 'UserName':
                ids = get_user_ids(where['value'])
                if len(ids) > 0:
                    add_option(request.data, 'UserId', ids, 'checkbox')
            elif where['name'] == 'CoachId':
                CoachId = int_arg(where['value'])
                if CoachId > 0:
                    add_option(request.data, 'CoachId', CoachId, 'int')
            else:
                add_option(request.data, where['name'], where['value'], where['displayType'])
        t = self.get_page_rows(request.data)
        if t.count() > 0:
            extra = []
            for item in t.all():
                dic = {}
                dic["eId"] = item.ScheduleId
                uinfo = get_member_info(int_arg(item.UserId, 0))
                if uinfo:
                    dic["Photograph"] = uinfo.Photograph
                    dic["TrueName"] = uinfo.TrueName
                    dic["Department"] = uinfo.Department
                    dic["Duty"] = uinfo.Duty
                    dic["CompanyName"] = uinfo.CompanyName
                cinfo = get_coach_info(item.CoachId)
                if cinfo:
                    dic["CoachPhotograph"] = cinfo.Photograph
                    dic["CoachTrueName"] = cinfo.TrueName
                extra.append(dic)
            t.extra_list = extra
        return Response(
            self.page_data(t)
        )

    @action(methods=['post'], detail=False)
    def add(self, request):
        main_data = request.data.get('mainData')
        coachid = int_arg(main_data.get('CoachId'), 0)
        starttime = datetime.datetime.strptime(main_data.get('Starttime'), '%Y-%m-%d %H:%M')
        endtime = datetime.datetime.strptime(main_data.get('EndTime'), '%Y-%m-%d %H:%M')

        attendee_list = main_data.get('attendee')
        if attendee_list:
            for item in attendee_list:
                if schedule_conflict(starttime, endtime, item):
                    return Response(
                        response.Content().error('设置日程时间段重叠了').raw()
                    )
        else:
            if schedule_conflict(starttime, endtime, coachid):
                return Response(
                    response.Content().error('设置日程时间段重叠了').raw()
                )

        res = self.add_row(main_data)
        # if coachid:
        #     user = User.objects.filter(name=models.SysUser.objects.get(pk=coachid).UserName).first()
        #     user_id = user.pk if user else None
        #     public_attr = PublicAttr.objects.create(start_time=starttime, end_time=endtime, type=2,
        #                                             user_id=user_id)
        #     Schedule.objects.create(title='教练日程', type=3, platform=2, public_attr=public_attr,
        #                             schedule_id=res.row.pk)
        if res.status:

            # create attendee data
            if attendee_list:
                for item in attendee_list:
                    # public_attr = PublicAttr.objects.create(start_time=starttime, end_time=endtime, type=2,
                    #                                         user_id=item)
                    # Schedule.objects.create(title='忙碌', type=3, platform=2, public_attr=public_attr,
                    #                         schedule_id=res.row.pk, remark=main_data.get('remark'))
                    attendee = AppScheduleAttendee()
                    attendee.schedule = res.row
                    attendee.attendee = item
                    attendee.save()

        return Response(
            res.ok().raw()
        )

    @action(methods=['post'], detail=False, url_path='update')
    def update_new(self, request):
        main_data = request.data.get('mainData')
        scheduleid = int_arg(main_data.get('ScheduleId'), None)
        coachid = int_arg(main_data.get('CoachId'), None)
        starttime = datetime.datetime.strptime(main_data.get('Starttime'), '%Y-%m-%d %H:%M')
        endtime = datetime.datetime.strptime(main_data.get('EndTime'), '%Y-%m-%d %H:%M')

        if schedule_conflict(starttime, endtime, coachid, excludeid=scheduleid):
            return Response(
                response.Content().error('设置日程时间段重叠了').raw()
            )

        attendee_list = main_data.get('attendee')
        if attendee_list:
            for item in attendee_list:
                if schedule_conflict(starttime, endtime, item, excludeid=scheduleid):
                    return Response(
                        response.Content().error('设置日程时间段重叠了').raw()
                    )

        res = self.update_row(main_data)
        if res.status:
            # update attendee data
            # schedule = Schedule.objects.filter(type=3, platform=2, schedule_id=scheduleid)
            # if schedule:
            #     schedule.first().public_attr.start_time = starttime
            #     schedule.first().public_attr.end_time = endtime
            #     schedule.first().public_attr.save()
            # PublicAttr.objects.filter(type=2, schedule_public_attr__in=schedule).update(status=6)
            # schedule.update(deleted=1)
            AppScheduleAttendee.objects.filter(schedule=res.row).all().delete()
            if attendee_list:
                for item in attendee_list:
                    attendee = AppScheduleAttendee()
                    attendee.schedule = res.row
                    attendee.attendee = item
                    attendee.save()

        return Response(
            res.ok().lower_raw()
        )

    @action(methods=['post'], detail=False, url_path='del')
    def Del(self, request):
        keys = request.data
        uid = request.user.pk
        roleid = models.SysUser.objects.filter(User_Id=uid).first().Role_Id
        if roleid == 4:
            exist = models.AppCoachschedule.objects.filter(ScheduleId__in=keys).exclude(CoachId=uid).exists()
            if exist:
                return Response(
                    response.Content().error('非本人的日程不能删除').raw()
                )
        elif roleid != 1:
            return Response(
                response.Content().error('非教练本人的日程不能删除').raw()
            )
        res = self.delete_row(keys)
        # schedule = Schedule.objects.filter(type=3, platform=2, schedule_id__in=keys)
        # PublicAttr.objects.filter(type=2, schedule_public_attr__in=schedule).update(status=6)
        # schedule.update(deleted=1)
        return Response(
            res.lower_raw()
        )

    @action(methods=['get'], detail=False)
    def GetCoachScheduleList(self, request):
        coachid = int_arg(request.query_params.get('coachid'), None)
        now = datetime.datetime.now().replace(microsecond=0) - datetime.timedelta(days=180)
        schedule = models.AppCoachschedule.objects.filter(Q(appscheduleattendee__attendee=coachid) | Q(CoachId=coachid),
                                                          Enable=1, EndTime__gt=now)

        res_list = []
        for item in schedule:
            truename = None
            img = None
            title = item.Title
            minfo = get_member_info(item.UserId)
            if minfo:
                truename = minfo.TrueName
                img = minfo.Photograph
                title += '\n' + minfo.TrueName + ' 已预约'
            attendee_list = []
            attendee_res_list = AppScheduleAttendee.objects.filter(schedule=item)
            if attendee_res_list.exists():
                for scheduleAttendee in attendee_res_list.all():
                    uid = scheduleAttendee.attendee
                    username = ''
                    user = SysUser.objects.filter(User_Id=uid).first()
                    if user:
                        username = user.UserTrueName
                    attendee_list.append({
                        'name': username,
                        'uid': uid
                    })
            u = models.SysUser.objects.filter(User_Id=item.UserId).first()
            messageid = None
            if u:
                messageid = u.MessageId
            dic = {
                'ScheduleId': item.ScheduleId,
                'UserId': item.UserId,
                'UserName': truename,
                'UserImg': img,
                'messageid': messageid,
                'CoachId': item.CoachId,
                'Title': title,
                'Starttime': item.Starttime,
                'EndTime': item.EndTime,
                'InterViewId': item.InterViewId,
                'typeid': item.TypeId,
                'remark': item.remark,
                'attendee': attendee_list,
                'public': item.public,
            }
            type_text = get_type_text(item.InterViewId)

            dic['type_text'] = type_text
            dic['schedule_type_text'] = get_schedule_type_text(item.TypeId)

            res_list.append(dic)
        return Response(
            response.Content().ok('SUC', res_list).raw()
        )

    @action(methods=['get'], detail=False)
    def GetStudentScheduleList(self, request):
        coachid = int_arg(request.query_params.get('coachid'), None)
        uid = int_arg(request.query_params.get('uid'), None)
        if coachid == 0:
            coachid = get_project_def_coach_by_user(uid)

        now = datetime.datetime.now().replace(microsecond=0)
        schedule = models.AppCoachschedule.objects.filter(Q(appscheduleattendee__attendee=coachid) | Q(CoachId=coachid),
                                                          Enable=1, EndTime__gt=now)

        res_list = []
        for item in schedule:
            truename = None
            img = None
            title = item.Title
            if item.UserId == uid:
                minfo = get_member_info(item.UserId)
                if minfo:
                    truename = minfo.TrueName
                    img = minfo.Photograph
                    title += '\n' + minfo.TrueName + ' 已预约'
            else:
                title = '忙碌'
            dic = {
                'ScheduleId': item.ScheduleId,
                'UserId': item.UserId,
                'UserName': truename,
                'UserImg': img,
                'CoachId': item.CoachId,
                'Title': title,
                'Starttime': item.Starttime,
                'EndTime': item.EndTime,
                'InterViewId': item.InterViewId,
                'typeid': item.TypeId,
            }
            type_text = get_type_text(item.InterViewId)
            dic['type_text'] = type_text
            dic['schedule_type_text'] = get_schedule_type_text(item.TypeId)

            res_list.append(dic)
        return Response(
            response.Content().ok('SUC', res_list).raw()
        )

    @action(methods=['post'], detail=False)
    def CancelStudentSchedule(self, request):
        main_data = request.data.get('mainData')
        uid = int_arg(main_data.get('UserId'), None)
        ScheduleId = int_arg(main_data.get('ScheduleId'), None)
        CoachId = int_arg(main_data.get('CoachId'), None)
        qxtype = int_arg(main_data.get('qxtype'), None)
        hxmt = int_arg(main_data.get('hxmt'), 0)
        hxmtid = int_arg(main_data.get('hxmtid'), 0)
        interviewid = int_arg(main_data.get('interviewid'), 0)
        reson = main_data.get('reson', '超过24小时的预约用户自行取消')

        sinfo = models.AppCoachschedule.objects.filter(ScheduleId=ScheduleId, CoachId=CoachId).first()
        role = models.SysUser.objects.filter(User_Id=request.user.pk).first().Role_Id
        if sinfo:
            if sinfo.UserId == uid and uid == request.user.pk or role == 3:
                if sinfo.TypeId == 2 or hxmt == 1:
                    # 化学面谈逻辑
                    rin = models.ProjectCoachInterview.objects.filter(Id=sinfo.InterViewId).first()
                    if rin:
                        rin.StudentinterviewId = 0
                        rin.StudentinterviewTime = None
                        rin.save()
                        # 预约取消，通知被教人/教练
                        cinfo = models.SysUser.objects.filter(User_Id=CoachId).first()
                        uinfo = models.SysUser.objects.filter(User_Id=uid).first()
                        proinf = get_project_info(rin.ProbjectId)
                        if proinf and proinf.Probject_Id > 0:
                            dic = {
                                'probjectname': proinf.Name,
                                'interviewtime': sinfo.Starttime.strftime(
                                    '%Y-%m-%d %H:%M') + '-' + sinfo.EndTime.strftime('%H:%M'),
                            }
                            if qxtype == 0:
                                dic['toname'] = uinfo.UserTrueName
                                push_message.delay(cinfo, 'hxinterviewcancel', dic, proinf.pk)
                            else:
                                dic['toname'] = cinfo.UserTrueName
                                push_message.delay(uinfo, 'hxinterviewcancel', dic, proinf.pk)
                            canceler = '教练'
                            if qxtype == 0:
                                canceler = '被教练者'
                            dicx = {
                                'probjectname': proinf.Name,
                                'reson': '更改约谈时间',
                                'interviewtime': sinfo.Starttime.strftime(
                                    '%Y-%m-%d %H:%M') + '-' + sinfo.EndTime.strftime('%H:%M'),
                                'fromname': uinfo.UserTrueName,
                                'toname': cinfo.UserTrueName,
                                'qxtype': qxtype,
                                'qxf': canceler
                            }
                            minfo = models.SysUser.objects.filter(User_Id=proinf.Relation_UserId).first()
                            push_message.delay(minfo, 'interviewex', dicx, proinf.pk)
                else:
                    rin = models.AppProbjectinterview.objects.filter(User_Id=uid, Id=sinfo.InterViewId).first()
                    if rin:
                        rin.status = 2
                        rin.CloseType = "其他原因"
                        rin.CloseReason = reson
                        rin.save()
                        # 预约取消，通知相关人
                        if qxtype < 2:
                            cinfo = models.SysUser.objects.filter(User_Id=CoachId).first()
                            uinfo = models.SysUser.objects.filter(User_Id=uid).first()
                            proinf = get_project_info(rin.ProbjectId)
                            if proinf and proinf.Probject_Id > 0:
                                dic = {
                                    'probjectname': proinf.Name,
                                    'interviewtime': sinfo.Starttime.strftime(
                                        '%Y-%m-%d %H:%M') + '-' + sinfo.EndTime.strftime('%H:%M'),
                                    'reason': reson
                                }
                                if qxtype == 0:
                                    dic['toname'] = uinfo.UserTrueName
                                    push_message.delay(cinfo, 'interviewcancel', dic, proinf.pk)
                                else:
                                    dic['toname'] = cinfo.UserTrueName
                                    push_message.delay(uinfo, 'interviewcancel', dic, proinf.pk)
                                canceler = '教练'
                                if qxtype == 0:
                                    canceler = '被教练者'
                                dicx = {
                                    'probjectname': proinf.Name,
                                    'reson': '更改约谈时间',
                                    'interviewtime': sinfo.Starttime.strftime(
                                        '%Y-%m-%d %H:%M') + '-' + sinfo.EndTime.strftime('%H:%M'),
                                    'fromname': uinfo.UserTrueName,
                                    'toname': cinfo.UserTrueName,
                                    'qxtype': qxtype,
                                    'qxf': canceler
                                }
                                minfo = models.SysUser.objects.filter(User_Id=proinf.Relation_UserId).first()
                                push_message.delay(minfo, 'interviewex', dicx, proinf.pk)
                # delete schedule
                # schedule = Schedule.objects.filter(type=3, platform=2, schedule_id=sinfo.pk)
                # PublicAttr.objects.filter(type=2, schedule_public_attr__in=schedule).update(status=6)
                # schedule.update(deleted=1)
                sinfo.delete()
                return Response(
                    response.Content().ok('预约取消成功,请重新预约新的时间').raw()
                )

        else:
            return Response(
                response.Content().error('不能取消非本人的预约').raw()
            )

    @action(methods=['post'], detail=False)
    def TxCoachScheduleAdd(self, request):
        probjectid = int_arg(request.query_params.get('probjectid'), None)
        coachid = int_arg(request.query_params.get('coachid'), None)
        userid = int_arg(request.query_params.get('userid'), 0)
        proinf = get_project_info(probjectid)
        username = request.user.UserName
        cinfo = models.SysUser.objects.filter(User_Id=coachid).first()
        if proinf and cinfo:
            dic = {
                'probjectname': proinf.Name,
                'fromname': username
            }
            push_message.delay(cinfo, 'remindcoachschedule', dic, proinf.pk)
            return Response(
                response.Content().ok().raw()
            )
        else:
            return Response(
                response.Content().error('未关联的项目教练').raw()
            )

    # Get schedule of all project that project manager owner
    @action(methods=['get'], detail=False)
    def scheduleformanager(self, request):
        coachid = request.query_params.get('coachid')
        uid = request.user.pk
        if models.SysUser.objects.filter(User_Id=request.user.pk).first().Role_Id != 3:
            return Response(
                response.Content().error().raw()
            )
        cname = ''
        cinfo = models.SysUser.objects.filter(User_Id=coachid).first()
        if cinfo:
            cname = cinfo.UserTrueName

        now = datetime.datetime.now().replace(microsecond=0)
        schedule_list = models.AppCoachschedule.objects.filter(
            Q(appscheduleattendee__attendee=coachid) | Q(CoachId=coachid),
            Enable=1, EndTime__gt=now)

        res_list = []
        for item in schedule_list.all():
            truename = None
            img = None
            title = item.Title
            minfo = get_member_info(item.UserId)
            if minfo:
                truename = minfo.TrueName
                img = minfo.Photograph
                title += '\n' + minfo.TrueName + ' 已预约'

            pinfo = get_project_info_by_user(item.UserId)
            pname = ''
            if pinfo:
                pname = pinfo.Name

            remark = item.remark
            if not item.public:
                remark = None
                title = '忙碌'

            attendee_list = []
            attendee_res_list = AppScheduleAttendee.objects.filter(schedule=item)
            if attendee_res_list.exists():
                for scheduleAttendee in attendee_res_list.all():
                    uid = scheduleAttendee.attendee
                    username = ''
                    user = SysUser.objects.filter(User_Id=uid).first()
                    if user:
                        username = user.UserTrueName
                    attendee_list.append({
                        'name': username,
                        'uid': uid
                    })

            dic = {
                'ScheduleId': item.ScheduleId,
                'UserId': item.UserId,
                'UserName': truename,
                'UserImg': img,
                'CoachId': item.CoachId,
                'Title': title,
                'Starttime': item.Starttime,
                'EndTime': item.EndTime,
                'InterViewId': item.InterViewId,
                'cname': cname,
                'type': item.TypeId,
                'pname': pname,
                'remark': remark,
                'attendee': attendee_list,
                'public': item.public
            }

            type_text = get_type_text(item.InterViewId)
            dic['type_text'] = type_text
            dic['schedule_type_text'] = get_schedule_type_text(item.TypeId)

            res_list.append(dic)
        return Response(
            response.Content().ok('SUC', res_list).raw()
        )

    @action(methods=['post'], detail=False)
    def addscheduleandinterview(self, request):
        uid = int_arg(request.data.get('uid'), 0)
        pid = int_arg(request.data.get('pid'), 0)
        coachid = int_arg(request.data.get('coachid'), 0)
        type = int_arg(request.data.get('type'), 0)
        starttime = datetime.datetime.strptime(request.data.get('starttime'), '%Y-%m-%d %H:%M')
        endtime = datetime.datetime.strptime(request.data.get('endtime'), '%Y-%m-%d %H:%M')
        hxmt = int_arg(request.data.get('hxmt'), 0)
        hxmtid = int_arg(request.data.get('hxmtid'), 0)
        interested_id = int_arg(request.data.get('interested_id'), 0)
        interview_type = request.data.get('interview_type', None)
        # concurrent progressing interview num limit
        ytlist = models.AppProbjectinterview.objects.filter(User_Id=uid, interviewType=0,
                                                            ProbjectId=pid,
                                                            status=1, Satisfaction=None)
        if models.SysUser.objects.filter(User_Id=request.user.pk).first().Role_Id in [4, 6, 7, 8]:
            maximum_unfinished_interview_num = int_arg(get_project_config_val(pid, "jlppcs"), 0)
            if maximum_unfinished_interview_num > 0 and ytlist.count() >= maximum_unfinished_interview_num:
                return Response(
                    response.Content().error('请您完成约谈后再预约').raw()
                )

        # check interview and not free type schedule conflict
        if schedule_conflict(starttime, endtime, coachid):
            return Response(
                response.Content().error('设置日程时间段重叠了').raw()
            )
        user = User.objects.filter(name=models.SysUser.objects.get(pk=coachid).UserName).first()
        user_id = user.pk if user else None
        # new schedule
        schedule = models.AppCoachschedule(Starttime=starttime, EndTime=endtime, CoachId=coachid, TypeId=0, UserId=0,
                                           InterViewId=0, Enable=1, Title='正式约谈')
        public_attr = PublicAttr.objects.create(start_time=starttime, end_time=endtime, type=2, user_id=user_id)
        app_schedule = Schedule(type=SCHEDULE_TYPE_INTERVIEW, platform=SCHEDULE_PLATFORM_V1, public_attr=public_attr)

        if hxmt == 1:
            hxmtinfo = models.ProjectCoachInterview.objects.filter(Id=hxmtid).first()
            if hxmtinfo:
                if hxmtinfo.StudentinterviewTime:
                    return Response(
                        response.Content().error('您已经预约过该教练了，请先完成后再操作').raw()
                    )
                hxmtinfo.StudentinterviewId = schedule.ScheduleId
                hxmtinfo.StudentinterviewTime = schedule.Starttime
                hxmtinfo.StudentStatus = 0
                hxmtinfo.Studentiscomplate = 0
                hxmtinfo.StudentUserId = uid
                hxmtinfo.save()
                if hxmtinfo.Id > 0:
                    schedule.InterViewId = hxmtinfo.Id
                    schedule.UserId = uid
                    schedule.TypeId = 2
                    schedule.Title = "化学面试"
                    schedule.save()
                    app_schedule.title = "化学面试"
                    app_schedule.schedule_id = schedule.pk
                    app_schedule.save()
                    if hxmtinfo.CoachId > 0 and hxmtinfo.StudentUserId > 0:
                        enable_im_and_add_friend([hxmtinfo.CoachId, int_arg(hxmtinfo.StudentUserId, 0)])
                    proinf = get_project_info(hxmtinfo.ProbjectId)
                    if proinf and proinf.Probject_Id > 0:
                        cinfo = models.SysUser.objects.filter(User_Id=coachid).first()
                        uinfo = models.SysUser.objects.filter(User_Id=uid).first()
                        minfo = models.SysUser.objects.filter(User_Id=proinf.Relation_UserId).first()
                        qymanager = get_project_company_manager(proinf.Probject_Id)
                        if qymanager.count() == 0:
                            qymanager = [models.AppMember()]
                        qyinfo = models.SysUser.objects.filter(User_Id=qymanager[0].User_Id).first()
                        companymanager = None
                        if qyinfo:
                            companymanager = qyinfo.UserTrueName
                        manager = None
                        email = None
                        if minfo:
                            manager = minfo.UserTrueName
                            email = minfo.Email
                        dic = {
                            'probjectname': proinf.Name,
                            'companyname': get_company_info(proinf.Company_Id).ShortName,
                            'companymanager': companymanager,
                            'interviewtime': schedule.Starttime.strftime(
                                '%Y-%m-%d %H:%M') + '-' + schedule.EndTime.strftime('%H:%M'),
                            'studentname': uinfo.UserTrueName,
                            'coachname': cinfo.UserTrueName,
                            'manager': manager,
                            'email': email
                        }
                        push_message.delay(cinfo, 'hxinterviewtocoach', dic, proinf.pk)
                        push_message.delay(uinfo, 'hxinterviewtostudent', dic, proinf.pk)
                        if minfo:
                            push_message.delay(minfo, 'hxinterviewtomanager', dic, proinf.pk)
                        if qyinfo:
                            push_message.delay(qyinfo, 'hxinterviewtocompany', dic, proinf.pk)
        else:
            # new interview
            proinf = get_project_info(pid)
            yytimes = get_interview_times(uid, pid)
            allyytimes = get_interview_times(0, pid)
            next_index = get_next_interview_index(uid, pid)
            oytcs = get_project_config_val(pid, "ytcs")
            pytcs = 1
            if oytcs:
                pytcs = int(oytcs)
            oytsfht = get_project_config_val(pid, "ytsfht")
            pytsfht = 0
            if oytsfht:
                pytsfht = int(oytsfht)
            omrytsc = int_arg(get_project_config_val(pid, "mrytsc"), 0)
            canusetimes = proinf.Times
            if omrytsc > 0:
                canusetimes = omrytsc
            inmod = models.AppProbjectinterview()
            if type == 0:
                # 判断教练和客户是否有教练关系
                if not models.AppProbjectrelation.objects.filter(ProbjectId=pid, UserId=uid, CoachId=coachid).exists():
                    return Response(
                        response.Content().error('教练与被教练者还没有匹配').raw()
                    )
                hh = allyytimes
                if omrytsc > 0:
                    hh = yytimes
                if hh > (canusetimes - 1):
                    return Response(
                        response.Content().error('该项目的预约时长已经用完了，请与项目管理员联系').raw()
                    )

                schedule.save()
                app_schedule.title = "正式约谈"
                app_schedule.schedule_id = schedule.pk
                app_schedule.save()
                _title = '第' + str(next_index) + '次约谈'
                inmod.ScheduleId = schedule.ScheduleId
                inmod.ProbjectId = pid
                inmod.User_Id = uid
                inmod.Coach_Id = coachid
                inmod.Title = _title
                inmod.status = 1
                inmod.StartTime = schedule.Starttime
                inmod.EndTime = schedule.EndTime
                inmod.nowInterview = next_index
                inmod.AllInterview = pytcs + pytsfht
                inmod.interviewType = 0
                if interview_type is not None:
                    inmod.interviewsubject = int_arg(interview_type)
            elif type == 1:
                # 判断教练和客户是否有教练关系
                if not models.AppProbjectrelation.objects.filter(ProbjectId=pid, UserId=interested_id,
                                                                 CoachId=coachid).exists():
                    return Response(
                        response.Content().error('教练与利益相关者的被教练者还没有匹配').raw()
                    )
                schedule.TypeId = 7
                schedule.Title = '利益相关访谈'
                schedule.save()
                app_schedule.title = "利益相关访谈"
                app_schedule.schedule_id = schedule.pk
                app_schedule.save()
                inmod.ScheduleId = schedule.ScheduleId
                inmod.ProbjectId = pid
                inmod.User_Id = uid
                inmod.Coach_Id = coachid
                inmod.Title = "教练约谈"
                inmod.status = 1
                inmod.StartTime = schedule.Starttime
                inmod.EndTime = schedule.EndTime
                inmod.nowInterview = 0
                inmod.AllInterview = pytcs + pytsfht
                inmod.interviewType = 1
                inmod.masteruserid = interested_id
            inmod.save()
            if inmod.Id > 0:
                schedule.InterViewId = inmod.Id
                schedule.UserId = uid
                schedule.Title = inmod.Title
                schedule.save()

                if proinf and proinf.Probject_Id > 0:
                    cinfo = models.SysUser.objects.filter(User_Id=coachid).first()
                    uinfo = models.SysUser.objects.filter(User_Id=uid).first()
                    # 项目管理员
                    xinfo = models.SysUser.objects.filter(User_Id=proinf.Relation_UserId).first()
                    company_manager_uid = 0
                    qymanager = get_project_company_manager(pid)
                    if qymanager.exists():
                        company_manager_uid = qymanager.first().User_Id
                    qyinfo = models.SysUser.objects.filter(User_Id=company_manager_uid).first()
                    companymanager = None
                    if qyinfo:
                        companymanager = qyinfo.UserTrueName
                    manager = None
                    email = None
                    if xinfo:
                        manager = xinfo.UserTrueName
                        email = xinfo.Email
                    dic = {
                        'probjectname': proinf.Name,
                        'companyname': get_company_info(proinf.Company_Id).ShortName,
                        'companymanager': companymanager,
                        'interviewtime': schedule.Starttime.strftime(
                            '%Y-%m-%d %H:%M') + '-' + schedule.EndTime.strftime('%H:%M'),
                        'studentname': uinfo.UserTrueName,
                        'coachname': cinfo.UserTrueName,
                        'manager': manager,
                        'email': email,
                        'num': next_index,
                    }
                    # 企业管理员可能有多个 IsManger project_id
                    user_ids = models.AppProbjectrelation.objects.filter(ProbjectId=proinf.Probject_Id,
                                                                         IsManger=True).values_list('UserId',
                                                                                                    flat=True)
                    company_user = models.SysUser.objects.filter(pk__in=user_ids)

                    if type == 0:
                        # 正式约谈 (37, 38, 39)
                        user_role = models.SysUser.objects.get(User_Id=request.user.pk).Role_Id
                        # 被教练者创建
                        if user_role != cinfo.Role_Id or user_role in [3, 5]:
                            push_message.delay(cinfo, 'interviewtostudent', dic, proinf.pk)
                        if user_role != uinfo.Role_Id or user_role in [3, 5]:
                            push_message.delay(uinfo, 'interviewtocoach', dic, proinf.pk)

                        push_message.delay(xinfo, 'interviewtomanager', dic, proinf.pk)
                    elif type == 1:
                        # 利益相关 预约访谈后通知被企业管理员、群智管理员（15）
                        masterinfo = models.AppMemberinterested.objects.filter(
                            ProbjectId=proinf.Probject_Id, Member_Id=uid, CoachId=cinfo.User_Id).first()
                        if masterinfo:
                            # 利益相关者
                            interest_user = models.SysUser.objects.filter(
                                User_Id=masterinfo.Interested_Id).first()
                            dic['url'] = settings.SITE_URL
                            # 被教练者name
                            dic['student_name'] = models.SysUser.objects.get(pk=inmod.masteruserid).UserTrueName
                            push_message.delay(cinfo, 'interviewtocoachbyinterested', dic, proinf.pk)
                            # 之前发的 interviewtomanager
                            dic['interest_name'] = uinfo.UserTrueName
                            all_interest = models.AppProbjectrelation.objects.filter(Interested_Id=interested_id, RolesId=7)
                            ids = all_interest.values_list('UserId', flat=True)
                            end = models.AppProbjectinterview.objects.filter(User_Id__in=ids, interviewType=1, status=1)
                            not_end_users = set(ids) - set(end.values_list('User_Id', flat=True))
                            users = models.SysUser.objects.filter(User_Id__in=not_end_users).values_list('UserTrueName')
                            user_names = get_str_name(users)
                            dic['end_num'] = end.count()
                            dic['not_end_num'] = all_interest.count() - end.count()
                            dic['all_num'] = all_interest.count()
                            dic['names'] = user_names
                            push_message.delay(xinfo, 'interviewtostudentbyinterested', dic, proinf.pk)
                            if company_user.exists:
                                for user in company_user:
                                    push_message.delay(user, 'interviewtostudentbyinterested', dic, proinf.pk)
        return Response(
            response.Content().ok('预约成功').raw()
        )
