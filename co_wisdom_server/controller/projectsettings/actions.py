from data import models


def get_project_setting_all(projectid):
    setting_list = models.AppProbjectsetting.objects.filter(Probject_Id=projectid)
    dic = {}
    for item in setting_list.all():
        key = item.KeyName.lower()
        dic[key] = {
            'val': item.KeyValue,
            'json': item.KeyJson
        }
    return dic

def get_project_config_val(pid, key, isJson=False):
    dic = get_project_setting_all(pid)
    if dic and key.lower() in dic:
        obj = dic[key.lower()]
        if obj:
            if isJson:
                return obj.get('json')
            else:
                return obj.get('val')
    return ''