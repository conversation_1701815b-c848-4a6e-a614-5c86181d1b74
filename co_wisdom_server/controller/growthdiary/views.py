import re

from rest_framework.decorators import action
from rest_framework.response import Response

from data import models, serializers, extension
from utils import int_arg, null_or_blank, delete_htmltag


class GrowthDiaryViewSet(extension.ResponseViewSet):
    queryset = models.AppGrowthdiary.objects.all()
    serializer_class = serializers.AppGrowthdiarySerializer

    @action(methods=['post'], detail=False)
    def addfront(self, request):
        main_data = request.data.get('mainData')
        Title = main_data.get('Title', '')
        main_data['User_Id'] = request.user.pk
        projectid = int_arg(main_data.get('ProbjectId'), 0)
        main_data['ProbjectId'] = projectid
        if null_or_blank(Title):
            cont = main_data.get('Remark', '')
            if not null_or_blank(cont):
                main_data['Title'] = delete_htmltag(cont)
        res = self.add_row(main_data)
        return Response(
            res.raw()
        )

    @action(methods=['post'], detail=False, url_path='UpdateFront')
    def update_new(self, request):
        main_data = request.data.get('mainData')
        Title = main_data.get('Title', '')
        if null_or_blank(Title):
            cont = main_data.get('Remark', '')
            if not null_or_blank(cont):
                main_data['Title'] = re.sub(r'<[^>]+>', '', cont)[:50]
        res = self.update_row(main_data)
        return Response(
            res.lower_raw()
        )
