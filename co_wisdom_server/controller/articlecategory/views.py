import json

from django.db.models import F
from rest_framework.decorators import action
from rest_framework.response import Response

from controller.resourcecategory.actions import get_dic_cate
from data import models, serializers, extension
from utils import response, int_arg
from utils.model import add_option


class ArticleCategoryViewSet(extension.ResponseViewSet):
    queryset = models.AppArticlecategory.objects.all()
    serializer_class = serializers.AppArticlecategorySerializer

    @action(methods=['post'], detail=False)
    def Add(self, request):
        main_data = request.data.get('mainData')
        ParentId = int_arg(main_data.get('ParentId'))
        main_data['ParentId'] = ParentId
        res = self.add_row(main_data)
        return Response(
            res.raw()
        )

    @action(methods=['post'], detail=False, url_path='update')
    def update_new(self, request):
        main_data = request.data.get('mainData')
        ParentId = int_arg(main_data.get('ParentId'))
        main_data['ParentId'] = ParentId
        res = self.update_row(main_data)
        return Response(
            res.raw()
        )

    @action(methods=['post'], detail=False)
    def getpagedata(self, request):
        t = self.get_page_rows(request.data)
        if t.count() > 0:
            extra = []
            for item in t.all():
                dic = {}
                dic["eCate_Id"] = item.Cate_Id
                # 二级类
                child = models.AppArticlecategory.objects.filter(ParentId=item.Cate_Id, Enable=1)
                if child.count() > 0:
                    dic['children'] = self.serializer_class(child.all(), many=True).data
                extra.append(dic)
            t.extra_list = extra
        return Response(
            self.page_data(t)
        )

    @action(methods=['get'], detail=False)
    def GetCategory(self, request):
        cates = models.AppArticlecategory.objects.filter(ParentId=0, Enable=1).order_by('-OrderNo')
        res_list = []
        for item in cates.all():
            dic = {}
            dic["CateId"] = item.Cate_Id
            dic["Name"] = item.CateName
            # 二级类
            child = models.AppArticlecategory.objects.filter(ParentId=item.Cate_Id, Enable=1).annotate(CateId=F('Cate_Id'), Name=F('CateName'))
            if child.count() > 0:
                dic['children'] = child.values('CateId', 'Name')
            else:
                dic['children'] = []
            res_list.append(dic)
        return Response(
            response.Content().ok('SUC', res_list).raw()
        )
