import datetime

from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.viewsets import ModelViewSet, ViewSet, GenericViewSet

from controller.company import views as company_views
from controller.company.actions import get_company_info
from controller.member.actions import get_member_info
from controller.project.actions import get_project_info, get_project_def_coach_by_user, get_project_info_by_user
from controller.projectsettingcoachneed.actions import get_project_setting_all
from controller.projectsettings.actions import get_project_config_val
from data import models, serializers, extension
from data.user.models import User
from utils import response, blank_to_none, int_arg, value_or_default, aesencrypt, randomPassword, float_arg, \
    null_or_blank
import json
from django.db.models import Avg, Count, Min, Sum






class ProjectSettingCoachNeedViewSet(extension.ResponseViewSet):
    queryset = models.AppProbjectsettingcoachneed.objects.all()
    serializer_class = serializers.AppProbjectsettingcoachneedSerializer

    @action(methods=['get'], detail=False)
    def probjectsetting(self, request):
        probjectid = int_arg(request.query_params.get('probjectid'), None)
        d = get_project_setting_all(probjectid)
        res = response.Content()
        res.data = d
        return Response(
            res.ok('SUC').raw()
        )

    @action(methods=['get'], detail=False)
    def probjectsettingone(self, request):
        probjectid = int_arg(request.query_params.get('probjectid'), None)
        key = request.data.get('key')
        obj = get_project_config_val(probjectid, key)
        return Response(
            json.dump(obj)
        )

    @action(methods=['post'], detail=False)
    def add(self, request):
        main_data = request.data.get('mainData')
        probjectid = int_arg(main_data.get('probjectid'), 0)
        if probjectid == 0:
            return Response(
                response.Content().error('关联项目不能空').raw()
            )
        for key, values in main_data.items():
            if key == 'probjectid':
                continue
            ino = models.AppProbjectsettingcoachneed()
            ino.Probject_Id = probjectid
            ino.KeyName = key
            ino.KeyValue = str(values)
            ino.save()
        return Response(
            response.Content().ok('新增成功').lower_raw()
        )

    @action(methods=['post'], detail=False)
    def UpdateFront(self, request):
        main_data = request.data.get('mainData')
        probjectid = int_arg(main_data.get('probjectid'), 0)
        if probjectid == 0:
            return Response(
                response.Content().error('关联项目不能空').raw()
            )
        for key, values in main_data.items():
            if key == 'probjectid':
                continue
            ino = models.AppProbjectsettingcoachneed()
            sinfo = models.AppProbjectsettingcoachneed.objects.filter(Probject_Id=probjectid, KeyName=key).first()
            if not sinfo:
                ino.Probject_Id = probjectid
                ino.KeyName = key
                ino.KeyValue = str(values)
                ino.save()
            else:
                sinfo.KeyValue = str(values)
                sinfo.save()
        return Response(
            response.Content().ok('修改成功').lower_raw()
        )