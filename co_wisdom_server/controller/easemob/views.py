import json

from rest_framework.response import Response
from rest_framework.views import APIView

from data import models
from utils import int_arg, response
from utils.easemob.api import EasemobAPI


class userstatus(APIView):
    def get(self, request):
        username = request.query_params.get('username', None)
        if not username:
            return Response(
                response.Content().error('缺少用户名参数').raw()
            )
        res = EasemobAPI().user_stat(username)
        if res:
            content = json.loads(res.content)
            return Response(
                content
            )