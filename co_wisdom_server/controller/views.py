import datetime
import json
import os

from django.conf import settings
from django.contrib.auth.models import User as sys_user
from django.http import FileResponse
from rest_framework import status
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.views import APIView
from rest_framework.viewsets import ModelViewSet, ViewSet, GenericViewSet

from django.conf import settings
from controller.account.serializers import UserSerializer
from data import models
from data.company.models import Company
from data.company.team.models import Team
from data.company.views import getMyCompany
from data.project import models as project_models
from data.project.views import getMyProject
from data.user.coachee.models import Coachee
from data.user.company_admin.models import Company_admin
from data.user.evaluator.models import Evaluator
from data.user.models import User
from data.user.stakeholder.models import Stakeholder
from data.user.views import transRoleIdToRoleBitwise
from utils import randomPassword, aesencrypt
from .serializers import CompanySerializer, ProjectSerializer, TeamSerializer, MemberSerializer
import logging

logger = logging.getLogger(__name__)

class Log(APIView):
    authentication_classes = []
    def post(self, request):
        try:
            log_str = request.data.get('payload')
            path = os.path.join(settings.BASE_DIR, "stats.log")
            file_object = open(path, 'a+')
            file_object.write(log_str)
            file_object.write('\n')
            file_object.close()
            # TODO: zip log file if file size over 1MB
        except Exception as e:
            logger.error('Write log file execute failed.', e)
        return Response(status=status.HTTP_200_OK)


class FileDownload(APIView):
    authentication_classes = []
    def get(self, request, model, date, name):
        relative_path = settings.UPLOAD_URL + model + '/' + date + '/' + name
        fileinfo = models.SysUpfile.objects.filter(FilePath=relative_path).first()
        if fileinfo:
            path = os.path.join(settings.MEDIA_ROOT, model, date, name)
            file = open(path, 'rb')
            response = FileResponse(file)
            return response
        else:
            return Response(status=status.HTTP_404_NOT_FOUND)


class CompanyViewSet(GenericViewSet):
    queryset = Company.objects.all()
    serializer_class = CompanySerializer

    @action(methods=['post'], detail=False)
    def getpagedata(self, request):
        companys = getMyCompany(request)
        extra = []
        rows = []
        for company in companys:
            project_count = project_models.Project.objects.filter(company_id=company.id).count()
            extra.append({"eCompany_Id": company.id, "ProbjectNum": project_count})
            rows.append({
                "CompanyAttr": company.category,
                "CompanyName": company.company_name,
                "Company_Id": company.id,
                "Industry": company.industry,
                "Instro": company.intro,
                "Logo": company.logo,
                "Manage_Id": request.user.pk,  # TODO company manager id
                "ParentId": company.parentid,
                "Personnels": company.employee_scale,
                "ShortName": company.short_name,
                "Video": company.video,
                "website": company.website
            })
        return Response({
            "status": 0,
            'msg': None,
            "rows": rows,
            "extra": extra,
            "total": len(companys)
        })


class TeamViewSet(GenericViewSet):
    queryset = Team.objects.all()
    serializer_class = TeamSerializer


class MemberViewSet(GenericViewSet):
    queryset = project_models.Membership.objects.all()
    serializer_class = MemberSerializer

    @action(methods=['post'], detail=False)
    def getpagedata(self, request):
        project_id = 0
        if request.data["wheres"]:
            for where in json.loads(request.data["wheres"]):
                if where["name"] == "ProbjectId" and where["value"]:
                    project_id = where["value"]
                    break
        # get project id from payload
        members = project_models.Membership.objects.filter(project_id=project_id)
        rows = []
        extra = []
        for member in members:
            member_base = None
            if member.role_id == 6:
                member_base = Coachee.objects.get(user_id=member.user.id)
            elif member.role_id == 5:
                member_base = Company_admin.objects.get(user_id=member.user.id)
            elif member.role_id == 7:
                member_base = Stakeholder.objects.get(user_id=member.user.id)
            extra.append({
                "Role_Id": member.role_id,
                "UserName": member.user.login_name,
                "coach": member.coach.user.login_name if member.coach else "--",
                "eUser_Id": member.user.id,
                "ildp": None,  # TODO
            })
            rows.append({
                "Age": member.user.age,
                "CoachRemark": member_base.coach_remark,
                "CompanyName": member.project.company.company_name,
                "CompanyWorkingDate": member_base.working_date_company,
                "Company_Id": member.project.company.id,
                "Degree": member.user.education,
                "Department": member_base.department,
                "Duty": member_base.duty,
                "Education": member.user.education,
                "Enable": 1,
                "GraduatioDate": member.user.graduation_date,
                "GWWorkingDate": member_base.working_date_position,
                "Interested_Id": member.coachee_related.user.id if member.coachee_related else None,
                "Major": member.user.major,
                "ManagerRemark": member_base.cowisdom_remark,
                "Member_Id": member.id,
                "MessageId": None,  # TODO
                "ParentDepartment": member_base.parent_department,
                "Photograph": member.user.pic,
                "Relation": member_base.relation if hasattr(member_base, "relation") else None,
                "Remark": member_base.remark,
                "School": member.user.school,
                "Sex": member.user.gender,
                "SendPush": None,
                "SendWeixin": None,
                "Sendemail": None,
                "Sendmsg": None,
                "Tel": None,
                "TrueName": member.user.real_name,
                "UseInWxApplet": None,
                "User_Id": member.user.id,
                "WorkingDate": member_base.working_date_first,
                "address": member.user.address,
                "birthday": member.user.birthday,
                "email": member.user.email,
                "employees": member_base.underlings_number,
                "employee": member_base.team_scale,  # team scale
                "gzaddr": member_base.office_address,
                "liaojie": member_base.coach_understand,
                "mobile": member.user.mobile,
                "qitzw": member_base.other_position,
                "wechat": None
            })
        return Response({
            "status": 0,
            'msg': None,
            "rows": rows,
            "extra": extra,
            "total": len(rows)
        })

    @action(methods=['post'], detail=False)
    def Add(self, request):
        data = request.data["mainData"]
        # form check
        errorMessage = None
        if len(data["UserName"]) == 0:
            errorMessage = "登录名不能为空"
        elif User.objects.filter(login_name=data["UserName"]).first():
            errorMessage = "登录名已经存在"
        elif len(data["password"]) == 0:
            data["password"] = aesencrypt(randomPassword(8))
        elif data["Role_Id"] is None or int(data["Role_Id"]) < 4:
            errorMessage = "请选择会员组"
        if errorMessage:
            return Response({
                "status": False,
                'message': errorMessage,
                "code": 0,
                "data": None
            })
        project = project_models.Project.objects.filter(id=data["ProbjectId"]).first()
        # create user
        role = transRoleIdToRoleBitwise(int(data["Role_Id"]))
        user = User(login_name=data["UserName"], login_password=data["password"], real_name=data["TrueName"],
                    email=data["email"], mobile=data["mobile"], role=role,
                    gender=None if len(data["Sex"]) == 0 else data["Sex"], date_joined=datetime.now())
        user.save()
        # create sys user
        sys_user.objects.create_user(id=user.id, username=user.login_name, email=user.email,
                                     password=user.login_password)
        # TODO 开通环信用户
        # create role
        role_user = None
        related_coachee = None
        one_year_days = 365.25
        company_time_delta = datetime.timedelta(
            days=int(0 if len(data["CompanyWorkingDateNum"]) == 0 else data["CompanyWorkingDateNum"]) * one_year_days)
        position_time_delta = datetime.timedelta(
            days=int(0 if len(data["GwWorkingDateNum"]) == 0 else data["GwWorkingDateNum"]) * one_year_days)
        first_time_delta = datetime.timedelta(
            days=int(0 if len(data["WorkingDateNum"]) == 0 else data["WorkingDateNum"]) * one_year_days)

        working_date_company = datetime.datetime.now() - company_time_delta
        working_date_position = datetime.datetime.now() - position_time_delta
        working_date_first = datetime.datetime.now() - first_time_delta
        if data["Role_Id"] == '5':
            role_user = Company_admin(user=user, company=project.company, team=None,
                                      working_date_company=working_date_company,
                                      working_date_position=working_date_position,
                                      working_date_first=working_date_first,
                                      duty=data["Duty"], department=data["Department"],
                                      cowisdom_remark=data["ManagerRemark"])
        elif data["Role_Id"] == '6':
            role_user = Coachee(user=user, company=project.company, team=None,
                                working_date_company=working_date_company,
                                working_date_position=working_date_position,
                                working_date_first=working_date_first,
                                duty=data["Duty"], department=data["Department"],
                                cowisdom_remark=data["ManagerRemark"])
        elif data["Role_Id"] == '7':
            related_coachee_id = data["Interested_Id"]
            related_coachee = Coachee.objects.filter(user_id=related_coachee_id).first()
            role_user = Stakeholder(user=user, company=project.company, team=None,
                                    working_date_company=working_date_company,
                                    working_date_position=working_date_position,
                                    working_date_first=working_date_first,
                                    duty=data["Duty"], department=data["Department"],
                                    cowisdom_remark=data["ManagerRemark"], related_coachee=related_coachee,
                                    relation=data["Relation"], cooperation_years=data["Concertyears"]
                                    )
        elif data["Role_Id"] == '8':
            role_user = Evaluator(user=user, company=project.company, team=None,
                                  working_date_company=working_date_company,
                                  working_date_position=working_date_position,
                                  working_date_first=working_date_first,
                                  duty=data["Duty"], department=data["Department"],
                                  cowisdom_remark=data["ManagerRemark"])
        role_user.save()
        # create membership
        new_member = project_models.Membership(project=project, user=user, isAdmin=1 if data["Role_Id"] == 5 else 0,
                                               role_id=data["Role_Id"], coachee_related=related_coachee
                                               )
        new_member.save()
        # TODO send welcome email
        return Response(
            {
                "code": 311,
                'data': UserSerializer(user, many=False).data,
                "message": '保存成功',
                "status": True
            }
        )

    @action(methods=['post'], detail=False, url_path='update')
    def member_update(self, request):
        data = request.data["mainData"]
        # form check
        errorMessage = None
        if len(data["UserName"]) == 0:
            errorMessage = "登录名不能为空"
        elif data["Role_Id"] is None or int(data["Role_Id"]) < 4:
            errorMessage = "请选择会员组"
        if errorMessage:
            return Response({
                "status": False,
                'message': errorMessage,
                "code": 0,
                "data": None
            })
        project = project_models.Project.objects.filter(id=data["ProbjectId"]).first()
        # update user
        role = transRoleIdToRoleBitwise(int(data["Role_Id"]))
        User.objects.filter(login_name=data["UserName"]).update(real_name=data["TrueName"],
                                                                email=data["email"], mobile=data["mobile"],
                                                                role=role,
                                                                gender=None if len(data["Sex"]) == 0 else data[
                                                                    "Sex"])
        user = User.objects.filter(login_name=data["UserName"]).first()
        # update sys user
        sys_user.objects.filter(username=user.login_name).update(email=user.email)
        # update role
        related_coachee = None
        one_year_days = 365.25
        company_time_delta = datetime.timedelta(
            days=int(0 if len(data["CompanyWorkingDateNum"]) == 0 else data["CompanyWorkingDateNum"]) * one_year_days)
        position_time_delta = datetime.timedelta(
            days=int(0 if len(data["GwWorkingDateNum"]) == 0 else data["GwWorkingDateNum"]) * one_year_days)
        first_time_delta = datetime.timedelta(
            days=int(0 if len(data["WorkingDateNum"]) == 0 else data["WorkingDateNum"]) * one_year_days)

        working_date_company = datetime.datetime.now() - company_time_delta
        working_date_position = datetime.datetime.now() - position_time_delta
        working_date_first = datetime.datetime.now() - first_time_delta
        if data["Role_Id"] == '5':
            Company_admin.objects.filter(user=user).update(working_date_company=working_date_company,
                                                           working_date_position=working_date_position,
                                                           working_date_first=working_date_first,
                                                           duty=data["Duty"], department=data["Department"],
                                                           cowisdom_remark=data["ManagerRemark"])
        elif data["Role_Id"] == '6':
            Coachee.objects.filter(user=user).update(working_date_company=working_date_company,
                                                     working_date_position=working_date_position,
                                                     working_date_first=working_date_first,
                                                     duty=data["Duty"], department=data["Department"],
                                                     cowisdom_remark=data["ManagerRemark"])
        elif data["Role_Id"] == '7':
            related_coachee_id = data["Interested_Id"]
            related_coachee = Coachee.objects.filter(user_id=related_coachee_id).first()
            Stakeholder.objects.filter(user=user).update(working_date_company=working_date_company,
                                                         working_date_position=working_date_position,
                                                         working_date_first=working_date_first,
                                                         duty=data["Duty"], department=data["Department"],
                                                         cowisdom_remark=data["ManagerRemark"],
                                                         related_coachee=related_coachee,
                                                         relation=data["Relation"],
                                                         cooperation_years=data["Concertyears"]
                                                         )
        elif data["Role_Id"] == '8':
            Evaluator.objects.filter(user=user).update(working_date_company=working_date_company,
                                                       working_date_position=working_date_position,
                                                       working_date_first=working_date_first,
                                                       duty=data["Duty"], department=data["Department"],
                                                       cowisdom_remark=data["ManagerRemark"])
        # update membership
        project_models.Membership.objects.filter(project=project, user=user).update(
            isAdmin=1 if data["Role_Id"] == 5 else 0,
            role_id=data["Role_Id"], coachee_related=related_coachee
        )
        return Response(
            {
                "code": 311,
                'data': UserSerializer(user, many=False).data,
                "message": '保存成功',
                "status": True
            }
        )


class ProjectViewSet(ModelViewSet):
    queryset = project_models.Project.objects.all()
    serializer_class = ProjectSerializer

    @action(methods=['post'], detail=False)
    def getpagedata(self, request):
        projects = getMyProject(request)
        rows = []
        for project in projects:
            Manager_UserId = None
            project_admin = project_models.Membership.objects.filter(project=project, isAdmin=1).first()
            if project_admin:
                Manager_UserId = project_admin.user.id
            rows.append({
                "Background": project.background,
                "CoachIds": None,  # TODO
                "Company_Id": project.company_id,
                "CreateDate": project.create_date,
                "Creator": None,
                "Enable": 1,
                "Endtime": project.time_end,
                "FilePath": project.file_path,
                "Instro": project.intro,
                "InterViewOrder": project.interview_order,
                "Manager_UserId": Manager_UserId,
                "Name": project.project_name,
                "Number": project.coachee_count,
                "Objective": project.objective,
                "Probject_Id": project.id,
                "Relation_UserId": project.owner.user.id,
                "Starttime": project.time_start,
                "Status": project.is_signed,
                "Times": project.times,
                "Video": project.video,
                "Website": project.website,
                "dothing": project.dothing,
                "remark": project.remark
            })
        return Response({
            "status": 0,
            'msg': None,
            "rows": rows,
            "extra": None,
            "total": projects.count()
        })
