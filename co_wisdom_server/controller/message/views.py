import datetime

from rest_framework.decorators import action
from rest_framework.response import Response

from data import models, serializers, extension
from utils import response, int_arg
from utils.messagecenter.center import push_message, push_custom_message


class AppMessageViewSet(extension.ResponseViewSet):
    queryset = models.AppMessage.objects.all()
    serializer_class = serializers.AppMessageSerializer

    @action(methods=['post'], detail=False)
    def UpdateStausAll(self, request):
        aceptid = int_arg(request.query_params.get('aceptid'), None)
        res_list = models.AppMessage.objects.filter(is_read=0, accept_userid=aceptid)
        for item in res_list.all():
            item.is_read = 1
            item.read_time = datetime.datetime.now()
            item.save()
        return Response(
            response.Content().ok().raw()
        )

    @action(methods=['post'], detail=False)
    def UpdateStaus(self, request):
        aceptid = int_arg(request.query_params.get('aceptid'), None)
        messageid = int_arg(request.query_params.get('messageid'), None)
        message = models.AppMessage.objects.filter(id=messageid, accept_userid=aceptid).first()
        if message and message.is_read == 0:
            message.is_read = 1
            message.read_time = datetime.datetime.now()
            message.save()
        return Response(
            response.Content().ok().raw()
        )

    @action(methods=['post'], detail=False)
    def UpdateReplay(self, request):
        main_data = request.data.get('mainData')
        aceptid = int_arg(main_data.get('aceptid'), None)
        messageid = int_arg(main_data.get('messageid'), None)
        content = main_data.get('content')
        message = models.AppMessage.objects.filter(id=messageid, accept_userid=aceptid).first()
        if message and message.is_read == 0:
            message.is_read = 1
            message.read_time = datetime.datetime.now()
            message.replay = content
            message.replaytime = datetime.datetime.now()
            message.replay_name = request.user.UserName
            message.save()
        return Response(
            response.Content().ok().raw()
        )

    @action(methods=['post'], detail=False)
    def getpagedatafront(self, request):
        t = self.get_page_rows(request.data)
        if t.count() > 0:
            extra = []
            for item in t.all():
                dic = {"eid": item.id}
                fromuser = models.AppMember.objects.filter(User_Id=item.post_userid).first()
                if fromuser:
                    dic["fromusername"] = fromuser.TrueName
                    dic["fromuserimg"] = fromuser.Photograph
                else:
                    dic["fromusername"] = item.post_user_name
                    dic["fromuserimg"] = ""
                extra.append(dic)
            t.extra_list = extra
        return Response(
            self.page_data(t)
        )

    @action(methods=['post'], detail=False)
    def DelMsg(self, request):
        keys = request.data
        aceptid = int_arg(request.query_params.get('aceptid'), None)
        models.AppMessage.objects.filter(accept_userid=aceptid, id__in=keys).all().delete()
        return Response(
            response.Content().ok().raw()
        )

    @action(methods=['post'], detail=False)
    def SendMsg(self, request):
        msg = request.data.get('msg')
        title = request.data.get('title', '')
        uid = int_arg(request.data.get('uid'), None)
        uinfo = models.SysUser.objects.filter(User_Id=uid).first()
        if uinfo:
            push_custom_message.delay(uinfo, title=title, msg=msg)
        return Response(
            response.Content().ok().raw()
        )
