from rest_framework.response import Response

from controller.coach.actions import get_coach_info
from controller.dictionary.actions import get_dictionary_list_value
from controller.hxmeetingdata.actions import get_hxmeeting_data
from controller.member.actions import get_member_info
from data import models, serializers
from utils import response, int_arg
from utils.easemob.common import get_im_id


def get_hxdata(interviewid):
    model = get_hxmeeting_data('2_'+ str(interviewid))
    return model


def write_interview_coach_to_student(project_coach_interview_list, jlpccs=0):
    data_list = []
    for item in project_coach_interview_list:
        dic = serializers.ProjectCoachInterviewSerializer(item, many=False).data
        uinfo = get_member_info(int_arg(item.StudentUserId, 0))
        if uinfo:
            dic["photograph"] = uinfo.Photograph
            dic["userName"] = uinfo.TrueName
            dic["department"] = uinfo.Department
            dic["duty"] = uinfo.Duty
            dic["companyName"] = uinfo.CompanyName
            dic['messageid'] = get_im_id(int_arg(uinfo.User_Id, 0))
        comodel = get_coach_info(item.CoachId)
        dic["img"] = comodel.Photograph
        dic["truename"] = comodel.TrueName
        dic["coachlevel"] = get_dictionary_list_value("coachlevel", comodel.CoachLevel)
        dic["jlzz"] = get_dictionary_list_value("jlzz", comodel.jlzz)
        dic["jlzw"] = get_dictionary_list_value("jlzw", comodel.jlzw)
        dic["coachmessageid"] = get_im_id(item.CoachId)
        if item.interviewId and item.interviewId > 0:
            coachsh = models.AppCoachschedule.objects.filter(ScheduleId=item.interviewId).first()
            if coachsh:
                dic['interviewTime'] = coachsh.Starttime.strftime('%Y-%m-%d %H:%M') + ' -' + coachsh.EndTime.strftime(
                    '%H:%M')
                hxdata = get_hxdata(item.Id)
                if hxdata:
                    dic["hxdata"] = serializers.AppHxmeetingdataSerializer(hxdata, many=False).data
        data_list.append(dic)
    res = response.Content()
    res.data = {
        'data': data_list,
        'jlpccs': jlpccs
    }
    return Response(
        res.ok('SUC').raw()
    )