import datetime
import json

from django.conf import settings
from rest_framework.decorators import action
from rest_framework.response import Response

from controller.coach.actions import get_coach_info
from controller.company.actions import get_company_info
from controller.dictionary.actions import get_dictionary_list_value
from controller.member.actions import get_member_info
from controller.project.actions import get_project_info
from controller.projectcoachinterview.actions import write_interview_coach_to_student
from controller.projectsettings.actions import get_project_config_val
from controller.resource.actions import get_default_help
from data import models, serializers, extension
from utils import response, int_arg, aesdecrypt
from utils.easemob.common import get_im_id, enable_im_and_add_friend
from utils.messagecenter.center import push_message
from utils.model import add_option
from utils.queryset import distinct
from controller.projectinterview.project_interview_constant import TYPE_MAP


class ProjectCoachInterviewViewSet(extension.ResponseViewSet):
    queryset = models.ProjectCoachInterview.objects.all()
    serializer_class = serializers.ProjectCoachInterviewSerializer

    @action(methods=['post'], detail=False)
    def add(self, request):
        main_data = request.data.get('mainData')
        ProbjectId = int_arg(main_data.get('ProbjectId'), 0)
        if ProbjectId == 0:
            return Response(
                response.Content().error('请先选择项目')
            )
        coachinterviewid = int_arg(main_data.get('coachinterviewid'), 0)
        interviewId = int_arg(main_data.get('interviewId'), 0)
        yytype = int_arg(main_data.get('yytype'), 0)
        starttime = main_data.get('starttime', '')
        endtime = main_data.get('endtime', '')
        if yytype == 1:
            # 群智预约
            umodel = models.AppProbjectrelation.objects.filter(ProbjectId=ProbjectId, IsManger=1).first()
            if not umodel:
                return Response(
                    response.Content().error('请先创建企业负责人，再安排面试').lower_raw()
                )
            main_data['UserId'] = umodel.UserId
            main_data['interviewTime'] = datetime.datetime.strptime(starttime, '%Y-%m-%d %H:%M')
        else:
            main_data['UserId'] = request.user.pk

        main_data['StudentStatus'] = 0
        main_data['Studentiscomplate'] = 0

        if coachinterviewid > 0:
            main_data['Id'] = coachinterviewid
            res = self.update_row(main_data)
            if res.status:
                cinterview = res.row
                cinfo = models.AppCoachschedule.objects.filter(CoachId=cinterview.CoachId, UserId=cinterview.UserId,
                                                               ScheduleId=interviewId).first()
                if cinfo:
                    cinfo.Starttime = datetime.datetime.strptime(starttime, '%Y-%m-%d %H:%M')
                    cinfo.EndTime = datetime.datetime.strptime(endtime, '%Y-%m-%d %H:%M')
                    cinfo.save()
        else:
            res = self.add_row(main_data)
            if res.status:
                if yytype == 1:
                    cinterview = res.row
                    cpinfo = models.Projectcoach.objects.filter(ProbjectId=cinterview.ProbjectId,
                                                                CoachId=cinterview.CoachId).first()
                    if cpinfo:
                        cpinfo.UserId = cinterview.UserId
                        cpinfo.Status = 1
                        cpinfo.save()
                    coach_schedule = models.AppCoachschedule()
                    coach_schedule.UserId = cinterview.UserId
                    coach_schedule.CoachId = cinterview.CoachId
                    coach_schedule.TypeId = 1
                    coach_schedule.Title = "企业面谈"
                    coach_schedule.Starttime = datetime.datetime.strptime(starttime, '%Y-%m-%d %H:%M')
                    coach_schedule.EndTime = datetime.datetime.strptime(endtime, '%Y-%m-%d %H:%M')
                    coach_schedule.Enable = 1
                    coach_schedule.save()
                    cinterview.interviewId = coach_schedule.ScheduleId
                    cinterview.save()
                    if cinterview.CoachId > 0 and cinterview.UserId > 0:
                        enable_im_and_add_friend([cinterview.CoachId, int_arg(cinterview.UserId, 0)])
                    # 通知企业和教练
                    proinfo = get_project_info(ProbjectId)
                    minfo = models.SysUser.objects.filter(User_Id=cinterview.UserId).first()
                    cinfo = models.SysUser.objects.filter(User_Id=cinterview.CoachId).first()
                    if proinfo and proinfo.Relation_UserId > 0:
                        uinfo = models.SysUser.objects.filter(User_Id=proinfo.Relation_UserId).first()
                        dic = {
                            'probjectname': proinfo.Name,
                            'companyname': get_company_info(proinfo.Company_Id).ShortName,
                            'companymanager': minfo.UserTrueName,
                            'manager': uinfo.UserTrueName,
                            'email': uinfo.Email,
                            'coachname': cinfo.UserTrueName,
                            'time': starttime
                        }
                        push_message.delay(minfo, 'choosecoachtocompany', dic, proinfo.pk)
                        push_message.delay(cinfo, 'choosecoachtocoach', dic, proinfo.pk)
        return Response(
            res.raw()
        )

    @action(methods=['post'], detail=False, url_path='update')
    def update_new(self, request):
        main_data = request.data.get('mainData')
        res = self.update_row(main_data)
        if res.status:
            cinter = res.row
            cpinfo = models.Projectcoach.objects.filter(ProbjectId=cinter.ProbjectId, CoachId=cinter.CoachId).first()
            if cpinfo:
                cpinfo.Status = 0
                if cinter.Status:
                    cpinfo.Status = cinter.Status + 1
                cpinfo.save()
            # 查找项目负责人 并发送通知
            probjectid = int_arg(cinter.ProbjectId)
            proinf = get_project_info(probjectid)
            if proinf and proinf.Relation_UserId > 0:
                minfo = models.SysUser.objects.filter(User_Id=proinf.Relation_UserId).first()
                cinfo = models.SysUser.objects.filter(User_Id=cinter.CoachId).first()
                uinfo = models.SysUser.objects.filter(User_Id=cinter.UserId).first()
                if uinfo:
                    suctext = '待定'
                    if cinter.Status == 1:
                        suctext = '适合'
                    elif cinter.Status == 3:
                        suctext = '不合适'
                    moretips = '我们会继续为您推荐更适合的教练'
                    if cinter.Status == 1:
                        moretips = ''
                    dic = {
                        'probjectname': proinf.Name,
                        'coachname': cinfo.UserTrueName,
                        'companymanager': minfo.UserTrueName,
                        'manager': minfo.UserTrueName,
                        'suctext': suctext,
                        'moretips': moretips
                    }
                    push_message.delay(uinfo, 'complatecoach', dic, proinf.pk)
                if minfo:
                    suctext = '待定'
                    if cinter.Status == 1:
                        suctext = '适合'
                    elif cinter.Status == 3:
                        suctext = '不合适'
                    moretips = cinter.NoPassReason
                    if cinter.Status == 1:
                        moretips = ''
                    dic = {
                        'probjectname': proinf.Name,
                        'coachname': cinfo.UserTrueName,
                        'companyname': get_company_info(proinf.Company_Id).ShortName,
                        'companymanager': uinfo.UserTrueName,
                        'suctext': suctext,
                        'moretips': moretips,
                        'passreason': cinter.PassReason,
                        'nopassreason': cinter.NoPassReason
                    }
                    push_message.delay(minfo, 'complatecoachtomanager', dic, proinf.pk)
                if cinfo and cinter.Status == 1:
                    suctext = '待定'
                    if cinter.Status == 1:
                        suctext = '适合'
                    elif cinter.Status == 3:
                        suctext = '不合适'
                    time = datetime.datetime.now()
                    if cinter.interviewTime:
                        time = cinter.interviewTime
                    dic = {
                        'probjectname': proinf.Name,
                        'companymanager': uinfo.UserTrueName,
                        'suctext': suctext,
                        'time': time.strftime('%Y-%m-%d %H:%M')
                    }
                    push_message.delay(cinfo, 'complatecoachtocoach', dic, proinf.pk)
        return Response(
            res.raw()
        )

    @action(methods=['post'], detail=False)
    def AddInterViewToStudent(self, request):
        main_data = request.data.get('mainData')
        ProbjectId = int_arg(main_data.get('ProbjectId'), 0)
        Id = int_arg(main_data.get('Id'), 0)
        students = main_data.get('StudentUserIdArr', '')
        StudentStatus = int_arg(main_data.get('StudentStatus'), 0)
        Studentiscomplate = int_arg(main_data.get('Studentiscomplate'), 0)
        if ProbjectId == 0:
            return Response(
                response.Content().error('未知的项目关联').lower_raw()
            )
        arr = students.split(',')
        if len(arr) == 0:
            return Response(
                response.Content().error('请先选择被教练者').lower_raw()
            )
        yttype = int_arg(get_project_config_val(ProbjectId, 'yttype'), 0)
        add_list = []
        for item in arr:
            StudentUserId = int(item)
            if yttype != 1:
                if models.ProjectCoachInterview.objects.filter(StudentUserId=StudentUserId, StudentStatus=1,
                                                               ProbjectId=ProbjectId).exists():
                    return Response(
                        response.Content().error('已经绑定教练了，请先解绑').lower_raw()
                    )

            cinter = models.ProjectCoachInterview.objects.filter(Id=Id).first()
            if cinter:
                cinter.pk = None
                ninfo = cinter
                ninfo.StudentUserId = StudentUserId
                ninfo.StudentStatus = StudentStatus
                ninfo.Studentiscomplate = Studentiscomplate
                ninfo.save()
                add_list.append(ninfo)
                project_coach = models.Projectcoach.objects.filter(ProbjectId=ProbjectId, CoachId=ninfo.CoachId).first()
                if project_coach:
                    project_coach.Status = 2
                    project_coach.save()
                if ninfo.CoachId > 0 and ninfo.StudentUserId > 0:
                    enable_im_and_add_friend([ninfo.CoachId, int_arg(ninfo.StudentUserId, 0)])
                # 如果没有化学面谈，需要在这里绑定教练关系
                mtbjr = int_arg(get_project_config_val(ninfo.ProbjectId, 'mtbjr'), 0)
                if mtbjr == 0:
                    pmod = models.AppProbjectrelation.objects.filter(ProbjectId=ninfo.ProbjectId,
                                                                     UserId=StudentUserId).first()
                    if pmod:
                        pmod.CoachId = ninfo.CoachId
                        pmod.save()
        if len(add_list) > 0:
            return Response(
                response.Content().ok().lower_raw()
            )
        else:
            return Response(
                response.Content().error('指定被教人失败').lower_raw()
            )

    @action(methods=['post'], detail=False)
    def UpdateStudent(self, request):
        main_data = request.data.get('mainData')
        ProbjectId = int_arg(main_data.get('ProbjectId'), 0)
        students = str(main_data.get('StudentUserIdArr', ''))
        if ProbjectId == 0:
            return Response(
                response.Content().error('未知的项目关联').lower_raw()
            )
        yttype = int_arg(get_project_config_val(ProbjectId, 'yttype'), 0)
        arr = students.split(',')
        if len(arr) == 0:
            return Response(
                response.Content().error('请先选择被教练者').lower_raw()
            )
        roleid = models.SysUser.objects.filter(User_Id=request.user.pk).first().Role_Id
        for item in arr:
            StudentUserId = int(item)
            exist = models.ProjectCoachInterview.objects.filter(StudentUserId=StudentUserId, StudentStatus=1,
                                                                ProbjectId=ProbjectId).exists()
            if exist and yttype != 1:
                return Response(
                    response.Content().error('已经绑定教练了，请先解绑').lower_raw()
                )
            main_data['StudentUserId'] = StudentUserId
            res = self.update_row(main_data)
            umod = res.row
            if umod.StudentStatus == 1:
                if not exist:
                    reslist = get_default_help(0)
                    for resource in reslist.all():
                        m = models.AppProbjectlearningaction(Title=resource.Title, ProbjectId=int_arg(umod.ProbjectId),
                                                             User_Id=int_arg(umod.StudentUserId),
                                                             Coach_Id=umod.CoachId, LearningPlan=resource.Content,
                                                             ResId=resource.ResId)
                        m.CreateDate = datetime.datetime.now()
                        m.Creator = request.user.UserName
                        m.save()
                # 清理其他我的待确认的教练
                if yttype == 0:
                    uinfo = models.SysUser.objects.filter(User_Id=int_arg(umod.StudentUserId, 0)).first()
                    cinterlist = models.ProjectCoachInterview.objects.filter(Status=1, StudentUserId=umod.StudentUserId,
                                                                             StudentStatus__in=[0, 2]).exclude(
                        Id=umod.Id)
                    if cinterlist.exists():
                        for c in cinterlist:
                            c.StudentStatus = 3
                            c.StudentPassReason = ""
                            c.StudentNoPassReason = "已确认教练，系统自动取消其他待选"
                            c.Studentiscomplate = 1
                            c.save()
                            if roleid > 3:
                                proinfo = get_project_info(ProbjectId)
                                if proinfo and proinfo.Probject_Id > 0:
                                    cinfox = models.SysUser.objects.filter(User_Id=c.ProbjectId).first()
                                    interviewtime = datetime.datetime.now()
                                    if c.StudentinterviewTime:
                                        interviewtime = c.StudentinterviewTime
                                    dic = {
                                        'probjectname': proinfo.Name,
                                        'interviewtime': interviewtime.strftime('%Y-%m-%d %H:%M'),
                                        'toname': uinfo.UserTrueName
                                    }

                                    push_message.delay(cinfox, 'hxinterviewcancel', dic, proinfo.pk)
                pmod_list = models.AppProbjectrelation.objects.filter(ProbjectId=ProbjectId, UserId=umod.StudentUserId,
                                                                 RolesId=6) | models.AppProbjectrelation.objects.filter(
                    ProbjectId=ProbjectId, Interested_Id=umod.StudentUserId, RolesId=7)
                if pmod_list and not exist:
                    for pmod in pmod_list.all():
                        pmod.CoachId = umod.CoachId
                        pmod.save()
                interested_list = models.AppMemberinterested.objects.filter(ProbjectId=ProbjectId,
                                                                            MasterMember_Id=umod.StudentUserId)
                if interested_list.exists():
                    for interested_item in interested_list.all():
                        interested_item.CoachId = umod.CoachId
                        interested_item.save()
                # 建立匹配关系
                if umod.CoachId > 0 and umod.StudentUserId > 0:
                    enable_im_and_add_friend([umod.CoachId, int_arg(umod.StudentUserId, 0)])
            if roleid <= 3:
                continue
            # 消息推送 用户 /教练/ 企业管理员 /项目负责人
            cinter = res.row
            cinfo = models.SysUser.objects.filter(User_Id=cinter.CoachId).first()
            sinfo = models.SysUser.objects.filter(User_Id=cinter.StudentUserId).first()
            minfo = models.SysUser.objects.filter(User_Id=cinter.UserId).first()
            probjectid = int_arg(cinter.ProbjectId)
            proinf = get_project_info(probjectid)
            if proinf and proinf.Relation_UserId > 0:
                uinfo = models.SysUser.objects.filter(User_Id=proinf.Relation_UserId).first()
                if uinfo:
                    suctext = '待定'
                    if cinter.StudentStatus == 1:
                        suctext = '适合'
                    elif cinter.StudentStatus == 3:
                        suctext = '不合适'
                    dic = {
                        'probjectname': proinf.Name,
                        'coachname': cinfo.UserTrueName,
                        'studentname': sinfo.UserTrueName,
                        'companyname': get_company_info(proinf.Company_Id).ShortName,
                        'companymanager': minfo.UserTrueName,
                        'suctext': suctext,
                        'passreason': cinter.StudentPassReason,
                        'nopassreason': cinter.StudentNoPassReason
                    }
                    push_message.delay(uinfo, 'sucesscoachtomanager', dic, proinf.pk)
                    if cinter.StudentStatus == 1:
                        push_message.delay(cinfo, 'sucesscoachtocoach', dic, proinf.pk)
                    mtbjr = int_arg(get_project_config_val(probjectid, 'mtbjr'), None)
                    if mtbjr == 1:
                        sinfox = models.SysUser.objects.filter(User_Id=sinfo.User_Id).first()
                        dic["password"] = aesdecrypt(sinfox.UserPwd)
                        push_message.delay(sinfo, 'sucesscoach', dic, proinf.pk)
        return Response(
            response.Content().ok().lower_raw()
        )

    @action(methods=['post'], detail=False)
    def getpagedatafront(self, request):
        studenthxmt = False
        if request.data["wheres"]:
            for where in json.loads(request.data["wheres"]):
                if where['name'] == 'studenthxmt':
                    studenthxmt = True
                else:
                    add_option(request.data, where['name'], where['value'], where['displayType'])
        project_coach_interview_list = self.get_page_rows(request.data)
        extra = []
        if project_coach_interview_list.count() > 0:
            uid = request.user.pk
            tmprow = [item for item in project_coach_interview_list.all() if
                      item.StudentUserId == uid and item.StudentStatus == 1]
            result = list(project_coach_interview_list)
            if len(tmprow) > 0:
                result = tmprow
            for item in result:
                dic = {}
                dic["eId"] = item.Id
                uinfo = None
                if studenthxmt:
                    uinfo = get_member_info(item.StudentUserId)
                else:
                    uinfo = get_member_info(item.UserId)
                if uinfo:
                    dic["photograph"] = uinfo.Photograph
                    dic["userName"] = uinfo.TrueName
                    dic["department"] = uinfo.Department
                    dic["duty"] = uinfo.Duty
                    dic["companyName"] = uinfo.CompanyName
                    dic["messageid"] = get_im_id(int_arg(uinfo.User_Id, 0))
                comodel = get_coach_info(item.CoachId)
                dic["img"] = comodel.Photograph
                dic["truename"] = comodel.TrueName
                dic["coachlevel"] = get_dictionary_list_value("coachlevel", comodel.CoachLevel)
                dic["jlzz"] = get_dictionary_list_value("jlzz", comodel.jlzz)
                dic["jlzw"] = get_dictionary_list_value("jlzw", comodel.jlzw)
                dic["coachmessageid"] = get_im_id(item.CoachId)
                if item.interviewId and item.interviewId > 0:
                    coachsh = models.AppCoachschedule.objects.filter(ScheduleId=item.interviewId).first()
                    if coachsh:
                        dic['interviewTime'] = coachsh.Starttime.strftime(
                            '%Y-%m-%d %H:%M') + ' -' + coachsh.EndTime.strftime('%H:%M')
                extra.append(dic)
            project_coach_interview_list.extra_list = extra
        return Response(self.page_data(project_coach_interview_list))

    @action(methods=['post'], detail=False)
    def getpagedata(self, request):
        t = self.get_page_rows(request.data)
        if t.count() > 0:
            extra = []
            for item in t.all():
                dic = {}
                dic["eId"] = item.Id
                uinfo = get_member_info(item.UserId)
                if uinfo:
                    dic["photograph"] = uinfo.Photograph
                    dic["userName"] = uinfo.TrueName
                    dic["department"] = uinfo.Department
                    dic["duty"] = uinfo.Duty
                    dic["companyName"] = uinfo.CompanyName
                    dic["messageid"] = get_im_id(int_arg(uinfo.User_Id, 0))
                comodel = get_coach_info(item.CoachId)
                dic["img"] = comodel.Photograph
                dic["truename"] = comodel.TrueName
                dic["coachlevel"] = get_dictionary_list_value("coachlevel", comodel.CoachLevel)
                dic["jlzz"] = get_dictionary_list_value("jlzz", comodel.jlzz)
                dic["jlzw"] = get_dictionary_list_value("jlzw", comodel.jlzw)
                dic["coachmessageid"] = get_im_id(item.CoachId)
                extra.append(dic)
            t.extra_list = extra
        return Response(
            self.page_data(t)
        )

    @action(methods=['get'], detail=False)
    def GetCoachListByProbject(self, request):
        uid = request.user.pk
        project_id = int_arg(request.query_params.get('probjectid'), None)
        project_coach_interview_list = models.ProjectCoachInterview.objects.filter(ProbjectId=project_id, UserId=uid,
                                                                                   iscomplate=1)
        project_coach_interview_list = distinct(project_coach_interview_list, ['CoachId'])
        result_list = []
        for item in project_coach_interview_list.all():
            dic = self.serializer_class(item, many=False).data
            uinfo = get_member_info(item.UserId)
            if uinfo:
                dic["photograph"] = uinfo.Photograph
                dic["userName"] = uinfo.TrueName
                dic["department"] = uinfo.Department
                dic["duty"] = uinfo.Duty
                dic["companyName"] = uinfo.CompanyName
                dic["messageid"] = get_im_id(int_arg(uinfo.User_Id, 0))
            comodel = get_coach_info(item.CoachId)
            dic["img"] = comodel.Photograph
            dic["truename"] = comodel.TrueName
            dic["coachlevel"] = get_dictionary_list_value("coachlevel", comodel.CoachLevel)
            dic["jlzz"] = get_dictionary_list_value("jlzz", comodel.jlzz)
            dic["jlzw"] = get_dictionary_list_value("jlzw", comodel.jlzw)
            dic["coachmessageid"] = get_im_id(item.CoachId)
            if item.interviewId and item.interviewId > 0:
                coachsh = models.AppCoachschedule.objects.filter(ScheduleId=item.interviewId).first()
                if coachsh:
                    dic['interviewTime'] = coachsh.Starttime.strftime(
                        '%Y-%m-%d %H:%M') + ' -' + coachsh.EndTime.strftime('%H:%M')
            result_list.append(dic)
        res = response.Content()
        res.data = result_list
        return Response(
            res.ok('SUC').raw()
        )

    @action(methods=['get'], detail=False)
    def GetInterViewCoachToStudent(self, request):
        uid = request.user.pk
        probjectid = int_arg(request.query_params.get('probjectid'), None)
        mtbjr = int_arg(get_project_config_val(probjectid, 'mtbjr'))
        if mtbjr != 1:
            # Do not dispatch coach if chemistry interview disable
            return Response(
                response.Content().ok().raw()
            )
        # //如有成功的，不在匹配
        #             // 排除 已选择教练 ，排除已负荷的教练，随机教练 20200626 修改为不随机，以一次分配为准
        #             // 次数已完的 默认显示待定的，不在匹配
        #             //被教者匹配教练数量 被教者匹配教练次数
        jlppsl = int_arg(get_project_config_val(probjectid, 'jlppsl'), 0)
        jlppcs = int_arg(get_project_config_val(probjectid, 'jlppcs'), 0)
        jlcount = models.Projectcoach.objects.filter(ProbjectId=probjectid, Status=2).count()
        bjrcount = models.AppProbjectrelation.objects.filter(ProbjectId=probjectid, RolesId=6).count()
        if jlcount == 0:
            return Response(
                response.Content().error('没有等待企业面试教练').raw()
            )
        ppsl = bjrcount * jlppsl
        pjlpcs = 0
        prinfo = models.AppProbjectrelation.objects.filter(ProbjectId=probjectid, UserId=uid).first()
        if prinfo and prinfo.jlppcs:
            pjlpcs += prinfo.jlppcs
        # 多教练项目
        project_coach_interview_list = models.ProjectCoachInterview.objects.filter(ProbjectId=probjectid,
                                                                                   StudentUserId=uid, StudentStatus=1)

        yttype = int_arg(get_project_config_val(probjectid, 'yttype'), 0)
        if yttype != 1:
            # 已经匹配的不再分配
            if project_coach_interview_list.count() > 0:
                return write_interview_coach_to_student(project_coach_interview_list[:])
        info_list = []
        waitcoachs = models.ProjectCoachInterview.objects.filter(ProbjectId=probjectid, StudentUserId=uid,
                                                                 StudentStatus__in=[0, 2])

        if waitcoachs:
            for waititem in waitcoachs.all():
                info_list.append(waititem)

        if waitcoachs.count() < jlppsl:
            coachids = models.Projectcoach.objects.filter(ProbjectId=probjectid, Status=2).order_by('MatchingTimes')
            i = waitcoachs.count()
            for item in coachids.all():
                if i >= jlppsl:
                    break
                cinter = models.ProjectCoachInterview.objects.filter(ProbjectId=probjectid, Status=1,
                                                                     CoachId=item.CoachId).first()
                if cinter:
                    cinter.pk = None
                    ninfo = cinter
                    ninfo.StudentUserId = uid
                    ninfo.StudentStatus = 0
                    ninfo.Studentiscomplate = 0
                    ninfo.save()
                    info_list.append(ninfo)
                    i += 1
                    item.MatchingTimes = int_arg(item.MatchingTimes, 0) + 1
                    item.save()

        return write_interview_coach_to_student(info_list)

    @action(methods=['get'], detail=False)
    def GetWaitInterViewCoachByUser(self, request):
        probjectid = int_arg(request.query_params.get('probjectid'), None)
        all = int_arg(request.query_params.get('all'), 0)
        coachid = int_arg(request.query_params.get('coachid'), 0)
        userids = []
        yttype = int_arg(get_project_config_val(probjectid, 'yttype'), 0)
        if yttype == 1:
            userids = models.AppProbjectrelation.objects.filter(ProbjectId=probjectid, RolesId=6).values_list('UserId',
                                                                                                              flat=True)
        else:
            userids = models.AppProbjectrelation.objects.filter(ProbjectId=probjectid, CoachId=0,
                                                                RolesId=6).values_list('UserId', flat=True)
        if all == 1:
            userids = models.AppProbjectrelation.objects.filter(ProbjectId=probjectid, RolesId=6).values_list('UserId',
                                                                                                              flat=True)
        userids = list(userids)
        if coachid > 0:
            douserids = models.ProjectCoachInterview.objects.filter(ProbjectId=probjectid, CoachId=coachid,
                                                                    Status=1).values_list('StudentUserId', flat=True)
            if len(douserids) > 0:
                for uid in douserids:
                    if uid in userids:
                        userids.remove(uid)
        if userids and len(userids) > 0:
            userinfos = models.AppMember.objects.filter(User_Id__in=userids).values('User_Id', 'TrueName', 'Department',
                                                                                    'Duty', 'email')
            res = response.Content()
            res.data = userinfos
            return Response(
                res.ok('SUC').raw()
            )
        return Response(
            response.Content().error().raw()
        )

    @action(methods=['get'], detail=False)
    def GetCoachIntetView(self, request):
        data_list = []
        probjectid = int_arg(request.query_params.get('probjectid'), None)
        status = int_arg(request.query_params.get('status'), 0)
        utype = int_arg(request.query_params.get('utype'), 0)
        coachids = models.ProjectCoachInterview.objects.filter(ProbjectId=probjectid)
        if utype == 0:  # 企业面试
            if status > 0:
                coachids = coachids.filter(iscomplate=1)
            else:
                coachids = coachids.filter(iscomplate=0)
            uids = models.AppProbjectrelation.objects.filter(ProbjectId=probjectid, IsManger=1).values_list('UserId')
            coachids = distinct(coachids.filter(UserId__in=uids), ['CoachId'])
        # 化学面试
        else:
            if status > 0:
                # StudentStatus 0: 未评价 1: 通过 2: 待定 3:不合适
                coachids = coachids.filter(Status=1, StudentStatus=1)
            else:
                coachids = coachids.filter(Status=1, StudentStatus__in=[0, 2])
            uids = models.AppProbjectrelation.objects.filter(ProbjectId=probjectid, IsManger=0).values_list('UserId')
            coachids = coachids.filter(StudentUserId__in=uids)
            # display all chemistry interview
            # coachids = distinct(coachids.filter(StudentUserId__in=uids), ['CoachId', 'StudentUserId'])
        for item in coachids.all():
            coachinfo = models.AppCoach.objects.filter(IsOpen=1, User_Id=item.CoachId).first()
            if coachinfo and coachinfo.CoachId > 0:
                studentusername = None
                if item.StudentUserId and item.StudentUserId > 0:
                    studentusername = models.SysUser.objects.filter(User_Id=item.StudentUserId).first().UserTrueName
                dic = {}
                dic["Id"] = item.Id
                dic["UserId"] = coachinfo.User_Id
                dic["img"] = coachinfo.Photograph
                dic["truename"] = coachinfo.TrueName
                dic["coachlevel"] = get_dictionary_list_value("coachlevel", coachinfo.CoachLevel)
                dic["jlzz"] = get_dictionary_list_value("jlzz", coachinfo.jlzz)
                dic["jlzw"] = get_dictionary_list_value("jlzw", coachinfo.jlzw)
                dic["ProbjectId"] = probjectid
                dic["ProName"] = get_project_info(probjectid).Name
                dic["lastlogin"] = models.SysUser.objects.filter(User_Id=coachinfo.User_Id).first().LastLoginDate
                dic["status"] = item.Status
                dic["interviewTime"] = item.interviewTime
                dic["PassReason"] = item.PassReason
                dic["NoPassReason"] = item.NoPassReason
                dic["StudentUserId"] = item.StudentUserId
                dic["StudentUserName"] = studentusername
                dic["Studentstatus"] = item.StudentStatus
                dic["StudentinterviewTime"] = item.StudentinterviewTime
                dic["StudentPassReason"] = item.StudentPassReason
                dic["StudentNoPassReason"] = item.StudentNoPassReason
                dic["qypici"] = item.qypici
                dic["hxpici"] = item.hxpici
                data_list.append(dic)
        mtbjr = int_arg(get_project_config_val(probjectid, 'mtbjr'), None)
        res = response.Content()
        res.data = {
            'data': data_list,
            'mtbjr': mtbjr
        }
        return Response(
            res.ok('SUC').raw()
        )

    @action(methods=['get'], detail=False)
    def AutoCoachToProbject(self, request):
        probjectid = int_arg(request.query_params.get('probjectid'), None)
        coachid = int_arg(request.query_params.get('coachid'), None)
        userid = int_arg(request.query_params.get('userid'), None)
        mtbjr = int_arg(get_project_config_val(probjectid, 'mtbjr'), None)
        if mtbjr == 1:
            return Response(
                response.Content().error('请先关闭化学面谈后再设置')
            )
        pinterview = models.ProjectCoachInterview.objects.filter(ProbjectId=probjectid, CoachId=coachid,
                                                                 Status=1).first()
        if pinterview and userid > 0:
            pinterview.StudentStatus = 1
            pinterview.StudentPassReason = "系统自动通过"
            pinterview.StudentNoPassReason = ""
            pinterview.Studentiscomplate = 1
            pinterview.StudentUserId = userid
            pinterview.StudentinterviewTime = datetime.datetime.now()
            pinterview.save()
        return Response(
            response.Content().ok('SUC').raw()
        )

    @action(methods=['get'], detail=False)
    def TxCompanyMatchingCoach(self, request):
        probjectid = int_arg(request.query_params.get('probjectid'), None)
        relation = models.AppProbjectrelation.objects.filter(ProbjectId=probjectid, IsManger=1)
        query = relation.values_list('UserId', flat=True)
        if len(query) > 0:
            prinfo = get_project_info(probjectid)
            for item in query:
                uinfo = models.SysUser.objects.filter(User_Id=item).first()
                if uinfo:
                    dic = {
                        'probjectname': prinfo.Name,
                        'probjectid': prinfo.Probject_Id
                    }
                    dic['url'] = settings.SITE_URL + 'cadmin/probject_info?probjectid=' + str(prinfo.Probject_Id)
                    push_message.delay(uinfo, 'matchingcoach', dic, prinfo.pk)
        return Response(
            response.Content().ok().raw()
        )

    @action(methods=['get'], detail=False)
    def TxManageIntendedCoach(self, request):
        probjectid = int_arg(request.query_params.get('probjectid'), None)
        coachid = int_arg(request.query_params.get('coachid'), None)
        proinfo = get_project_info(probjectid)
        cinfo = models.SysUser.objects.filter(User_Id=coachid).first()
        if proinfo and proinfo.Relation_UserId > 0:
            uinfo = models.SysUser.objects.filter(User_Id=proinfo.Relation_UserId).first()
            dic = {
                'probjectname': proinfo.Name,
                'companyname': get_company_info(proinfo.Company_Id).ShortName,
                'companymanager': request.user.first_name,
                'coachname': cinfo.UserTrueName
            }
            push_message.delay(uinfo, 'intendedcoach', dic, proinfo.pk)
            return Response(
                response.Content().ok().raw()
            )
        else:
            return Response(
                response.Content().error('项目不存在或项目负责人未设置').raw()
            )

    # 获取所有教练
    @action(methods=['get'], detail=False)
    def GetCoachListByProbjectUser(self, request):
        probjectid = int_arg(request.query_params.get('probjectid'), None)
        userid = int_arg(request.query_params.get('userid'), None)
        coachid = int_arg(request.query_params.get('coachid'), 0)
        uid = request.user.pk
        if userid and userid > 0:
            uid = userid
        _list = models.ProjectCoachInterview.objects.filter(ProbjectId=probjectid, StudentUserId=uid, iscomplate=1,
                                                            StudentStatus=1)
        if coachid > 0:
            _list = _list.exclude(CoachId=coachid)
        query = distinct(_list, ['CoachId'])
        data = []
        for item in query.all():
            dic = self.serializer_class(item, many=False).data
            comodel = get_coach_info(item.CoachId)
            if comodel:
                dic["img"] = comodel.Photograph
                dic["truename"] = comodel.TrueName
                dic["coachlevel"] = get_dictionary_list_value("coachlevel", comodel.CoachLevel)
                dic["jlzz"] = get_dictionary_list_value("jlzz", comodel.jlzz)
                dic["jlzw"] = get_dictionary_list_value("jlzw", comodel.jlzw)
                dic["coachmessageid"] = get_im_id(item.CoachId)
            data.append(dic)
        return Response(
            response.Content().ok('SUC', data).raw()
        )
