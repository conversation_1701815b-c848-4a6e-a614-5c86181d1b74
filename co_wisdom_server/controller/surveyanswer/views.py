import datetime
import json

from rest_framework.decorators import action
from rest_framework.response import Response

from controller.project.actions import get_project_info
from controller.survey.actions import get_survey_info, get_survey_view
from controller.surveysubject.actions import get_survey_subject_detail
from data import models, serializers, extension
from utils import response, int_arg
from utils.business import new_pici
from utils.model import add_option
from utils.user import get_user_ids


class SurveyAnswerViewSet(extension.ResponseViewSet):
    queryset = models.AppSurveyanswers.objects.all()
    serializer_class = serializers.AppSurveyanswersSerializer

    @action(methods=['post'], detail=False)
    def surveysubmit(self, request):
        main_data = request.data.get('mainData')
        surveyid = int_arg(main_data.get('surveyid'))
        if surveyid == 0:
            return Response(
                response.Content().error('非法的调查提交').raw()
            )
        evalitem = request.data.get('Extra')
        evachild = get_survey_subject_detail(surveyid)
        if len(evalitem) != len(evachild):
            return Response(
                response.Content().error('请全部填写完整在提交').raw()
            )
        userid = request.user.pk
        probjectId = int_arg(main_data.get('probjectId'))
        pici = new_pici()
        for index, item in enumerate(evalitem):
            mo = models.AppSurveyanswers()
            mo.UserId = userid
            mo.App_SurveyId = surveyid
            mo.ProbjectId = probjectId
            mo.SubjectId = int_arg(item.get("name"))
            mo.Answers = item.get("answer", "")
            mo.Ip = item.get("input", "")
            mo.CreateTime = datetime.datetime.now()
            mo.TestId = index
            mo.pici = pici
            mo.save()
        return Response(
            response.Content().ok().raw()
        )

    @action(methods=['post'], detail=False)
    def getpagedata(self, request):
        for where in json.loads(request.data["wheres"]):
            if where['name'] == 'UserName':
                ids = get_user_ids(where['value'])
                if len(ids) > 0:
                    add_option(request.data, 'UserId', ids, 'checkbox')
            elif where['name'] == 'SurveyName':
                ids = models.AppSurvey.objects.filter(Enable=1, Title__icontains=where['value']).values_list(
                    'SurveyId', flat=True)
                if len(ids) > 0:
                    add_option(request.data, 'App_SurveyId', ids, 'checkbox')
            elif where['name'] == 'ProName':
                ids = models.AppProbject.objects.filter(Enable=1, Name__icontains=where['value']).values_list(
                    'Probject_Id', flat=True)
                if len(ids) > 0:
                    add_option(request.data, 'ProbjectId', ids, 'checkbox')
            else:
                add_option(request.data, where['name'], where['value'], where['displayType'])
        t = self.get_page_rows(request.data)
        if t.count() > 0:
            extra = []
            for item in t.all():
                dic = {}
                dic["eId"] = item.Id
                dic["SurveyName"] = get_survey_info(item.App_SurveyId).Title
                dic["ProName"] = get_project_info(item.ProbjectId).Name
                username = models.SysUser.objects.filter(User_Id=item.UserId).first().UserName
                dic["UserName"] = username
                extra.append(dic)
            t.extra_list = extra
        return Response(
            self.page_data(t)
        )

    @action(methods=['get'], detail=False)
    def getsurveyview(self, request):
        probjectid = int_arg(request.query_params.get('probjectid'))
        pici = request.query_params.get('pici')
        answer = models.AppSurveyanswers.objects.filter(ProbjectId=probjectid, pici=pici).first()
        result = []
        if answer:
            res = get_survey_view(answer.App_SurveyId, answer.UserId, pici)
            return Response(
                res.raw()
            )
        return Response(
            response.Content().error().raw()
        )
