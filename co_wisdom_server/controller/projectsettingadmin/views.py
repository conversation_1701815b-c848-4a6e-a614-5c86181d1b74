import datetime

from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.viewsets import ModelViewSet, ViewSet, GenericViewSet

from controller.projectsettingadmin.actions import get_project_setting_admin_all
from controller.projectsettingcoach.actions import get_project_setting_all, get_project_config_val
from data import models, serializers, extension
from data.user.models import User
from utils import response, blank_to_none, int_arg, value_or_default, aesencrypt, randomPassword, float_arg, \
    null_or_blank
import json
from django.db.models import Avg, Count, Min, Sum






class ProjectSettingAdminViewSet(extension.ResponseViewSet):
    queryset = models.AppProjectSettingAdmin.objects.all()
    serializer_class = serializers.AppProjectSettingAdminSerializer

    @action(methods=['get'], detail=False)
    def projectsetting(self, request):
        pid = int_arg(request.query_params.get('projectid'), None)
        d = get_project_setting_admin_all(pid)
        res = response.Content()
        res.data = d
        return Response(
            res.ok('SUC').raw()
        )


    @action(methods=['post'], detail=False)
    def add(self, request):
        main_data = request.data.get('mainData')
        projectid = int_arg(main_data.get('projectid'), 0)
        if projectid == 0:
            return Response(
                response.Content().error('关联项目不能空').raw()
            )
        for key, values in main_data.items():
            if key == 'probjectid':
                continue
            ino = models.AppProjectSettingAdmin()
            ino.pid = projectid
            ino.KeyName = key
            ino.KeyValue = str(values)
            ino.save()
        return Response(
            response.Content().ok('新增成功').lower_raw()
        )

    @action(methods=['post'], detail=False)
    def UpdateFront(self, request):
        main_data = request.data.get('mainData')
        projectid = int_arg(main_data.get('projectid'), 0)
        if projectid == 0:
            return Response(
                response.Content().error('关联项目不能空').raw()
            )
        for key, values in main_data.items():
            if key == 'projectid':
                continue

            sinfo = models.AppProjectSettingAdmin.objects.filter(pid=projectid, KeyName=key).first()
            if not sinfo:
                ino = models.AppProjectSettingAdmin(CreateDate=datetime.datetime.now())
                ino.pid = projectid
                ino.KeyName = key
                ino.KeyValue = str(values)
                ino.save()
            else:
                sinfo.KeyValue = str(values)
                sinfo.save()
        return Response(
            response.Content().ok('修改成功').lower_raw()
        )