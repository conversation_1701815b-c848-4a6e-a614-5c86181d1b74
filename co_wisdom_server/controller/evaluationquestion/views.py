from rest_framework.response import Response

from rest_framework.decorators import action
from data import models, serializers, extension
from utils import int_arg


class EvaluationQuestionViewSet(extension.ResponseViewSet):
    queryset = models.AppEvaluationquestion.objects.all()
    serializer_class = serializers.AppEvaluationquestionSerializer

    @action(methods=['post'], detail=False)
    def add(self, request):
        main_data = request.data.get('mainData')
        res = self.add_row(main_data)
        return Response(
            res.raw()
        )

    @action(methods=['post'], detail=False)
    def getDetailPage(self, request):
        value = request.data.get('value')
        order = request.data.get('order')
        page = int_arg(request.data.get('page'))
        rows = int_arg(request.data.get('rows'))
        res = models.AppEvaluationquestion.objects.filter(EvaluationId=value)
        start = (page - 1) * rows
        end = page * rows
        new_res = res
        total = len(new_res)
        if len(res) > start:
            new_res = res[start:end]
        if len(new_res) > 0:
            total = len(new_res)
        return Response(
            {
                "status": 0,
                'msg': None,
                "rows": serializers.AppEvaluationquestionSerializer(new_res, many=True).data,
                "extra": None,
                "total": total,
                'summary': None
            }
        )
