import datetime

from controller.coach.actions import get_coach_info
from controller.member.actions import get_member_info
from data import models
from utils import response


def get_evaluation_detail(evaluationid):
    res_list = models.AppEvaluationquestion.objects.filter(EvaluationId=evaluationid).order_by('Sort')
    return res_list

def get_evaluation_question(eqid):
    res_list = models.AppEvaluationquestion.objects.filter(EQid=eqid).order_by('Sort').first()
    if res_list:
        return res_list
    return None