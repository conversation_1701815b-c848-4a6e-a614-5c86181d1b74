import os

from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.viewsets import ModelViewSet, ViewSet, GenericViewSet

from django.conf import settings
from controller.project.actions import get_start_project_num_by_company, get_complete_project_num_by_company, \
    get_sign_project_num_by_company, get_project_info
from data import models, serializers, extension
from utils import response, blank_to_none, null_or_blank, int_arg
import json

from utils.model import add_option
from utils.user import get_user_ids


class ProjectUploadReportViewSet(extension.ResponseViewSet):
    queryset = models.AppProbjectuploadreport.objects.all()
    serializer_class = serializers.AppProbjectuploadreportSerializer

    @action(methods=['post'], detail=False)
    def getpagedata(self, request):
        for where in json.loads(request.data["wheres"]):
            if where['name'] == 'UserName':
                ids = get_user_ids(where['value'])
                if len(ids) > 0:
                    add_option(request.data, 'UserId', ids, 'checkbox')
            elif where['name'] == 'ProName':
                ids = models.AppProbject.objects.filter(Enable=1, Name__icontains=where['value']).values_list(
                    'Probject_Id', flat=True)
                if len(ids) > 0:
                    add_option(request.data, 'ProbjectId', ids, 'checkbox')
            else:
                add_option(request.data, where['name'], where['value'], where['displayType'])
        t = self.get_page_rows(request.data)
        if t.count() > 0:
            extra = []
            for item in t.all():
                dic = {}
                dic["eId"] = item.Id
                dic["ProName"] = get_project_info(item.ProbjectId).Name
                user = models.SysUser.objects.filter(User_Id=item.UserId).first()
                dic["UserName"] = user.UserName
                dic['UserRole'] = user.RoleName
                extra.append(dic)
            t.extra_list = extra
        return Response(
            self.page_data(t)
        )

    @action(methods=['post'], detail=False)
    def upload(self, request):
        file_obj = request.FILES.get('fileInput')
        res = self.upload_file(file_obj, ['.pdf', '.doc', '.docx', '.ppt', '.pptx'])
        return Response(
            res.raw()
        )

    @action(methods=['post'], detail=False)
    def add(self, request):
        main_data = request.data.get('mainData')
        files = main_data.get('files', '')
        if null_or_blank(files):
            return Response(
                response.Content().error('请选择上传测评/报告文件').raw()
            )
        arr = files.split(',')
        file_list = []
        for item in arr:
            filename = item.split(',')[0]
            fileinfo = models.SysUpfile.objects.filter(FilePath=item).first()
            if fileinfo:
                filename = fileinfo.FileName.split('.')[0]
                main_data['FileName'] = filename
                main_data['FilePath'] = item
                file_list.append(main_data)
        for item in file_list:
            self.add_row(item)
        return Response(
            response.Content().ok().raw()
        )

    @action(methods=['post'], detail=False)
    def DelFile(self, request):
        main_data = request.data.get('mainData')
        id = int_arg(request.query_params.get('id'), None)
        cinfo = models.AppProbjectuploadreport.objects.filter(Id=id).first()
        if not cinfo:
            return Response(
                response.Content().error('非法的文件删除').raw()
            )
        cinfo.delete()
        filepath = os.path.join(settings.BASE_DIR, cinfo.FilePath)
        os.remove(filepath)
        return Response(
            response.Content().ok().raw()
        )



