import datetime
import json
from drf_yasg.utils import swagger_auto_schema
from drf_yasg import openapi

from django.conf import settings
from rest_framework.decorators import action
from rest_framework.response import Response

from controller.company.actions import get_company_info
from controller.ildp.actions import get_my_project_objectives
from controller.member.actions import get_project_member_numbers, get_work_time, get_member_info, \
    add_or_update_member_interested, get_work_year, get_re_template_val
from controller.project.actions import get_project_info, get_project_def_coach_by_user, get_project_info_by_user
from controller.projectrelation.actions import relation_filter_by_interest
from controller.projectsettings.actions import get_project_config_val
from controller.user.actions import get_children_name, update_django_user_pwd, add_sys_user
from data import models, serializers, extension
from utils import response, int_arg, aesencrypt, randomPassword, float_arg, \
    null_or_blank
from utils.easemob.common import enable_im
from utils.messagecenter.center import push_message
from utils.model import add_option
from utils.messagecenter.actions import get_newmember_has_link, get_newmember_no_link


class AppMemberViewSet(extension.ResponseViewSet):
    queryset = models.AppMember.objects.all()
    serializer_class = serializers.AppMemberSerializer
    # 项目管理员创建
    @action(methods=['post'], detail=False)
    def add(self, request):
        main_data = request.data.get('mainData')
        TrueName = main_data.get('TrueName', '')
        mobile = main_data.get('mobile', '')
        email = main_data.get('email', '')
        manage_role = main_data.get('manage_role', '')
        password = main_data.get('password', '')
        UserName = main_data.get('UserName', '').strip()
        Interested_Id = int_arg(main_data.get('Interested_Id'), 0)
        roleid = int_arg(main_data.get('Role_Id'), 0)
        userid = int_arg(main_data.get('userid'), 0)
        relation = main_data.get('Relation', '')

        if roleid < 4:
            return Response(
                response.Content().error('请选择会员组').lower_raw()
            )
        ProbjectId = int_arg(main_data.get('ProbjectId'), 0)
        if ProbjectId <= 0:
            return Response(
                response.Content().error('关联的项目不能空').lower_raw()
            )

        pinfo = get_project_info(ProbjectId)

        Company_Id = int_arg(main_data.get('Company_Id'), 0)
        if Company_Id == 0:
            Company_Id = pinfo.Company_Id
            main_data['Company_Id'] = Company_Id
        if Company_Id > 0:
            comx = get_company_info(Company_Id)
            main_data['CompanyName'] = comx.CompanyName

        if roleid == 7:
            if not relation:
                return Response(
                    response.Content().error('利益相关者与被教练者关系未填写').lower_raw()
                )
            if userid > 0 and Interested_Id > 0:
                if userid == Interested_Id:
                    return Response(
                        response.Content().error('不能设置自己为利益相关者').lower_raw()
                    )
                # Add stakeholder with coachee in the same project
                duplicate = models.AppProbjectrelation.objects.filter(UserId=userid,
                                                                      Interested_Id=str(Interested_Id)).exists()
                if duplicate:
                    return Response(
                        response.Content().error('该用户已经被添加为利益相关者').lower_raw()
                    )

                coachid = get_project_def_coach_by_user(Interested_Id)

                prelation = models.AppProbjectrelation()
                if ProbjectId > 0 and Company_Id > 0:
                    prelation.CompanyId = Company_Id
                    prelation.ProbjectId = ProbjectId
                    prelation.UserId = userid
                    prelation.CoachId = coachid
                    prelation.IsManger = 0
                    prelation.RolesId = roleid
                    prelation.Interested_Id = str(Interested_Id)
                    try:
                        prelation.save()
                    except Exception as e:
                        return Response(
                            response.Content().error('创建项目关系失败').lower_raw()
                        )
                enable = int_arg(main_data.get('Enable'), 0)
                Concertyears = int_arg(main_data.get('Concertyears'), 0)
                add_or_update_member_interested(Interested_Id, userid, ProbjectId, enable, coachid,
                                                relation, Concertyears)
                return Response(
                    response.Content().ok().raw()
                )

        if UserName == '':
            return Response(
                response.Content().error('登录名不能空').lower_raw()
            )
        else:
            cfusername = models.SysUser.objects.filter(UserName=UserName).first()
            if cfusername:
                return Response(
                    response.Content().error('该登陆名已经存在').lower_raw()
                )
        if TrueName == '' or mobile == '' or email == '':
            return Response(
                response.Content().error('姓名/手机号/邮箱不能空').lower_raw()
            )

        if password == '':
            # 获取项目默认密码
            defpwd = get_project_config_val(ProbjectId, 'defpwd')
            if not defpwd:
                defpwd = randomPassword(6)
            password = aesencrypt(defpwd)
            main_data['password'] = password

        numbers = pinfo.Number
        if numbers > 0 and get_project_member_numbers(pinfo.Probject_Id) >= numbers and roleid == 6:
            return Response(
                response.Content().error('项目账号人数超限制，请与项目负责人联系').lower_raw()
            )

        WorkingDateNum = float_arg(main_data.get('WorkingDateNum'), 0)
        CompanyWorkingDateNum = float_arg(main_data.get('CompanyWorkingDateNum'), 0)
        GwWorkingDateNum = float_arg(main_data.get('GwWorkingDateNum'), 0)
        Concertyears = int_arg(main_data.get('Concertyears'), 0)
        if WorkingDateNum > 0:
            main_data['WorkingDate'] = get_work_time(WorkingDateNum)
        if CompanyWorkingDateNum > 0:
            main_data['CompanyWorkingDate'] = get_work_time(CompanyWorkingDateNum)
        if GwWorkingDateNum > 0:
            main_data['GwWorkingDate'] = get_work_time(GwWorkingDateNum)
        Photograph = main_data.get('Photograph', '')
        if Photograph and type(Photograph) is list and 'path' in Photograph[0]:
            main_data['Photograph'] = Photograph[0].get('path')

        userModel = models.SysUser()
        userModel.Email = email
        # userModel.manage_role = manage_role
        userModel.PhoneNo = mobile
        userModel.UserPwd = password
        userModel.UserName = UserName
        userModel.UserTrueName = TrueName
        userModel.Role_Id = roleid
        userModel.RoleName = get_children_name(roleid)
        userModel.CreateDate = datetime.datetime.now().replace(microsecond=0)
        userModel.ModifyDate = datetime.datetime.now().replace(microsecond=0)
        userModel.Creator = request.user.UserName
        userModel.Enable = int_arg(main_data.get('Enable'), 0)
        if Photograph != '':
            userModel.HeadImageUrl = Photograph
        try:
            userModel.save()
        except Exception as e:
            return Response(
                response.Content().error('创建用户失败').lower_raw()
            )
        add_sys_user(userModel)
        enable_im(userModel.User_Id)
        main_data['User_Id'] = userModel.User_Id
        res = self.add_row(main_data)
        if res.status:
            prelation = models.AppProbjectrelation()
            if ProbjectId > 0 and Company_Id > 0:
                prelation.CompanyId = Company_Id
                prelation.ProbjectId = ProbjectId
                prelation.UserId = userModel.User_Id
                prelation.CoachId = 0
                is_manager = 0
                if userModel.Role_Id == 5:
                    is_manager = 1
                prelation.IsManger = is_manager
                prelation.RolesId = userModel.Role_Id
                prelation.Interested_Id = str(Interested_Id)
                try:
                    prelation.save()
                except Exception as e:
                    return Response(
                        response.Content().error('创建项目关系失败').lower_raw()
                    )
            if Interested_Id > 0:
                relation = main_data.get('Relation', '')
                coachidx = get_project_def_coach_by_user(Interested_Id)
                enable = int_arg(main_data.get('Enable'), 0)
                add_or_update_member_interested(Interested_Id, userModel.User_Id, ProbjectId, enable, coachidx,
                                                relation,
                                                Concertyears)
            # 发送欢迎邮件
            retmp = get_re_template_val(userModel, ProbjectId)
            if userModel.Enable == 1:
                if userModel.Role_Id == 5:
                    push_message.delay(userModel, 'newuser', retmp, ProbjectId)
                elif userModel.Role_Id == 6:
                    bjrmt = int_arg(get_project_config_val(ProbjectId, 'mtbjr'), None)
                    retmp['tips'] = '请您尽快预约教练的时间,进行约谈'
                    # 有化学面谈
                    retmp["project"] = pinfo.Name
                    retmp["path_link"] = get_newmember_has_link(ProbjectId)
                    if bjrmt and bjrmt == 1:
                        retmp['tips'] = '请尽快预约教练的时间，开启化学面谈进行教练匹配，选择适合您的教练'
                        push_message.delay(userModel, 'newmember_has', retmp, ProbjectId)
                    # 无化学面谈
                    else:
                        retmp["path_link"] = get_newmember_no_link()

                        push_message.delay(userModel, 'newmember_no', retmp, ProbjectId)
                elif userModel.Role_Id == 8:
                    push_message.delay(userModel, 'newevaluator', retmp, ProbjectId)
                elif userModel.Role_Id == 7:
                    pass
                else:
                    push_message.delay(userModel, 'newuser', retmp, ProbjectId)
        return Response(
            res.lower_raw()
        )

    @action(methods=['post'], detail=False, url_path='update')
    def update_new(self, request):
        main_data = request.data.get('mainData')
        Member_Id = int_arg(main_data.get('Member_Id'), 0)
        if Member_Id == 0:
            return Response(
                response.Content().error('账户不存在').lower_raw()
            )
        mobile = main_data.get('mobile', '')
        email = main_data.get('email', '')
        password = main_data.get('password', '')
        age = int_arg(main_data.get('Age'), 0)
        main_data['Age'] = age
        # TODO birthday
        Company_Id = int_arg(main_data.get('Company_Id'), 0)
        if Company_Id > 0:
            comx = get_company_info(Company_Id)
            main_data['CompanyName'] = comx.CompanyName
        WorkingDateNum = float_arg(main_data.get('WorkingDateNum'), 0)
        CompanyWorkingDateNum = float_arg(main_data.get('CompanyWorkingDateNum'), 0)
        GwWorkingDateNum = float_arg(main_data.get('GwWorkingDateNum'), 0)
        if WorkingDateNum > 0:
            main_data['WorkingDate'] = get_work_time(WorkingDateNum)
        if CompanyWorkingDateNum > 0:
            main_data['CompanyWorkingDate'] = get_work_time(CompanyWorkingDateNum)
        if GwWorkingDateNum > 0:
            main_data['GwWorkingDate'] = get_work_time(GwWorkingDateNum)
        Role_Id = int_arg(main_data.get('Role_Id'), 0)
        ProbjectId = int_arg(main_data.get('ProbjectId'), 0)
        Photograph = main_data.get('Photograph', '')
        if Photograph and type(Photograph) is list and 'path' in Photograph[0]:
            main_data['Photograph'] = Photograph[0].get('path')

        Interested_Id = int_arg(main_data.get('Interested_Id'), 0)
        main_data['Interested_Id'] = Interested_Id
        uid = int_arg(main_data.get('User_Id'), 0)

        # update stakeholder logic
        # member_data = models.AppProbjectrelation.objects.filter(UserId=uid, RolesId=6)
        # if Role_Id == 7 and member_data.exists():
        #     #     this member has coachee and stakeholder role
        #     #     update stakeholder data only
        #     relation = main_data.get('Relation', '')
        #     coachidx = get_project_def_coach_by_user(Interested_Id)
        #     enable = int_arg(main_data.get('Enable'), 0)
        #     Concertyears = int_arg(main_data.get('Concertyears'), 0)
        #     add_or_update_member_interested(Interested_Id, uid, ProbjectId, enable, coachidx, relation,
        #                                     Concertyears)
            # return Response(
            #     response.Content().ok().raw()
            # )

        res = self.update_row(main_data)
        if res.status:
            uifo = models.SysUser.objects.filter(User_Id=uid).first()
            if uifo:
                TrueName = main_data.get('TrueName', '')
                enable = int_arg(main_data.get('Enable'), 0)
                if uifo.HeadImageUrl != Photograph or null_or_blank(uifo.HeadImageUrl):
                    uifo.HeadImageUrl = Photograph
                if uifo.Enable != enable:
                    uifo.Enable = enable
                if uifo.UserTrueName != TrueName or null_or_blank(uifo.UserTrueName):
                    uifo.UserTrueName = TrueName
                if uifo.PhoneNo != mobile or null_or_blank(uifo.PhoneNo):
                    uifo.PhoneNo = mobile
                if uifo.Email != email or null_or_blank(uifo.Email):
                    uifo.Email = email
                if uifo.Role_Id != Role_Id:
                    uifo.Role_Id = Role_Id
                uifo.save()

                if ProbjectId > 0 and Interested_Id > 0:
                    relation = main_data.get('Relation', '')
                    coachidx = get_project_def_coach_by_user(Interested_Id)
                    enable = int_arg(main_data.get('Enable'), 0)
                    Concertyears = int_arg(main_data.get('Concertyears'), 0)
                    add_or_update_member_interested(Interested_Id, uid, ProbjectId, enable, coachidx, relation,
                                                    Concertyears)

        return Response(
            res.lower_raw()
        )

    @action(methods=['post'], detail=False)
    def personUpdate(self, request):
        # employee = 直系下属  Concertyears=合作年限 Relation=上下级关系
        main_data = request.data.get('mainData')
        Member_Id = int_arg(main_data.get('Member_Id'), 0)
        # 利益相关者id
        user_id = int_arg(main_data.get('User_Id'), 0)
        if Member_Id == 0:
            uid = int_arg(main_data.get('User_Id'), 0)
            if uid == 0:
                main_data['User_Id'] = request.user.pk
            return Response(
                response.Content().error('账户不存在').raw()
            )
        manage_role = main_data.get('manage_role', None)
        # 被教练者id
        interested_id = main_data.get('Interested_Id', None)
        interested_user = models.AppMemberinterested.objects.filter(Member_Id=user_id,
                                                                    MasterMember_Id=interested_id).first()

        if interested_id and interested_user:
            interested_user.Concertyears = main_data.get('Concertyears')
            interested_user.Relation = main_data.get('Relation')
            interested_user.save()
        mobile = main_data.get('mobile', '')
        email = main_data.get('email', '')
        Role_Id = int_arg(main_data.get('Role_Id'), 0)
        ProbjectId = int_arg(main_data.get('ProbjectId'), 0)
        Photograph = main_data.get('Photograph', '')
        if Photograph and type(Photograph) is list and 'path' in Photograph[0]:
            main_data['Photograph'] = Photograph[0].get('path')
        # 工作年限
        WorkingDateNum = float_arg(main_data.get('WorkingDateNum'), 0)
        # 公司年限
        CompanyWorkingDateNum = float_arg(main_data.get('CompanyWorkingDateNum'), 0)
        GwWorkingDateNum = float_arg(main_data.get('GwWorkingDateNum'), 0)
        if WorkingDateNum > 0:
            main_data['WorkingDate'] = get_work_time(WorkingDateNum)
        if CompanyWorkingDateNum > 0:
            main_data['CompanyWorkingDate'] = get_work_time(CompanyWorkingDateNum)
        if GwWorkingDateNum > 0:
            main_data['GwWorkingDate'] = get_work_time(GwWorkingDateNum)
        res = self.update_row(main_data)
        if res.status:
            uid = int_arg(main_data.get('User_Id'), 0)
            uifo = models.SysUser.objects.filter(User_Id=uid).first()
            if uifo:
                TrueName = main_data.get('TrueName', '')
                if uifo.HeadImageUrl != Photograph or null_or_blank(uifo.HeadImageUrl):
                    uifo.HeadImageUrl = Photograph
                if uifo.UserTrueName != TrueName or null_or_blank(uifo.UserTrueName):
                    uifo.UserTrueName = TrueName
                if uifo.PhoneNo != mobile or null_or_blank(uifo.PhoneNo):
                    uifo.PhoneNo = mobile
                if uifo.Email != email or null_or_blank(uifo.Email):
                    uifo.Email = email
                # if manage_role:
                #     uifo.manage_role = manage_role
                uifo.save()
        return Response(
            res.raw()
        )

    @action(methods=['post'], detail=False)
    def upload(self, request):
        file_obj = request.FILES.get('fileInput')
        res = self.upload_file(file_obj, ['.jpg', '.png'])
        # if res.status and res.data:
        #     path = res.data
        #     minfo = models.AppMember.objects.filter(User_Id=request.user.pk).first()
        #     if minfo:
        #         minfo.Photograph = path
        #         minfo.save()
        #     userinfo = models.SysUser.objects.filter(User_Id=request.user.pk).first()
        #     if userinfo:
        #         userinfo.HeadImageUrl = path
        #         userinfo.save()
        return Response(
            res.raw()
        )

    @action(methods=['get'], detail=False)
    def getentity(self, request):
        res = self.get_entity(request)
        if res.status:
            data = self.serializer_class(res.row, many=False).data
            uid = int_arg(res.row.User_Id, 0)
            uifo = models.SysUser.objects.filter(User_Id=uid).first()
            if not uifo:
                mobile = data.get('mobile', '')
                uifo = models.SysUser.objects.filter(PhoneNo=mobile)
            if uifo:
                data['Role_Id'] = uifo.Role_Id
            res.data = data
        return Response(
            res.raw()
        )

    @action(methods=['post'], detail=False)
    def getpagedata(self, request):
        project_id = 0
        for where in json.loads(request.data["wheres"]):
            if where['name'] == 'ProbjectId':
                project_id = int_arg(where['value'], 0)
                ids = models.AppProbjectrelation.objects.filter(ProbjectId=where['value']).values_list('UserId',
                                                                                                       flat=True)
                if len(ids) > 0:
                    add_option(request.data, 'User_Id', ids, 'checkbox')
                else:
                    add_option(request.data, 'User_Id', 0, 'int')
            else:
                add_option(request.data, where['name'], where['value'], where['displayType'])
        t = self.get_page_rows(request.data)
        if t.count() > 0:
            list = []
            if project_id > 0:
                for item in t.all():
                    dic = {}
                    dic["eUser_Id"] = item.User_Id
                    uinfo = models.SysUser.objects.filter(User_Id=item.User_Id).first()
                    if uinfo:
                        dic["UserName"] = uinfo.UserName
                    pinfo = models.AppProbjectrelation.objects.filter(UserId=item.User_Id,
                                                                      ProbjectId=project_id).first()
                    if pinfo:
                        dic["Role_Id"] = pinfo.RolesId
                        if pinfo.CoachId > 0:
                            coinfo = models.SysUser.objects.filter(User_Id=pinfo.CoachId).first()
                            dic["cocah"] = coinfo.UserTrueName
                        else:
                            dic["cocah"] = "--"
                    # 关联者 //children
                    relation_list = relation_filter_by_interest([item.User_Id]).filter(ProbjectId=project_id)
                    if relation_list.count() > 0:
                        dicre_list = []
                        for r in relation_list:
                            memberinfo = get_member_info(r.UserId)
                            if memberinfo and memberinfo.Member_Id > 0:
                                dicr = self.serializer_class(memberinfo, many=False).data
                                uinfox = models.SysUser.objects.filter(User_Id=r.UserId).first()
                                if uinfox:
                                    dicr["UserName"] = uinfox.UserName
                                dicr["Role_Id"] = r.RolesId
                                dicr["cocah"] = "--"
                                dicr["ildp"] = 1
                                interest_info = models.AppMemberinterested.objects.filter(MasterMember_Id=item.User_Id,
                                                                                          Member_Id=r.UserId).first()
                                if interest_info:
                                    dicr['Relation'] = interest_info.Relation
                                    dicr['Interested_Id'] = interest_info.MasterMember_Id
                                    dicr['userid'] = interest_info.Member_Id
                                    dicr['Concertyears'] = interest_info.Concertyears
                                dicre_list.append(dicr)
                        dic['children'] = dicre_list
                    dic['ildp'] = int_arg(get_project_config_val(project_id, 'bgidlp'), None)
                    list.append(dic)
                t.extra_list = list
        return Response(
            self.page_data(t)
        )

    @action(methods=['get'], detail=False)
    def GetUserByProbject(self, request):
        probjectid = int_arg(request.query_params.get('probjectid'), 0)
        ids = models.AppProbjectrelation.objects.filter(ProbjectId=probjectid, RolesId=6).values_list('UserId',
                                                                                                      flat=True)
        if len(ids) == 0:
            ids = [0]
        members = models.AppMember.objects.filter(User_Id__in=ids)
        listdic = []
        for item in members.all():
            uid = item.User_Id
            dic = self.serializer_class(item, many=False).data
            uinfo = models.SysUser.objects.filter(User_Id=uid).first()
            if uinfo:
                dic["UserName"] = uinfo.UserName
            listdic.append(dic)
        res = response.Content()
        res.data = listdic
        return Response(
            res.ok().raw()
        )

    @action(methods=['post'], detail=False)
    def updatestatus(self, request):
        uid = int_arg(request.query_params.get('uid'), None)
        status = int_arg(request.query_params.get('status'), None)
        if uid == 0:
            return Response(
                response.Content().error('非法的用户 id')
            )
        res = response.Content()
        member = models.AppMember.objects.filter(User_Id=uid).first()
        if member:
            member.Enable = status
            member.save()
            uinfo = models.SysUser.objects.filter(User_Id=uid).first()
            if uinfo:
                uinfo.Enable = status
                uinfo.save()
            res.data = self.serializer_class(res.row, many=False).data
        return Response(
            res.ok().raw()
        )

    @action(methods=['post'], detail=False)
    def setpwd(self, request):
        uid = int_arg(request.query_params.get('uid'), None)
        probjectid = int_arg(request.query_params.get('probjectid'), None)
        pwd = request.query_params.get('pwd', '')
        if uid == 0 and probjectid == 0:
            return Response(
                response.Content().error('非法的用户 id')
            )
        uinfo = models.SysUser.objects.filter(User_Id=uid).first()
        if uinfo:
            plain_pwd = ''
            if null_or_blank(pwd):
                # 获取项目默认密码
                plain_pwd = get_project_config_val(probjectid, 'defpwd')
                if plain_pwd:
                    pwd = aesencrypt(plain_pwd)
                else:
                    plain_pwd = randomPassword(6)
                    pwd = aesencrypt(plain_pwd)
            uinfo.UserPwd = pwd
            uinfo.save()
            update_django_user_pwd(uinfo)
            push_message.delay(uinfo, 'editpwd', {'password': plain_pwd, 'url': settings.SITE_URL}, probjectid)
            return Response(
                response.Content().ok('重置密码成功').raw()
            )
        else:
            return Response(
                response.Content().ok('用户信息不对').raw()
            )

    @action(methods=['post'], detail=False)
    def associatedpro(self, request):
        input = request.data.get('input', None)
        probjectid = int_arg(request.data.get('probjectid'), None)
        if probjectid == 0:
            return Response(
                response.Content().error('非法的关联项目')
            )
        uinfo = models.AppMember.objects.filter(mobile=input).first() | models.AppMember.objects.filter(
            email=input).first()
        if uinfo:
            uinfo.Interested_Id = 0
            uinfo.save()
            flag = models.AppProbjectrelation.objects.filter(ProbjectId=probjectid, UserId=uinfo.User_Id).exists()
            if not flag:
                prelation = models.AppProbjectrelation()
                prelation.CompanyId = uinfo.Company_Id
                prelation.ProbjectId = probjectid
                prelation.UserId = uinfo.User_Id
                prelation.CoachId = 0
                prelation.IsManger = 0
                prelation.RolesId = 0
                prelation.Interested_Id = "0"
                prelation.save()
            return Response(
                response.Content().OK('关联项目成功，请从新分配权限及关系').raw()
            )
        else:
            return Response(
                response.Content().error('关联项目异常，用户不存在').raw()
            )

    @action(methods=['post'], detail=False)
    def disassociatepro(self, request):
        probjectid = int_arg(request.data.get('probjectid'), None)
        uid = int_arg(request.data.get('uid'), None)
        retionuid = int_arg(request.data.get('retionuid'), None)
        if retionuid and retionuid > 0:
            prorelationinfo = relation_filter_by_interest([retionuid]).filter(ProbjectId=probjectid, UserId=uid)
            if prorelationinfo.count() > 0:
                prorelationinfo.all().delete()
            interested_list = models.AppMemberinterested.objects.filter(Member_Id=uid, MasterMember_Id=retionuid,
                                                                        ProbjectId__in=probjectid)
            if interested_list.count() > 0:
                interested_list.all().delete()
        else:
            prorelationinfo = models.AppProbjectrelation.objects.filter(ProbjectId=probjectid,
                                                                        UserId=uid)
            if prorelationinfo.count() > 0:
                prorelationinfo.all().delete()
        return Response(
            response.Content().ok('解除关联项目成功').raw()
        )

    @action(methods=['post'], detail=False)
    def AddInterested(self, request):
        main_data = request.data.get('mainData')
        TrueName = main_data.get('TrueName', '')
        mobile = main_data.get('mobile', '')
        password = main_data.get('password', '')
        email = main_data.get('email', '')
        Relation = main_data.get('Relation', '')
        UserName = main_data.get('UserName', '').strip()
        Interested_Id = request.user.pk
        if UserName == '':
            return Response(
                response.Content().error('登录名不能空').raw()
            )
        if TrueName == '' or mobile == '' or email == '':
            return Response(
                response.Content().error('姓名/手机号/邮箱不能空').raw()
            )
        pinfo = get_project_info_by_user(Interested_Id)
        coachid = get_project_def_coach_by_user(Interested_Id)
        Company_Id = int_arg(main_data.get('Company_Id'), 0)
        if Company_Id == 0:
            Company_Id = pinfo.Company_Id
            main_data['Company_Id'] = Company_Id
        if Company_Id > 0:
            comx = get_company_info(Company_Id)
            main_data['CompanyName'] = comx.CompanyName
        ProbjectId = int_arg(main_data.get('ProbjectId'), 0)
        if ProbjectId == 0:
            ProbjectId = pinfo.Probject_Id

        umodel = models.SysUser.objects.filter(UserName=UserName).first()
        uid = 0
        if umodel and umodel.User_Id > 0:
            uid = umodel.User_Id
        else:
            userModel = models.SysUser()
            userModel.Email = email
            userModel.PhoneNo = mobile
            userModel.UserPwd = password
            userModel.UserName = UserName
            if null_or_blank(userModel.UserPwd):
                # 获取项目默认密码
                defpwd = get_project_config_val(ProbjectId, 'defpwd')
                if defpwd:
                    userModel.UserPwd = aesencrypt(defpwd)
                else:
                    userModel.UserPwd = aesencrypt(randomPassword(6))
            userModel.UserTrueName = TrueName
            userModel.Role_Id = 7
            userModel.RoleName = get_children_name(7)
            userModel.CreateDate = datetime.datetime.now().replace(microsecond=0)
            userModel.ModifyDate = datetime.datetime.now().replace(microsecond=0)
            userModel.Creator = request.user.UserName
            xmlyzsh = int_arg(get_project_config_val(ProbjectId, 'xmlyzsh'), None)
            userModel.Enable = 0
            if xmlyzsh == -1:
                userModel.Enable = 1

            try:
                userModel.save()
            except Exception as e:
                return Response(
                    response.Content().error('创建用户失败').raw()
                )
            add_sys_user(userModel)
            enable_im(userModel.User_Id)
            uid = userModel.User_Id
        isexist = models.AppMember.objects.filter(User_Id=uid).exists()
        res = response.Content()
        if not isexist:
            main_data['User_Id'] = uid
            res = self.add_row(main_data)
        if res.status:
            if ProbjectId > 0:
                prelation = models.AppProbjectrelation()
                prelation.CompanyId = Company_Id
                prelation.ProbjectId = ProbjectId
                prelation.UserId = uid
                prelation.CoachId = 0
                prelation.IsManger = 0
                prelation.RolesId = userModel.Role_Id
                prelation.Interested_Id = str(Interested_Id)
                try:
                    prelation.save()
                except Exception as e:
                    return Response(
                        response.Content().error('创建项目关系失败').raw()
                    )

                add_or_update_member_interested(Interested_Id, uid, ProbjectId, 0, coachid, Relation, 0)
        return Response(
            res.raw()
        )

    @action(methods=['post'], detail=False)
    def AuditInterested(self, request):
        probjectid = int_arg(request.query_params.get('probjectid'), 0)
        uid = int_arg(request.query_params.get('uid'), 0)
        retionId = int_arg(request.query_params.get('retionId'), 0)
        Enable = int_arg(request.query_params.get('Enable'), 1)
        add_or_update_member_interested(uid, retionId, ProbjectId=probjectid, Enable=Enable)
        return Response(
            response.Content().ok().raw()
        )

    @action(methods=['post'], detail=False)
    def EditMember(self, request):
        main_data = request.data.get('mainData')
        Member_Id = int_arg(main_data.get('Member_Id'), 0)
        TrueName = main_data.get('TrueName', '')
        mobile = main_data.get('mobile', '')
        email = main_data.get('email', '')
        if null_or_blank(TrueName) or null_or_blank(mobile) or null_or_blank(email):
            return Response(
                response.Content().error('姓名/手机号/邮箱不能空').raw()
            )
        WorkingDateNum = float_arg(main_data.get('WorkingDateNum'), 0)
        CompanyWorkingDateNum = float_arg(main_data.get('CompanyWorkingDateNum'), 0)
        GwWorkingDateNum = float_arg(main_data.get('GwWorkingDateNum'), 0)
        if WorkingDateNum > 0:
            main_data['WorkingDate'] = get_work_time(WorkingDateNum)
        if CompanyWorkingDateNum > 0:
            main_data['CompanyWorkingDate'] = get_work_time(CompanyWorkingDateNum)
        if GwWorkingDateNum > 0:
            main_data['GwWorkingDate'] = get_work_time(GwWorkingDateNum)

        Photograph = main_data.get('Photograph', '')
        if Photograph and type(Photograph) is list and 'path' in Photograph[0]:
            main_data['Photograph'] = Photograph[0].get('path')
            Photograph = Photograph[0].get('path')
            main_data['Photograph'] = Photograph
        elif not Photograph:
            Photograph = None
            del main_data['Photograph']

        res = self.update_row(main_data)
        return Response(
            res.raw()
        )
    # 企业管理员
    @action(methods=['post'], detail=False)
    def AddMember(self, request):
        main_data = request.data.get('mainData')
        TrueName = main_data.get('TrueName', '')
        mobile = main_data.get('mobile', '')
        email = main_data.get('email', '')
        password = main_data.get('password', '')
        UserName = main_data.get('UserName', '').strip()
        Interested_Id = int_arg(main_data.get('Interested_Id'), 0)
        if UserName == '':
            return Response(
                response.Content().error('登录名不能空').raw()
            )
        else:
            cfusername = models.SysUser.objects.filter(UserName=UserName).first()
            if cfusername:
                return Response(
                    response.Content().error('该登陆名已经存在').raw()
                )
        if TrueName == '' or mobile == '' or email == '':
            return Response(
                response.Content().error('姓名/手机号/邮箱不能空').raw()
            )
        ProbjectId = int_arg(main_data.get('probjectid'), 0)
        if ProbjectId <= 0:
            return Response(
                response.Content().error('关联的项目不能空').raw()
            )
        pinfo = None
        if ProbjectId > 0:
            pinfo = get_project_info(ProbjectId)
        else:
            pinfo = get_project_info_by_user(request.user.pk)
        numbers = pinfo.Number
        if numbers > 0 and get_project_member_numbers(pinfo.Probject_Id) >= numbers:
            return Response(
                response.Content().error('项目账号人数超限制，请与项目负责人联系').raw()
            )
        Company_Id = int_arg(main_data.get('Company_Id'), 0)
        if Company_Id == 0:
            Company_Id = pinfo.Company_Id
            main_data['Company_Id'] = Company_Id
        if Company_Id > 0:
            comx = get_company_info(Company_Id)
            main_data['CompanyName'] = comx.CompanyName

        WorkingDateNum = float_arg(main_data.get('WorkingDateNum'), 0)
        CompanyWorkingDateNum = float_arg(main_data.get('CompanyWorkingDateNum'), 0)
        GwWorkingDateNum = float_arg(main_data.get('GwWorkingDateNum'), 0)
        if WorkingDateNum > 0:
            main_data['WorkingDate'] = get_work_time(WorkingDateNum)
        if CompanyWorkingDateNum > 0:
            main_data['CompanyWorkingDate'] = get_work_time(CompanyWorkingDateNum)
        if GwWorkingDateNum > 0:
            main_data['GwWorkingDate'] = get_work_time(GwWorkingDateNum)

        Photograph = main_data.get('Photograph', '')
        if Photograph and type(Photograph) is list and 'path' in Photograph[0]:
            main_data['Photograph'] = Photograph[0].get('path')
            Photograph = Photograph[0].get('path')
            main_data['Photograph'] = Photograph
        elif not Photograph:
            Photograph = None
            del main_data['Photograph']
        userModel = models.SysUser()
        userModel.Email = email
        userModel.PhoneNo = mobile
        if null_or_blank(password):
            # 获取项目默认密码
            defpwd = get_project_config_val(ProbjectId, 'defpwd')
            if defpwd:
                password = aesencrypt(defpwd)
            else:
                password = aesencrypt(randomPassword(6))
        userModel.UserPwd = password
        userModel.UserName = UserName

        userModel.UserTrueName = TrueName
        userModel.Role_Id = 6
        userModel.RoleName = get_children_name(6)
        userModel.CreateDate = datetime.datetime.now().replace(microsecond=0)
        userModel.ModifyDate = datetime.datetime.now().replace(microsecond=0)
        userModel.Creator = request.user.UserName
        userModel.Enable = int_arg(main_data.get('Enable'), 0)
        if Photograph:
            userModel.HeadImageUrl = Photograph
        umodel = models.SysUser.objects.filter(UserName=userModel.UserName).first()
        uid = 0
        if umodel and umodel.User_Id > 0:
            main_data['User_Id'] = umodel.User_Id
            uid = umodel.User_Id
        else:
            try:
                userModel.save()
            except Exception as e:
                return Response(
                    response.Content().error('创建用户失败').raw()
                )
            add_sys_user(userModel)
            enable_im(userModel.User_Id)
            main_data['User_Id'] = userModel.User_Id
            uid = userModel.User_Id
            retmp = get_re_template_val(userModel, ProbjectId)
            bjrmt = int_arg(get_project_config_val(ProbjectId, 'mtbjr'), None)
            retmp['tips'] = '请您尽快预约教练的时间,进行约谈'
            if bjrmt and bjrmt == 1:
                retmp['tips'] = '请尽快预约教练的时间，开启化学面谈进行教练匹配，选择适合您的教练'
        res = self.add_row(main_data)
        if res.status:
            if ProbjectId > 0:
                prelation = models.AppProbjectrelation()
                prelation.CompanyId = Company_Id
                prelation.ProbjectId = ProbjectId
                prelation.UserId = uid
                prelation.CoachId = 0
                prelation.IsManger = 0
                prelation.RolesId = userModel.Role_Id
                prelation.Interested_Id = '0'
                try:
                    prelation.save()
                except Exception as e:
                    return Response(
                        response.Content().error('创建项目关系失败').raw()
                    )
            if userModel.Enable == 1:
                if userModel.Role_Id == 6:
                    retmp = get_re_template_val(userModel, ProbjectId)
                    bjrmt = int_arg(get_project_config_val(ProbjectId, 'mtbjr'), None)
                    retmp['tips'] = '请您尽快预约教练的时间,进行约谈'
                    # 有化学面谈
                    retmp["project"] = pinfo.Name
                    retmp["path_link"] = get_newmember_has_link(ProbjectId)
                    if bjrmt and bjrmt == 1:
                        retmp['tips'] = '请尽快预约教练的时间，开启化学面谈进行教练匹配，选择适合您的教练'
                        push_message.delay(userModel, 'newmember_has', retmp, ProbjectId)
                    # 无化学面谈
                    else:
                        retmp["path_link"] = get_newmember_no_link()

                        push_message.delay(userModel, 'newmember_no', retmp, ProbjectId)
                        
            if numbers > 0 and get_project_member_numbers(pinfo.Probject_Id) >= numbers:
                # 创建利益相关者不发邮件
                if userModel.Role_Id != 7:
                    # 通知项目负责人 被教人创建完成 NewUserToManagerMsg
                    uinfo = models.SysUser.objects.filter(User_Id=pinfo.Relation_UserId).first()
                    if uinfo:
                        dic = {
                            'tips': '企业已经创建完被教练者账户'
                        }
                        push_message.delay(uinfo, 'newmembertomanager', dic, ProbjectId)
        return Response(
            res.raw()
        )

    @action(methods=['get'], detail=False)
    def GetMessageId(self, request):
        uid = int_arg(request.query_params.get('uid'), None)
        uinfo = models.SysUser.objects.filter(User_Id=uid).first()
        res = response.Content()
        if uinfo and not null_or_blank(uinfo.MessageId):
            res.data = uinfo.MessageId
            return Response(
                res.ok('SUC').raw()
            )
        else:
            return Response(
                res.error('用户未开通IM服务').raw()
            )

    @action(methods=['get'], detail=False)
    def getuserinfobymessageid(self, request):
        hxUser = request.query_params.get('hxUser')
        uinfo = models.SysUser.objects.filter(MessageId=hxUser).first()
        res = response.Content()
        if uinfo and not null_or_blank(uinfo.MessageId):
            res.data = uinfo.values('User_Id', 'UserTrueName', 'HeadImageUrl')
            return Response(
                res.ok('SUC').raw()
            )
        else:
            return Response(
                res.error('用户未开通IM服务').raw()
            )

    @action(methods=['post'], detail=False)
    def GetTxInviteMember(self, request):
        uid = int_arg(request.query_params.get('uid'), None)
        probjectid = int_arg(request.query_params.get('probjectid'), None)
        uinfo = models.SysUser.objects.filter(User_Id=uid).first()
        if uinfo:
            retmp = get_re_template_val(uinfo, probjectid)
            if uinfo.Enable == 1:
                if uinfo.Role_Id == 5:
                    push_message.delay(uinfo, 'newuser', retmp)
                elif uinfo.Role_Id == 6:
                    bjrmt = int_arg(get_project_config_val(probjectid, 'mtbjr'), None)
                    retmp['tips'] = '请您尽快预约教练的时间,进行约谈'
                    if bjrmt and bjrmt == 1:
                        retmp['tips'] = '请尽快预约教练的时间，开启化学面谈进行教练匹配，选择适合您的教练'
                    push_message.delay(uinfo, 'newmember', retmp, probjectid)
                elif uinfo.Role_Id == 8:
                    push_message.delay(uinfo, 'newevaluator', retmp, probjectid)
                else:
                    push_message.delay(uinfo, 'newuser', retmp, probjectid)
        return Response(
            response.Content().ok().raw()
        )

    @action(methods=['get'], detail=False)
    def GetUserInfoR17(self, request):
        uid = int_arg(request.query_params.get('uid'), None)
        minfo = get_member_info(uid)
        if not minfo:
            return Response(
                response.Content().error('未获取到用户信息').raw()
            )
        pinfo = get_project_info_by_user(uid)
        if not pinfo:
            return Response(
                response.Content().error('未获取到项目信息').raw()
            )
        res = response.Content()
        sex = '女'
        if minfo.Sex == 1:
            sex = '男'
        res.data = {
            'userid': minfo.User_Id,
            'truename': minfo.TrueName,
            'sex': sex,
            'company': minfo.CompanyName,
            'duty': minfo.Duty,
            'background': pinfo.Background
        }
        return Response(
            res.ok('SUC').raw()
        )

    @action(methods=['get'], detail=False)
    def GetUserInfoR111(self, request):
        uid = int_arg(request.query_params.get('uid'), None)
        minfo = get_member_info(uid)
        res = response.Content()
        sex = '女'
        if minfo.Sex == 1:
            sex = '男'
        res.data = {
            'userid': minfo.User_Id,
            'truename': minfo.TrueName,
            'sex': sex,
            'company': minfo.Company_Id,
            'duty': minfo.Duty,
            'parentdepartment': minfo.ParentDepartment,
            'companyworkingdatenum': get_work_year(minfo.CompanyWorkingDate),
            'gwworkingdatenum': get_work_year(minfo.GwWorkingDate),
            'workingdatenum': get_work_year(minfo.WorkingDate),
            'employee': minfo.employee,
            'address': minfo.address,
            'qitzw': minfo.qitzw
        }
        return Response(
            res.ok('SUC').raw()
        )

    @action(methods=['get'], detail=False)
    def GetUserInfoR112(self, request):
        uid = request.user.pk
        minfo = get_member_info(uid)
        res = response.Content()
        sex = '女'
        if minfo.Sex == 1:
            sex = '男'
        res.data = {
            'userid': minfo.User_Id,
            'truename': minfo.TrueName,
            'sex': sex,
            'companyname': minfo.CompanyName,
            'duty': minfo.Duty
        }
        return Response(
            res.ok('SUC').raw()
        )

    @action(methods=['get'], detail=False)
    def GetUserInfoR313(self, request):
        uid = int_arg(request.query_params.get('uid'), None)
        minfo = get_member_info(uid)
        pinfo = get_project_info_by_user(uid)
        res = response.Content()
        sex = '女'
        if minfo.Sex == 1:
            sex = '男'
        res.data = {
            'userid': minfo.User_Id,
            'truename': minfo.TrueName,
            'sex': sex,
            'companyname': minfo.CompanyName,
            'duty': minfo.Duty,
            'times': pinfo.Times
        }
        return Response(
            res.ok('SUC').raw()
        )

    @action(methods=['get'], detail=False)
    def GetUserInfoR25(self, request):
        coach_id = int_arg(request.query_params.get('coachid'), None)
        interested_id = int_arg(request.query_params.get('interestedid'), None)
        uid = int_arg(request.query_params.get('uid'), None)
        rinfo = get_member_info(uid)
        concertyears = 0
        relation = ''
        minterest = models.AppMemberinterested.objects.filter(Member_Id=uid, CoachId=coach_id,
                                                              MasterMember_Id=interested_id).first()
        if minterest:
            concertyears = minterest.Concertyears
            relation = minterest.Relation
        minfo = get_member_info(interested_id)
        res = response.Content()
        sex = ''
        if rinfo.Sex == 1:
            sex = '男'
        elif rinfo.Sex == 2:
            sex = '女'
        res.data = {
            'userid': minfo.User_Id,
            'truename': minfo.TrueName,
            'ftname': rinfo.TrueName,
            'sex': sex,
            'companyname': rinfo.CompanyName,
            'duty': rinfo.Duty,
            'worktime': get_work_year(rinfo.CompanyWorkingDate),
            'concertyears': concertyears,
            'relation': relation
        }
        return Response(
            res.ok('SUC').raw()
        )

    @action(methods=['get'], detail=False)
    def GetUserInfoR24(self, request):
        uid = int_arg(request.query_params.get('uid'), None)
        rid = request.user.pk
        rinfo = get_member_info(rid)
        minfo = get_member_info(uid)
        minterest = models.AppMemberinterested.objects.filter(Member_Id=rid, MasterMember_Id=uid).first()
        concertyears = 0
        relation = ''
        if minterest:
            concertyears = minterest.Concertyears
            relation = minterest.Relation
        res = response.Content()
        sex = '女'
        if rinfo.Sex == 1:
            sex = '男'
        res.data = {
            'userid': minfo.User_Id,
            'truename': minfo.TrueName,
            'ftname': rinfo.TrueName,
            'sex': sex,
            'companyname': rinfo.CompanyName,
            'duty': rinfo.Duty,
            'worktime': get_work_year(rinfo.CompanyWorkingDate),
            'concertyears': rinfo.Concertyears,
            'relation': relation
        }
        return Response(
            res.ok('SUC').raw()
        )

    @action(methods=['get'], detail=False)
    def GetUserInfoR45(self, request):
        uid = int_arg(request.query_params.get('uid'), None)
        probjectid = int_arg(request.query_params.get('probjectid'), None)
        rid = request.user.pk
        minfo = get_member_info(uid)
        if not minfo:
            return Response(
                response.Content().error('未找到用户信息').raw()
            )
        minterest = models.AppMemberinterested.objects.filter(Member_Id=rid, MasterMember_Id=uid).first()
        concertyears = 0
        relation = ''
        if minterest:
            concertyears = minterest.Concertyears
            relation = minterest.Relation
        ability = ''
        uAbility = get_my_project_objectives(probjectid, uid)
        if uAbility.status:
            ability = uAbility.data
        res = response.Content()
        res.data = {
            'userid': minfo.User_Id,
            'truename': minfo.TrueName,
            'concertyears': concertyears,
            'relation': relation,
            'ability': ability
        }
        return Response(
            res.ok('SUC').raw()
        )

    @action(methods=['post'], detail=False)
    def alluserformanager(self, request):
        uid = request.data.get('uid')
        if not uid:
            uid = request.user.pk
        coach_id = int(request.data.get('coachid', 0))

        if models.SysUser.objects.filter(User_Id=uid).first().Role_Id != 3:
            return Response(
                response.Content().error('请求用户不是管理员').raw()
            )
        now = datetime.datetime.now()
        project_list = models.AppProbject.objects.filter(Relation_UserId=uid, Enable=1, Starttime__lte=now,
                                                         Endtime__gt=now)
        project_ids = project_list.values_list('Probject_Id', flat=True)
        member_list = models.AppProbjectrelation.objects.filter(ProbjectId__in=project_ids,
                                                                RolesId__in=[6, 7]).order_by('-ProbjectId')
        if coach_id:
            member_list = models.AppProbjectrelation.objects.filter(CoachId=coach_id,
                                                                    RolesId=6).order_by('-ProbjectId')
        data = []
        for item in member_list:
            username = models.AppMember.objects.get(User_Id=item.UserId).TrueName
            project_name = models.AppProbject.objects.get(pk=item.ProbjectId).Name
            data.append({
                'value': username + ' (' + project_name + ')',
                'info': {
                    'pid': item.ProbjectId,
                    'uid': item.UserId,
                    'roleid': item.RolesId
                }
            })
        # 之前代码逻辑，留下做对照
        # if coach_id:
        #     member_list = member_list.filter(CoachId=coach_id)
        # member_ids = member_list.values_list('UserId', flat=True)
        # username_list = list(models.AppMember.objects.filter(User_Id__in=member_ids).values_list('User_Id', 'TrueName'))
        # for item in member_list.all():
        #     if item.RolesId == 7:
        #         if member_list.filter(ProbjectId=item.ProbjectId, CoachId=item.CoachId, RolesId=6,
        #                               UserId=item.UserId).exists():
        #             # This user has both stakeholder and coachee role
        #             continue
        #     username_set = [x[1] for x in username_list if x[0] == item.UserId]
        #     if not username_set:
        #         break
        #     username = username_set[0]
        #     projectname_set = [x[1] for x in projectname_list if x[0] == item.ProbjectId]
        #     if not projectname_set:
        #         break
        #     projectname = projectname_set[0]
        #     value = username + ' (' + projectname + ')'
        #     data.append({
        #         'value': value,
        #         'info': {
        #             'pid': item.ProbjectId,
        #             'uid': item.UserId,
        #             'roleid': item.RolesId
        #         }
        #     })
        return Response(
            response.Content().ok('', data).raw()
        )

    @action(methods=['post'], detail=False)
    def alluserforcoach(self, request):
        uid = request.data.get('uid')
        if not uid:
            uid = request.user.pk
        if models.SysUser.objects.filter(User_Id=uid).first().Role_Id != 4:
            return Response(
                response.Content().error('请求用户不是教练').raw()
            )
        now = datetime.datetime.now()
        project_list = models.AppProbject.objects.filter(Enable=1, Starttime__lte=now, Endtime__gt=now)
        probjectids = project_list.values_list('Probject_Id', flat=True)
        projectname_list = list(project_list.values_list('Probject_Id', 'Name'))
        member_list = models.AppProbjectrelation.objects.filter(ProbjectId__in=probjectids, CoachId=uid,
                                                                RolesId=6).order_by('-ProbjectId')

        member_ids = member_list.values_list('UserId', flat=True)
        username_list = list(models.AppMember.objects.filter(User_Id__in=member_ids).values_list('User_Id', 'TrueName'))
        data = []
        for item in member_list.all():
            username = [x[1] for x in username_list if x[0] == item.UserId][0]
            projectname = [x[1] for x in projectname_list if x[0] == item.ProbjectId][0]
            value = username + ' (' + projectname + ')'
            data.append({
                'value': value,
                'info': {
                    'pid': item.ProbjectId,
                    'uid': item.UserId,
                    'roleid': item.RolesId
                }
            })
        return Response(
            response.Content().ok('', data).raw()
        )

    @action(methods=['post'], detail=False)
    def DelMemberProbject(self, request):
        probjectId = int_arg(request.query_params.get('probjectId'), None)
        uid_list = request.data
        if uid_list and probjectId:
            member_list = models.AppProbjectrelation.objects.filter(ProbjectId=probjectId, UserId__in=uid_list)
            member_list.all().delete()
        return Response(
            response.Content().ok('').raw()
        )

    @action(methods=['post'], detail=False)
    def myinterested(self, request):
        try:
            uid = request.data.get('uid')
            pid = request.data.get('pid')
        except Exception as e:
            return Response(
                response.Content().error('param not valid').raw()
            )
        member_interested_list = models.AppMemberinterested.objects.filter(ProbjectId=pid, Member_Id=uid)
        data = []
        for item in member_interested_list.all():
            interested_id = item.MasterMember_Id
            interested_name = ''
            interested_user = models.SysUser.objects.filter(User_Id=interested_id).first()
            if interested_user:
                interested_name = interested_user.UserTrueName
            interview = models.AppProbjectinterview.objects.filter(ProbjectId=pid, interviewType=1, User_Id=uid,
                                                                   masteruserid=interested_id, status=1).first()
            interview_id = None
            interview_starttime = ''
            interview_endtime = ''
            times = None
            Satisfaction = None
            if interview:
                interview_id = interview.Id
                times = interview.Times
                Satisfaction= interview.Satisfaction
                interview_starttime = datetime.datetime.strftime(interview.StartTime, '%Y-%m-%d %H:%M')
                interview_endtime = datetime.datetime.strftime(interview.EndTime, '%Y-%m-%d %H:%M')
            interested_relation = models.AppProbjectrelation.objects.filter(ProbjectId=pid, RolesId=6, UserId=interested_id).first()
            coach_id = None
            if interested_relation:
                coach_id = interested_relation.CoachId
            coach_name = None
            coach_message_id = ''
            if coach_id:
                coach_user = models.SysUser.objects.filter(User_Id=coach_id).first()
                if coach_user:
                    coach_name = coach_user.UserTrueName
                    coach_message_id = coach_user.MessageId
            data.append({
                'interested_name': interested_name,
                'interested_id': interested_id,
                'interview_id': interview_id,
                'interview_starttime': interview_starttime,
                'interview_endtime': interview_endtime,
                'coach_id': coach_id,
                'coach_name': coach_name,
                'coach_message_id': coach_message_id,
                'times': times,
                'Satisfaction': Satisfaction
            })
        interview_or_exam = int_arg(get_project_config_val(pid, 'mtlyxgz'), None)
        result = {
            'interview_or_exam': interview_or_exam,
            'interview_data': data
        }
        return Response(
            response.Content().ok('', result).raw()
        )

    params = [
        openapi.Parameter('interested_id', openapi.IN_QUERY, description='用户id(被教练者id)', type=openapi.TYPE_NUMBER),
        openapi.Parameter('user_id', openapi.IN_QUERY, description='利益相关者id', type=openapi.TYPE_NUMBER),
    ]

    @swagger_auto_schema(
        operation_id='获取利益相关者信息',
        operation_summary='获取利益相关者信息',
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'user_id': openapi.Schema(type=openapi.TYPE_NUMBER, description='user_id'),
                'interested_id': openapi.Schema(type=openapi.TYPE_NUMBER, description='interested_id'),
            }
        ),
        tags=['用户']
    )
    @action(methods=['post'], detail=False, url_path='interestUser')
    def get_interest_info(self, request):
        try:
            user_id = int(request.data.get('user_id', 0))
            user = models.AppMember.objects.get(User_Id=user_id)
        except Exception as e:
            print(e)
            return Response(
                response.Content().error('请检查传入参数').raw()
            )
        interested_id = int_arg(request.data.get('interested_id'))
        instance = self.serializer_class(user)
        data = instance.data
        if interested_id:
            try:
                user_relation = models.AppMemberinterested.objects.get(Member_Id=user_id, MasterMember_Id=interested_id)
            except models.AppMemberinterested.DoesNotExist:
                return Response(
                    response.Content().error('interested_id参数错误').raw()
                )

            data['Relation'] = user_relation.Relation
            data['Concertyears'] = user_relation.Concertyears

        return Response(response.Content().ok(data=data).raw())
