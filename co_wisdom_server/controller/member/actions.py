import datetime

from controller.company.actions import get_company_info
from controller.project.actions import get_project_info
from data import models
from utils import int_arg, aesdecrypt
from utils.easemob.common import enable_im_and_add_friend


def get_member_info(uid):
    uinfo = models.AppMember.objects.filter(User_Id=uid).first()
    if uinfo:
        return uinfo
    return None


def get_work_time(number):
    now = datetime.datetime.now()
    one_year_seconds = 365 * 24 * 3600
    time_delta = datetime.timedelta(seconds=int(number * one_year_seconds))
    date = now - time_delta
    return date


def get_project_member_numbers(Probject_Id):
    roleid = 6
    num = models.AppProbjectrelation.objects.filter(RolesId=roleid, ProbjectId=Probject_Id).count()
    return num


def add_or_update_member_interested(MasterMember_Id, Member_Id, ProbjectId, Enable, CoachId=0, Relation='',
                                    Concertyears=0):
    info = models.AppMemberinterested.objects.filter(ProbjectId=ProbjectId, MasterMember_Id=MasterMember_Id,
                                                     Member_Id=Member_Id).first()
    if info:
        info.Enable = Enable
        info.Relation = Relation
        info.Concertyears = Concertyears
        info.save()
        status = '被拒绝'
        if Enable == 1:
            status = '已通过'
        uinfo = models.SysUser.objects.filter(User_Id=MasterMember_Id).first()
        retmp = {
            'status': status
        }
        # push_message(uinfo, 'txmemberinterested', retmp)
    else:
        info = models.AppMemberinterested()
        info.MasterMember_Id = MasterMember_Id
        info.Member_Id = Member_Id
        info.ProbjectId = ProbjectId
        info.Enable = Enable
        info.CoachId = CoachId
        info.Relation = Relation
        info.Concertyears = Concertyears
        info.save()
    if info and info.Enable == 1:
        # 更新用户账号状态
        uinfo = models.SysUser.objects.filter(User_Id=Member_Id).first()
        if uinfo:
            uinfo.Enable = Enable
            uinfo.save()
        minfo = models.AppMember.objects.filter(User_Id=Member_Id).first()
        if minfo:
            minfo.Enable = Enable
            minfo.save()
        if info.CoachId > 0:
            enable_im_and_add_friend([int_arg(info.Member_Id, 0), int_arg(info.CoachId, 0)])


def get_work_year(worktime):
    if not worktime:
        return 0
    interval = datetime.datetime.now() - worktime
    return int(interval.total_seconds() / (3600 * 24 * 365))


def get_re_template_val(user, projectid):
    dic = {
        'password': aesdecrypt(user.UserPwd)
    }
    company = ''
    project_name = ''
    background = ''
    manager = ''
    mobile = ''
    email = ''
    pinfo = get_project_info(projectid)
    if pinfo:
        project_name = pinfo.Name
        background = pinfo.Background
        cinfo = get_company_info(pinfo.Company_Id)
        if cinfo:
            company = cinfo.ShortName
        minfo = models.SysUser.objects.filter(User_Id=int_arg(pinfo.Relation_UserId, 0)).first()
        if minfo:
            manager = minfo.UserTrueName
            mobile = minfo.PhoneNo
            email = minfo.Email
    dic["company"] = company
    dic["probjectname"] = project_name
    dic["background"] = background
    dic["manager"] = manager
    dic["mobile"] = mobile
    dic["email"] = email
    return dic


def get_company_name(user_id):
    """ 通过user_id 查找company_name """
    try:
        company_name = models.AppCompany.objects.get(pk=models.AppMember.objects.get(User_Id=user_id).Company_Id).CompanyName
        return company_name
    except Exception as e:
        print(e)
