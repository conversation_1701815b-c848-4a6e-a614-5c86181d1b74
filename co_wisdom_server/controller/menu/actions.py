import json

from data import models


def get_menu_action_list(roleId):
    if roleId == 1:
        all_menu = models.SysMenu.objects.all()
        res = []
        for item in all_menu.all():
            action = json.loads(item.Auth)

            res.append({
                'id': item.Menu_Id,
                'name': item.MenuName,
                'url': item.Url,
                'parentId': item.ParentId,
                'icon': item.Icon,
                'permission': item.OrderNo
            })