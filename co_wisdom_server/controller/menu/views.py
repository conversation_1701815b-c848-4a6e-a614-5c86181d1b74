from rest_framework.decorators import action
from rest_framework.response import Response

from data import models, serializers, extension


class SysMenuViewSet(extension.ResponseViewSet):
    queryset = models.SysMenu.objects.all()
    serializer_class = serializers.SysMenuSerializer

    @action(methods=['post'], detail=False)
    def getMenu(self, request):
        all_menu = models.SysMenu.objects.order_by('-OrderNo', '-ParentId')
        res = []
        for item in all_menu.all():
            res.append({
                'id': item.Menu_Id,
                'parentId': item.ParentId,
                'name': item.MenuName,
                'OrderNo': item.OrderNo
            })
        return Response(
            res
        )
