from django.urls import path, include
from django.conf.urls import url
from . import views
from rest_framework import routers

router = routers.DefaultRouter()
router.register(r'user', views.UserViewSet, basename='user')
router.register(r'coach', views.CoachViewSet, basename='coach')
router.register(r'coachee', views.CoacheeViewSet, basename='coachee')
router.register(r'project_owner', views.ProjectOwnerViewSet, basename='project_owner')
router.register(r'company_admin', views.CompanyAdminViewSet, basename='company_admin')
router.register(r'stakeholder', views.StakeholderViewSet, basename='stakeholder')
router.register(r'evaluator', views.EvaluatorViewSet, basename='evaluator')

urlpatterns = [
    path('login', views.login.as_view(), name='login'),
    # url(r'^', include(router.urls))
]