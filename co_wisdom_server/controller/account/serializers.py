
from rest_framework import serializers
from data.user.models import User
from data.user.coach.models import Coach
from data.user.coachee.models import Coachee
from data.user.project_owner.models import Project_owner
from data.user.company_admin.models import Company_admin
from data.user.stakeholder.models import Stakeholder
from data.user.evaluator.models import Evaluator


class UserSerializer(serializers.ModelSerializer):
    class Meta:
        model = User
        fields = "__all__"
class CoachSerializer(serializers.HyperlinkedModelSerializer):
    class Meta:
        model = Coach
        fields = "__all__"
class CoacheeSerializer(serializers.HyperlinkedModelSerializer):
    class Meta:
        model = Coachee
        fields = "__all__"
class ProjectOwnerSerializer(serializers.HyperlinkedModelSerializer):
    class Meta:
        model = Project_owner
        fields = "__all__"
class CompanyAdminSerializer(serializers.HyperlinkedModelSerializer):
    class Meta:
        model = Company_admin
        fields = "__all__"
class StakeholderSerializer(serializers.HyperlinkedModelSerializer):
    class Meta:
        model = Stakeholder
        fields = "__all__"
class EvaluatorSerializer(serializers.HyperlinkedModelSerializer):
    class Meta:
        model = Evaluator
        fields = "__all__"