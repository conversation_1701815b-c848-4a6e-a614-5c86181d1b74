from django.http import HttpResponse
from django.contrib.auth.models import User as sys_user
from django.contrib.auth import authenticate, login as auth_login
from data.user.models import User
from data.user.coach.models import Coach
from data.user.coachee.models import <PERSON><PERSON>
from data.user.project_owner.models import Project_owner
from data.user.company_admin.models import Company_admin
from data.user.evaluator.models import Evaluator
from data.user.stakeholder.models import Stakeholder
from utils import aesencrypt
from rest_framework_simplejwt.tokens import RefreshToken
from .serializers import UserSerializer, CoachSerializer, CoacheeSerializer, ProjectOwnerSerializer, \
    CompanyAdminSerializer, EvaluatorSerializer, StakeholderSerializer
from rest_framework.viewsets import ModelViewSet
from rest_framework.response import Response
from rest_framework.views import APIView
from data.user.views import business_user_info, getUserMenu

# Create your views here.
class login(APIView):
    def post(self, request):
        username = request.data['userName']
        password = request.data['passWord']
        user = User.objects.filter(login_name=username).first()
        if user is not None:
            if check_password(user, password):
                s_user = sys_login(request, user)
                user_business_info = business_user_info(user)
                # jwt token
                refresh = RefreshToken.for_user(s_user)
                user_business_info["token"] = str(refresh.access_token)
                menus = getUserMenu(user)
                return Response({
                    "status": 301,
                    'message': "ok",
                    'data': {
                        "userinfo": user_business_info,
                        "menus": menus
                    }
                })
        return Response({
            "status": 0,
            "message": "login failed",
            "data": None
        })


def check_password(user, password):
    userPassword = user.login_password
    encryptedPassword = aesencrypt(password).decode('utf8')
    return encryptedPassword == userPassword


# user viewset
class UserViewSet(ModelViewSet):
    queryset = User.objects.all()
    serializer_class = UserSerializer


class CoachViewSet(ModelViewSet):
    queryset = Coach.objects.all()
    serializer_class = CoachSerializer


class CoacheeViewSet(ModelViewSet):
    queryset = Coachee.objects.all()
    serializer_class = CoacheeSerializer


class ProjectOwnerViewSet(ModelViewSet):
    queryset = Project_owner.objects.all()
    serializer_class = ProjectOwnerSerializer


class CompanyAdminViewSet(ModelViewSet):
    queryset = Company_admin.objects.all()
    serializer_class = CompanyAdminSerializer


class StakeholderViewSet(ModelViewSet):
    queryset = Stakeholder.objects.all()
    serializer_class = StakeholderSerializer


class EvaluatorViewSet(ModelViewSet):
    queryset = Evaluator.objects.all()
    serializer_class = EvaluatorSerializer


def sys_login(request, user):
    s_user = authenticate(username=user.login_name, password=user.login_password)
    if s_user is not None:
        # auth_login(request, s_user)
        return s_user
    return None
