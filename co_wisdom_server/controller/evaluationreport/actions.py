import datetime

from controller.coach.actions import get_coach_info
from controller.member.actions import get_member_info
from data import models
from utils import response


def get_reportid(userid, evaluationid, projectid=0):
    report = models.AppEvalutionreport.objects.filter(Userid=userid, ProbjectId=projectid, EvaluationId=evaluationid).first()
    if report:
        return report.pk
    return 0

def get_question(result, title, color):

    default_question_dic = {'fixed_text': title, 'question_value': []}
    for r in result:
        title = models.AppEvaluationquestion.objects.filter(Sort=r['tIndex'],
                                                            EvaluationId=17).first().Title
        default_question_dic['question_value'].append({'question_content': title, 'background_color': color})

    return default_question_dic


def get_all_json(queryset):
    # A突破进取 B抗压复原 C自我成长 D敏捷应变 E引领变革 F战略远见 G激励赋能 H发展下属 J果敢决策 K系统思考 L多方协同 M集体担责
    dic = {
        'A': {'sort': [24, 27, 30, 50], 'score': [], 'user_id': [], 'other_score': [], 'other_user_id': []},
        'B': {'sort': [15, 16, 23, 39, 55, 62, 74, 77], 'score': [], 'user_id': [], 'other_score': [], 'other_user_id': []},
        'C': {'sort': [2, 11, 14, 26, 38, 70], 'score': [], 'user_id': [], 'other_score': [], 'other_user_id': []},
        'D': {'sort': [5, 17, 18, 29, 54, 65, 76], 'score': [], 'user_id': [], 'other_score': [], 'other_user_id': []},
        'E': {'sort': [6, 9, 13, 25, 31, 78], 'score': [], 'user_id': [], 'other_score': [], 'other_user_id': []},
        'F': {'sort': [3, 4, 10, 22, 28, 40, 58], 'score': [], 'user_id': [], 'other_score': [], 'other_user_id': []},
        'G': {'sort': [7, 19, 43, 49, 63, 64, 67, 69], 'score': [], 'user_id': [], 'other_score': [], 'other_user_id': []},
        'H': {'sort': [8, 32, 44, 51, 68], 'score': [], 'user_id': [], 'other_score': [], 'other_user_id': []},
        'J': {'sort': [1, 33, 41, 52], 'score': [], 'user_id': [], 'other_score': [], 'other_user_id': []},
        'K': {'sort': [21, 42, 45, 56, 61, 66, 71, 73, 75], 'score': [], 'user_id': [], 'other_score': [], 'other_user_id': []},
        'L': {'sort': [12, 35, 36, 37, 47, 60, 72], 'score': [], 'user_id': [], 'other_score': [], 'other_user_id': []},
        'M': {'sort': [20, 34, 46, 48, 53, 57, 59], 'score': [], 'user_id': [], 'other_score': [], 'other_user_id': []},
    }
    for r in queryset:
        for _k, _v in dic.items():
            if r.tIndex in _v['sort']:
                if r.EvaluationId == 17:
                    dic[_k]['score'].append(r.Score)
                    dic[_k]['user_id'].append(r.Userid)
                elif r.EvaluationId == 18:
                    dic[_k]['other_score'].append(r.Score)
                    dic[_k]['other_user_id'].append(r.Userid)

    return dic