import json
import numpy as np
from django.conf import settings
from rest_framework.decorators import action
from rest_framework.response import Response
from drf_yasg import openapi
from drf_yasg.utils import swagger_auto_schema
from django.db.models import Avg, Q, Variance


from controller.evaluation.actions import get_evaluation_info
from controller.evaluationresult.actions import get_result_report
from controller.project.actions import get_project_info
from data import models, serializers, extension
from utils import response, int_arg
from utils.model import add_option
from utils.user import get_user_ids
from utils.messagecenter.center import push_message
from utils.messagecenter.actions import get_template_to_manage_link, get_template_to_project_manage_link,\
    get_end_report_to_coach_link, get_end_report_to_student_link
from .actions import get_question, get_all_json
from controller.projectreport.actions import get_json, get_default_histogram


class EvaluationReportViewSet(extension.ResponseViewSet):
    queryset = models.AppEvalutionreport.objects.all()
    serializer_class = serializers.AppEvalutionreportSerializer

    @action(methods=['post'], detail=False)
    def getpagedata(self, request):
        for where in json.loads(request.data["wheres"]):
            if where['name'] == 'UserName':
                ids = get_user_ids(where['value'])
                if len(ids) > 0:
                    add_option(request.data, 'Userid', ids, 'checkbox')
            elif where['name'] == 'EvalName':
                ids = models.AppEvaluation.objects.filter(Enable=1, EvalName__icontains=where['value']).values_list(
                    'EvaluationId', flat=True)
                if len(ids) > 0:
                    add_option(request.data, 'EvaluationId', ids, 'checkbox')
            elif where['name'] == 'ProName':
                ids = models.AppProbject.objects.filter(Enable=1, Name__icontains=where['value']).values_list(
                    'Probject_Id', flat=True)
                if len(ids) > 0:
                    add_option(request.data, 'ProbjectId', ids, 'checkbox')
            elif where['name'] == 'CoachId':
                ids = models.AppProbjectrelation.objects.filter(CoachId=int(where['value'])).values_list(
                    'UserId', flat=True)
                if len(ids) > 0:
                    add_option(request.data, 'Userid', ids, 'checkbox')
            else:
                add_option(request.data, where['name'], where['value'], where['displayType'])
        t = self.get_page_rows(request.data)
        if t.count() > 0:
            extra = []
            for item in t.all():
                dic = {}
                dic["eId"] = item.Id
                evmodel = get_evaluation_info(item.EvaluationId)
                dic["EvalName"] = evmodel.EvalName
                dic["ProName"] = get_project_info(item.ProbjectId).Name
                username = models.SysUser.objects.filter(User_Id=item.Userid).first().UserName
                dic["UserName"] = username
                extra.append(dic)
            t.extra_list = extra
        return Response(
            self.page_data(t)
        )

    @action(methods=['post'], detail=False)
    def upload(self, request):
        file_obj = request.FILES.get('fileInput')
        res = self.upload_file(file_obj, ['.jpg', '.png', '.pdf'])
        return Response(
            res.raw()
        )

    @action(methods=['post'], detail=False)
    def UpStatus(self, request):
        ReportId = int_arg(request.query_params.get('reportid'))
        Status = int_arg(request.query_params.get('Status'))
        minfo = models.AppEvalutionreport.objects.filter(Id=ReportId).first()
        if minfo:
            minfo.status = Status
            minfo.save()

            # 完成调研通知群智管理员\企业管理员查看（27）
            template_to_manage = 'exam_end_to_manage'
            project = models.AppProbject.objects.get(pk=minfo.ProbjectId)
            project_manage_user = models.SysUser.objects.get(pk=project.Relation_UserId)
            student = models.SysUser.objects.get(pk=minfo.Userid)
            evaluation_name = models.AppEvaluation.objects.get(EvaluationId=minfo.EvaluationId).EvalName


            to_project_manage_params = {
                'studentname': student.UserTrueName,
                'evaluation_name': evaluation_name,
                'path_link': get_template_to_manage_link(ReportId, project.pk, minfo.Userid)

            }
            push_message.delay(project_manage_user, template_to_manage, to_project_manage_params, project.pk)
            # 完成调研通知群智管理员\企业管理员查看（27）
            # 推送企业管理员 企业管理员可能有多个 IsManger project_id
            # user_ids = models.AppProbjectrelation.objects.filter(ProbjectId=project.pk,
            #                                                      IsManger=True).values_list('UserId', flat=True)
            # company_user = models.SysUser.objects.filter(pk__in=user_ids)
            # if company_user.exists:
            #     for user in company_user:
            #         # TODO 现在企业管理员是否有权限查看
            #         to_project_manage_params['path_link'] = get_template_to_project_manage_link()
            #         push_message.delay(user, template_to_manage, to_project_manage_params, project.pk)

            end_report_to_manage = 'end_report_to_manage'

            to_coach_params = {'evaluation_name': evaluation_name, 'title':evaluation_name}

            # 完成调研通知教练查看（28）
            coachid = models.AppProbjectrelation.objects.filter(UserId=minfo.Userid, ProbjectId=project.pk,
                                                                RolesId=6).first().CoachId
            coach = models.SysUser.objects.filter(pk=coachid).first()
            if coach:
                to_coach_params['path_link'] = get_end_report_to_coach_link(ReportId, project.pk, minfo.Userid)
                push_message.delay(coach, end_report_to_manage, to_coach_params, project.pk)

            # 完成调研通知被教练者查看（29）
            to_coach_params['path_link'] = get_end_report_to_student_link(ReportId, project.pk, minfo.Userid)

            push_message.delay(student, end_report_to_manage, to_coach_params, project.pk)

            evuser = models.AppProbjectexamrelationuser.objects.filter(ProbjectId=minfo.ProbjectId,
                                                                       ExId=minfo.EvaluationId, UserId=minfo.Userid,
                                                                       TypeId=1).first()
            if evuser:
                evuser.Status = 2
                evuser.ReportId = minfo.Id
                evuser.save()
            return Response(
                response.Content().ok().raw()
            )
        return Response(
            response.Content().error().raw()
        )

    @action(methods=['post'], detail=False)
    def UpReport(self, request):
        resultid = int_arg(request.query_params.get('resultid'))
        reportid = int_arg(request.query_params.get('reportid'))
        analyseids = request.query_params.get('analyseids')
        eva_result = models.AppEvaluationresult.objects.filter(Id=resultid).first()
        if not eva_result:
            return Response(
                response.Content().error('非法的报告数据').raw()
            )
        report = get_result_report(resultid, analyseids)
        data = {}
        data["ProbjectId"] = eva_result.ProbjectId
        data["EvaluationId"] = eva_result.EvaluationId
        data["Userid"] = eva_result.Userid
        data["reportjson"] = json.dumps(report.data.get('data'), ensure_ascii=False, default=str)
        data["status"] = 0
        if reportid > 0:
            # update
            data['Id'] = reportid
            res = self.update_row(data)
            return Response(
                res.raw()
            )
        res = self.add_row(data)
        return Response(
            res.raw()
        )

    @action(methods=['post'], detail=False, authentication_classes=[])
    def update_guide(self, request):
        try:
            report = models.AppEvalutionreport.objects.get(pk=request.data.get('report_id'))
        except models.AppEvalutionreport.DoesNotExist:
            return Response(response.Content().error('报告ID错误').raw())
        res = json.loads(report.reportjson)
        res['develop_guide'] = request.data.get('guide', None)
        report.reportjson = json.dumps(res, ensure_ascii=False, default=str)
        report.save()
        return Response(response.Content().ok({'guide': request.data.get('guide', None)}).raw())

    @swagger_auto_schema(
        operation_id='个人报告接口',
        operation_summary='个人报告接口',
        manual_parameters=[
            openapi.Parameter('uid', openapi.IN_QUERY, type=openapi.TYPE_INTEGER, description='用户id'),
            openapi.Parameter('pid', openapi.IN_QUERY, type=openapi.TYPE_INTEGER, description='项目id'),
        ]
    )
    @action(methods=['get'], detail=False, url_path='anaylse', authentication_classes=[])
    def anaylse_user_report(self, request):
        try:
            user = models.SysUser.objects.get(pk=request.query_params.get('uid', 0))
            project = models.AppProbject.objects.get(pk=request.query_params.get('pid', 0))
        except Exception as e:
            return Response(response.Content().error('请求参数错误').raw())
        report_result = models.AppEvaluationresult.objects.filter((Q(Userid=user.pk) & Q(EvaluationId=17)) |
                                                                  (Q(Interested_Id=user.pk) & Q(EvaluationId=18)),
                                                                  ProbjectId=project.pk)

        # 参与者
        lbi_finished_user_list = report_result.filter(EvaluationId=18).values_list('Userid', flat=True)

        member_interested_list = models.AppMemberinterested.objects.filter(ProbjectId=project.pk,
                                                                           MasterMember_Id=user.pk,
                                                                           Member_Id__in=lbi_finished_user_list)

        memberids = member_interested_list.values_list('Member_Id', flat=True)

        otheruser = models.AppMember.objects.filter(User_Id__in=memberids)
        attend = [{'relation': '上级', 'title': '参加调研的上级', 'content': [], 'color': '#5470c6'},
                          {'relation': '平级', 'title': '参加调研的平级', 'content': [], 'color': '#91cc75'},
                          {'relation': '下级', 'title': '参加调研的下级', 'content': [], 'color': '#fac858'}]
        for item in otheruser.all():
            interested_info = member_interested_list.filter(Member_Id=item.User_Id).first()
            relation = interested_info.Relation if interested_info else ''
            content = item.TrueName + ' ' + item.Duty
            for attend_item in attend:
                if attend_item['relation'] == relation:
                    attend_item['content'].append(content)

        # 自评与他评对照
        spider_data = {'fixed_text': '该图表呈现了关于领导者日常管理行为的自我评价和基于观察的综合评价分数对比。（1分-从未展现，6分-堪称楷模）',
                       'values_list': [],
                       'steps': [{
                           'title': '自评',
                           'color': '#5470c6'
                       },{
                           'title': '他评',
                           'color': '#ee6666'
                       }]}

        SPIDER_MAP = {
            'A': '突破进取',
            'B': '抗压复原',
            'C': '自我成长',
            'D': '敏捷应变',
            'E': '引领变革',
            'F': '战略远见',
            'G': '激励赋能',
            'H': '发展下属',
            'J': '果敢决策',
            'K': '系统思考',
            'L': '多方协同',
            'M': '集体担责',
        }

        behaviour_dic = get_all_json(report_result)
        # 按照前端给的数据排序 ["突破进取", "抗压复原", "自我成长", "果敢决策", "多方协同", "系统思考", "战略远见", "引领变革", "敏捷应变", "发展下属", "激励赋能", "集体担责"]

        sort_spider = [{'title': '突破进取', 'values': ''}, {'title': '抗压复原', 'values': ''},
                       {'title': '自我成长', 'values': ''}, {'title': '果敢决策', 'values': ''},
                       {'title': '多方协同', 'values': ''}, {'title': '系统思考', 'values': ''},
                       {'title': '战略远见', 'values': ''}, {'title': '引领变革', 'values': ''},
                       {'title': '敏捷应变', 'values': ''}, {'title': '发展下属', 'values': ''},
                       {'title': '激励赋能', 'values': ''}, {'title': '集体担责', 'values': ''}]

        for s in sort_spider:
            for spider, score in behaviour_dic.items():
                if SPIDER_MAP[spider] == s['title']:
                    s['values'] = [round(np.mean(score['score']), 1), round(np.mean(score['other_score']), 1)]
        spider_data['values_list'] = sort_spider

        # 360对照

        # 效能分析
        
        # 行为项分类
        result_sort_var = report_result.values('tIndex').annotate(score_var=Variance('Score'),
                                                                  score_avg=Avg('Score')).order_by('score_var')
        # ===============================================问题======================================================
        questions = []
        list_before = result_sort_var[0:int(result_sort_var.count() / 2)]
        # 一致优势
        advantage_list = sorted((list_before), key=lambda item: item['score_avg'], reverse=True)
        advantage_dic = get_question(advantage_list[0:5], '你和同事一致认为，你的优势在以下领导行为得以体现', '#ee6666')
        questions.append(advantage_dic)

        # 一致进步
        progress_list = sorted((list_before), key=lambda item: item['score_avg'], reverse=False)
        progress_dic = get_question(progress_list[0:5], '你和同事一致认为，你的优势在以下领导行为中你有较大提升空间', '#ee6666')
        questions.append(progress_dic)

        list_after = result_sort_var[int(result_sort_var.count() / 2):]
        # 盲区优势
        unknown_advantage_list = sorted((list_after), key=lambda item: item['score_avg'], reverse=True)
        unknown_advantage_dic = get_question(unknown_advantage_list[0:5], '在以下的领导力行为中，你的同事看到了被你忽视的优势', '#ee6666')
        questions.append(unknown_advantage_dic)

        # 盲区进步
        unknown_progress_list = sorted((list_after), key=lambda item: item['score_avg'], reverse=False)
        unknown_progress_dic = get_question(unknown_progress_list[0:5], '在以下的领导力行为中，你的同事看到了未被你发掘的进步空间', '#ee6666')
        questions.append(unknown_progress_dic)

        # ============================================一致性柱状图=========================================================
        histogram_label = []
        histogram = {'class': 'bar', 'title': '',
                     'splitArea': True, 'is_overlap': False,
                     'fixed_text': '各项领导能力得分按照自评和他评的一致性由高到低进行排序，您可以从中看到自己和他人对于哪些领导力有效性的评价是有共识的，以及自己在哪些领导力有效性的认知方面存在盲区',
                     'top_text_list': ['效能不佳', '', '相对有效', '稳定发挥', '充分发挥'],
                     'steps': [{'title': '自评', 'color': '#EE6666'},
                               {'title': '他评', 'color': '#5470c6'}], 'axisx': [], 'axisy': [], 'bar': []
                     }

        histogram_axisx = {'label': '', 'bar_label': [1, 2, 3, 4, 5, 6], 'range': [1, 6], 'hideLable': True,
                           'interval': 1}
        other_data = get_json(report_result.filter(Interested_Id=user.pk), 18)
        self_dic = get_json(report_result.filter(Userid=user.pk), 17)

        for s, t in other_data.items():
            other_data[s]['avg'] = round(np.mean(t['score']), 1)
            other_data[s]['var'] = round(np.var(t['score']), 1)

        for k, v in self_dic.items():
            self_dic[k]['var'] = np.var(v['score'])
            self_dic[k]['avg'] = round(np.mean(v['score']), 1)

        histogram_bar_list = []
        # 按照自评+他评方差排序
        for sk, sv in self_dic.items():
            for _k, _v in other_data.items():
                if sk == _k:
                    self_dic[sk]['all_var'] = round(np.var(self_dic[sk]['score'] + other_data[_k]['score']), 1)
                    other_data[_k]['all_var'] = round(np.var(self_dic[sk]['score'] + other_data[_k]['score']), 1)

        # 自评 他评排序取值
        self_dic = dict(sorted(self_dic.items(), key=lambda item: item[1]['all_var'], reverse=True))
        other_data = dict(sorted(other_data.items(), key=lambda item: item[1]['all_var'], reverse=True))
        print('other_data', other_data)
        print('self_data', self_dic)
        dic_self = {'height': 15, 'value': [], 'color': [], 'label': []}
        dic_other = {'height': 15, 'value': [], 'color': [], 'label': []}
        for k, v in other_data.items():
            histogram_label.append(SPIDER_MAP[k])
            dic_other['value'].append(v['avg'])
            dic_other['label'].append(v['avg'])
            dic_other['color'].append('#EE6666')

        for value in self_dic.values():
            dic_self['value'].append(value['avg'])
            dic_self['label'].append(value['avg'])
            dic_self['color'].append('#5470c6')
        print('dic_self', dic_self['value'])
        print('dic_other', dic_other['value'])
        histogram_bar_list.append(dic_self)
        histogram_bar_list.append(dic_other)
        # 柱状图（自评 他评）bar
        histogram['bar'] = histogram_bar_list
        # 柱状图（自评 他评）x
        histogram['axisx'].append(histogram_axisx)
        # 柱状图（自评 他评）y
        histogram_axisy = {'label': '', 'bar_label': histogram_label}
        histogram['axisy'].append(histogram_axisy)

        # ==========================================12项领导力=======================================================
        comparison = get_default_histogram(self_dic, other_data)
        comparison_sort = sorted(comparison, key=lambda item: item['bar'][0]['value'][0], reverse=True)

        try:
            project = models.AppProbject.objects.filter(pk=project.pk).first()
            project_name = project.Name
            company_name = models.AppCompany.objects.get(pk=project.Company_Id).CompanyName
        except models.AppCompany.DoesNotExist:
            company_name = None
            project_name = None
        eval_report = models.AppEvalutionreport.objects.filter(ProbjectId=project.pk, Userid=user.pk).first()

        if not eval_report:
            eval_report = models.AppEvalutionreport.objects.create(ProbjectId=project.pk, Userid=user.pk, EvaluationId=17)
            eval_report.save()
            
        report_date = eval_report.ModifyDate.strftime("%Y-%m-%d")

        default_guide = '''在我们过往的经验中发现，中国的领导者往往聚焦于低分项。因此，当您基于该报告思考未来的发展时，特意提醒您对自己的优势和特长给予充分的重视和肯定，不管您的得分如何，每一位管理者在了解自己的需求以及寻求长期持续发展的机会时，都需要确认并充分发挥自身的优势，这是您未来领导力发展的基础。
        <br/><br/>在肯定优势的基础上，您也可以从本报告中清晰地看到自己需要进步的领导力，以及在哪些领导行为上存在较大的进步空间。需要注意的是，报告中分数和行为仅能为您指明发展的方向，要想获得实质性的成长和改变，还需要您结合利益相关者结合具体事件而进行的反馈。
        <br/><br/>此时，你已经从360度系统的维度看到自己在组织中的声誉和盲区，这其中必然包含着你充分认同的部分，和需要你继续探索的部分。为了能够让您充分利用这份报告，我们建议您进行以下思考：
 
<br/><br/>1、哪些是你要持续保持的优势？
<br/>2、哪些是你需要提升的能力？
<br/>3、这个报告中哪些内容令你感到出乎意料？
<br/>4、综合以上，你可以在实际工作中做哪些新行为的尝试？

<br/><br/>看见是改变的第一步。一个新行为的养成是不容易的，在不同的工作情形下，与不同的人互动总是会带来新的挑战。群智教练可以为您提供陪伴式辅导，支持你解决日常工作难题的同时，提升领导力效能。如果您希望在后续的发展中得到群智教练的支持，请和我们取得联系。
<br/><br/>看见是改变的第一步。一个新行为的养成是不容易的，在不同的工作情形下，与不同的人互动总是会带来新的挑战。在您接下来的领导力发展旅程中，群智教练会与您一路同行，为您提供陪伴式辅导，支持你解决日常工作难题的同时，提升领导力效能 。您的持续反思和行动，将成为走向卓越的关键第一步，欢迎启程！'''

        data = {
            'ver': 2,
            'title': 'LBI领导力行为指数评估报告',
            'company_name': company_name,
            'project_name': project_name,
            'report_date': report_date,
            'attend': attend,
            'comparison': {'class': 'spider', 'values': spider_data},
            'user_name': user.UserTrueName,
            'comparison_360': comparison_sort,
            'efficiency': histogram,
            'behaviour_list': questions,
            'develop_guide': default_guide,
            'reportid': eval_report.pk
        }

        eval_report.reportjson = json.dumps(data, ensure_ascii=False, default=str)
        eval_report.status = 1
        eval_report.save()

        return Response(
            response.Content().ok('SUC', data=data).raw()
        )
