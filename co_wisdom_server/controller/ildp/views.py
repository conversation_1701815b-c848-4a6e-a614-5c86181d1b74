import datetime
from django.conf import settings
from rest_framework.decorators import action
from rest_framework.response import Response

from controller.CacheTmpResult.actions import del_key
from controller.dictionary.actions import get_dictionary_list_value
from controller.projectinterview.actions import update_times
from data import models, serializers, extension
from utils import response, int_arg, null_or_blank, enter_to_br
from utils.messagecenter.center import push_message
from utils.messagecenter.actions import get_pici_link, subject_type, get_interview_end_to_coach_link,\
    get_interview_end_to_student_link, get_interview_end_to_coach_ok_link, get_interview_end_ok_to_student_link


class ILDPViewSet(extension.ResponseViewSet):
    queryset = models.AppIldp.objects.all()
    serializer_class = serializers.AppIldpSerializer

    @action(methods=['post'], detail=False)
    def AddOrUpdateNew(self, request):
        """ ILDP添加或新增 """
        main_data = request.data.get('mainData')
        id = int_arg(main_data.get('Id'), 0)
        truename = main_data.get('truename', '')
        ProbjectId = int_arg(main_data.get('ProbjectId'), 0)
        # 被教练者id
        UserId = int_arg(main_data.get('UserId'), 0)

        CoachId = int_arg(main_data.get('CoachId'), 0)
        KeyName = main_data.get('KeyName', '')
        diary = main_data.get('diary', '')
        if null_or_blank(truename):
            uinfo = models.SysUser.objects.filter(User_Id=UserId).first()
            main_data['truename'] = uinfo.UserTrueName
        if null_or_blank(KeyName):
            return Response(
                response.Content().error('非法的ILDP教练目标入口地址').raw()
            )
        _tArr = KeyName.split('_')
        index = KeyName.index('_') + 1
        # 被教练者id_
        _tkey = KeyName[:index]
        main_data['KeyName'] = _tkey
        _model = models.AppIldp.objects.filter(KeyName=_tkey, ProbjectId=ProbjectId, UserId=UserId).first()
        if not _model:
            _model = models.AppIldp.objects.filter(ProbjectId=ProbjectId, UserId=UserId).order_by('-Id').first()
        current_user = models.SysUser.objects.filter(User_Id=request.user.pk).first()
        roleid = current_user.Role_Id
        if _model:
            report = models.AppModelreportreport.objects.filter(ProbjectId=ProbjectId, UserId=UserId,
                                                                ModelCode='r45',
                                                                status=1).order_by('-CreateDate').first()

            # 加上修改ILDP限制 改变观察报告已邀请用户用户部分已填写 不能修改
            exam_user = models.AppProbjectexamrelationuser.objects.filter(ProbjectId=ProbjectId, interested_id=UserId,
                                                                          PageCode='r45')
            if report:
                exam_user = exam_user.filter(CreateDate__gte=report.CreateDate)

            status_list = exam_user.values_list('Status', flat=True)
            if 0 in status_list and 1 in status_list:
                return Response(
                    response.Content().error('本阶段不能修改ILDP').raw()
                )

            main_data['Id'] = _model.Id
            # 被教练者第一次 更新满意度及能力
            if roleid == 6:
                if _model.Satisfaction is None:
                    _interviewid = int_arg(_tArr[3])
                    pinfo = models.AppProbjectinterview.objects.filter(Id=_interviewid, User_Id=UserId,
                                                                       ProbjectId=ProbjectId).first()
                    if pinfo:
                        pinfo.Satisfaction = main_data.get('Satisfaction')
                        pinfo.growupSatisfied = main_data.get('growupSatisfied')
                        pinfo.inputSatisfied = main_data.get('inputSatisfied')
                        pinfo.save()
                        if main_data.get('plan_list'):
                            arr = main_data.get('plan_list')
                            for item in arr:
                                action = models.AppProbjectlearningaction(Title=item, ActionPlan=item,
                                                                          ProbjectId=ProbjectId, User_Id=UserId,
                                                                          interviewId=_interviewid)
                                action.CreateDate = datetime.datetime.now()
                                action.Creator = request.user.UserName
                                action.save()
                        # 被教练者填写常规约谈,通知已填写的教练
                        coach = models.SysUser.objects.get(pk=pinfo.Coach_Id)
                        student = models.SysUser.objects.get(pk=pinfo.User_Id)
                        company = models.AppCompany.objects.get(
                            Company_Id=models.AppProbjectrelation.objects.filter(
                                ProbjectId=pinfo.ProbjectId, UserId=pinfo.User_Id).first().CompanyId)
                        interview_ildp_to_coach_ok = 'interview_ildp_to_coach_ok'
                        pici = get_pici_link(pinfo.Coach_Id, pinfo.interviewsubject, pinfo.ProbjectId, pinfo.pk)

                        dic = {
                            'studentname': student.UserTrueName,
                            'interview_date': pinfo.EndTime.strftime('%Y-%m-%d'),
                            'company': company.CompanyName,
                            'path_link': get_interview_end_to_coach_link(pinfo.interviewsubject,
                                                                         pinfo.ProbjectId, pici, student.pk),
                        }

                        dic['path_link'] = get_interview_end_to_coach_ok_link(pinfo.interviewsubject,
                                                                              pinfo.ProbjectId, pici,
                                                                              student.pk)
                        push_message.delay(coach, interview_ildp_to_coach_ok, dic, pinfo.ProbjectId)
            elif roleid == 4:
                if null_or_blank(_model.A1):
                    _interviewid = int(_tArr[3])
                    pinfo = models.AppProbjectinterview.objects.filter(Id=_interviewid, User_Id=UserId,
                                                                       ProbjectId=ProbjectId).first()
                    if pinfo:
                        update_times(pinfo)
                        if not null_or_blank(diary):
                            diaryinfo = models.AppGrowthdiary()
                            diaryinfo.User_Id = 0
                            diaryinfo.Coach_Id = CoachId
                            diaryinfo.ProbjectId = ProbjectId
                            diaryinfo.isPublic = 0
                            diaryinfo.Remark = diary
                            diaryinfo.Title = diary[:100]
                            diaryinfo.save()
                    # 教练者填写目标约谈,通知已填写的被教练者
                    coach = models.SysUser.objects.get(pk=pinfo.Coach_Id)
                    student = models.SysUser.objects.get(pk=pinfo.User_Id)
                    company = models.AppCompany.objects.get(
                        Company_Id=models.AppProbjectrelation.objects.filter(
                            ProbjectId=pinfo.ProbjectId, UserId=pinfo.User_Id).first().CompanyId)
                    interview_ildp_to_student_ok = 'interview_ildp_to_student_ok'
                    pici = get_pici_link(pinfo.User_Id, pinfo.interviewsubject, pinfo.ProbjectId, pinfo.pk)

                    dic = {
                        'coach': coach.UserTrueName,
                        'interview_date': pinfo.EndTime.strftime('%Y-%m-%d'),
                        'company': company.CompanyName,
                        'path_link': get_interview_end_to_student_link()
                    }
                    dic['path_link'] = get_interview_end_ok_to_student_link(pinfo.interviewsubject,
                                                                            pinfo.ProbjectId,
                                                                            pici, coach.pk)
                    push_message.delay(student, interview_ildp_to_student_ok, dic, pinfo.ProbjectId)

            del_key(KeyName)
            res = self.update_row(main_data)
            return Response(
                res.raw()
            )
        # 新增
        if roleid == 6:
            _interviewid = int_arg(_tArr[3])
            pinfo = models.AppProbjectinterview.objects.filter(Id=_interviewid, ProbjectId=ProbjectId).first()
            if pinfo:
                pinfo.Satisfaction = main_data.get('Satisfaction')
                pinfo.growupSatisfied = main_data.get('growupSatisfied')
                pinfo.inputSatisfied = main_data.get('inputSatisfied')
                pinfo.save()
                # 被教练者填写目标约谈,通知未填写的教练
                coach = models.SysUser.objects.get(pk=pinfo.Coach_Id)
                student = models.SysUser.objects.get(pk=pinfo.User_Id)
                company = models.AppCompany.objects.get(Company_Id=models.AppProbjectrelation.objects.filter(
                    ProbjectId=pinfo.ProbjectId, UserId=pinfo.User_Id).first().CompanyId)
                interview_ildp_to_coach = 'interview_ildp_to_coach'
                pici = get_pici_link(pinfo.Coach_Id, pinfo.interviewsubject, pinfo.ProbjectId, pinfo.pk)

                dic = {
                    'studentname': student.UserTrueName,
                    'interview_date': pinfo.EndTime.strftime('%Y-%m-%d'),
                    'company': company.CompanyName,
                    'path_link': get_interview_end_to_coach_link(pinfo.interviewsubject, pinfo.ProbjectId, pici, student.pk),
                }
                push_message.delay(coach, interview_ildp_to_coach, dic, pinfo.ProbjectId)

                if main_data.get('plan_list'):
                    arr = main_data.get('plan_list', [])
                    for item in arr:
                        action = models.AppProbjectlearningaction(Title=item, ActionPlan=item,
                                                                  ProbjectId=ProbjectId, User_Id=UserId,
                                                                  interviewId=_interviewid)
                        action.CreateDate = datetime.datetime.now()
                        action.Creator = request.user.UserName
                        action.save()
        # 教练填写目标约谈
        elif roleid == 4:
            _interviewid = int_arg(_tArr[3])
            if _interviewid is not None:
                pinfo = models.AppProbjectinterview.objects.filter(Id=_interviewid, ProbjectId=ProbjectId).first()
                if pinfo:
                    update_times(pinfo)
                    # 教练者填写目标约谈,通知未填写的被教练者
                    coach = models.SysUser.objects.get(pk=pinfo.Coach_Id)
                    student = models.SysUser.objects.get(pk=pinfo.User_Id)
                    company = models.AppCompany.objects.get(Company_Id=models.AppProbjectrelation.objects.filter(
                        ProbjectId=pinfo.ProbjectId, UserId=pinfo.User_Id).first().CompanyId)
                    interview_ildp_to_student = 'interview_ildp_to_student'
                    pici = get_pici_link(pinfo.User_Id, pinfo.interviewsubject, pinfo.ProbjectId, pinfo.pk)

                    dic = {
                        'coach': coach.UserTrueName,
                        'interview_date': pinfo.EndTime.strftime('%Y-%m-%d'),
                        'company': company.CompanyName,
                        'path_link': get_interview_end_to_student_link()
                    }
                    push_message.delay(student, interview_ildp_to_student, dic, pinfo.ProbjectId)

                    if not null_or_blank(diary):
                        diaryinfo = models.AppGrowthdiary()
                        diaryinfo.User_Id = 0
                        diaryinfo.Coach_Id = CoachId
                        diaryinfo.ProbjectId = ProbjectId
                        diaryinfo.isPublic = 0
                        diaryinfo.Remark = diary
                        diaryinfo.Title = diary[:100]
                        diaryinfo.save()
        del_key(KeyName)
        res = self.add_row(main_data)
        return Response(
            res.raw()
        )

    @action(methods=['post'], detail=False)
    def getpagedatafront(self, request):
        t = self.get_page_rows(request.data)
        if t.count() > 0:
            extra = []
            for item in t.all():
                dic = {}
                dic["eId"] = item.Id
                dic["A5Text"] = get_dictionary_list_value('behaviour', item.A5)
                dic["A6Text"] = get_dictionary_list_value('behaviour', item.A6)
                dic["A7Text"] = get_dictionary_list_value('behaviour', item.A7)
                cinfo = models.SysUser.objects.filter(User_Id=item.CoachId).first()
                if cinfo:
                    dic['coachname'] = cinfo.UserTrueName
                extra.append(dic)
            t.extra_list = extra
        return Response(
            self.page_data(t)
        )

    @action(methods=['get'], detail=False)
    def getentity(self, request):
        res = self.get_entity(request)
        if res.status:
            dic = self.serializer_class(res.row, many=False).data
            cinfo = models.SysUser.objects.filter(User_Id=res.row.CoachId).first()
            if cinfo:
                dic['coachname'] = cinfo.UserTrueName
            # 发展目标 devplan  目标1@@计划|||
            plan_list = []
            if not null_or_blank(res.row.devplan):
                arrmb = res.row.devplan.split('|||')
                if len(arrmb) > 0:
                    for item in arrmb:
                        mb = item.split('@@')
                        if len(mb) == 3:
                            dicx = {}
                            dicx["key"] = mb[0]
                            dicx["value"] = get_dictionary_list_value('behaviour', mb[0])
                            dicx["time"] = mb[1]
                            dicx["mb"] = enter_to_br(mb[2])
                            plan_list.append(dicx)
            else:
                if not null_or_blank(res.row.A5):
                    dicx = {}
                    dicx["key"] = res.row.A5
                    dicx["value"] = get_dictionary_list_value('behaviour', res.row.A5)
                    dicx["mb"] = enter_to_br(res.row.A5other)
                    dicx["time"] = res.row.CreateDate
                    plan_list.append(dicx)
                if not null_or_blank(res.row.A6):
                    dicx = {}
                    dicx["key"] = res.row.A6
                    dicx["value"] = get_dictionary_list_value('behaviour', res.row.A6)
                    dicx["mb"] = enter_to_br(res.row.A6other)
                    dicx["time"] = res.row.CreateDate
                    plan_list.append(dicx)
                if not null_or_blank(res.row.A7):
                    dicx = {}
                    dicx["key"] = res.row.A7
                    dicx["value"] = get_dictionary_list_value('behaviour', res.row.A7)
                    dicx["mb"] = enter_to_br(res.row.A7other)
                    dicx["time"] = res.row.CreateDate
                    plan_list.append(dicx)
            dic['devplan'] = plan_list
            if not null_or_blank(res.row.studentaction):
                dic["studentaction"] = enter_to_br(res.row.studentaction)
            if not null_or_blank(res.row.coachaction):
                dic["coachaction"] = enter_to_br(res.row.coachaction)
            if not null_or_blank(res.row.A1):
                dic["A1"] = enter_to_br(res.row.A1)
            if not null_or_blank(res.row.A2):
                dic["A2"] = enter_to_br(res.row.A2)
            if not null_or_blank(res.row.A3):
                dic["A3"] = enter_to_br(res.row.A3)
            if not null_or_blank(res.row.A4):
                dic["A4"] = enter_to_br(res.row.A4)
            res.data = dic
        return Response(
            res.raw()
        )

    # 获取我的最新ILDP教练目标
    @action(methods=['get'], detail=False)
    def GetMyProjectObjectives(self, request):
        probjectid = int_arg(request.query_params.get('probjectid'))
        userid = int_arg(request.query_params.get('userid'))
        ildp = models.AppIldp.objects.filter(ProbjectId=probjectid, UserId=userid).order_by('-Id').first()
        if ildp:
            if not null_or_blank(ildp.devplan):
                arrmb = ildp.devplan.split('|||')
                behaviour_list = []
                remark_list = []
                if len(arrmb) > 0:
                    for item in arrmb:
                        mb = item.split('@@')
                        if len(mb) >= 2:
                            behaviour_list.append(get_dictionary_list_value('behaviour', mb[0]))
                            remark_list.append(mb[2])
                data = behaviour_list.join(',') + '|||' + remark_list.join('@@')
                return Response(
                    response.Content().ok('SUC', data).raw()
                )
            else:
                data = get_dictionary_list_value('behaviour', ildp.A5) + ',' + get_dictionary_list_value('behaviour',
                                                                                                         ildp.A6) + ',' + get_dictionary_list_value(
                    'behaviour', ildp.A7) + '|||' + ildp.A5other + '@@' + ildp.A6other + '@@' + ildp.A7other
                return Response(
                    response.Content().ok('SUC', data).raw()
                )
        return Response(
            response.Content().error('未填写ILDP教练目标').raw()
        )

    @action(methods=['get'], detail=False)
    def GetILDPEntity(self, request):
        probjectid = int_arg(request.query_params.get('probjectid'))
        userid = int_arg(request.query_params.get('userid'))
        ildp = models.AppIldp.objects.filter(ProbjectId=probjectid, UserId=userid).order_by('-Id').first()
        if ildp:
            dic = self.serializer_class(ildp, many=False).data
            cinfo = models.SysUser.objects.filter(User_Id=ildp.CoachId).first()
            if cinfo:
                dic['coachname'] = cinfo.UserTrueName
            # 发展目标 devplan  目标1@@计划|||
            plan_list = []
            if not null_or_blank(ildp.devplan):
                arrmb = ildp.devplan.split('|||')
                if len(arrmb) > 0:
                    for item in arrmb:
                        mb = item.split('@@')
                        if len(mb) == 3:
                            dicx = {}
                            dicx["key"] = get_dictionary_list_value('behaviour', mb[0])
                            dicx["value"] = mb[0]
                            dicx["time"] = mb[1]
                            dicx["mb"] = enter_to_br(mb[2])
                            plan_list.append(dicx)
            else:
                if not null_or_blank(ildp.A5):
                    dicx = {}
                    dicx["key"] = get_dictionary_list_value('behaviour', ildp.A5)
                    dicx["value"] = ildp.A5
                    dicx["mb"] = enter_to_br(ildp.A5other)
                    dicx["time"] = ildp.CreateDate
                    plan_list.append(dicx)
                if not null_or_blank(ildp.A6):
                    dicx = {}
                    dicx["key"] = get_dictionary_list_value('behaviour', ildp.A6)
                    dicx["value"] = ildp.A6
                    dicx["mb"] = enter_to_br(ildp.A6other)
                    dicx["time"] = ildp.CreateDate
                    plan_list.append(dicx)
                if not null_or_blank(ildp.A7):
                    dicx = {}
                    dicx["key"] = get_dictionary_list_value('behaviour', ildp.A7)
                    dicx["value"] = ildp.A7
                    dicx["mb"] = enter_to_br(ildp.A7other)
                    dicx["time"] = ildp.CreateDate
                    plan_list.append(dicx)
            dic['devplan'] = plan_list
            if not null_or_blank(ildp.studentaction):
                dic["studentaction"] = enter_to_br(ildp.studentaction)
            if not null_or_blank(ildp.coachaction):
                dic["coachaction"] = enter_to_br(ildp.coachaction)
            if not null_or_blank(ildp.A1):
                dic["A1"] = enter_to_br(ildp.A1)
            if not null_or_blank(ildp.A2):
                dic["A2"] = enter_to_br(ildp.A2)
            if not null_or_blank(ildp.A3):
                dic["A3"] = enter_to_br(ildp.A3)
            if not null_or_blank(ildp.A4):
                dic["A4"] = enter_to_br(ildp.A4)
            return Response(
                response.Content().ok('SUC', dic).raw()
            )
        return Response(
            response.Content().error('未填写ILDP教练目标').raw()
        )
