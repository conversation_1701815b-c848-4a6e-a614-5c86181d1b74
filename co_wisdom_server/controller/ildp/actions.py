from controller.dictionary.actions import get_dictionary_list_value
from data import models
from utils import response, null_or_blank


# 获取我的最新ILDP教练目标
def get_my_project_objectives(projectid, userid):
    ildp = models.AppIldp.objects.filter(ProbjectId=projectid, UserId=userid).order_by('-Id').first()
    if ildp:
        if not null_or_blank(ildp.devplan):
            arrmb = ildp.devplan.split('|||')
            behaviour_list = []
            remark_list = []
            if len(arrmb) > 0:
                for item in arrmb:
                    mb = item.split('@@')
                    if len(mb) >= 2:
                        text = get_dictionary_list_value('behaviour', mb[0])
                        remark = mb[2]
                        behaviour_list.append(text)
                        remark_list.append(remark)
            return response.Content().ok('SUC', ','.join(behaviour_list) + '|||' + '@@'.join(remark_list))
        else:
            A5Text = get_dictionary_list_value('behaviour', ildp.A5)
            A6Text = get_dictionary_list_value('behaviour', ildp.A6)
            A7Text = get_dictionary_list_value('behaviour', ildp.A7)
            return response.Content().ok('SUC',
                                         A5Text + "," + A6Text + "," + A7Text + "|||" + ildp.A5other + "@@" + ildp.A6other + "@@" + ildp.A7other)
    return response.Content().error()