{"cells": [{"cell_type": "code", "execution_count": 1, "id": "d96629f3-e498-408a-86f8-cca9084f6072", "metadata": {}, "outputs": [], "source": ["from openai import OpenAI\n", "client=OpenAI(api_key=\"***************************************************\")\n"]}, {"cell_type": "code", "execution_count": 2, "id": "59396c75-d237-49ed-8604-56ec5b0fcc78", "metadata": {}, "outputs": [], "source": ["# file = client.files.create(\n", "#     file=open(\"guwen.txt\", \"rb\"), \n", "#     purpose = \"assistants\"\n", "# )"]}, {"cell_type": "code", "execution_count": 3, "id": "72beee5a-18e2-453c-b319-6bd3826a3b72", "metadata": {}, "outputs": [], "source": ["# print(file)"]}, {"cell_type": "code", "execution_count": 4, "id": "1b7dfd67-ee8d-4904-99b4-eb0cb94e5e50", "metadata": {}, "outputs": [], "source": ["# assistant = client.beta.assistants.create(\n", "#     name = \"顾问1\",\n", "#     instructions = \"根据上传的用户常见问题和解答文件回答用户问题\",\n", "#     tools = [{\"type\": \"retrieval\"}],\n", "#     model = \"gpt-4-1106-preview\",\n", "#     file_ids=[file.id]\n", "# )\n", "assistant = {'id': 'asst_9i5Xpjpjt7ufH7mNgsyx8aYd'}"]}, {"cell_type": "code", "execution_count": 18, "id": "fd12f79f-3c92-4727-a366-41d3fe95f68c", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Thread(id='thread_UJRCDLqhCm1XAwb21MzSR2zb', created_at=1705481321, metadata={}, object='thread')\n"]}], "source": ["thread = client.beta. threads.create()\n", "print(thread)"]}, {"cell_type": "code", "execution_count": 19, "id": "90c8b3c0-b44b-44c0-9ccc-5768a6fb1ecf", "metadata": {}, "outputs": [], "source": ["message = client.beta.threads.messages.create(\n", "    thread_id = thread.id,\n", "    role = \"user\",\n", "    content= \"年龄大了，没时间学习\"\n", ")"]}, {"cell_type": "code", "execution_count": 20, "id": "e708de21-04cd-4bfe-9681-fb429de14fca", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Run(id='run_f88KEZXoRck9gGTbFvj1rv0E', assistant_id='asst_9i5Xpjpjt7ufH7mNgsyx8aYd', cancelled_at=None, completed_at=None, created_at=1705481336, expires_at=1705481936, failed_at=None, file_ids=['file-uWyEuocSM5h9WfH1WwUPzbQO'], instructions='根据上传的用户常见问题和解答文件回答用户问题', last_error=None, metadata={}, model='gpt-4-1106-preview', object='thread.run', required_action=None, started_at=None, status='queued', thread_id='thread_UJRCDLqhCm1XAwb21MzSR2zb', tools=[ToolAssistantToolsRetrieval(type='retrieval')])\n"]}], "source": ["run = client.beta.threads.runs.create(\n", "    thread_id = thread.id,\n", "    assistant_id = 'asst_9i5Xpjpjt7ufH7mNgsyx8aYd'\n", ")\n", "print(run)"]}, {"cell_type": "code", "execution_count": 21, "id": "00af7e53-7102-4a37-81cd-100b924d09ac", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Run(id='run_f88KEZXoRck9gGTbFvj1rv0E', assistant_id='asst_9i5Xpjpjt7ufH7mNgsyx8aYd', cancelled_at=None, completed_at=1705481361, created_at=1705481336, expires_at=None, failed_at=None, file_ids=['file-uWyEuocSM5h9WfH1WwUPzbQO'], instructions='根据上传的用户常见问题和解答文件回答用户问题', last_error=None, metadata={}, model='gpt-4-1106-preview', object='thread.run', required_action=None, started_at=1705481336, status='completed', thread_id='thread_UJRCDLqhCm1XAwb21MzSR2zb', tools=[ToolAssistantToolsRetrieval(type='retrieval')])\n"]}], "source": ["run = client.beta.threads.runs.retrieve(\n", "    thread_id=thread.id,\n", "    run_id=run.id\n", ")\n", "print(run)"]}, {"cell_type": "code", "execution_count": 22, "id": "1c56287e-75a7-4254-af7c-24971e8b4e41", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["SyncCursorPage[ThreadMessage](data=[ThreadMessage(id='msg_zLE8ho0hxLQze9tIxVVLSMwb', assistant_id='asst_9i5Xpjpjt7ufH7mNgsyx8aYd', content=[MessageContentText(text=Text(annotations=[], value='用户问题：“我57岁了，太老了，没有那么多时间学习”。\\n\\nAssistant 回答：“认可这个年龄都是职场成功人士，有丰富的经验，很多高管都是这个年龄来学习的，不一定是做个轰轰烈烈的2次创业，而是一种很理想的‘微退休’状态，既发挥余热，又不和社会脱节，还有地位，同时时间灵活，不会太累。举例：Karen自己的转型时也不年轻了，国外老师都是70岁的人，还有十几二十年；举例远洋集团高级副总裁，新奥集团的党委副书记，吴士宏，都是这个年龄来群智学习。”'), type='text')], created_at=1705481342, file_ids=[], metadata={}, object='thread.message', role='assistant', run_id='run_f88KEZXoRck9gGTbFvj1rv0E', thread_id='thread_UJRCDLqhCm1XAwb21MzSR2zb'), ThreadMessage(id='msg_I6EYCQO6AaqUDuyPXAzJv0nl', assistant_id=None, content=[MessageContentText(text=Text(annotations=[], value='年龄大了，没时间学习'), type='text')], created_at=1705481324, file_ids=[], metadata={}, object='thread.message', role='user', run_id=None, thread_id='thread_UJRCDLqhCm1XAwb21MzSR2zb')], object='list', first_id='msg_zLE8ho0hxLQze9tIxVVLSMwb', last_id='msg_I6EYCQO6AaqUDuyPXAzJv0nl', has_more=False)\n"]}], "source": ["messages = client.beta.threads.messages.list(\n", "    thread_id=thread.id\n", ")\n", "print(messages)"]}, {"cell_type": "code", "execution_count": 10, "id": "3703a432-c2b0-4365-8bd5-429e9196d8ae", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{'id': 'asst_9i5Xpjpjt7ufH7mNgsyx8aYd'}\n", "user: 年龄大了，没时间学习\n", "assistant: \n"]}], "source": ["print(assistant)\n", "for message in reversed(messages.data):\n", "    print(message.role + \": \" + message.content[0].text.value)"]}, {"cell_type": "code", "execution_count": 11, "id": "070537b4-4c13-4d2c-a4f7-23501e43394b", "metadata": {}, "outputs": [], "source": ["message = client.beta.threads.messages.create(\n", "    thread_id = thread.id,\n", "    role = \"user\",\n", "    content= \"还有别的例子吗\"\n", ")"]}, {"cell_type": "code", "execution_count": 12, "id": "f8df1820-42f2-4189-b62a-5df4de836e69", "metadata": {}, "outputs": [], "source": ["run = client.beta.threads.runs.create(\n", "    thread_id = thread.id,\n", "    assistant_id = 'asst_9i5Xpjpjt7ufH7mNgsyx8aYd'\n", ")"]}, {"cell_type": "code", "execution_count": 13, "id": "8789d642-5717-4b0c-9e05-4fadecc4ec98", "metadata": {}, "outputs": [], "source": ["run = client.beta.threads.runs.retrieve(\n", "    thread_id=thread.id,\n", "    run_id=run.id\n", ")"]}, {"cell_type": "code", "execution_count": 14, "id": "2b764c6a-5ebc-4b94-a5cb-bac55967ab6b", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["SyncCursorPage[ThreadMessage](data=[ThreadMessage(id='msg_TXxeq5XbYyG4ZVMIR8Ad5vyw', assistant_id='asst_9i5Xpjpjt7ufH7mNgsyx8aYd', content=[MessageContentText(text=Text(annotations=[], value='除了前面提到的例子外，其他的职场成功人士在57岁这个年龄段仍然积极学习的案例没有提供更多的。但是我的建议是不要受年龄的限制。无论年纪大小，学习和提升自己始终是可能的，特别是在今天这个快速变化的时代。您可能有大量的经验和知识，可以结合新学的内容以创新和适应当前的职场需求。您可以考虑灵活地安排自己的时间，找到适合自己节奏的学习方式，这样就可以不断地增长见识，同时维持职场竞争力。所以，请保持积极向上的态度，不断探索和尝试新事物。'), type='text')], created_at=1705391558, file_ids=[], metadata={}, object='thread.message', role='assistant', run_id='run_aI9rakDD7M3hu584Oqrp5TDR', thread_id='thread_vuIYXxC2yO6QHbofvBzYQOBU'), ThreadMessage(id='msg_4sPjNbIGW0K6k1Ns318HNaiT', assistant_id=None, content=[MessageContentText(text=Text(annotations=[], value='还有别的例子吗'), type='text')], created_at=1705391525, file_ids=[], metadata={}, object='thread.message', role='user', run_id=None, thread_id='thread_vuIYXxC2yO6QHbofvBzYQOBU'), ThreadMessage(id='msg_yLLAC1fZvAW17vyoMLnzmovT', assistant_id='asst_9i5Xpjpjt7ufH7mNgsyx8aYd', content=[MessageContentText(text=Text(annotations=[], value='您在担忧年龄大了，没有时间学习，但实际上很多职场成功人士都是在这个年龄继续学习的。例如，Karen在转型时也不年轻了，国外也有70岁的老师还在学习，展望未来十几二十年的职业生涯。比如远洋集团的高级副总裁和新奥集团的党委副书记，吴士宏，他们都是在这个年龄来群智学习的。这个年龄阶段不一定要做出轰轰烈烈的改变，而可以是一种“微退休”的状态：既能发挥自己的余热，又不会与社会脱节，同时保持自己的地位，而时间也比较灵活，不会感到太累。所以，不用担心年龄问题，学习和改变总是有可能的。'), type='text')], created_at=1705391468, file_ids=[], metadata={}, object='thread.message', role='assistant', run_id='run_7nkg74Jynxpfumc5uXuRZjg3', thread_id='thread_vuIYXxC2yO6QHbofvBzYQOBU'), ThreadMessage(id='msg_nVFXUN3HL0GmBf3PGDEwSpsw', assistant_id=None, content=[MessageContentText(text=Text(annotations=[], value='年龄大了，没时间学习'), type='text')], created_at=1705391424, file_ids=[], metadata={}, object='thread.message', role='user', run_id=None, thread_id='thread_vuIYXxC2yO6QHbofvBzYQOBU')], object='list', first_id='msg_TXxeq5XbYyG4ZVMIR8Ad5vyw', last_id='msg_nVFXUN3HL0GmBf3PGDEwSpsw', has_more=False)\n"]}], "source": ["messages = client.beta.threads.messages.list(\n", "    thread_id=thread.id\n", ")\n", "print(messages)"]}, {"cell_type": "code", "execution_count": null, "id": "3147c84f-f304-4616-8ebd-d1bed7381185", "metadata": {}, "outputs": [], "source": ["for message in reversed(messages.data):\n", "    print(message.role + \": \" + message.content[0].text.value)"]}, {"cell_type": "code", "execution_count": 15, "id": "1fb9fb81-03c4-40e9-9bae-8dc682ca2e3e", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["SyncPage[FileObject](data=[FileObject(id='file-uWyEuocSM5h9WfH1WwUPzbQO', bytes=659, created_at=1705378507, filename='guwen.txt', object='file', purpose='assistants', status='processed', status_details=None), FileObject(id='file-H8ESjLOTSZq3Zz0wRJKnybML', bytes=659, created_at=1705053394, filename='guwen.txt', object='file', purpose='assistants', status='processed', status_details=None), FileObject(id='file-ATfiaTzcAwPZElxYRGjwfSHv', bytes=3763, created_at=1704787342, filename='xiaoqing.txt', object='file', purpose='assistants', status='processed', status_details=None), FileObject(id='file-akZkvBarnSZCAY3g2cTq07hj', bytes=22203, created_at=1704787304, filename='销售顾问参考资料.md', object='file', purpose='assistants', status='processed', status_details=None), FileObject(id='file-NmhwYMJvzxqZXRe5LGP5kgGB', bytes=1379, created_at=1704787297, filename='PCC info.txt', object='file', purpose='assistants', status='processed', status_details=None), FileObject(id='file-5yfWTn1ziQ49DGWh8psNBlGu', bytes=29337, created_at=1704787297, filename='DATA For AIcoach.txt', object='file', purpose='assistants', status='processed', status_details=None), FileObject(id='file-X8oTWjB5fwcplm1get6wRMg6', bytes=940, created_at=1704787162, filename='CITO course info.txt', object='file', purpose='assistants', status='processed', status_details=None), FileObject(id='file-jPIfBb2Fgvw6BBaPhYj0ZsTG', bytes=1257, created_at=1704787107, filename='customer_classify.txt', object='file', purpose='assistants', status='processed', status_details=None), FileObject(id='file-CZRveNHYDbYVDt9TbTtgNSak', bytes=2215, created_at=1704787107, filename='example.txt', object='file', purpose='assistants', status='processed', status_details=None), FileObject(id='file-A48f8Y6pzGGiBmYx0WnP5Vev', bytes=3763, created_at=1704442821, filename='xiaoqing.txt', object='file', purpose='assistants', status='processed', status_details=None), FileObject(id='file-doF0Q9niAEheBYwl6NfKw4JZ', bytes=3763, created_at=1704442697, filename='xiaoqing.txt', object='file', purpose='assistants', status='processed', status_details=None), FileObject(id='file-2OYJPGSEGYit4wpBePdDXhzt', bytes=13594, created_at=1704442652, filename='CITO course infomation.docx', object='file', purpose='assistants', status='processed', status_details=None), FileObject(id='file-vLotagFB4uezgIHD4S11pRh4', bytes=3763, created_at=1704441545, filename='xiaoqing.txt', object='file', purpose='assistants', status='processed', status_details=None), FileObject(id='file-cUk6tjP7sWbmrywT0rDzGtXT', bytes=3763, created_at=1704364618, filename='xiaoqing.txt', object='file', purpose='assistants', status='processed', status_details=None), FileObject(id='file-E3Go9JOK0bp7NvshWvpjya47', bytes=14786, created_at=1704364528, filename='xiaoqing.docx', object='file', purpose='assistants', status='processed', status_details=None), FileObject(id='file-R3CAVpjfjjlgjJONuOABIwxW', bytes=14786, created_at=1704364354, filename='xiaoqing.docx', object='file', purpose='assistants', status='processed', status_details=None), FileObject(id='file-NDa3o1uwkcJ7FOo37UbkbtY6', bytes=624251, created_at=1704364281, filename='DATA For AIcoach.docx', object='file', purpose='assistants', status='processed', status_details=None), FileObject(id='file-mdabPIaY0CNRey4QU4AN10g9', bytes=13594, created_at=1704364267, filename='CITO教练课程费用与优惠.docx', object='file', purpose='assistants', status='processed', status_details=None), FileObject(id='file-1oil1Fz3j8Zo5wD2WMFskSIl', bytes=14964, created_at=1704364262, filename='PCC.docx', object='file', purpose='assistants', status='processed', status_details=None), FileObject(id='file-S0UOqwA2Njh66foZakcTMQcN', bytes=604210, created_at=1704364231, filename='顾问 SOP Structure.pdf', object='file', purpose='assistants', status='processed', status_details=None), FileObject(id='file-t1Zm1YRG0lEgOX0dxwkUUvvV', bytes=314422, created_at=1704364146, filename='CITO客户经典画像.pdf', object='file', purpose='assistants', status='processed', status_details=None), FileObject(id='file-ifbL33hNSud4txCHk5LEJ5QI', bytes=528151, created_at=1700709478, filename='_CRM填写标准.pdf', object='file', purpose='assistants', status='processed', status_details=None), FileObject(id='file-pVmr1dK5kF0qVDbB8jFUvY4T', bytes=102333, created_at=1700709377, filename='mao.txt', object='file', purpose='assistants', status='processed', status_details=None), FileObject(id='file-kSfRdrZjNxZHal54K1gkumq5', bytes=22692, created_at=1700709377, filename='sara.txt', object='file', purpose='assistants', status='processed', status_details=None), FileObject(id='file-OBQzcJhccML8fbB9N6olDKCE', bytes=114443, created_at=1700709377, filename='puyan.txt', object='file', purpose='assistants', status='processed', status_details=None), FileObject(id='file-sOgXdMmHkDjhwKAYZNKtmK1G', bytes=63041, created_at=1699335495, filename='m.txt', object='file', purpose='assistants', status='processed', status_details=None), FileObject(id='file-ZQNQiUSb6ffMADIMtH6gZ5cf', bytes=74758, created_at=1699326864, filename='p.txt', object='file', purpose='assistants', status='processed', status_details=None), FileObject(id='file-cOkOVIQDof7xbefpsr4YckeF', bytes=63041, created_at=1699326853, filename='m.txt', object='file', purpose='assistants', status='processed', status_details=None), FileObject(id='file-wBc8SALnKZxNXvBMzKVIfjui', bytes=58348, created_at=1699324051, filename='output.txt', object='file', purpose='assistants', status='processed', status_details=None), FileObject(id='file-6LM9uG0EYgEWGMl53J0FFnrh', bytes=2905, created_at=1694591819, filename='step_metrics.csv', object='file', purpose='fine-tune-results', status='processed', status_details=None), FileObject(id='file-JDJgVbY7M4aw9IgPhfSiBhyA', bytes=1460, created_at=1694591074, filename='fine-tune-0913-test.jsonl', object='file', purpose='fine-tune', status='processed', status_details=None), FileObject(id='file-P5ZYF3RCeCWcLBruYslZZBCA', bytes=9414, created_at=1694591059, filename='fine-tune-0913.jsonl', object='file', purpose='fine-tune', status='processed', status_details=None), FileObject(id='file-OYSjEJuEMStXRAcI0kBIOjTH', bytes=2968, created_at=1694500113, filename='step_metrics.csv', object='file', purpose='fine-tune-results', status='processed', status_details=None), FileObject(id='file-hv0ChncS3fh8kMDFQOB1IC2e', bytes=510, created_at=1694499623, filename='fine-tune-book-test.jsonl', object='file', purpose='fine-tune', status='processed', status_details=None), FileObject(id='file-LN12kbgScxe24HwWQydRpmQF', bytes=3859, created_at=1694499609, filename='fine-tune-book.jsonl', object='file', purpose='fine-tune', status='processed', status_details=None), FileObject(id='file-Pxnvn4eaelPr4UVZc5uzXxd5', bytes=1632, created_at=1694257003, filename='step_metrics.csv', object='file', purpose='fine-tune-results', status='processed', status_details=None), FileObject(id='file-3PUM6LqmXGHKNuq4jCEVnWeG', bytes=49934, created_at=1694256057, filename='fine-tune-xiaozhi002.json', object='file', purpose='fine-tune', status='processed', status_details=None), FileObject(id='file-iAtgOgdI7RHUsKb52hA8lRkZ', bytes=1547, created_at=1694086128, filename='step_metrics.csv', object='file', purpose='fine-tune-results', status='processed', status_details=None), FileObject(id='file-XAWjFVfxQcIgXj6V22QVylUg', bytes=36448, created_at=1694085681, filename='fine-tune-xiaozhi001.json', object='file', purpose='fine-tune', status='processed', status_details=None), FileObject(id='file-t25Ui036ITN4v4IWEJ1ClZp6', bytes=36453, created_at=1694085507, filename='fine-tune-xiaozhi001.json', object='file', purpose='fine-tune', status='error', status_details='Invalid file format. Example 15, message 9, role 自己，得到，张青，1637373737，深圳 is not valid.'), FileObject(id='file-O5YjDVDVjbEcckljRSSwh5Xn', bytes=2165, created_at=1693999817, filename='step_metrics.csv', object='file', purpose='fine-tune-results', status='processed', status_details=None), FileObject(id='file-aCBSundWrARe5BfFhDYTYiam', bytes=100760, created_at=1693998501, filename='fine-tune-xiaozhi.json', object='file', purpose='fine-tune', status='processed', status_details=None), FileObject(id='file-tUCE9hch7pTwlKKtO4sSGIr7', bytes=1733, created_at=1692762574, filename='step_metrics.csv', object='file', purpose='fine-tune-results', status='processed', status_details=None), FileObject(id='file-bYNzPdhCoWj9QrTFjKzLfXzE', bytes=13099, created_at=1692761935, filename='gpt-fine-tune-file.json', object='file', purpose='fine-tune', status='processed', status_details=None), FileObject(id='file-PPrN3w4AaPeDJGzJt0Jw34tf', bytes=11076, created_at=1692761280, filename='gpt-fine-tune-file.json', object='file', purpose='fine-tune', status='processed', status_details=None), FileObject(id='file-WEA8QzBOcwezLDspdwgmn5NA', bytes=839, created_at=1689340417, filename='compiled_results.csv', object='file', purpose='fine-tune-results', status='processed', status_details=None), FileObject(id='file-wPrvarfbBU7V7HcSPKD01Q9c', bytes=424, created_at=1689331252, filename='file', object='file', purpose='fine-tune', status='processed', status_details=None), FileObject(id='file-KWqPcWqFLwliL8ecS54h8z1S', bytes=822, created_at=1689259084, filename='compiled_results.csv', object='file', purpose='fine-tune-results', status='processed', status_details=None), FileObject(id='file-7VpkKi4NWXqpcjdr0Clgog3A', bytes=424, created_at=1689232008, filename='file', object='file', purpose='fine-tune', status='processed', status_details=None), FileObject(id='file-q3qufjLHqdYUhSL4WTJkbkUI', bytes=424, created_at=1689231435, filename='file', object='file', purpose='fine-tune', status='processed', status_details=None), FileObject(id='file-uo2yEEObE21IigITmcrvEjs0', bytes=563, created_at=1689174696, filename='compiled_results.csv', object='file', purpose='fine-tune-results', status='processed', status_details=None), FileObject(id='file-fZ7hxi9qCA47jS62NxYHyFoO', bytes=449, created_at=1689170929, filename='compiled_results.csv', object='file', purpose='fine-tune-results', status='processed', status_details=None), FileObject(id='file-iArTA3PJeC9q8nwn3OoUtWWF', bytes=820, created_at=1689164777, filename='compiled_results.csv', object='file', purpose='fine-tune-results', status='processed', status_details=None), FileObject(id='file-vg3myKzCKUlLRQ5xGvIjveiJ', bytes=424, created_at=1689162973, filename='file', object='file', purpose='fine-tune', status='processed', status_details=None), FileObject(id='file-XqScsQDjqCWDKU3a7KpUFz4U', bytes=424, created_at=1689154735, filename='file', object='file', purpose='fine-tune', status='processed', status_details=None), FileObject(id='file-xaVVaz6ua3aEfBLNJLvGXBZa', bytes=424, created_at=1689154089, filename='file', object='file', purpose='fine-tune', status='processed', status_details=None), FileObject(id='file-spOc9KWBKN14T56J7Eq4ZDVe', bytes=416, created_at=1689082470, filename='compiled_results.csv', object='file', purpose='fine-tune-results', status='processed', status_details=None), FileObject(id='file-AbajARWMOGB0caWmNdc5ZJ5u', bytes=1146, created_at=1689077475, filename='compiled_results.csv', object='file', purpose='fine-tune-results', status='processed', status_details=None), FileObject(id='file-VbYbK145fK7M5ZHZCDyJyhC0', bytes=420, created_at=1689074076, filename='compiled_results.csv', object='file', purpose='fine-tune-results', status='processed', status_details=None), FileObject(id='file-Fkm5u6RMAm746Nyhd0q5NUkN', bytes=951, created_at=1689073367, filename='compiled_results.csv', object='file', purpose='fine-tune-results', status='processed', status_details=None), FileObject(id='file-B26Y3OMq2JQyymeRUEwQQL0w', bytes=132, created_at=1689071965, filename='file', object='file', purpose='fine-tune', status='processed', status_details=None), FileObject(id='file-L1Z6B7lYt7GSgpDywkhejrTP', bytes=132, created_at=1689070665, filename='file', object='file', purpose='fine-tune', status='processed', status_details=None), FileObject(id='file-1E4XslY8PKw9eymtkQZjquJb', bytes=132, created_at=1689070652, filename='file', object='file', purpose='fine-tune', status='processed', status_details=None), FileObject(id='file-cX4055qXLLKdH67mExHWpLoS', bytes=132, created_at=1689066562, filename='test222_prepared.jsonl', object='file', purpose='fine-tune', status='processed', status_details=None), FileObject(id='file-gehmL3ha2YBflL1DqCsUWbSV', bytes=2255, created_at=1689064615, filename='fine-file2', object='file', purpose='fine-tune', status='processed', status_details=None)], object='list', has_more=False)\n"]}], "source": ["files = client.files.list()\n", "print(files)"]}, {"cell_type": "code", "execution_count": 24, "id": "cb09674e-981c-46bc-a9f9-55b4ffe7474d", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/var/folders/96/pvst49k90dx5m679gq2169v00000gn/T/ipykernel_11362/1001295340.py:1: DeprecationWarning: The `.content()` method should be used instead\n", "  content = client.files.retrieve_content(\"file-qISUTKgiOXC21XTeiqiLfUr2\")\n"]}, {"ename": "BadRequestError", "evalue": "Error code: 400 - {'error': {'message': 'Not allowed to download files of purpose: assistants', 'type': 'invalid_request_error', 'param': None, 'code': None}}", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mBadRequestError\u001b[0m                           <PERSON><PERSON> (most recent call last)", "Cell \u001b[0;32mIn[24], line 1\u001b[0m\n\u001b[0;32m----> 1\u001b[0m content \u001b[38;5;241m=\u001b[39m \u001b[43mclient\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mfiles\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mretrieve_content\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mfile-qISUTKgiOXC21XTeiqiLfUr2\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m)\u001b[49m\n\u001b[1;32m      2\u001b[0m \u001b[38;5;28mprint\u001b[39m(content)\n", "File \u001b[0;32m/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/typing_extensions.py:2499\u001b[0m, in \u001b[0;36mdeprecated.__call__.<locals>.wrapper\u001b[0;34m(*args, **kwargs)\u001b[0m\n\u001b[1;32m   2496\u001b[0m \u001b[38;5;129m@functools\u001b[39m\u001b[38;5;241m.\u001b[39mwraps(arg)\n\u001b[1;32m   2497\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m \u001b[38;5;21mwrapper\u001b[39m(\u001b[38;5;241m*\u001b[39margs, \u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39mkwargs):\n\u001b[1;32m   2498\u001b[0m     warnings\u001b[38;5;241m.\u001b[39mwarn(msg, category\u001b[38;5;241m=\u001b[39mcategory, stacklevel\u001b[38;5;241m=\u001b[39mstacklevel \u001b[38;5;241m+\u001b[39m \u001b[38;5;241m1\u001b[39m)\n\u001b[0;32m-> 2499\u001b[0m     \u001b[38;5;28;01m<PERSON><PERSON>\u001b[39;00m \u001b[43marg\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43margs\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[0;32m/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/openai/resources/files.py:258\u001b[0m, in \u001b[0;36mFiles.retrieve_content\u001b[0;34m(self, file_id, extra_headers, extra_query, extra_body, timeout)\u001b[0m\n\u001b[1;32m    245\u001b[0m \u001b[38;5;250m\u001b[39m\u001b[38;5;124;03m\"\"\"\u001b[39;00m\n\u001b[1;32m    246\u001b[0m \u001b[38;5;124;03mReturns the contents of the specified file.\u001b[39;00m\n\u001b[1;32m    247\u001b[0m \n\u001b[0;32m   (...)\u001b[0m\n\u001b[1;32m    255\u001b[0m \u001b[38;5;124;03m  timeout: Override the client-level default timeout for this request, in seconds\u001b[39;00m\n\u001b[1;32m    256\u001b[0m \u001b[38;5;124;03m\"\"\"\u001b[39;00m\n\u001b[1;32m    257\u001b[0m extra_headers \u001b[38;5;241m=\u001b[39m {\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mAccept\u001b[39m\u001b[38;5;124m\"\u001b[39m: \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mapplication/json\u001b[39m\u001b[38;5;124m\"\u001b[39m, \u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39m(extra_headers \u001b[38;5;129;01mor\u001b[39;00m {})}\n\u001b[0;32m--> 258\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_get\u001b[49m\u001b[43m(\u001b[49m\n\u001b[1;32m    259\u001b[0m \u001b[43m    \u001b[49m\u001b[38;5;124;43mf\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43m/files/\u001b[39;49m\u001b[38;5;132;43;01m{\u001b[39;49;00m\u001b[43mfile_id\u001b[49m\u001b[38;5;132;43;01m}\u001b[39;49;00m\u001b[38;5;124;43m/content\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m,\u001b[49m\n\u001b[1;32m    260\u001b[0m \u001b[43m    \u001b[49m\u001b[43moptions\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mmake_request_options\u001b[49m\u001b[43m(\u001b[49m\n\u001b[1;32m    261\u001b[0m \u001b[43m        \u001b[49m\u001b[43mextra_headers\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mextra_headers\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mextra_query\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mextra_query\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mextra_body\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mextra_body\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mtimeout\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mtimeout\u001b[49m\n\u001b[1;32m    262\u001b[0m \u001b[43m    \u001b[49m\u001b[43m)\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    263\u001b[0m \u001b[43m    \u001b[49m\u001b[43mcast_to\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;28;43mstr\u001b[39;49m\u001b[43m,\u001b[49m\n\u001b[1;32m    264\u001b[0m \u001b[43m\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[0;32m/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/openai/_base_client.py:1055\u001b[0m, in \u001b[0;36mSyncAPIClient.get\u001b[0;34m(self, path, cast_to, options, stream, stream_cls)\u001b[0m\n\u001b[1;32m   1052\u001b[0m opts \u001b[38;5;241m=\u001b[39m FinalRequestOptions\u001b[38;5;241m.\u001b[39mconstruct(method\u001b[38;5;241m=\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mget\u001b[39m\u001b[38;5;124m\"\u001b[39m, url\u001b[38;5;241m=\u001b[39mpath, \u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39moptions)\n\u001b[1;32m   1053\u001b[0m \u001b[38;5;66;03m# cast is required because mypy complains about returning Any even though\u001b[39;00m\n\u001b[1;32m   1054\u001b[0m \u001b[38;5;66;03m# it understands the type variables\u001b[39;00m\n\u001b[0;32m-> 1055\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m cast(ResponseT, \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mrequest\u001b[49m\u001b[43m(\u001b[49m\u001b[43mcast_to\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mopts\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mstream\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mstream\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mstream_cls\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mstream_cls\u001b[49m\u001b[43m)\u001b[49m)\n", "File \u001b[0;32m/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/openai/_base_client.py:859\u001b[0m, in \u001b[0;36mSyncAPIClient.request\u001b[0;34m(self, cast_to, options, remaining_retries, stream, stream_cls)\u001b[0m\n\u001b[1;32m    850\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m \u001b[38;5;21mrequest\u001b[39m(\n\u001b[1;32m    851\u001b[0m     \u001b[38;5;28mself\u001b[39m,\n\u001b[1;32m    852\u001b[0m     cast_to: Type[ResponseT],\n\u001b[0;32m   (...)\u001b[0m\n\u001b[1;32m    857\u001b[0m     stream_cls: \u001b[38;5;28mtype\u001b[39m[_StreamT] \u001b[38;5;241m|\u001b[39m \u001b[38;5;28;01mNone\u001b[39;00m \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;01mNone\u001b[39;00m,\n\u001b[1;32m    858\u001b[0m ) \u001b[38;5;241m-\u001b[39m\u001b[38;5;241m>\u001b[39m ResponseT \u001b[38;5;241m|\u001b[39m _StreamT:\n\u001b[0;32m--> 859\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_request\u001b[49m\u001b[43m(\u001b[49m\n\u001b[1;32m    860\u001b[0m \u001b[43m        \u001b[49m\u001b[43mcast_to\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mcast_to\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    861\u001b[0m \u001b[43m        \u001b[49m\u001b[43moptions\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43moptions\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    862\u001b[0m \u001b[43m        \u001b[49m\u001b[43mstream\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mstream\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    863\u001b[0m \u001b[43m        \u001b[49m\u001b[43mstream_cls\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mstream_cls\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    864\u001b[0m \u001b[43m        \u001b[49m\u001b[43mremaining_retries\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mremaining_retries\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    865\u001b[0m \u001b[43m    \u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[0;32m/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/openai/_base_client.py:949\u001b[0m, in \u001b[0;36mSyncAPIClient._request\u001b[0;34m(self, cast_to, options, remaining_retries, stream, stream_cls)\u001b[0m\n\u001b[1;32m    946\u001b[0m         err\u001b[38;5;241m.\u001b[39mresponse\u001b[38;5;241m.\u001b[39mread()\n\u001b[1;32m    948\u001b[0m     log\u001b[38;5;241m.\u001b[39mdebug(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mRe-raising status error\u001b[39m\u001b[38;5;124m\"\u001b[39m)\n\u001b[0;32m--> 949\u001b[0m     \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_make_status_error_from_response(err\u001b[38;5;241m.\u001b[39mresponse) \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;28;01m<PERSON><PERSON>\u001b[39;00m\n\u001b[1;32m    951\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_process_response(\n\u001b[1;32m    952\u001b[0m     cast_to\u001b[38;5;241m=\u001b[39mcast_to,\n\u001b[1;32m    953\u001b[0m     options\u001b[38;5;241m=\u001b[39moptions,\n\u001b[0;32m   (...)\u001b[0m\n\u001b[1;32m    956\u001b[0m     stream_cls\u001b[38;5;241m=\u001b[39mstream_cls,\n\u001b[1;32m    957\u001b[0m )\n", "\u001b[0;31mBadRequestError\u001b[0m: Error code: 400 - {'error': {'message': 'Not allowed to download files of purpose: assistants', 'type': 'invalid_request_error', 'param': None, 'code': None}}"]}], "source": ["content = client.files.retrieve_content(\"file-qISUTKgiOXC21XTeiqiLfUr2\")\n", "print(content)"]}, {"cell_type": "code", "execution_count": null, "id": "ed9fffc6-350b-41d7-b161-0638ffc4ff2d", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.3"}}, "nbformat": 4, "nbformat_minor": 5}