{"cells": [{"cell_type": "code", "execution_count": 1, "id": "d96629f3-e498-408a-86f8-cca9084f6072", "metadata": {}, "outputs": [], "source": ["from openai import OpenAI\n", "client=OpenAI(api_key=\"***************************************************\")\n"]}, {"cell_type": "code", "execution_count": 2, "id": "59396c75-d237-49ed-8604-56ec5b0fcc78", "metadata": {}, "outputs": [], "source": ["file = client.files.create(\n", "    file=open(\"guwen.txt\", \"rb\"), \n", "    purpose = \"assistants\"\n", ")"]}, {"cell_type": "code", "execution_count": 3, "id": "72beee5a-18e2-453c-b319-6bd3826a3b72", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["FileObject(id='file-uWyEuocSM5h9WfH1WwUPzbQO', bytes=659, created_at=1705378507, filename='guwen.txt', object='file', purpose='assistants', status='processed', status_details=None)\n"]}], "source": ["print(file)"]}, {"cell_type": "code", "execution_count": 4, "id": "1b7dfd67-ee8d-4904-99b4-eb0cb94e5e50", "metadata": {}, "outputs": [], "source": ["assistant = client.beta.assistants.create(\n", "    name = \"顾问1\",\n", "    instructions = \"根据上传的用户常见问题和解答文件回答用户问题\",\n", "    tools = [{\"type\": \"retrieval\"}],\n", "    model = \"gpt-4-1106-preview\",\n", "    file_ids=[file.id]\n", ")"]}, {"cell_type": "code", "execution_count": 5, "id": "fd12f79f-3c92-4727-a366-41d3fe95f68c", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Thread(id='thread_6Mq40hnRiEAQARxGKVB83rlO', created_at=1705378545, metadata={}, object='thread')\n"]}], "source": ["thread = client.beta. threads.create()\n", "print(thread)"]}, {"cell_type": "code", "execution_count": 6, "id": "90c8b3c0-b44b-44c0-9ccc-5768a6fb1ecf", "metadata": {}, "outputs": [], "source": ["message = client.beta.threads.messages.create(\n", "    thread_id = thread.id,\n", "    role = \"user\",\n", "    content= \"年龄大了，没时间学习\"\n", ")"]}, {"cell_type": "code", "execution_count": 7, "id": "e708de21-04cd-4bfe-9681-fb429de14fca", "metadata": {}, "outputs": [], "source": ["run = client.beta.threads.runs.create(\n", "    thread_id = thread.id,\n", "    assistant_id = assistant.id\n", ")"]}, {"cell_type": "code", "execution_count": 8, "id": "00af7e53-7102-4a37-81cd-100b924d09ac", "metadata": {}, "outputs": [], "source": ["run = client.beta.threads.runs.retrieve(\n", "    thread_id=thread.id,\n", "    run_id=run.id\n", ")"]}, {"cell_type": "code", "execution_count": 9, "id": "1c56287e-75a7-4254-af7c-24971e8b4e41", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["SyncCursorPage[ThreadMessage](data=[ThreadMessage(id='msg_M74aG0MkITtDMCjpg8mvbdhj', assistant_id='asst_9i5Xpjpjt7ufH7mNgsyx8aYd', content=[MessageContentText(text=Text(annotations=[], value='您并不太老，也不是没有时间学习。其实在您这个年龄，很多人都是职场中的成功人士，拥有丰富的经验。就像有很多高管都选择在这个年龄继续学习一样，您不一定需要为了做出轰轰烈烈的第二次创业而学习。这更像是一种理想的“微退休”状态，您可以既发挥自己的余热，又不与社会脱节，同时保持一定的地位，而且时间安排上也会相对灵活，并不会感到太累。\\n\\n比如Karen在自己的转型期也已不再年轻，而我们在国外看到的老师们往往都是70岁左右的人，他们还有很多年可以继续学习和教学。再比如远洋集团的高级副总裁和新奥集团的党委副书记吴士宏，他们都是在您这个年龄段选择来群智学习的。\\n\\n所以不要觉得自己年龄大就没有学习的机会了，反而这是一个可以充分利用自己经验，与时俱进，掌握新知识的好时机。'), type='text')], created_at=1705378581, file_ids=[], metadata={}, object='thread.message', role='assistant', run_id='run_buSgV8iZb9rWNfZzIYdRzK2d', thread_id='thread_6Mq40hnRiEAQARxGKVB83rlO'), ThreadMessage(id='msg_Ixfux2Z55zHvo88K5QcslWcY', assistant_id=None, content=[MessageContentText(text=Text(annotations=[], value='年龄大了，没时间学习'), type='text')], created_at=1705378571, file_ids=[], metadata={}, object='thread.message', role='user', run_id=None, thread_id='thread_6Mq40hnRiEAQARxGKVB83rlO')], object='list', first_id='msg_M74aG0MkITtDMCjpg8mvbdhj', last_id='msg_Ixfux2Z55zHvo88K5QcslWcY', has_more=False)\n"]}], "source": ["messages = client.beta.threads.messages.list(\n", "    thread_id=thread.id\n", ")\n", "print(messages)"]}, {"cell_type": "code", "execution_count": 10, "id": "3703a432-c2b0-4365-8bd5-429e9196d8ae", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Assistant(id='asst_9i5Xpjpjt7ufH7mNgsyx8aYd', created_at=1705378543, description=None, file_ids=['file-uWyEuocSM5h9WfH1WwUPzbQO'], instructions='根据上传的用户常见问题和解答文件回答用户问题', metadata={}, model='gpt-4-1106-preview', name='顾问1', object='assistant', tools=[ToolRetrieval(type='retrieval')])\n", "user: 年龄大了，没时间学习\n", "assistant: 您并不太老，也不是没有时间学习。其实在您这个年龄，很多人都是职场中的成功人士，拥有丰富的经验。就像有很多高管都选择在这个年龄继续学习一样，您不一定需要为了做出轰轰烈烈的第二次创业而学习。这更像是一种理想的“微退休”状态，您可以既发挥自己的余热，又不与社会脱节，同时保持一定的地位，而且时间安排上也会相对灵活，并不会感到太累。\n", "\n", "比如Karen在自己的转型期也已不再年轻，而我们在国外看到的老师们往往都是70岁左右的人，他们还有很多年可以继续学习和教学。再比如远洋集团的高级副总裁和新奥集团的党委副书记吴士宏，他们都是在您这个年龄段选择来群智学习的。\n", "\n", "所以不要觉得自己年龄大就没有学习的机会了，反而这是一个可以充分利用自己经验，与时俱进，掌握新知识的好时机。\n"]}], "source": ["print(assistant)\n", "for message in reversed(messages.data):\n", "    print(message.role + \": \" + message.content[0].text.value)"]}, {"cell_type": "code", "execution_count": null, "id": "070537b4-4c13-4d2c-a4f7-23501e43394b", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.3"}}, "nbformat": 4, "nbformat_minor": 5}