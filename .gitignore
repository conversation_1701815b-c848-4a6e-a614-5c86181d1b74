# Byte-compiled / optimized / DLL files
__pycache__/
*.py[cod]
*$py.class
# C extensions
*.so
# Distribution / packaging
.Python
env/
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
# PyInstaller
#  Usually these files are written by a python script from a template
#  before PyInstaller builds the exe, so as to inject date/other infos into it.
*.manifest
*.spec
# Installer logs
pip-log.txt
pip-delete-this-directory.txt
migrations/
00*.py
.idea
.vscode
run/
run/*
co_wisdom_server/stats.log
co_wisdom_server/celerybeat-schedule.bak
co_wisdom_server/celerybeat-schedule.dat
co_wisdom_server/celerybeat-schedule.dir
co_wisdom_server/uwsgi.ini
co_wisdom_server/Upload
.DS_Store
/co_wisdom_server/cert
migrate.py
/co_wisdom_server/co_wisdom_server/cert
dump.rdb
co_wisdom_server/.env
